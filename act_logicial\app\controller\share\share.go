// Package share  分享
package share

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/publishing_marketing/account"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_share"
	accountTeam "git.woa.com/trpcprotocol/publishing_marketing/account_team"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/logic/share"
	"trpc.act.logicial/app/model/invitation"
	"trpc.act.logicial/app/util"
)

// ShareServiceTmp 结构体
type ShareServiceTmp struct{}

// GetShareCode 获取分享码
func (s *ShareServiceTmp) GetShareCode(ctx context.Context, req *pb.ShareCodeReq,
	rsp *pb.ShareCodeRsp) (err error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	shareCode, err := util.GetShareCode(userAccount.Uid, userAccount.AccountType, req.FsourceId)
	if err != nil {
		return err
	}
	rsp.ShareCode = shareCode
	return
}

// GetShortShareCode 获取分享12位短码
func (s *ShareServiceTmp) GetShortShareCode(ctx context.Context, req *pb.ShortShareCodeReq,
	rsp *pb.ShortShareCodeRsp) (err error) {
	// metadata.SetUserAccount(ctx, accountPb.UserAccount{
	// 	Uid:         "29080-212554473218401583801",
	// 	AccountType: accountPb.AccountType_INTL,
	// 	IntlAccount: &accountPb.IntlAccount{
	// 		OpenId:    "16657983015453317121",
	// 		GameId:    "29080",
	// 		ChannelId: 6,
	// 		Token:     "f7e7475e8f3add68a447b5d4171888f481cac1fb",
	// 	},
	// })

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	shortTableName, err := util.GetShortShareTableName(ctx, req.FsourceId)
	var shortCode invitation.ShortCode
	selectdb := DB.DefaultConnect().WithContext(ctx).Table(shortTableName).Where("uid = ? and Fsource_id = ?",
		userAccount.Uid, req.FsourceId).First(&shortCode)
	if selectdb.Error != nil {
		if errors.Is(selectdb.Error, gorm.ErrRecordNotFound) {
			// 找不到数据
		} else {
			// 报错,数据库操作异常
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
		}
	}
	// 判断用户是否已经拥有邀请码
	if len(shortCode.ShareCode) > 0 {
		rsp.ShareCode = shortCode.ShareCode
		return nil
	}
	// 获取邀请码
	shareCode, err := util.GetShortShareCode(userAccount.Uid, req.FsourceId)
	if err != nil {
		return err
	}
	// 判断邀请码是否重复，重试三次
	for i := 0; i < 3; i++ {
		var total int64
		db := DB.DefaultConnect().WithContext(ctx).Table(shortTableName).Where("share_code = ? and Fsource_id = ?", shareCode,
			req.FsourceId).Count(&total)
		if db.Error != nil {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
		}
		if total == 0 {
			break
		}
		if i == 2 && total > 0 {
			return errs.NewCustomError(ctx, code.GenerateShareCodeFail, "generate  shareCode fail")
		}
		shareCode, err = util.GetShortShareCode(fmt.Sprintf("%v-retry%v", userAccount.Uid, i), req.FsourceId)
	}

	// 插入数据库
	shortCodeData := invitation.ShortCode{
		UID:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		FsourceId:   req.FsourceId,
		ShareCode:   shareCode,
	}
	where := invitation.ShortCode{
		FsourceId: req.FsourceId,
		ShareCode: shareCode,
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(shortTableName).Where(&where).FirstOrCreate(&shortCodeData)
	err = db.Error
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	rsp.ShareCode = shortCodeData.ShareCode
	return nil
}

// RecordShortUserShare 短码分享
func (s *ShareServiceTmp) RecordShortUserShare(ctx context.Context, req *pb.RecordShortUserShareReq,
	rsp *pb.RecordShortUserShareRsp) (err error) {

	if req.ShareCode == "" {
		return errs.NewCustomError(ctx, code.ErrorShareCode, "share code is empty")
	}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	shortTableName, err := util.GetShortShareTableName(ctx, req.FsourceId)

	var shortCode invitation.ShortCode
	selectdb := DB.DefaultConnect().WithContext(ctx).Table(shortTableName).Where("Fsource_id = ? and share_code = ?",
		req.FsourceId, req.ShareCode).First(&shortCode)
	if errors.Is(selectdb.Error, gorm.ErrRecordNotFound) {
		return errs.NewCustomError(ctx, code.ErrorShareCode, "record user share error not found share code")
	}
	if selectdb.Error != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"sel shortCode db error, \t [Error]:{%v} ", selectdb.Error)
	}
	if userAccount.Uid == shortCode.UID && int32(userAccount.AccountType) == shortCode.AccountType {
		return errs.NewCustomError(ctx, code.ShareSelf, "record user share error cant share self")
	}
	key := fmt.Sprintf("RecordShortUserShare-%s-%s:%s-%d", req.FsourceId, req.ShareCode, userAccount.Uid,
		int(userAccount.AccountType))
	isGetLock, err := redis.GetClient().SetNX(ctx, key, "1", 2*time.Second).Result()
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"isGetLock db error, \t [Error]:{%v} ", err)
	}
	if !isGetLock {
		return errs.NewCustomError(ctx, code.RequestTooOften, "record user share error not get lock skip")
	}

	logStatus := 1
	if req.IsOnlyLog {
		logStatus = 0
	}

	tableName, err := util.GetShareTableName(ctx, req.FsourceId)
	if err != nil {
		return err
	}
	where := invitation.Invitation{
		InviteeUid:         userAccount.Uid,
		InviteeAccountType: int32(userAccount.AccountType),
		FsourceId:          shortCode.FsourceId,
		IsDelete:           0,
	}
	var isHas int64
	firstDb := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(&where).Count(&isHas)

	if firstDb.Error != nil {
		if errors.Is(firstDb.Error, gorm.ErrRecordNotFound) {
		} else {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"isHas db error, \t [Error]:{%v} ", firstDb.Error)
		}
	}
	// 已经被邀请过了
	if isHas > 0 {
		return errs.NewCustomError(ctx, code.ShortShareCodeAlreadyRecord, "user already record used err")
	}
	// 判断邀请人队伍是否达到最大限制
	if req.MaxShareNum != 0 {
		var hasShareNum int64
		query := invitation.Invitation{
			UID:         shortCode.UID,
			AccountType: shortCode.AccountType,
			FsourceId:   shortCode.FsourceId,
			Status:      logStatus,
			IsDelete:    0,
		}
		db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(&query).Count(&hasShareNum)
		if err = db.Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"hasShareNum db error, \t [Error]:{%v} ", err)
		}
		if hasShareNum >= int64(req.MaxShareNum) {
			return errs.NewCustomError(ctx, code.ReachedMaxLimitOfInvitedUsers, "Reached the maximum limit of invited users err")
		}
	}

	invitationData := invitation.Invitation{
		UID:                shortCode.UID,
		AccountType:        shortCode.AccountType,
		InviteeUid:         userAccount.Uid,
		InviteeAccountType: int32(userAccount.AccountType),
		FsourceId:          shortCode.FsourceId,
		Status:             logStatus,
		IsDelete:           0,
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(&where).FirstOrCreate(&invitationData)
	redis.GetClient().Del(ctx, key) // 释放锁
	err = db.Error
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"FirstOrCreate invitationData db error, \t [Error]:{%v} ", err)
	}

	rsp.HasRecordShare = true
	var inviteeOpenid string
	uidSplit := strings.Split(shortCode.UID, "-")
	if len(uidSplit) == 2 {
		inviteeOpenid = uidSplit[1]
	}
	rsp.InviteeOpenid = inviteeOpenid
	log.WithFieldsContext(ctx, "log_type", "short_share", "source_id", req.FsourceId).Infof(
		"add share invitationData:[%v], rsp:[%v]", invitationData, rsp)
	return nil
}

// GetUserTeamSizeUntilYesterday 获取当前用户截止昨天的小队人数
func (s *ShareServiceTmp) GetUserTeamSizeUntilYesterday(ctx context.Context, req *pb.GetUserTeamSizeUntilYesterdayReq,
	rsp *pb.GetUserTeamSizeUntilYesterdayRsp) error {
	teamSizeUntilYesterday, err := share.GetUserTeamSizeUntilYesterday(ctx, req.TimeZone, req.FsourceId)
	if err != nil {
		return err
	}
	rsp.GroupSize = teamSizeUntilYesterday
	return nil
}

// IsInvitedUser 判断是否用户被邀请
func (s *ShareServiceTmp) IsInvitedUser(ctx context.Context, req *pb.IsInvitedUserReq,
	rsp *pb.IsInvitedUserRsp) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}

	// 查询是否被绑定
	tableName, err := util.GetShareTableName(ctx, req.FsourceId)
	if err != nil {
		return err
	}
	where := invitation.Invitation{
		InviteeUid:         userAccount.Uid,
		InviteeAccountType: int32(userAccount.AccountType),
		FsourceId:          req.FsourceId,
		IsDelete:           0,
	}
	var isHas int64
	firstDb := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(&where).Count(&isHas)

	if firstDb.Error != nil {
		if errors.Is(firstDb.Error, gorm.ErrRecordNotFound) {
		} else {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"isHas db error, \t [Error]:{%v} ", firstDb.Error)
		}
	}

	if isHas > 0 {
		rsp.IsInvited = true
	} else {
		rsp.IsInvited = false
		err = errs.NewCustomError(ctx, code.ShortShareCodeNoAlreadyRecord, "user not already record used err")
	}

	return
}

// GetShareNum 获取分享数量
func (s *ShareServiceTmp) GetShareNum(ctx context.Context, req *pb.ShareNumReq,
	rsp *pb.ShareNumRsp) error {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}

	tableName, err := util.GetShareTableName(ctx, req.FsourceId)
	if err != nil {
		return err
	}

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
		"status":       1,
		"is_delete":    0,
	}
	var shareNum int64
	err = DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition).Count(&shareNum).Error
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	rsp.ShareNum = shareNum
	return nil
}

// RecordUserShare 记录用户分享
func (s *ShareServiceTmp) RecordUserShare(ctx context.Context, req *pb.RecordUserShareReq,
	rsp *pb.RecordUserShareRsp) error {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}

	shareCode, err := util.ParseCode(req.ShareCode)
	if err != nil {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode, "decode shareCode err", err))
		return nil
	}

	if shareCode.Si != req.FsourceId {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode, "source id difference error"))
		return nil
	}

	if userAccount.Uid == shareCode.Ai && int32(userAccount.AccountType) == shareCode.At {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ShareSelf, "ShareSelf"))
		return nil
	}

	key := fmt.Sprintf("RecordUserShare-%s-%s:%s-%d", req.FsourceId, req.ShareCode, userAccount.Uid,
		int(userAccount.AccountType))
	isGetLock, err := redis.GetClient().SetNX(ctx, key, "1", 2*time.Second).Result()
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	if !isGetLock {
		return errs.NewCustomError(ctx, code.RequestTooOften, "RecordUserShare not get lock skip")
	}

	logStatus := 1
	if req.IsOnlyLog {
		logStatus = 0
	}

	tableName, err := util.GetShareTableName(ctx, req.FsourceId)
	if err != nil {
		return err
	}

	invitationData := invitation.Invitation{
		UID:                shareCode.Ai,
		AccountType:        shareCode.At,
		InviteeUid:         userAccount.Uid,
		InviteeAccountType: int32(userAccount.AccountType),
		FsourceId:          shareCode.Si,
		Status:             logStatus,
		IsDelete:           0,
	}
	where := invitation.Invitation{
		InviteeUid:         userAccount.Uid,
		InviteeAccountType: int32(userAccount.AccountType),
		FsourceId:          shareCode.Si,
		IsDelete:           0,
	}

	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(&where).FirstOrCreate(&invitationData)
	redis.GetClient().Del(ctx, key) // 释放锁

	err = db.Error
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}

	rsp.HasRecordShare = true
	return nil
}

// CheckRecordNum 判断分享数量是否满足
func (s *ShareServiceTmp) CheckRecordNum(ctx context.Context, req *pb.CheckRecordNumReq,
	rsp *pb.CheckRecordNumRsp) (err error) {
	shareNumRsp := &pb.ShareNumRsp{}
	err = s.GetShareNum(ctx, &pb.ShareNumReq{FsourceId: req.FsourceId}, shareNumRsp)
	if err != nil {
		return
	}
	if shareNumRsp.ShareNum < int64(req.CheckNum) {
		return errs.NewCustomError(ctx, code.RecordNumNotMatch, "Record Num Not Match")
	}
	rsp.CheckSuccess = true
	return
}

// DoShareUserDraw 抽奖分享奖品
func (s *ShareServiceTmp) DoShareUserDraw(ctx context.Context, req *pb.DoShareUserDrawReq,
	rsp *pb.DoShareUserDrawRsp) (err error) {
	// 查询是否开奖过
	var isOpen int64
	selectDb := DB.DefaultConnect().WithContext(ctx).Table("share_draw_log").Where("Fsource_id", req.FsourceId).
		Count(&isOpen)
	err = selectDb.Error
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	// 判断是否开奖
	if isOpen > 0 {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	// 上锁
	key := fmt.Sprintf("DoShareUserDraw-%s", req.FsourceId)
	isGetLock, err := redis.GetClient().SetNX(ctx, key, "1", 2*time.Second).Result()
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	if !isGetLock {
		return errs.NewCustomError(ctx, code.RequestTooOften, "RecordUserShare not get lock skip")
	}

	var conf = config.GetConfig()
	var prizeList = conf.PrizeList

	fmt.Println("==========================prizeList")
	fmt.Println(prizeList)
	tableName, err := util.GetShareTableName(ctx, req.FsourceId)
	if err != nil {
		return err
	}
	condition := map[string]interface{}{
		"Fsource_id": req.FsourceId,
		"status":     1,
		"is_delete":  0,
	}
	var winnersList []string
	var datas []invitation.ShareDrawLog
	// 遍历奖池中抽取
	for _, v := range prizeList {
		// 获取邀请了大于等于10的人员列表
		var users []string
		db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition).Select("uid").
			Group("uid").
			Having("COUNT(uid) >= ?", v.Condition)
		if len(winnersList) > 0 {
			db = db.Not("uid", winnersList)
		}
		err = db.Pluck("uid", &users).Error
		if err != nil {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
		}
		var winners []string
		// 将查出来的人抽取第一个奖池
		winners = drawLottery(v.Num, users)
		winnersList = append(winnersList, winners...)
		for _, user := range winners {
			data := invitation.ShareDrawLog{
				UID:       user,
				FsourceId: req.FsourceId,
				Prize:     v.Name,
			}
			datas = append(datas, data)
		}

	}
	createDb := DB.DefaultConnect().WithContext(ctx).Table("share_draw_log").Create(&datas)
	redis.GetClient().Del(ctx, key) // 释放锁
	err = createDb.Error
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	fmt.Println("==========================winners")
	fmt.Println(datas)
	return
}

// GetUidByShareCode 从邀请短码中获取邀请人openid
func (s *ShareServiceTmp) GetUidByShareCode(ctx context.Context, req *pb.GetUidByShareCodeReq,
	rsp *pb.GetUidByShareCodeRsp) (
	err error,
) {

	shortTableName, err := util.GetShortShareTableName(ctx, req.FsourceId)
	if err != nil {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode, "get shortShareTableName error",
			err.Error()))
		return
	}

	var shortCode invitation.ShortCode
	db := DB.DefaultConnect().WithContext(ctx).Table(shortTableName).Where("Fsource_id = ? and share_code = ?",
		req.FsourceId, req.ShareCode).First(&shortCode)
	if db.Error != nil {
		if errors.Is(db.Error, gorm.ErrRecordNotFound) {
			return errs.NewCustomError(ctx, code.ErrorShareCode, "record user share error not found share code")
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	fmt.Println("test: ", shortCode)

	rsp.InviteeOpenid = shortCode.UID

	return nil
}

// GetInvitedInfoList 获取跟当前用户相关的邀请人信息
func (s *ShareServiceTmp) GetInvitedInfoList(ctx context.Context, req *pb.GetInvitedInfoListReq,
	rsp *pb.GetInvitedInfoListRsp) (
	err error,
) {
	// 先判断有没有分享码
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	shareTableName, err := util.GetShortShareTableName(ctx, req.FsourceId)
	if err != nil {
		return
	}
	tableName, err := util.GetShareTableName(ctx, req.FsourceId)
	if err != nil {
		return err
	}
	var shareCodeData *invitation.ShortCode
	findDb := DB.DefaultConnect().Debug().WithContext(ctx).Table(shareTableName).
		Where("uid = ? and Fsource_id = ? and account_type = ? and is_delete = 0", userAccount.Uid, req.FsourceId,
			userAccount.AccountType).
		Find(&shareCodeData)

	if findDb.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}
	if findDb.RowsAffected != 0 {
		rsp.ShareCode = shareCodeData.ShareCode
	}
	uids := make([]string, 0)

	if len(rsp.ShareCode) > 0 {
		// 代表是队长
		rsp.IsLeader = true
		invitedWhere := invitation.Invitation{
			UID:         userAccount.Uid,
			AccountType: int32(userAccount.AccountType),
			FsourceId:   req.FsourceId,
			Status:      1,
			IsDelete:    0,
		}
		invitedDatas := make([]*invitation.Invitation, 0)

		findDb = DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).Where(&invitedWhere).Find(&invitedDatas)

		if findDb.Error != nil {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql db error, \t [Error]:{%v} ", findDb.Error)
		}

		if findDb.RowsAffected != 0 {
			for _, item := range invitedDatas {
				uids = append(uids, item.InviteeUid)
			}
		}
		uids = append(uids, userAccount.Uid)
	} else {
		// 代表是队员
		rsp.IsLeader = false
		where := invitation.Invitation{
			InviteeUid:         userAccount.Uid,
			InviteeAccountType: int32(userAccount.AccountType),
			FsourceId:          req.FsourceId,
			Status:             1,
			IsDelete:           0,
		}

		var invitedData *invitation.Invitation

		findDb := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).Where(&where).Find(&invitedData)

		if findDb.Error != nil {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql db error, \t [Error]:{%v} ", findDb.Error)
		}
		if findDb.RowsAffected != 0 {
			var shareCodeData *invitation.ShortCode
			findDb := DB.DefaultConnect().Debug().WithContext(ctx).Table(shareTableName).
				Where("uid = ? and Fsource_id = ? and account_type = ?", invitedData.UID, req.FsourceId, invitedData.AccountType).
				Find(&shareCodeData)

			if findDb.Error != nil {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql db error, \t [Error]:{%v} ", findDb.Error)
				return
			}
			if findDb.RowsAffected != 0 {
				rsp.ShareCode = shareCodeData.ShareCode
			}

			teams := make([]*invitation.Invitation, 0)
			findDb = DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
				Where("uid = ? and Fsource_id = ? and account_type = ? and status = 1 ", invitedData.UID, req.FsourceId,
					invitedData.AccountType).Find(&teams)

			if findDb.Error != nil {
				return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql db error, \t [Error]:{%v} ", findDb.Error)
			}
			if findDb.RowsAffected != 0 {
				for _, item := range teams {
					uids = append(uids, item.InviteeUid)
				}
			}
			uids = append(uids, invitedData.UID)
		}
	}

	log.WithFieldsContext(ctx, "log_type").Infof("teams uids: %+v", uids)
	if len(uids) > 0 {
		accountData, _ := proto.Marshal(&accountPb.UserAccount{
			Uid:         userAccount.Uid,
			AccountType: accountPb.AccountType(1),
			IntlAccount: &accountPb.IntlAccount{
				OpenId:    strings.Split(userAccount.Uid, "-")[1],
				ChannelId: userAccount.IntlAccount.ChannelId,
				GameId:    strings.Split(userAccount.Uid, "-")[0],
			},
		})
		callopts := []client.Option{
			client.WithMetaData(metadata.UserAccount, accountData),
		}
		accountTeamProxy := accountTeam.NewTeamClientProxy()
		users := make([]*account.UserAccount, 0)
		for _, uid := range uids {
			if len(uid) > 0 {
				users = append(users, &account.UserAccount{
					Uid:         uid,
					AccountType: accountPb.AccountType(1),
				})
			}
		}
		accountTeamRsp, errG := accountTeamProxy.GetTeamInfoList(ctx, &accountTeam.GetTeamInfoListReq{
			Users:     users,
			FsourceId: req.FsourceId,
			GameId:    req.GameId,
		}, callopts...)
		if errG != nil {
			err = errG
			return
		}
		rsp.UserInfoList = accountTeamRsp.Users
		log.WithFieldsContext(ctx, "log_type").Infof("users %v, %+v", len(accountTeamRsp.Users), accountTeamRsp.Users)
		if len(accountTeamRsp.Users) > 0 {
			rsp.LeaderInfo = accountTeamRsp.Users[len(accountTeamRsp.Users)-1]
		}
	}

	return
}

// GetTeamInfoByShareCode 获取团队信息通过shareCode
func (s *ShareServiceTmp) GetTeamInfoByShareCode(ctx context.Context, req *pb.GetTeamInfoByShareCodeReq,
	rsp *pb.GetTeamInfoByShareCodeRsp) (
	err error,
) {
	// rsp = &feature_platform_pb.GetTeamInfoByShareCodeRsp{}
	rsp.ShareCode = req.ShareCode

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	var shareCodeData *invitation.ShortCode
	shareTableName, err := util.GetShortShareTableName(ctx, req.FsourceId)
	if err != nil {
		return
	}
	findDb := DB.DefaultConnect().Debug().WithContext(ctx).Table(shareTableName).Where("share_code = ? and Fsource_id = ?",
		req.ShareCode, req.FsourceId).
		Find(&shareCodeData)

	if findDb.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}
	if findDb.RowsAffected == 0 {
		err = errs.NewCustomError(ctx, code.ErrorShareCode, "record user share error not found share code")
		return
	}
	if shareCodeData.UID == userAccount.Uid {
		rsp.IsLeader = true
	}

	tableName, err := util.GetShareTableName(ctx, req.FsourceId)
	if err != nil {
		return
	}

	invitedData := make([]*invitation.Invitation, 0)

	findDb = DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where("uid = ? and Fsource_id = ? and account_type = ? and status = 1 and is_delete = 0", shareCodeData.UID,
			req.FsourceId,
			shareCodeData.AccountType).
		Find(&invitedData)

	if findDb.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}

	// if findDb.RowsAffected != 0 {
	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         userAccount.Uid,
		AccountType: accountPb.AccountType(1),
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    strings.Split(userAccount.Uid, "-")[1],
			ChannelId: userAccount.IntlAccount.ChannelId,
			GameId:    strings.Split(userAccount.Uid, "-")[0],
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	accountTeamProxy := accountTeam.NewTeamClientProxy()
	users := make([]*account.UserAccount, 0)
	for _, item := range invitedData {
		users = append(users, &account.UserAccount{
			Uid:         item.InviteeUid,
			AccountType: accountPb.AccountType(1),
		})
	}
	users = append(users, &account.UserAccount{
		Uid:         shareCodeData.UID,
		AccountType: accountPb.AccountType(1),
	})
	accountTeamRsp, errG := accountTeamProxy.GetTeamInfoList(ctx, &accountTeam.GetTeamInfoListReq{
		Users:     users,
		FsourceId: req.FsourceId,
		GameId:    req.GameId,
	}, callopts...)
	if errG != nil {
		err = errG
		return
	}
	rsp.UserInfoList = accountTeamRsp.Users
	if len(accountTeamRsp.Users) > 0 {
		rsp.LeaderInfo = accountTeamRsp.Users[len(accountTeamRsp.Users)-1]
	}
	// }

	return
}

// GetUserTeamSize 获取当前用户小队数量
func (s *ShareServiceTmp) GetUserTeamSize(ctx context.Context, req *pb.GetUserTeamSizeReq, rsp *pb.GetUserTeamSizeRsp) (
	err error,
) {
	teamSize, err := share.GetUserTeamSize(ctx, req.FsourceId)
	if err != nil {
		return err
	}
	rsp.GroupSize = teamSize
	return nil
}

// HaveShareCode 是否有分享码
func (s *ShareServiceTmp) HaveShareCode(ctx context.Context, req *pb.HaveShareCodeReq, rsp *pb.HaveShareCodeRsp) (
	err error,
) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	shortTableName, err := util.GetShortShareTableName(ctx, req.FsourceId)
	var shortCode invitation.ShortCode
	selectdb := DB.DefaultConnect().WithContext(ctx).Table(shortTableName).Where("uid = ? and Fsource_id = ?",
		userAccount.Uid, req.FsourceId).First(&shortCode)
	if selectdb.Error != nil {
		if errors.Is(selectdb.Error, gorm.ErrRecordNotFound) {
			// 找不到数据
		} else {
			// 报错,数据库操作异常
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
		}
	}
	// 判断用户是否已经拥有邀请码
	if len(shortCode.ShareCode) > 0 {
		rsp.ShareCode = shortCode.ShareCode
		if req.CheckNotCreate {
			err = errs.NewCustomError(ctx, code.HaveCreateShareCode, "share code have create")
			return
		}
		return
	} else {
		if req.CheckNotCreate {
			return
		}
		err = errs.NewCustomError(ctx, code.NoCreateShareCode, "no create share code")
	}
	return
}

// DelShortUserShare 删除用户短码分享记录
func (s *ShareServiceTmp) DelShortUserShare(ctx context.Context, req *pb.DelShortUserShareReq,
	rsp *pb.DelShortUserShareRsp) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	err = share.DelShortUserShare(ctx, userAccount.Uid, int32(userAccount.AccountType), req.ShareCode, req.FsourceId)
	if err != nil {
		return
	}
	return
}

// GetTeamShareCode 获取队伍分享码
func (s *ShareServiceTmp) GetTeamShareCode(ctx context.Context, req *pb.GetTeamShareCodeReq,
	rsp *pb.GetTeamShareCodeRsp) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	shareCode, err := share.GetTeamShareCode(ctx, userAccount.Uid, int32(userAccount.AccountType), req.FsourceId)
	if err != nil {
		return
	}
	rsp.ShareCode = shareCode
	return err
}

// draw 这是一个 Go 语言编写的抽奖函数，该函数根据传入的奖品列表、奖品数量和名单，随机抽取获奖者。函数返回的是一个以奖品名为 key，以获奖者名单为 value 的 map。具体流程如下：
// - 遍历奖品列表，对于每个奖品，抽取 prizeNum 个获奖者；
// - 随机从名单中抽取获奖者，加入到对应奖品的获奖者列表中；
// - 重复 1 和 2，直到所有奖品的获奖者抽取完毕或名单已被抽空。
// 其中，prizeList 是一个以奖品名为 key，以奖品数量为 value 的 map；nameList 是获奖者名单的数组；winners 是一个以奖品名为 key，以获奖者名单为 value 的 map，用于存储抽奖结果。函数使用了 Go 语言的内置函数 make() 和 append()，以及 math/rand 包中的随机数函数 rand.Intn()。
func draw(prizeList map[string]int, nameList []string) (winners map[string][]string, list []string) {
	for prize, prizeNum := range prizeList {
		for i := 0; i < prizeNum; i++ {
			if len(nameList) == 0 {
				break
			}
			winnerIndex := rand.Intn(len(nameList))
			winner := nameList[winnerIndex]
			nameList[winnerIndex] = nameList[len(nameList)-1]
			nameList = nameList[:len(nameList)-1]
			winners[prize] = append(winners[prize], winner)
		}
	}
	// 输出中奖名单
	fmt.Println(winners)
	for _, v := range winners {
		for _, l := range v {
			list = append(list, l)
		}
	}
	return
}

func drawLottery(num int, names []string) []string {
	rand.Seed(time.Now().UnixNano()) // 设置随机种子
	var winners []string
	if num > len(names) {
		num = len(names)
	}
	for i := 0; i < num; i++ {
		index := rand.Intn(len(names))
		winners = append(winners, names[index])
		names = append(names[:index], names[index+1:]...)
	}
	return winners
}
