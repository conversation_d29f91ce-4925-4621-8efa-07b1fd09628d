package tweet

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"trpc.publishing_application.standalonesite/app/pkg/metadatadecode"

	"git.code.oa.com/trpc-go/trpc-go"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/common"

	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"

	"trpc.publishing_application.standalonesite/app/logic/formatted"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountIntlGamePB "git.code.oa.com/trpcprotocol/publishing_marketing/account_intlgame"
	pointsPB "git.woa.com/trpcprotocol/publishing_application/lipass_points"
	gamePB "git.woa.com/trpcprotocol/publishing_application/stand_alone_site_game_game"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	redis2 "github.com/go-redis/redis/v8"
	redisClient "github.com/go-redis/redis/v8"
	es7 "github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"golang.org/x/net/html"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	hotService "trpc.publishing_application.standalonesite/app/logic/hot"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/pkg/security"
	"trpc.publishing_application.standalonesite/app/util"
)

var (
	gameProxy     = gamePB.NewGameClientProxy()
	pointsProxy   = pointsPB.NewPointsClientProxy()
	intlgameProxy = accountIntlGamePB.NewIntlgameClientProxy()
)

type TagType string

type CheckPostContent struct {
	Title          string
	Content        string
	ContentSummary string
	PicUrls        []string
	ContentType    int32
}

// CheckPostCreationReq 校验发布内容
func CheckPostCreationReq(c context.Context, req *CheckPostContent) error {
	/*
	   1、文字限制10000
	   2、标题限制200
	   3、图片限制100张
	*/
	if utf8.RuneCountInString(req.Title) > 200 {
		return errs.NewCustomError(c, code.CreateFailedTitleOverstepLimit, "Publish failed, the title length is invalid")
	}
	// 图片资讯可以内容为空，富文本不可以为空
	if utf8.RuneCountInString(req.Content) == 0 && req.ContentType == constants.POST_TYPE_RICH_TEXT {
		return errs.NewCustomError(c, code.CreateRichTextEmptyContent, "Publish failed, the text length is empty.")
	}
	if utf8.RuneCountInString(req.ContentSummary) > 10000 {
		return errs.NewCustomError(c, code.CreateFailedContextSummaryOverstepLimit, "Publish failed, the content summary length is invalid")
	}
	// 过滤空字符串
	if len(req.PicUrls) > 0 {
		var picUrls []string
		for _, picUrl := range req.PicUrls {
			if picUrl != "" { // 检查字符串是否为空
				picUrls = append(picUrls, picUrl) // 如果不是空字符串，则添加到结果数组中
			}
		}
		req.PicUrls = picUrls
	}

	if len(req.PicUrls) > 100 {
		return errs.NewCustomError(c, code.CreateFailedIMGOverstepLimit, "Publish failed, the number of content images is invalid")
	}
	// 图片类型的动态一定要上传图片
	if req.ContentType == constants.POST_TYPE_IMAGE_TEXT && len(req.PicUrls) == 0 {
		return errs.NewCustomError(c, code.CreateFailedNeedPicError, "Publish failed, the number of content images is invalid")
	}

	// 兼容处理，如果前端富文本类型没有传抽离的文字内容，则获取富文本中的文字内容和富文本中的所有图片链接
	// 解析 HTML 文档
	htmlStr := strings.Join([]string{"<html><body>", req.Content, "</body></html>"}, "")
	doc, err := html.Parse(strings.NewReader(htmlStr))
	if err != nil {
		return errs.NewCustomError(c, code.InvalidParamsErr, "Publish failed, the post content is invalid")
	}

	// 用于存储从文本字段抽离出来的图片链接、文本的数组、换行次数
	var contentImgLinks []string
	var contentTexts []string
	var lineBreakCount int
	var contentSummary string
	var contentPicUrls []string

	// 查找并提取 div 和 p 标签文本，同时提取 img 标签的链接
	traverse(doc, []string{"div", "p"}, &contentImgLinks, &contentTexts, &lineBreakCount)

	for _, picUrl := range contentImgLinks {
		if picUrl != "" { // 检查图片链接字符串是否为空
			if len(picUrl) > 1000 {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePost the image link is too long : %s", picUrl)
				return errs.NewCustomError(c, code.CreateFailedPicUrlTooLong, "Publish failed, the image link is too long")
			}
			contentPicUrls = append(contentPicUrls, picUrl) // 如果不是空字符串，则添加到结果数组中
		}
	}

	if len(contentPicUrls) > 1000 {
		return errs.NewCustomError(c, code.CreateFailedPicUrlCountTooMany, "Publish failed, the number of image links exceeds the limit")
	}

	var contentTextLength int
	for _, contentText := range contentTexts {
		contentTextLength += utf8.RuneCountInString(contentText)
	}
	totalContentLength := contentTextLength + len(contentPicUrls) + lineBreakCount
	if totalContentLength > 10000 {
		return errs.NewCustomError(c, code.CreateFailedContextOverstepLimit, "Publish failed, the content length is invalid")
	}

	if req.ContentType == constants.POST_TYPE_RICH_TEXT && utf8.RuneCountInString(req.ContentSummary) == 0 {
		contentSummary = strings.Join(contentTexts, " ")
		req.ContentSummary = contentSummary
		req.PicUrls = contentPicUrls
	}

	return nil
}

func CreatePostFrequencyLimitCheck(c context.Context, intlOpenid string) error {
	minuteLimitRedisKey := cache.GetUserCreatePostLimitMinuteKey(intlOpenid)
	conf := config.GetConfig()
	if res, _ := redis.GetClient().Get(c, minuteLimitRedisKey).Result(); cast.ToInt64(res) >= conf.UserLimit.Post.Minute.Count {
		return errs.NewCustomError(c, code.UserCreatePostLimitMinuteError, "Post sent too frequently, please try again later.")
	}
	hourLimitRedisKey := cache.GetUserCreatePostLimitHourKey(intlOpenid)
	if res, _ := redis.GetClient().Get(c, hourLimitRedisKey).Result(); cast.ToInt64(res) >= conf.UserLimit.Post.Hour.Count {
		return errs.NewCustomError(c, code.UserCreatePostLimitHourError, "Post sent too frequently, please try again later.")
	}
	dayLimitRedisKey := cache.GetUserCreatePostLimitDayKey(intlOpenid)
	if res, _ := redis.GetClient().Get(c, dayLimitRedisKey).Result(); cast.ToInt64(res) >= conf.UserLimit.Post.Day.Count {
		return errs.NewCustomError(c, code.UserCreatePostLimitDayError, "Post sent too frequently, please try again later.")
	}
	return nil
}

func CreatePostSetUserLimit(c context.Context, intlOpenid string) {
	minuteLimitRedisKey := cache.GetUserCreatePostLimitMinuteKey(intlOpenid)
	conf := config.GetConfig()
	if ok, err := redis.GetClient().SetNX(c, minuteLimitRedisKey, 1, time.Duration(conf.UserLimit.Post.Minute.Duration)*time.Minute).Result(); !ok && err == nil {
		redis.GetClient().Incr(c, minuteLimitRedisKey)
	}
	hourLimitRedisKey := cache.GetUserCreatePostLimitHourKey(intlOpenid)
	if ok, err := redis.GetClient().SetNX(c, hourLimitRedisKey, 1, time.Duration(conf.UserLimit.Post.Hour.Duration)*time.Hour).Result(); !ok && err == nil {
		redis.GetClient().Incr(c, hourLimitRedisKey)
	}
	dayLimitRedisKey := cache.GetUserCreatePostLimitDayKey(intlOpenid)
	if ok, err := redis.GetClient().SetNX(c, dayLimitRedisKey, 1, time.Duration(conf.UserLimit.Post.Day.Duration)*24*time.Hour).Result(); !ok && err == nil {
		redis.GetClient().Incr(c, dayLimitRedisKey)
	}
}

/*
官方账号发布的定时发布类型的帖子，p_post表is_audit=2，有个定时任务将is_audit字段改为1。
但是cms这里展示又会将官方账号定时发布的帖子展示为未审核状态。而我们的db里存的数据是主表p_post和审核表p_post_audit，所以我们可以用p_post_audit的audit_status字段作为筛选，不使用主表p_post的is_audit字段
*/
// CreatePost 创建文章
func CreatePostNew(c context.Context, intlOpenid string, req *pb.CreatePostNewReq, isAudit int, socialmediaPostId, gameId, areaId string, isOfficial bool, language string) (resp *pb.GetPostRsp, err error) {
	resp = &pb.GetPostRsp{}
	postUuid := util.CreateSnowflakeID()
	currentTime := time.Now()
	post := &model.Post{
		PostUUID:          postUuid,
		IntlOpenid:        intlOpenid,
		PlateID:           req.PlateId,
		Type:              req.Type,
		IsTop:             0,
		TopSort:           0,
		TopOn:             0,
		IsAudit:           int32(isAudit),
		LatestRepliedOn:   0,
		GameId:            gameId,
		AreaId:            areaId,
		Visibility:        0,
		SocialmediaPostId: socialmediaPostId,
		CreatedOnMs:       currentTime.UnixMicro(),
		IsHide:            0,
	}
	var isTimer bool
	if isOfficial {
		// 如果是官方账号加上定时发布时间和官方帖子标识
		post.PublishOn = int64(req.PublishOn)
		post.IsOfficial = 1
		post.IsAudit = 1
		// 不能小于当前时间，并且也不能小于未来15分钟之后的时间
		if post.PublishOn > 0 && post.PublishOn < (time.Now().Unix()+15*60) {
			// 定时发布时间小于当前时间
			return nil, errs.NewCustomError(c, code.InvalidParamsErr, "publish time less then now time")
		}
		// 假如有定时发布时间，校验是否是在当前时间之后，是的话审核下架，到期之后上架
		if post.PublishOn > time.Now().Unix() {
			isTimer = true
			post.IsAudit = 2
			currentTime = time.Unix(int64(req.PublishOn), 0)
			post.CreatedOnMs = util.SecondToRandMicro(post.PublishOn)
		}
	}
	// 先查询用户信息
	user, err := dao.GetUserByIntlOpenid(intlOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostNew GetUserByIntlOpenid err: %v, intlOpenid: %s", err, intlOpenid)
		return nil, errs.NewCustomError(c, code.GetUserInfoError, "CreatePost failed to get user info")
	}

	var friendCardInfoStr string
	if req.NeedFriendCard {
		friendCardInfoStr, err = GetFriendCardInfo(c, intlOpenid)
		if err != nil {
			return nil, err
		}
	}
	var guildId, guildAndAreaId string
	var nikkeAreaId int32
	if req.NeedGuildCard {
		guildId, nikkeAreaId, err = GetUserCurrentGuildCardInfo(c, intlOpenid)
		if err != nil {
			return nil, err
		}
		guildAndAreaId = fmt.Sprintf("%s_%d", guildId, nikkeAreaId)
	}
	var postContents []*model.PostContent
	var allLanguage []string
	isAdmin := formatted.GetUserAdmin(intlOpenid)
	for i, content := range req.Contents {
		contentSummary := ""
		if req.Type == constants.POST_TYPE_RICH_TEXT {
			contentSummary = content.ContentSummary
		}
		allLanguage = append(allLanguage, content.Language)
		if i == 0 {
			post.Language = content.Language
		}
		// 未声明时，original_url为空
		if req.OriginalUrl != "" && req.CreatorStatementType == int32(constants.ECreatorStatementType_None) {
			req.CreatorStatementType = int32(constants.ECreatorStatementType_FromUrl)
		}
		// 创作声明多语言版本之间耦合，故取第一个
		postContents = append(postContents, &model.PostContent{
			PostUUID:             postUuid,
			IntlOpenid:           intlOpenid,
			IsOriginal:           content.IsOriginal,
			OriginalURL:          req.OriginalUrl,
			OriginalReprint:      content.OriginalReprint,
			Title:                content.Title,
			Content:              content.Content,
			Platform:             content.Platform,
			ExtInfo:              content.ExtInfo,
			PicUrls:              strings.Join(content.PicUrls, ","),
			ContentSummary:       contentSummary,
			Language:             content.Language,
			Order:                int32(i + 1),
			FriendCardInfo:       friendCardInfoStr,
			CreatorStatementType: constants.ECreatorStatementType(req.CreatorStatementType),
			RiskRemindType:       constants.ERiskRemindType(req.RiskRemindType),
			AiContentType:        constants.EAiContentType(req.AiContentType),
			GuildId:              guildAndAreaId,
		})
		if language == content.Language {
			resp.Title = content.Title
			resp.Content = content.Content
			resp.ContentSummary = content.ContentSummary
			resp.PicUrls = content.PicUrls
			resp.IsOriginal = content.IsOriginal
			resp.OriginalUrl = req.OriginalUrl
			resp.OriginalReprint = content.OriginalReprint
			resp.CreatedOn = time.Now().Unix()
			resp.CreatorStatementType = req.CreatorStatementType
			resp.RiskRemindType = req.RiskRemindType
			resp.AiContentType = req.AiContentType
			// 自己创建的帖子一定可修改状态
			resp.CanEditStatement = isAdmin
		}
	}

	// 获取用户是否被降权
	isDemotion := formatted.GetUserDemotion(intlOpenid)
	if err = dao.CreatePostNew(post, postContents, currentTime, isDemotion); err != nil {
		go func(ctx context.Context, intlGameId string, err2 error) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportPostPublish(ctx, post, req.Tags, postContents, false, user, guildId, err2)
		}(c, gameId, err)

		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostNew dao.CreatePostNew err: %v, intlOpenid: %s", err, intlOpenid)
		return resp, errs.NewCustomError(c, code.CreatePostDBFailed, "An error occurred while creating a post. Please try again later.")
	}
	go func(ctx context.Context, intlGameId string) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportPostPublish(ctx, post, req.Tags, postContents, false, user, guildId, nil)
	}(c, gameId)
	// 创建tag-post关联关系
	if len(req.Tags) > 0 {
		var tagPostRelations []*model.TagPost
		for _, tagID := range req.Tags {
			tagPostRelations = append(tagPostRelations, &model.TagPost{
				TagID:    tagID,
				PostUUID: postUuid,
				PlateID:  req.PlateId,
			})
		}
		if err = dao.BatchSaveTagPostRelation(tagPostRelations); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostNew dao.BatchSaveTagPostRelation err: %v, intlOpenid: %s", err, intlOpenid)
			return resp, errs.NewCustomError(c, code.CreatePostSaveTagFailed, "An error occurred while creating a post. Please try again later.")
		}
		// 给当前的话题加上引用帖子数
		if err = dao.BatchIncrTagsPostNum(req.Tags, 1); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostNew BatchIncrTagsPostNum err: %v, intlOpenid: %s", err, intlOpenid)
		}
	}

	//组装返回体
	resp.PostUuid = postUuid
	resp.IntlOpenid = intlOpenid
	resp.Type = req.Type
	resp.CanDelete = true //这是自己发布的动态所以都可以自己删除
	resp.GameId = gameId
	resp.AreaId = areaId
	resp.Tags = nil
	resp.User = formatted.ReturnDynamicProtoUserInfoFormatted(user.Format())
	resp.PlateId = req.PlateId
	resp.Language = post.Language
	resp.IsAudit = int32(isAudit)
	resp.IsOfficial = post.IsOfficial
	resp.PublishOn = int32(post.PublishOn)

	_, err = CreatePostAudit(c, post, postContents, req.Tags, isAudit, false)
	// 记录添加错误 需要回滚
	if err != nil {
		errs.NewCustomError(c, code.CreatePostAuditErr, "CreatePostAudit err: %v", err)
		return resp, err
	}

	PushPostToSearch(post, postContents, req.Tags, 0, isDemotion)

	userState, err := dao.GetUserStateByUserOpenid(intlOpenid)
	if err == nil {
		userState.AllPostNum++
		userDoc := map[string]interface{}{
			"all_post_num": userState.AllPostNum,
		}
		if isAudit == 1 {
			userState.PostNum++
			userDoc["post_num"] = userState.PostNum
		}
		// 更新用户发布的动态数
		dao.UpdateUserPostNum(userState.IntlOpenid, userState.AllPostNum, userState.PostNum)
		go func() {
			defer recovery.CatchGoroutinePanic(context.Background())
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, user.IntlOpenid, userDoc)
		}()
	}

	// 非白名单 写入审核表, 官方发布的动态不写入这个审核, 走定时任务审核
	//if isAudit == 2 && !isOfficial {
	//	go PushPostToSecurityDetection(c, &CheckPostContent{
	//		Title:          postContent.Title,
	//		Content:        postContent.Content,
	//		ContentSummary: postContent.ContentSummary,
	//		PicUrls:        strings.Split(postContent.PicUrls, ","),
	//		ContentType:    req.Type,
	//	}, postAudit, intlOpenid, user.Username)
	//}
	// 如果是需要定时任务的写入redis
	if isTimer {
		go SetOfficialPublishPostToCache(context.Background(), post.PostUUID, post.PublishOn)
	}
	go cache.DeleteUserPostsCache(intlOpenid, "", 10)

	// 如果发布帖子的来源是“独立站上线推广的H5页面”则记录到关联表，用于H5页面的礼包领取
	if req.From == 1 {
		postFrom := &model.PostFrom{
			PostUUID:   postUuid,
			IntlOpenid: intlOpenid,
			Source:     1,
		}

		if err = dao.CreatePostFrom(postFrom); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostNew CreatePostFrom err: %v, intlOpenid: %s, postUuid: %s", err, intlOpenid, postUuid)
		}
	}
	// 如果是ch作品发布，则需要更新ch作品信息
	if req.ChWorkId != 0 {
		// 查询ch uid
		bindInfo, err := dao.GetBoundChUserInfo(intlOpenid)
		if err != nil || bindInfo == nil || bindInfo.ChUid == "" {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostNew GetBoundChUserInfo err: %v, intlOpenid: %s", err, intlOpenid)
		}
		err = dao.CreateChWorkPost(intlOpenid, resp.PostUuid, req.ChWorkId, bindInfo.ChUid)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostNew CreateChWorkPost err: %v, intlOpenid: %s, postUuid: %s", err, intlOpenid, postUuid)
			// return nil, err
		}
	}

	if req.NeedGuildCard {
		// 创建帖子的时候，如果戴上了公会卡，则触发分享公会到工会广场并应援
		go PublishGuldCard(c, intlOpenid, guildId, nikkeAreaId)
	}
	return resp, nil
}

func GetFriendCardInfo(c context.Context, intlOpenid string) (string, error) {
	var friendCardInfoStr string
	batchSelectSavedRoleInfoByUIDListReq := &gamePB.BatchSelectSavedRoleInfoByUIDListReq{
		Uid: []string{intlOpenid},
	}
	batchSelectSavedRoleInfoByUIDListRsp, err := gameProxy.BatchSelectSavedRoleInfoByUIDList(c, batchSelectSavedRoleInfoByUIDListReq)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetFriendCardInfo gameProxy.BatchSelectSavedRoleInfoByUIDList err: %v, intlOpenid: %s", err, intlOpenid)
		return friendCardInfoStr, errs.NewCustomError(c, code.GetUserSaveRoleInfoError, "GetFriendCardInfo failed to get user role info")
	}
	if len(batchSelectSavedRoleInfoByUIDListRsp.UserSavedRoleList) == 0 {
		return friendCardInfoStr, errs.NewCustomError(c, code.GetUserSaveRoleInfoEmpty, "GetFriendCardInfo failed to get user role info")
	}
	roleResult := batchSelectSavedRoleInfoByUIDListRsp.UserSavedRoleList[0]
	if !roleResult.HasSaved {
		return friendCardInfoStr, errs.NewCustomError(c, code.GetUserSaveRoleInfoNotSave, "GetFriendCardInfo failed to get user role info")
	}
	roleInfoByte, err := json.Marshal(roleResult.RoleInfo)
	if err != nil {
		return friendCardInfoStr, errs.NewCustomError(c, code.CreatePostJsonMarshalRoleInfoError, "GetFriendCardInfo failed to parse role info")
	}
	friendCardInfoStr = string(roleInfoByte)

	return friendCardInfoStr, nil
}

func GetUserCurrentGuildCardInfo(c context.Context, intlOpenid string) (string, int32, error) {
	var guildId, intlUserOpenid string
	var nikkeAreaIdI int32
	intlOpenidList := strings.Split(intlOpenid, "-")
	if len(intlOpenidList) != 2 {
		return guildId, nikkeAreaIdI, errs.NewCustomError(c, code.GetGuildUserOpenidError, "GetGuildCardInfo | Failed to obtain user openid, please check")
	} else {
		intlUserOpenid = intlOpenidList[1]
	}
	getUserGamePlayerInfoRsp, err := user.GetUserNikkeBasicInfo(c, intlOpenid, "")
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetGuildCardInfo GetUserNikkeBasicInfo error,intlOpenid:%s, err: %v", intlOpenid, err)
		return guildId, nikkeAreaIdI, errs.NewCustomError(c, code.GetGuildUserPlayerBasicInfoError, "GetGuildCardInfo | Failed to obtain user nikke game information, please check")
	}
	if !getUserGamePlayerInfoRsp.HasSavedRoleInfo {
		return guildId, nikkeAreaIdI, errs.NewCustomError(c, code.GetGuildUserNotSaveRoleError, "GetGuildCardInfo | Failed to obtain user nikke role information, please check")
	}
	nikkeAreaIdI32, err := strconv.ParseInt(getUserGamePlayerInfoRsp.AreaId, 10, 32)
	if err != nil {
		return guildId, nikkeAreaIdI, errs.NewCustomError(c, code.GetGuildUserInvalidNikkeAreaIdError, "GetGuildCardInfo | Failed to obtain user nikke role area id information, please check")
	}
	nikkeAreaIdI = int32(nikkeAreaIdI32)
	req := &gamePB.GetUserGuildInfoBulkReq{
		NikkeAreaId: nikkeAreaIdI,
		IntlOpenIds: []string{intlUserOpenid},
	}
	userGuildInfo, err := gameProxy.GetUserGuildInfoBulk(c, req)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetGuildCardInfo GetUserGuildInfoBulk error,req:%v, err: %v", req, err)
		return guildId, nikkeAreaIdI, errs.NewCustomError(c, code.GetGuildUserGuildInfoError, "GetGuildCardInfo | Failed to obtain user guild information, please check")
	}
	if userGuildInfo == nil || len(userGuildInfo.UserGuildInfos) == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetGuildCardInfo GetUserGuildInfoBulk error,req:%v, empty guild info", req)
		return guildId, nikkeAreaIdI, errs.NewCustomError(c, code.GetGuildUserGuildInfoEmptyError, "GetGuildCardInfo | Failed to obtain user nikke guild information, please check")
	}
	guildId = userGuildInfo.UserGuildInfos[0].GuildId
	return guildId, nikkeAreaIdI, nil
}

func PublishGuldCard(c context.Context, intlOpenid, guildId string, nikkeAreaId int32) {
	var intlUserOpenid string
	intlOpenidList := strings.Split(intlOpenid, "-")
	if len(intlOpenidList) != 2 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishGuldCard intlOpenid error,intlOpenid:%s", intlOpenid)
		return
	} else {
		intlUserOpenid = intlOpenidList[1]
	}
	req := &gamePB.PublishGuldCardInnerReq{
		IntlOpenId:  intlUserOpenid,
		NikkeAreaId: nikkeAreaId,
		GuildId:     guildId,
	}
	_, err := gameProxy.PublishGuldCardInner(c, req)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishGuldCard PublishGuldCardInner error,req:%v, err: %v", req, err)
		return
	}
}

// UpdatePost 更新文章
func UpdatePost(c context.Context, intlOpenid string, req *pb.UpdatePostReq, isAudit int, gameId, areaId string, isOfficial bool, language string) (resp *pb.GetPostRsp, err error) {
	resp = &pb.GetPostRsp{}

	var postUuid string
	if req.PostUuid != "" {
		postUuid = req.PostUuid
	}

	postInfo, err := GetPostInfo(c, intlOpenid, postUuid, language, 1)
	if err != nil {
		// 获取不到需要更新的数据，直接退出更新
		return nil, err
	}
	// 如果不是自己的帖子，没有权限更新
	if postInfo.IntlOpenid != intlOpenid {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UpdatePost GetPostInfo, author not post intlOpenid err: %v, intlOpenid: %s, post uuid: %s", err, intlOpenid, postUuid)
		return nil, errs.NewCustomError(c, code.NoPermissionToUpdatePost, "Not permission!")
	}

	post := &model.Post{
		Model: &model.Model{
			CreatedOn: postInfo.CreatedOn,
		},
		PostUUID:    postUuid,
		IntlOpenid:  intlOpenid,
		PlateID:     req.PlateId,
		Language:    postInfo.Language,
		Type:        req.Type,
		IsTop:       postInfo.IsTop,
		TopSort:     postInfo.TopSort,
		TopOn:       postInfo.TopOn,
		IsAudit:     postInfo.IsAudit,
		GameId:      gameId,
		AreaId:      areaId,
		CreatedOnMs: postInfo.CreatedOnMs,
		Platform:    postInfo.Platform,
	}
	isTimer := false
	if isOfficial {
		// 如果是官方账号加上定时发布时间和官方帖子标识
		post.IsOfficial = 1
		post.IsAudit = 1

		// 先判断这个定时发布时间是否大于当前15分钟之前，不是的话需要报错
		if req.PublishOn > 0 && int64(req.PublishOn) < (time.Now().Unix()+15*60) {
			// 定时发布时间小于当前时间
			return nil, errs.NewCustomError(c, code.InvalidParamsErr, "publish time less then now time")
		}

		if postInfo.IsAudit == 2 {
			// 定时发布之前编辑帖子并且修改了不同的定时发布时间
			if req.PublishOn > 0 {
				if req.PublishOn != postInfo.PublishOn {
					post.PublishOn = int64(req.PublishOn)
					post.Model.CreatedOn = post.PublishOn
					post.CreatedOnMs = util.SecondToRandMicro(post.PublishOn)
				} else {
					post.PublishOn = int64(postInfo.PublishOn)
					post.Model.CreatedOn = postInfo.CreatedOn
					post.CreatedOnMs = postInfo.CreatedOnMs
				}
			} else {
				// 定时发布之前编辑帖子之后选择了立马发布
				post.PublishOn = time.Now().Unix()
				post.Model.CreatedOn = time.Now().Unix()
				post.CreatedOnMs = time.Now().UnixMicro()
			}
		} else {
			// 定时发布之后（已经发布对外了）
			if req.PublishOn > 0 && req.PublishOn != postInfo.PublishOn {
				// 定时发布之后编辑内容选择再次定时发布，发布时间应该是这一次的发布时间
				post.PublishOn = int64(req.PublishOn)
				post.Model.CreatedOn = post.PublishOn
				post.CreatedOnMs = util.SecondToRandMicro(post.PublishOn)
			} else {
				// 定时发布之后编辑内容选择立即发布，发布时间应该是上一次的发布时间
				post.PublishOn = int64(postInfo.PublishOn)
				if post.PublishOn > 0 {
					// 如果没有定时发布时间那就不改这个创建时间
					post.Model.CreatedOn = post.PublishOn
					post.CreatedOnMs = util.SecondToRandMicro(post.PublishOn)
				}
			}
		}

		// 假如有定时发布时间，校验是否是在当前时间之后，是的话审核下架，到期之后上架
		if post.PublishOn > time.Now().Unix() {
			post.IsAudit = 2
			isTimer = true
		}
	}

	// 先查询用户信息
	user, err := dao.GetUserByIntlOpenid(intlOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePost GetUserByIntlOpenid err: %v, intlOpenid: %s", err, intlOpenid)
		return nil, errs.NewCustomError(c, code.GetUserInfoError, "CreatePost failed to get user info")
	}

	var friendCardInfoStr string
	if req.NeedRefreshFriendCard == 1 {
		friendCardInfoStr, err = GetFriendCardInfo(c, intlOpenid)
		if err != nil {
			return nil, err
		}
	}
	var guildAndAreaId, guildId string
	var nikkeAreaId int32
	if req.NeedRefreshGuildCard == 1 {
		guildId, nikkeAreaId, err = GetUserCurrentGuildCardInfo(c, intlOpenid)
		if err != nil {
			return nil, err
		}
		guildAndAreaId = fmt.Sprintf("%s_%d", guildId, nikkeAreaId)
	}
	var postContents []*model.PostContent
	var deleteContentLanguageIds []int64
	var deleteLanguages []string
	respData := &pb.GetPostRsp{}
	var newLanguage string
	isAdmin := formatted.GetUserAdmin(intlOpenid)
	for i, content := range req.Contents {
		contentSummary := ""
		if req.Type == constants.POST_TYPE_RICH_TEXT {
			contentSummary = content.ContentSummary
		}
		// 普通用户的帖子只有一种语言
		if i == 0 {
			newLanguage = content.Language
		}
		postContents = append(postContents, &model.PostContent{
			PostUUID:        postUuid,
			IntlOpenid:      intlOpenid,
			IsOriginal:      content.IsOriginal,
			OriginalURL:     req.OriginalUrl,
			OriginalReprint: content.OriginalReprint,
			Title:           content.Title,
			Content:         content.Content,
			Platform:        content.Platform,
			ExtInfo:         content.ExtInfo,
			PicUrls:         strings.Join(content.PicUrls, ","),
			ContentSummary:  contentSummary,
			Order:           int32(i + 1),
			Language:        content.Language,
			Model: &model.Model{
				ID:         int64(content.Id),
				ModifiedOn: time.Now().Unix(),
			},
			FriendCardInfo:       friendCardInfoStr,
			GuildId:              guildAndAreaId,
			AiContentType:        constants.EAiContentType(req.AiContentType),
			RiskRemindType:       constants.ERiskRemindType(req.RiskRemindType),
			CreatorStatementType: constants.ECreatorStatementType(req.CreatorStatementType),
		})

		if language == content.Language {
			respData.Title = content.Title
			respData.Content = content.Content
			respData.ContentSummary = content.ContentSummary
			respData.PicUrls = content.PicUrls
			respData.IsOriginal = content.IsOriginal
			respData.OriginalUrl = req.OriginalUrl
			respData.OriginalReprint = content.OriginalReprint
			respData.ModifiedOn = time.Now().Unix()
			respData.RiskRemindType = int32(req.RiskRemindType)
			respData.CreatorStatementType = int32(req.CreatorStatementType)
			respData.AiContentType = int32(req.AiContentType)
			respData.CanEditStatement = isAdmin
		}
	}

	// 判断更新的是官方帖子还是普通用户帖子
	if isOfficial {
		// 对比哪些需要删除的数据
		for _, contentItem := range postInfo.ContentLanguages {
			isExist := false
			for _, content := range req.Contents {
				if contentItem.Id == content.Id {
					isExist = true
					break
				}
			}
			if !isExist {
				deleteContentLanguageIds = append(deleteContentLanguageIds, int64(contentItem.Id))
				deleteLanguages = append(deleteLanguages, contentItem.Language)
			}
		}
		if err = dao.UpdatePostNew(req.NeedRefreshFriendCard, req.NeedRefreshGuildCard, post, postContents, deleteContentLanguageIds, deleteLanguages); err != nil {
			go func(ctx context.Context, intlGameId string, err2 error) {
				newC := trpc.CloneContext(ctx)
				defer recovery.CatchGoroutinePanic(newC)
				common.ReportPostPublish(c, post, req.Tags, postContents, false, user, guildId, err2)
			}(c, gameId, err)
			return resp, err
		}
		// 更新es
		UpdatePostToSearch(post, postContents, req.Tags, int64(postInfo.HotNum), deleteLanguages)

		// 如果是需要定时任务的写入redis
		if isTimer {
			go SetOfficialPublishPostToCache(context.Background(), post.PostUUID, post.PublishOn)
		} else {
			go DelOfficialPublishPostByCache(context.Background(), post.PostUUID)
		}
	} else {
		// 如果有内容修改了，则需要生成新的审核记录
		var hasNormalUserPostChangeContent bool
	outerLoop:
		for _, contentItem := range postInfo.ContentLanguages {
			for _, content := range req.Contents {
				if contentItem.Id == content.Id {
					// 先判断长度
					if len(contentItem.PicUrls) != len(content.PicUrls) {
						hasNormalUserPostChangeContent = true
						break outerLoop
					}

					picUrlMap := make(map[string]struct{})
					for _, item := range contentItem.PicUrls {
						picUrlMap[item] = struct{}{}
					}
					for _, item := range content.PicUrls {
						if _, exists := picUrlMap[item]; !exists {
							hasNormalUserPostChangeContent = true
							break outerLoop
						}
					}
					// 先判断长度
					if len(content.Title) != len(contentItem.Title) || len(content.Content) != len(contentItem.Content) {
						hasNormalUserPostChangeContent = true
						break outerLoop
					}
					// 在判断内容
					if content.Title != contentItem.Title || content.Content != contentItem.Content {
						hasNormalUserPostChangeContent = true
						break outerLoop
					}
				}
			}
		}
		if hasNormalUserPostChangeContent {
			_, err = CreatePostAudit(c, post, postContents, req.Tags, isAudit, true)
			if err != nil {
				errs.NewCustomError(c, code.CreatePostAuditErr, "CreatePostAudit err: %v", err)
				return respData, err
			}
		}
		// 不需要审核的字段，直接更新生效
		if req.PlateId != postInfo.PlateId || newLanguage != postInfo.Language || req.NeedRefreshFriendCard > 0 || req.NeedRefreshGuildCard > 0 || req.RiskRemindType != postInfo.RiskRemindType || req.CreatorStatementType != postInfo.CreatorStatementType || req.AiContentType != postInfo.AiContentType || req.OriginalUrl != postInfo.OriginalUrl {
			if err = dao.UpdateNormalUserPost(post, postContents, postInfo.Language, newLanguage, postInfo.PlateId, req.PlateId, req.NeedRefreshFriendCard, req.NeedRefreshGuildCard); err != nil {
				go func(ctx context.Context, intlGameId string, err2 error) {
					newC := trpc.CloneContext(ctx)
					defer recovery.CatchGoroutinePanic(newC)
					common.ReportPostPublish(c, post, req.Tags, postContents, false, user, guildId, err2)
				}(c, gameId, err)
				return resp, err
			}
			// 更新es
		}
		UpdateNormalUserPostToSearch(post, postContents, req.Tags, postInfo.Language, newLanguage, hasNormalUserPostChangeContent)
		UpdatePostReportInfo(c, post, postContents, req.PlateId, newLanguage, req.Type, req.Tags)
	}

	go func(ctx context.Context, intlGameId string) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportPostPublish(c, post, req.Tags, postContents, false, user, guildId, nil)
	}(c, gameId)

	var postTagsIds []int64
	if len(postInfo.Tags) > 0 {
		for _, tag := range postInfo.Tags {
			postTagsIds = append(postTagsIds, tag.Id)
		}
	}
	// 创建tag-post关联关系
	if len(req.Tags) > 0 {
		err := PostUpdateTagBind(c, req.Tags, postTagsIds, postUuid)
		if err != nil {
			return resp, err
		}
	}
	//组装返回体
	respData.PostUuid = postUuid
	respData.IntlOpenid = intlOpenid
	respData.Type = req.Type
	respData.CanDelete = true //这是自己发布的动态所以都可以自己删除
	respData.GameId = gameId
	respData.AreaId = areaId
	respData.Tags = nil
	respData.User = formatted.ReturnDynamicProtoUserInfoFormatted(user.Format())
	respData.PlateId = req.PlateId
	respData.Language = post.Language
	respData.IsAudit = post.IsAudit
	respData.IsOfficial = post.IsOfficial
	respData.PublishOn = int32(post.PublishOn)

	for _, postContentItem := range postContents {
		go cache.RemovePostContentCache(c, postContentItem.PostUUID, postContentItem.Language)
	}
	go cache.DeletePostCache(context.Background(), post.PostUUID)
	return respData, nil
}

// PushPostToSecurityDetection 对发布的动态文字、图片内容进行安全审核
func PushPostToSecurityDetection(c context.Context, contents []*model.PostContent, intlOpenid string, username string, contentType int32, postUuid string, postAudit *model.PostAudit) {
	defer recovery.CatchGoroutinePanic(context.Background())
	accountInfo := security.AccountInfo{
		Account:  intlOpenid,
		RoleName: username,
		PlatId:   3,
	}
	// postAudit := &model.PostAudit{

	// 	PostUUID: postUuid,
	// }
	for _, content := range contents {
		var checkContent string
		if content.ContentSummary != "" {
			// 如果这个字段有值那就说明这个已经转换过的，不用二次转换
			checkContent = util.RemoveSymbol(content.ContentSummary)
		} else {
			// 兼容处理，如果前端富文本类型没有传抽离的文字内容，则获取富文本中的文字内容和富文本中的所有图片链接
			// 解析 HTML 文档
			if content.Content != "" {
				// 用于存储从文本字段抽离出来的图片链接、文本的数组、换行次数
				_, contentTexts, _, err := util.TrimHtmlLabel(content.Content)
				if err == nil {
					checkContent = strings.Join(contentTexts, " ")
				} else {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushPostToSecurityDetection | trim html label failed, err:%v, content: %s", err, content.Content)
					checkContent = content.Content
				}
			}
		}
		// 1.先检查文本内容
		postAudit.TextRiskLevel, postAudit.TextRiskType = security.PushTextToSecurityDetection(checkContent, content.Title, "", content.ID, 3001, accountInfo)
		// if contentType == constants.POST_TYPE_RICH_TEXT {
		// 	postAudit.TextRiskLevel, postAudit.TextRiskType = security.PushTextToSecurityDetection(checkContent, content.Title, "", content.ID, 3001, accountInfo)
		// }

		// 2.如果文本没问题，并且动态有图片，则走图片安全机审
		if len(content.PicUrls) == 0 {
			postAudit.PicRiskLevel = 1
			postAudit.PicRiskType = 100
		} else {
			if postAudit.TextRiskLevel == security.RISK_LEVLE_NORMAL {
				postAudit.PicRiskLevel, postAudit.PicRiskType = security.PushPicToSecurityDetection(strings.Split(content.PicUrls, ","), content.ID, 3002, accountInfo)
			}
		}
		// 判断机审状态,如果是审核通过的状态就直接通过，有问题就是转入人工审核
		if postAudit.TextRiskType == 100 && postAudit.TextRiskLevel == 1 && postAudit.PicRiskLevel == 1 && postAudit.PicRiskType == 100 {
			postAudit.MachineStatus = constants.ReviewStatusPass
		} else {
			postAudit.MachineStatus = constants.ReviewStatusReject
		}

		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("PushPostToSecurityDetection update audit info, data: %+v", postAudit)

		// 将机审结果入库
		if err := dao.UpdatePostAuditByID(postAudit); err != nil {
			errs.NewCustomError(c, code.UpdatePostAuditErr, "PushPostToSecurityDetection ds.UpdatePostAuditByID err: %v", err)
		}
		auditDoc := map[string]interface{}{
			"text_risk_level": postAudit.TextRiskLevel,
			"text_risk_type":  postAudit.TextRiskType,
			"pic_risk_level":  postAudit.PicRiskLevel,
			"pic_risk_type":   postAudit.PicRiskType,
			"machine_status":  postAudit.MachineStatus,
			"modified_on":     time.Now().Unix(),
		}

		// 等最新代码上之后，就不用再更新ElasticSearchSetting.TweetIndex这个索引了
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postAudit.PostUUID, auditDoc)
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetAuditIndex, fmt.Sprintf("%d", postAudit.ID), auditDoc)

		if postAudit.MachineStatus == constants.ReviewStatusPass {
			// 更新es的缓存
			dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)
			// 走cms的审核逻辑
			err := CMSReviewPost(c, &pb.CMSReviewPostReq{
				PostAuditIds: []int64{postAudit.ID},
				// PostUuids:    []string{postAudit.PostUUID},
				Type:       1,
				UpdateUser: "admin",
			}, true)
			if err != nil {
				errs.NewCustomError(c, code.UpdatePostAuditErr, "PushPostToSecurityDetection review post err: %v", err)
			}
		}
	}
}

// CreatePostAudit 创建审核记录
func CreatePostAudit(c context.Context, post *model.Post, postContents []*model.PostContent, tags []int64, isAudit int, isEdit bool) (*model.PostAudit, error) {
	// 先判断是否存在已有待审核记录，如果有则设置为“已失效”，重新生成帖子审核记录信息
	err := dao.ChangePostNotAuditIntoInvalid(post.PostUUID)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostAudit ChangePostNotAuditIntoInvalid err,post.PostUUID: [%s],err=(%v)", post.PostUUID, err)
		return nil, errs.NewCustomError(c, code.ChangePostNotAuditIntoInvalidError, "CreatePostAudit err.")
	}
	// 更新es
	doc := map[string]interface{}{
		"status":      constants.PostAuditChangeToInvalid,
		"modified_on": time.Now().Unix(),
	}
	boolQuery := es7.NewBoolQuery()
	contentUuidQuery := es7.NewTermQuery("post_uuid", post.PostUUID)
	contentTypeQuery := es7.NewTermQuery("status", constants.PostAuditUnHandler)
	boolQuery.Must(contentUuidQuery, contentTypeQuery)
	scritpStr := `
		ctx._source.status = params.status;
		ctx._source.modified_on = params.modified_on;
	`
	dao.EsUpdateDocByCondition(config.GetConfig().ElasticSearchSetting.TweetAuditIndex, boolQuery, scritpStr, doc)

	// 重新生成
	postAudit := &model.PostAudit{
		PostUUID:       post.PostUUID,
		IntlOpenid:     post.IntlOpenid,
		Status:         constants.PostAuditUnHandler,
		AuditOn:        0,
		AreaID:         post.AreaId,
		GameID:         post.GameId,
		MachineStatus:  0,
		Type:           post.Type,
		Language:       post.Language,
		PostActionType: constants.PostAuditActionAdd,
	}
	if isEdit {
		postAudit.PostActionType = constants.PostAuditActionEdit
	}
	if len(postContents) > 0 {
		postContent := postContents[0]
		postAudit.Platform = postContent.Platform
		postAudit.Title = postContent.Title
		postAudit.Content = postContent.Content
		postAudit.ContentSummary = postContent.ContentSummary
		postAudit.PicUrls = postContent.PicUrls
		postAudit.ExtInfo = postContent.ExtInfo
	}
	// 白名单用户直接生成审批通过的记录, 如果是官方帖子的话也需要把status = 2
	if isAudit == 1 || post.IsOfficial == 1 {
		postAudit.AuditOn = time.Now().Unix()
		postAudit.Status = constants.PostAuditPush
		// 机审状态，预防官方帖子不用处理直接发布
		postAudit.MachineStatus = 1
	}
	if err := dao.CreatePostAudit(postAudit); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostAudit CreatePostAudit err,post.PostUUID: [%v],err=(%v)", postAudit, err)
		return nil, errs.NewCustomError(c, code.CreatePostAuditErr, "CreatePostAudit err.")
	}

	if !(isAudit == 1 || post.IsOfficial == 1) {
		SetNeedAuditPostUUIDToCache(c, post.PostUUID, time.Now().Unix(), postAudit.ID)
	}
	PushPostAuditToES(post, postAudit, tags, isAudit)
	return postAudit, nil
}

// DeletePostAuditESData 删除审核记录
func DeletePostAuditESData(c context.Context, postUuid, auditIntroduce string) error {
	// 更新es
	doc := map[string]interface{}{
		"is_del":          1,
		"deleted_on":      time.Now().Unix(),
		"audit_introduce": auditIntroduce,
	}
	boolQuery := es7.NewBoolQuery()
	contentUuidQuery := es7.NewTermQuery("post_uuid", postUuid)
	boolQuery.Must(contentUuidQuery)
	scritpStr := `
		ctx._source.is_del = params.is_del;
		ctx._source.deleted_on = params.deleted_on;
		ctx._source.audit_introduce = params.audit_introduce;
	`
	dao.EsUpdateDocByCondition(config.GetConfig().ElasticSearchSetting.TweetAuditIndex, boolQuery, scritpStr, doc)

	return nil
}

func SetNeedAuditPostUUIDToCache(c context.Context, postUuid string, createdOn int64, postAuditID int64) {
	if postAuditID > 0 {
		postUuid = fmt.Sprintf("%s-%d", postUuid, postAuditID)
	}
	redisKey := cache.GetNeedAuditPostUUIDKey()
	var redisZ = &redis2.Z{
		Score:  float64(createdOn),
		Member: postUuid,
	}
	err := redis.GetClient().ZAdd(context.Background(), redisKey, redisZ).Err()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetNeedAuditPostUUIDToCache err,set need machine audit post uuids to cache, post_uuid: [%s], post create time: [%d],err=(%v)", postUuid, createdOn, err)
	}
}

// 提取并返回给定节点及其子节点的文本内容
func extractText(n *html.Node) string {
	if n.Type == html.TextNode {
		return n.Data
	}

	var buf bytes.Buffer
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		buf.WriteString(extractText(c))
	}
	return buf.String()
}

// 遍历节点，查找特定标签并提取内容
func traverse(n *html.Node, tagNames []string, imgLinks *[]string, texts *[]string, lineBreakCount *int) {
	if n.Type == html.ElementNode {
		// 查找指定标签
		for _, tagName := range tagNames {
			if n.Data == tagName {
				text := extractText(n)
				if strings.TrimSpace(text) != "" {
					*texts = append(*texts, strings.TrimSpace(text))
				}
			}
		}

		// 处理 img 标签
		if n.Data == "img" {
			for _, attr := range n.Attr {
				if attr.Key == "src" {
					// 解析url，判断是否是表情
					parse, err := url.Parse(attr.Val)
					if err == nil {
						queryParams := parse.Query()
						imageType := queryParams.Get("imgtype")
						if imageType == "emoji" {
							break
						}
					}
					*imgLinks = append(*imgLinks, attr.Val)
					break
				}
			}
		}

		if n.Data == "br" {
			*lineBreakCount++
		}
	}

	// 递归处理子节点
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		traverse(c, tagNames, imgLinks, texts, lineBreakCount)
	}
}

func DeletePost(c context.Context, postUUID string, intlOpenid string, delReason int32) (*model.Post, error) {
	post := &model.Post{}
	var err error

	userState, err := dao.GetUserStateByUserOpenid(intlOpenid)
	if err != nil {
		return post, errs.NewCustomError(c, code.GetUserInfoFailed, "DeletePost get user state err: %v", err)
	}

	post, err = dao.GetPost(postUUID)
	if err != nil {
		return post, errs.NewCustomError(c, code.GetPostsFailed, "DeletePost get post info err: %v", err)
	}
	// 获取是否是管理员
	admin := formatted.GetUserAdmin(userState.IntlOpenid)
	if post.IntlOpenid != userState.IntlOpenid && !admin {
		return post, errs.NewCustomError(c, code.DeleteFailedNotPermission, "DeletePost No permission to perform this request")
	}
	// 官方帖子只有自己才能删除
	if post.IntlOpenid != userState.IntlOpenid && post.IsOfficial != 0 {
		return post, errs.NewCustomError(c, code.DeleteFailedNotPermission, "DeletePost No permission to perform this request")
	}

	var delStatus = constants.UserDeleted
	if post.IntlOpenid != userState.IntlOpenid {
		delStatus = constants.CAdminDeleted
	}

	postLanguage := make([]string, 0)
	if post.IsOfficial != 1 {
		// 非官方帖子直接传入对应的语言删除，官方帖子直接删全部语言
		postLanguage = append(postLanguage, post.Language)
	}

	// 更新动态删除状态，更新审批记录
	err = dao.DeletePostInfoAndLanguage(postUUID, map[string]interface{}{
		"is_del":     1,
		"deleted_on": time.Now().Unix(),
		"del_reason": delReason,
		"del_type":   delStatus,
	}, postLanguage)

	if err != nil {
		go func(ctx context.Context, err2 error) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportPostDelete(c, intlOpenid, post, int(delStatus), err2)
		}(c, err)
		return post, errs.NewCustomError(c, code.DeletePostFailed, "DeletePostFailed Dynamic deletion failed, err: %v", err)
	}

	// 更新举报管理记录状态
	reportIndex := config.GetConfig().ElasticSearchSetting.ReportIndex
	const ReportContentTypePost int = 1 // 动态
	err = DeletePostCommentReport(c, reportIndex, ReportContentTypePost, postUUID, intlOpenid)
	if err != nil {
		log.ErrorContextf(c, "DeletePostCommentReport(post) error:%v, postUUID:%v", err, postUUID)
		return post, errs.NewCustomError(c, code.DeletePostFailed, "DeletePostCommentReport(post) failed, err: %v", err)
	}

	DeletePostAuditESData(c, postUUID, "user delete post")

	go func(ctx context.Context) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportPostDelete(c, intlOpenid, post, int(delStatus), nil)
	}(c)

	// todo 删除索引
	// DeleteSearchPost(post)
	// 更新es
	postDoc := map[string]interface{}{
		"is_del":          1,
		"deleted_on":      time.Now().Unix(),
		"audit_introduce": "user delete post",
		"del_reason":      delReason,
		"del_type":        delStatus,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUUID, postDoc)
	var userDoc = make(map[string]interface{})
	// 更新用户发布的动态数
	if post.IsAudit == 1 {
		userState.PostNum--
		if userState.PostNum < 0 {
			userState.PostNum = 0
		}
		userDoc["post_num"] = userState.PostNum
	}
	userState.AllPostNum--
	if userState.AllPostNum < 0 {
		userState.AllPostNum = 0
	}
	userDoc["all_post_num"] = userState.AllPostNum
	dao.UpdateUserPostNum(userState.IntlOpenid, userState.AllPostNum, userState.PostNum)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, userState.IntlOpenid, userDoc)
	}()
	tagIDs, err := dao.GetTagIdByPostUUID(postUUID)
	if err == nil {
		err = dao.BatchDecrTagsPostNum(tagIDs, 1)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeletePost BatchDecrTagsPostNum err: %v, postUUID: %s", err, postUUID)
		}
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeletePost GetTagIdByPostUUID err: %v, postUUID: %s", err, postUUID)
	}
	postHotRedisKey := cache.GetPostHotKey(post.GameId, post.AreaId)
	// 删除post redis中对应的postid
	redis.GetClient().ZRem(c, postHotRedisKey, post.PostUUID)
	// 删除动态下的评论, 24-10-22产品说不用删除评论
	//go dao.CommentDeleteByPostId(postUUID)
	// 删除用户收藏
	// 删除用户收藏
	go func(postUuid string) {
		defer recovery.CatchGoroutinePanic(context.Background())
		dao.PostCollectionDeleteByPostUuid(postUuid)
		DelOfficialPublishPostByCache(context.Background(), postUuid)
	}(postUUID)
	// 写入用户消息, 如果登录用户不是当前发帖者，就要发送站内信通知用户
	if intlOpenid != post.IntlOpenid {
		go writemessage.SetUserMessage(&model.Message{
			Type:                   constants.MsgTypeOfficialDeletePost,
			PostUUID:               post.PostUUID,
			GameID:                 post.GameId,
			AreaID:                 post.AreaId,
			ReceiverUserIntlOpenid: post.IntlOpenid,
			SenderUserIntlOpenid:   fmt.Sprintf("%s", post.GameId),
			ExtInfo:                fmt.Sprintf("{\"del_type\": %d, \"del_reason\": %d}", delStatus, delReason),
		}, post.IntlOpenid, constants.SiteMessageCount)
	}
	go cache.DeletePostCache(context.Background(), post.PostUUID)
	go cache.DeleteUserPostsCache(intlOpenid, "", 10)
	go cache.RemoveUserPostCacheKeys(post.IntlOpenid)
	// 删除redis的待审核数据
	if post.IsAudit == 2 {
		redis.GetClient().ZRem(context.Background(), cache.GetNeedAuditPostUUIDKey(), post.PostUUID)
	}
	return post, nil
}

func CreatePostBrowse(c context.Context, postUUID string) error {
	// 取出第一条数据，然后更新全部语言的浏览数
	postStats, err := dao.GetPostStatsByPostUuid(postUUID)
	if err != nil {
		return errs.NewCustomError(c, code.GetPostContentsFailed, "CreatePostBrowse |Failed to get dynamic stats details, postUUID: %s", postUUID)
	}
	// 更新Post浏览数
	postStats.BrowseCount++
	dao.UpdatePostStatsBrowse(postUUID, postStats.BrowseCount)

	// 更新ES
	hotNum := hotService.CalculatingPostHotNum(postStats)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())

		doc := map[string]interface{}{
			"browse_count": postStats.BrowseCount,
			"hot_num":      hotNum,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postStats.PostUUID, doc)

	}()

	return nil
}

func CreatePostForward(c context.Context, postUUID string, intlOpenid string) (int32, error) {
	// 加载Post
	post, err := dao.GetPost(postUUID)
	if err != nil {
		return 0, errs.NewCustomError(c, code.GetPostsFailed, "CreatePostForward |Failed to get dynamic details")
	}

	// 如果是未审批通过的，则只能自己转发
	if post.IsAudit == 2 && post.IntlOpenid != intlOpenid {
		return 0, errs.NewCustomError(c, code.CanNotForwardUnapprovedPost, "Failed to forward dynamic")
	}

	// 获取默认排序第一的数据，然后当前post_uuid下所有的转发数
	postStats, err := dao.GetPostStatsByPostUuid(postUUID)
	if err != nil {
		return 0, errs.NewCustomError(c, code.GetPostContentsFailed, "CreatePostForward |Failed to get dynamic content details")
	}

	// 判断当前用户今天是否分享过
	result, err := redis.GetClient().Get(c, cache.GetUserSharePostKey(intlOpenid, postUUID)).Result()
	if err == nil && result == "1" {
		return int32(postStats.ForwardCount), nil
	}
	// 更新Post浏览数
	postStats.ForwardCount++
	dao.UpdatePostStatsForward(postUUID, int32(postStats.ForwardCount))

	// todo 更新索引
	// PushPostToSearch(post)
	hotNum := hotService.CalculatingPostHotNum(postStats)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())

		doc := map[string]interface{}{
			"forward_count": postStats.ForwardCount,
			"hot_num":       hotNum,
		}

		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postStats.PostUUID, doc)

	}()
	_, todayEndTime := util.GetTodayStartAndEndTimestamp()
	redis.GetClient().SetNX(c, cache.GetUserSharePostKey(intlOpenid, postUUID), "1", time.Duration(todayEndTime-time.Now().Unix())*time.Second)

	return int32(postStats.ForwardCount), nil
}

// 更新es中post
func UpdatePostToSearch(post *model.Post, postContent []*model.PostContent, tags []int64, hotNum int64, deleteLanguage []string) {
	auditStatus := 1
	// 先定义这个机审和人审的状态，因为官方帖子不需要审核的话这两个机审状态也不需要赋值
	var machineStatus, artificialStatus int
	if post.IsAudit == 1 || post.IsOfficial == 1 {
		auditStatus = 2
		machineStatus = 1
	}
	allLanguage := make([]string, 0, len(postContent))
	data := map[string]interface{}{
		"plate_id":            post.PlateID,
		"type":                post.Type,
		"visibility":          post.Visibility,
		"tags":                tags,
		"socialmedia_post_id": post.SocialmediaPostId,
		"modified_on":         post.ModifiedOn,
		"hot_num":             hotNum,
		"is_audit":            post.IsAudit,
		"audit_status":        auditStatus,
		"is_official":         post.IsOfficial,
		"publish_on":          post.PublishOn,
		"created_on":          post.CreatedOn,
		"created_on_ms":       post.CreatedOnMs,
		"machine_status":      machineStatus,    //机审状态默认是0
		"artificial_status":   artificialStatus, // 人审的状态默认是0
	}
	for i, content := range postContent {
		if i == 0 {
			data["language"] = content.Language
			data["title"] = content.Title
			data["content"] = content.Content
			data["content_summary"] = content.ContentSummary
			data["pic_urls"] = content.PicUrls
			data["is_original"] = content.IsOriginal
			data["original_url"] = content.OriginalURL
			data["platform"] = content.Platform
			data["ext_info"] = content.ExtInfo
			data["guild_id"] = content.GuildId
		}
		allLanguage = append(allLanguage, content.Language)

		// 更新分词的内容
		var postContentStr string

		if content.ContentSummary != "" {
			postContentStr = content.ContentSummary
		} else {
			postContentStr = content.Content
		}
		if post.Language == "en" || post.Language == "ja" || post.Language == "ko" || post.Language == "zh" || post.Language == "zh-TW" {
			allContentLangKey := fmt.Sprintf("all_content_lang_%s", content.Language)
			data[allContentLangKey] = fmt.Sprintf("%s|||%s", content.Title, postContentStr)
		}

	}

	// 删除这条数据对应的参数
	if len(deleteLanguage) > 0 {
		for _, lang := range deleteLanguage {
			if post.Language == "en" || post.Language == "ja" || post.Language == "ko" || post.Language == "zh" || post.Language == "zh-TW" {
				allContentLangKey := fmt.Sprintf("all_content_lang_%s", lang)
				data[allContentLangKey] = ""
			}

		}
	}

	data["post_languages"] = allLanguage

	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, data)

}

// 更新es中post
func UpdateNormalUserPostToSearch(post *model.Post, postContent []*model.PostContent, tags []int64, oldLanguage, newLangauge string, hasChangeContent bool) {
	allLanguage := make([]string, 0, len(postContent))
	data := map[string]interface{}{
		"plate_id":    post.PlateID,
		"tags":        tags,
		"modified_on": post.ModifiedOn,
	}
	if hasChangeContent {
		data["audit_status"] = constants.PostAuditUnHandler
	}
	for i, content := range postContent {
		if i == 0 {
			data["language"] = content.Language
		}
		allLanguage = append(allLanguage, content.Language)

		if oldLanguage != newLangauge && newLangauge != "" {
			// 更新分词的内容
			var postContentStr string
			if content.ContentSummary != "" {
				postContentStr = content.ContentSummary
			} else {
				postContentStr = content.Content
			}
			if content.Language == "en" || content.Language == "ja" || content.Language == "ko" || content.Language == "zh" || content.Language == "zh-TW" {
				newAllContentLangKey := fmt.Sprintf("all_content_lang_%s", content.Language)
				data[newAllContentLangKey] = fmt.Sprintf("%s|||%s", content.Title, postContentStr)
				oldAllContentLangKey := fmt.Sprintf("all_content_lang_%s", oldLanguage)
				data[oldAllContentLangKey] = ""
			}
		}
	}

	data["post_languages"] = allLanguage
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, data)
}

// UpdatePostReportInfo 更新举报管理中帖子的记录
func UpdatePostReportInfo(ctx context.Context, post *model.Post, postContents []*model.PostContent, plateId int32, language string, subContentType int32, tags []int64) error {
	query := es7.NewBoolQuery()
	query = query.Filter(es7.NewTermQuery("content_type", post.Type),
		es7.NewTermQuery("content_uuid", post.PostUUID))
	src, _ := query.Source()
	log.InfoContextf(ctx, "UpdatePostReportInfo query json:%s", util.ToJson(src))

	scriptText := `
		ctx._source.plate_id = params.plate_id;
		ctx._source.language = params.language;
		ctx._source.sub_content_type = params.sub_content_type;
		ctx._source.title = params.title;
		ctx._source.content = params.content;
		ctx._source.pic_urls = params.pic_urls;
		ctx._source.tags = params.tags;
		ctx._source.ext_info = params.ext_info;
		ctx._source.platform = params.platform;
	`
	var title, content, extInfo, platform string
	var picUrls []string
	if len(postContents) > 0 {
		postContent := postContents[0]
		title = postContent.Title
		content = postContent.Content
		if len(postContent.PicUrls) > 0 {
			picUrls = strings.Split(postContent.PicUrls, ",")
		}
		extInfo = postContent.ExtInfo
		platform = postContent.Platform
	}
	params := map[string]interface{}{
		"plate_id":         plateId,
		"language":         language,
		"sub_content_type": subContentType,
		"title":            title,
		"content":          content,
		"pic_urls":         picUrls,
		"tags":             tags,
		"ext_info":         extInfo,
		"platform":         platform,
	}
	script := es7.NewScript(scriptText).Params(params)

	client := getEsClient()
	_, err := client.UpdateByQuery().Index(config.GetConfig().ElasticSearchSetting.ReportIndex).Query(query).Script(script).Do(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "UpdatePostReportInfo error:%v, src:%v", err, util.ToJson(src))
		return errs.NewCustomError(ctx, code.UpdatePostReportInfoError, "Failed to update post report info")
	}

	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.ReportIndex)
	return nil
}

// 推送post到es
func PushPostToSearch(post *model.Post, postContent []*model.PostContent, tags []int64, hotNum int64, isDemotion bool) {
	auditStatus := 1
	// 先定义这个机审和人审的状态，因为官方帖子不需要审核的话这两个机审状态也不需要赋值
	var machineStatus, artificialStatus int
	if post.IsAudit == 1 || post.IsOfficial == 1 {
		auditStatus = 2
		machineStatus = 1
	}
	// 处理用户intl_user_openid, 区别于intl_openid; ps: 这个intl_openid是一定会有的。
	var intlUserOpenid string
	if strings.Contains(post.IntlOpenid, "-") && len(strings.Split(post.IntlOpenid, "-")) > 1 {
		intlUserOpenid = strings.Split(post.IntlOpenid, "-")[1]
	}

	var allContentLangVal string
	var postContentStr string

	data := map[string]interface{}{
		"id":                  post.ID,
		"post_uuid":           post.PostUUID,
		"plate_id":            post.PlateID,
		"intl_openid":         post.IntlOpenid,
		"intl_user_openid":    intlUserOpenid,
		"type":                post.Type,
		"comment_count":       0,
		"collection_count":    0,
		"upvote_count":        0,
		"upvote_map":          "{}",
		"browse_count":        0,
		"forward_count":       0,
		"power_num":           0,
		"power_num_float":     1, // 权重值替换的字段
		"visibility":          post.Visibility,
		"is_top":              post.IsTop,
		"top_sort":            post.TopSort,
		"top_on":              post.TopOn,
		"tags":                tags,
		"socialmedia_post_id": post.SocialmediaPostId,
		"latest_replied_on":   post.LatestRepliedOn,
		"created_on":          post.CreatedOn,
		"modified_on":         post.ModifiedOn,
		"deleted_on":          0,
		"hot_num":             hotNum,
		"game_id":             post.GameId,
		"area_id":             post.AreaId,
		"is_audit":            post.IsAudit,
		"audit_status":        auditStatus,
		"is_del":              post.IsDel,
		"created_on_ms":       post.CreatedOnMs,
		"is_official":         post.IsOfficial,
		"publish_on":          post.PublishOn,
		"machine_status":      machineStatus,    //机审状态默认是0
		"artificial_status":   artificialStatus, // 人审的状态默认是0
		"is_hide":             0,
		"pic_risk_type":       0,
		"text_risk_type":      0,
		"pic_risk_level":      0,
		"text_risk_level":     0,
		"demotion_num":        0,
	}
	var languages []string
	for i, content := range postContent {
		if i == 0 {
			data["language"] = content.Language
			data["title"] = content.Title
			data["content"] = content.Content
			data["content_summary"] = content.ContentSummary
			data["pic_urls"] = content.PicUrls
			data["essence_on"] = content.EssenceOn
			data["is_essence"] = content.IsEssence
			data["is_original"] = content.IsOriginal
			data["original_url"] = content.OriginalURL
			data["platform"] = content.Platform
			data["ext_info"] = content.ExtInfo
			data["guild_id"] = content.GuildId
		}
		languages = append(languages, content.Language)

		if content.ContentSummary != "" {
			postContentStr = content.ContentSummary
		} else {
			postContentStr = content.Content
		}
		if post.Language == "en" || post.Language == "ja" || post.Language == "ko" || post.Language == "zh" || post.Language == "zh-TW" {
			// 目前只配置了这几种语言的字段，后续要新增再继续追加
			allContentLangKey := fmt.Sprintf("all_content_lang_%s", content.Language)
			allContentLangVal = fmt.Sprintf("%s|||%s", content.Title, postContentStr)
			data[allContentLangKey] = allContentLangVal
		}
	}
	data["post_languages"] = languages
	if isDemotion {
		data["demotion_num"] = config.GetConfig().Dynamic.DemotionNum
	}
	postDoc := make([]map[string]interface{}, 0)
	postDoc = append(postDoc, data)
	dao.EsBulkPushDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, "post_uuid", postDoc)
}

// 推送post审核记录到es
func PushPostAuditToES(post *model.Post, audit *model.PostAudit, tags []int64, isAudit int) {
	auditStatus := constants.PostAuditUnHandler
	// 先定义这个机审和人审的状态，因为官方帖子不需要审核的话这两个机审状态也不需要赋值
	var machineStatus, artificialStatus int
	if isAudit == 1 || post.IsOfficial == 1 {
		auditStatus = constants.PostAuditPush
		machineStatus = 1
	}
	// 处理用户intl_user_openid, 区别于intl_openid; ps: 这个intl_openid是一定会有的。
	var intlUserOpenid string
	if strings.Contains(post.IntlOpenid, "-") && len(strings.Split(post.IntlOpenid, "-")) > 1 {
		intlUserOpenid = strings.Split(post.IntlOpenid, "-")[1]
	}

	var allContentLangVal string
	var postContentStr string

	data := map[string]interface{}{
		"id":                audit.ID,
		"post_action_type":  audit.PostActionType,
		"post_uuid":         post.PostUUID,
		"plate_id":          post.PlateID,
		"intl_openid":       post.IntlOpenid,
		"intl_user_openid":  intlUserOpenid,
		"type":              post.Type,
		"language":          post.Language,
		"title":             audit.Title,
		"content":           audit.Content,
		"content_summary":   audit.ContentSummary,
		"pic_urls":          audit.PicUrls,
		"platform":          audit.Platform,
		"ext_info":          audit.ExtInfo,
		"tags":              tags,
		"created_on":        post.CreatedOn,
		"modified_on":       post.ModifiedOn,
		"is_del":            post.IsDel,
		"deleted_on":        0,
		"game_id":           post.GameId,
		"area_id":           post.AreaId,
		"audit_on":          audit.AuditOn,
		"status":            auditStatus,
		"machine_status":    machineStatus,    //机审状态默认是0
		"artificial_status": artificialStatus, // 人审的状态默认是0
		"pic_risk_type":     audit.PicRiskType,
		"text_risk_type":    audit.TextRiskType,
		"pic_risk_level":    audit.PicRiskLevel,
		"text_risk_level":   audit.TextRiskLevel,
	}
	if post.Language == "en" || post.Language == "ja" || post.Language == "ko" || post.Language == "zh" || post.Language == "zh-TW" {
		// 目前只配置了这几种语言的字段，后续要新增再继续追加
		allContentLangKey := fmt.Sprintf("all_content_lang_%s", post.Language)
		if audit.ContentSummary != "" {
			postContentStr = audit.ContentSummary
		} else {
			postContentStr = audit.Content
		}
		allContentLangVal = fmt.Sprintf("%s|||%s", audit.Title, postContentStr)
		data[allContentLangKey] = allContentLangVal
	}

	postDoc := make([]map[string]interface{}, 0)
	postDoc = append(postDoc, data)
	isSuccess, err := dao.EsBulkPushDoc(config.GetConfig().ElasticSearchSetting.TweetAuditIndex, "id", postDoc)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushPostAuditToES EsPutDoc error: %v, postDoc: %+v", err, postDoc)
		return
	}
	if !isSuccess {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushPostAuditToES EsBulkPushDoc fail; postDoc: %+v", postDoc)
		return
	}
}

func GetPostInfoByUUID(c context.Context, postUUID string) (*model.Post, error) {
	// 先获取缓存数据
	var postInfo *model.Post
	postInfoRedisKey := cache.GetOnePostBaseInfoKey(postUUID)
	postBaseCacheInfo, err := redis.GetClient().Get(c, postInfoRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postBaseCacheInfo), &postInfo)
		if err == nil {
			return postInfo, nil
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfoByUUID redis cache json unmarshal err: %v", err)
		}
	} else if !errors.Is(err, redisClient.Nil) {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfoByUUID redis err: %v", err)
		return nil, errs.NewCustomError(c, code.GetPostInfoByUUIDRedisError, "Failed to get dynamic base info")
	}

	postInfo, err = dao.GetPost(postUUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			postByte, err := json.Marshal(postInfo)
			if err == nil {
				redis.GetClient().SetEX(c, postInfoRedisKey, string(postByte), 2*time.Minute).Result()
			}
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfoByUUID db err: %v", err)
		return nil, errs.NewCustomError(c, code.GetPostInfoByUUIDError, "Failed to get dynamic base info")
	}
	postByte, err := json.Marshal(postInfo)
	if err == nil {
		redis.GetClient().SetEX(c, postInfoRedisKey, string(postByte), 2*time.Minute).Result()
	}
	return postInfo, nil
}

// 完成对应的任务
func CompletePostRelatedTask(ctx context.Context, actionType int32, language string, postUuid string) {
	// 拿出lip绑定的用户openid
	gameChannelid := metadatadecode.ParseHeaderCookie(ctx, "game_channelid")
	gameChannelidI, _ := strconv.ParseInt(gameChannelid, 10, 64)
	gameGameid := metadatadecode.ParseHeaderCookie(ctx, "game_gameid")
	gameGameidI, _ := strconv.ParseInt(gameGameid, 10, 64)
	gameOpenid := metadatadecode.ParseHeaderCookie(ctx, "game_openid")
	gameToken := metadatadecode.ParseHeaderCookie(ctx, "game_token")
	reqParam := &accountIntlGamePB.Openid2OpenidWithTokenReq{
		ToGameid:     30004,
		ToChannelid:  131,
		SrcChannelid: gameChannelidI,
		SrcGameid:    gameGameidI,
		SrcOpenid:    gameOpenid,
		SrcToken:     gameToken,
	}
	resp, err := intlgameProxy.Openid2OpenidWithToken(context.Background(), reqParam)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("CompletePostRelatedTask | GetIntlGameLoginErrorDataServiceErr failed. reqParam:%v, err:%v", reqParam, err)
	} else {
		if resp.Openid != "" {
			go func() {
				// 更新任务进度
				defer recovery.CatchGoroutinePanic(context.Background())
				_, err := pointsProxy.UserNikkeStandalonesiteEventSync(context.Background(), &pointsPB.UserNikkeStandalonesiteEventSyncReq{
					ActionType:     actionType,
					UserIntlOpenid: resp.Openid,
					Language:       language,
					PostUuid:       postUuid,
				})
				if err != nil {
					log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("CompletePostRelatedTask | UserNikkeStandalonesiteEventSync failed. actionType:%d, language:%s, err:%v", actionType, language, err)
				}
			}()
			return
		}
	}
	log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("CompletePostRelatedTask | current user not bind lip pass. reqParam: %v", reqParam)
}

// 完成对应的任务
func CheckIfUserHasPostedByNIKKEH5Page(ctx context.Context, req *pb.CheckIfUserHasPostedByNIKKEH5PageReq) (bool, error) {
	if req.IntlOpenid == "" {
		return false, nil
	}
	hasPosted, err := dao.CheckIfUserHasPostByChannel(req.IntlOpenid, 1)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("CheckIfUserHasPostedByNIKKEH5Page | CheckIfUserHasPostByChannel error. IntlOpenid:%s", req.IntlOpenid)
		return false, errs.NewCustomError(ctx, code.QueryUserPostFromInfoError, "Query the abnormal data associated with the user's post channel")
	}
	return hasPosted, nil
}

// 用户内部调用的post创建函数
func CreatePostInternal(ctx context.Context, req *pb.CreatePostNewReq, intlOpenid string, gameId string, areaId string, language string, withFrequencyLimit bool) (*pb.CreatePostRsp, error) {
	if language == "" {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.InvalidParams, "language Parameter error")
	}
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}

	var isOfficial bool
	// 验证是否是官方号，不是的话报错
	authType := formatted.GetUserAuth(intlOpenid)
	if authType == 1 || authType == 3 {
		isOfficial = true
	}

	// 如果板块是official,则必须是官方账号或者是机构账号
	plateInfo, err := dao.PlateGet(&dao.PlateConditions{
		Id: int64(req.PlateId),
	})
	if err != nil {
		return nil, errs.NewCustomError(ctx, code.InvalidParamsErr, "plate id failed")
	}
	if plateInfo.UniqueIdentifier == constants.PLATE_OFFICIAL && !isOfficial {
		return nil, errs.NewCustomError(ctx, code.NoPermission, "The current plate not permission")
	}

	if len(req.Contents) > 1 && !isOfficial {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.InvalidParams, "current user not official or agency")
	}

	// TODO 需要限制不同权限用户发布的板块动态权限是不一样的，例如普通用户只能发到nikkeart、outpost板块

	// 根据类型判断长度
	for i, content := range req.Contents {
		var checkContent = &CheckPostContent{
			Title:          content.Title,
			Content:        content.Content,
			ContentSummary: content.ContentSummary,
			PicUrls:        content.PicUrls,
			ContentType:    req.Type,
		}
		if err = CheckPostCreationReq(ctx, checkContent); err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.CheckPostCreationReq err: %v", err)
			return nil, err
		}
		req.Contents[i].ContentSummary = checkContent.ContentSummary
		req.Contents[i].PicUrls = checkContent.PicUrls

	}
	// 判断标签是否存在
	if len(req.Tags) > 0 {
		// 去重
		uniqueMap := make(map[int64]bool)
		var tagIdList []int64

		for _, tag := range req.Tags {
			if _, exists := uniqueMap[tag]; !exists {
				uniqueMap[tag] = true
				tagIdList = append(tagIdList, tag)
			}
		}
		req.Tags = tagIdList
		err := CheckTagIsExist(ctx, req.Tags, int64(req.PlateId))
		if err != nil {
			return nil, err
		}
	}

	isAudit := 2
	// 如果是白名单用户，则不需要走机审逻辑
	// exists, err := user.IsInUserWhiteList(c, gameId, areaId, myOpenid)
	// if err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePost err: %v\n", err)
	// 	return nil, err
	// }
	if isOfficial {
		isAudit = 1
	} else {
		if withFrequencyLimit {
			// 用户发布动态限频校验
			if errCode := CreatePostFrequencyLimitCheck(ctx, intlOpenid); errCode != nil {
				log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePostFrequencyLimitCheck err: %v", errCode)
				return nil, errCode
			}
		}
	}

	// 用于压测创建数据
	if trpc.GlobalConfig().Global.EnvName == "test" && strings.Contains(intlOpenid, "-100000") {
		isAudit = 1
	}

	post, err := CreatePostNew(ctx, intlOpenid, req, isAudit, "", gameId, areaId, isOfficial, language)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePostNew err: %v\n", err)
		// return nil, errs.NewCustomError(c, code.CreatePostFailed, "An error occurred while creating a post. Please try again later.")
		return nil, err
	}

	CreatePostSetUserLimit(ctx, intlOpenid)

	go user.DeleteUserInfoCache(ctx, intlOpenid)

	return &pb.CreatePostRsp{
		PostData: post,
	}, nil
}

// 更新帖子创作声明
func UpdatePostStatement(ctx context.Context, intlOpenid string, req *pb.UpdateStatementReq) error {
	if intlOpenid == "" || req.PostUuid == "" {
		return errs.NewCustomError(ctx, code.InvalidParamsErr, "intlOpenid is empty")
	}
	// 获取帖子内容详情
	postContents, err := dao.GetPostContentList(req.PostUuid, true)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("UpdatePostStatement GetPostContentList err: %v", err)
		return errs.NewCustomError(ctx, code.GetPostContentsFailed, "get post content failed")
	}
	if len(postContents) == 0 {
		return errs.NewCustomError(ctx, code.GetPostContentsFailed, "post content is empty")
	}
	// 判断权限
	isAdmin := formatted.GetUserAdmin(intlOpenid)
	if !isAdmin {
		return errs.NewCustomError(ctx, code.NoPermission, "no permission")
	}
	// 修改内容
	err = dao.UpdatePostStatement(req.PostUuid, constants.ECreatorStatementType(req.CreatorStatementType), constants.ERiskRemindType(req.RiskRemindType), constants.EAiContentType(req.AiContentType), req.OriginalUrl)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("UpdatePostStatement UpdatePostStatement err: %v", err)
		return errs.NewCustomError(ctx, code.ErrUpdatePostStatement, "update post statement failed")
	}
	cache.RemoveUserPostCacheKeys(intlOpenid)
	return nil
}
