package functional

import "gorm.io/gorm"

type Option func(*gorm.DB)

// WithGameId ...
func WithGameId(gameId string) Option {
	return func(db *gorm.DB) {
		db.Where("game_id = ? ", gameId)
	}
}

// WithActivityId ...
func WithActivityId(activityId string) Option {
	return func(db *gorm.DB) {
		db.Where("activity_id = ? ", activityId)
	}
}

// WithUserId ...
func WithUserId(userId string) Option {
	return func(db *gorm.DB) {
		db.Where("user_id = ? ", userId)
	}
}

// WithPrizeType ...
func WithPrizeType(prizeType int) Option {
	return func(db *gorm.DB) {
		db.Where("prize_type = ? ", prizeType)
	}
}

// WithLevel ...
func WithLevel(level int) Option {
	return func(db *gorm.DB) {
		db.Where("level = ? ", level)
	}
}

// WithStatus ...
func WithStatus(status int) Option {
	return func(db *gorm.DB) {
		db.Where("status = ? ", status)
	}
}

// WithSendPrizeTimesLessThan ...
func WithSendPrizeTimesLessThan(lessThan int) Option {
	return func(db *gorm.DB) {
		db.Where("send_prize_times < ?", lessThan)
	}
}

// WithLimit ...
func WithLimit(limit int) Option {
	return func(db *gorm.DB) {
		db.Limit(limit)
	}
}

// WithOffset ...
func WithOffset(offset int) Option {
	return func(db *gorm.DB) {
		db.Offset(offset)
	}
}

// WithUpdateTimeOrder ...
func WithUpdateTimeOrder(asc bool) Option {
	return func(db *gorm.DB) {
		if asc {
			db.Order("update_time asc")
			return
		} else {
			db.Order("update_time desc")
			return
		}
	}
}
