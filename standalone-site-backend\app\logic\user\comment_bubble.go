package user

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	user_pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	redisClient "github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

// 为用户新增评论气泡
func AddCommentBubbleToUser(intlOpenids []string, commentBubbleId int64) (err error) {
	ctx := context.Background()
	if commentBubbleId == 0 || len(intlOpenids) == 0 {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Warnf("AddCommentBubbleToUser failed, commentBubbleId or intlOpenid is empty")
		return
	}
	// 存入db
	commentBubbleInfo, err := dao.GetCommentBubbleInfo(commentBubbleId)
	secondDuration := commentBubbleInfo.SecondDuration
	if commentBubbleInfo.IsPermanent == 1 {
		secondDuration = 1500000000
	}
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("AddCommentBubbleToUser failed, get commentBubbleInfo failed, err: %v", err)
		return err
	}

	// 获取这批用户的已有气泡
	hasOwnedCommentBubbleList, err := dao.GetUserOwnedCommentBubbleByUserId(intlOpenids, commentBubbleId)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("AddCommentBubbleToUser failed, getUserOwnedCommentBubbleByUserId failed, err: %v", err)
		return err
	}
	userCommentBubbles := make([]*model.UserCommentBubble, 0)
	for _, intlOpenid := range intlOpenids {
		isFound := false
		for _, hasOwnedCommentBubble := range hasOwnedCommentBubbleList {
			if hasOwnedCommentBubble.IntlOpenid == intlOpenid {
				hasOwnedCommentBubble.ValidEndAt += int64(secondDuration)
				// 过期时间设置为2090-01-01  00:00:00
				if hasOwnedCommentBubble.ValidEndAt > 3786883200 {
					hasOwnedCommentBubble.ValidEndAt = 3786883200
				}

				hasOwnedCommentBubble.IsDel = 0
				// 如果用户已经拥有该气泡，累加生效时间
				userCommentBubbles = append(userCommentBubbles, hasOwnedCommentBubble)
				isFound = true
				break
			}
		}
		if !isFound {
			validEndAt := time.Now().Unix() + int64(secondDuration)
			if validEndAt > 3786883200 {
				validEndAt = 3786883200
			}
			userCommentBubble := &model.UserCommentBubble{
				IntlOpenid:      intlOpenid,
				CommentBubbleID: commentBubbleId,
				ValidEndAt:      validEndAt,
				ValidBeginAt:    time.Now().Unix(),
				GameID:          commentBubbleInfo.GameID,
				AreaID:          commentBubbleInfo.AreaID,
				Model: &model.Model{
					IsDel: 0,
				},
				IsWeared: 0, // 为找到的，直接设置为未佩戴
			}
			userCommentBubbles = append(userCommentBubbles, userCommentBubble)
		}
	}
	err = dao.BatchUpdateOrCreateUserCommentBubbles(userCommentBubbles)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("AddCommentBubbleToUser failed, BatchUpdateOrCreateUserCommentPendants failed, err: %v", err)
		return err
	}
	go BatchRemoveUserCommentBubbleCache(context.Background(), intlOpenids)
	return nil
}

// 获取用户评论气泡列表
func GetUserCommentBubbles(c context.Context, intlOpenid string, language string, nextPageCursor string, limit int64, previousPageCursor string) (rsp *user_pb.GetUserCommentBubbleListRsp, err error) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetUserCommentBubbles, intlOpenid: %s, langauge: %s, nextPageCursor:%s, limit:%d", intlOpenid, language, nextPageCursor, limit)
	rsp = &user_pb.GetUserCommentBubbleListRsp{
		UserCommentBubbles: make([]*user_pb.UserCommentBubbleItem, 0),
		PageInfo: &user_pb.UserPageInfo{
			NextPageCursor: "",
			IsFinish:       false,
		},
	}
	userCommentBubbles := make([]*user_pb.UserCommentBubbleItem, 0)
	rspUserCommentBubbles := make([]*user_pb.UserCommentBubbleItem, 0)
	if intlOpenid == "" {
		return rsp, nil
	}
	if language == "" {
		language = "en"
	}
	userCommentBubblesKey := cache.GetUserCommentBubblesListKey(intlOpenid, language, nextPageCursor, limit)
	if userCommentBubblesKey == "" {
		return rsp, errs.NewCustomError(c, code.CommentBubbleGetListFailed, "userCommentBubblesKey is empty")
	}
	userCommentBubblesListStr, rErr := redis.GetClient().Get(c, userCommentBubblesKey).Result()
	// log.Debugf("userAvaterPendantsListStr: %s", userAvaterPendantsListStr)
	if rErr == nil && userCommentBubblesListStr != "" {
		umErr := json.Unmarshal([]byte(userCommentBubblesListStr), &rsp)
		if umErr == nil {
			// 请求参数是否在缓存列表中
			isParamInCache, _ := IsRequestParamsInCommentBubbleCacheList(c, userCommentBubblesKey)
			// 评论气泡用户列表
			isInCahce, _ := IsUserInCommentBubbleCacheList(c, intlOpenid)
			// 同时在缓存中则返回
			if isInCahce && isParamInCache {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetUserCommentBubbles from cache: %s", userCommentBubblesKey)
				return rsp, nil
			}
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCommentBubbles failed, json.Unmarshal failed, err: %v", umErr)
		}
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetUserCommentBubbles failed, json.Unmarshal failed, err: %v", rErr)
	}
	// 获取当前用户未过期的气泡
	curTimestamp := time.Now().Unix()
	userValidCommentBubbles, err := dao.GetValidUserCommentBubbles(intlOpenid, curTimestamp)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCommentBubbles failed, dao.GetValidUserCommentBubbles failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.CommentBubbleGetListFailed, "get user comment bubble failed")
	}
	// 获取所有气泡数据
	allCommentBubbles, err := dao.GetAllCommentBubbleDataWithLanguage(true)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCommentBubbles failed, dao.GetValidUserCommentBubbles failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.CommentBubbleGetListFailed, "get user comment bubble failed")
	}

	var isWearedCommentBubble *user_pb.UserCommentBubbleItem
	var ownedCommentBubbles []*user_pb.UserCommentBubbleItem = make([]*user_pb.UserCommentBubbleItem, 0)
	var notOwnedCommentBubbles []*user_pb.UserCommentBubbleItem = make([]*user_pb.UserCommentBubbleItem, 0)
	intCacheTime := 24 * time.Hour
	// 已经排好序的气泡
	for _, allCommentBubbleItem := range allCommentBubbles {
		isUserAvaterFound := false
		curTitleLanguage := ""
		curConditionLanguage := ""
		for _, languageItem := range allCommentBubbleItem.Languages {
			if languageItem.Language == language {
				curTitleLanguage = languageItem.Title
				curConditionLanguage = languageItem.Condition
				break
			} else if languageItem.Language == "en" {
				// 默认英语
				if curConditionLanguage == "" && curTitleLanguage == "" {
					curTitleLanguage = languageItem.Title
					curConditionLanguage = languageItem.Condition
				}

			}
		}
		for _, userValidCommentBubbleItem := range userValidCommentBubbles {
			// 状态是已穿戴，且生效时间大于当前时间
			var isWeared int32
			if userValidCommentBubbleItem.IsWeared == 1 && userValidCommentBubbleItem.ValidEndAt > curTimestamp {
				isWeared = 1
			}
			if userValidCommentBubbleItem.CommentBubbleID == allCommentBubbleItem.ID {
				userValidDuration := userValidCommentBubbleItem.ValidEndAt - curTimestamp
				d := time.Duration(userValidDuration) * time.Second
				if d < intCacheTime {
					intCacheTime = d
				}
				// 找到当前用户对应的气泡
				isUserAvaterFound = true
				curLanguageAvaterItem := &user_pb.UserCommentBubbleItem{
					Id:            allCommentBubbleItem.ID,
					Title:         curTitleLanguage,
					CommentBubble: allCommentBubbleItem.Icon,
					BgColor:       allCommentBubbleItem.BgColor,
					Condition:     curConditionLanguage,
					JumpUrl:       allCommentBubbleItem.JumpURL,
					IsWeared:      isWeared,
					ValidEndAt:    userValidCommentBubbleItem.ValidEndAt,
					ValidBeginAt:  userValidCommentBubbleItem.ValidBeginAt,
					IsOwned:       1,
					IsPermanent:   allCommentBubbleItem.IsPermanent,
				}
				// 已穿戴单独存储
				if isWeared == 1 {
					// 已穿戴的气泡只有一条
					if isWearedCommentBubble != nil {
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetUserCommentBubbles failed, isWearedCommentBubble is not nil, intlOpenid: %d", intlOpenid)
						curLanguageAvaterItem.IsWeared = 0
						// return nil, errs.NewCustomError(c, code.CommentBubbleGetListFailed, "isWearedCommentBubble is not nil")
					} else {
						isWearedCommentBubble = curLanguageAvaterItem
					}

				} else if allCommentBubbleItem.IsDel == 0 {
					// 未穿戴且未被删除，添加
					ownedCommentBubbles = append(ownedCommentBubbles, curLanguageAvaterItem)
				}
				break
			}
		}
		// log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetUserCommentBubbles , intlOpenid: %s, allCommentBubbleItem: %v", intlOpenid, allCommentBubbleItem)
		// 未找到，且未被删除，添加
		if !isUserAvaterFound && allCommentBubbleItem.IsDel == 0 {
			notOwnedCommentBubbles = append(notOwnedCommentBubbles, &user_pb.UserCommentBubbleItem{
				Id:            allCommentBubbleItem.ID,
				Title:         curTitleLanguage,
				CommentBubble: allCommentBubbleItem.Icon,
				BgColor:       allCommentBubbleItem.BgColor,
				Condition:     curConditionLanguage,
				JumpUrl:       allCommentBubbleItem.JumpURL,
				IsWeared:      0,
				ValidEndAt:    0,
				ValidBeginAt:  0,
				IsOwned:       0,
				IsPermanent:   allCommentBubbleItem.IsPermanent,
			})
		}
	}
	// 组合数据
	if isWearedCommentBubble == nil {
		userCommentBubbles = append(ownedCommentBubbles, notOwnedCommentBubbles...)
	} else {
		userCommentBubbles = append([]*user_pb.UserCommentBubbleItem{isWearedCommentBubble}, append(ownedCommentBubbles, notOwnedCommentBubbles...)...)
	}
	// 返回分页数据
	// 解析出分页开始位置
	var startIndex int64
	if nextPageCursor != "" {
		index, err := util.DecryptPageCursorI(nextPageCursor)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCommentBubbles failed, util.DecryptPageCursorI failed, err: %v", err)
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
		}
		startIndex = index
	}

	if limit == 0 {
		rsp.PageInfo.NextPageCursor = ""
		rsp.PageInfo.IsFinish = true
		rsp.UserCommentBubbles = userCommentBubbles
	} else {
		isFinished := false
		for i, userCommentBubbleItem := range userCommentBubbles {
			i64 := int64(i)
			isFinished = i64 >= int64(len(userCommentBubbles))-1
			if i64 >= startIndex && i64 < startIndex+limit {
				rspUserCommentBubbles = append(rspUserCommentBubbles, userCommentBubbleItem)
			} else if i64 >= startIndex+limit {
				rsp.UserCommentBubbles = rspUserCommentBubbles
				break
			}
		}
		if isFinished {
			rsp.PageInfo.PreviousPageCursor = previousPageCursor
			rsp.PageInfo.NextPageCursor = ""
			rsp.PageInfo.IsFinish = true
			rsp.UserCommentBubbles = rspUserCommentBubbles
		} else {
			next, err := util.EncryptPageCursorI(startIndex + limit)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCommentBubbles failed, util.EncryptPageCursorI failed, err: %v", err)
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetUserCommentBubbles | Failed to create comments nextPageCursor")
			}
			rsp.PageInfo.PreviousPageCursor = previousPageCursor
			rsp.PageInfo.NextPageCursor = next
			rsp.PageInfo.IsFinish = false
		}
	}
	// 写入缓存
	go func(intCacheTime time.Duration) {
		defer recovery.CatchGoroutinePanic(context.Background())
		if data, jErr := json.Marshal(rsp); jErr == nil {
			redis.GetClient().SetEX(context.Background(), userCommentBubblesKey, data, intCacheTime+1*time.Minute)
			AddToUserInCommentBubbleCacheList(context.Background(), intlOpenid)
			AddCommentBubbleRequestParamsCacheList(context.Background(), userCommentBubblesKey)
		}
	}(intCacheTime)
	go RemoveCurWearCommentBubble(c, []string{intlOpenid})
	return rsp, nil
}

// 获取用户评论气泡列表v2版本
func GetUserCommentBubblesV2(c context.Context, intlOpenid string, language string, nextPageCursor string, limit int64, previousPageCursor string) (rsp *user_pb.GetUserCommentBubbleListRsp, err error) {
	rsp = &user_pb.GetUserCommentBubbleListRsp{
		UserCommentBubbles: make([]*user_pb.UserCommentBubbleItem, 0),
		PageInfo: &user_pb.UserPageInfo{
			NextPageCursor: "",
			IsFinish:       false,
		},
	}
	if intlOpenid == "" {
		return rsp, nil
	}
	userCommentBubbles := make([]*user_pb.UserCommentBubbleItem, 0)
	rspUserCommentBubbles := make([]*user_pb.UserCommentBubbleItem, 0)
	// 获取当前用户未过期的气泡
	curTimestamp := time.Now().Unix()
	// userValidCommentBubbles, err := dao.GetValidUserCommentBubbles(intlOpenid, curTimestamp)
	userValidCommentBubbles, err := getValidCommentBubblesOfUser(c, intlOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCommentBubblesV2 failed, dao.GetValidUserCommentBubbles failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.CommentBubbleGetListFailed, "get user comment bubble failed")
	}
	// 获取所有气泡数据
	// allCommentBubbles, err := dao.GetAllCommentBubbleDataWithLanguage()
	// 获取所有气泡数据
	allCommentBubbles, err := GetAllCommentBubbleList(c, true, false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCommentBubblesV2 failed, dao.GetValidUserCommentBubbles failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.CommentBubbleGetListFailed, "get user comment bubble failed")
	}
	// 获取当前穿戴的气泡
	curWearedCommentBubble, err := GetUserCurWearedCommentBubble(c, intlOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCommentBubblesV2 failed, GetUserCurWearedCommentBubble failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.CommentBubbleGetListFailed, "get user comment bubble failed")
	} else {
		if curWearedCommentBubble != nil {
			// 若当前穿戴已删除，加入列表
			if curWearedCommentBubble.IsDel == 1 {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Info("GetUserCommentBubblesV2 found weared comment deleted, id: %d, intlOpenid: %d", curWearedCommentBubble.ID, intlOpenid)
				allCommentBubbles = append([]*model.CommentBubble{curWearedCommentBubble}, allCommentBubbles...)
			}
		}
	}
	var isWearedCommentBubble *user_pb.UserCommentBubbleItem
	var ownedCommentBubbles []*user_pb.UserCommentBubbleItem = make([]*user_pb.UserCommentBubbleItem, 0)
	var notOwnedCommentBubbles []*user_pb.UserCommentBubbleItem = make([]*user_pb.UserCommentBubbleItem, 0)
	intCacheTime := 24 * time.Hour
	// 已经排好序的气泡
	for _, allCommentBubbleItem := range allCommentBubbles {
		isUserAvaterFound := false
		curTitleLanguage := ""
		curConditionLanguage := ""
		for _, languageItem := range allCommentBubbleItem.Languages {
			if languageItem.Language == language {
				curTitleLanguage = languageItem.Title
				curConditionLanguage = languageItem.Condition
				break
			} else if languageItem.Language == "en" {
				// 默认英语
				if curConditionLanguage == "" && curTitleLanguage == "" {
					curTitleLanguage = languageItem.Title
					curConditionLanguage = languageItem.Condition
				}

			}
		}
		for _, userValidCommentBubbleItem := range userValidCommentBubbles {
			// 状态是已穿戴，且生效时间大于当前时间
			var isWeared int32
			if userValidCommentBubbleItem.IsWeared == 1 && userValidCommentBubbleItem.ValidEndAt > curTimestamp {
				isWeared = 1
			}
			if userValidCommentBubbleItem.CommentBubbleID == allCommentBubbleItem.ID {
				userValidDuration := userValidCommentBubbleItem.ValidEndAt - curTimestamp
				d := time.Duration(userValidDuration) * time.Second
				if d < intCacheTime {
					intCacheTime = d
				}
				// 找到当前用户对应的气泡
				isUserAvaterFound = true
				curLanguageAvaterItem := &user_pb.UserCommentBubbleItem{
					Id:            allCommentBubbleItem.ID,
					Title:         curTitleLanguage,
					CommentBubble: allCommentBubbleItem.Icon,
					BgColor:       allCommentBubbleItem.BgColor,
					Condition:     curConditionLanguage,
					JumpUrl:       allCommentBubbleItem.JumpURL,
					IsWeared:      isWeared,
					ValidEndAt:    userValidCommentBubbleItem.ValidEndAt,
					ValidBeginAt:  userValidCommentBubbleItem.ValidBeginAt,
					IsOwned:       1,
					IsPermanent:   allCommentBubbleItem.IsPermanent,
				}
				// 已穿戴单独存储
				if isWeared == 1 {
					// 已穿戴的气泡只有一条
					if isWearedCommentBubble != nil {
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetUserCommentBubbles failed, isWearedCommentBubble is not nil, isWearedCommentBubble id:%d, cur id: %d intlOpenid: %s", isWearedCommentBubble.Id, curLanguageAvaterItem.Id, intlOpenid)
						curLanguageAvaterItem.IsWeared = 0
						// return nil, errs.NewCustomError(c, code.CommentBubbleGetListFailed, "isWearedCommentBubble is not nil")
					} else {
						isWearedCommentBubble = curLanguageAvaterItem
					}

				} else if allCommentBubbleItem.IsDel == 0 {
					// 未穿戴且未被删除，添加
					ownedCommentBubbles = append(ownedCommentBubbles, curLanguageAvaterItem)
				}
				break
			}
		}
		// log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetUserCommentBubblesV2 , intlOpenid: %s, allCommentBubbleItem: %v", intlOpenid, allCommentBubbleItem)
		// 未找到，且未被删除，添加
		if !isUserAvaterFound && allCommentBubbleItem.IsDel == 0 {
			notOwnedCommentBubbles = append(notOwnedCommentBubbles, &user_pb.UserCommentBubbleItem{
				Id:            allCommentBubbleItem.ID,
				Title:         curTitleLanguage,
				CommentBubble: allCommentBubbleItem.Icon,
				BgColor:       allCommentBubbleItem.BgColor,
				Condition:     curConditionLanguage,
				JumpUrl:       allCommentBubbleItem.JumpURL,
				IsWeared:      0,
				ValidEndAt:    0,
				ValidBeginAt:  0,
				IsOwned:       0,
				IsPermanent:   allCommentBubbleItem.IsPermanent,
			})
		}
	}
	// 组合数据
	if isWearedCommentBubble == nil {
		userCommentBubbles = append(ownedCommentBubbles, notOwnedCommentBubbles...)
	} else {
		userCommentBubbles = append([]*user_pb.UserCommentBubbleItem{isWearedCommentBubble}, append(ownedCommentBubbles, notOwnedCommentBubbles...)...)
	}
	// 返回分页数据
	// 解析出分页开始位置
	var startIndex int64
	if nextPageCursor != "" {
		index, err := util.DecryptPageCursorI(nextPageCursor)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCommentBubblesV2 failed, util.DecryptPageCursorI failed, err: %v", err)
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
		}
		startIndex = index
	}

	if limit == 0 {
		rsp.PageInfo.NextPageCursor = ""
		rsp.PageInfo.IsFinish = true
		rsp.UserCommentBubbles = userCommentBubbles
	} else {
		isFinished := false
		for i, userCommentBubbleItem := range userCommentBubbles {
			i64 := int64(i)
			isFinished = i64 >= int64(len(userCommentBubbles))-1
			if i64 >= startIndex && i64 < startIndex+limit {
				rspUserCommentBubbles = append(rspUserCommentBubbles, userCommentBubbleItem)
			} else if i64 >= startIndex+limit {
				rsp.UserCommentBubbles = rspUserCommentBubbles
				break
			}
		}
		if isFinished {
			rsp.PageInfo.PreviousPageCursor = previousPageCursor
			rsp.PageInfo.NextPageCursor = ""
			rsp.PageInfo.IsFinish = true
			rsp.UserCommentBubbles = rspUserCommentBubbles
		} else {
			next, err := util.EncryptPageCursorI(startIndex + limit)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCommentBubblesV2 failed, util.EncryptPageCursorI failed, err: %v", err)
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetUserCommentBubblesV2 | Failed to create comments nextPageCursor")
			}
			rsp.PageInfo.PreviousPageCursor = previousPageCursor
			rsp.PageInfo.NextPageCursor = next
			rsp.PageInfo.IsFinish = false
		}
	}
	return rsp, nil
}

// 获取用户有效的气泡
func getValidCommentBubblesOfUser(c context.Context, intlOpenid string) (validCommentBubbles []*model.UserCommentBubble, err error) {
	curTimestamp := time.Now().Unix()
	validCommentBubbles = make([]*model.UserCommentBubble, 0)
	// 1.先从换从中获取
	userValidCommentBubblesCachekey := cache.GetUserValidCommentBubblesCacheKey(intlOpenid)
	userValidCommentBubblesCacheStr, err := redis.GetClient().Get(c, userValidCommentBubblesCachekey).Result()
	if err == nil && userValidCommentBubblesCacheStr != "" {
		umErr := json.Unmarshal([]byte(userValidCommentBubblesCacheStr), &validCommentBubbles)
		if umErr == nil {
			return validCommentBubbles, nil
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getValidCommentBubblesOfUser failed, json.Unmarshal failed, intlOpenid: %s err: %v", intlOpenid, umErr)
		}
	} else {
		if err != nil && !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getValidCommentBubblesOfUser failed, redis.GetClient().Get failed,key: %s, err: %v", userValidCommentBubblesCachekey, err)
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("getValidCommentBubblesOfUser from cache failed,key: %s, err: %v", userValidCommentBubblesCachekey, err)
	}
	// 2.从db中获取
	validCommentBubbles, err = dao.GetValidUserCommentBubbles(intlOpenid, curTimestamp)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getValidCommentBubblesOfUser failed, dao.GetValidUserCommentBubbles failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.CommentBubbleGetListFailed, "get user comment bubble failed")
	}
	// 3.设置缓存
	var minValidateEndTime = time.Now().Unix()
	minValidateEndTime = 3786883200

	var cacheTime = time.Duration(24 * time.Hour)
	if len(validCommentBubbles) > 0 {
		for _, validCommentBubble := range validCommentBubbles {
			if validCommentBubble.ValidEndAt < minValidateEndTime {
				minValidateEndTime = validCommentBubble.ValidEndAt
			}
		}
	}
	// 获取最小的有效截止时间: 最多 24 小时 最少 1分钟
	diffTime := minValidateEndTime - curTimestamp
	if diffTime > 60 && diffTime <= 24*60*60 {
		cacheTime = time.Duration(diffTime) * time.Second
	} else if diffTime > 0 && diffTime > 24*60*60 {
		cacheTime = 24 * time.Hour
	} else {
		cacheTime = 1 * time.Minute
	}
	cacheStr, mErr := json.Marshal(validCommentBubbles)
	if mErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getValidCommentBubblesOfUser failed, json.Marshal failed, intlOpenid: %s err: %v", intlOpenid, mErr)
	} else {
		err = redis.GetClient().Set(c, userValidCommentBubblesCachekey, string(cacheStr), cacheTime).Err()
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getValidCommentBubblesOfUser failed, redis.GetClient().Set failed,key: %s, val: %s, err: %v", userValidCommentBubblesCachekey, string(cacheStr), err)
		}
	}
	return validCommentBubbles, err
}

// 设置用户气泡
func SetUserCommentBubbles(c context.Context, intlOpenid string, commentBubbleId int64, setWearStatus int32) (err error) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SetUserCommentBubbles, intlOpenid: %s, commentBubbleId: %d,setWearStatus: %d", intlOpenid, commentBubbleId, setWearStatus)
	// 取消穿戴
	if setWearStatus == 0 {
		err = dao.CancleWearUserCommentBubble(intlOpenid, commentBubbleId)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetUserCommentBubbles failed, dao.CancleWearUserCommentBubble failed, err: %v", err)
			return errs.NewSystemError(c, errs.ErrorTypeMysql, code.SetCommentBubbleToUserFailed, "set comment bubble failed")
		}
	} else {
		// 获取用户头像
		usrCommentBubble, err := dao.GetUserCommentBubble(intlOpenid, commentBubbleId)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetUserCommentBubbles failed, dao.GetUserCommentBubble failed, err: %v", err)
			return errs.NewSystemError(c, errs.ErrorTypeMysql, code.SetCommentBubbleToUserFailed, "get user comment bubble failed")
		}
		if usrCommentBubble == nil {
			return errs.NewCustomError(c, code.SetCommentBubbleToUserFailed, "comment bubble not found")
		}
		// 未获得气泡
		if usrCommentBubble.ValidEndAt < time.Now().Unix() {
			return errs.NewCustomError(c, code.CommentBubbleNotAvalible, "comment bubble is expired")
		}
		// 先更新db
		_, err = dao.SetWearUserCommentBubble(intlOpenid, commentBubbleId)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetUserCommentBubbles failed, dao.SetWearUserCommentBubble failed, err: %v", err)
			return errs.NewSystemError(c, errs.ErrorTypeMysql, code.SetCommentBubbleToUserFailed, "set comment bubble failed")
		}
	}

	// 清理缓存
	cacheKey := cache.GetUserWearedCommentBubbleKey(intlOpenid)
	redis.GetClient().Del(c, cacheKey)
	go BatchRemoveUserCommentBubbleCache(context.Background(), []string{intlOpenid})
	// 设置成功，清空缓存数据
	return nil
}

// 获取用户当前穿戴的气泡
func GetUserCurWearedCommentBubble(c context.Context, intlOpenid string) (userCommentBubble *model.CommentBubble, err error) {
	if intlOpenid == "" {
		return nil, nil
	}
	intlOpenidsKey := cache.GetUserWearCommentBubbleIntlOpenidsKey()
	// 先从缓存中获取
	userWearedCommentBubbleKey := cache.GetUserWearedCommentBubbleKey(intlOpenid)
	commentBubbleStr, err := redis.GetClient().Get(c, userWearedCommentBubbleKey).Result()
	if err == nil {
		// 没有穿戴
		if commentBubbleStr == "" {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedCommentBubble not wear, intlOpenid: %s, commentBubbleStr: %s", intlOpenid, commentBubbleStr)
			return nil, nil
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetUserCurWearedCommentBubble from cache success, intlOpenid: %s, commentBubbleStr: %s", intlOpenid, commentBubbleStr)
		umErr := json.Unmarshal([]byte(commentBubbleStr), &userCommentBubble)
		if umErr == nil {
			redis.GetClient().SAdd(c, intlOpenidsKey, intlOpenid)
			return userCommentBubble, nil
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedCommentBubble failed, json.Unmarshal failed, intlOpenid: %s err: %v", intlOpenid, umErr)
			return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetCurWearedCommentBubbleError, "get comment bubble failed")
		}
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetUserCurWearedCommentBubble failed, redis.GetClient().Get failed, err: %v", err)
	}
	// db获取
	userWearedCommentBubble, err := dao.GetWearedUserCommentBubble(intlOpenid)
	if err != nil {
		// 没有有效穿戴
		if errors.Is(err, gorm.ErrRecordNotFound) {
			redis.GetClient().SetNX(c, userWearedCommentBubbleKey, "", 2*time.Minute)
			return nil, nil
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedCommentBubble failed, dao.GetWearedUserCommentBubble failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetCurWearedCommentBubbleError, "get comment bubble failed")
	}
	commentBubble, err := dao.GetCommentBubbleInfo(userWearedCommentBubble.CommentBubbleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			redis.GetClient().SetNX(c, userWearedCommentBubbleKey, "", 2*time.Minute)
			return nil, nil
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedCommentBubble failed, dao.GetCommentBubbleInfo failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetCommentBubbleError, "get comment bubble failed")
	}

	commentBubbleByte, err := json.Marshal(commentBubble)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedCommentBubble failed, json.Marshal failed, intlOpenid: %s err: %v", intlOpenid, err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetCurWearedCommentBubbleError, "get comment bubble failed")
	}
	cacheTime := int64(userWearedCommentBubble.ValidEndAt - time.Now().Unix())
	if cacheTime <= 0 {
		cacheTime = 2 * 60
	} else if cacheTime > 24*60*60 {
		cacheTime = 24 * 60 * 60
	}
	_, err = redis.GetClient().SetEX(c, userWearedCommentBubbleKey, string(commentBubbleByte), time.Duration(cacheTime)*time.Second).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedCommentBubble failed, redis.GetClient().SetEX failed, err: %v", err)
	} else {
		_, err := redis.GetClient().SAdd(c, intlOpenidsKey, intlOpenid).Result()
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedCommentBubble failed, redis.GetClient().SAdd failed,intlOpenidsKey: %s, intlOpenid%s, err: %v", intlOpenidsKey, intlOpenid, err)
		}
	}

	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedCommentBubble success, intlOpenid: %s, commentBubble: %s, id: %d", intlOpenid, string(commentBubbleByte), commentBubble.ID)
	return commentBubble, nil
}

// 清除用户气泡数据
func BatchRemoveUserCommentBubbleCache(c context.Context, userIntlOpenids []string) {
	c = context.Background()
	defer recovery.CatchGoroutinePanic(c)
	for _, userIntlOpenid := range userIntlOpenids {
		userOpenidsCommentBubbleKey := cache.GetUserIntlOpenidsInCommentBubbleCacheKey()
		redis.GetClient().SRem(c, userOpenidsCommentBubbleKey, userIntlOpenid)
		// 用户气泡关系数据
		userValidCommentBubblesCachekey := cache.GetUserValidCommentBubblesCacheKey(userIntlOpenid)
		redis.GetClient().Del(c, userValidCommentBubblesCachekey)
	}

}

// 用户是否在气泡缓存列表中
func IsUserInCommentBubbleCacheList(c context.Context, intlOpenid string) (isInCache bool, err error) {
	// 先从缓存中获取
	userOpenidsCommentBubbleKey := cache.GetUserIntlOpenidsInCommentBubbleCacheKey()
	isInCache, err = redis.GetClient().SIsMember(c, userOpenidsCommentBubbleKey, intlOpenid).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("IsUserInCommentBubbleCacheList failed, redis.GetClient().SIsMember failed, err: %v", err)
		return false, err
	}
	return isInCache, nil
}

// 气泡请求列表参数是否在缓存列表中
func IsRequestParamsInCommentBubbleCacheList(c context.Context, cacheKey string) (isInCache bool, err error) {
	userOpenidsCommentBubbleKey := cache.GetUserCommentBubbleListCacheParamsCacheKey()
	isInCache, err = redis.GetClient().SIsMember(c, userOpenidsCommentBubbleKey, cacheKey).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("IsRequestParamsInCommentBubbleCacheList failed, redis.GetClient().SIsMember failed, err: %v", err)
		return false, err
	}
	return isInCache, nil
}

// 将用户添加到缓存列表
func AddToUserInCommentBubbleCacheList(c context.Context, intlOpenid string) {
	userOpenidsCommentBubbleKey := cache.GetUserIntlOpenidsInCommentBubbleCacheKey()
	redis.GetClient().SAdd(c, userOpenidsCommentBubbleKey, intlOpenid)
}

// 将请求参数添加到缓存列表
func AddCommentBubbleRequestParamsCacheList(c context.Context, cacheKey string) {
	userOpenidsCommentBubbleKey := cache.GetUserCommentBubbleListCacheParamsCacheKey()
	redis.GetClient().SAdd(c, userOpenidsCommentBubbleKey, cacheKey)
}

func RemoveCurWearCommentBubble(c context.Context, intlOpenids []string) {
	defer recovery.CatchGoroutinePanic(c)
	for _, intlOpenid := range intlOpenids {
		userWearedCommentBubbleKey := cache.GetUserWearedCommentBubbleKey(intlOpenid)
		redis.GetClient().Del(c, userWearedCommentBubbleKey)
	}
}

// 清除所有用户的缓存
func RemoveAllUserCommentBubbleCache(c context.Context) {
	allDataKey := cache.GetUserIntlOpenidsInCommentBubbleCacheKey()
	redis.GetClient().Del(c, allDataKey)
	requestParamsKey := cache.GetUserCommentBubbleListCacheParamsCacheKey()
	redis.GetClient().Del(c, requestParamsKey)
	//
	allCommentBubbleListCacheKeyFF := cache.GetAllCommentBubbleListCacheKey(false, false)
	allCommentBubbleListCacheKeyFT := cache.GetAllCommentBubbleListCacheKey(false, true)
	allCommentBubbleListCacheKeyTF := cache.GetAllCommentBubbleListCacheKey(true, false)
	allCommentBubbleListCacheKeyTT := cache.GetAllCommentBubbleListCacheKey(true, true)
	redis.GetClient().Del(c, allCommentBubbleListCacheKeyFF, allCommentBubbleListCacheKeyFT, allCommentBubbleListCacheKeyTF, allCommentBubbleListCacheKeyTT)
	// 用户穿戴数拒
	intlOpenidsKey := cache.GetUserWearCommentBubbleIntlOpenidsKey()
	members, err := redis.GetClient().SMembers(c, intlOpenidsKey).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveAllUserAvatarPendantCache failed, redis.GetClient().SMembers failed, err: %v", err)
		return
	}
	RemoveCurWearCommentBubble(c, members)
	redis.GetClient().Del(c, intlOpenidsKey)
}

// 获取所有气泡数据
func GetAllCommentBubbleList(ctx context.Context, withLanguage bool, withDeleted bool) (commentBubbles []*model.CommentBubble, err error) {
	commentBubbles = []*model.CommentBubble{}
	// 1. 先从cache中获取
	cacheKey := cache.GetAllCommentBubbleListCacheKey(withDeleted, withLanguage)
	if cacheKey == "" {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllCommentBubbleList cacheKey is empty")
		// return
	} else {
		result, err := redis.GetClient().Get(ctx, cacheKey).Result()
		if err != nil && !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllCommentBubbleList error: %s", err.Error())
		} else {
			err := json.Unmarshal([]byte(result), &commentBubbles)
			if err != nil {
				log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllCommentBubbleList unmarshal error content: %s, err: %s", result, err.Error())
			} else {
				commentBubbles = commentBubbles
				return commentBubbles, nil
			}
		}
	}
	var commentBubblesDB = []*model.CommentBubble{}
	// 2. DB中获取
	if withLanguage {
		commentBubblesDB, err = dao.GetAllCommentBubbleDataWithLanguage(withDeleted)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllCommentBubbleList error: %s", err.Error())
			return commentBubbles, err
		}
	} else {
		commentBubblesDB, err = dao.GetAllCommentBubbleDataWithoutLanguage(withDeleted)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllCommentBubbleList error: %s", err.Error())
			return commentBubbles, err
		}
	}
	// 3. 写入缓存
	if cacheKey != "" {
		commentBubblesStr, err := json.Marshal(commentBubblesDB)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllCommentBubbleList unmarshal error: %s", err.Error())
		} else {
			redis.GetClient().Set(ctx, cacheKey, string(commentBubblesStr), 10*time.Minute)
		}
	}
	return commentBubblesDB, nil
}
