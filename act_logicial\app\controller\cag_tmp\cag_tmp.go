package cag_tmp

import (
	"context"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_cag_tmp"
	logic "trpc.act.logicial/app/logic/cag_tmp"
)

// CagTmpImpl cag 临时玩法
type CagTmpImpl struct {
	pb.UnimplementedCagTmp
}

// ClaimCreateRolePrize 领取创建角色奖励
func (s *CagTmpImpl) ClaimCreateRolePrize(ctx context.Context, req *pb.ClaimCreateRolePrizeReq) (
	*pb.ClaimCreateRolePrizeRsp, error) {
	rsp, err := logic.ClaimCreateRolePrize(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ClaimLevelPrize 领取等级奖励
func (s *CagTmpImpl) ClaimLevelPrize(ctx context.Context, req *pb.ClaimLevelPrizeReq) (
	*pb.ClaimLevelPrizeRsp, error) {
	rsp, err := logic.ClaimLevelPrize(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetLevelPrizeStatus 查询等级奖励获取状态
func (s *CagTmpImpl) GetLevelPrizeStatus(ctx context.Context, req *pb.GetLevelPrizeStatusReq) (
	*pb.GetLevelPrizeStatusRsp, error) {
	rsp, err := logic.GetLevelPrizeStatus(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetCreateRolePrizeStatus 查询创建角色奖励获取状态
func (s *CagTmpImpl) GetCreateRolePrizeStatus(ctx context.Context, req *pb.GetCreateRolePrizeStatusReq) (
	*pb.GetCreateRolePrizeStatusRsp, error) {
	rsp, err := logic.GetCreateRolePrizeStatus(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ResendCagRoleLevePrize cag角色等级奖励重试发放
func (s *CagTmpImpl) ResendCagRoleLevePrize(ctx context.Context, req *pb.ResendCagRoleLevePrizeReq) (
	*pb.ResendCagRoleLevePrizeRsp, error) {
	rsp, err := logic.ResendCagRoleLevePrize(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}
