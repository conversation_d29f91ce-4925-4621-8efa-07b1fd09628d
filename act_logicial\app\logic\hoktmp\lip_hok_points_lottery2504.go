package hoktmp

import (
	"context"
	"encoding/json"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_hok_tmp"
	"github.com/spf13/cast"
	"strings"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/common"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/constant"
	hok_request "trpc.act.logicial/app/logic/hoktmp/hok_query_batch_request_pb"
	"trpc.act.logicial/app/logic/lip"
	"trpc.act.logicial/app/model/hok"
	"trpc.act.logicial/app/service"
	"trpc.act.logicial/app/util"
)

type PointTaskItem struct {
	//TargetID      string // 指标ID
	StartStopTime []string
}
type ClaimTaskPointRewardsRsp struct {
	SuccessTaskUUIDList []string
	HasPendingTasks     bool // 存在未发送任务
}

// RedDotLogicalJudgment 红点逻辑判断
func RedDotLogicalJudgment(ctx context.Context, req *pb.RedDotLogicalJudgmentReq) (bool, error) {
	account, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards get userAccount error:%v", err)
		return false, code.ErrUserNotLoginError
	}
	// LIP UID 转为 游戏UID
	hokOpenId, err := service.GetGameOpenId(ctx, constant.GAMEID_HOK)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards get game openid error:%v", err)
		return false, err
	}
	if hokOpenId == "" {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards hok openid is empty")
		return false, errs.NewCustomError(ctx, code.GameIDNotObtained, "GameID Not Obtained")
	}
	// 任务配置
	taskCompleteStatusService := config.GetConfig().LIPBandingHOK2504Conf
	loginTaskUUID := taskCompleteStatusService.CumulativeLoginTaskUUID
	addedFriendTaskUUID := taskCompleteStatusService.CumulativeAddedFriendTaskUUID
	OneTeamPlayEventTaskUUID := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID01
	threeTeamPlayTaskUUID := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID02
	fiveTeamPlayTaskUUID := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID03
	tenTeamPlayTaskUUID := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID04
	pointTaskItemMap := make(map[string]PointTaskItem, 6)
	pointTaskItemMap[loginTaskUUID] = PointTaskItem{
		StartStopTime: req.LoginStartStopTime,
	}
	pointTaskItemMap[addedFriendTaskUUID] = PointTaskItem{
		StartStopTime: req.AddedFriendStartStopTime,
	}
	pointTaskItemMap[OneTeamPlayEventTaskUUID] = PointTaskItem{
		StartStopTime: req.TeamPlayStartStopTime,
	}
	pointTaskItemMap[threeTeamPlayTaskUUID] = PointTaskItem{
		StartStopTime: req.TeamPlayStartStopTime,
	}
	pointTaskItemMap[fiveTeamPlayTaskUUID] = PointTaskItem{
		StartStopTime: req.TeamPlayStartStopTime,
	}
	pointTaskItemMap[tenTeamPlayTaskUUID] = PointTaskItem{
		StartStopTime: req.TeamPlayStartStopTime,
	}
	taskPointRewards, err := ClaimTaskPointRewards(ctx, account.GetIntlAccount().GetOpenId(), hokOpenId,
		pointTaskItemMap, false)
	if err != nil {
		return false, err
	}
	return taskPointRewards.HasPendingTasks, nil
}

// ClaimSingleTaskPointRewards 单个任务领取
func ClaimSingleTaskPointRewards(ctx context.Context, taskUUID string, startStopTime []string) ([]*pb.TaskStatusItem, error) {

	account, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	// LIP UID 转为 游戏UID
	hokOpenId, err := service.GetGameOpenId(ctx, constant.GAMEID_HOK)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards get game openid error:%v", err)
		return nil, err
	}
	if hokOpenId == "" {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards hok openid is empty")
		return nil, errs.NewCustomError(ctx, code.GameIDNotObtained, "GameID Not Obtained")
	}
	pointTaskItemMap := make(map[string]PointTaskItem, 6)
	pointTaskItemMap[taskUUID] = PointTaskItem{
		StartStopTime: startStopTime,
	}
	taskPointRewards, err := ClaimTaskPointRewards(ctx, account.GetIntlAccount().GetOpenId(), hokOpenId, pointTaskItemMap, true)
	if err != nil {
		return nil, err
	}
	items := make([]*pb.TaskStatusItem, 0)
	for _, v := range taskPointRewards.SuccessTaskUUIDList {
		items = append(items, &pb.TaskStatusItem{
			TaskUuid: v,
			Status:   1,
		})
	}
	return items, nil
}

// ClaimAllTaskPointRewards LIP 集群一键领取
func ClaimAllTaskPointRewards(ctx context.Context, req *pb.LIPBandingHokClaimAllTaskPointRewardsReq) (
	[]*pb.TaskStatusItem, error) {

	account, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	// LIP UID 转为 游戏UID
	hokOpenId, err := service.GetGameOpenId(ctx, constant.GAMEID_HOK)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards get game openid error:%v", err)
		return nil, err
	}
	if hokOpenId == "" {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards hok openid is empty")
		return nil, errs.NewCustomError(ctx, code.GameIDNotObtained, "GameID Not Obtained")
	}

	// 任务配置
	taskCompleteStatusService := config.GetConfig().LIPBandingHOK2504Conf
	loginTaskUUID := taskCompleteStatusService.CumulativeLoginTaskUUID
	addedFriendTaskUUID := taskCompleteStatusService.CumulativeAddedFriendTaskUUID
	teamPlayEventTaskUUID01 := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID01
	teamPlayEventTaskUUID02 := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID02
	teamPlayEventTaskUUID03 := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID03
	teamPlayEventTaskUUID04 := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID04
	pointTaskItemMap := make(map[string]PointTaskItem, 6)
	pointTaskItemMap[loginTaskUUID] = PointTaskItem{
		StartStopTime: req.LoginStartStopTime,
	}
	pointTaskItemMap[addedFriendTaskUUID] = PointTaskItem{
		StartStopTime: req.AddedFriendStartStopTime,
	}
	pointTaskItemMap[teamPlayEventTaskUUID01] = PointTaskItem{
		StartStopTime: req.TeamPlayStartStopTime,
	}
	pointTaskItemMap[teamPlayEventTaskUUID02] = PointTaskItem{
		StartStopTime: req.TeamPlayStartStopTime,
	}
	pointTaskItemMap[teamPlayEventTaskUUID03] = PointTaskItem{
		StartStopTime: req.TeamPlayStartStopTime,
	}
	pointTaskItemMap[teamPlayEventTaskUUID04] = PointTaskItem{
		StartStopTime: req.TeamPlayStartStopTime,
	}
	taskPointRewards, err := ClaimTaskPointRewards(ctx, account.GetIntlAccount().GetOpenId(), hokOpenId, pointTaskItemMap, true)
	if err != nil {
		return nil, err
	}
	items := make([]*pb.TaskStatusItem, 0)
	for _, v := range taskPointRewards.SuccessTaskUUIDList {
		items = append(items, &pb.TaskStatusItem{
			TaskUuid: v,
			Status:   1,
		})
	}

	go func(v []string) {
		for _, taskUUID := range v {
			// 上报tlog
			var taskId int32
			switch taskUUID {
			case loginTaskUUID:
				taskId = 2
			case addedFriendTaskUUID:
				taskId = 3
			case teamPlayEventTaskUUID01:
				taskId = 4
			case teamPlayEventTaskUUID02:
				taskId = 5
			case teamPlayEventTaskUUID03:
				taskId = 6
			case teamPlayEventTaskUUID04:
				taskId = 7
			}
			env := trpc.GlobalConfig().Global.EnvName
			extentContent := make(map[string]interface{})
			extentContent["env"] = env
			extentContent["task_id"] = taskId
			extentContent["uid"] = account.Uid
			action := "backup_office_lip_hok_task_finish"
			subAction := "cm_click"
			common.ReportTlog(context.Background(), action, subAction, 29134, extentContent)
		}
	}(taskPointRewards.SuccessTaskUUIDList)

	return items, nil
}

// ClaimTaskPointRewards 未领取奖励判断 发送
func ClaimTaskPointRewards(ctx context.Context, lipOpenid, gameOpenid string, taskUUIDItemMap map[string]PointTaskItem,
	send bool) (*ClaimTaskPointRewardsRsp, error) {

	taskCompleteStatusService := config.GetConfig().LIPBandingHOK2504Conf
	// 任务配置
	bandingTaskUUID := taskCompleteStatusService.BandingLIPTaskUUID
	loginTaskUUID := taskCompleteStatusService.CumulativeLoginTaskUUID
	addedFriendTaskUUID := taskCompleteStatusService.CumulativeAddedFriendTaskUUID
	oneTeamPlayEventTaskUUID := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID01
	threeTeamPlayTaskUUID := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID02
	fiveTeamPlayTaskUUID := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID03
	tenTeamPlayTaskUUID := taskCompleteStatusService.CumulativeTeamPlayEventTaskUUID04
	matchesNum01 := taskCompleteStatusService.CompletedMatchesNum01
	matchesNum02 := taskCompleteStatusService.CompletedMatchesNum02
	matchesNum03 := taskCompleteStatusService.CompletedMatchesNum03
	matchesNum04 := taskCompleteStatusService.CompletedMatchesNum04
	if matchesNum01 == 0 || matchesNum02 == 0 || matchesNum03 == 0 || matchesNum04 == 0 {
		return nil, errs.NewCustomError(ctx, code.GetConfErr, "get conf err")
	}

	// 批量查询用户游戏任务完成状态
	metricsTaskStatusReq := &pb.GetLipBandingMetricsTaskStatusReq{}
	// 待查询积分任务状态
	var taskUUIDList []string
	for taskUUID, taskItem := range taskUUIDItemMap {
		switch taskUUID {
		case bandingTaskUUID: // 绑定任务
			// 查询绑定任务
			taskUUIDList = append(taskUUIDList, taskUUID)
		case loginTaskUUID: // 登录任务
			metricsTaskStatusReq.LoginStartStopTime = taskItem.StartStopTime
			taskUUIDList = append(taskUUIDList, taskUUID)
		case addedFriendTaskUUID: // 添加好友任务
			metricsTaskStatusReq.AddedFriendStartStopTime = taskItem.StartStopTime
			taskUUIDList = append(taskUUIDList, taskUUID)
		case oneTeamPlayEventTaskUUID, threeTeamPlayTaskUUID, fiveTeamPlayTaskUUID, tenTeamPlayTaskUUID: // 组队比赛任务
			metricsTaskStatusReq.TeamPlayStartStopTime = taskItem.StartStopTime
			taskUUIDList = append(taskUUIDList, taskUUID)
		}
	}
	// 游戏任务
	metricsTaskStatus, err := GetLipBandingMetricsTaskStatus(ctx, metricsTaskStatusReq)
	if err != nil {
		return nil, err
	}
	// 积分任务
	taskPoints := lip.TaskPoints{
		TaskUuids:    taskUUIDList,
		IntegralType: 1,
		LipOpenid:    lipOpenid,
		GameOpenid:   gameOpenid,
		IntlGameId:   cast.ToInt64(constant.GAMEID_HOK),
	}
	taskCompletionStatus, err := taskPoints.QueryTaskCompletionStatus(ctx)
	if err != nil {
		return nil, err
	}

	var successTaskUUIDList, failTaskUUIDList []string
	var pendingSendTaskUUIDList []string // 待发送任务列表
	var addStatus int32                  // 0-失败 ；1-成功

	for _, v := range taskCompletionStatus {
		if v.GetStatus() != 0 {
			// 非未完成状态
			continue
		}

		switch v.GetTaskUuid() {
		case bandingTaskUUID: // 绑定任务直接发送
			addPointsTask := &lip.TaskPoints{
				TaskUuids: []string{
					v.GetTaskUuid(),
				},
				IntegralType: 1,
				LipOpenid:    lipOpenid,
				GameOpenid:   gameOpenid,
				IntlGameId:   cast.ToInt64(constant.GAMEID_HOK),
			}
			addStatus, err = addPointsTask.CompleteTaskAddPoint(ctx)
			if err != nil {
				return nil, err
			}
			if addStatus != 1 {
				return nil, errs.NewCustomError(ctx, code.SendHOKBandingLIPPointError, "Send Banding Point Error")
			}
		case loginTaskUUID: // 登录任务
			if isLoginToday := metricsTaskStatus.GetIsLoginToday(); isLoginToday {
				pendingSendTaskUUIDList = append(pendingSendTaskUUIDList, v.TaskUuid)
			}
		case addedFriendTaskUUID: // 添加好友任务
			if addedFriend := metricsTaskStatus.GetHasAddedFriend(); addedFriend {
				pendingSendTaskUUIDList = append(pendingSendTaskUUIDList, v.TaskUuid)
			}
		case oneTeamPlayEventTaskUUID: // 组队比赛任务
			if completedMatchesNum := metricsTaskStatus.GetCompletedMatchesThisWeek(); completedMatchesNum >= matchesNum01 {
				pendingSendTaskUUIDList = append(pendingSendTaskUUIDList, v.TaskUuid)
			}
		case threeTeamPlayTaskUUID: // 组队比赛任务
			if completedMatchesNum := metricsTaskStatus.GetCompletedMatchesThisWeek(); completedMatchesNum >= matchesNum02 {
				pendingSendTaskUUIDList = append(pendingSendTaskUUIDList, v.TaskUuid)
			}
		case fiveTeamPlayTaskUUID: // 组队比赛任务
			if completedMatchesNum := metricsTaskStatus.GetCompletedMatchesThisWeek(); completedMatchesNum >= matchesNum03 {
				pendingSendTaskUUIDList = append(pendingSendTaskUUIDList, v.TaskUuid)
			}
		case tenTeamPlayTaskUUID: // 组队比赛任务
			if completedMatchesNum := metricsTaskStatus.GetCompletedMatchesThisWeek(); completedMatchesNum >= matchesNum04 {
				pendingSendTaskUUIDList = append(pendingSendTaskUUIDList, v.TaskUuid)
			}
		}
	}

	claimTaskPointRewardsRsp := &ClaimTaskPointRewardsRsp{}
	if len(pendingSendTaskUUIDList) != 0 {
		claimTaskPointRewardsRsp.HasPendingTasks = true
	} else {
		return claimTaskPointRewardsRsp, nil
	}
	if !send { // 不发送直接返回
		return claimTaskPointRewardsRsp, nil
	}
	// 发送积分
	for _, taskUUID := range pendingSendTaskUUIDList {
		addPointsTask := &lip.TaskPoints{
			TaskUuids: []string{
				taskUUID,
			},
			IntegralType: 1,
			LipOpenid:    lipOpenid,
			GameOpenid:   gameOpenid,
			IntlGameId:   cast.ToInt64(constant.GAMEID_HOK),
		}
		addStatus, err = addPointsTask.CompleteTaskAddPoint(ctx)
		if err != nil {
			failTaskUUIDList = append(failTaskUUIDList, taskUUID)
			log.WithFieldsContext(ctx, "log_type", "AddPoint_err", "m", "ClaimTaskPointRewards").
				Infof("ClaimTaskPointRewards CompleteTaskAddPoint; addPointsTask:[%v], err: %v",
					addPointsTask, err)
			continue
		}
		if addStatus != 1 {
			failTaskUUIDList = append(failTaskUUIDList, taskUUID)
		} else {
			successTaskUUIDList = append(successTaskUUIDList, taskUUID)
		}
	}

	if len(failTaskUUIDList) != 0 {
		log.WithFieldsContext(ctx, "log_type", "failTaskUUIDList", "m", "ClaimTaskPointRewards").
			Errorf("ClaimTaskPointRewards failTaskUUIDList:[%v]", failTaskUUIDList)
		return nil, errs.NewCustomError(ctx, code.SendHOKBandingLIPTaskPointError, "Send Point Error")
	}
	claimTaskPointRewardsRsp.SuccessTaskUUIDList = successTaskUUIDList
	return claimTaskPointRewardsRsp, nil
}

// GetLipBandingMetricsTaskStatus LIP集群-获取指标任务状态
func GetLipBandingMetricsTaskStatus(ctx context.Context, req *pb.GetLipBandingMetricsTaskStatusReq) (
	*pb.GetLipBandingMetricsTaskStatusRsp, error) {

	taskCompleteStatusService := config.GetConfig().LIPBandingHOK2504Conf
	teamPlayTargetID := taskCompleteStatusService.CumulativeTeamPlayTargetID
	loginTargetID := taskCompleteStatusService.CumulativeLoginTargetID
	addedFriendTargetID := taskCompleteStatusService.CumulativeAddedFriendTargetID

	metricsInfos := make([]*pb.MetricsInfo, 0)
	teamPlayStartStopTime := req.GetTeamPlayStartStopTime()
	loginStartStopTime := req.GetLoginStartStopTime()
	addedFriendStartStopTime := req.GetAddedFriendStartStopTime()
	if len(teamPlayStartStopTime) == 2 {
		metricsInfos = append(metricsInfos, &pb.MetricsInfo{
			TargetId: teamPlayTargetID,
			StartDay: teamPlayStartStopTime[0],
			EndDay:   teamPlayStartStopTime[1],
		})
	}
	if len(loginStartStopTime) == 2 {
		metricsInfos = append(metricsInfos, &pb.MetricsInfo{
			TargetId: loginTargetID,
			StartDay: loginStartStopTime[0],
			EndDay:   loginStartStopTime[1],
		})
	}
	if len(addedFriendStartStopTime) == 2 {
		metricsInfos = append(metricsInfos, &pb.MetricsInfo{
			TargetId: addedFriendTargetID,
			StartDay: addedFriendStartStopTime[0],
			EndDay:   addedFriendStartStopTime[1],
		})
	}

	if len(metricsInfos) == 0 {
		return nil, errs.NewCustomError(ctx, code.TimeRangeErr, "Time Range Err")
	}
	// LIP UID 转为 游戏UID
	hokOpenId, err := service.GetGameOpenId(ctx, constant.GAMEID_HOK)
	if nil != err {
		log.ErrorContextf(ctx, "LIPCallsHOKClusterProxy get game openid error:%v", err)
		return nil, err
	}
	if hokOpenId == "" {
		log.ErrorContextf(ctx, "LIPCallsHOKClusterProxy hok openid is empty")
		return nil, errs.NewCustomError(ctx, code.GameIDNotObtained, "GameID Not Obtained")
	}
	uid := strings.Join([]string{constant.GAMEID_HOK, hokOpenId}, "-")
	batchGetHOKMetricsDataReq := &pb.BatchGetHOKMetricsDataReq{
		Uid:         uid,
		MetricsList: metricsInfos,
	}
	marshal, err := json.Marshal(batchGetHOKMetricsDataReq)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "debug", "m", "GetLipBandingMetricsTaskStatus").Infof(
			"GetLipBandingMetricsTaskStatus Marshal batchGetHOKMetricsDataReq:[%v] err: %v", batchGetHOKMetricsDataReq, err)
		return nil, errs.NewCustomError(ctx, code.CommonJSONMarshalErr, "json marshal err")
	}
	// 调用代理接口
	proxyRspStr, err := LIPCallsHOKClusterProxy(ctx, LIPCallsHOKClusterProxyParam{
		MethodName: "BatchGetHOKMetricsData",
		PostString: string(marshal),
	})
	if err != nil {
		return nil, err
	}
	var dataInfos hok.HttpProxyRsp
	if err = json.Unmarshal([]byte(proxyRspStr), &dataInfos); err != nil {
		return nil, errs.NewCustomError(ctx, code.JsonParseError,
			"GetLipBandingMetricsTaskStatus Unmarshal rsp err,proxyRspStr=[%v], \t [Error]:{%v} ", proxyRspStr, err)
	}
	log.InfoContextf(ctx, "GetLipBandingMetricsTaskStatus end success; dataInfos:[%v]", &dataInfos)
	var hasAddedFriend bool
	var completedMatchesThisWeek int32
	for _, v := range dataInfos.DataItem.MetricsDataList {
		switch v.Target.TargetID {
		case addedFriendTargetID:
			if cast.ToInt32(v.TargetValue) > 0 {
				hasAddedFriend = true
			}
		//case loginTargetID:
		//	if cast.ToInt32(v.TargetValue) > 0 {
		//		loginToday = true
		//	}
		case teamPlayTargetID:
			completedMatchesThisWeek = cast.ToInt32(v.TargetValue)
		}
	}
	return &pb.GetLipBandingMetricsTaskStatusRsp{
		IsLoginToday:             true,
		HasAddedFriend:           hasAddedFriend,
		CompletedMatchesThisWeek: completedMatchesThisWeek,
	}, nil

}

// GetCompletedTeamMatchesInRange 范围时间内完成的组队对局数量
func GetCompletedTeamMatchesInRange(ctx context.Context, timeRange []string) ([]*hok_request.DataInfo, error) {
	if len(timeRange) != 2 {
		return nil, errs.NewCustomError(ctx, code.TimeRangeErr, "Time Range Err")
	}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	regionConfRsp, err := gameProxy.GetGameRegionConf(ctx, &gamePb.GetGameRegionConfReq{
		GameId: HOKGameId,
	})
	if err != nil {
		return nil, err
	}
	zoneIdNum := regionConfRsp.ZoneId
	if zoneIdNum == 0 {
		return nil, errs.NewCustomError(ctx, code.HokTmpGetGameRegionConfDataError, "Game Region Conf Data Error")
	}

	taskCompleteStatusService := config.GetConfig().LIPBandingHOK2504Conf
	service := taskCompleteStatusService.TaskMetricsService
	namespace := taskCompleteStatusService.TaskMetricsNamespace
	path := taskCompleteStatusService.TaskMetricsPath
	activityID := taskCompleteStatusService.TaskMetricsActivityID
	teamPlayTargetID := taskCompleteStatusService.CumulativeTeamPlayTargetID
	bid := taskCompleteStatusService.TaskMetricsXBid
	caller := taskCompleteStatusService.TaskMetricsCaller
	openid := userAccount.IntlAccount.OpenId
	uid := userAccount.Uid

	port, err := util.GetServer(service, namespace)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "GetCompletedTeamMatchesInRange").Errorf(
			"GetCompletedTeamMatchesInRange err:[%v]", err)
		return nil, err
	}

	getDataGroupMetricsParam := &GetDataGroupMetricsParam{
		Host:       port,
		Path:       path,
		UID:        uid,
		ActivityID: activityID,
		Targets: []*hok_request.TargetInfo{
			{
				TargetID: teamPlayTargetID,
				OpenID:   openid,
				ZoneID:   uint32(zoneIdNum),
				StartDay: timeRange[0],
				EndDay:   timeRange[1],
			},
		},
		XBid:   bid,
		Caller: caller,
	}
	groupMetrics, err := getDataGroupMetricsParam.GetDataGroupMetrics(ctx)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "debug", "m", "GetCompletedTeamMatchesInRange").Errorf(
			"GetCompletedTeamMatchesInRange GetDataGroupMetrics getDataGroupMetricsParam: %v,err:[%v]", getDataGroupMetricsParam, err)
		return nil, errs.NewCustomError(ctx, code.HokTmpGetDataGroupMetricsError, "Get Data Group Metrics Error")
	}
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "GetCompletedTeamMatchesInRange").Infof(
		"GetCompletedTeamMatchesInRange success getDataGroupMetricsParam: [%v] groupMetrics:[%v]",
		getDataGroupMetricsParam, groupMetrics)
	return groupMetrics.GetDatas(), nil
}

// BatchGetRangeTimeMetricsData 批量获取范围时间内用户指标数据
func BatchGetRangeTimeMetricsData(ctx context.Context, uid string, targetList []*pb.MetricsInfo) ([]*hok_request.DataInfo, error) {

	regionConfRsp, err := gameProxy.GetGameRegionConf(ctx, &gamePb.GetGameRegionConfReq{
		GameId: HOKGameId,
	})
	if err != nil {
		return nil, err
	}
	zoneIdNum := regionConfRsp.ZoneId
	if zoneIdNum == 0 {
		return nil, errs.NewCustomError(ctx, code.HokTmpGetGameRegionConfDataError, "Game Region Conf Data Error")
	}
	log.WithFieldsContext(ctx, "log_type", "zoneIdNum_debug", "m", "BatchGetRangeTimeMetricsData").
		Infof("BatchGetRangeTimeMetricsData show zoneIdNum:[%v]", zoneIdNum)
	split := strings.Split(uid, "-")
	if len(split) != 2 {
		return nil, errs.NewCustomError(ctx, code.InvalidUID, "Invalid UID")
	}
	openid := split[1]
	taskCompleteStatusService := config.GetConfig().LIPBandingHOK2504Conf
	ser := taskCompleteStatusService.TaskMetricsService
	namespace := taskCompleteStatusService.TaskMetricsNamespace
	path := taskCompleteStatusService.TaskMetricsPath
	activityID := taskCompleteStatusService.TaskMetricsActivityID
	bid := taskCompleteStatusService.TaskMetricsXBid
	caller := taskCompleteStatusService.TaskMetricsCaller
	targetInfos := make([]*hok_request.TargetInfo, 0, len(targetList))
	for _, targetItem := range targetList {
		targetInfos = append(targetInfos, &hok_request.TargetInfo{
			TargetID: targetItem.TargetId,
			OpenID:   openid,
			ZoneID:   uint32(zoneIdNum),
			StartDay: targetItem.StartDay,
			EndDay:   targetItem.EndDay,
		})
	}

	port, err := util.GetServer(ser, namespace)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "BatchGetRangeTimeMetricsData").Errorf(
			"BatchGetRangeTimeMetricsData err:[%v]", err)
		return nil, err
	}

	getDataGroupMetricsParam := &GetDataGroupMetricsParam{
		Host:       port,
		Path:       path,
		UID:        uid,
		ActivityID: activityID,
		Targets:    targetInfos,
		XBid:       bid,
		Caller:     caller,
	}
	groupMetrics, err := getDataGroupMetricsParam.GetDataGroupMetrics(ctx)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "debug", "m", "BatchGetRangeTimeMetricsData").Errorf(
			"BatchGetRangeTimeMetricsData GetDataGroupMetrics getDataGroupMetricsParam: %v,err:[%v]", getDataGroupMetricsParam, err)
		return nil, errs.NewCustomError(ctx, code.HokTmpGetDataGroupMetricsError, "Get Data Group Metrics Error")
	}
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "BatchGetRangeTimeMetricsData").Infof(
		"BatchGetRangeTimeMetricsData success getDataGroupMetricsParam: [%v] groupMetrics:[%v]",
		getDataGroupMetricsParam, groupMetrics)
	return groupMetrics.GetDatas(), nil
}

// HOKBandingLIPSendPoint2504 HOK绑定LIP积分发送
func HOKBandingLIPSendPoint2504(ctx context.Context) ([]*pb.TaskStatusItem, error) {
	account, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	// LIP UID 转为 游戏UID
	hokOpenId, err := service.GetGameOpenId(ctx, constant.GAMEID_HOK)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards get game openid error:%v", err)
		return nil, err
	}
	if hokOpenId == "" {
		log.ErrorContextf(ctx, "ClaimAllTaskPointRewards hok openid is empty")
		return nil, errs.NewCustomError(ctx, code.GameIDNotObtained, "GameID Not Obtained")
	}
	// 任务配置
	taskCompleteStatusService := config.GetConfig().LIPBandingHOK2504Conf
	bandingLIPTaskUUID := taskCompleteStatusService.BandingLIPTaskUUID
	pointTaskItemMap := make(map[string]PointTaskItem, 6)
	pointTaskItemMap[bandingLIPTaskUUID] = PointTaskItem{}
	taskPointRewards, err := ClaimTaskPointRewards(ctx, account.GetIntlAccount().GetOpenId(), hokOpenId, pointTaskItemMap, true)
	if err != nil {
		return nil, err
	}
	items := make([]*pb.TaskStatusItem, 0)
	for _, v := range taskPointRewards.SuccessTaskUUIDList {
		items = append(items, &pb.TaskStatusItem{
			TaskUuid: v,
			Status:   1,
		})
	}
	return items, nil
}
