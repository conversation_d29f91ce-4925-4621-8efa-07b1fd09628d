// Package taris dragon tarisdragon
package taris

import "trpc.act.logicial/app/model"

// TarisDragonRaidBountyModel Model
type TarisDragonRaidBountyModel struct {
	model.AppModel
}

// TableName .
func (TarisDragonRaidBountyModel) TableName() string {
	return "trssj_dupcd_kill_sumpoint_di"
}

// TarisDragonRaidBountyDetail TODO
// TarisRankTemp 临时结构
type TarisDragonRaidBountyDetail struct {
	TarisDragonRaidBountyModel
	Dtstatdate      string `gorm:"type:archar(255);column:dtstatdate"`
	DupCd           string `gorm:"type:archar(255);column:dupcd"`
	VGuildId        string `gorm:"type:archar(255);column:vguildid"`
	VGuildName      string `gorm:"type:archar(255);column:vguildname"`
	GuildChairManId string `gorm:"type:archar(255);column:guildchairmanid"`
	IZoneAreaId     string `gorm:"type:archar(255);column:izoneareaid"`
	VRoleName       string `gorm:"type:archar(255);column:vrolename"`
	SumPoint        string `gorm:"type:archar(255);column:sumpoint"` // 排名
	LastTime        string `gorm:"type:archar(255);column:lasttime"` // 最后击杀时间
}

// TarisDragonRaidBountyTotalModel TODO
type TarisDragonRaidBountyTotalModel struct {
	model.AppModel
}

// TableName .
func (TarisDragonRaidBountyTotalModel) TableName() string {
	return "taris_dragon_raid_bounty_total_points"
}

// TarisDragonRaidBountyTotalDetail TODO
// TarisRankTemp 临时结构
type TarisDragonRaidBountyTotalDetail struct {
	TarisDragonRaidBountyTotalModel
	ID               int64  `gorm:"type:int(11);column:id;primary_key"`
	GuildID          string `gorm:"type:varchar(255);column:guild_id;"`
	AreaId           int64  `gorm:"type:int(11);column:area_id;"`
	ZoneId           int64  `gorm:"type:int(11);column:zone_id;"`
	Points           int64  `gorm:"type:int(11);column:points;"`                  // 积分
	TimePoints1      int64  `gorm:"type:int(11);column:time_points_1;"`           // 第一时间段积分
	TimePoints2      int64  `gorm:"type:int(11);column:time_points_2;"`           // 第一时间段积分
	TimePoints3      int64  `gorm:"type:int(11);column:time_points_3;"`           // 第一时间段积分
	TimePoints4      int64  `gorm:"type:int(11);column:time_points_4;"`           // 第一时间段积分
	TimePoints5      int64  `gorm:"type:int(11);column:time_points_5;"`           // 第一时间段积分
	TimeDtEventTime1 int64  `gorm:"type:int(11);column:time_dteventtime_1;"`      // 第一时间通过完成时间戳
	TimeDtEventTime2 int64  `gorm:"type:int(11);column:time_dteventtime_2;"`      // 第一时间通过完成时间戳
	TimeDtEventTime3 int64  `gorm:"type:int(11);column:time_dteventtime_3;"`      // 第一时间通过完成时间戳
	TimeDtEventTime4 int64  `gorm:"type:int(11);column:time_dteventtime_4;"`      // 第一时间通过完成时间戳
	TimeDtEventTime5 int64  `gorm:"type:int(11);column:time_dteventtime_5;"`      // 第一时间通过完成时间戳
	TimeRank1        int64  `gorm:"type:int(11);column:time_rank_1;"`             // 第一时间段排名
	TimeRank2        int64  `gorm:"type:int(11);column:time_rank_2;"`             // 第一时间段排名
	TimeRank3        int64  `gorm:"type:int(11);column:time_rank_3;"`             // 第一时间段排名
	TimeRank4        int64  `gorm:"type:int(11);column:time_rank_4;"`             // 第一时间段排名
	TimeRank5        int64  `gorm:"type:int(11);column:time_rank_5;"`             // 第一时间段排名
	TimeGuildName1   string `gorm:"type:varchar(255);column:time_guild_name_1;"`  // 第一时间段工会名称
	TimeMasterName1  string `gorm:"type:varchar(255);column:time_master_name_1;"` // 第一时间段会长名称
	TimeMasterID1    string `gorm:"type:varchar(255);column:time_master_id_1;"`   // 第一时间段会长ID
	TimeGuildName2   string `gorm:"type:varchar(255);column:time_guild_name_2;"`  // 第一时间段工会名称
	TimeMasterName2  string `gorm:"type:varchar(255);column:time_master_name_2;"` // 第一时间段会长名称
	TimeMasterID2    string `gorm:"type:varchar(255);column:time_master_id_2;"`   // 第一时间段会长ID
	TimeGuildName3   string `gorm:"type:varchar(255);column:time_guild_name_3;"`  // 第一时间段工会名称
	TimeMasterName3  string `gorm:"type:varchar(255);column:time_master_name_3;"` // 第一时间段会长名称
	TimeMasterID3    string `gorm:"type:varchar(255);column:time_master_id_3;"`   // 第一时间段会长ID
	TimeGuildName4   string `gorm:"type:varchar(255);column:time_guild_name_4;"`  // 第一时间段工会名称
	TimeMasterName4  string `gorm:"type:varchar(255);column:time_master_name_4;"` // 第一时间段会长名称
	TimeMasterID4    string `gorm:"type:varchar(255);column:time_master_id_4;"`   // 第一时间段会长ID
	TimeGuildName5   string `gorm:"type:varchar(255);column:time_guild_name_5;"`  // 第一时间段工会名称
	TimeMasterName5  string `gorm:"type:varchar(255);column:time_master_name_5;"` // 第一时间段会长名称
	TimeMasterID5    string `gorm:"type:varchar(255);column:time_master_id_5;"`   // 第一时间段会长ID
}

// TarisDragonRaidBountyESModel TODO
type TarisDragonRaidBountyESModel struct {
	model.AppModel
}

// TableName .
func (TarisDragonRaidBountyESModel) TableName() string {
	return "taris_dragon_raid_bounty_points_top"
}

// TarisDragonRaidBountyESDetail TODO
// TarisRankTemp 临时结构
type TarisDragonRaidBountyESDetail struct {
	TarisDragonRaidBountyESModel
	GuildMaster   string `json:"guild_master"`
	GuildID       string `json:"guild_id"`
	GuildName     string `json:"guild_name"`
	GuildMasterID string `json:"guild_master_id"`
	AreaId        int64  `json:"area_id"`
	ZoneId        int64  `json:"zone_id"`
	Points        int64  `json:"points"`      // 积分
	Rank          int64  `json:"rank"`        // 排名
	PeriodTime    string `json:"period_time"` // 当前周期
}

// TarisDragonErrorGuildIdModel TODO
type TarisDragonErrorGuildIdModel struct {
	model.AppModel
}

// TableName 工会违规
func (TarisDragonErrorGuildIdModel) TableName() string {
	return "taris_dragon_error_guild_id"
}

// TarisDragonErrorGuildIdDetail TODO
// TarisRankTemp 临时结构
type TarisDragonErrorGuildIdDetail struct {
	TarisDragonErrorGuildIdModel
	ID      int64  `gorm:"type:int(11);column:id;primary_key"`
	GuildID string `gorm:"type:varchar(255);column:guild_id;"`
}
