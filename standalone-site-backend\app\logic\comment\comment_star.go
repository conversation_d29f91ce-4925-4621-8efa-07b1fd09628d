package comment

import (
	"context"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"trpc.publishing_application.standalonesite/app/common"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

// GetCommentStar 获取评论点赞信息
func GetCommentStar(commentID int64, intlOpenID string) (*model.CommentStar, error) {
	return dao.CommentStarGet(0, commentID, intlOpenID)
}

// GetCommentStarNew 获取评论点赞信息
func GetCommentStarNew(commentID string, intlOpenID string) (*model.CommentStar, error) {
	return dao.CommentStarGetV2(0, commentID, intlOpenID)
}

func UserStarComment(c context.Context, intlOpenID string, commentUUID, gameID, areaID string) (bool, error) {
	limitRedisKey := cache.GetUserStarCommentLimitKey(intlOpenID, commentUUID)
	if ok, err := redis.GetClient().SetNX(c, limitRedisKey, 1, 2*time.Minute).Result(); ok {
		defer redis.GetClient().Del(c, limitRedisKey)
		// 判断必要参数是否为空
		if commentUUID == "" || intlOpenID == "" {
			return false, errs.NewCustomError(c, code.InvalidParams, "Required parameter is missing")
		}
		// 判断是否存在该条评论详细信息
		status := false
		commentInfo, getErrCode := GetCommunityCommentInfo(c, commentUUID)
		if getErrCode != nil {
			return status, getErrCode
		}
		// 如果评论未审核则不可点赞, 自己可以自己的未审核评论点赞
		if commentInfo.IsAudit != 1 && intlOpenID != commentInfo.IntlOpenid {
			return status, errs.NewCustomError(c, code.CommentNotReviewStarFailed, "UserStarComment | The content of the comment has not been reviewed, and the like comment failed.")
		}
		// 判断是否已有对应用户对该评论的点赞记录
		_, err := GetCommentStarNew(commentUUID, intlOpenID)
		if err != nil {
			if err.Error() != "record not found" {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserStarComment GetCommentStarNew err: %v, commentID is %s, userID is %s", err, commentUUID, intlOpenID)
				return status, errs.NewCustomError(c, code.GetCommentStarFailed, "UserStarComment | Failed to obtain comment like information.")
			}
			// 如果没有记录，则创建Star
			status = true
			err = CreateCommentStarNew(c, commentInfo, intlOpenID, gameID, areaID)
			if err != nil {
				return status, errs.NewCustomError(c, code.CreateCommentStarFailed, "UserStarComment | Failed to like the comment")
			}
		} else {
			// 取消Star
			err = DeleteCommentStarNew(c, commentInfo)
			if err != nil {
				return status, errs.NewCustomError(c, code.DeleteCommentStarFailed, "UserStarComment | Cancel comment like operation failed")
			}
		}
		// 删除动态下评论列表缓存
		if commentInfo.PostUUID != "" {
			go DeleteCommentListCache(context.Background(), commentInfo.PostUUID, intlOpenID, "", 10)
		}
		return status, nil
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserStarComment redis return error: %v", err)
		return false, errs.NewCustomError(c, code.UserStarCommentFrequentLimit, "UserStarComment | Frequent operation limits for users to like comments.")
	}
}

// CreateCommentStarNew 创建评论点赞记录
func CreateCommentStarNew(c context.Context, commentInfo *model.Comment, intlOpenID string, gameID, areaID string) error {
	// 获取评论内容
	commentStateInfo, err := dao.GetCommentStateByCommentUuid(commentInfo.CommentUUID)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreateCommentStarNew getCommentContent err: %v", err)
		return err
	}
	// 创建DB记录
	err = dao.CommentStarCreate(&model.CommentStar{
		PostUUID:    commentInfo.PostUUID,
		CommentUUID: commentInfo.CommentUUID,
		IntlOpenid:  intlOpenID,
		Type:        constants.CommentStarType(commentInfo.Type),
	})
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreateCommentStarNew CreateCommentStar err: %v", err)
		go func(ctx context.Context, intlGameId string) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportPostUpVoteLog(c, intlOpenID, nil, commentInfo, false, gameID, err)
		}(c, gameID)

		return err
	}
	go func(ctx context.Context, intlGameId string) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportPostUpVoteLog(c, intlOpenID, nil, commentInfo, false, gameID, nil)
	}(c, gameID)
	commentStateInfo.UpvoteCount++

	// 更新评论记录的点赞数
	updateErr := updateCommentStar(c, commentStateInfo)
	if updateErr != nil {
		return updateErr
	}

	// 记录被点赞的评论commentuuid，用于计算热度的定时任务
	if commentInfo.Type == int32(constants.DYNAMIC_COMMENT) {
		commentHotCalculationUUIDsKey := cache.GetCommentHotCalculationUUIDKey()
		redis.GetClient().SAdd(c, commentHotCalculationUUIDsKey, commentInfo.CommentUUID)
	}

	msgType := constants.MsgTypeCommentStar
	if commentInfo.Type == 1 {
		msgType = constants.MsgTypeCommentReplyStar
	}
	commentMaster, err := dao.GetUserByIntlOpenid(commentInfo.IntlOpenid)
	if err == nil && commentMaster.IntlOpenid != intlOpenID {
		go writemessage.SetUserMessage(&model.Message{
			Type:                   msgType,
			PostUUID:               commentInfo.PostUUID,
			CommentUUID:            commentInfo.CommentUUID,
			ReplyUUID:              commentInfo.ReplyUUID,
			Reply2ReplyUUID:        commentInfo.Reply2ReplyUUID,
			GameID:                 gameID,
			AreaID:                 areaID,
			SenderUserIntlOpenid:   intlOpenID,
			ReceiverUserIntlOpenid: commentMaster.IntlOpenid,
		}, commentMaster.IntlOpenid, constants.LikeMessageCount)
	}

	return nil
}

// DeleteCommentStarNew 取消评论点赞记录
func DeleteCommentStarNew(c context.Context, comment *model.Comment) error {
	// 获取评论内容
	commentStateInfo, err := dao.GetCommentStateByCommentUuid(comment.CommentUUID)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteCommentStarNew getCommentContent err: %v", err)
		return err
	}
	// 软删除DB点赞表p_comment_star对应记录
	err = dao.CommentStarDeleteNew(comment.CommentUUID)
	if err != nil {
		return err
	}
	// 更新评论的点赞数
	commentStateInfo.UpvoteCount--
	if commentStateInfo.UpvoteCount < 0 {
		commentStateInfo.UpvoteCount = 0
	}
	updateErr := updateCommentStar(c, commentStateInfo)
	if updateErr != nil {
		return updateErr
	}
	// 记录被取消点赞的评论commentuuid，用于计算热度的定时任务
	if comment.Type == int32(constants.DYNAMIC_COMMENT) {
		commentHotCalculationUUIDsKey := cache.GetCommentHotCalculationUUIDKey()
		redis.GetClient().SAdd(c, commentHotCalculationUUIDsKey, comment.CommentUUID)
	}
	return nil
}

func GetCommunityCommentInfo(c context.Context, commentUUID string) (*model.Comment, error) {
	commentInfo, err := dao.GetCommentByUUID(commentUUID)
	if err != nil {
		return nil, errs.NewCustomError(c, code.GetCommentContentFailed, "GetCommunityCommentInfo | Failed to get comment content")
	}
	return commentInfo, nil
}

// updateCommentStar 调用CMS-TOC接口更新评论点赞数
func updateCommentStar(c context.Context, commentState *model.CommentState) error {
	err := dao.CommentStateUpdateUpvoteCount(commentState)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("updateCommentStar comment content update failed. err: %v, commentstateInfo: %+v", err, commentState)
		return err
	}
	return nil
}
