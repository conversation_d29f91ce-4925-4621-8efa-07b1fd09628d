package util

import (
	"net/url"
	"strings"

	"golang.org/x/net/html"
)

// 遍历节点，查找特定标签并提取内容
func Traverse(n *html.Node, tagNames []string, imgLinks *[]string, texts *[]string, lineBreakCount *int) {
	if n.Type == html.ElementNode {
		// 查找指定标签
		for _, tagName := range tagNames {
			if n.Data == tagName {
				text := extractText(n)
				if strings.TrimSpace(text) != "" {
					*texts = append(*texts, strings.TrimSpace(text))
				}
			}
		}

		// 处理 img 标签
		if n.Data == "img" {
			for _, attr := range n.Attr {
				if attr.Key == "src" {
					// 解析url，判断是否是表情
					parse, err := url.Parse(attr.Val)
					if err == nil {
						queryParams := parse.Query()
						imageType := queryParams.Get("imgtype")
						if imageType == "emoji" {
							break
						}
					}
					*imgLinks = append(*imgLinks, attr.Val)
					break
				}
			}
		}

		if n.Data == "br" {
			*lineBreakCount++
		}
	}

	// 递归处理子节点
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		Traverse(c, tagNames, imgLinks, texts, lineBreakCount)
	}
}

func extractATexts(n *html.Node, aValues *[]string) {
	if n.Type == html.ElementNode && n.Data == "a" {
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			if c.Type == html.TextNode {
				*aValues = append(*aValues, c.Data)
			}
		}
	}
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		extractATexts(c, aValues)
	}
}

// parseATags parses the generated HTML and extracts the index from <a> tags.
func ParseATags(htmlContent string) ([]string, error) {
	var values []string
	// 包裹一个根节点，防止解析错误
	doc, err := html.Parse(strings.NewReader("<div>" + htmlContent + "</div>"))
	if err != nil {
		return values, err
	}

	var aValues []string
	extractATexts(doc, &aValues)

	// 如果没找到a标签，也返回原始字符串
	if len(aValues) == 0 {
		return []string{htmlContent}, nil
	}
	return aValues, nil
}
