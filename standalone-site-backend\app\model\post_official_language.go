package model

type PostOfficialLanguage struct {
	*Model
	PostUUID       string `gorm:"column:post_uuid" json:"post_uuid"`             //动态唯一ID
	Language       string `gorm:"column:language" json:"language"`               //语言：如简体中文zh，英文en
	Title          string `gorm:"column:title" json:"title"`                     //标题
	Content        string `gorm:"column:content" json:"content"`                 //内容
	ContentSummary string `gorm:"column:content_summary" json:"content_summary"` //内容概要
	ExtInfo        string `gorm:"column:ext_info" json:"ext_info"`               //扩展字段
	PicUrls        string `gorm:"column:pic_urls" json:"pic_urls"`               //图片链接，逗号分隔
	Order          int32  `gorm:"column:order" json:"order"`                     // 顺序值
}

// TableName sets the insert table name for this struct type
func (p *PostOfficialLanguage) TableName() string {
	return "p_post_official_language"
}
