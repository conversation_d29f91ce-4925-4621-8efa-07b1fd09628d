package constants

// UserAuditT 审核类型 1:昵称;2:个签；3:头像
type UserAuditT uint8

const (
	USER_AUDIT_TYPE_NAME UserAuditT = iota + 1
	USER_AUDIT_TYPE_REMARK
	USER_AUDIT_TYPE_AVATAR
)

// UserAuditStatusT 审核状态 1:未处理 2:已发布 3:已忽略
type UserAuditStatusT uint8

const (
	USER_AUDIT_STATUS_TYPE_PENDING UserAuditStatusT = iota + 1
	USER_AUDIT_STATUS_TYPE_PASS
	USER_AUDIT_STATUS_TYPE_NOPASS
)

// UserAuditActionStatusT CMS审批用户操作类型：1审核通过，2审核不通过，3删除已审核通过数据
type UserAuditActionStatusT uint8

const (
	USER_AUDIT_ACTION_TYPE_PASS UserAuditActionStatusT = iota + 1
	USER_AUDIT_ACTION_TYPE_NOPASS
	USER_AUDIT_ACTION_TYPE_DELETE
)

// 删除原因0-用户自行删除1-c端管理员删除2-b端审核删除3-b端管理员删除4-b端举报删除
type UserAuditDelType uint8

const (
	USER_AUDIT_DELETE_TYPE_SELF UserAuditDelType = iota
	USER_AUDIT_DELETE_TYPE_CADMIN
	USER_AUDIT_DELETE_TYPE_BREVIEW
	USER_AUDIT_DELETE_TYPE_BADMIN
	USER_AUDIT_DELETE_TYPE_BREPORT
)
