package cron

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.act.logicial/app/cache"
	"trpc.act.logicial/app/mysql/df_activity_repo"
)

// LoadGunActivityConf 加载活动配置
func LoadGunActivityConf(ctx context.Context) error {
	// 到数据库查询数据
	log.DebugContextf(ctx, "start load gun activity conf")
	defer func() {
		if r := recover(); r != nil {
			log.ErrorContextf(ctx, "LoadGunActivityConf Recovered from panic:", r)
		}
	}()
	datas, err := df_activity_repo.GunActivityRepoClient.GetGunActivityConf(ctx, 1000)
	if nil != err {
		log.ErrorContextf(ctx, "load gun activity conf error: %v", err)
	}
	log.DebugContextf(ctx, "load gun activity conf, res num: %v", len(datas))
	// 加载到缓存
	for _, data := range datas {
		key := fmt.Sprintf("%v_%v", data.GameId, data.ActivityId)
		cache.SetGunActivityToCache(ctx, key, data)
	}
	return nil
}
