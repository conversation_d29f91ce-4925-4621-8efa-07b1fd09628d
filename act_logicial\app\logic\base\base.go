// Package base 基础
package base

import (
	"context"
	"errors"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"strings"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	utilModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_record"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	base "trpc.act.logicial/app/model/base"
)

const (
	// PeriodicMissionKey TODO
	PeriodicMissionKey = "periodic_mission"
)

// AddPeriodicTaskCompletionData 添加周期任务完成数据
func AddPeriodicTaskCompletionData(ctx context.Context, data base.AddWeeklyMissionParam, timezone int32) (bool, error) {

	tagList, err := GetCurrentPeriodTaskCompletedList(ctx, timezone, data.PeriodicTimeList, data.FsourceID)
	if err != nil {
		return false, err
	}
	for _, tagId := range tagList {
		if data.Tag == tagId {
			return false, errs.NewCustomError(ctx, code.BaseCurrentTaskHasCompleted, "Current Task Has Completed")
		}
	}
	// 添加任务完成状态
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return false, err
	}
	duration, _ := time.ParseDuration(fmt.Sprintf("%vh", timezone))
	todayDateStr := time.Now().Add(duration).Format("2006-01-02")
	curDateTime, _ := time.ParseInLocation("2006-01-02 15:00:00", todayDateStr+" 00:00:00", time.Local)
	var baseLogTable base.BaseTotalModel
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLogTable.TableName())
	if err != nil {
		return false, err
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   data.FsourceID,
		"storage_key":  data.StorageKey,
		"tag_id":       data.Tag,
		"status":       0,
	}
	baseLog := base.BaseTotalLogModel{
		Fday:        curDateTime,
		Uid:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		FsourceId:   data.FsourceID,
		StorageKey:  data.StorageKey,
		TagId:       data.Tag,
		TotalValue:  1,
		TodayValue:  1,
		TotalNum:    1,
		TodayNum:    1,
		TotalUseNum: 0,
		TodayUseNum: 0,
		Status:      0,
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition)
	starTime, endTime := GetStarAndEndTime(data.PeriodicTimeList)
	if starTime != 0 && endTime != 0 {
		db = db.Where("created_at > ? and created_at <= ?", starTime, endTime)
	}
	if starTime == 0 {
		db = db.Where("created_at <= ?", endTime)
	}
	if endTime == 0 {
		db = db.Where("created_at > ?", starTime)
	}
	db.FirstOrCreate(&baseLog)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return false, err
	}
	if db.RowsAffected == 0 {
		return false, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error create err")
	}
	return true, nil
}

// GetCurrentPeriodTaskCompletedList 获取当前周期内完成的任务列表
func GetCurrentPeriodTaskCompletedList(ctx context.Context, timezone int32, periodicTimeList []int64,
	fSourceId string) ([]string, error) {

	if len(periodicTimeList) < 2 {
		return nil, errs.NewCustomError(ctx, code.CommonDataError, "Periodic Time List len err")
	}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	// 格式化时间
	starTime, endTime := GetStarAndEndTime(periodicTimeList)

	var baseLogTable base.BaseTotalModel
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLogTable.TableName())
	if err != nil {
		return nil, err
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   fSourceId,
		"storage_key":  PeriodicMissionKey,
		"status":       0,
	}

	var tagList []string
	sel := "tag_id"
	db := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Select(sel).Where(condition)
	if starTime != 0 && endTime != 0 {
		db = db.Where("created_at > ? and created_at <= ?", starTime, endTime)
	}
	if starTime == 0 {
		db = db.Where("created_at <= ?", endTime)
	}
	if endTime == 0 {
		db = db.Where("created_at > ?", starTime)
	}
	if dbErr := db.Order("id desc").Find(&tagList).Error; dbErr != nil && !errors.Is(dbErr, gorm.ErrRecordNotFound) {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr)
		return nil, err
	}
	return tagList, nil
}

// GetStarAndEndTime TODO
func GetStarAndEndTime(periodicTimeList []int64) (int64, int64) {
	currentTime := time.Now().Unix()
	var starTime, endTime int64
	for idx, v := range periodicTimeList {
		if currentTime <= v {
			// 第一个周期
			if idx == 0 {
				endTime = v
				break
			}
			// 中间的周期
			starTime, endTime = periodicTimeList[idx-1], v
			break
		}
	}
	// 最后一个周期
	if starTime == 0 && endTime == 0 {
		starTime = periodicTimeList[len(periodicTimeList)-1]
	}
	return starTime, endTime
}

// AddData 增加数据
func AddData(ctx context.Context, param base.AddParamStruct, timezone int32) (hasAdd bool,
	err error) {
	now := time.Now()
	defer func() {
		log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", param.FsourceID).
			Infof("AddData tiem : time defer:[%v]", time.Since(now))
	}()
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	duration, _ := time.ParseDuration(fmt.Sprintf("%vh", timezone))
	todayDateStr := time.Now().Add(duration).Format("2006-01-02")
	curDateTime, _ := time.ParseInLocation("2006-01-02 15:00:00", todayDateStr+" 00:00:00", time.Local)
	var baseLogTable base.BaseTotalModel
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLogTable.TableName())
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   param.FsourceID,
		"storage_key":  param.StorageKey,
		"tag_id":       param.Tag,
		"status":       0,
	}

	baseLog := base.BaseTotalLogModel{
		Fday:        curDateTime,
		Uid:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		FsourceId:   param.FsourceID,
		StorageKey:  param.StorageKey,
		TagId:       param.Tag,
		TotalValue:  param.Value,
		TodayValue:  param.Value,
		TotalNum:    1,
		TodayNum:    1,
		TotalUseNum: 0,
		TodayUseNum: 0,
		Status:      0,
	}
	log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", param.FsourceID).
		Infof("AddData tiem : time 01:[%v]", time.Since(now))
	db := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition).FirstOrCreate(&baseLog)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", param.FsourceID).
		Infof("AddData tiem : time 02:[%v]", time.Since(now))
	if db.RowsAffected > 0 {
		return
	}
	// 1：单次可修改
	if param.Type == 1 {
		if dbErr := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition).
			Updates(base.BaseTotalLogModel{
				TodayValue: param.Value, TotalValue: param.Value}).Error; dbErr != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", dbErr)
			return
		}

		hasAdd = true
		return
	}
	log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", param.FsourceID).
		Infof("AddData tiem : time 03:[%v]", time.Since(now))
	// 2：日限、总限 如果不是今天数据，生成今天数据

	if baseLog.Fday.Format("2006-01-02") != todayDateStr {
		// 有数据判断总体是否超限
		if param.TotalLimit != 0 && baseLog.TotalNum >= param.TotalLimit {
			// 超过总限
			err = errs.NewCustomError(ctx, code.BaseTotalLimit,
				"base add data total limit,tag=%v", param.Tag)
			return
		}
		createData := base.BaseTotalLogModel{
			Fday:        curDateTime,
			Uid:         userAccount.Uid,
			AccountType: int32(userAccount.AccountType),
			StorageKey:  param.StorageKey,
			FsourceId:   param.FsourceID,
			TagId:       param.Tag,
			TotalValue:  baseLog.TotalValue + param.Value,
			TodayValue:  param.Value,
			TotalNum:    baseLog.TotalNum + 1,
			TodayNum:    1,
			TotalUseNum: baseLog.TotalUseNum,
			TodayUseNum: 0,
			Status:      0,
		}
		log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", param.FsourceID).
			Infof("AddData tiem : time 04:[%v]", time.Since(now))
		res := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Create(&createData)
		if res.RowsAffected > 0 {
			// 将 之前有数据的状态设置为1
			condition["Fday"] = baseLog.Fday
			DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition).Updates(base.BaseTotalLogModel{
				Status: 1})
			hasAdd = true
			log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", param.FsourceID).
				Infof("AddData tiem : time 05:[%v]", time.Since(now))
			return
		}
		// 如果不是 插入冲突报错，返回错误
		if !strings.HasPrefix(res.Error.Error(), "Error 1062") {
			err = errs.NewCustomError(ctx, code.BaseSystemError, "insert today data ,err=%v", res.Error.Error())
			return
		}
	}
	log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", param.FsourceID).
		Infof("AddData tiem : time 06:[%v]", time.Since(now))
	// 更新今日数据
	condition["Fday"] = curDateTime

	updateData := map[string]interface{}{
		"total_num": gorm.Expr("total_num + ?", 1),
		"today_num": gorm.Expr("today_num + ?", 1),
	}
	res := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition)
	if param.TotalLimit != 0 {
		res = res.Where("total_num < ?", param.TotalLimit)
	}
	if param.DayLimit != 0 {
		res = res.Where("today_num < ?", param.DayLimit)
	}
	res.Updates(updateData)
	if res.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", res.Error)
		return
	}
	log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", param.FsourceID).
		Infof("AddData tiem : time 07:[%v]", time.Since(now))
	if res.RowsAffected == 0 {
		var todayData base.BaseTotalLogModel
		DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition).First(&todayData)
		if param.TotalLimit != 0 && todayData.TotalNum >= param.TotalLimit {
			// 超过总限
			err = errs.NewCustomError(ctx, code.BaseTotalLimit,
				"base add data total limit,tag=%v", param.Tag)
		} else {
			// 超过日限
			err = errs.NewCustomError(ctx, code.BaseDayLimit,
				"base add data total limit,tag=%v", param.Tag)
		}
		log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", param.FsourceID).
			Infof("AddData tiem : time 08:[%v]", time.Since(now))
		return
	}
	hasAdd = true
	return
}

// GetData 获取数据
func GetData(ctx context.Context, condition map[string]interface{}) (
	hasData bool, returnData base.BaseTotalLogModel, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var baseLog base.BaseTotalLogModel
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLog.TableName())
	if err != nil {
		return
	}

	db := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition)

	if dbErr := db.Order("id desc").First(&baseLog).Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			hasData = false
		} else {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", dbErr)
		}
		return
	}
	hasData = true
	returnData = baseLog
	return
}

// GetAllData 获取数据
func GetAllData(ctx context.Context, condition map[string]interface{}) (
	returnData []base.BaseTotalLogModel, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var baseLog base.BaseTotalLogModel
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLog.TableName())
	if err != nil {
		return
	}

	db := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition)
	if dbErr := db.Find(&returnData).Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", dbErr)
			return
		}
	}
	return
}

// BatchGetTagByUIDList TODO
func BatchGetTagByUIDList(ctx context.Context, uidList []string, sourceId, recordTag string) ([]*pb.UserTagItem,
	error) {

	var UserTagList []*pb.UserTagItem
	for _, uid := range uidList {
		var returnData []base.BaseTotalLogModel
		var baseLog base.BaseTotalLogModel
		var tagId string
		condition := map[string]interface{}{
			"uid":          uid,
			"account_type": account.AccountType_INTL,
			"Fsource_id":   sourceId,
			"storage_key":  recordTag,
		}
		userAccount := account.UserAccount{
			Uid:         uid,
			AccountType: account.AccountType_INTL,
		}
		accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLog.TableName())
		if err != nil {
			return nil, err
		}
		db := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition)
		if dbErr := db.Find(&returnData).Error; dbErr != nil {
			if errors.Is(dbErr, gorm.ErrRecordNotFound) {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", dbErr)
				return nil, err
			}
		}
		if len(returnData) != 0 {
			tagId = returnData[0].TagId
		}
		UserTagList = append(UserTagList, &pb.UserTagItem{
			Uid:   uid,
			TagId: tagId,
		})
	}
	return UserTagList, nil
}
