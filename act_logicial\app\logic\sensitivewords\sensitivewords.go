package sensitivewords

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/logic/common"
	"trpc.act.logicial/app/model/sensitivewords"
)

const (
	CONTENT_SECURITY_SCENE_ID = 1001
	NICK                      = "nick"
	NormalLabel               = 100 //文本类别:100：正常
	ValidateInvalidAccesskey  = 10400
)

// CheckSensitive 判断是不是敏感词
func CheckSensitive(ctx context.Context, message, gameId, openId, nick, platId string) (isSensitive bool, err error) {

	// 获取配置
	excludeWords := config.GetConfig().GameSecretMap[gameId].ExcludeWords
	fmt.Println("---------------excludeWords---------------")
	fmt.Printf("%#v\n", excludeWords)
	fmt.Printf("%#v\n", message)
	for _, item := range excludeWords {
		if strings.Contains(strings.ToLower(message), item) {
			return false, nil
		}
	}
	domain := config.GetConfig().GameSecretMap[gameId].ContentSecurityHost
	uri := config.GetConfig().ContentSecurityTextUri
	secretId := config.GetConfig().GameSecretMap[gameId].SecretId
	secretKey := config.GetConfig().GameSecretMap[gameId].SecretKey

	tasks := common.TasksList{}
	itemTasks := make([]*common.CheckDataItemTask, 0)
	itemTasks = append(itemTasks, &common.CheckDataItemTask{
		SceneId: CONTENT_SECURITY_SCENE_ID,
		Text:    message,
		Account: common.AccountInfo{
			Account:  openId,
			RoleName: nick,
			PlatId:   platId,
		},
	})
	tasks.Tasks = itemTasks
	jsonData, err := json.Marshal(tasks)
	if err != nil {
		fmt.Sprintf("CheckSensitive Failed to marshal JSON: %v", err)
		return false, err
	}

	rspBody, err := ContentSecurityPostRequest(ctx, common.PostParam{
		Domain:    domain,
		Uri:       uri,
		Body:      string(jsonData),
		SecretId:  secretId,
		SecretKey: secretKey,
	})
	if err != nil || len(rspBody.Data) == 0 {
		// 文档中说明国际服调用报错可放行，此处如rspBody.Data为空则放行
		switch rspBody.ErrCode {
		case ValidateInvalidAccesskey:
			return false, errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.InvalidAccesskey,
				"CheckSensitive ContentSecurityPostRequest error, validate consumer error,Invalid access key")
		}
		return false, err
	}
	return rspBody.Data[0].TextCheckResult.Label != NormalLabel, nil
}

func ContentSecurityPostRequest(ctx context.Context, postParam common.PostParam) (sensitivewords.Response, error) {

	rspData := sensitivewords.Response{}
	url := strings.Join([]string{postParam.Domain, postParam.Uri}, "")
	header := GetSignatureHeader(http.MethodPost, postParam.SecretId, postParam.SecretKey, postParam.Uri,
		postParam.QueryParams, postParam.Body)
	header["content-type"] = "application/json"
	optionOne := httpclient.ClientOption{
		URL:        url,
		Header:     header,
		Type:       http.MethodPost,
		PostString: postParam.Body,
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	if resultOption.RequestError != nil {
		// 请求失败
		return rspData, errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"ContentSecurityPostRequest http error, \t [Error]:{%v} ", url)
	}
	response := resultOption.Result
	if err := json.Unmarshal([]byte(response), &rspData); err != nil {
		return rspData, errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.JsonParseError,
			"parse result,result=%v, \t [Error]:{%v} ", response, err)
	}
	return rspData, nil

}

// GetCanonicalQueryString 根据请求参数生成CanonicalQueryString
func GetCanonicalQueryString(queryParams map[string]string) string {
	var buf bytes.Buffer
	if len(queryParams) < 1 {
		return ""
	}
	keys := make([]string, 0)
	for k := range queryParams {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for i := range keys {
		k := keys[i]
		buf.WriteString(url.PathEscape(k))
		buf.WriteString("=")
		buf.WriteString(url.PathEscape(queryParams[k]))
		buf.WriteString("&")
	}
	buf.Truncate(buf.Len() - 1)
	return buf.String()
}

// GetBodySign 获取body编码
func GetBodySign(body string) string {
	h := sha256.New()
	h.Write([]byte(body))
	res := hex.EncodeToString(h.Sum(nil))
	return res
}

// GetSignatureString 获取签名，method只能为GET或POST
func GetSignatureString(method string, secretId string, secretKey string, uri string, gmtDate string,
	queryParams map[string]string, body string) string {

	canonicalQueryString := GetCanonicalQueryString(queryParams)
	bodySign := GetBodySign(body)

	res := method + "\n"
	res = res + uri + "\n"
	res = res + canonicalQueryString + "\n"
	res = res + secretId + "\n"
	res = res + gmtDate + "\n"
	res = res + bodySign + "\n"

	hmacHash := hmac.New(sha256.New, []byte(secretKey))
	hmacHash.Write([]byte(res))

	hashResult := base64.StdEncoding.EncodeToString(hmacHash.Sum(nil))
	return hashResult
}

// GetSignatureHeader 获取鉴权头部，method只能为GET或POST
func GetSignatureHeader(method string, secretId string, secretKey string, uri string, queryParams map[string]string,
	body string) map[string]string {

	gmtDate := GetFormatNow()
	sign := GetSignatureString(method, secretId, secretKey, uri, gmtDate, queryParams, body)

	res := make(map[string]string)
	res["X-HMAC-SIGNATURE"] = sign
	res["X-HMAC-SECRET-ID"] = secretId
	res["X-HMAC-ALGORITHM"] = "hmac-sha256"
	res["Date"] = gmtDate
	return res
}

// GetFormatNow 获取时间字符串
func GetFormatNow() string {
	now := time.Now().UTC()
	strNow := now.Format("Mon, 02 Jan 2006 15:04:05 GMT")
	return strNow
}
