package nikke

import "trpc.act.logicial/app/model"

type CdkeyTmp0824Model struct {
	model.AppModel
}

// TableName .
func (CdkeyTmp0824Model) TableName() string {
	return "df_cdkey0824_temp"
}

// SendTemp 临时结构
type CdkeyTmp0824 struct {
	CdkeyTmp0824Model
	Cdkey       string `gorm:"type:varchar(32);column:cdkey;primary_key"`
	SendDate    string `gorm:"type:varchar(4);column:send_date;"`
	SendIp      string `gorm:"type:varchar(16);column:send_ip;"`
	Status      int32  `gorm:"type:smallint(6);column:status"`
	UID         string `gorm:"type:varchar(32);column:uid;"`
	AccountType string `gorm:"type:smallint(6);column:account_type;0"`
	UniqueKey   string `gorm:"type:varchar(30);column:unique_key;not null"`
}

// 充值返利表
type DfMdsFlowModel struct {
	model.AppModel
}

// TableName .
func (DfMdsFlowModel) TableName() string {
	return "t_df_mds_flow"
}

type DfMdsFlow struct {
	DfMdsFlowModel
	ID              uint32 `json:"id" column:"id"`
	UserOpenid      string `json:"user_openid" column:"user_openid"`
	GameCoinsAmount int32  `json:"game_coins_amount" column:"game_coins_amount"` // 游戏币数量
	HasReceived     int32  `json:"has_received" column:"has_received"`           // 是否领取
	SerialId        string `json:"serial_id" column:"serial_id"`                 // 流水号
	SendedAt        int64  `json:"sended_at" column:"sended_at"`                 // 发送时间

}

type DFCBTMdsFlowModel struct {
	model.AppModel
}

func (DFCBTMdsFlowModel) TableName() string {
	return "t_df_cbt_mds_flow"
}

type DfCBTMdsFlow struct {
	DFCBTMdsFlowModel
	ID                    uint32 `json:"id" column:"id"`
	UserOpenid            string `json:"user_openid" column:"user_openid"`
	GameCoinsAmount       int32  `json:"game_coins_amount" column:"game_coins_amount"`               // 游戏币数量
	HasReceived           int32  `json:"has_received" column:"has_received"`                         // 是否领取
	SerialId              string `json:"serial_id" column:"serial_id"`                               // 流水号
	SendedAt              int64  `json:"sended_at" column:"sended_at"`                               // 发送时间
	GreenGunSerialId      string `json:"green_gun_serial_id" column:"green_gun_serial_id"`           // 赠品：绿枪流水号
	GreenGunSendedAt      int64  `json:"green_gun_sended_at" column:"green_gun_sended_at"`           // 赠品：绿枪发货时间
	PurplePandentSerialId string `json:"purple_pandent_serial_id" column:"purple_pandent_serial_id"` // 赠品：紫吊坠流水号
	PurplePandentSendedAt int64  `json:"purple_pandent_sended_at" column:"purple_pandent_sended_at"` // 赠品：紫吊坠发货时间
	BlueGunSerialId       string `json:"blue_gun_serial_id" column:"blue_gun_serial_id"`             // 赠品：蓝枪流水号
	BlueGunSendedAt       int64  `json:"blue_gun_sended_at" column:"blue_gun_sended_at"`             // 赠品：蓝枪发货时间
}
