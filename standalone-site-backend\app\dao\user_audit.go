package dao

import (
	"gorm.io/gorm"
	"time"
	"trpc.publishing_application.standalonesite/app/constants"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

type UserAuditConditions struct {
	Ids         []int64
	IntlOpenid  string
	AuditStatus constants.UserAuditStatusT
	GtId        int64
	Type        int8
	Order       []*OrderConditions
	LtId        int64
}

func UserAuditCreate(userAudit *model.UserAudit) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.UserAudit{}).TableName()).Omit("User").Create(&userAudit).Error

	return err
}

func UserAuditDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserAudit{}).TableName()).Omit("User").Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func UserAuditUpdate(userAudit *model.UserAudit) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserAudit{}).TableName()).Where("id = ? AND is_del = ?", userAudit.ID, 0).Updates(&userAudit).Error
}

func UserAuditList(conditions *UserAuditConditions, offset, limit int, ignoreDelete bool) ([]*model.UserAudit, error) {
	var userAudits []*model.UserAudit
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserAudit{}).TableName())

	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}

	if len(conditions.Ids) > 0 {
		db = db.Where("id in ?", conditions.Ids)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.AuditStatus > 0 {
		db = db.Where("audit_status = ?", conditions.AuditStatus)
	}
	if conditions.GtId > 0 {
		db = db.Where("id > ?", conditions.GtId)
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if conditions.Type > 0 {
		db = db.Where("type = ?", conditions.Type)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}

	if ignoreDelete {
		db = db.Unscoped()
	}
	if err = db.Find(&userAudits).Error; err != nil {
		return nil, err
	}
	var userOpenids = make([]string, 0)
	for _, audit := range userAudits {
		userOpenids = append(userOpenids, audit.IntlOpenid)
	}
	if len(userOpenids) == 0 {
		return userAudits, err
	}
	var userInfos []*model.UserContent
	userInfos, err = GetUserListByOpenid(userOpenids)
	if err != nil {
		return nil, err
	}
	for i, audit := range userAudits {
		for _, info := range userInfos {
			if audit.IntlOpenid == info.IntlOpenid {
				userAudits[i].User = info
			}
		}
	}
	return userAudits, nil
}

func UserAuditCount(conditions *UserAuditConditions, ignoreDelete bool) (count int64, err error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserAudit{}).TableName())
	if len(conditions.Ids) > 0 {
		db = db.Where("id in ?", conditions.Ids)
	}
	if ignoreDelete {
		db = db.Unscoped()
	}
	if len(conditions.Ids) > 0 {
		db = db.Where("id in ?", conditions.Ids)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.AuditStatus > 0 {
		db = db.Where("audit_status = ?", conditions.AuditStatus)
	}
	if conditions.GtId > 0 {
		db = db.Where("id > ?", conditions.GtId)
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if conditions.Type > 0 {
		db = db.Where("type = ?", conditions.Type)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if err = db.Count(&count).Error; err != nil {
		return
	}
	return
}

func UserAuditGet(id int64, intlOpenid string) (*model.UserAudit, error) {
	var userAudit model.UserAudit
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserAudit{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if intlOpenid != "" {
		db = db.Where("intl_openid = ?", intlOpenid)
	}
	//db = db.Joins("User").Where("User.is_del = ?", 0).Order("User.id DESC")
	if err := db.First(&userAudit).Error; err != nil {
		return nil, err
	}
	userInfo, err := GetUserByIntlOpenid(userAudit.IntlOpenid)
	if err != nil {
		return nil, err
	}
	userAudit.User = userInfo
	return &userAudit, nil
}

func BatchUpdateUserAuditLanguage(list map[string][]string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserAudit{}).TableName()).Transaction(func(tx *gorm.DB) error {
		for language, openids := range list {
			err := tx.Unscoped().Where("intl_openid in ?", openids).Update("language", language).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}
