package hok

type HttpProxyRsp struct {
	Code     int32 `json:"code"`
	CodeType int32 `json:"code_type"`
	DataItem Data  `json:"data"`
}
type Data struct {
	MetricsDataList []MetricsData `json:"metrics_data_list"`
}

type MetricsData struct {
	Code        int    `json:"Code"`
	Msg         string `json:"Msg"`
	Target      Target `json:"Target"`
	TargetValue string `json:"TargetValue"`
}

type Target struct {
	CountryID  int    `json:"CountryID"`
	EndDay     string `json:"EndDay"`
	HeroID     int    `json:"HeroID"`
	HeroIDList string `json:"HeroIDList"`
	OpenID     string `json:"OpenID"`
	SkinID     int    `json:"SkinID"`
	SkinIDList string `json:"SkinIDList"`
	StartDay   string `json:"StartDay"`
	TargetID   string `json:"TargetID"`
	Wave       int    `json:"Wave"`
	ZoneID     int    `json:"ZoneID"`
}
