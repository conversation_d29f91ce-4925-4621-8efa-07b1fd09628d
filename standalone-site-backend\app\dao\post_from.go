package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetPostFrom(id int64, postUuid, intlOpenid string) (*model.PostFrom, error) {
	var postFrom model.PostFrom

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostFrom{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if postUuid != "" {
		db = db.Where("post_uuid = ?", postUuid)
	}
	if intlOpenid != "" {
		db = db.Where("intl_openid = ?", intlOpenid)
	}
	err := db.First(&postFrom).Error
	if err != nil {
		return nil, err
	}
	return &postFrom, nil
}

func GetPostFromList(limit int) ([]*model.PostFrom, error) {
	var postFrom []*model.PostFrom

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostFrom{}).TableName())

	if limit > 0 {
		db = db.Limit(limit)
	}
	err := db.Unscoped().Order("id DESC").Find(&postFrom).Error
	if err != nil {
		return nil, err
	}
	return postFrom, nil
}

// 查询用户是否有从独立站上线推广H5页面过来发布过帖子
func CheckIfUserHasPostByChannel(intlOpenid string, sourceType int) (bool, error) {
	if intlOpenid == "" {
		return false, nil
	}
	var count int64
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostFrom{}).TableName()).
		Where("intl_openid = ?", intlOpenid).
		Where("`source` = ?", sourceType).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func CreatePostFrom(postFrom *model.PostFrom) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostFrom{}).TableName()).Create(&postFrom).Error
}
