package model

type AvatarPendantLanguage struct {
	*Model
	ID              int64  `gorm:"column:id;primary_key" json:"id"`                   //
	GameID          string `gorm:"column:game_id" json:"game_id"`                     //游戏id
	AreaID          string `gorm:"column:area_id" json:"area_id"`                     //区域id
	AvatarPendantID int64  `gorm:"column:avatar_pendant_id" json:"avatar_pendant_id"` //挂件id
	Language        string `gorm:"column:language" json:"language"`                   //语言
	Title           string `gorm:"column:title" json:"title"`                         //标题
	Condition       string `gorm:"column:condition" json:"condition"`                 //条件
	JumpURL         string `gorm:"column:jump_url" json:"jump_url"`                   //跳转链接
	Creator         string `gorm:"column:creator" json:"creator"`                     //创建人
	Updater         string `gorm:"column:updater" json:"updater"`                     //更新人
}

func (a *AvatarPendantLanguage) TableName() string {
	return "p_avatar_pendant_language"
}
