package cache

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"trpc.publishing_application.standalonesite/app/constants"
)

type FromCacheSuccHandleFunc = func(context.Context, interface{}) (interface{}, error)
type FromCacheFailedHandleFunc = func(context.Context) (interface{}, error)

/*
*
*	从缓存中获取数据，缓存中红获取数据失败，则调用fromCacheFailedHandle 函数，成功执行 fromCacheSuccHandle
result: 传入一个地址： &Type{}
*
*/
func GetCacheWithUnmarshal(c context.Context, redisKey string, result interface{}, fromCacheSuccHandle *FromCacheSuccHandleFunc, fromCacheFailedHandle *FromCacheFailedHandleFunc, withCache bool) (res interface{}, err error) {
	// c := context.Background()
	if redisKey == "" {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetCacheWithUnmarshal redis key is %s, err is %s", redisKey, err.Error())
		if fromCacheFailedHandle == nil {
			return nil, nil
		} else {
			return (*fromCacheFailedHandle)(c)
		}
	}
	if withCache {
		if res, err := redis.GetClient().Get(c, redisKey).Result(); err == nil {
			if res == "" {
				return (*fromCacheFailedHandle)(c)
			} else {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetCacheWithUnmarshal redis key is %s, res is %s", redisKey, res)
				// unmarshal
				// resInterface := interface{}(nil)
				// var re any
				err := json.Unmarshal([]byte(res), &result)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetCacheWithUnmarshal redis key is %s, res is %s, err is %s", redisKey, res, err.Error())
					// return nil, errs.NewCustomError(context.Background(), code.ErrCodeUnmarshalFailed, "GetCacheWithUnmarshal failed")
					// unmarshal failed
					if fromCacheFailedHandle == nil {
						return nil, nil
					} else {
						return (*fromCacheFailedHandle)(c)
					}
				} else {
					if fromCacheSuccHandle == nil {
						return result, nil
					} else {
						return (*fromCacheSuccHandle)(c, result)
					}

				}
			}
		}
	}
	res, err = (*fromCacheFailedHandle)(c)
	// result = res
	return res, err
}

// 设置rediskey
func SetCacheWithMarshal(c context.Context, redisKey string, res interface{}, du time.Duration) error {
	if redisKey == "" {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetCacheWithMarshal redis key is %s, failed for empty key", redisKey)
		return nil
	}
	if res == nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetCacheWithMarshal redis key is %s, res is nil", redisKey)
		return nil
	}
	// marshal
	resBytes, err := json.Marshal(res)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetCacheWithMarshal redis key is %s, res is %s, err is %s", redisKey, res, err.Error())
		return err
	}
	// set cache
	err = redis.GetClient().Set(c, redisKey, string(resBytes), du).Err()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetCacheWithMarshal redis key is %s, res is %s,	err is %s", redisKey, res, err.Error())
		return err
	}
	return nil
}
