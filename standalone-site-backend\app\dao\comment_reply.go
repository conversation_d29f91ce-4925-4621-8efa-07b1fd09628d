package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

func CommentReplyListForDelete(commentId int64) ([]*model.CommentReplyForDelete, error) {
	var comments []*model.CommentReplyForDelete
	var err error

	if err = DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName()).Where("is_del = ?", 0).Where("is_parent_del = ?", 0).Where("comment_id = ?", commentId).Where("reply2reply_id > 0").Find(&comments).Error; err != nil {
		return nil, err
	}

	return comments, nil
}

func CommentReplyCreate(reply *model.CommentReply) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName()).Create(reply).Error

	return err
}

func CommentReplyGet(id int64) (*model.CommentReply, error) {
	var reply model.CommentReply
	db := DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName())
	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	} else {
		return nil, gorm.ErrRecordNotFound
	}

	err := db.First(&reply).Error
	if err != nil {
		return &reply, err
	}

	return &reply, nil
}

func CommentReplyGetNoIgnoreDel(id int64) (*model.CommentReply, error) {
	var reply model.CommentReply
	db := DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName())
	if id > 0 {
		db = db.Where("id = ?", id)
	} else {
		return nil, gorm.ErrRecordNotFound
	}

	err := db.Unscoped().First(&reply).Error
	if err != nil {
		return &reply, err
	}

	return &reply, nil
}

func CommentReplyDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName()).Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func CommentReplyDeleteByIds(Ids []int64) (int64, error) {
	res := DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName()).Where("id IN ? AND is_del = ?", Ids, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	})

	return res.RowsAffected, res.Error
}

func CommentReplyDeleteByCommentIds(commentIds []int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName()).Where("comment_id IN ?", commentIds).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func CommentReplyUpdate(reply *model.CommentReply) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName()).Where("id = ? AND is_del = ?", reply.ID, 0).Save(reply).Error
}

func CommentReplyUpdateUpvoteCount(id int64, upvoteCount int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName()).Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"modified_on":  time.Now().Unix(),
		"upvote_count": upvoteCount,
	}).Error
}

func CommentReplySetComentReplyParentDel(commentIds []int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName()).Where("id IN ? ", commentIds).Updates(map[string]interface{}{
		"modified_on":   time.Now().Unix(),
		"is_parent_del": 1,
	}).Error
}

func CommentReplyCount(conditions *model.ConditionsT) (int64, error) {
	var count int64
	db := DB.SelectConnect("db_standalonesite").Table((&model.CommentReply{}).TableName())
	for k, v := range *conditions {
		if k != "ORDER" {
			db = db.Where(k, v)
		}
	}
	if err := db.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}
