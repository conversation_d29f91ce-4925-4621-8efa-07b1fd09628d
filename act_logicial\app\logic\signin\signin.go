package siginin

import (
	"context"
	"fmt"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	utilModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	baseLogic "trpc.act.logicial/app/logic/base"
	"trpc.act.logicial/app/model/base"
)

const (
	// SigninStorageKey 签到Key
	SigninStorageKey = "signin"
)

// GetUserSignedInDates 获取用户已签到日期列表
func GetUserSignedInDates(ctx context.Context, sourceId string) ([]string, error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   sourceId,
		"storage_key":  SigninStorageKey,
	}
	allData, err := baseLogic.GetAllData(ctx, condition)
	if err != nil {
		return nil, err
	}
	signedList := make([]string, 0, len(allData))
	for _, v := range allData {
		signedList = append(signedList, v.Fday.Format("2006-01-02"))
	}
	return signedList, nil
}

// CheckHasSignin 判断是否签到
func CheckHasSignin(ctx context.Context, sourceId string, checkToday bool, timeZone int32) (hasSgin bool, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   sourceId,
		"storage_key":  SigninStorageKey,
	}
	hasData, lastData, errB := baseLogic.GetData(ctx, condition)
	if errB != nil {
		err = errB
		return
	}
	if !hasData {
		hasSgin = false
		return
	}
	duration, _ := time.ParseDuration(fmt.Sprintf("%vh", timeZone))
	todayDate := time.Now().Add(duration)
	fmt.Println("---------------today---------------")
	fmt.Printf("%#v\n", todayDate.Format("2006-01-02"))
	fmt.Printf("%#v\n", lastData.Fday.Format("2006-01-02"))
	if lastData.Fday.Format("2006-01-02") == todayDate.Format("2006-01-02") || !checkToday {
		hasSgin = true
	} else {
		hasSgin = false
	}
	return
}

// DoSignin 签到
func DoSignin(ctx context.Context, sourceId string, timeZone int32) (success bool, insertId int, signinDays int32,
	continuousSigninDays int32, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   sourceId,
		"storage_key":  SigninStorageKey,
	}
	hasData, lastData, errB := baseLogic.GetData(ctx, condition)
	if errB != nil {
		err = errB
		return
	}
	duration, _ := time.ParseDuration(fmt.Sprintf("%vh", timeZone))
	todayDate := time.Now().Add(duration)
	curDateTime, _ := time.ParseInLocation("2006-01-02 15:00:00", todayDate.Format("2006-01-02")+" 00:00:00", time.Local)

	// 没有数据 新增
	if !hasData {
		insertId, err = createSigninData(ctx, sourceId, curDateTime, 1, 1)
		if err == nil {
			success = true
		}
		signinDays = 1
		continuousSigninDays = 1
		return
	}
	// 如果当天返回报错

	if lastData.Fday.Format("2006-01-02") == todayDate.Format("2006-01-02") {
		success = false
		return
	}
	// 总签到天数+1
	// 判断是否是前一天，如果是前一天，连续签到天数+1,
	todayVal := 1

	if lastData.Fday.Format("2006-01-02") == todayDate.Add(-24*time.Hour).Format("2006-01-02") {
		todayVal = int(lastData.TodayValue) + 1
	}
	insertId, err = createSigninData(ctx, sourceId, curDateTime, lastData.TotalValue+1, todayVal)
	if err == nil {
		success = true
	}
	signinDays = int32(lastData.TotalValue + 1)
	continuousSigninDays = int32(todayVal)
	return
}

// GetSigninDays 获取签到天数
func GetSigninDays(ctx context.Context, sourceId string, continuous bool) (days int32, err error) {
	// 查询最近一天记录
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   sourceId,
		"storage_key":  SigninStorageKey,
	}
	hasData, lastData, errB := baseLogic.GetData(ctx, condition)
	if errB != nil {
		err = errB
		return
	}
	if !hasData {
		days = 0
		return
	}

	if continuous {
		days = int32(lastData.TodayValue)
	} else {
		days = int32(lastData.TotalValue)
	}
	return
}

func createSigninData(ctx context.Context, sourceId string, curDateTime time.Time, totalVal, todayVal int) (id int,
	err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	createData := base.BaseTotalLogModel{
		Fday:        curDateTime,
		Uid:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		StorageKey:  SigninStorageKey,
		FsourceId:   sourceId,
		TagId:       "",
		TotalValue:  totalVal,
		TodayValue:  todayVal,
	}
	var baseLogTable base.BaseTotalModel
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLogTable.TableName())
	if err != nil {
		return
	}
	dbErr := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Create(&createData).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	id = createData.ID
	return
}

// RollbackSignin TODO
func RollbackSignin(ctx context.Context, lastId int) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var baseLogTable base.BaseTotalModel
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLogTable.TableName())
	if err != nil {
		return
	}
	res := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where("id = ?", lastId).Where("uid = ?",
		userAccount.Uid).Where("account_type = ?", userAccount.AccountType).Delete(&base.BaseTotalLogModel{})
	if res.RowsAffected == 0 {

		return
	}
	if res.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", res.Error.Error())
		return
	}
	return
}
