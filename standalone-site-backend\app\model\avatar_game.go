package model

type AvatarGame struct {
	*Model
	ID          int64  `gorm:"column:id;primary_key" json:"id"`           //
	GameID      string `gorm:"column:game_id" json:"game_id"`             //游戏id
	AreaID      string `gorm:"column:area_id" json:"area_id"`             //区域id
	AvatarID    int64  `gorm:"column:avatar_id" json:"avatar_id"`         //头像id
	GameIntelID int64  `gorm:"column:game_intel_id" json:"game_intel_id"` //游戏intl_id
}

func (a *AvatarGame) TableName() string {
	return "p_avatar_game"
}
