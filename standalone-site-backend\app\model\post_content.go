package model

import (
	"errors"
	"fmt"

	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/util"
)

type PostContent struct {
	*Model
	PostUUID        string `gorm:"column:post_uuid" json:"post_uuid"`               //动态唯一ID
	IntlOpenid      string `gorm:"column:intl_openid" json:"intl_openid"`           //INTLSDK登录的用户openid
	IsEssence       int32  `gorm:"column:is_essence" json:"is_essence"`             //是否精华
	EssenceOn       int64  `gorm:"column:essence_on" json:"essence_on"`             //设置精华时长
	IsOriginal      int32  `gorm:"column:is_original" json:"is_original"`           //是否原创 1是 2不是
	OriginalURL     string `gorm:"column:original_url" json:"original_url"`         //非原创内容引用的链接
	Platform        string `gorm:"column:platform" json:"platform"`                 //社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
	OriginalReprint int32  `gorm:"column:original_reprint" json:"original_reprint"` //原创是否允许转载 1 允许 2 不允许
	Title           string `gorm:"column:title" json:"title"`                       //标题
	Content         string `gorm:"column:content" json:"content"`                   //内容
	PicUrls         string `gorm:"column:pic_urls" json:"pic_urls"`                 //图片链接，逗号分隔
	ContentSummary  string `json:"content_summary"`
	ExtInfo         string `json:"ext_info"`
	Order           int32  `gorm:"column:order" json:"order"` // 顺序值
	Language        string `json:"language"`
	FriendCardInfo  string `json:"friend_card_info"` // 帖子发布时添加的用户游戏加好友信息卡快照，json数据
	GuildId         string `json:"guild_id"`         // 帖子发布时添加的用户公会卡快照
	CreatorStatementType constants.ECreatorStatementType `json:"creator_statement_type"` // 创作声明类型
	RiskRemindType       constants.ERiskRemindType       `json:"risk_remind_type"`       // 风险提醒类型
	AiContentType        constants.EAiContentType        `json:"ai_content_type"`        // AI内容类型
}

type PostContentTemp struct {
	*Model
	PostUUID        string `gorm:"column:post_uuid" json:"post_uuid"`               //动态唯一ID
	IntlOpenid      string `gorm:"column:intl_openid" json:"intl_openid"`           //INTLSDK登录的用户openid
	IsEssence       int32  `gorm:"column:is_essence" json:"is_essence"`             //是否精华
	PowerNum        int64  `gorm:"column:power_num" json:"power_num"`               //权重
	EssenceOn       int64  `gorm:"column:essence_on" json:"essence_on"`             //设置精华时长
	IsOriginal      int32  `gorm:"column:is_original" json:"is_original"`           //是否原创 1是 2不是
	OriginalURL     string `gorm:"column:original_url" json:"original_url"`         //非原创内容引用的链接
	Platform        string `gorm:"column:platform" json:"platform"`                 //社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
	OriginalReprint int32  `gorm:"column:original_reprint" json:"original_reprint"` //原创是否允许转载 1 允许 2 不允许
	Title           string `gorm:"column:title" json:"title"`                       //标题
	Content         string `gorm:"column:content" json:"content"`                   //内容
	PicUrls         string `gorm:"column:pic_urls" json:"pic_urls"`                 //图片链接，逗号分隔
	ContentSummary  string `json:"content_summary"`
	ExtInfo         string `json:"ext_info"`
	Order           int32  `gorm:"column:order" json:"order"` // 顺序值
	Language        string `json:"language"`
	CommentCount    int64  `gorm:"column:comment_count" json:"comment_count"`       //评论数
	CollectionCount int64  `gorm:"column:collection_count" json:"collection_count"` //收藏数
	UpvoteCount     int64  `gorm:"column:upvote_count" json:"upvote_count"`         //点赞总数
	UpvoteMap       string `gorm:"column:upvote_map" json:"upvote_map"`             //点赞Map
	BrowseCount     int64  `gorm:"column:browse_count" json:"browse_count"`         //浏览数
	ForwardCount    int32  `gorm:"column:forward_count" json:"forward_count"`       //转发数
}

type PostContentExtInfo struct {
	Platform        string `json:"platform"`
	VideoID         string `json:"video_id"`
	VideoPreviewUrl string `json:"video_preview_url"`
	VideoUrl        string `json:"video_url"`
	VideoCover      string `json:"video_cover"`
}

func (p *PostContent) TableName() string {
	return "p_post_content"
}

// GetSubMeterTable 计算用户分表
func (p *PostContent) GetPostContentSubMeterTable(postuuid string) (string, error) {
	suffixId := util.ExtractSurplusByOpenid(postuuid, 2)
	if suffixId < 0 {
		return "", errors.New("get post_content table name failed")
	}
	tableName := fmt.Sprintf("%s_%d", "p_post_content", suffixId)
	return tableName, nil
}
