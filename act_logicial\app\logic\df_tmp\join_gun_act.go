package df_tmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/ams"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/logic/df_tmp_common"
	"trpc.act.logicial/app/model/df_activity"
	"trpc.act.logicial/app/mysql/df_activity_repo"
	"trpc.act.logicial/app/redis"
	"trpc.act.logicial/app/util"
)

// JoinGunActProc
type JoinGunActProc struct {
	userInfo *accountPb.UserAccount
	langType string
}

// JoinGunAct 加入拼枪活动
func JoinGunAct(ctx context.Context, req *pb.JoinGunActReq) (*pb.JoinGunActRsp, error) {
	log.DebugContextf(ctx, "JoinGunAct enter, req: %v", req)
	rsp := &pb.JoinGunActRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "JoinGunAct get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	proc := &JoinGunActProc{
		userInfo: &userAccount,
		langType: metadata.GetLangType(ctx),
	}
	defer func() {
		if nil != err {
			log.ErrorContextf(ctx, "JoinGunAct rsp error:%v", err)
		}
	}()
	err = proc.Process(ctx, req, rsp)
	if nil != err {
		return nil, err
	}
	return rsp, nil
}
func (p *JoinGunActProc) Process(ctx context.Context,
	req *pb.JoinGunActReq, rsp *pb.JoinGunActRsp) error {
	// 加锁
	key := redis.GetUserJoinGunActLockKey(req.GameId, p.userInfo.Uid)
	ok := redis.LockByKey(ctx, key, 10)
	if !ok {
		log.ErrorContextf(ctx, "JoinGunActProc LockByKey fail, key: %v", key)
		return code.ErrRequestFrequencyExceededLimitError
	}
	// 解锁
	defer func() {
		redis.UnLockByKey(ctx, key)
	}()

	// 校验活动时间
	err := df_tmp_common.CheckGunActivity(ctx, req.GameId, req.ActivityId)
	if nil != err {
		log.ErrorContextf(ctx, "JoinGunActProc CheckGunActivity error: %v", err)
		return err
	}
	// 检查枪械id是否合法
	_, err = df_tmp_common.GetHelpNumByGunId(req.GunId)
	if nil != err {
		log.ErrorContextf(ctx, "JoinGunActProc GetHelpNumByGunId error: %v", err)
		return err
	}

	// 检查是否参与过活动
	if err = p.checkLimit(ctx, req); nil != err {
		log.ErrorContextf(ctx, "JoinGunActProc checkLimit error: %v", err)
		return err
	}

	// 加入活动
	taskId := util.NewUUIDReplace()
	if err = p.joinAct(ctx, req, taskId); nil != err {
		log.ErrorContextf(ctx, "JoinGunActProc joinAct error: %v", err)
		return err
	}

	// 回包
	rsp.TaskId = taskId
	rsp.ShareLink = df_tmp_common.GetGunActShareUrl(req.GameId, req.ActivityId, taskId)
	return nil
}

// 加入活动
func (p *JoinGunActProc) joinAct(ctx context.Context,
	req *pb.JoinGunActReq, taskId string) error {
	gameName := df_tmp_common.GetGameName(req.GameId)
	err := df_activity_repo.GunActivityRepoClient.AddUserTask(ctx, &df_activity.GunActivityTask{
		GameId:      req.GameId,
		ActivityId:  req.ActivityId,
		UserId:      p.userInfo.Uid,
		OpenId:      p.userInfo.IntlAccount.OpenId,
		TaskId:      taskId,
		GunId:       req.GunId,
		LangType:    p.langType,
		AmsSerialId: ams.CreateAmsSerial(gameName, config.GetConfig().AmsAppId), // 发奖的时候会用到
	})
	if nil != err {
		log.ErrorContextf(ctx, "JoinGunActProc AddUserTask error: %v", err)
		return code.ErrSystemError
	}
	return nil
}

func (p *JoinGunActProc) checkLimit(ctx context.Context,
	req *pb.JoinGunActReq) error {
	// 获取已完成的数量
	var notInStatus []int
	notInStatus = append(notInStatus, df_activity.GunTaskStatusCancel)
	notInStatus = append(notInStatus, df_activity.GunTaskStatusNotCompleted)
	num, err := df_activity_repo.GunActivityRepoClient.GetUserTaskNumByGunId(ctx,
		req.GameId, req.ActivityId, p.userInfo.Uid, req.GunId, notInStatus) // 查询时排除掉超时未完成的任务
	if nil != err {
		log.ErrorContextf(ctx, "JoinGunActProc GetUserTaskNumByGunId error: %v", err)
		return code.ErrSystemError
	}
	if num > 0 {
		log.ErrorContextf(ctx, "JoinGunActProc GetUserTaskNumByGunId num: %v", num)
		return code.ErrActivityJoinReqLimit
	}
	// 获取总数
	totalNum, err := df_activity_repo.GunActivityRepoClient.GetUserTaskNumByGunId(ctx,
		req.GameId, req.ActivityId, p.userInfo.Uid, req.GunId, []int{}) // 查询所有任务总数
	if nil != err {
		log.ErrorContextf(ctx, "JoinGunActProc GetUserTaskNumByGunId get total num error: % v", err)
		return code.ErrSystemError
	}
	log.DebugContextf(ctx, "JoinGunActProc GetUserTaskNumByGunId totalNum: %v", totalNum)
	if totalNum > 1000 { // 增加保护， 避免用户大批量参加、取消刷爆存储
		log.ErrorContextf(ctx, "JoinGunActProc GetUserTaskNumByGunId too much task totalNum: %v", totalNum)
		return code.ErrActivityJoinReqLimit
	}
	return nil
}
