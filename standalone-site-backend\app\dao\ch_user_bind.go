package dao

import (
	"errors"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetBoundChUserInfo(intlOpenid string) (*model.ChUserBind, error) {
	chUserBind := &model.ChUserBind{}
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChUserBind{}).TableName())
	err := tx.Where("intl_openid = ?", intlOpenid).First(&chUserBind).Error
	return chUserBind, err
}

func ChangeSyncStatus(intlOpenid string, syncStatus int, language string) error {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChUserBind{}).TableName())
	err := tx.Where("intl_openid = ?", intlOpenid).Updates(map[string]interface{}{
		"sync_language": language,
		"is_auto_sync":  syncStatus,
	}).Error
	return err
}

func GetAllBoundInfoByChUid(chUid string) ([]*model.ChUserBind, error) {
	chUserBind := make([]*model.ChUserBind, 0)
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChUserBind{}).TableName())
	err := tx.Where("ch_uid = ?", chUid).Find(&chUserBind).Error
	return chUserBind, err
}

func GetAllBoundInfoByIntlOpenid(intlOpenid string) ([]*model.ChUserBind, error) {
	chUserBind := make([]*model.ChUserBind, 0)
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChUserBind{}).TableName())
	err := tx.Where("intl_openid = ?", intlOpenid).Find(&chUserBind).Error
	return chUserBind, err
}

//	func CreateAccountBindInfo(intlOpenid, chUid string, language string) error {
//		tx := DB.SelectConnect("db_standalonesite").Table((&model.ChUserBind{}).TableName())
//		err := tx.Create(&model.ChUserBind{
//			IntlOpenid:     intlOpenid,
//			ChUid:          chUid,
//			IsAutoSync:     0,
//			AbnormalStatus: int(constants.CH_USER_STATUS_NORMAL),
//			SyncLanguage:   language,
//		}).Error
//		return err
//	}
//
// A B 0; A B 1;
// A B
func CreateOrUpdateAccountBindInfo(intlOpenid, chUid, language string, gameId string, areaId string, country string) error {
	err := DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		chUserBindModel := &model.ChUserBind{}
		err := tx.Table((&model.ChUserBind{}).TableName()).Unscoped().Where("intl_openid = ?", intlOpenid).Find(&chUserBindModel).Error
		if errors.Is(err, gorm.ErrRecordNotFound) || chUserBindModel.Model == nil || chUserBindModel.Model.ID == 0 {
			// 未找到对应内容，新建
			err = tx.Table((&model.ChUserBind{}).TableName()).Create(&model.ChUserBind{
				IntlOpenid:     intlOpenid,
				ChUid:          chUid,
				IsAutoSync:     0,
				AbnormalStatus: constants.CH_USER_STATUS_NORMAL,
				SyncLanguage:   language,
				GameId:         gameId,
				AreaId:         areaId,
				Country:        country,
			}).Error
		} else {
			// 更新
			err = tx.Table((&model.ChUserBind{}).TableName()).Where("intl_openid = ?", intlOpenid).Updates(map[string]interface{}{
				"ch_uid":          chUid,
				"is_auto_sync":    0,
				"abnormal_status": constants.CH_USER_STATUS_NORMAL,
				"sync_language":   language,
				"game_id":         gameId,
				"area_id":         areaId,
				"is_del":          0,
				"deleted_on":      0,
				"country":         country,
			}).Error
		}
		return err
	})
	return err
}

func GetAllBoundInfosByChUids(chUids []string) ([]*model.ChUserBind, error) {
	chUserBind := make([]*model.ChUserBind, 0)
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChUserBind{}).TableName())
	err := tx.Where("ch_uid in (?)", chUids).Find(&chUserBind).Error
	return chUserBind, err
}

// 冻结账户
func FreezedChUser(intlOpenid string, chUid string, abnormalStatus int) error {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChUserBind{}).TableName())
	err := tx.Where("intl_openid = ? and ch_uid = ?", intlOpenid, chUid).Updates(map[string]interface{}{
		"abnormal_status": abnormalStatus,
	}).Error
	return err
}

func GetBoundChUserInfoByContries(inContries []constants.ECreatorHubCountry, notInCountries []constants.ECreatorHubCountry, withNormalStatus bool, limit int64) ([]*model.ChUserBind, error) {
	chUserBind := make([]*model.ChUserBind, 0)
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChUserBind{}).TableName())
	if len(inContries) > 0 {
		tx = tx.Where("country in (?)", inContries)
	} else if len(notInCountries) > 0 {
		tx = tx.Where("country not in (?)", notInCountries)
	}
	if withNormalStatus {
		tx = tx.Where("abnormal_status = ?", constants.CH_USER_STATUS_NORMAL)
	}
	if limit > 0 {
		tx = tx.Limit(int(limit))
	}
	err := tx.Find(&chUserBind).Error
	return chUserBind, err
}

func GetAllChBoundInfos() ([]*model.ChUserBind, error) {
	chUserBind := make([]*model.ChUserBind, 0)
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChUserBind{}).TableName())
	err := tx.Where("is_del = 0").Find(&chUserBind).Error
	return chUserBind, err
}

func UpdateCountry(id int64, country string) error {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChUserBind{}).TableName())
	err := tx.Where("id = ?", id).Updates(map[string]interface{}{
		"country": country,
	}).Error
	return err
}
