package common

import (
	"context"
	"strings"

	"github.com/spf13/cast"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/pkg/metadatadecode"
	"trpc.publishing_application.standalonesite/app/util"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	"git.code.oa.com/trpc-go/trpc-go"
	"trpc.publishing_application.standalonesite/app/config"
)

const (
	// ReportActionComment 评论上报
	ReportActionComment = "standalonesite_post_comment"
	// ReportActionUpVote 点赞上报
	ReportActionUpVote = "standalonesite_post_up_vote"
	// ReportActionShare 分享上报
	ReportActionShare = "standalonesite_post_share"
	// ReportActionPostPublish 发布上报
	ReportActionPostPublish = "standalonesite_post_published"
	// ReportActionDeletePost 删除动态
	ReportActionDeletePost = "standalonesite_post_delete"
	// ReportActionDeleteComment 删除评论
	ReportActionDeleteComment = "standalonesite_post_delete_comment"
	// ReportActionFollow 用户关注
	ReportActionFollow = "standalonesite_user_follow"
	// ReportActionUserModify 用户修改信息
	ReportActionUserModify = "standalonesite_user_modify"
	// ReportBindCreatorhub
	ReportBindCreatorhub = "standalonesite_backup_bind_creatorhub_ret"
	// report 自动同步
	ReportCreatorhubAutoSync = "standalonesite_backup_creatorhub_atuo_send"
)

// ReportComment 上报
func ReportPostCommentLog(ctx context.Context, comment *model.Comment, content string, userInfo *model.UserContent, err error) {
	// 测试环境不上报（压测）
	if trpc.GlobalConfig().Global.EnvName == "test" {
		return
	}
	// return
	var errCode int
	if err != nil {
		errCode, _, _ = Dec(err)
		if errCode == 0 {
			errCode = 1
		}
	}
	var gameId string
	fatherContentId := comment.ReplyUUID
	actType := comment.Type
	if constants.CommentType(comment.Type) == constants.DYNAMIC_REPLY {
		fatherContentId = comment.Reply2ReplyUUID
	}
	cmsState := 0
	if comment.IsAudit == 1 {
		cmsState = 1
	}
	gameId = metadatadecode.GetIntlGameId(ctx)
	_, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	_, intlOpenid := SplitLipOpenid(userInfo.IntlOpenid)
	reportParams := metadatadecode.GetReportParams(ctx)
	tlogData := report.ReportTlogData{
		Host: config.GetConfig().Report.Host,
		Path: config.GetConfig().Report.Path,
		Header: report.ReportTlogHeader{
			XLanguage: reportParams.Lang,
			XGameId:   30054,
			XSource:   "pc_web",
		},
		Action:         ReportActionComment,
		SubAction:      "",
		OriginalGameId: gameId,
		ExtentContent: map[string]interface{}{
			"game_id":           gameId,
			"user_area_id":      areaId,
			"open_id":           intlOpenid,
			"ret":               errCode,
			"content_id":        comment.CommentUUID,
			"father_content_id": fatherContentId,
			"feed_id":           comment.PostUUID,
			"acttype":           actType,
			"cmsstate":          cmsState,
			"env":               trpc.GlobalConfig().Global.EnvName,
			"content":           content,
			"nick_name":         userInfo.Username,
			"layer_type":        comment.CommentBubbleId > 0,
			"lang":              reportParams.Lang,
			"scene":             reportParams.Scene,
			"page_id":           reportParams.PageID,
			"client_type":       reportParams.ClientType,
		},
	}
	report.ReportTlog(ctx, tlogData)
}

func ReportPostUpVoteLog(ctx context.Context, lipOpenId string, post *model.Post, comment *model.Comment, isPostUpVote bool, gameId string, err error) {
	// 测试环境不上报（压测）
	if trpc.GlobalConfig().Global.EnvName == "test" {
		return
	}
	var errCode int
	if err != nil {
		errCode, _, _ = Dec(err)
		if errCode == 0 {
			errCode = 1
		}
	}
	var actType int
	var contentID string
	var plateId int32
	if isPostUpVote {
		actType = 1
		contentID = post.PostUUID
		plateId = post.PlateID
	} else {
		actType = 2
		contentID = comment.CommentUUID
	}

	reportParams := metadatadecode.GetReportParams(ctx)
	_, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	tlogData := report.ReportTlogData{
		Host: config.GetConfig().Report.Host,
		Path: config.GetConfig().Report.Path,
		Header: report.ReportTlogHeader{
			XLanguage: reportParams.Lang,
			XGameId:   30054,
			XSource:   "pc_web",
		},
		Action:         ReportActionUpVote,
		SubAction:      "",
		OriginalGameId: gameId,
		ExtentContent: map[string]interface{}{
			"game_id":      gameId,
			"user_area_id": areaId,
			"open_id":      lipOpenId,
			"ret":          errCode,
			"content_id":   contentID,
			"acttype":      actType,
			"module":       plateId,
			"env":          trpc.GlobalConfig().Global.EnvName,
			"lang":         reportParams.Lang,
		},
	}
	report.ReportTlog(ctx, tlogData)
}

func ReportPostShareLog(ctx context.Context, lipOpenId string, postUuid string, err error) {
	// 测试环境不上报（压测）
	if trpc.GlobalConfig().Global.EnvName == "test" {
		return
	}
	var errCode int
	if err != nil {
		errCode, _, _ = Dec(err)
		if errCode == 0 {
			errCode = 1
		}
	}
	reportParams := metadatadecode.GetReportParams(ctx)
	gameId := metadatadecode.GetIntlGameId(ctx)
	_, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	_, intlOpenid := SplitLipOpenid(lipOpenId)
	tlogData := report.ReportTlogData{
		Host: config.GetConfig().Report.Host,
		Path: config.GetConfig().Report.Path,
		Header: report.ReportTlogHeader{
			XLanguage: reportParams.Lang,
			XGameId:   30054,
			XSource:   "pc_web",
		},
		Action:         ReportActionShare,
		SubAction:      "",
		OriginalGameId: gameId,
		ExtentContent: map[string]interface{}{
			"game_id":      gameId,
			"user_area_id": areaId,
			"open_id":      intlOpenid,
			"ret":          errCode,
			"post_uuid":    postUuid,
			"env":          trpc.GlobalConfig().Global.EnvName,
			"lang":         reportParams.Lang,
			"scene":        reportParams.Scene,
			"page_id":      reportParams.PageID,
			"client_type":  reportParams.ClientType,
		},
	}
	report.ReportTlog(ctx, tlogData)
}

func ReportPostPublish(ctx context.Context, post *model.Post, tagIds []int64, postContent []*model.PostContent, updatePost bool, userInfo *model.UserContent, guildId string, err error) {
	// 测试环境不上报（压测）
	// if trpc.GlobalConfig().Global.EnvName == "test" {
	// 	return
	// }
	var errCode int
	if err != nil {
		errCode, _, _ = Dec(err)
		if errCode == 0 {
			errCode = 1
		}
	}
	cmsStats := 0
	if post.IsAudit == 1 {
		cmsStats = 1
	}
	reportParams := metadatadecode.GetReportParams(ctx)
	_, intlOpenid := SplitLipOpenid(userInfo.IntlOpenid)
	_, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	gameId := metadatadecode.GetIntlGameId(ctx)
	//多语言的帖子分批上报
	for _, content := range postContent {

		var contentList = make([]string, 0)

		// 判断帖子的内容的长度
		if post.Type == constants.POST_TYPE_RICH_TEXT {
			// 富文本需要裁剪到1000个字符以内
			contentList = handleContent(content.ContentSummary)
		} else if post.Type == constants.POST_TYPE_VIDEO_TEXT {
			contentList = handleContent(content.Content)
		} else {
			contentList = append(contentList, content.PicUrls)
		}
		for i, item := range contentList {
			tlogData := report.ReportTlogData{
				Host: config.GetConfig().Report.Host,
				Path: config.GetConfig().Report.Path,
				Header: report.ReportTlogHeader{
					XLanguage: reportParams.Lang,
					XGameId:   30054,
					XSource:   "pc_web",
				},
				Action:         ReportActionPostPublish,
				SubAction:      "",
				OriginalGameId: gameId,
				ExtentContent: map[string]interface{}{
					"game_id":        gameId,
					"user_area_id":   areaId,
					"open_id":        intlOpenid,
					"content_id":     post.PostUUID,
					"ret":            errCode,
					"created_at":     post.CreatedOn,
					"forum_id":       post.PlateID,
					"topic_id":       tagIds,
					"content_type":   post.Type,
					"content_lang":   content.Language,
					"env":            trpc.GlobalConfig().Global.EnvName,
					"module":         post.PlateID,
					"cmsstate":       cmsStats,
					"acttype":        cast.ToInt(updatePost),
					"content":        item,
					"seq_id":         i,
					"title":          content.Title,
					"nick_name":      userInfo.Username,
					"is_friend_card": content.FriendCardInfo != "",
					"lang":           reportParams.Lang,
					"scene":          reportParams.Scene,
					"page_id":        reportParams.PageID,
					"client_type":    reportParams.ClientType,
					"uninon_id":      guildId,
				},
			}
			report.ReportTlog(ctx, tlogData)
		}

	}

}

func handleContent(content string) []string {
	var contentList = make([]string, 0)
	if len(content) > 1000 {
		// 裁剪
		var size = 1000
		contentList = util.SafeSubstring(content, size)
	} else {
		contentList = append(contentList, content)
	}
	return contentList
}

func ReportPostDelete(ctx context.Context, lipOpenId string, post *model.Post, delType int, err error) {
	// 测试环境不上报（压测）
	if trpc.GlobalConfig().Global.EnvName == "test" {
		return
	}
	var errCode int
	if err != nil {
		errCode, _, _ = Dec(err)
		if errCode == 0 {
			errCode = 1
		}
	}
	_, openid := SplitLipOpenid(lipOpenId)
	_, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	reportParams := metadatadecode.GetReportParams(ctx)
	intlGameId := metadatadecode.GetIntlGameId(ctx)
	tlogData := report.ReportTlogData{
		Host: config.GetConfig().Report.Host,
		Path: config.GetConfig().Report.Path,
		Header: report.ReportTlogHeader{
			XLanguage: reportParams.Lang,
			XGameId:   30054,
			XSource:   "pc_web",
		},
		Action:         ReportActionDeletePost,
		SubAction:      "",
		OriginalGameId: intlGameId,
		ExtentContent: map[string]interface{}{
			"game_id":      intlGameId,
			"user_area_id": areaId,
			"open_id":      openid,
			"ret":          errCode,
			"content_id":   post.PostUUID,
			"env":          trpc.GlobalConfig().Global.EnvName,
			"module":       post.PlateID,
			"acttype":      delType,
			"lang":         reportParams.Lang,
			"scene":        reportParams.Scene,
			"page_id":      reportParams.PageID,
			"client_type":  reportParams.ClientType,
		},
	}
	report.ReportTlog(ctx, tlogData)
}

func ReportCommentDelete(ctx context.Context, lipOpenId string, comment *model.Comment, delType int, err error) {
	// 测试环境不上报（压测）
	if trpc.GlobalConfig().Global.EnvName == "test" {
		return
	}
	var errCode int
	if err != nil {
		errCode, _, _ = Dec(err)
		if errCode == 0 {
			errCode = 1
		}
	}
	_, openid := SplitLipOpenid(lipOpenId)
	intlGameId := metadatadecode.GetIntlGameId(ctx)
	reportParams := metadatadecode.GetReportParams(ctx)
	_, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	tlogData := report.ReportTlogData{
		Host: config.GetConfig().Report.Host,
		Path: config.GetConfig().Report.Path,
		Header: report.ReportTlogHeader{
			XLanguage: reportParams.Lang,
			XGameId:   30054,
			XSource:   "pc_web",
		},
		Action:         ReportActionDeleteComment,
		SubAction:      "",
		OriginalGameId: intlGameId,
		ExtentContent: map[string]interface{}{
			"game_id":      intlGameId,
			"user_area_id": areaId,
			"open_id":      openid,
			"ret":          errCode,
			"content_id":   comment.CommentUUID,
			"env":          trpc.GlobalConfig().Global.EnvName,
			"acttype":      delType,
			"lang":         reportParams.Lang,
			"scene":        reportParams.Scene,
			"page_id":      reportParams.PageID,
			"client_type":  reportParams.ClientType,
		},
	}
	report.ReportTlog(ctx, tlogData)
}

func ReportUserFollow(ctx context.Context, lipOpenId string, follow *model.UserCollection, isUnFollow bool, err error) {
	// 测试环境不上报（压测）
	if trpc.GlobalConfig().Global.EnvName == "test" {
		return
	}
	var errCode int
	if err != nil {
		errCode, _, _ = Dec(err)
		if errCode == 0 {
			errCode = 1
		}
	}
	var actType int
	if isUnFollow {
		actType = 2
	} else {
		actType = 1
	}
	_, openid := SplitLipOpenid(lipOpenId)
	intlGameId := metadatadecode.GetIntlGameId(ctx)
	reportParams := metadatadecode.GetReportParams(ctx)
	_, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	tlogData := report.ReportTlogData{
		Host: config.GetConfig().Report.Host,
		Path: config.GetConfig().Report.Path,
		Header: report.ReportTlogHeader{
			XLanguage: reportParams.Lang,
			XGameId:   30054,
			XSource:   "pc_web",
		},
		Action:         ReportActionFollow,
		SubAction:      "",
		OriginalGameId: intlGameId,
		ExtentContent: map[string]interface{}{
			"game_id":      intlGameId,
			"open_id":      openid,
			"user_area_id": areaId,
			"ret":          errCode,
			"dst_openid":   follow.ToIntlOpenid,
			"env":          trpc.GlobalConfig().Global.EnvName,
			"acttype":      actType,
			"lang":         reportParams.Lang,
			"scene":        reportParams.Scene,
			"page_id":      reportParams.PageID,
			"client_type":  reportParams.ClientType,
		},
	}
	report.ReportTlog(ctx, tlogData)
}

func ReportUserModifyInfo(ctx context.Context, lipOpenId string, username, userRemark, userAvatar string, err error) {
	// 测试环境不上报（压测）
	if trpc.GlobalConfig().Global.EnvName == "test" {
		return
	}
	var errCode int
	if err != nil {
		errCode, _, _ = Dec(err)
		if errCode == 0 {
			errCode = 1
		}
	}
	_, openid := SplitLipOpenid(lipOpenId)
	intlGameId := metadatadecode.GetIntlGameId(ctx)
	reportParams := metadatadecode.GetReportParams(ctx)
	_, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	tlogData := report.ReportTlogData{
		Host: config.GetConfig().Report.Host,
		Path: config.GetConfig().Report.Path,
		Header: report.ReportTlogHeader{
			XLanguage: reportParams.Lang,
			XGameId:   30054,
			XSource:   "pc_web",
		},
		Action:         ReportActionUserModify,
		SubAction:      "",
		OriginalGameId: intlGameId,
		ExtentContent: map[string]interface{}{
			"game_id":      intlGameId,
			"user_area_id": areaId,
			"open_id":      openid,
			"ret":          errCode,
			"username":     username,
			"remark":       userRemark,
			"avatar":       userAvatar,
			"env":          trpc.GlobalConfig().Global.EnvName,
			"lang":         reportParams.Lang,
			"scene":        reportParams.Scene,
			"page_id":      reportParams.PageID,
			"client_type":  reportParams.ClientType,
		},
	}
	report.ReportTlog(ctx, tlogData)
}

// SplitLipOpenid 切割传递进来的用户id，如果不能切割直接赋值会openid
func SplitLipOpenid(openid string) (gameId, intlOpenid string) {
	if strings.Contains(openid, "-") {
		splitData := strings.Split(openid, "-")
		gameId = splitData[0]
		intlOpenid = splitData[1]
	} else {
		intlOpenid = openid
	}
	return
}

// 用户绑定
func ReportBindCreatorhubLog(ctx context.Context, lipOpenId, uid string, err error) {
	// 测试环境不上报（压测）
	if trpc.GlobalConfig().Global.EnvName == "test" {
		return
	}
	var errCode int
	if err != nil {
		errCode, _, _ = Dec(err)
		if errCode == 0 {
			errCode = 1
		}
	}
	_, openid := SplitLipOpenid(lipOpenId)
	intlGameId := metadatadecode.GetIntlGameId(ctx)
	reportParams := metadatadecode.GetReportParams(ctx)
	_, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	tlogData := report.ReportTlogData{
		Host: config.GetConfig().Report.Host,
		Path: config.GetConfig().Report.Path,
		Header: report.ReportTlogHeader{
			XLanguage: reportParams.Lang,
			XGameId:   30054,
			XSource:   "pc_web",
		},
		Action:         ReportBindCreatorhub,
		SubAction:      "",
		OriginalGameId: intlGameId,
		ExtentContent: map[string]interface{}{
			"creatorhub_uid": uid,
			"game_id":        intlGameId,
			"user_area_id":   areaId,
			"open_id":        openid,
			"ret":            errCode,
			"env":            trpc.GlobalConfig().Global.EnvName,
			"lang":           reportParams.Lang,
			"scene":          reportParams.Scene,
			"page_id":        reportParams.PageID,
			"client_type":    reportParams.ClientType,
		},
	}
	report.ReportTlog(ctx, tlogData)
}

// 开启自动同步
func ReportAutoSyncLog(ctx context.Context, lipOpenId, uid string, auto int, err error) {
	// 测试环境不上报（压测）
	if trpc.GlobalConfig().Global.EnvName == "test" {
		return
	}
	var errCode int
	if err != nil {
		errCode, _, _ = Dec(err)
		if errCode == 0 {
			errCode = 1
		}
	}
	_, openid := SplitLipOpenid(lipOpenId)
	intlGameId := metadatadecode.GetIntlGameId(ctx)
	reportParams := metadatadecode.GetReportParams(ctx)
	_, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	tlogData := report.ReportTlogData{
		Host: config.GetConfig().Report.Host,
		Path: config.GetConfig().Report.Path,
		Header: report.ReportTlogHeader{
			XLanguage: reportParams.Lang,
			XGameId:   30054,
			XSource:   "pc_web",
		},
		Action:         ReportBindCreatorhub,
		SubAction:      "",
		OriginalGameId: intlGameId,
		ExtentContent: map[string]interface{}{
			"creatorhub_uid": uid,
			"game_id":        intlGameId,
			"user_area_id":   areaId,
			"open_id":        openid,
			"ret":            errCode,
			"auto":           auto,
			"env":            trpc.GlobalConfig().Global.EnvName,
			"lang":           reportParams.Lang,
			"scene":          reportParams.Scene,
			"page_id":        reportParams.PageID,
			"client_type":    reportParams.ClientType,
		},
	}
	report.ReportTlog(ctx, tlogData)
}
