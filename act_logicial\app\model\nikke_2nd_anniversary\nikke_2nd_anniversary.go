// Package nikketwondanniversary nikke 2周年
package nikketwondanniversary

import "trpc.act.logicial/app/model"

// NikkeTwondAnniversaryModel Model
type NikkeTwondAnniversaryModel struct {
	model.AppModel
}

// TableName .
func (NikkeTwondAnniversaryModel) TableName() string {
	return "nikke_2nd_anniversary_send"
}

// NikkeTwoAnniversary 临时结构
type NikkeTwoAnniversary struct {
	NikkeTwondAnniversaryModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(255);column:uid;"`
	Status      int    `gorm:"type:int(1);column:status;not null"`
	Tag         string `gorm:"type:varchar(255);column:tag;not null"`
	FsourceID   string `gorm:"type:varchar(255);column:Fsource_id;not null"`
	LangType    string `gorm:"type:varchar(255);column:lang_type;"`
	AccountType int    `gorm:"type:smallint(6);column:account_type" comment:"用户类型"`
}
