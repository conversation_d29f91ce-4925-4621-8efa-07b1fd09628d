package model

type InitAuthUserData struct {
	*Model
	AvatarUrl  string `json:"avatar_url"`
	Remark     string `json:"remark"`
	Username   string `json:"username"`
	GameId     string `json:"game_id"`
	AreaId     string `json:"area_id"`
	Creator    string `json:"creator"`
	Updater    string `json:"updater"`
	IntlOpenid string `json:"intl_openid"`
}

func (p *InitAuthUserData) TableName() string {
	return "p_init_auth_user_data"
}
