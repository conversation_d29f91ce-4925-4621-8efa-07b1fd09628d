package model

type GameLanguage struct {
	*Model
	GameId    int    `json:"game_id"`   //游戏id
	Language  string `json:"language"`  // 语言
	Name      string `json:"name"`      // 名称
	Introduce string `json:"introduce"` // 介绍
}

type GameLanguageFormated struct {
	ID        int64  `json:"id"`        // id
	GameId    int    `json:"game_t_id"` //游戏id
	Language  string `json:"language"`  // 语言
	Name      string `json:"name"`      // 名称
	Introduce string `json:"introduce"` // 介绍
}

func (t *GameLanguage) Format() *GameLanguageFormated {
	if t.Model == nil {
		return &GameLanguageFormated{}
	}

	return &GameLanguageFormated{
		ID:        t.ID,
		GameId:    t.GameId,
		Name:      t.Name,
		Introduce: t.Introduce,
		Language:  t.Language,
	}
}

func (t *GameLanguage) TableName() string {
	return "p_game_language"
}
