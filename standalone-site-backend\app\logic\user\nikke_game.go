package user

import (
	"context"
	"fmt"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	toolsPB "git.woa.com/trpcprotocol/publishing_application/stand_alone_site_game_tools"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
)

var (
	toolsProxy = toolsPB.NewToolsClientProxy()
)

func GetUserNikkeBasicInfo(c context.Context, queryIntlOpenid, version string) (*pb.GetUserGamePlayerInfoRsp, error) {
	// 后台相关接口已经做了15分钟缓存，这里就不用再缓存了
	// needCache = false
	var resp = &pb.GetUserGamePlayerInfoRsp{}

	// userRedisKey := cache.GetUserNikkeGameInfoKey(queryIntlOpenid)
	// if needCache {
	// 	if userCacheInfo, err := redis.GetClient().Get(c, userRedisKey).Result(); err == nil {
	// 		if userCacheInfo != "" {
	// 			playerInfo := &pb.GetUserGamePlayerInfoRsp{}
	// 			err = json.Unmarshal([]byte(userCacheInfo), playerInfo)
	// 			if err == nil {
	// 				return playerInfo, nil
	// 			} else {
	// 				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserNikkeBasicInfo json.Unmarshal err: %v", err)
	// 			}
	// 		}
	// 	}
	// }

	// 走trpc协议调用获取用户nikke游戏数据
	getUserPlayerBasicInfoReq := &toolsPB.GetUserPlayerBasicInfoReq{
		IntlOpenid: queryIntlOpenid,
		Version:    version,
	}
	playerInfo, err := toolsProxy.GetUserPlayerBasicInfoNoAuth(c, getUserPlayerBasicInfoReq)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserNikkeBasicInfo GetUserPlayerBasicInfoNoAuth err: %v\n", err)
		return resp, errs.NewCustomError(c, code.GetUserPlayerBasicInfoError, "Query Nikke player information abnormality")
	}
	resp.HasSavedRoleInfo = playerInfo.HasSavedRoleInfo
	// 用户尚未选择角色，直接返回
	if playerInfo.HasSavedRoleInfo {
		if playerInfo.RoleInfo != nil {
			resp.AreaId = fmt.Sprintf("%d", playerInfo.RoleInfo.AreaId)
			resp.RoleName = playerInfo.UserProfileItem.NickName
		}
		if playerInfo.PlayerBaseInfo != nil {
			resp.PlayerLevel = playerInfo.PlayerBaseInfo.PlayerLevel
			resp.OwnNikkeCnt = playerInfo.PlayerBaseInfo.OwnNikkeCnt
			resp.AvatarFrame = playerInfo.PlayerBaseInfo.AvatarFrame
		}
		if playerInfo.PlayerBattleInfo != nil {
			resp.TowerFloor = playerInfo.PlayerBattleInfo.TowerFloor
			resp.NormalProgress = playerInfo.PlayerBattleInfo.NormalProgress
			resp.HardProgress = playerInfo.PlayerBattleInfo.HardProgress
			resp.Costume = playerInfo.PlayerBattleInfo.Costume
		}
		if playerInfo.UserProfileItem != nil {
			resp.Icon = playerInfo.UserProfileItem.Icon
			resp.TeamCombat = playerInfo.UserProfileItem.TeamCombat
		}
	}

	// if needCache {
	// 	userCacheByte, err := json.Marshal(resp)
	// 	if err == nil {
	// 		redis.GetClient().SetEX(c, userRedisKey, string(userCacheByte), 2*time.Minute).Result()
	// 	}
	// }
	return resp, nil

}

// 客态获取别人的nikke角色信息，用于好友卡加好友场景
func GetOtherUserNikkeBasicInfo(c context.Context, queryIntlOpenid, version string, areaId int64, roleId string) (*pb.GetUserGamePlayerInfoRsp, error) {
	var resp = &pb.GetUserGamePlayerInfoRsp{}

	// 走trpc协议调用获取用户nikke游戏数据
	getUserRoleBasicInfoNoAuthReq := &toolsPB.GetUserRoleBasicInfoNoAuthReq{
		IntlOpenid: queryIntlOpenid,
		Version:    version,
		AreaId:     areaId,
		RoleId:     roleId,
	}
	roleBasicInfo, err := toolsProxy.GetUserRoleBasicInfoNoAuth(c, getUserRoleBasicInfoNoAuthReq)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetOtherUserNikkeBasicInfo GetUserRoleBasicInfoNoAuth error,getUserRoleBasicInfoNoAuthReq:%v, err: %v\n", getUserRoleBasicInfoNoAuthReq, err)
		return resp, errs.NewCustomError(c, code.GetOtherUserRoleBasicInfoError, "Query Nikke player information abnormality")
	}
	if roleBasicInfo.RoleInfo != nil {
		resp.AreaId = fmt.Sprintf("%d", roleBasicInfo.RoleInfo.AreaId)
		resp.RoleName = roleBasicInfo.RoleInfo.RoleName
	}
	if roleBasicInfo.PlayerBaseInfo != nil {
		resp.PlayerLevel = roleBasicInfo.PlayerBaseInfo.PlayerLevel
		resp.OwnNikkeCnt = roleBasicInfo.PlayerBaseInfo.OwnNikkeCnt
		resp.AvatarFrame = roleBasicInfo.PlayerBaseInfo.AvatarFrame
	}
	if roleBasicInfo.PlayerBattleInfo != nil {
		resp.TowerFloor = roleBasicInfo.PlayerBattleInfo.TowerFloor
		resp.NormalProgress = roleBasicInfo.PlayerBattleInfo.NormalProgress
		resp.HardProgress = roleBasicInfo.PlayerBattleInfo.HardProgress
		resp.Costume = roleBasicInfo.PlayerBattleInfo.Costume
	}
	if roleBasicInfo.UserProfileItem != nil {
		resp.Icon = roleBasicInfo.UserProfileItem.Icon
		resp.TeamCombat = roleBasicInfo.UserProfileItem.TeamCombat
	}

	return resp, nil

}
