package comment

//func MergePostCommentContexts(comments []*model.Comment) ([]*model.CommentMyFormated, error) {
//	PostUUIDs := make([]string, 0, len(comments))
//	userOpenids := make([]string, 0, len(comments))
//	for _, comment := range comments {
//		PostUUIDs = append(PostUUIDs, comment.PostUUID)
//		userOpenids = append(userOpenids, comment.IntlOpenid)
//	}
//
//	postContents, err := dao.PostContentList(&model.ConditionsT{
//		"post_uuid IN ?": PostUUIDs,
//		"ORDER":          "sort ASC",
//	}, 0, 0)
//	if err != nil {
//		return nil, err
//	}
//
//	users, err := dao.GetUserListByOpenid(userOpenids)
//	if err != nil {
//		return nil, err
//	}
//
//	userMap := make(map[string]*model.UserFormated, len(users))
//	for _, user := range users {
//		userMap[user.IntlOpenid] = user.Format()
//	}
//
//	contentMap := make(map[string][]*model.PostContentFormated, len(postContents))
//	for _, content := range postContents {
//		contentMap[content.PostUUID] = append(contentMap[content.PostUUID], content.Format())
//	}
//
//	// 数据整合
//	commentsFormated := make([]*model.CommentMyFormated, 0, len(comments))
//	for _, comment := range comments {
//		postFormated := comment.CommentMyFormated()
//		postFormated.User = userMap[comment.IntlOpenid]
//		if comment.Post != nil {
//			postFormated.Post = comment.Post.Format()
//			postFormated.Post.Contents = contentMap[comment.PostUUID]
//		}
//		postFormated.Contents = comment.CommentContentS
//
//		postFormated.Reply = comment.CommentReply
//
//		commentsFormated = append(commentsFormated, postFormated)
//
//	}
//	return commentsFormated, nil
//}
