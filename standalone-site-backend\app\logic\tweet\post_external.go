package tweet

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"github.com/spf13/cast"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

type PostThirdData struct {
	RankInfo *pb.CreatorHubActivityRank
	TaskInfo *pb.CreatorHubActivityInfo
}

func GetExternalPosts(c context.Context, req *pb.GetPostListReq, intlOpenID, language string, plateUniqueIdentifier string) (*pb.GetPostListRsp, error) {
	var err error

	postListRsp := &pb.GetPostListRsp{
		PageInfo: &pb.PageInfo{},
		List:     make([]*pb.GetPostRsp, 0),
	}
	var postList []*model.Post
	var taskPostMap map[string][]string
	var rankPostMap map[int64][]string
	var nextPageCursor, previousPageCursor string

	// 先获取缓存数据
	postListRedisKey := cache.GetExternalPostsKey(plateUniqueIdentifier, req.PlateId, language, req.TaskId, req.RankId, req.Platform, req.NextPageCursor, req.Limit)
	postListCacheInfo, err := redis.GetClient().Get(c, postListRedisKey).Result()
	if err == nil {
		if postListCacheInfo == "" {
			postListRsp.PageInfo.IsFinish = true
			return postListRsp, nil
		}
		err = json.Unmarshal([]byte(postListCacheInfo), postListRsp)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetExternalPosts cache json.Unmarshal error.postListRedisKey: %s, err: %v", postListRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetExternalPostsJsonUnmarshalError, "Failed to obtain post list, data parsing exception")
		} else {
			return postListRsp, nil
		}
	}
	// 查询类型：下一页数据
	if req.PageType == pb.PageType_NEXTPAGE {
		previousPageCursor = req.NextPageCursor
		var idCursor int64
		if req.NextPageCursor != "" {
			idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
			}
		}
		condition := &dao.FetchPostExternalCondition{
			PlateId: req.PlateId,
			Order:   make([]*dao.FetchPostExternalOrder, 0),
		}
		// condition.Order = append(condition.Order, &dao.FetchPostExternalOrder{
		// 	ColumnStr: "hot_num",
		// 	OrderKey:  "DESC",
		// }, &dao.FetchPostExternalOrder{
		// 	ColumnStr: "created_on_ms",
		// 	OrderKey:  "DESC",
		// })
		condition.Order = append(condition.Order, &dao.FetchPostExternalOrder{
			ColumnStr: "created_on_ms",
			OrderKey:  "DESC",
		})
		if intlOpenID != "" {
			condition.InterOpenid = intlOpenID
		}
		if idCursor > 0 {
			condition.IdCursor = idCursor
		}
		if !req.NeedAllRegion && language != "" {
			condition.Language = language
		}
		//if req.TaskId > 0 {
		//	condition.TaskId = cast.ToString(req.TaskId)
		//}
		if req.RankId > 0 {
			condition.RankId = req.RankId
		}
		if req.Platform != "" {
			condition.Platform = req.Platform
		}

		if plateUniqueIdentifier == constants.PLATE_CREATORHUB {
			taskIds, err := GetCreatorhubActivity(c, req.TaskId)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetExternalPosts | Failed to get task")
			}
			condition.TaskIds = taskIds
		}

		posts, err := dao.FetchPostExternal(condition, int(req.Limit))
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetExternalPosts | Failed to FetchPost")
		}
		postList, taskPostMap, rankPostMap = postExternalToPostData(posts)

		// 生成下一页的游标
		if len(postList) > 0 {
			nextPageCursor, err = util.EncryptPageCursorI(postList[len(postList)-1].CreatedOnMs)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetExternalPosts | Failed to create nextPageCursor")
			}
		}
	}
	// 转换成回包
	postListRsp.List, err = MergePosts(c, intlOpenID, postList, language, false)
	if err != nil {
		return nil, err
	}

	var postThirdRankInfo = make(map[string]*pb.CreatorHubActivityRank)
	var postThirdActivityInfo = make(map[string]*pb.CreatorHubActivityInfo)

	// 查询当前动态是否有关联赛道等信息
	if plateUniqueIdentifier == constants.PLATE_CREATORHUB {
		var rankIds []int64

		for rankId, _ := range rankPostMap {
			rankIds = append(rankIds, rankId)
		}
		// 获取赛道列表信息
		rankList, err := dao.GetRankListById(rankIds)
		if err == nil && len(rankList) > 0 {
			for _, list := range rankList {
				isExistLang := false
				// 判断是否有赛道对应的动态
				if postUuidsByRank, ok := rankPostMap[list.ID]; ok {
					for _, rankLanguage := range list.Languages {
						if rankLanguage.Language == language {
							for _, postUuid := range postUuidsByRank {
								postThirdRankInfo[postUuid] = &pb.CreatorHubActivityRank{
									Id:       int32(list.ID),
									RankName: rankLanguage.Name,
								}
							}
							isExistLang = true
							break
						}

					}
					if !isExistLang {
						for _, rankLanguage := range list.Languages {
							if rankLanguage.Language == "default" {
								for _, postUuid := range postUuidsByRank {
									postThirdRankInfo[postUuid] = &pb.CreatorHubActivityRank{
										Id:       int32(list.ID),
										RankName: rankLanguage.Name,
									}
								}
								break
							}

						}
					}

				}

			}
		}
		// 获取活动列表信息
		var taskIds []int64

		for taskId, _ := range taskPostMap {
			taskIds = append(taskIds, cast.ToInt64(taskId))
		}
		tasks, err := dao.GetCreatorHubActivityListByTaskIds(taskIds)
		if err == nil && len(tasks) > 0 {
			for _, task := range tasks {
				if postUuidsByTask, ok := taskPostMap[cast.ToString(task.TaskID)]; ok {

					isExistLang := false
					for _, tLang := range task.Languages {
						if tLang.Language == language {
							for _, postUuid := range postUuidsByTask {
								postThirdActivityInfo[postUuid] = &pb.CreatorHubActivityInfo{
									Id:       int32(task.ID),
									TaskId:   task.TaskID,
									TaskName: tLang.Name,
								}
							}
							isExistLang = true
							break
						}
					}
					if !isExistLang {
						for _, tLang := range task.Languages {
							if tLang.Language == "default" {
								for _, postUuid := range postUuidsByTask {
									postThirdActivityInfo[postUuid] = &pb.CreatorHubActivityInfo{
										Id:       int32(task.ID),
										TaskId:   task.TaskID,
										TaskName: tLang.Name,
									}
								}
								break
							}
						}
					}
				}
			}

		}

	}
	for _, rsp := range postListRsp.List {
		if taskData, ok := postThirdActivityInfo[rsp.PostUuid]; ok {
			rsp.TaskInfo = taskData
		}
		if rankData, ok := postThirdRankInfo[rsp.PostUuid]; ok {
			rsp.RankInfo = rankData
		}
	}
	if len(postListRsp.List) == 0 || len(postListRsp.List) < int(req.Limit) {
		postListRsp.PageInfo.IsFinish = true
	} else {
		postListRsp.PageInfo.NextPageCursor = nextPageCursor
	}
	postListRsp.PageInfo.PreviousPageCursor = previousPageCursor

	postListRspByte, err := json.Marshal(postListRsp)
	if err == nil {
		redis.GetClient().SetEX(c, postListRedisKey, string(postListRspByte), 2*time.Minute).Result()
	}

	return postListRsp, nil
}

func postExternalToPostData(postExternalList []*model.PostExternal) ([]*model.Post, map[string][]string, map[int64][]string) {
	var postList = make([]*model.Post, 0, len(postExternalList))
	var taskIds = make(map[string][]string)
	var rankIds = make(map[int64][]string)
	for _, external := range postExternalList {
		postList = append(postList, &model.Post{
			Model:             external.Model,
			PostUUID:          external.PostUUID,
			IntlOpenid:        external.IntlOpenid,
			PlateID:           external.PlateID,
			Language:          external.Language,
			Type:              external.Type,
			IsTop:             external.IsTop,
			TopSort:           external.TopSort,
			TopOn:             external.TopOn,
			IsAudit:           external.IsAudit,
			Visibility:        external.Visibility,
			SocialmediaPostId: external.SocialmediaPostId,
			LatestRepliedOn:   external.LatestRepliedOn,
			GameId:            external.GameId,
			AreaId:            external.AreaId,
			CreatedOnMs:       external.CreatedOnMs,
			Platform:          external.Platform,
		})
		if external.TaskId != "" {
			taskIds[external.TaskId] = append(taskIds[external.TaskId], external.PostUUID)
		}
		if external.RankId > 0 {
			rankIds[int64(external.RankId)] = append(rankIds[int64(external.RankId)], external.PostUUID)
		}
	}
	return postList, taskIds, rankIds
}

// 获取正在进行中的和已结束的6个活动
func GetCreatorhubActivity(c context.Context, taskId int32) ([]string, error) {
	var taskIds []string
	// 从p_ch表中捞取最近的活动，正在进行中的活动
	condition := &model.CreatorHubActivityWhere{
		NowTime: time.Now().Unix(),
		Status:  constants.CreatorhubActivityStatus_Published,
	}
	conditionByOver := &model.CreatorHubActivityWhereByStatus{
		Status: []int{constants.CreatorhubActivityStatus_Over},
		Order: map[string]string{
			"publish_time": "desc",
		},
		Limit: 6,
	}
	normalActivity, _, err := dao.GetActivityListByGtId(condition)
	if err != nil {
		return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "Failed to get normal activity")
	}
	// 只查询状态已经结束的
	activitiesList, err := dao.GetAllActivityListByStatus(conditionByOver)
	if err != nil {
		return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "Failed to get activity")
	}
	for _, activity := range normalActivity {
		taskIds = append(taskIds, cast.ToString(activity.TaskID))
	}
	for _, activity := range activitiesList {
		taskIds = append(taskIds, cast.ToString(activity.TaskID))
	}
	// 判断是否有筛选作品
	if taskId > 0 {
		for _, id := range taskIds {
			if id == cast.ToString(taskId) {
				return []string{id}, nil
			}
		}
		return []string{}, nil
	}
	return taskIds, nil
}
