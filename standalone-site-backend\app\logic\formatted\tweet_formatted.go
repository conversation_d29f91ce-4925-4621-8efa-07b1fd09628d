package formatted

import (
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/model"
)

func ReturnProtoTweetInfo(tweetInfo *model.PostFormatted) *pb.GetPostRsp {
	//var contents = make([]*pb.PostContent, 0, len(tweetInfo.Contents))
	//for _, content := range tweetInfo.Contents {
	//	contents = append(contents, &pb.PostContent{
	//		Id:      content.ID,
	//		PostId:  content.PostUUID,
	//		Content: content.Content,
	//		Type:    int32(content.Type),
	//		Sort:    content.Sort,
	//	})
	//}
	data := &pb.GetPostRsp{
		//Id:              tweetInfo.ID,
		//IntlOpenid:      tweetInfo.IntlOpenid,
		//User:            ReturnDynamicProtoUserInfoFormatted(tweetInfo.User),
		//Contents:        contents,
		//PowerNum:        tweetInfo.PowerNum,
		//CommentCount:    tweetInfo.CommentCount,
		//CollectionCount: tweetInfo.CollectionCount,
		//UpvoteCount:     tweetInfo.UpvoteCount,
		//BrowseCount:     tweetInfo.BrowseCount,
		//Visibility:      uint32(tweetInfo.Visibility),
		//IsTop:           int32(tweetInfo.IsTop),
		//TopSort:         int32(tweetInfo.TopSort),
		//TopOn:           tweetInfo.TopOn,
		//IsStar:          tweetInfo.IsStar,
		//IsComment:       tweetInfo.IsComment,
		//IsCollection:    tweetInfo.IsCollection,
		//IsFollow:        tweetInfo.IsFollow,
		//IsEssence:       int32(tweetInfo.EssenceOn),
		//EssenceOn:       tweetInfo.EssenceOn,
		//IsLock:          int32(tweetInfo.IsLock),
		//Type:            tweetInfo.Type,
		//IsAudit:         tweetInfo.IsAudit,
		//IsDel:           tweetInfo.IsDel,
		//CanDelete:       tweetInfo.CanDelete,
		//IsOriginal:      tweetInfo.IsOriginal,
		//OriginalUrl:     tweetInfo.OriginalUrl,
		//OriginalReprint: tweetInfo.OriginalReprint,
		//LatestRepliedOn: tweetInfo.LatestRepliedOn,
		//CreatedOn:       tweetInfo.CreatedOn,
		//ModifiedOn:      tweetInfo.ModifiedOn,
		//Tags:            tweetInfo.Tags,
		//AttachmentPrice: tweetInfo.AttachmentPrice,
		//IpLoc:           tweetInfo.IPLoc,
		//GameId:          tweetInfo.GameId,
		//GameName:        tweetInfo.GameName,
		//AreaId:          tweetInfo.AreaId,
		//Platform:        tweetInfo.Platform,
	}
	return data
}
