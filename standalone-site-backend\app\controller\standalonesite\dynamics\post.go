package dynamics

import (
	"context"
	"strings"

	"trpc.publishing_application.standalonesite/app/dao"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/logic/tweet"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/pkg/metadatadecode"
)

func (s *DynamicsImpl) CreatePostNew(c context.Context, req *pb.CreatePostNewReq) (*pb.CreatePostRsp, error) {
	var err error
	//获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid
	// err = user.CheckUserAdultStatus(c, myOpenid)
	// if err != nil {
	// 	return nil, err
	// }
	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}

	language := metadata.GetLangType(c)
	if language == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "language Parameter error")
	}

	// myOpenid := "29080-12945745392039390084"
	// gameId := "16"
	// areaId := "global"
	// language := "en"

	var isOfficial bool
	// 验证是否是官方号，不是的话报错
	authType := formatted.GetUserAuth(myOpenid)
	if authType == 1 || authType == 3 {
		isOfficial = true
	}

	// 验证是否被禁言了
	mute := formatted.GetUserMute(myOpenid)
	if mute {
		return nil, errs.NewCustomError(c, code.UserHasBeenBanned, "You have been banned, please try again later!")
	}

	// 如果板块是official,则必须是官方账号或者是机构账号
	plateInfo, err := dao.PlateGet(&dao.PlateConditions{
		Id: int64(req.PlateId),
	})
	if err != nil {
		return nil, errs.NewCustomError(c, code.InvalidParamsErr, "plate id failed")
	}
	if plateInfo.UniqueIdentifier == constants.PLATE_OFFICIAL && !isOfficial {
		return nil, errs.NewCustomError(c, code.NoPermission, "The current plate not permission")
	}

	if len(req.Contents) > 1 && !isOfficial {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "current user not official or agency")
	}

	// TODO 需要限制不同权限用户发布的板块动态权限是不一样的，例如普通用户只能发到nikkeart、outpost板块

	// 根据类型判断长度
	for i, content := range req.Contents {
		var checkContent = &tweet.CheckPostContent{
			Title:          content.Title,
			Content:        content.Content,
			ContentSummary: content.ContentSummary,
			PicUrls:        content.PicUrls,
			ContentType:    req.Type,
		}
		if err = tweet.CheckPostCreationReq(c, checkContent); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CheckPostCreationReq err: %v", err)
			return nil, err
		}
		req.Contents[i].ContentSummary = checkContent.ContentSummary
		req.Contents[i].PicUrls = checkContent.PicUrls

	}
	// 判断标签是否存在
	if len(req.Tags) > 0 {
		// 去重
		uniqueMap := make(map[int64]bool)
		var tagIdList []int64

		for _, tag := range req.Tags {
			if _, exists := uniqueMap[tag]; !exists {
				uniqueMap[tag] = true
				tagIdList = append(tagIdList, tag)
			}
		}
		req.Tags = tagIdList
		err := tweet.CheckTagIsExist(c, req.Tags, int64(req.PlateId))
		if err != nil {
			return nil, err
		}
	}

	isAudit := 2
	// 如果是白名单用户，则不需要走机审逻辑
	// exists, err := user.IsInUserWhiteList(c, gameId, areaId, myOpenid)
	// if err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePost err: %v\n", err)
	// 	return nil, err
	// }
	if isOfficial {
		isAudit = 1
	} else {
		// 用户发布动态限频校验
		if errCode := tweet.CreatePostFrequencyLimitCheck(c, myOpenid); errCode != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePostFrequencyLimitCheck err: %v", errCode)
			return nil, errCode
		}
	}

	// 用于压测创建数据
	if trpc.GlobalConfig().Global.EnvName == "test" && strings.Contains(myOpenid, "-100000") {
		isAudit = 1
	}

	post, err := tweet.CreatePostNew(c, myOpenid, req, isAudit, "", gameId, areaId, isOfficial, language)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePostNew err: %v\n", err)
		// return nil, errs.NewCustomError(c, code.CreatePostFailed, "An error occurred while creating a post. Please try again later.")
		return nil, err
	}

	tweet.CreatePostSetUserLimit(c, myOpenid)

	go user.DeleteUserInfoCache(c, myOpenid)

	return &pb.CreatePostRsp{
		PostData: post,
	}, nil
}

func (s *DynamicsImpl) OfficialPublishPostTimer(c context.Context, req *pb.OfficialPublishPostTimerReq) (*pb.OfficialPublishPostTimerRsp, error) {
	go tweet.OfficialPostPublishTimer(context.Background())
	return &pb.OfficialPublishPostTimerRsp{}, nil
}

func (s *DynamicsImpl) UpdatePost(c context.Context, req *pb.UpdatePostReq) (*pb.UpdatePostRsp, error) {
	var err error
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid

	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}

	language := metadata.GetLangType(c)
	if language == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "language Parameter error")
	}

	// myOpenid := "29080-12945745392039390084"
	// gameId := "16"
	// areaId := "global"
	// language := "en"

	var isOfficical bool
	// 验证是否是官方号，不是的话报错
	authType := formatted.GetUserAuth(myOpenid)
	if authType == 1 || authType == 3 {
		isOfficical = true
	}

	// 验证是否被禁言了
	mute := formatted.GetUserMute(myOpenid)
	if mute {
		return nil, errs.NewCustomError(c, code.UserHasBeenBanned, "You have been banned, please try again later!")
	}

	if len(req.Contents) == 0 {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "update post content params is empty")
	}

	// if len(req.Contents) > 1 && !isOfficical {
	// 	return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "current user not official or agency")
	// }
	// 根据类型判断长度
	for i, content := range req.Contents {
		var checkContent = &tweet.CheckPostContent{
			Title:          content.Title,
			Content:        content.Content,
			ContentSummary: content.ContentSummary,
			PicUrls:        content.PicUrls,
			ContentType:    req.Type,
		}
		if err = tweet.CheckPostCreationReq(c, checkContent); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CheckPostCreationReq err: %v", err)
			return nil, err
		}
		req.Contents[i].ContentSummary = checkContent.ContentSummary
		req.Contents[i].PicUrls = checkContent.PicUrls
	}
	// 判断标签是否存在
	if len(req.Tags) > 0 {
		req.Tags, err = tweet.CheckTag(c, req.Tags)
		if err != nil {
			return nil, err
		}
	}

	isAudit := 2
	// 如果是白名单用户，则不需要走机审逻辑
	exists, err := user.IsInUserWhiteList(c, gameId, areaId, myOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePost err: %v\n", err)
		return nil, err
	}
	if exists {
		isAudit = 1
	}

	post, err := tweet.UpdatePost(c, myOpenid, req, isAudit, gameId, areaId, isOfficical, language)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePost err: %v\n", err)
		return nil, err
	}

	go user.DeleteUserInfoCache(context.Background(), myOpenid)
	go cache.RemoveUserPostCacheKeys(myOpenid)
	go cache.DeleteUserPostsCache(myOpenid, "", 10)
	go cache.RemovePostTagsCacheKey(req.PostUuid)

	// 清除翻译信息
	go cache.RemovePostTranslational(req.PostUuid)

	return &pb.UpdatePostRsp{
		PostData: post,
	}, nil
}

func (s *DynamicsImpl) PostChangeTagBind(c context.Context, req *pb.PostChangeTagBindReq) (*pb.PostChangeTagBindRsp, error) {
	var err error
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid

	err = tweet.UpdatePostWithTag(c, req.PostUuid, myOpenid, req.TagIds)
	if err != nil {
		return nil, err
	}
	return &pb.PostChangeTagBindRsp{}, nil
}

func (s *DynamicsImpl) UpdateStatement(c context.Context, req *pb.UpdateStatementReq) (*pb.UpdateStatementRsp, error) {
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	intlOpenid := userAccount.Uid
	err = tweet.UpdatePostStatement(c, intlOpenid, req)
	return &pb.UpdateStatementRsp{}, err
}
