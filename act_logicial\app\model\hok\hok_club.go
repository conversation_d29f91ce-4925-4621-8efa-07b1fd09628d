// Package hok TODO
package hok

import "trpc.act.logicial/app/model"

// User TODO
type User struct {
	Uin         string            `json:"uin,omitempty"`          // qq/openid
	AreaId      string            `json:"area_id,omitempty"`      // 大区
	RoleId      string            `json:"role_id,omitempty"`      // 角色ID
	Platform    string            `json:"platform,omitempty"`     // 平台类型, 0-ios, 1-android
	AccType     string            `json:"acc_type,omitempty"`     // 手游账号类型, wx, qq
	PartitionId string            `json:"partition_id,omitempty"` // 小区
	Ext         map[string]string `json:"ext,omitempty"`          // 用户扩展数据信息(比如组队信息/动态用户参数场景等), 优先级高于任务静态扩展信息
}

// GetGroupTaskReq TODO
type GetGroupTaskReq struct {
	App      string `json:"app,omitempty"`
	User     User   `json:"user,omitempty"`
	GroupId  int32  `json:"groupid,omitempty"`
	UseCache bool   `json:"use_cache,omitempty"`
}

// GetGroupTaskRsp TODO
type GetGroupTaskRsp struct {
	Code int32               `json:"code,omitempty"`
	Data GetGroupTaskRspData `json:"data,omitempty"`
}

// GetGroupTaskRspData TODO
type GetGroupTaskRspData struct {
	GroupID   int32     `json:"groupid,omitempty"`
	TaskGroup TaskGroup `json:"taskgroup,omitempty"`
}

// TaskGroup TODO
type TaskGroup struct {
	GroupID    int32     `json:"groupid,omitempty"`
	GroupInfo  GroupInfo `json:"groupinfo,omitempty"`
	GroupTasks []*Task   `json:"grouptasks,omitempty"`
	GroupData  GroupData `json:"groupdata,omitempty"`
}

// Task TODO
type Task struct {
	TaskID   int32    `json:"taskid,omitempty"`
	TaskInfo TaskInfo `json:"taskinfo,omitempty"`
	TaskData TaskData `json:"taskdata,omitempty"`
	TaskGift TaskGift `json:"taskgift,omitempty"`
}

// TaskInfo TODO
type TaskInfo struct {
	ID      int32  `json:"id,omitempty"`
	Type    string `json:"type,omitempty"`
	Name    string `json:"name,omitempty"`
	Desc    string `json:"desc,omitempty"`
	Remark  string `json:"remark,omitempty"`
	Sort    int32  `json:"sort,omitempty"`
	Score   int32  `json:"score,omitempty"`
	IconURL string `json:"iconurl,omitempty"`
	StepID  int32  `json:"stepid,omitempty"`
}

// TaskData TODO
type TaskData struct {
	ID         int32             `json:"id,omitempty"`
	Start      string            `json:"start,omitempty"`
	End        string            `json:"end,omitempty"`
	Target     int32             `json:"target,omitempty"`
	Progress   int32             `json:"progress,omitempty"`
	IsFinished bool              `json:"isfinished,omitempty"`
	IsAwarded  bool              `json:"isawarded,omitempty"`
	Ext        map[string]string `json:"ext,omitempty"`
	Status     int32             `json:"status,omitempty"`
}

// TaskGift TODO
type TaskGift struct {
	ID      int32  `json:"id,omitempty"`
	Type    string `json:"type,omitempty"`
	Name    string `json:"name,omitempty"`
	Content string `json:"content,omitempty"`
}

// GroupInfo TODO
type GroupInfo struct {
	ID        int32  `json:"id,omitempty"`
	Type      string `json:"type,omitempty"`
	TypeInfo  string `json:"typeinfo,omitempty"`
	Name      string `json:"name,omitempty"`
	StartDate string `json:"startdate,omitempty"`
	EndDate   string `json:"enddate,omitempty"`
	Remarks   string `json:"remarks,omitempty"`
	IconURL   string `json:"iconurl,omitempty"`
	Steps     int32  `json:"steps,omitempty"`
}

// GroupData TODO
type GroupData struct {
	GroupID         int32 `json:"groupid,omitempty"`
	RedPoint        int32 `json:"redpoint,omitempty"`
	TaskNum         int32 `json:"tasknum,omitempty"`
	FinishedTaskNum int32 `json:"finishedtasknum,omitempty"`
	AwardedTaskNum  int32 `json:"awardedtasknum,omitempty"`
	TotalScore      int32 `json:"totalscore,omitempty"`
	AwardedScore    int32 `json:"awardedscore,omitempty"`
}

// HOKUserParticipationModel TODO
type HOKUserParticipationModel struct {
	model.AppModel
}

// TableName TODO
func (HOKUserParticipationModel) TableName() string {
	return "hok_club_user_participation"
}

// HokClubUserParticipation 用户参与表
type HokClubUserParticipation struct {
	HOKUserParticipationModel
	ID          uint   `gorm:"column:id; primary_key; auto_increment" json:"id"`
	UID         string `gorm:"column:uid" json:"uid"`
	AccountType int    `gorm:"column:account_type" json:"account_type"`
	LangType    string `gorm:"column:lang_type" json:"lang_type"`
}

// HOKUserParticipationV2Model TODO
type HOKUserParticipationV2Model struct {
	model.AppModel
}

// TableName TODO
func (HOKUserParticipationV2Model) TableName() string {
	return "hok_club_user_participation_v2"
}

// HokClubUserParticipationV2 用户参与表
type HokClubUserParticipationV2 struct {
	HOKUserParticipationV2Model
	ID          uint   `gorm:"column:id; primary_key; auto_increment" json:"id"`
	UID         string `gorm:"column:uid" json:"uid"`
	AccountType int    `gorm:"column:account_type" json:"account_type"`
	LangType    string `gorm:"column:lang_type" json:"lang_type"`
}

// HokPeriodicGiftClaimModel TODO
type HokPeriodicGiftClaimModel struct {
	model.AppModel
}

// TableName TODO
func (HokPeriodicGiftClaimModel) TableName() string {
	return "hok_periodic_gift_claim"
}

// HokPeriodicGiftClaim TODO
type HokPeriodicGiftClaim struct {
	HokPeriodicGiftClaimModel
	ID          uint   `gorm:"column:id; primary_key; auto_increment" json:"id"`
	UID         string `gorm:"column:uid" json:"uid"`
	AccountType int    `gorm:"column:account_type" json:"account_type"`
	PresentID   string `gorm:"column:present_id" json:"present_id"`
}
