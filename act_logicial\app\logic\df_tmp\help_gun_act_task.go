package df_tmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"github.com/spf13/cast"
	"time"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/common"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/logic/df_tmp_common"
	"trpc.act.logicial/app/model/df_activity"
	"trpc.act.logicial/app/mysql/df_activity_repo"
	"trpc.act.logicial/app/redis"
)

// HelpGunActTaskProc
type HelpGunActTaskProc struct {
	userInfo  *accountPb.UserAccount
	dstOpenId string
}

// HelpGunActTask 助力还有拼枪任务
func HelpGunActTask(ctx context.Context, req *pb.HelpGunActTaskReq) (*pb.HelpGunActTaskRsp, error) {
	log.DebugContextf(ctx, "HelpGunActTask enter, req: %v", req)
	rsp := &pb.HelpGunActTaskRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "HelpGunActTask get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	proc := &HelpGunActTaskProc{
		userInfo: &userAccount,
	}
	defer func() {

		if nil != err {
			log.ErrorContextf(ctx, "HelpGunActTask rsp error:%v", err)
		} else {
			// 上报tlog
			env := trpc.GlobalConfig().Global.EnvName
			extentContent := make(map[string]interface{})
			extentContent["env"] = env
			extentContent["dst_open_id"] = proc.dstOpenId
			extentContent["open_id"] = proc.userInfo.IntlAccount.OpenId
			action := "moddedfirearms_backup_support_friend"
			subAction := "cm_click"
			common.ReportTlog(ctx, action, subAction, cast.ToInt32(req.GameId), extentContent)
		}
	}()
	err = proc.Process(ctx, req, rsp)
	if nil != err {
		return nil, err
	}
	return rsp, nil
}
func (p *HelpGunActTaskProc) Process(ctx context.Context,
	req *pb.HelpGunActTaskReq, rsp *pb.HelpGunActTaskRsp) error {
	// 加锁
	key := redis.GetGunActTaskHelpLockKey(req.GameId, p.userInfo.Uid)
	ok := redis.LockByKey(ctx, key, 10)
	if !ok {
		log.ErrorContextf(ctx, "HelpGunActTaskProc LockByKey fail, key: %v", key)
		return code.ErrRequestFrequencyExceededLimitError
	}
	// 解锁
	defer func() {
		redis.UnLockByKey(ctx, key)
	}()

	// 检查活动是否存在
	err := df_tmp_common.CheckGunActivity(ctx, req.GameId, req.ActivityId)
	if nil != err {
		log.ErrorContextf(ctx, "HelpGunActTaskProc CheckGunActivity error:%v, "+
			"gameId: %v, activityId: %v", err, req.GameId, req.ActivityId)
		return err
	}
	err = p.checkLimit(ctx, req)
	if nil != err {
		return err
	}

	// 暂不使用事务处理，使用事务极端情况下会导致长时间加锁，导致后台定时任务被长时间锁住，导致性能下架。 允许极端并发情况下的冗余助力。
	// 查询任务
	task, err := p.getTask(ctx, req)
	if nil != err {
		log.ErrorContextf(ctx, "HelpGunActTaskProc getTask error:%v", err)
		return err
	}
	p.dstOpenId = task.OpenId
	if req.HelpType == pb.GunActHelpType_FollowCommunity { // 关注社群的助力值，只能用在自己的任务上
		if task.UserId != p.userInfo.Uid {
			log.ErrorContextf(ctx, "HelpGunActTaskProc follow community,user not match "+
				"userId: %v, req userId: %v, taskId: %v",
				task.UserId, p.userInfo.Uid, task.TaskId)
			return code.ErrParamError
		}
	} else {
		if task.UserId == p.userInfo.Uid { // 普通助力，不允许自己给自己助力
			log.ErrorContextf(ctx, "HelpGunActTaskProc not allow help self "+
				"userId: %v, req userId: %v, taskId: %v",
				task.UserId, p.userInfo.Uid, task.TaskId)
			return code.ErrActivityNotAllowHelpSelf
		}
	}
	// 获取枪械拼枪完成任务数
	needHelpNum, err := df_tmp_common.GetHelpNumByGunId(task.GunId)
	if nil != err {
		log.ErrorContextf(ctx, "HelpGunActTaskProc GetHelpNumByGunId error:%v, taskId: %v", err, task.TaskId)
		return err
	}

	// 写入助力记录
	err = p.addHelpRecord(ctx, req, task, needHelpNum)
	if nil != err {
		return err
	}
	return nil
}

// checkLimit 检查限制
func (p *HelpGunActTaskProc) checkLimit(ctx context.Context, req *pb.HelpGunActTaskReq) error {
	// 获取助力个数
	count, err := df_activity_repo.GunActivityRepoClient.GetTaskHelpNumByUser(ctx, req.GameId,
		req.ActivityId, p.userInfo.Uid, uint32(req.HelpType))
	if nil != err {
		log.ErrorContextf(ctx, "HelpGunActTaskProc checkLimit error:%v, userId: %v, taskId: %v",
			err, p.userInfo.Uid, req.HelpId)
	}

	// 检查限制
	limitNum := config.GetConfig().GunActUserHelpLimitNum
	if limitNum <= 0 { // 默认只能助力一次
		limitNum = 1
	}
	if req.HelpType == pb.GunActHelpType_Normal {
		limitNum = 1 // 关注社群只能使用助力一次
	}
	if int32(count) >= limitNum {
		log.ErrorContextf(ctx, "HelpGunActTaskProc checkLimit, userId: %v, "+
			"taskId: %v, num: %v, limitNum: %v, helpType: %v",
			p.userInfo.Uid, req.HelpId, count, limitNum, req.HelpType)
		return code.ErrActivityTaskHelpNumLimit
	}
	return nil
}

// addHelpRecord 添加助力记录
func (p *HelpGunActTaskProc) addHelpRecord(ctx context.Context, req *pb.HelpGunActTaskReq,
	task *df_activity.GunActivityTask, needHelpNum int32) error {
	// 查询任务已经完成的助力数
	num, err := df_activity_repo.GunActivityRepoClient.GetTaskHelpNum(ctx, task.TaskId, 0)
	if nil != err {
		log.ErrorContextf(ctx, "HelpGunActTaskProc GetTaskHelpNum error:%v, taskId: %v",
			err, task.TaskId)
		return code.ErrSystemError
	}

	if int32(num) >= needHelpNum { // 已经完成了任务数，不需要处理
		log.DebugContextf(ctx, "HelpGunActTaskProc task finished, "+
			"taskId: %v, help userId: %v, helpNum: %v, needNum: %v",
			task.TaskId, p.userInfo.Uid, num, needHelpNum)
		return code.ErrActivityTaskFinish
	}

	// 插入记录
	taskHelp := &df_activity.GunActivityTaskHelp{
		GameId:     req.GameId,
		ActivityId: req.ActivityId,
		UserId:     p.userInfo.Uid,
		TaskId:     task.TaskId,
		Email:      req.Email,
		HelpType:   int(req.HelpType),
	}
	err = df_activity_repo.GunActivityRepoClient.AddTaskHelpRecord(ctx, taskHelp)
	if nil != err {
		log.ErrorContextf(ctx, "HelpGunActTaskProc AddTaskHelpRecord error:%v, taskId: %v",
			err, task.TaskId)
		return code.ErrSystemError
	}

	// 查询任务最新已经完成的助力数
	num, err = df_activity_repo.GunActivityRepoClient.GetTaskHelpNum(ctx, task.TaskId, 0)
	if nil != err { // 非关键错误， 后台定时任务会继续完成处理
		log.ErrorContextf(ctx, "HelpGunActTaskProc GetTaskHelpNum get new error:%v, taskId: %v",
			err, task.TaskId)
		return nil
	}

	if int32(num) < needHelpNum { // 还未完成，不处理
		log.DebugContextf(ctx, "HelpGunActTaskProc task not finished, "+
			"taskId: %v, help userId: %v, helpNum: %v, needNum: %v",
			task.TaskId, p.userInfo.Uid, num, needHelpNum)
		return nil
	}
	// 已经完成，更新任务状态
	log.DebugContextf(ctx, "HelpGunActTaskProc task finished, "+
		"taskId: %v, help userId: %v, helpNum: %v, needNum: %v",
		task.TaskId, p.userInfo.Uid, num, needHelpNum)

	// 将状态有处理中改为等待领奖
	err = df_activity_repo.GunActivityRepoClient.UpdateTaskStatus(ctx, task.TaskId,
		df_activity.GunTaskStatusWaitClaimPrize, df_activity.GunTaskStatusProcessing)
	if nil != err { // 更新失败， 非关键错误，不返回错误，定时任务会继续处理
		log.ErrorContextf(ctx, "HelpGunActTaskProc UpdateTaskStatus error:%v, taskId: %v",
			err, task.TaskId)
	}
	return nil
}

// checkTaskTime 校验时间
func (p *HelpGunActTaskProc) checkTaskTime(ctx context.Context, task *df_activity.GunActivityTask) bool {
	// 判断任务时间是否到期
	// 任务过期时间
	taskExpireTime := config.GetConfig().GunActTaskExpireTime - 3 //减去3s，避免和定时任务处理时间冲突
	if taskExpireTime <= 0 {
		taskExpireTime = 3600 * 24 // 默认24小时
	}
	createTimeStamp := df_tmp_common.GetMysqlTimeStamp(task.CreateTime) // 修正时间
	nowTimeStamp := time.Now().Unix()
	log.DebugContextf(ctx, "HelpGunActTaskProc checkTaskTime createTimeStamp: %v, "+
		"nowTimeStamp: %v, CreateTime: %v, taskId: %v",
		createTimeStamp, nowTimeStamp, task.CreateTime, task.TaskId)
	if createTimeStamp+int64(taskExpireTime) >= nowTimeStamp { // 未到期
		return true
	}
	// 已经到期
	return false
}

func (p *HelpGunActTaskProc) getTask(ctx context.Context,
	req *pb.HelpGunActTaskReq) (task *df_activity.GunActivityTask, err error) {
	tasks, err := df_activity_repo.GunActivityRepoClient.GetTaskByTaskId(ctx,
		req.HelpId) // helpId就是任务id
	if nil != err {
		log.ErrorContextf(ctx, "HelpGunActTaskProc GetTaskByTaskId error: %v", err)
		return nil, code.ErrSystemError
	}
	if tasks == nil || len(tasks) <= 0 {
		log.ErrorContextf(ctx, "HelpGunActTaskProc GetTaskByTaskId not find task,"+
			" taskId: %v", req.HelpId)
		return nil, code.ErrActivityNotFindTask
	}
	task = tasks[0]
	// 判断任务状态
	if task.Status == df_activity.GunTaskStatusProcessing { // 正在助力中，返回任务，进行助力
		log.DebugContextf(ctx, "HelpGunActTaskProc task status is processing, "+
			"will help, taskId: %v, help userId: %v",
			task.TaskId, p.userInfo.Uid)
		return task, nil
	}
	// 判断任务是否到期，到期则不处理
	if !p.checkTaskTime(ctx, task) {
		log.ErrorContextf(ctx, "HelpGunActTaskProc task expire, taskId: %v", task.TaskId)
	}

	// 其他状态，要么任务取消，要么助力已完成，获取邀请用户的最新任务进行助力
	tasks, err = df_activity_repo.GunActivityRepoClient.GetTasksByUser(ctx,
		req.GameId, req.ActivityId, task.UserId, "", 10) // 获取用户最近创建的任务
	if nil != err {
		log.ErrorContextf(ctx, "HelpGunActTaskProc GetTasksByUser error: %v", err)
		return nil, code.ErrSystemError
	}
	if tasks == nil || len(tasks) <= 0 {
		log.ErrorContextf(ctx, "HelpGunActTaskProc GetTasksByUser error: %v", err)
		return nil, code.ErrActivityTaskFinish
	}

	// 获取最近未助力完成的任务
	task = nil
	for _, t := range tasks {
		if t.Status == df_activity.GunTaskStatusProcessing {
			task = t
			break
		}
	}
	if task == nil { // 用户没有其他需要助力的任务， 则返回任务以及完成
		log.ErrorContextf(ctx, "HelpGunActTaskProc task finish, no more task, taskId: %v", req.HelpId)
		return nil, code.ErrActivityTaskFinish
	}
	// 判断最新的任务是否到期，到期则不处理
	if !p.checkTaskTime(ctx, task) {
		log.ErrorContextf(ctx, "HelpGunActTaskProc new task expire, taskId: %v", task.TaskId)
		return nil, code.ErrActivityTaskFinish
	}
	return task, nil
}
