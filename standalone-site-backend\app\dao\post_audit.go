package dao

import (
	"errors"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/model"
)

func DeletePostAuditByPostId(postId int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Where("post_uuid = ?", postId).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func GetPostAudit(id int64, postUuid, intlOpenid string) (*model.PostAudit, error) {
	var postAudit model.PostAudit

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if postUuid != "" {
		db = db.Where("post_uuid = ?", postUuid)
	}
	if intlOpenid != "" {
		db = db.Where("intl_openid = ?", intlOpenid)
	}
	err := db.First(&postAudit).Error
	if err != nil {
		return nil, err
	}
	// post, err := GetPost(postAudit.PostUUID)
	// if err != nil {
	// 	if errors.Is(err, gorm.ErrRecordNotFound) {
	// 		return &postAudit, nil
	// 	}
	// 	return nil, err
	// }
	// postAudit.Post = post
	return &postAudit, nil
}

func GetPostAuditV2(id int64, postUuid, intlOpenid string) (*model.PostAudit, error) {
	var postAudit model.PostAudit

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if postUuid != "" {
		db = db.Where("post_uuid = ?", postUuid)
	}
	if intlOpenid != "" {
		db = db.Where("intl_openid = ?", intlOpenid)
	}
	err := db.Unscoped().First(&postAudit).Error
	if err != nil {
		return nil, err
	}
	// post, err := GetPost(postAudit.PostUUID)
	// if err != nil {
	// 	if errors.Is(err, gorm.ErrRecordNotFound) {
	// 		return &postAudit, nil
	// 	}
	// 	return nil, err
	// }
	// postAudit.Post = post
	return &postAudit, nil
}

func GetLatestPostAudit(postUuid string) (*model.PostAudit, error) {
	if postUuid == "" {
		return nil, nil
	}
	var postAudit model.PostAudit
	db := DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName())
	err := db.Where("post_uuid = ?", postUuid).Order("id DESC").First(&postAudit).Error
	if err != nil {
		return nil, err
	}
	return &postAudit, nil
}

// 获取还未送审的数据
func GetPostNotAuditList(gameId, areaId string, limit int) ([]*model.PostAudit, error) {
	var postAudit []*model.PostAudit

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName())
	if gameId != "" && areaId != "" {
		db.Where("game_id = ? and area_id = ?", gameId, areaId)
	}
	if limit > 0 {
		db = db.Limit(limit)
	}
	err := db.Where("status = ?", constants.PostAuditUnHandler).Order("id DESC").Find(&postAudit).Error
	if err != nil {
		return nil, err
	}
	return postAudit, nil
}

func GetPostAuditListByIDs(idList []int64) ([]*model.PostAudit, error) {
	var postAudits []*model.PostAudit
	if len(idList) == 0 {
		return postAudits, nil
	}

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName())
	err := db.Where("id in ?", idList).Find(&postAudits).Error
	if err != nil {
		return nil, err
	}
	return postAudits, nil
}

func GetPostEditNotAuditListByPostUUIDs(postUUIDs []string) ([]*model.PostAudit, error) {
	var postAudits []*model.PostAudit
	if len(postUUIDs) == 0 {
		return postAudits, nil
	}

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName())
	err := db.Where("post_uuid in ?", postUUIDs).Where("post_action_type = ?", constants.PostAuditActionEdit).Where("status = ?", constants.PostAuditUnHandler).Find(&postAudits).Error
	if err != nil {
		return nil, err
	}
	return postAudits, nil
}

func GetPostEditNotAuditListByPostUUID(postUUID string) (*model.PostAudit, error) {
	postAudit := &model.PostAudit{}
	if postUUID == "" {
		return postAudit, nil
	}

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName())
	err := db.Where("post_uuid = ?", postUUID).Where("post_action_type = ?", constants.PostAuditActionEdit).Where("status = ?", constants.PostAuditUnHandler).First(&postAudit).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return postAudit, nil
	} else {
		return postAudit, err
	}
}

// 获取全量审核的数据
func GetAllPostAuditList(limit int) ([]*model.PostAudit, error) {
	var postAudit []*model.PostAudit

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName())

	if limit > 0 {
		db = db.Limit(limit)
	}
	err := db.Unscoped().Order("id DESC").Find(&postAudit).Error
	if err != nil {
		return nil, err
	}
	return postAudit, nil
}

// 获取全量审核的数据
func GetAllPostAuditListV2(id, limit int) ([]*model.PostAudit, error) {
	var postAudit []*model.PostAudit

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName())

	if id > 0 {
		db = db.Where("id > ?", id)
	}
	if limit > 0 {
		db = db.Limit(limit)
	}
	err := db.Unscoped().Order("id ASC").Find(&postAudit).Error
	if err != nil {
		return nil, err
	}
	return postAudit, nil
}

func CreatePostAudit(audit *model.PostAudit) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Omit("Post").Create(&audit).Error
}

func DeletePostAudit(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func UpdatePostAudit(audit *model.PostAudit) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Where("post_uuid = ? AND is_del = ?", audit.PostUUID, 0).Updates(audit).Error
}

func UpdatePostAuditByID(audit *model.PostAudit) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Where("id = ?", audit.ID).Updates(audit).Error
}

func UpdatePostAuditInfo(postUUID string, updateData map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Where("post_uuid = ?", postUUID).Updates(updateData).Error
}

func UpdatePostAuditInfoByID(id int64, updateData map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Where("id = ?", id).Updates(updateData).Error
}

func UpdatePostAuditInfoByBatchPostUuid(postUUID []string, updateData map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Where("post_uuid in ?", postUUID).Unscoped().Updates(updateData).Error
}

func GetPostsNotAuditList(limit int) ([]*model.PostAudit, error) {
	var notAuditList []*model.PostAudit

	err := DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Where("status = 1 and (machine_status = 1 and artificial_status = 0) and is_del = 0").Limit(limit).Find(&notAuditList).Error
	if err != nil {
		return nil, err
	}
	return notAuditList, nil
}

func UpdatePostsAuditV2(postUUids []string, updateData map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Where("post_uuid in ?", postUUids).Updates(updateData).Error
}

// 将待审核的记录设置为已失效
func ChangePostNotAuditIntoInvalid(postUUID string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostAudit{}).TableName()).Where("post_uuid = ?", postUUID).Where("status = ?", constants.PostAuditUnHandler).Updates(map[string]interface{}{
		"status":      constants.PostAuditChangeToInvalid,
		"modified_on": time.Now().Unix(),
	}).Error
}
