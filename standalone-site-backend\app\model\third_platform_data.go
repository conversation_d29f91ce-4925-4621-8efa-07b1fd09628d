package model

import "trpc.publishing_application.standalonesite/app/constants"

type MediaInfo struct {
	MediaType       string `json:"media_type"`        // 资源类型，photo video
	OriginalUrl     string `json:"original_url"`      // 资源原始url
	PreviewImageUrl string `json:"preview_image_url"` // 预览图url
	MediaID         string `json:"media_id"`          // 从作品url中提取的资源id
	ChannelType     int32  `json:"channel_type"`
}

type AuthorWorksInfo struct {
	Uid             string      `json:"uid"`
	AuthorOpenid    string      `json:"open_id,omitempty"`
	AuthorName      string      `json:"author_name,omitempty"`
	AuthorImage     string      `json:"author_image,omitempty"`
	AuthorLink      string      `json:"author_link,omitempty"`
	WorkId          int64       `json:"work_id,omitempty"`
	WorkChannel     int64       `json:"work_channel,omitempty"`      // 0:unknown; 1:youtube; 2:facebook; 3:twitter; 4:twitch; 5:youtubeshort; 6:tiktok; 7:facebooklive
	WorkPublishTime int64       `json:"work_publish_time,omitempty"` // 时间戳
	WorkUrlLink     string      `json:"work_original_url,omitempty"` // 原地址链接
	WorkTitle       string      `json:"work_title,omitempty"`
	WorkMediaInfo   []MediaInfo `json:"work_media_infos,omitempty"` // 预览的信息，包含图片、视频等
	WorkDesc        string      `json:"work_desc,omitempty"`
	// WorkOriginalUrl string      `json:"work_original_url,omitempty"`
}

type CreatorHubPostData struct {
	WorkInfos []AuthorWorksInfo `json:"work_infos,omitempty"`
}

type CreatorHubWorksListData struct {
	WorkInfos []*AuthorWorksInfo `json:"work_infos,omitempty"`
	NextIdx   int32              `json:"next_idx,omitempty"`
}
type CreatorHubPostResp struct {
	Ret  int                `json:"ret"`
	Msg  string             `json:"msg"`
	Data CreatorHubPostData `json:"data"`
}

type CommunityUserInfo struct {
	Uid      string `json:"uid"`    // 用户id
	NickName string `json:"nick"`   // 用户昵称
	Gender   int    `json:"gender"` // 性别, 0男, 1女, 其他未知
	PicUrl   string `json:"picurl"` // 头像地址
}

type LikeInfo struct {
	LikeNum    int `json:"like_num"`    // 点赞数
	LikeStatus int `json:"like_status"` // 点赞状态，0未点赞, 1已点赞
}

type CommentItem struct {
	BizID          constants.PostCommentT `json:"biz_id"`           // 业务id 3=LIP资讯评论；4=LIP动态评论
	CommentID      string                 `json:"comment_id"`       // 评论id
	ParentID       string                 `json:"parent_id"`        // 评论父内容id
	Content        string                 `json:"content"`          // 评论内容
	UserInfo       CommunityUserInfo      `json:"user_info"`        // 用户信息
	Timestamp      int                    `json:"timestamp"`        // 评论时间戳
	LikeInfo       LikeInfo               `json:"like_info"`        // 点赞信息
	IsReviewed     int                    `json:"is_reviewed"`      // 审核状态，0待审核，1已审核通过
	ContentExtInfo string                 `json:"content_ext_info"` // 评论扩展信息
}

type CommunityCommentListResp struct {
	Code      int                      `json:"code"`
	Msg       string                   `json:"msg"`
	RequestID string                   `json:"request_id"`
	Data      CommunityCommentListData `json:"data"`
}

type CommunityCommentListData struct {
	Result   int           `json:"result"`
	ErrMsg   string        `json:"errmsg"`
	IsFinish int           `json:"is_finish"`    // 结束拉取标志
	NextSeq  string        `json:"next_seq"`     // 下次拉取地址, 透传到 start_seq
	Comments []CommentItem `json:"comment_list"` // 评论列表
	TotalNum int           `json:"total_num"`    // 评论总数
}

type CommunityCommentAddResp struct {
	Code      int                     `json:"code"`
	Msg       string                  `json:"msg"`
	RequestID string                  `json:"request_id"`
	Data      CommunityCommentAddData `json:"data"`
}

type CommunityCommentAddData struct {
	Result  int         `json:"result"`
	ErrMsg  string      `json:"errmsg"`
	Comment CommentItem `json:"comment_item"` // 评论
}

type CommunityCommentUpdateResp struct {
	Code      int                        `json:"code"`
	Msg       string                     `json:"msg"`
	RequestID string                     `json:"request_id"`
	Data      CommunityCommentUpdateData `json:"data"`
}

type CommunityCommentUpdateData struct {
	Result int    `json:"result"`
	ErrMsg string `json:"errmsg"`
}

type CommunityCommentDelResp struct {
	Code      int                     `json:"code"`
	Msg       string                  `json:"msg"`
	RequestID string                  `json:"request_id"`
	Data      CommunityCommentDelData `json:"data"`
}

type CommunityCommentDelData struct {
	Result int    `json:"result"`
	ErrMsg string `json:"errmsg"`
}

type CommunityCommentReviewResp struct {
	Code      int                        `json:"code"`
	Msg       string                     `json:"msg"`
	RequestID string                     `json:"request_id"`
	Data      CommunityCommentReviewData `json:"data"`
}

type CommunityCommentReviewData struct {
	Result int    `json:"result"`
	ErrMsg string `json:"errmsg"`
}

type CommunityCommentPunishResp struct {
	Code      int                        `json:"code"`
	Msg       string                     `json:"msg"`
	RequestID string                     `json:"request_id"`
	Data      CommunityCommentPunishData `json:"data"`
}

type CommunityCommentPunishData struct {
	Result int    `json:"result"`
	ErrMsg string `json:"errmsg"`
}

type CommunityCommentPunishByUidResp struct {
	Code      int                             `json:"code"`
	Msg       string                          `json:"msg"`
	RequestID string                          `json:"request_id"`
	Data      CommunityCommentPunishByUidData `json:"data"`
}

type CommunityCommentPunishByUidData struct {
	Result int    `json:"result"`
	ErrMsg string `json:"errmsg"`
}

type GetUserTotalPointsResp struct {
	Code     int64               `json:"code"`
	CodeType int64               `json:"code_type"`
	Msg      string              `json:"msg"`
	Data     UserTotalPointsData `json:"data"`
}

type UserTotalPointsData struct {
	TotalPoints         int64 `json:"total_points"`
	TotalPointsEarned   int64 `json:"total_points_earned"`
	TotalPointsConsumed int64 `json:"total_points_consumed"`
}

// CMS新闻资讯
type CMSNew struct {
	ContentDesc string `json:"content_desc"`
	ContentId   string `json:"content_id"`
	ExtInfo     string `json:"ext_info"`
}

type CMSNewsRsp struct {
	Code int32 `json:"code"`
	Data struct {
		Errmsg      string   `json:"errmsg"`
		InfoContent []CMSNew `json:"info_content"`
		NextOffset  int32    `json:"next_offset"`
		Result      int32    `json:"result"`
		IsFinish    int32    `json:"is_finish"`
		TotalNum    int32    `json:"total_num"`
	} `json:"data"`
	Msg       string `json:"msg"`
	RequestId string `json:"request_id"`
}
