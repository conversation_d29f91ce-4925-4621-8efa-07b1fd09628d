package handler

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/lipass_points"
	"github.com/Shopify/sarama"
	"trpc.act.logicial/app/common"
	"trpc.act.logicial/app/logic/nikketmp"
)

// UserMidasPayMsgHandler Kafka消息队列消费者，用于处理消息队列中的midas支付流水数据
func UserMidasPayMsgHandler(ctx context.Context, msgArray []*sarama.ConsumerMessage) (err error) {
	var handlers []func() error
	log.WithFieldsContext(ctx, "log_type", "UserMidasPayMsgHandler").Infof("UserMidasPayMsgHandlerdata: %+v",
		msgArray)
	// msgArray.Timestamp
	for _, v := range msgArray {
		value := string(v.Value)
		handlers = append(handlers, func() error {
			err := Nikke25thUserMidasPayMsg(common.GetRollBackCtx(), value)
			return err
		})
	}
	err = trpc.GoAndWait(handlers...)
	return
}

// Nikke25thUserMidasPayMsg nikke2.5周年累计充值
func Nikke25thUserMidasPayMsg(ctx context.Context, sendStr string) (err error) {
	userMidasPayReq := &pb.UserMidasPaySyncReq{}
	if err = json.Unmarshal([]byte(sendStr), &userMidasPayReq); err != nil {
		log.WithFieldsContext(ctx, "log_type", "Nikke25thUserMidasPayMsgError").Errorf(
			"failed to get kafka user midas pay req,err:%v\n", err)
		// err = errs.NewCustomError(ctx, code.GetKafkaJsonErr, "json unmarshal error, error = %v", err.Error())
		// return err
		// 消费kafka数据异常失败的，则直接告警，错误数据记录db，不返回错误，让kafka消息队列继续往下消费
		return nil
	}
	log.WithFieldsContext(ctx, "log_type", "Nikke25thUserMidasPayMsg").Infof("ConsumeUserMidasPayMsg data: %+v",
		userMidasPayReq)
	edgeStart := time.Date(2025, 4, 24, 10, 0, 0, 0, time.FixedZone("UTC+8", 8*60*60))
	edgeEnd := time.Date(2025, 5, 21, 23, 59, 59, 0, time.FixedZone("UTC+9", 9*60*60))
	timestamp, err := strconv.ParseInt(userMidasPayReq.EventTime, 10, 64)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "Nikke25thUserMidasPayMsgError_ParseInt").
			Errorf("Nikke25thUserMidasPayMsg,err:%v\n",
				err)
		return
	}
	if timestamp >= (edgeStart.UnixNano()/1e6) && timestamp <= (edgeEnd.UnixNano()/1e6) {
		if userMidasPayReq.IntlGameId == 29157 || userMidasPayReq.IntlGameId == 29080 {

			err = nikketmp.LipPoint25thKafka(ctx, userMidasPayReq)
			if err != nil {
				log.WithFieldsContext(ctx, "log_type", "Nikke25thUserMidasPayMsgError").Errorf("Nikke25thUserMidasPayMsg,err:%v\n",
					err)
				return
			}
		}
	}

	return nil
}
