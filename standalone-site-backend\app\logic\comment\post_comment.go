package comment

import (
	"context"
	"errors"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/logic/tweet"
)

// SetCommentTopOrBottom 设置置顶置底操作, actionType 1-置顶；2-置底；3-取消置顶置底
func SetCommentTopOrBottom(c context.Context, commentUuid string, actionType int32, intlOpenid string) error {
	authType := formatted.GetUserAuth(intlOpenid)
	if authType != constants.USER_AUTH_TYPE_OFFICIAL {
		return errs.NewCustomError(c, code.SetCommentPositionInfoNotPermission, "Not permission!")
	}
	// 设置一个分布锁
	key := cache.GetCommentSetTopOrBottomCacheKey(commentUuid)
	lock, err := cache.AcquireLock(context.Background(), key, time.Duration(1)*time.Hour)
	if err != nil {
		// 记录错误
		return err
	}
	if !lock {
		// 已经被锁了,提示操作频繁
		return errs.NewCustomError(context.Background(), code.RedisUnlockErr, "Frequent operation! Please try again later")
	}
	_, err = tweet.CMSSetCommentPositionInfo(c, &pb.CMSSetCommentPositionInfoReq{
		CommentUuid: commentUuid,
		PosStatus:   actionType,
	})
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).
			Errorf("SetCommentTopOrBottom err: %v", err)
		return err
	}
	// 删除缓存
	go func(commentUuid string, intlOpenid string) {
		defer func() {
			cache.ReleaseLock(context.Background(), key)
			recovery.CatchGoroutinePanic(context.Background())
		}()
		commentInfo, err := dao.GetCommentByUUID(commentUuid)
		if err != nil {
			log.WithFieldsContext(context.Background(), "logs_types", constants.LogType_Standalonesite).Errorf("SetCommentTopOrBottom | delete cache failed. get comment failed; comment_uuid:%s, err: %v", commentUuid, err)
			return
		}
		// 先获取缓存数据
		commentsHotBaseRedisKey := cache.GetCommentBaseListGuestKey(commentInfo.PostUUID, "", 10, constants.OrderByTypeHot)
		commentsDefaultBaseRedisKey := cache.GetCommentBaseListGuestKey(commentInfo.PostUUID, "", 10, constants.OrderByTypeDefault)

		commentsUserHotBaseRedisKey := cache.GetCommentBaseListHostKey(commentInfo.PostUUID, intlOpenid, "", 10, constants.OrderByTypeHot)
		commentsUserDefaultBaseRedisKey := cache.GetCommentBaseListHostKey(commentInfo.PostUUID, intlOpenid, "", 10, constants.OrderByTypeDefault)

		redis.GetClient().Del(context.Background(), commentsHotBaseRedisKey, commentsDefaultBaseRedisKey, commentsUserHotBaseRedisKey, commentsUserDefaultBaseRedisKey)

	}(commentUuid, intlOpenid)
	return nil
}

/**
设置置顶置底规则：
1. CMS 设置置顶置底大于其他任何权限
2. 非CMS操作时，只有版主和贴主可以操作
3. 非CMS操作时，若已经置顶/置底，无法再次置顶、置底
*/
// actionType： 1:置顶 2:置底 0： 取消置顶置底
func SetCommentTopOrBottomV2(c context.Context, commentUuid string, top_bottom_status constants.ECommentPosStatusString, intlOpenid string, isCMSOperation bool) error {
	// 前置校验
	if commentUuid == "" {
		return errs.NewCustomError(c, code.ErrSetCommentTopButtom, "commentUid is empty")
	}
	if !isCMSOperation && intlOpenid == "" {
		return errs.NewCustomError(c, code.ErrSetCommentTopButtom, "intlOpenid is empty")
	}
	var actionType int32
	if top_bottom_status == constants.ECommentPosStatusString_Top {
		actionType = 1
	} else if top_bottom_status == constants.ECommentPosStatusString_Bottom {
		actionType = 2
	}
	// 获取当前评论的置顶置底信息
	comment, err := dao.GetCommentByUUID(commentUuid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetCommentTopOrBottomV2 GetCommentByUUID err: %v\n", err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errs.NewCustomError(c, code.ErrSetCommentTopButtomNoComment, "comment not found")
		} else {
			return errs.NewCustomError(c, code.ErrSetCommentTopButtom, "Failed to get comment")
		}
	}
	if comment == nil || comment.CommentUUID == "" {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetCommentTopOrBottomV2 GetCommentByUUID empty: %s", commentUuid)
		return errs.NewCustomError(c, code.ErrSetCommentTopButtomNoComment, "comment not empty")
	}
	// 获取post信息
	post, err := dao.GetPost(comment.PostUUID)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetCommentTopOrBottomV2 GetPost err: %v\n", err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errs.NewCustomError(c, code.ErrSetCommentTopButtomNoPost, "post not found")
		} else {
			return errs.NewCustomError(c, code.ErrSetCommentTopButtom, "get post failed")
		}
	}
	if post == nil || post.PostUUID == "" {
		return errs.NewCustomError(c, code.ErrSetCommentTopButtomNoPost, "post not found")
	} //
	// 已置顶/置底无法再次置顶/置底
	if actionType == 1 &&
		(comment.PosStatus == int(constants.ETopBottomAction_CMSSetTop) ||
			comment.PosStatus == int(constants.ETopBottomAction_ModeratorSetTop) ||
			comment.PosStatus == int(constants.ETopBottomAction_PosterSetTop)) {
		return errs.NewCustomError(c, code.StatusIsChanged, "comment has been set top")
	}
	if actionType == 2 &&
		(comment.PosStatus == int(constants.ETopBottomAction_CMSSetBottom) ||
			comment.PosStatus == int(constants.ETopBottomAction_ModeratorSetBottom) ||
			comment.PosStatus == int(constants.ETopBottomAction_PosterSetBottom)) {
		return errs.NewCustomError(c, code.StatusIsChanged, "comment has been set bottom")
	}
	if actionType == 0 &&
		(comment.PosStatus == int(constants.ETopBottomAction_CancelTopButtom_Alias) ||
			comment.PosStatus == int(constants.ETopBottomAction_CancelTopButtom)) {
		return errs.NewCustomError(c, code.StatusIsChanged, "comment has been unset")
	}
	// 判断当前用户的身份：cms 设置、版主设置、贴主设置
	// 判断权限
	// 1. 若当前是CMS操作,一定具备权限
	// CMS 设置
	if isCMSOperation {
		if actionType == 0 {
			actionType = 3
		}
		_, err := tweet.CMSSetCommentPositionInfo(c, &pb.CMSSetCommentPositionInfoReq{
			CommentUuid: commentUuid,
			PosStatus:   actionType,
		})
		return err
	} else {
		isAdmin := formatted.GetUserAdmin(intlOpenid)
		postUserOpenid := post.IntlOpenid
		posStatus := constants.ETopBottomAction_CancelTopButtom
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("service.SetCommentTopOrBottomV2 isAdmin:%v, intlOpenid: %s, postUserOpenid:%s, comment.PosStatus:%d\n",
			isAdmin, intlOpenid, postUserOpenid, comment.PosStatus)
		if isAdmin &&
			(comment.PosStatus == int(constants.ETopBottomAction_CMSSetBottom) ||
				comment.PosStatus == int(constants.ETopBottomAction_CMSSetTop)) {
			// 2. 若当前是版主操作，若当前置顶置底已经是CMS设置状态，报错误码，否则继续
			return errs.NewCustomError(c, code.ErrSetCommentTopButtomNoPermissoin, "no permission")
		} else if postUserOpenid == intlOpenid &&
			(comment.PosStatus == int(constants.ETopBottomAction_CMSSetBottom) ||
				comment.PosStatus == int(constants.ETopBottomAction_CMSSetTop)) {
			// 3. 若当前是贴主操作，若当前置顶置底已经是CMS或者版主设置，报错误码，否则继续
			return errs.NewCustomError(c, code.ErrSetCommentTopButtomNoPermissoin, "no permission")
		} else if postUserOpenid != intlOpenid && !isAdmin {
			return errs.NewCustomError(c, code.ErrSetCommentTopButtomNoPermissoin, "no permission | no admin or poster")
		}
		// 4. 执行操作
		if actionType == 0 {
			posStatus = constants.ETopBottomAction_CancelTopButtom
		} else if actionType == 1 {
			if isAdmin {
				posStatus = constants.ETopBottomAction_ModeratorSetTop
			} else {
				posStatus = constants.ETopBottomAction_PosterSetTop
			}

		} else if actionType == 2 {
			if isAdmin {
				posStatus = constants.ETopBottomAction_ModeratorSetBottom
			} else {
				posStatus = constants.ETopBottomAction_PosterSetBottom
			}
		}
		err := tweet.HandleSetCommentTopOrButtomData(c, commentUuid, posStatus)
		cache.RemoveUserCommentCacheKeys(intlOpenid)
		return err
	}
}
