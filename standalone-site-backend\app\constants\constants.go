// Package constants 常量
package constants

import "fmt"

const (
	// CommunityPrefix source前缀
	CommunityPrefix = "community-game"
	// RecordType_Bind 记录类型-绑定
	RecordType_Bind = 1
	// RecordType_SignIn 记录类型-签到
	RecordType_SignIn = 2
	// GameIDTOF tof intl gameid
	GameIDTOF = "29093"
	// ExpiredCoins 积分过期
	ExpiredCoins = "Expired Coins"
	// LogType_Standalonesite 打印日志用的
	LogType_Standalonesite = "standalonesite_ugc"
	// LogType_Report 打印日志用的
	LogType_Report = "lip_information_tweet"

	// x-common-params 的key值
	GameId     = "game_id"
	AreaID     = "area_id"
	Source     = "source"
	IntlGameId = "intl_game_id"
	LipRegion  = "lip_region"
	Scene      = "data_statistics_scene"
	PageID     = "data_statistics_page_id"
	ClientType = "data_statistics_client_type"
	Lang       = "data_statistics_lang"
)

// GetCommunitySource Id 获取社区sourceid
func GetCommunitySource(gameId string) (str string) {
	str = fmt.Sprintf("%s-%s", CommunityPrefix, gameId)
	return
}
