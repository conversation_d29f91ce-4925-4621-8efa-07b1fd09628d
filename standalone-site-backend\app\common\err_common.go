package common

import (
	"errors"
	"git.code.oa.com/trpc-go/trpc-go/errs"
)

// DecCode 解构error错误码中的数字
func DecCode(err error) (int, error) {
	code, _, e := Dec(err)
	if e != nil {
		return 0, e
	}
	return code, nil
}

// DecMsg 解构error错误码中的信息
func DecMsg(err error) (string, error) {
	_, msg, e := Dec(err)
	if e != nil {
		return "", e
	}
	return msg, nil
}

// Dec 解构error错误码中的数字和错误信息
func Dec(err error) (int, string, error) {
	if err == nil {
		return 0, "", nil
	}
	var errs *errs.Error
	if errors.As(err, &errs) {
		return int(errs.Code), errs.Msg, nil
	}
	// 兼容非trpc错误类型
	return 0, "", err
}
