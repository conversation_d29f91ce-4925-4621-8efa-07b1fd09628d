package dftransfer

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"sort"
	"strings"
	"time"

	mail "git.woa.com/trpcprotocol/publishing_marketing/aigc_mail"
	gameDfPb "git.woa.com/trpcprotocol/publishing_marketing/game_df"
	redisOrigin "github.com/go-redis/redis/v8"
	"github.com/google/uuid"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/ams"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/datadump"
	deltaversePb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/deltaverse"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/object"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	amsPresentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present_ams"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_account_tranfer"
	redisOrgin "github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/global"
	model "trpc.act.logicial/app/model/df_transfer"
)

const (
	GaGameIDStr                                           = "30150"
	DBPageSize                                            = 50
	EmailPageSize                                         = 100
	SendStatus_NotBegin                                   = 0
	SendStatus_Success                                    = 1
	SendStatus_Fail                                       = 2
	SendStatus_Email_Max                                  = 3
	SendPresentType_AMS                                   = "AMS"
	SendPresentType_UpdateSOLLevel                        = "UpdateSOLLevel"                        // 更新所有OutSOLLevel
	SendPresentType_UpdateTDMLevel                        = "UpdateTDMLevel"                        // 更新所有OutTDMLevel
	SendPresentType_UpdateSOLRankScore                    = "UpdateSOLRankScore"                    // 更新所有OutBattleRankScore
	SendPresentType_UpdateAllOutBattleRankScore           = "UpdateAllOutBattleRankScore"           // 更新所有对外BattleRankScore
	SendPresentType_SubscribeOrUpgradeGarenaBattlePass    = "SubscribeOrUpgradeGarenaBattlePass"    // 开通或升级BP付费版
	SendPresentType_CalculateBattlePassExperienceProgress = "CalculateBattlePassExperienceProgress" // BP购买等级经验值
	AccountBannedExcludingMigrationCode                   = 611015
	EmailHasExcelMaxNumNum                                = 180 // 邮件最大数量
	EmailHasExcelMaxNum                                   = 10  // 邮件包含最大Excel数量
)

type ExcelData struct {
	Key      string
	FileName string
	Title    [][]string
	Data     [][]string
}
type SendEmailInfo struct {
	FieldName  string
	EmailTitle string
	ExcelTitle []*gameDfPb.IDIPReturnValueDetails
}

var (
	gameProxy       = gamePb.NewGameClientProxy()
	redisKey        = fmt.Sprintf("%s_df_transfer_num", global.GetPrefix())
	redisBanPrefix  = "df_transfer_ban"
	gameDFProxy     = gameDfPb.NewDfClientProxy()
	mailProxy       = mail.NewMailClientProxy()
	amsPresentProxy = amsPresentPb.NewAmsClientProxy()
	GaServiceType   = "dfgarena"
	GaAppid         = ""
	EmailList       = []*SendEmailInfo{
		&SendEmailInfo{
			FieldName:  "email_operator_data",
			EmailTitle: "干员系统道具查询(********)",
		},
	}
	EmailList1 = []*SendEmailInfo{
		&SendEmailInfo{
			FieldName:  "email_basic_data",
			EmailTitle: "查询openid基本信息(********)",
		},
		&SendEmailInfo{
			FieldName:  "email_mp_data",
			EmailTitle: "玩家MP详细信息查询(29236054)",
		},
		&SendEmailInfo{
			FieldName:  "email_sol_data",
			EmailTitle: "查询玩家SOL详细信息(29236053)",
		},
		&SendEmailInfo{
			FieldName:  "email_trianglecoin_data",
			EmailTitle: "三角币和充值信息查询(29236048)",
		},
		&SendEmailInfo{
			FieldName:  "email_mandel_data",
			EmailTitle: "曼德尔币存量(datamore)",
		},
		&SendEmailInfo{
			FieldName:  "email_collection_house_data",
			EmailTitle: "商品仓库查询(29236040)",
			ExcelTitle: []*gameDfPb.IDIPReturnValueDetails{
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ItemId",
					Type:        "uint64",
					Description: "物品ID",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ItemName",
					Type:        "string",
					Description: "名称",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ItemNum",
					Type:        "int32",
					Description: "数量",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ItemGid",
					Type:        "uint64",
					Description: "物品GID",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ItemQuality",
					Type:        "uint64",
					Description: "物品品质",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "IsCollectibles",
					Type:        "int64",
					Description: "是否为典藏：0 不是， 1 是",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "GainedTime",
					Type:        "int64",
					Description: "获得时间",
				},
			},
		},
		&SendEmailInfo{
			FieldName:  "email_commercial_skin_data",
			EmailTitle: "典藏皮肤信息查询(29236042)",
			ExcelTitle: []*gameDfPb.IDIPReturnValueDetails{
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "PropID",
					Type:        "uint64",
					Description: "道具ID",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Rarity",
					Type:        "uint64",
					Description: "稀有度：优品、极品",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Wear",
					Type:        "int64",
					Description: "磨损度",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "UniqueNo",
					Type:        "uint64",
					Description: "专属编号",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "KillCnter",
					Type:        "uint64",
					Description: "击杀计数器",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "CustomName",
					Type:        "string",
					Description: "自定义名字",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceId",
					Type:        "uint64",
					Description: "外观方案id",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceSeed",
					Type:        "uint64",
					Description: "外观随机种子",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Name",
					Type:        "string",
					Description: "皮肤名",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "GID",
					Type:        "uint64",
					Description: "GID 新增字段",
				},
			},
		},
		&SendEmailInfo{
			FieldName:  "email_security_box_data",
			EmailTitle: "顶级安全箱赛季权限卡(datamore)",
		},
		&SendEmailInfo{
			FieldName:  "email_operator_data",
			EmailTitle: "干员系统道具查询(********)",
		},
		&SendEmailInfo{
			FieldName:  "email_operator_collectibles_data",
			EmailTitle: "典藏挂饰信息查询(********)",
			ExcelTitle: []*gameDfPb.IDIPReturnValueDetails{
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "PropID",
					Type:        "uint64",
					Description: "道具ID",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Rarity",
					Type:        "uint64",
					Description: "稀有度：优品、极品",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Wear",
					Type:        "int64",
					Description: "磨损度",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "UniqueNo",
					Type:        "uint64",
					Description: "专属编号",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "CustomName",
					Type:        "string",
					Description: "自定义名字",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceId",
					Type:        "uint64",
					Description: "外观方案id",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceSeed",
					Type:        "uint64",
					Description: "外观随机种子",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "MarketBuyTime",
					Type:        "int64",
					Description: "从矩阵市场购买得到的典藏挂饰时间",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "SourceType",
					Type:        "int32",
					Description: "典藏挂饰来源类型",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "SuitActive",
					Type:        "int32",
					Description: "对应套装是否已集齐激活",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowType",
					Type:        "int32",
					Description: "展示条目类型",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowRarity",
					Type:        "int32",
					Description: "展示条目稀有度",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowAppearanceId",
					Type:        "uint64",
					Description: "展示条目外观方案id",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowAppearanceSeed",
					Type:        "uint64",
					Description: "展示条目外观随机种子",
				},
			},
		},
		&SendEmailInfo{
			FieldName:  "email_more_email_data",
			EmailTitle: fmt.Sprintf("超过%d封邮件信息", EmailHasExcelMaxNumNum),
			ExcelTitle: []*gameDfPb.IDIPReturnValueDetails{
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ItemId",
					Type:        "string",
					Description: "道具ID",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ItemName",
					Type:        "int32",
					Description: "道具名称",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Num",
					Type:        "int32",
					Description: "道具数量",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Rarity",
					Type:        "uint64",
					Description: "稀有度：优品、极品",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Wear",
					Type:        "int64",
					Description: "磨损度",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "UniqueNo",
					Type:        "uint64",
					Description: "专属编号",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "CustomName",
					Type:        "string",
					Description: "自定义名字",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceId",
					Type:        "uint64",
					Description: "外观方案id",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceSeed",
					Type:        "uint64",
					Description: "外观随机种子",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "MarketBuyTime",
					Type:        "int64",
					Description: "从矩阵市场购买得到的典藏挂饰时间",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "SourceType",
					Type:        "int32",
					Description: "典藏挂饰来源类型",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "SuitActive",
					Type:        "int32",
					Description: "对应套装是否已集齐激活",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowType",
					Type:        "int32",
					Description: "展示条目类型",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowRarity",
					Type:        "int32",
					Description: "展示条目稀有度",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowAppearanceId",
					Type:        "uint64",
					Description: "展示条目外观方案id",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowAppearanceSeed",
					Type:        "uint64",
					Description: "展示条目外观随机种子",
				},
			},
		},
		{
			FieldName:  "email_orange_gun_collectors_data",
			EmailTitle: "（橙）枪典藏数据", // 29236042
			ExcelTitle: []*gameDfPb.IDIPReturnValueDetails{
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "PropID",
					Type:        "uint64",
					Description: "道具ID",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Rarity",
					Type:        "uint64",
					Description: "稀有度：优品、极品",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Wear",
					Type:        "int64",
					Description: "磨损度",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "UniqueNo",
					Type:        "uint64",
					Description: "专属编号",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "KillCnter",
					Type:        "uint64",
					Description: "击杀计数器",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "CustomName",
					Type:        "string",
					Description: "自定义名字",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceId",
					Type:        "uint64",
					Description: "外观方案id",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceSeed",
					Type:        "uint64",
					Description: "外观随机种子",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Name",
					Type:        "string",
					Description: "皮肤名",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "GID",
					Type:        "uint64",
					Description: "GID 新增字段",
				},
			},
		},
		{
			FieldName:  "email_pendant_data",
			EmailTitle: "（橙）典藏挂饰", // (********)
			ExcelTitle: []*gameDfPb.IDIPReturnValueDetails{
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "PropID",
					Type:        "uint64",
					Description: "道具ID",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Rarity",
					Type:        "uint64",
					Description: "稀有度：优品、极品",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Wear",
					Type:        "int64",
					Description: "磨损度",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "UniqueNo",
					Type:        "uint64",
					Description: "专属编号",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "CustomName",
					Type:        "string",
					Description: "自定义名字",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceId",
					Type:        "uint64",
					Description: "外观方案id",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceSeed",
					Type:        "uint64",
					Description: "外观随机种子",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "MarketBuyTime",
					Type:        "int64",
					Description: "从矩阵市场购买得到的典藏挂饰时间",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "SourceType",
					Type:        "int32",
					Description: "典藏挂饰来源类型",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "SuitActive",
					Type:        "int32",
					Description: "对应套装是否已集齐激活",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowType",
					Type:        "int32",
					Description: "展示条目类型",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowRarity",
					Type:        "int32",
					Description: "展示条目稀有度",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowAppearanceId",
					Type:        "uint64",
					Description: "展示条目外观方案id",
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowAppearanceSeed",
					Type:        "uint64",
					Description: "展示条目外观随机种子",
				},
			},
		},
	}
)

type DeltaverseReq struct {
	Openid string `json:"openid"`
	Zoneid int64  `json:"zoneid"`
}

func GetGaAmsSerial() (serialNo string) {
	if GaAppid == "" {
		rsp, err := gameProxy.GetGameRegionConf(context.Background(), &gamePb.GetGameRegionConfReq{
			GameId: GaGameIDStr,
		})
		fmt.Println("---------------err---------------")
		fmt.Printf("%#v\n", err)
		if err != nil {
			// err = errs.NewSystemError(ctx, errs.Ga, code.AmsConfigError, "get gameid config err,err=%v", err)
			return
		}
		// GaServiceType = rsp.GetGameCode()
		GaAppid = rsp.Appid
	}

	serialNo = ams.CreateAmsSerial(GaServiceType, GaAppid)
	return
}

func GetTransferNum(ctx context.Context) (num int32, err error) {

	val, errG := redis.GetClient().Do(ctx, "Get", redisKey).Int()
	if errG != nil && errG != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errG.Error())
		return
	}
	if val != 0 {
		num = cast.ToInt32(val)
		return
	}
	tableName := (model.DfSteamGATransfer{}).TableName()
	var count int64
	errM := DB.DefaultConnect().Table(tableName).Count(&count).Error
	if errM != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", errM.Error())
		return
	}
	num = cast.ToInt32(count)
	redis.GetClient().Do(ctx, "SetNX", redisKey, num).Result()
	return
}

func GetSteamBindGAInfo(ctx context.Context, userAccount *accountPb.UserAccount) (hasBind bool, bindInfo model.DfSteamGATransfer, err error) {
	tableName := (model.DfSteamGATransfer{}).TableName()

	dbErr := DB.DefaultConnect().Debug().Table(tableName).Where("uid = ?", userAccount.Uid).Where("status > ?", 0).First(&bindInfo).Error
	if dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error1, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	hasBind = true
	return
}
func CheckSteamAccountInTransfer(ctx context.Context, userAccount *accountPb.UserAccount) (err error) {
	tableName := (model.DfSteamGATransfer{}).TableName()
	var bindInfo model.DfSteamGATransfer
	dbErr := DB.DefaultConnect().Debug().Table(tableName).Where("uid = ?", userAccount.Uid).Where("status > ?", 0).First(&bindInfo).Error
	if dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	err = errs.NewCustomError(ctx, code.SteamAccountHasTransfer, "steam account has transfer")
	return
}
func CheckGAAccountInTransfer(ctx context.Context, gaOpenid string) (err error) {
	tableName := (model.DfSteamGATransfer{}).TableName()
	var bindInfo model.DfSteamGATransfer
	dbErr := DB.DefaultConnect().Debug().Table(tableName).Where("ga_openid = ?", gaOpenid).Where("status > ?", 0).First(&bindInfo).Error
	if dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	err = errs.NewCustomError(ctx, code.GAAccountHasTransfer, "ga account has transfer")
	return
}

func DoSteamBindGA(ctx context.Context, userAccount *accountPb.UserAccount, gaOpenid string, gaEmail string) (err error) {
	tableName := (model.DfSteamGATransfer{}).TableName()
	langType := metadata.GetLangType(ctx)
	addData := model.DfSteamGATransfer{
		DfSteamGATransferModel: model.DfSteamGATransferModel{},
		Uid:                    userAccount.Uid,
		AccountType:            int32(userAccount.AccountType),
		GaOpeinid:              gaOpenid,
		Status:                 int32(pb.SteamBindGAStatus_Confirm),
		GaEmail:                gaEmail,
		CalmDate:               GetTransDate(),
		LangType:               langType,
	}
	dbErr := DB.DefaultConnect().Debug().Table(tableName).Create(&addData).Error
	if dbErr != nil {
		if !strings.HasPrefix(dbErr.Error(), "Error 1062") {
			err = errs.NewCustomError(ctx, code.DFTransferAccountHasTransfer, "has record")
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	// redis 数量+1
	redis.GetClient().Incr(ctx, redisKey)
	RemoveLogCalmBan(ctx, userAccount.Uid)
	return
}
func DoSteamUnbindGA(ctx context.Context, uid string) (err error) {
	// 查询数据，判断时间
	tableName := (model.DfSteamGATransfer{}).TableName()
	var bindInfo model.DfSteamGATransfer
	dbErr := DB.DefaultConnect().Debug().Table(tableName).Where("uid = ?", uid).Where("status > ?", 0).First(&bindInfo).Error
	if dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			err = errs.NewCustomError(ctx, code.DFTransferAccountNotTransfer, "has not record")
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	// 判断状态  如果当前状态是 confirm 则可以删除
	if bindInfo.Status != int32(pb.SteamBindGAStatus_Confirm) {
		err = errs.NewCustomError(ctx, code.DFTransferAccountNotInConfirm, "not in confirm")
		return
	}
	dbResult := DB.DefaultConnect().Debug().Table(tableName).Where("uid = ?", uid).Where("status = ?", int32(pb.SteamBindGAStatus_Confirm)).Delete(&bindInfo)
	if dbResult.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbResult.Error.Error())
		return
	}
	if dbResult.RowsAffected == 0 {
		err = errs.NewCustomError(ctx, code.DFTransferAccountNotInConfirm, "not in confirm")
	}
	return
}

func RemoveLogCalmBan(ctx context.Context, uid string) (err error) {
	redisKey := fmt.Sprintf("%s_%s:%s", global.GetPrefix(), redisBanPrefix, uid)
	fmt.Println("---------------redisKey---------------")
	fmt.Printf("%#v\n", redisKey)
	redis.GetClient().Do(ctx, "DEL", redisKey)
	return
}
func AddLogCalmBan(ctx context.Context, uid string) (err error) {
	redisKey := fmt.Sprintf("%s_%s:%s", global.GetPrefix(), redisBanPrefix, uid)
	fmt.Println("---------------redisKey---------------")
	fmt.Printf("%#v\n", redisKey)
	redis.GetClient().Do(ctx, "SET", redisKey, 1, "EX", 86400*30) // 30天
	return
}
func GetLogCalmBan(ctx context.Context, userAccount *accountPb.UserAccount, areaId int32) (hasBan bool, err error) {
	redisKey := fmt.Sprintf("%s_%s:%s", global.GetPrefix(), redisBanPrefix, userAccount.Uid)
	fmt.Println("---------------redisKey---------------")
	fmt.Printf("%#v\n", redisKey)
	result, errR := redis.GetClient().Get(ctx, redisKey).Result()
	if errR != nil && errR != redisOrigin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"redis error, \t [Error]:{%v} ", errR.Error())
		return
	}
	fmt.Println("---------------GetLogCalmBan result---------------")
	fmt.Printf("%#v\n", result)
	if result == "1" {
		// 判断是否被封禁
		_, errD := gameDFProxy.CheckSteamAccountIsInBlock(ctx, &gameDfPb.CheckSteamAccountIsInBlockReq{
			AreaId: areaId,
		})
		fmt.Printf("%#v\n", errD)
		if errD != nil {
			fmt.Println("---------------errParse.Code---------------")
			errParse := errs.ParseError(ctx, errD)
			fmt.Printf("%#v\n", errParse.Code)
			if errParse.Code == AccountBannedExcludingMigrationCode {
				hasBan = true
				return
			}
			return
		}
		RemoveLogCalmBan(ctx, userAccount.Uid)
	}

	return
}

func getdmfeatureMap(req DeltaverseReq) (paramMap map[string]interface{}) {
	paramMap = map[string]interface{}{
		"openid":     req.Openid,
		"zoneareaid": fmt.Sprintf("%d", req.Zoneid),
	}
	return
}

// GetFirstLoginTimestamp 获取第一次登录时间
func GetFirstLoginTimestamp(ctx context.Context, req DeltaverseReq) (firstLoginTimestamp int64, err error) {
	paramMap := getdmfeatureMap(req)
	response, errR := deltaversePb.SendRequest(ctx, deltaversePb.SendRequestParam{
		ServiceType:        "projectd_oversea",
		DestinationService: "dmfeature-13568",
		Path:               "/dmfeature/13568/firstLoginTime",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if errR != nil {
		err = errs.NewCustomError(ctx, code.DFDeltaverseError,
			"json error, \t [Error]:{%v} ", errR.Error())
		return
	}
	type rspStuct struct {
		Code int `json:"code"`
		Data struct {
			A1 string `json:"a1"`
		} `json:"data"`
		Msg     string `json:"msg"`
		TraceID string `json:"traceId"`
	}
	var rsp rspStuct
	errM := json.Unmarshal([]byte(response), &rsp)
	if errM != nil || rsp.Code != 0 {
		log.WithFieldsContext(ctx, "log_type", "json_error").Errorf(
			"oboRankTotal SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		err = errs.NewCustomError(ctx, code.DFDeltaverseError,
			"json error, \t [Error]:{%v} ", errM.Error())
		return
	}
	layout := "2006-01-02 15:04:05" // Go 的时间格式必须使用这个特定的布局

	// 解析日期字符串

	t, errR := time.Parse(layout, rsp.Data.A1)
	if errR != nil {
		err = errs.NewCustomError(ctx, code.DFDeltaverseError,
			"json error, \t [Error]:{%v} ", errR.Error())
		return
	}
	firstLoginTimestamp = t.Unix()
	return
}

// GetMaxLevelBoxNum 获取最高等级的宝箱数量
func GetMaxLevelBoxNum(ctx context.Context, req DeltaverseReq) (num int32, err error) {
	paramMap := getdmfeatureMap(req)
	response, errR := deltaversePb.SendRequest(ctx, deltaversePb.SendRequestParam{
		ServiceType:        "projectd_oversea",
		DestinationService: "dmfeature-13568",
		Path:               "/dmfeature/13568/securityBoxCard",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if errR != nil {
		err = errs.NewCustomError(ctx, code.DFDeltaverseError,
			"json error, \t [Error]:{%v} ", errR.Error())
		return
	}
	type rspStuct struct {
		Code int `json:"code"`
		Data struct {
			A1 string `json:"a1"`
		} `json:"data"`
		Msg     string `json:"msg"`
		TraceID string `json:"traceId"`
	}
	var rsp rspStuct
	errM := json.Unmarshal([]byte(response), &rsp)
	if errM != nil || rsp.Code != 0 {
		log.WithFieldsContext(ctx, "log_type", "json_error").Errorf(
			"oboRankTotal SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		err = errs.NewCustomError(ctx, code.DFDeltaverseError,
			"json error, \t [Error]:{%v} ", errM.Error())
		return
	}
	num = cast.ToInt32(rsp.Data.A1)
	return
}

// GetFirstLoginCountry 获取首次登录国家
func GetFirstLoginCountry(ctx context.Context, req DeltaverseReq) (countryCode string, err error) {
	paramMap := getdmfeatureMap(req)
	response, errR := deltaversePb.SendRequest(ctx, deltaversePb.SendRequestParam{
		ServiceType:        "projectd_oversea",
		DestinationService: "dmfeature-13568",
		Path:               "/dmfeature/13568/countryCode",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if errR != nil {
		err = errs.NewCustomError(ctx, code.DFDeltaverseError,
			"json error, \t [Error]:{%v} ", errR.Error())
		return
	}
	type rspStuct struct {
		Code int `json:"code"`
		Data struct {
			A1 string `json:"a1"`
		} `json:"data"`
		Msg     string `json:"msg"`
		TraceID string `json:"traceId"`
	}
	var rsp rspStuct
	errM := json.Unmarshal([]byte(response), &rsp)
	if errM != nil || rsp.Code != 0 {
		log.WithFieldsContext(ctx, "log_type", "json_error").Errorf(
			"oboRankTotal SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		err = errs.NewCustomError(ctx, code.DFDeltaverseError,
			"json error, \t [Error]:{%v} ", errM.Error())
		return
	}
	countryCode = rsp.Data.A1
	return
}

// GetMandelCoinNum 获取mandel币数量
func GetMandelCoinNum(ctx context.Context, req DeltaverseReq) (num int32, err error) {
	paramMap := getdmfeatureMap(req)
	response, errR := deltaversePb.SendRequest(ctx, deltaversePb.SendRequestParam{
		ServiceType:        "projectd_oversea",
		DestinationService: "dmfeature-13568",
		Path:               "/dmfeature/13568/mandelCoin",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if errR != nil {
		err = errs.NewCustomError(ctx, code.DFDeltaverseError,
			"json error, \t [Error]:{%v} ", errR.Error())
		return
	}
	type rspStuct struct {
		Code    int    `json:"code"`
		Data    string `json:"data"`
		Msg     string `json:"msg"`
		TraceID string `json:"traceId"`
	}
	var rsp rspStuct
	errM := json.Unmarshal([]byte(response), &rsp)
	if errM != nil || rsp.Code != 0 {
		log.WithFieldsContext(ctx, "log_type", "json_error").Errorf(
			"GetMandelCoinNum SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		err = errs.NewCustomError(ctx, code.DFDeltaverseError,
			"json error, \t [Error]:{%v} ", errM.Error())
		return
	}
	num = cast.ToInt32(rsp.Data)
	return
}

// ScheduleConfirmAccount 定时确认账户
func ScheduleConfirmAccount(ctx context.Context, calmDate string, steamAreaId int32, GAAreaId int32) (err error) {
	// 添加锁
	lockKey := global.GetPrefix() + "_df_confirm_lock_"
	isLocking, errR := redis.GetClient().SetNX(ctx, lockKey, 1, 1*time.Hour).Result()
	fmt.Println("-----------------isLocking-------------")
	fmt.Printf("%#v\n", isLocking)
	fmt.Printf("%#v\n", errR)
	defer redis.GetClient().Del(ctx, lockKey)
	if errR != nil && errR != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errR.Error())
		return
	}
	if !isLocking {
		err = errs.NewCustomError(ctx, code.DFTransferLockErr,
			"confirm isLocking, \t [Error]:{%v} ", errR.Error())
		return
	}
	// 取数据
	tableName := (model.DfSteamGATransfer{}).TableName()
	var bindList []model.DfSteamGATransfer
	// 分页查询
	fmt.Println("---------------1---------------")
	var totalRecords int64
	dbErr := DB.DefaultConnect().Table(tableName).WithContext(ctx).Debug().Where("calm_date = ?", calmDate).
		Where("status = ?", pb.SteamBindGAStatus_Confirm).Where("deleted_at = ?", 0).Count(&totalRecords).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error 1, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	if totalRecords == 0 {
		// return
	}
	totalPages := int(math.Ceil(float64(totalRecords) / float64(DBPageSize)))
	for i := 0; i < int(totalRecords); i += DBPageSize {
		// 分页查询
		for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
			offset := (pageNumber - 1) * DBPageSize
			dbErr := DB.DefaultConnect().Table(tableName).
				Where("calm_date = ?", calmDate).
				Where("status = ?", pb.SteamBindGAStatus_Confirm).
				Where("deleted_at = ?", 0).Find(&bindList).
				Offset(offset).Limit(DBPageSize).Error
			if dbErr != nil {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error 2, \t [Error]:{%v} ", dbErr.Error())
				return
			}

			// 设置状态
			// Ban账号
			for _, item := range bindList {
				uidList := strings.Split(item.Uid, "-")
				if len(uidList) != 2 {
					continue
				}
				openid := uidList[1]
				// 查询状态
				_, errB := gameDFProxy.IsAccountBannedExcludingMigration(ctx, &gameDfPb.IsAccountBannedExcludingMigrationReq{
					Openid: openid,
					AreaId: steamAreaId,
				})
				if errB != nil {
					errParse := errs.ParseError(ctx, errB)
					fmt.Println("---------------errParse.Code---------------")
					fmt.Printf("%#v\n", errParse.Code)
					if errParse.Code == AccountBannedExcludingMigrationCode {
						// 解绑
						errM := DoSteamUnbindGA(ctx, item.Uid)
						if errM != nil {
							continue
						}
						fmt.Println("---------------AddLogCalmBan---------------")
						fmt.Printf("%#v\n", item.Uid)
						// 设置 提示封禁状态
						AddLogCalmBan(ctx, item.Uid)
					}
					continue
				}

				_, err = gameDFProxy.BanDFSteamGameAccount(ctx, &gameDfPb.BanDFSteamGameAccountReq{
					Openid: openid,
					AreaId: steamAreaId,
				})
				if err != nil {
					fmt.Println("--------------BanDFSteamGameAccount----------------")
					fmt.Printf("%#v\n", err)
					// 报错 告警
					continue
				}
				// 获取更新数据 和 邮件数据
				basciEmailList, mpEmailList, solEmailList, triangleCoinEmailList, mandelEmailList,
					collectionHouseEmailList, commercialSkinEmailList, topSecurityBoxEmailList, operatorEmailList, operatorCollectiblesEmailList,
					orangeGunCollectorsEmailList, orangePendantEmailList, // 新增
					moreEmailList, oneUserSendData, err := GetUserData(ctx, openid, item.GaOpeinid, steamAreaId)
				if err != nil {
					log.WithFieldsContext(ctx, "log_type", "df_steam_ga_transfer").
						Infof("df_steam_ga_transfer GetUserData err:[%v]", err)
					// 报错 告警
					continue
				}
				oneUserSendDataByte, errJS := json.Marshal(oneUserSendData)
				basciEmailListByte, errJE := json.Marshal(basciEmailList)
				mpEmailListByte, errMP := json.Marshal(mpEmailList)
				solEmailListByte, errSL := json.Marshal(solEmailList)
				triangleCoinEmailListByte, errTC := json.Marshal(triangleCoinEmailList)
				mandelEmailListByte, errM := json.Marshal(mandelEmailList)
				collectionHouseEmailListByte, errCH := json.Marshal(collectionHouseEmailList)
				commercialSkinEmailListByte, errCS := json.Marshal(commercialSkinEmailList)
				topSecurityBoxEmailListByte, errTS := json.Marshal(topSecurityBoxEmailList)
				operatorEmailListByte, errO := json.Marshal(operatorEmailList)
				operatorCollectiblesEmailListByte, errOC := json.Marshal(operatorCollectiblesEmailList)
				moreEmailListByte, errMOR := json.Marshal(moreEmailList)
				orangeGunCollectorsEmailListByte, errOrangeGun := json.Marshal(orangeGunCollectorsEmailList)
				orangePendantEmailListByte, errOrangePendant := json.Marshal(orangePendantEmailList)
				if errJE != nil || errJS != nil || errMP != nil || errSL != nil || errTC != nil || errM != nil ||
					errCH != nil || errCS != nil || errTS != nil || errO != nil || errOC != nil || errMOR != nil ||
					errOrangeGun != nil || errOrangePendant != nil {
					// 报错 告警
					continue
				}
				fmt.Println("---------------oneUserSendDataByte---------------")
				fmt.Printf("%#v\n", errJS)
				fmt.Println("---------------oneUserEmailDataByte---------------")
				fmt.Printf("%#v\n", errJE)
				updates := map[string]interface{}{
					"status":                           pb.SteamBindGAStatus_Transtering,
					"transfer_data":                    string(oneUserSendDataByte),
					"email_basic_data":                 string(basciEmailListByte),
					"email_mp_data":                    string(mpEmailListByte),
					"email_sol_data":                   string(solEmailListByte),
					"email_trianglecoin_data":          string(triangleCoinEmailListByte),
					"email_mandel_data":                string(mandelEmailListByte),
					"email_collection_house_data":      string(collectionHouseEmailListByte),
					"email_commercial_skin_data":       string(commercialSkinEmailListByte),
					"email_security_box_data":          string(topSecurityBoxEmailListByte),
					"email_operator_data":              string(operatorEmailListByte),
					"email_operator_collectibles_data": string(operatorCollectiblesEmailListByte),
					"email_more_email_data":            string(moreEmailListByte),
					"email_orange_gun_collectors_data": string(orangeGunCollectorsEmailListByte),
					"email_pendant_data":               string(orangePendantEmailListByte),
				}
				// _ = updates
				// Updates
				dbErr = DB.DefaultConnect().WithContext(ctx).Debug().Table(tableName).Where("id = ?", item.ID).
					Where("status = ?", pb.SteamBindGAStatus_Confirm).Updates(updates).Error
				if dbErr != nil {
					err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error 3, \t [Error]:{%v} ", dbErr.Error())
					continue
				}
			}

		}
	}

	var count int64
	// 查询是否所有的状态都被转移到转移中状态
	dbErr = DB.DefaultConnect().WithContext(ctx).Debug().Table(tableName).Where("calm_date = ?", calmDate).
		Where("status = ?", pb.SteamBindGAStatus_Confirm).Where("deleted_at = ?", 0).
		Count(&count).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error 4, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	fmt.Println("---------------count---------------")
	fmt.Printf("%#v\n", count)
	// 不是全部状态都是转移中
	if count != 0 {
		err = errs.NewCustomError(ctx, code.DFTransferNotAllUser, "not all account transfer to transfering")
		return
	}

	err = SendCheckEmail(ctx, calmDate, steamAreaId)

	return
}

// SendCheckEmail 发送确认邮件
func SendCheckEmail(ctx context.Context, calmDate string, areaId int32) (err error) {
	log.WithFieldsContext(ctx, "log_type", "SendCheckEmail", "str_filed_1", calmDate, "str_filed_2", cast.ToString(areaId)).Info("SendCheckEmail Begin")
	fmt.Println("---------------SendCheckEmail---------------")
	tableName := (model.DfSteamGATransfer{}).TableName()

	var totalRecords int64
	dbErr := DB.DefaultConnect().Table(tableName).Where("calm_date = ?", calmDate).Where("status = ?", pb.SteamBindGAStatus_Transtering).Where("deleted_at = ?", 0).Count(&totalRecords).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error 5, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	fmt.Println("---------------totalRecords---------------")
	fmt.Printf("%#v\n", totalRecords)
	if totalRecords == 0 {
		return
	}

	// emailHasExcelMaxNum := 10
	// totalEmailLen := len(EmailList)
	type EmailUidData struct {
		Uid       string `gorm:"column:uid"`
		GaOpenid  string `gorm:"column:ga_openid"`
		EmailData string `gorm:"column:email_data"`
	}
	oneTypeEmailNum := int(math.Ceil(float64(totalRecords) / float64(EmailPageSize) / float64(EmailHasExcelMaxNum)))
	totalEmails := int(math.Ceil(float64(totalRecords)/float64(EmailPageSize)/float64(EmailHasExcelMaxNum))) * len(EmailList)
	for emailIndex, emailData := range EmailList {
		// if emailIndex < 5 {
		// 	continue
		// }
		// 邮件数量
		oneTypeAllEmails := int(math.Ceil(float64(totalRecords) / float64(EmailPageSize*EmailHasExcelMaxNum)))

		totalExcels := int(math.Ceil(float64(totalRecords) / float64(EmailPageSize)))

		for emailNum := 0; emailNum < int(oneTypeAllEmails); emailNum++ {
			curlEmailIndex := emailIndex*oneTypeEmailNum + emailNum + 1
			// var bindInfo []EmailUidData
			// bindInfo = make([]EmailUidData, 0)
			// 每个excel包含多少个用户
			curPageBeginNum := emailNum * EmailHasExcelMaxNum
			curMaxPageNum := emailNum*EmailHasExcelMaxNum + EmailHasExcelMaxNum
			if curMaxPageNum > totalExcels {
				curMaxPageNum = totalExcels
			}
			attachementList := make([]*mail.AttachmentBytes, 0)
			for pageNumber := curPageBeginNum; pageNumber < curMaxPageNum; pageNumber++ {
				// 分页查询
				offset := pageNumber * EmailPageSize
				var bindPages []EmailUidData
				query := fmt.Sprintf("uid,ga_openid, %s as email_data", emailData.FieldName)
				dbErr := DB.DefaultConnect().Debug().Table(tableName).Select(query).
					Where("calm_date = ?", calmDate).
					Where("status = ?", pb.SteamBindGAStatus_Transtering).
					Where("deleted_at = ?", 0).Offset(offset).
					Limit(EmailPageSize).Scan(&bindPages).Error
				if dbErr != nil {
					err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error 6, \t [Error]:{%v} ", dbErr.Error())
					return
				}
				// if len(bindPages) > 0 {
				// 	bindInfo = append(bindInfo, bindPages...)
				// }
				excelData := ExcelData{
					FileName: emailData.EmailTitle,
				}

				// bs, _ := json.Marshal(bindInfo)
				// fmt.Println("---------------bindInfo---------------")
				// fmt.Printf("%#v\n", string(bs))
				// 处理excel数据
				titleParameter := make([]string, 0)
				titleDesc := make([]string, 0)
				titleType := make([]string, 0)
				titleParameter = append(titleParameter, "openid")
				titleType = append(titleType, "string")
				titleDesc = append(titleDesc, "用户openid")
				titleParameter = append(titleParameter, "Ga Openid")
				titleType = append(titleType, "string")
				titleDesc = append(titleDesc, "Ga Openid")
				var hasSetTitle bool
				for _, item := range bindPages {
					var userEmailData model.UserEmailData
					errM := json.Unmarshal([]byte(item.EmailData), &userEmailData)
					if errM != nil {
						log.WithFieldsContext(ctx, "log_type", "df_transfer_error").Infof("EmailData=%v, err: %v", item.EmailData, errM.Error())
						continue
					}
					fmt.Println("---------------bindInfo---------------")
					uidList := strings.Split(item.Uid, "-")
					if len(uidList) != 2 {
						log.WithFieldsContext(ctx, "log_type", "df_transfer_error").Infof("uidList=%v, err: uid error")
						continue
					}
					openid := uidList[1]
					// var excel
					// 如果设置了title 使用设置的title
					if emailData.ExcelTitle != nil && hasSetTitle == false {
						fmt.Println("---------------emailData.ExcelTitle---------------")
						hasSetTitle = true
						for _, excelTitle := range emailData.ExcelTitle {
							titleParameter = append(titleParameter, excelTitle.Parameter)
							titleType = append(titleType, excelTitle.Type)
							titleDesc = append(titleDesc, excelTitle.Description)
						}
					}
					fmt.Println("---------------1---------------")
					for oneIndex, oneData := range userEmailData.List {
						// 一行数据
						oneLine := make([]string, 0)
						excelOneData := make([][]string, 0)
						oneLine = append(oneLine, openid)
						oneLine = append(oneLine, item.GaOpenid)
						for _, info := range oneData.InfoList {
							if hasSetTitle == false && oneIndex == 0 {
								titleParameter = append(titleParameter, info.Parameter)
								titleType = append(titleType, info.Type)
								titleDesc = append(titleDesc, info.Description)
							}
							oneLine = append(oneLine, info.Value)
						}
						hasSetTitle = true
						excelOneData = append(excelOneData, oneLine)
						excelData.Data = append(excelData.Data, excelOneData...)
					}
					fmt.Println("---------------2---------------")
					// 如何用户没数据 加一条
					if len(userEmailData.List) == 0 {
						oneLine := make([]string, 0)
						excelOneData := make([][]string, 0)
						oneLine = append(oneLine, openid)
						oneLine = append(oneLine, item.GaOpenid)
						for i := 0; i < len(titleParameter)-1; i++ {
							oneLine = append(oneLine, "-")
						}
						excelOneData = append(excelOneData, oneLine)
						excelData.Data = append(excelData.Data, excelOneData...)
					}
				}
				excelData.Title = append(excelData.Title, titleParameter, titleType, titleDesc)
				mergeData := make([][]string, 0)
				mergeData = append(mergeData, excelData.Title...)
				mergeData = append(mergeData, excelData.Data...)
				excelData.Data = mergeData
				// for _, item := range excelData {
				var filePath string
				uuid, _ := uuid.NewUUID()
				fmt.Println("---------------uuidfileName---------------")
				fmt.Printf("%#v\n", uuid.String()+".xlsx")
				// filePath, err = datadump.CreateExcel(ctx, excelData.FileName+".xlsx", excelData.Data)
				fmt.Println("---------------write---------------")
				filePath, err = datadump.CreateExcel(ctx, uuid.String()+".xlsx", excelData.Data)
				if err != nil {
					fmt.Println("---------------err---------------")
					fmt.Printf("%#v\n", err)
					return
				}
				fmt.Println("---------------read---------------")
				dataByte, errR := ioutil.ReadFile(filePath)
				if errR != nil {
					fmt.Println("---------------errR---------------")
					fmt.Printf("%#v\n", err)
					log.InfoContextf(ctx, "ReadFile  err:%v", err)

					return
				}
				fmt.Println("---------------3---------------")
				attachement := &mail.AttachmentBytes{
					FileName: fmt.Sprintf("%s_%s(%d).xlsx", calmDate, excelData.FileName, pageNumber+1),
					Data:     dataByte,
				}
				fmt.Println("---------------4---------------")
				attachementList = append(attachementList, attachement)
			}

			emailConfig := config.GetConfig().DfSteamGATransfer.EmailConfig
			req := &mail.SendExtranetMailNewReq{
				Sender:         emailConfig.Sender,
				SenderAlias:    emailConfig.SenderAlias,
				Passwd:         emailConfig.Passwd,
				ReceiverList:   emailConfig.ReceiverList,
				BccList:        emailConfig.BccList,
				Host:           emailConfig.Host,
				Port:           emailConfig.Port,
				Title:          fmt.Sprintf("%s 账号迁移-%s[%d/%d]", calmDate, emailData.EmailTitle, curlEmailIndex, totalEmails),
				Content:        fmt.Sprintf("共发生转移人数：%d", totalRecords),
				AttachmentList: attachementList,
			}
			// fmt.Printf("%#v\n", req)
			_, err = mailProxy.SendExtranetMailNew(ctx, req)
			fmt.Println("---------------emailErr---------------")
			fmt.Printf("%#v\n", req)
			fmt.Printf("%#v\n", len(attachementList))
			fmt.Printf("%#v\n", err)
			log.InfoContextf(ctx, "SendCheckEmail req:%v, err:%v,title:%v", req, err, fmt.Sprintf("%s 账号迁移-%s[%d/%d]", calmDate, emailData.EmailTitle, curlEmailIndex, totalEmails))
			if err != nil {
				errs.NewCustomError(ctx, code.DFTransferSendEmailErr, "err=%v", err.Error())
			}
			log.WithFieldsContext(ctx, "log_type", "SendCheckEmailSend", "str_filed_1", calmDate, "str_filed_2", cast.ToString(areaId)).Info("SendCheckEmail req:%v, err:%v,title:%v", req, err, fmt.Sprintf("%s 账号迁移-%s[%d/%d]", calmDate, emailData.EmailTitle, curlEmailIndex, totalEmails))
		}
	}
	// }
	// TODO 更新邮件表

	return
}

func GetUserData(ctx context.Context, openid string, gaOpenid string, areaId int32) (
	basciEmailList model.UserEmailData,
	mpEmailList model.UserEmailData,
	solEmailList model.UserEmailData,
	triangleCoinEmailList model.UserEmailData,
	mandelEmailList model.UserEmailData,
	collectionHouseEmailList model.UserEmailData,
	commercialSkinEmailList model.UserEmailData,
	topSecurityBoxEmailList model.UserEmailData,
	operatorEmailList model.UserEmailData,
	operatorCollectiblesEmailList model.UserEmailData,
	orangeGunCollectorsEmailList model.UserEmailData, // 橙枪（典藏）
	orangePendantEmailList model.UserEmailData, // 典藏挂饰橙色
	moreEmailList model.UserEmailData,
	sendData model.UserSendData, err error) {
	// 获取用户数据
	// 1、查询openid基本信息 CmdID ********
	basciEmailData, sendBasicEmailDataList, err := GetBasicSendData(ctx, openid, areaId)
	fmt.Println("-------------GetBasicSendData-----------------")
	fmt.Printf("%#v\n", err)
	if err != nil {
		return
	}
	basciEmailData.GaOpenid = gaOpenid
	basciEmailList = model.UserEmailData{
		List: []model.OneEmailData{
			basciEmailData,
		},
	}

	// 2、玩家MP详细信息查询 CmdID 29236054
	mpEmailData, sendMpEmailDataList, err := GetMpSendData(ctx, openid, areaId)
	fmt.Println("-------------GetMpSendData-----------------")
	fmt.Printf("%#v\n", err)
	if err != nil {
		return
	}
	mpEmailData.GaOpenid = gaOpenid
	mpEmailList = model.UserEmailData{
		List: []model.OneEmailData{
			mpEmailData,
		},
	}

	// 3、查询玩家SOL详细信息 CmdID 29236053
	solEmailData, sendSolEmailDataList, err := GetSolSendData(ctx, openid, areaId)
	fmt.Println("-------------GetSolSendData-----------------")
	fmt.Printf("%#v\n", err)
	if err != nil {
		return
	}
	solEmailData.GaOpenid = gaOpenid
	solEmailList = model.UserEmailData{
		List: []model.OneEmailData{
			solEmailData,
		},
	}

	// 4、三角币和充值信息查询 CmdID 29236048
	triangleCoinAndRechargeEmailData, triangleCoinAndRechargeEmailDataList, err := GetTriangleCoinAndRechargeData(ctx, openid, areaId)
	fmt.Println("-------------GetTriangleCoinAndRechargeData-----------------")
	fmt.Printf("%#v\n", err)
	if err != nil {
		return
	}
	triangleCoinAndRechargeEmailData.GaOpenid = gaOpenid
	triangleCoinEmailList = model.UserEmailData{
		List: []model.OneEmailData{
			triangleCoinAndRechargeEmailData,
		},
	}
	// 5、曼德尔币
	mandelEmailData, sendMandelEmailDataList, err := GetMandelSendData(ctx, openid, areaId)
	fmt.Println("-------------GetMandelSendData-----------------")
	fmt.Printf("%#v\n", err)
	if err != nil {
		return
	}
	mandelEmailData.GaOpenid = gaOpenid
	mandelEmailList = model.UserEmailData{
		List: []model.OneEmailData{
			mandelEmailData,
		},
	}
	// 6、藏品仓库查询 CmdID 29236040
	collectionHouseEmailData, sendCollectionHouseEmailDataList, err := GetCollectionHorizonSendData(ctx, openid, areaId)
	fmt.Println("-------------GetCollectionHorizonSendData-----------------")
	fmt.Printf("%#v\n", err)
	if err != nil {
		return
	}
	for idx := range collectionHouseEmailData {
		collectionHouseEmailData[idx].GaOpenid = gaOpenid
	}
	collectionHouseEmailList = model.UserEmailData{
		List: collectionHouseEmailData,
	}
	// 7、典藏皮肤信息查询 CmdID 29236042
	commercialSkinEmailData, sendCommercialSkinEmailDataList, err := GetCommercialSkinSendData(ctx, openid, areaId)
	fmt.Println("-------------GetCommercialSkinSendData-----------------")
	fmt.Printf("%#v\n", err)
	if err != nil {
		return
	}
	for idx := range commercialSkinEmailData {
		commercialSkinEmailData[idx].GaOpenid = gaOpenid
	}
	commercialSkinEmailList = model.UserEmailData{
		List: commercialSkinEmailData,
	}
	// 8、 顶级安全箱赛季权限卡
	topSecurityBoxEmailData, sendTopSecurityBoxEmailDataList, err := GetTopSecurityBoxSendData(ctx, openid, areaId)
	fmt.Println("-------------GetTopSecurityBoxSendData-----------------")
	fmt.Printf("%#v\n", err)
	if err != nil {
		return
	}
	topSecurityBoxEmailData.GaOpenid = gaOpenid
	topSecurityBoxEmailList = model.UserEmailData{
		List: []model.OneEmailData{
			topSecurityBoxEmailData,
		},
	}
	// 9、干员系统查询
	operatorEmailData, sendOperatorEmailDataList, err := GetOperatorSendData(ctx, openid, areaId)
	fmt.Println("-------------GetOperatorSendData-----------------")
	fmt.Printf("%#v\n", err)
	if err != nil {
		return
	}
	for idx := range operatorEmailData {
		operatorEmailData[idx].GaOpenid = gaOpenid
	}
	operatorEmailList = model.UserEmailData{
		List: operatorEmailData,
	}

	// 10、(干员)典藏挂饰信息查询
	operatorCollectiblesEmailData, sendOperatorCollectiblesEmailDataList, err := GetOperatorCollectiblesSendData(ctx, openid, areaId)
	fmt.Println("-------------GetOperatorCollectiblesSendData-----------------")
	fmt.Printf("%#v\n", err)
	if err != nil {
		return
	}
	operatorCollectiblesEmailList = model.UserEmailData{
		List: operatorCollectiblesEmailData,
	}

	// 11、橙枪（典藏） 典藏皮肤信息查询(29236042)
	orangeGunCollectorsEmailList = buildCollectionEmailData(commercialSkinEmailData)
	// 12、典藏挂饰橙色 典藏挂饰信息查询(********)
	orangePendantEmailList = buildCollectionEmailData(operatorCollectiblesEmailData)

	// emailData = model.UserEmailData{
	// 	List: []model.OneEmailData{
	// 		basciEmailData,
	// 		mpEmailData,
	// 		solEmailData,
	// 		triangleCoinAndRechargeEmailData,
	// 		mandelEmailData,
	// 		// commercialSkinEmailData,
	// 		topSecurityBoxEmailData,
	// 		// operatorEmailData,
	// 	},
	// }
	// emailData.List = append(emailData.List, collectionHouseEmailList...)
	// emailData.List = append(emailData.List, commercialSkinEmailList...)
	// emailData.List = append(emailData.List, operatorEmailList...)
	// emailData.List = append(emailData.List, operatorCollectiblesEmailList...)
	list := make([]model.OneSendData, 0)
	list = append(list, sendBasicEmailDataList...)
	list = append(list, sendMpEmailDataList...)
	list = append(list, sendSolEmailDataList...)
	list = append(list, triangleCoinAndRechargeEmailDataList...)
	list = append(list, sendMandelEmailDataList...)
	list = append(list, sendCollectionHouseEmailDataList...)
	list = append(list, sendCommercialSkinEmailDataList...)
	list = append(list, sendTopSecurityBoxEmailDataList...)
	list = append(list, sendOperatorEmailDataList...)
	list = append(list, sendOperatorCollectiblesEmailDataList...)

	sendData = model.UserSendData{
		List: list,
	}
	//   邮件排序
	moreEmailList, sendData = SortUserData(openid, gaOpenid, sendData)
	// sort.Slice(emailData.List, func(i, j int) bool {
	// 	return emailData.List[i].Priority < emailData.List[j].Priority
	// })

	return
}

// 构建新增邮件数据
func buildCollectionEmailData(dataList []model.OneEmailData) model.UserEmailData {
	mLen := len(OrangeGunCollectors) + len(OrangePendantCollectors)
	m := make(map[string]struct{}, mLen)
	for _, v := range OrangeGunCollectors {
		m[v] = struct{}{}
	}
	for _, v := range OrangePendantCollectors {
		m[v] = struct{}{}
	}
	// 过滤数据 检查传入数据中是否包含 典藏皮肤或者典藏挂饰
	newDataList := make([]model.OneEmailData, 0, len(dataList))
	for idx, data := range dataList {
		for _, info := range data.InfoList {
			if _, ok := m[info.Value]; !ok {
				continue
			}
			newDataList = append(newDataList, dataList[idx])
			break
		}
	}
	return model.UserEmailData{
		List: newDataList,
	}
}

// 1、查询openid基本信息 CmdID ********
// 1-1、1）全面战场战场等级——将转移对象的GA账号同步为被转移Steam账号的等级
// 1-2、2）烽火行动行动等级——将转移对象的GA账号同步为被转移Steam账号的等级
// 1-3、14）烽火地带物资——将转移对象的GA账号下发被转移Steam账号的仓库价值的哈夫币
// 1-4、16）全面战场武器等级——将转移对象的GA账号下发对应被转移Steam账号全面战场等级的不同数量的高级武器经验币（每10级发一把满级枪经验的经验币）
// 1-15、17）活跃皮肤礼包——将转移对象的GA账号下发一个统一的活跃活动皮肤礼包
func GetBasicSendData(ctx context.Context, openid string, areaId int32) (userEmailData model.OneEmailData, sendList []model.OneSendData, err error) {
	basiscRsp, errB := gameDFProxy.SelectDFMBasicAccountInformation(ctx, &gameDfPb.SelectDFMBasicAccountInformationReq{
		Openid: openid,
		AreaId: areaId,
	})
	if errB != nil {
		err = errB
		return
	}
	userEmailData = model.OneEmailData{
		Key:      "basic_info",
		FileName: "查询openid基本信息(********)",
		Openid:   openid,
		InfoList: basiscRsp.GetReturnValueList(),
	}
	sendList = make([]model.OneSendData, 0)
	// 1）全面战场战场等级
	tdmLevel, errG := GetIntValueFormatFromList(ctx, basiscRsp.GetReturnValueList(), "tdmlevel")
	if errG != nil {
		err = errG
		return
	}
	if tdmLevel > 1 {
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_UpdateTDMLevel,
			Num:  tdmLevel,
			// Seq:  GetGaAmsSerial(),
		})
	}

	// 2）烽火行动行动等级
	solLevel, errG := GetIntValueFormatFromList(ctx, basiscRsp.GetReturnValueList(), "level")
	if errG != nil {
		err = errG
		return
	}
	if solLevel > 1 {
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_UpdateSOLLevel,
			Num:  solLevel,
			// Seq:  GetGaAmsSerial(),
		})
	}

	// 哈夫币 = 哈夫币+ 仓库价值
	hafCoin, errG := GetIntValueFormatFromList(ctx, basiscRsp.GetReturnValueList(), "hafcoinnum")
	if errG != nil {
		err = errG
		return
	}
	propcapital, errG := GetIntValueFormatFromList(ctx, basiscRsp.GetReturnValueList(), "propcapital")
	if errG != nil {
		err = errG
		return
	}
	// 哈夫币 = 哈夫币+ 仓库价值
	if hafCoin+propcapital > 0 {
		// configData, _ := ItemToAmsData["17020000010"]
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_AMS,
			Num:  hafCoin + propcapital,
			// Seq:      GetGaAmsSerial(),
			AmsId:    GA_AMSID,
			GroupId:  "4021823",
			Priority: 1,
			ItemName: "哈夫币",
		})
	}

	// 全面战场武器等级 经验币
	expCoinNum := ConfigureExpCoin(tdmLevel)
	if expCoinNum > 0 {
		// configData, _ := ItemToAmsData["32210000003"]
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_AMS,
			Num:  expCoinNum,
			// Seq:      GetGaAmsSerial(),
			AmsId:    GA_AMSID,
			GroupId:  "4021831",
			Priority: 1,
			ItemName: "武器高级经验卡",
		})
	}

	// 活跃皮肤礼包——将转移对象的GA账号下发一个统一的活跃活动皮肤礼包

	sendList = append(sendList, model.OneSendData{
		Type: SendPresentType_AMS,
		Num:  1,
		// Seq:      GetGaAmsSerial(),
		AmsId:    GA_AMSID,
		GroupId:  "4022822",
		Priority: 1,
		ItemName: "活跃皮肤礼包",
	})
	return
}

// 2、玩家MP详细信息查询 CmdID 29236054
// 2-1、3）全面战场当赛季段位——将转移对象的GA账号同步为被转移Steam账号的段位和积分数
// 2-2、10）通行证——将转移对象的GA账号下发被转移Steam账号的通行证类型和等级
func GetMpSendData(ctx context.Context, openid string, areaId int32) (userEmailData model.OneEmailData, sendList []model.OneSendData, err error) {

	mpRsp, errB := gameDFProxy.GetPlayerMPDetail(ctx, &gameDfPb.PlayerMPDetailReq{
		Openid: openid,
		AreaId: areaId,
	})
	if errB != nil {
		err = errB
		return
	}
	userEmailData = model.OneEmailData{
		Key:      "mp_info",
		FileName: "玩家MP详细信息查询(29236054)",
		Openid:   openid,
		InfoList: mpRsp.GetReturnValueList(),
	}
	levelscore, errG := GetIntValueFormatFromList(ctx, mpRsp.GetReturnValueList(), "levelscore")
	if errG != nil {
		err = errG
		return
	}
	if levelscore > 0 {
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_UpdateAllOutBattleRankScore,
			Num:  levelscore,
			// Seq:          GetGaAmsSerial(),
			ScoreType:    1,
			ScoreSubType: 1,
		})
	}

	// 通行证
	battlepasslevel, errG := GetIntValueFormatFromList(ctx, mpRsp.GetReturnValueList(), "battlepasslevel")
	if errG != nil {
		err = errG
		return
	}
	battlepasstype, errG := GetIntValueFormatFromList(ctx, mpRsp.GetReturnValueList(), "battlepasstype")
	if errG != nil {
		err = errG
		return
	}
	// 0是指未购买，2是开通烽火，3是开通全面战场，4是全部开通
	// battlepasstype = 2
	if battlepasstype > 0 {
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_SubscribeOrUpgradeGarenaBattlePass,
			Num:  battlepasstype,
			// Seq:      GetGaAmsSerial(),
			Priority: 1,
		})
	}
	if battlepasslevel > 1 {
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_CalculateBattlePassExperienceProgress,
			Num:  battlepasslevel - 1,
			// Seq:      GetGaAmsSerial(),
			Priority: 1,
		})
	}

	return
}

// 3、查询玩家SOL详细信息 CmdID 29236053
// 3-1、4）烽火行动当赛季段位——将转移对象的GA账号同步为被转移Steam账号的段位和积分数
func GetSolSendData(ctx context.Context, openid string, areaId int32) (userEmailData model.OneEmailData, sendList []model.OneSendData, err error) {
	solRsp, errB := gameDFProxy.QueryPlayerSOLDetails(ctx, &gameDfPb.QueryPlayerSOLDetailsReq{
		Openid: openid,
		AreaId: areaId,
	})
	if errB != nil {
		err = errB
		return
	}
	userEmailData = model.OneEmailData{
		Key:      "sol_info",
		FileName: "查询玩家SOL详细信息(29236053)",
		Openid:   openid,
		InfoList: solRsp.GetReturnValueList(),
	}
	levelscore, errG := GetIntValueFormatFromList(ctx, solRsp.GetReturnValueList(), "levelscore")
	if errG != nil {
		err = errG
		return
	}
	if levelscore > 1000 {
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_UpdateSOLRankScore,
			Num:  levelscore,
			// Seq:          GetGaAmsSerial(),
			ScoreType:    1,
			ScoreSubType: 1,
			Priority:     0,
		})
	}

	return
}

// 4、三角币和充值信息查询 CmdID 29236048
// 4-1、6）三角币——将转移对象的GA账号下发被转移Steam账号的三角币存量
// 4-2、7）三角券——将转移对象的GA账号下发被转移Steam账号的三角券存量
func GetTriangleCoinAndRechargeData(ctx context.Context, openid string, areaId int32) (userEmailData model.OneEmailData, sendList []model.OneSendData, err error) {
	triangleRsp, errB := gameDFProxy.GetTriangleCoinAndRecharge(ctx, &gameDfPb.TriangleCoinRechargeReq{
		Openid: openid,
		AreaId: areaId,
	})
	if errB != nil {
		err = errB
		return
	}
	userEmailData = model.OneEmailData{
		Key:      "triangle_info",
		FileName: "三角币和充值信息查询(29236048)",
		Openid:   openid,
		InfoList: triangleRsp.GetReturnValueList(),
	}
	// 三角币存量
	trianglecoinnum, errG := GetIntValueFormatFromList(ctx, triangleRsp.GetReturnValueList(), "trianglecoinnum")
	if errG != nil {
		err = errG
		return
	}
	if trianglecoinnum > 0 {
		// configData, _ := ItemToAmsData["17888808888"]
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_AMS,
			Num:  trianglecoinnum,
			// Seq:      GetGaAmsSerial(),
			AmsId:    GA_AMSID,
			GroupId:  "4021824",
			Priority: 1,
			ItemName: "三角币",
		})
	}
	// 三角券存量
	bindedtrianglecoinnum, errG := GetIntValueFormatFromList(ctx, triangleRsp.GetReturnValueList(), "bindedtrianglecoinnum")
	if errG != nil {
		err = errG
		return
	}
	if bindedtrianglecoinnum > 0 {
		// configData, _ := ItemToAmsData["17888808889"]
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_AMS,
			Num:  bindedtrianglecoinnum,
			// Seq:      GetGaAmsSerial(),
			AmsId:    GA_AMSID,
			GroupId:  "4021825",
			Priority: 1,
			ItemName: "三角券",
		})
	}

	return
}

// 5、曼德尔币
// 5-1、8）曼德尔币——将转移对象的GA账号下发被转移Steam账号的曼德尔币存量
func GetMandelSendData(ctx context.Context, openid string, areaId int32) (userEmailData model.OneEmailData, sendList []model.OneSendData, err error) {
	num, err := GetMandelCoinNum(ctx, DeltaverseReq{
		Openid: openid,
		Zoneid: int64(areaId),
	})
	if err != nil {
		return
	}
	userEmailData = model.OneEmailData{
		Key:      "mandel_info",
		FileName: "曼德尔币存量(datamore)",
		Openid:   openid,
		InfoList: []*gameDfPb.IDIPReturnValueDetails{
			{
				Parameter:   "MandelCoin",
				Type:        "int32",
				Value:       cast.ToString(num),
				Description: "曼德尔币存量",
			},
		},
	}
	if num > 0 {
		// configData, _ := ItemToAmsData["17888808887"]
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_AMS,
			Num:  num,
			// Seq:      GetGaAmsSerial(),
			AmsId:    GA_AMSID,
			GroupId:  "4021826",
			Priority: 1,
			ItemName: "曼德尔币",
		})
	}

	return
}

// 6、藏品仓库查询 CmdID 29236040
// 6-1、9）量子密钥——将转移对象的GA账号下发被转移Steam账号的量子密钥存量
// 6-2、11）曼德尔砖——将转移对象的GA账号下发被转移Steam账号的曼德尔砖存量
// 6-3、12）普通商业化皮肤——将转移对象的GA账号下发被转移Steam账号的普通商业化皮肤存量
// 6-4、15）3*3保险箱——将转移对象的GA账号下发被转移Steam账号的藏品内的库存的3*3保险箱分天体验卡和已获取的3*3保险箱赛季权限卡
func GetCollectionHorizonSendData(ctx context.Context, openid string, areaId int32) (userEmailList []model.OneEmailData, sendList []model.OneSendData, err error) {
	collectionRsp, errB := gameDFProxy.GetCollectionStorage(ctx, &gameDfPb.CollectionStorageReq{
		Openid: openid,
		AreaId: areaId,
	})
	if errB != nil {
		err = errB
		return
	}
	itemListStr, errG := GetStrValueFormatFromList(ctx, collectionRsp.GetReturnValueList(), "itemlist_list")
	if errG != nil {
		err = errG
		return
	}
	if itemListStr == "" {
		return
	}

	rspList, errG := GetRspArray(ctx, itemListStr)
	if errG != nil {
		err = errG
		return
	}
	// 邮件
	userEmailList = GetArrayEmailListByArray(ctx, openid, "collection_info", "商品仓库查询(29236040)", rspList)
	// 发货
	sendList = GetCollectionHorizonSendList(ctx, openid, rspList)
	return
}

// 7、典藏皮肤信息查询 CmdID 29236042
// 7-1、13）典藏商业化皮肤——将转移对象的GA账号下发被转移Steam账号的典藏商业化皮服存量、成色、磨损度，典藏传说皮肤按规则处理编号
func GetCommercialSkinSendData(ctx context.Context, openid string, areaId int32) (userEmailList []model.OneEmailData, sendList []model.OneSendData, err error) {
	exclusiveSkinRsp, errB := gameDFProxy.GetExclusiveSkinInfo(ctx, &gameDfPb.ExclusiveSkinReq{
		Openid: openid,
		AreaId: areaId,
	})
	fmt.Println("---------------errB---------------")
	fmt.Printf("%#v\n", errB)
	if errB != nil {
		err = errB
		return
	}
	itemListStr, errG := GetStrValueFormatFromList(ctx, exclusiveSkinRsp.GetReturnValueList(), "itemlist_list")
	fmt.Println("---------------errG---------------")
	fmt.Printf("%#v\n", errG)
	if errG != nil {
		err = errG
		return
	}
	if itemListStr == "" {
		return
	}
	rspList, errG := GetRspArray(ctx, itemListStr)
	if errG != nil {
		err = errG
		return
	}
	// 邮件
	userEmailList = GetArrayEmailListByArray(ctx, openid, "exclusive_skin_info", "典藏皮肤信息查询(29236042)", rspList)
	// 发货
	sendList = GetCommercialSkinSendList(ctx, openid, rspList)
	return
}

// 8、 顶级安全箱赛季权限卡
// 32300000093 13子）顶级安全箱赛季权限卡
func GetTopSecurityBoxSendData(ctx context.Context, openid string, areaId int32) (userEmailData model.OneEmailData, sendList []model.OneSendData, err error) {
	num, err := GetMaxLevelBoxNum(ctx, DeltaverseReq{
		Openid: openid,
		Zoneid: int64(areaId),
	})
	if err != nil {
		return
	}
	userEmailData = model.OneEmailData{
		Key:      "top_box_info",
		FileName: "顶级安全箱赛季权限卡(datamore)",
		Openid:   openid,
		InfoList: []*gameDfPb.IDIPReturnValueDetails{
			{
				Parameter:   "SecurityBoxCard",
				Type:        "int32",
				Value:       cast.ToString(num),
				Description: "顶级安全箱赛季权限卡",
			},
		},
	}
	// configData, _ := ItemToAmsData["32300000093"]
	// configData, _ := ItemToAmsData["32300000133"]
	if num > 0 {
		sendList = append(sendList, model.OneSendData{
			Type: SendPresentType_AMS,
			Num:  num,
			// Seq:      GetGaAmsSerial(),
			AmsId: GA_AMSID_2504,
			//GroupId:  "4021830",
			GroupId:  "4025561", // new 顶级安全箱赛季权限卡（S4）
			Priority: 1,
			ItemName: "顶级安全箱赛季权限卡",
		})
	}
	return
}

// 9、干员信息查询（********）
// 9-1、5）干员——解锁截至LONG 2版本的全干员下发给到被转移对象的GA账号（除了威龙通过新手签到获取）
func GetOperatorSendData(ctx context.Context, openid string, areaId int32) (userEmailList []model.OneEmailData, sendList []model.OneSendData, err error) {
	// 干员查询
	operatorsRsp, errB := gameDFProxy.QueryDeltaForceOperatorsItems(ctx, &gameDfPb.QueryDeltaForceOperatorsItemsReq{
		Openid: openid,
		AreaId: areaId,
	})
	if errB != nil {
		err = errB
		return
	}
	itemListStr, errG := GetStrValueFormatFromList(ctx, operatorsRsp.GetReturnValueList(), "itemlist_list")
	fmt.Println("---------------errG---------------")
	fmt.Printf("%#v\n", errG)
	if errG != nil {
		err = errG
		return
	}
	if itemListStr == "" {
		return
	}
	rspList, errG := GetRspArray(ctx, itemListStr)
	if errG != nil {
		err = errG
		return
	}
	userEmailList = GetArrayEmailListByArray(ctx, openid, "operators_info", "干员系统道具查询(********)", rspList)
	// 发货
	sendList = GetOperatorSendList(ctx, openid, rspList)

	// 9-1、5）干员——解锁截至LONG 2版本的全干员下发给到被转移对象的GA账号（除了威龙通过新手签到获取）
	// configData, _ := ItemToAmsData["88000000026"]
	sendList = append(sendList, model.OneSendData{
		Type: SendPresentType_AMS,
		Num:  1,
		// Seq:      GetGaAmsSerial(),
		AmsId:    GA_AMSID,
		GroupId:  "4021816", // 骇爪
		Priority: 1,
		ItemName: "骇爪",
	})
	// configData1, _ := ItemToAmsData["88000000035"]
	sendList = append(sendList, model.OneSendData{
		Type: SendPresentType_AMS,
		Num:  1,
		// Seq:      GetGaAmsSerial(),
		AmsId:    GA_AMSID,
		GroupId:  "4021817", // 乌鲁鲁
		Priority: 1,
		ItemName: "乌鲁鲁",
	})
	// configData2, _ := ItemToAmsData["88000000036"]
	sendList = append(sendList, model.OneSendData{
		Type: SendPresentType_AMS,
		Num:  1,
		// Seq:      GetGaAmsSerial(),
		AmsId:    GA_AMSID,
		GroupId:  "4021818", // 赫拉格
		Priority: 1,
		ItemName: "Zoya",
	})
	sendList = append(sendList, model.OneSendData{
		Type: SendPresentType_AMS,
		Num:  1,
		// Seq:      GetGaAmsSerial(),
		AmsId:    GA_AMSID,
		GroupId:  "4022919", // 赫拉格
		Priority: 1,
		ItemName: "深蓝",
	})
	return
}

// 10、典藏挂饰信息查询(********)
func GetOperatorCollectiblesSendData(ctx context.Context, openid string, areaId int32) (userEmailList []model.OneEmailData, sendList []model.OneSendData, err error) {
	// 干员挂饰查询
	operatorCollectiblesRsp, errB := gameDFProxy.QueryDeltaForceOperatorCollectibles(ctx, &gameDfPb.QueryDeltaForceOperatorCollectiblesReq{
		Openid: openid,
		AreaId: areaId,
	})
	if errB != nil {
		err = errB
		return
	}
	itemListStr, errG := GetStrValueFormatFromList(ctx, operatorCollectiblesRsp.GetReturnValueList(), "mysticalpendantdata_list")
	fmt.Println("---------------errG---------------")
	fmt.Printf("%#v\n", errG)
	if errG != nil {
		err = errG
		return
	}
	if itemListStr == "" {
		return
	}
	rspList, errG := GetRspArray(ctx, itemListStr)
	if errG != nil {
		err = errG
		return
	}
	userEmailList = GetArrayEmailListByArray(ctx, openid, "operator_collectibles_info", "典藏挂饰信息查询(********)", rspList)

	// 发货
	sendList = GetOperatorCollectiblesSendList(ctx, openid, rspList)

	return
}
func ScheduleAccountTransfer(ctx context.Context, calmDate string, steamAreaId int32, GAAreaId int32) (err error) {
	// 加锁
	// 添加锁
	lockKey := global.GetPrefix() + "_df_transfer_lock_"
	isLocking, errR := redis.GetClient().SetNX(ctx, lockKey, 1, 1*time.Hour).Result()
	defer redis.GetClient().Del(ctx, lockKey)
	if errR != nil && errR != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errR.Error())
		return
	}
	if !isLocking {
		err = errs.NewCustomError(ctx, code.DFTransferLockErr,
			"transfer isLocking, \t [Error]:{%v} ", errR.Error())
		return
	}
	// 取数据
	// 查询待迁移数据
	tableName := (model.DfSteamGATransfer{}).TableName()
	// var bindInfo []model.DfSteamGATransfer
	var totalRecords int64
	dbErr := DB.DefaultConnect().Table(tableName).Where("calm_date = ?", calmDate).
		Where("status = ?", pb.SteamBindGAStatus_Transtering).
		Where("deleted_at = ?", 0).Count(&totalRecords).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	if totalRecords == 0 {
		return
	}
	totalPages := int(math.Ceil(float64(totalRecords) / float64(DBPageSize)))
	for i := 0; i < int(totalRecords); i += DBPageSize {
		// 分页查询
		for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
			offset := (pageNumber - 1) * DBPageSize
			var bindInfo []model.DfSteamGATransfer
			dbErr := DB.DefaultConnect().Table(tableName).Where("calm_date = ?", calmDate).
				Where("status = ?", pb.SteamBindGAStatus_Transtering).
				Where("send_status in ?", []int32{SendStatus_NotBegin, SendStatus_Fail}).
				Where("deleted_at = ?", 0).Offset(i).Limit(DBPageSize).
				Offset(offset).Limit(DBPageSize).Order("id asc").Find(&bindInfo).Error
			if dbErr != nil {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", dbErr.Error())
				return
			}
			for _, userData := range bindInfo {
				userSendData, sendStatus := SendTransferPresent(ctx, userData, GAAreaId)
				oneUserSendDataByte, errJE := json.Marshal(userSendData)
				if errJE != nil {
					log.WithFieldsContext(ctx, "log_type", "df_transfer_error").Infof("userSendData=%v, err: %v", userSendData, errJE.Error())
					continue
				}
				fmt.Println("---------------oneUserEmailDataByte---------------")
				fmt.Printf("%#v\n", errJE)
				updates := map[string]interface{}{
					"send_status":   sendStatus,
					"transfer_data": string(oneUserSendDataByte),
				}
				if sendStatus == SendStatus_Success {
					updates["status"] = pb.SteamBindGAStatus_Finish
				}
				// 更新表
				dbErr := DB.DefaultConnect().Table(tableName).Where("id = ?", userData.ID).Updates(updates).Error
				if dbErr != nil {
					err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error, \t [Error]:{%v} ", dbErr.Error())
					return
				}
			}
		}
	}

	return
}

func SendTransferPresent(ctx context.Context, userData model.DfSteamGATransfer, gaAreaID int32) (userSendData model.UserSendData, sendStatus int32) {
	success := true
	userSendData.List = make([]model.OneSendData, 0)
	for _, oneSendData := range userData.TransferDataStruct.List {
		// 超过最大邮件限制
		if oneSendData.SendStatus == SendStatus_Success || oneSendData.SendStatus == SendStatus_Email_Max {
			userSendData.List = append(userSendData.List, oneSendData)
			continue
		}
		// 判断下发时加上流水号
		if oneSendData.Seq == "" {
			oneSendData.Seq = GetGaAmsSerial()
		}

		updateParamBasicItem := &gameDfPb.UpdateGAParamBasicItem{
			Openid: userData.GaOpeinid,
			Serial: oneSendData.Seq,
			AreaId: uint32(gaAreaID),
		}
		switch oneSendData.Type {

		case SendPresentType_AMS:
			// AMS
			_, errA := amsPresentProxy.SendOpenidAmsPresent(ctx, &amsPresentPb.SendOpenidAmsPresentReq{
				AmsId:      oneSendData.AmsId,
				GroupId:    oneSendData.GroupId,
				Serial:     oneSendData.Seq,
				PackageNum: oneSendData.Num,
				Openid:     userData.GaOpeinid,
				LangType:   userData.LangType,
				RoleInfo: &gamePb.RoleInfo{
					GameId:   GaGameIDStr,
					AreaId:   int64(gaAreaID),
					GameName: GaServiceType,
				},
				MoreInfo: oneSendData.MoreInfo,
			})
			if errA != nil {
				success = false
				oneSendData.SendStatus = SendStatus_Fail
			} else {
				oneSendData.SendStatus = SendStatus_Success
			}
			// oneSendData.Seq = seq
		// 更新全面战场等级分
		case SendPresentType_UpdateAllOutBattleRankScore:
			_, errS := gameDFProxy.UpdateAllOutBattleRankScore(ctx, &gameDfPb.UpdateAllOutBattleRankScoreReq{
				UpdateParamBasicItem: updateParamBasicItem,
				ScoreType:            oneSendData.ScoreType,
				ScoreSubtype:         oneSendData.ScoreSubType,
				Score:                int64(oneSendData.Num),
			})
			if errS != nil {
				success = false
				oneSendData.SendStatus = SendStatus_Fail
			} else {
				oneSendData.SendStatus = SendStatus_Success
			}
		// 修改烽火地带等级分
		case SendPresentType_UpdateSOLRankScore:
			_, errS := gameDFProxy.UpdateFireActionRankScore(ctx, &gameDfPb.UpdateFireActionRankScoreReq{
				UpdateParamBasicItem: updateParamBasicItem,
				ScoreType:            oneSendData.ScoreType,
				ScoreSubtype:         oneSendData.ScoreSubType,
				Score:                int64(oneSendData.Num),
			})
			if errS != nil {
				success = false
				oneSendData.SendStatus = SendStatus_Fail
			} else {
				oneSendData.SendStatus = SendStatus_Success
			}
		// 更新全面战场等级
		case SendPresentType_UpdateTDMLevel:
			_, errS := gameDFProxy.UpdateAllOutBattleSQLAndMPLevel(ctx, &gameDfPb.UpdateAllOutBattleSQLAndMPLevelReq{
				UpdateParamBasicItem: updateParamBasicItem,
				Level:                int64(oneSendData.Num),
			})
			if errS != nil {
				success = false
				oneSendData.SendStatus = SendStatus_Fail
			} else {
				oneSendData.SendStatus = SendStatus_Success
			}
		// 更新烽火地带等级
		case SendPresentType_UpdateSOLLevel:
			_, errS := gameDFProxy.UpdateFireActionSQLAndMPLevel(ctx, &gameDfPb.UpdateFireActionSQLAndMPLevelReq{
				UpdateParamBasicItem: updateParamBasicItem,
				Level:                int64(oneSendData.Num),
			})
			if errS != nil {
				success = false
				oneSendData.SendStatus = SendStatus_Fail
			} else {
				oneSendData.SendStatus = SendStatus_Success
			}
		case SendPresentType_SubscribeOrUpgradeGarenaBattlePass:
			// 订阅/升级GarenaBattlePass
			_, errS := gameDFProxy.SubscribeOrUpgradeGarenaBattlePass(ctx, &gameDfPb.SubscribeOrUpgradeGarenaBattlePassReq{
				Openid:  userData.GaOpeinid,
				AreaId:  gaAreaID,
				BuyType: cast.ToInt32(oneSendData.Num),
				Serial:  oneSendData.Seq,
			})
			if errS != nil {
				success = false
				oneSendData.SendStatus = SendStatus_Fail
			} else {
				oneSendData.SendStatus = SendStatus_Success
			}
		case SendPresentType_CalculateBattlePassExperienceProgress:
			_, errS := gameDFProxy.CalculateBattlePassExperienceProgress(ctx, &gameDfPb.CalculateBattlePassExperienceProgressReq{
				Openid: userData.GaOpeinid,
				AreaId: gaAreaID,
				Level:  cast.ToInt32(oneSendData.Num),
				Serial: oneSendData.Seq,
			})
			if errS != nil {
				success = false
				oneSendData.SendStatus = SendStatus_Fail
			} else {
				oneSendData.SendStatus = SendStatus_Success
			}

		}
		userSendData.List = append(userSendData.List, oneSendData)

	}
	if success {
		sendStatus = SendStatus_Success
	} else {
		sendStatus = SendStatus_Fail
	}
	return
}

// 获取下周四时间
func GetTransDate() string {
	// 获取当前时间
	now := time.Now()

	// 计算今天是星期几
	weekday := now.Weekday()
	if weekday == time.Sunday {
		weekday = 7
	}
	// 计算距离下周四的天数
	daysUntilNextThursday := (time.Thursday - weekday + 7)
	// if daysUntilNextThursday == 0 {
	// 	daysUntilNextThursday = 7 // 如果今天是星期四，则下周四是 7 天后
	// }

	// 获取下周四的日期
	nextThursday := now.AddDate(0, 0, int(daysUntilNextThursday))

	// 格式化为 YYYYMMDD
	formattedDate := nextThursday.Format("********")

	return formattedDate
}
func GetTransDateTimestamp(date string) int64 {
	transDate, _ := time.Parse("********", date)
	return transDate.Unix()
}
func GetTransDateLeftTimestamp(date string) int64 {
	transDate, _ := time.Parse("********", date)
	return transDate.Unix() - time.Now().Unix()
}
func GetTransDateFinishTimestamp(date string) int64 {
	transDate, _ := time.Parse("********", date)
	return transDate.Unix() + 86400*6
}

func GetIntValueFormatFromList(ctx context.Context, list []*gameDfPb.IDIPReturnValueDetails, key string) (val int32, err error) {
	for _, v := range list {
		if v.Parameter == key {
			// TODO 报错
			val = cast.ToInt32(v.Value)
			return
		}
	}
	err = errs.NewCustomError(ctx, code.DFTransferAccountNotMatchKey, "not found key %v", key)
	return
}
func GetStrValueFormatFromList(ctx context.Context, list []*gameDfPb.IDIPReturnValueDetails, key string) (val string, err error) {
	for _, v := range list {
		if v.Parameter == key {
			val = v.Value
			return
		}
	}
	err = errs.NewCustomError(ctx, code.DFTransferAccountNotMatchKey, "not found key %v", key)
	return
}

func GetRspArray(ctx context.Context, rspstr string) (arrList [][]*gameDfPb.IDIPReturnValueDetails, err error) {
	arrList = make([][]*gameDfPb.IDIPReturnValueDetails, 0)
	errJ := json.Unmarshal([]byte(rspstr), &arrList)
	if errJ != nil {
		err = errs.NewCustomError(ctx, code.DFTransferAccountNotMatchKey, "rsp not match %v", errJ)
		return
	}
	return
}

func CommercialSkinArrayEmailList(ctx context.Context, openid string, emailKey string, fileName string, parameter string, parameterType string, description string, arrMap map[string]int32) (emailList []model.OneEmailData) {
	for key, _ := range arrMap {

		emailList = append(emailList, model.OneEmailData{
			Key:      emailKey,
			FileName: fileName,
			Openid:   openid,
			InfoList: []*gameDfPb.IDIPReturnValueDetails{
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   parameter,
					Type:        parameterType,
					Description: description,
					Value:       key,
				},
			},
		})
	}
	return
}
func SortUserData(openid string, gaOpenid string, sendData model.UserSendData) (moreEmailData model.UserEmailData, sortSendData model.UserSendData) {
	moreEmailData.List = make([]model.OneEmailData, 0)
	// 对 sendData 的List进行 Priority字段 排序
	sort.Slice(sendData.List, func(i, j int) bool {
		return sendData.List[i].Priority < sendData.List[j].Priority
	})
	sortSendData = sendData
	// sortEmailData = emailData
	var sendNum int32 = 0
	for key, list := range sortSendData.List {
		infoList := make([]*gameDfPb.IDIPReturnValueDetails, 0)
		// 非AMS礼包不计数
		if list.Type != SendPresentType_AMS {
			continue
		}
		sendNum++
		if sendNum > EmailHasExcelMaxNumNum {
			sortSendData.List[key].SendStatus = SendStatus_Email_Max
			// "Rarity":         rarity,
			// "Wear":           wear,
			// "UniqueNo":       uniqueNo,
			// "KillCnter":      killCnter,
			// "CustomName":     customName,
			// "AppearanceId":   appearanceId,
			// "AppearanceSeed": appearanceSeed,
			// "MarketBuyTime":      marketBuyTime,
			// "SourceType":         sourceType,
			// "SuitActive":         suitActive,
			// "ShowType":           showType,
			// "ShowRarity":         showRarity,
			// "ShowAppearanceId":   showAppearanceId,
			// "ShowAppearanceSeed": showAppearanceSeed,
			infoList = append(infoList,
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ItemId",
					Type:        "string",
					Description: "道具ID",
					Value:       list.ItemID,
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ItemName",
					Type:        "int32",
					Description: "道具名称",
					Value:       list.ItemName,
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Num",
					Type:        "int32",
					Description: "道具数量",
					Value:       cast.ToString(list.Num),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Rarity",
					Type:        "uint64",
					Description: "稀有度：优品、极品",
					Value:       cast.ToString(list.MoreInfo["Rarity"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "Wear",
					Type:        "int64",
					Description: "磨损度",
					Value:       cast.ToString(list.MoreInfo["Wear"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "UniqueNo",
					Type:        "uint64",
					Description: "专属编号",
					Value:       cast.ToString(list.MoreInfo["UniqueNo"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "CustomName",
					Type:        "string",
					Description: "自定义名字",
					Value:       cast.ToString(list.MoreInfo["CustomName"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceId",
					Type:        "uint64",
					Description: "外观方案id",
					Value:       cast.ToString(list.MoreInfo["AppearanceId"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "AppearanceSeed",
					Type:        "uint64",
					Description: "外观随机种子",
					Value:       cast.ToString(list.MoreInfo["AppearanceSeed"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "MarketBuyTime",
					Type:        "int64",
					Description: "从矩阵市场购买得到的典藏挂饰时间",
					Value:       cast.ToString(list.MoreInfo["MarketBuyTime"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "SourceType",
					Type:        "int32",
					Description: "典藏挂饰来源类型",
					Value:       cast.ToString(list.MoreInfo["SourceType"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "SuitActive",
					Type:        "int32",
					Description: "对应套装是否已集齐激活",
					Value:       cast.ToString(list.MoreInfo["SuitActive"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowType",
					Type:        "int32",
					Description: "展示条目类型",
					Value:       cast.ToString(list.MoreInfo["ShowType"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowRarity",
					Type:        "int32",
					Description: "展示条目稀有度",
					Value:       cast.ToString(list.MoreInfo["ShowRarity"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowAppearanceId",
					Type:        "uint64",
					Description: "展示条目外观方案id",
					Value:       cast.ToString(list.MoreInfo["ShowAppearanceId"]),
				},
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   "ShowAppearanceSeed",
					Type:        "uint64",
					Description: "展示条目外观随机种子",
					Value:       cast.ToString(list.MoreInfo["ShowAppearanceSeed"]),
				},
			)
			moreEmailData.List = append(moreEmailData.List, model.OneEmailData{
				Key:      "email_max_info",
				FileName: fmt.Sprintf("超过%d封邮件信息", EmailHasExcelMaxNumNum),
				Openid:   openid,
				InfoList: infoList,
			})
		}
	}

	return
}
func GetArrayEmailListByArray(ctx context.Context, openid string, emailKey string, fileName string, arr [][]*gameDfPb.IDIPReturnValueDetails) (emailList []model.OneEmailData) {
	for _, infoList := range arr {
		emailList = append(emailList, model.OneEmailData{
			Key:      emailKey,
			FileName: fileName,
			Openid:   openid,
			InfoList: infoList,
		})
	}
	return
}

func GetCommercialSkinSendList(ctx context.Context, openid string, arr [][]*gameDfPb.IDIPReturnValueDetails) (sendList []model.OneSendData) {

	sendList = make([]model.OneSendData, 0)
	for _, infoList := range arr {
		// 计算数量
		// addSendList := true
		var itemID string
		var rarity string
		var wear string
		var uniqueNo string
		var killCnter string
		var customName string
		var appearanceId string
		var appearanceSeed string
		itemNum := int32(1)
		for _, info := range infoList {
			if info.Parameter == "PropID" {
				itemID = info.Value
			}
			if info.Parameter == "Rarity" {
				rarity = info.Value
			}
			if info.Parameter == "Wear" {
				wear = info.Value
			}
			if info.Parameter == "UniqueNo" {
				uniqueNo = info.Value
			}
			if info.Parameter == "KillCnter" {
				killCnter = info.Value
			}
			if info.Parameter == "CustomName" {
				customName = info.Value
			}
			if info.Parameter == "AppearanceId" {
				appearanceId = info.Value
			}
			if info.Parameter == "AppearanceSeed" {
				appearanceSeed = info.Value
			}

		}
		if exists, _ := object.InArray(itemID, ItemCommercialSkinList); !exists {
			continue
		}
		// 发货
		if configData, hasData := ItemToAmsData[itemID]; hasData {
			sendList = append(sendList, model.OneSendData{
				Type: SendPresentType_AMS,
				Num:  itemNum,
				// Seq:      GetGaAmsSerial(),
				AmsId:    configData.AmsID,
				GroupId:  configData.AmsGroupID,
				Priority: configData.Priority,
				ItemName: configData.ItemName,
				ItemID:   itemID,
				MoreInfo: map[string]string{
					"Rarity":         rarity,
					"Wear":           wear,
					"UniqueNo":       uniqueNo,
					"KillCnter":      killCnter,
					"CustomName":     customName,
					"AppearanceId":   appearanceId,
					"AppearanceSeed": appearanceSeed,
				},
			})
		}

	}
	return
}

func GetOperatorCollectiblesSendList(ctx context.Context, openid string, arr [][]*gameDfPb.IDIPReturnValueDetails) (sendList []model.OneSendData) {
	// sendMap := make(map[string]model.OneSendData)
	sendList = make([]model.OneSendData, 0)
	for _, infoList := range arr {
		// 计算数量
		// addSendList := true
		var itemID string
		var rarity string
		var wear string
		var uniqueNo string
		var killCnter string
		var customName string
		var appearanceId string
		var appearanceSeed string
		var marketBuyTime string
		var sourceType string
		var suitActive string
		var showType string
		var showRarity string
		var showAppearanceId string
		var showAppearanceSeed string
		itemNum := int32(1)
		for _, info := range infoList {
			if info.Parameter == "PropID" {
				itemID = info.Value
			}
			if info.Parameter == "Rarity" {
				rarity = info.Value
			}
			if info.Parameter == "Wear" {
				wear = info.Value
			}
			if info.Parameter == "UniqueNo" {
				uniqueNo = info.Value
			}
			if info.Parameter == "KillCnter" {
				killCnter = info.Value
			}
			if info.Parameter == "CustomName" {
				customName = info.Value
			}
			if info.Parameter == "AppearanceId" {
				appearanceId = info.Value
			}
			if info.Parameter == "AppearanceSeed" {
				appearanceSeed = info.Value
			}
			if info.Parameter == "MarketBuyTime" {
				marketBuyTime = info.Value
			}
			if info.Parameter == "SourceType" {
				sourceType = info.Value
			}
			if info.Parameter == "SuitActive" {
				suitActive = info.Value
			}
			if info.Parameter == "ShowType" {
				showType = info.Value
			}
			if info.Parameter == "SuitActive" {
				suitActive = info.Value
			}
			if info.Parameter == "ShowRarity" {
				showRarity = info.Value
			}
			if info.Parameter == "ShowAppearanceId" {
				showAppearanceId = info.Value
			}
			if info.Parameter == "ShowAppearanceSeed" {
				showAppearanceSeed = info.Value
			}
		}
		// 发货
		if exists, _ := object.InArray(itemID, ItemCollectiblesList); !exists {
			continue
		}

		if configData, hasData := ItemToAmsData[itemID]; hasData {
			sendList = append(sendList, model.OneSendData{
				Type: SendPresentType_AMS,
				Num:  itemNum,
				// Seq:      GetGaAmsSerial(),
				AmsId:    configData.AmsID,
				GroupId:  configData.AmsGroupID,
				Priority: configData.Priority,
				ItemName: configData.ItemName,
				ItemID:   itemID,
				MoreInfo: map[string]string{
					"Rarity":             rarity,
					"Wear":               wear,
					"UniqueNo":           uniqueNo,
					"KillCnter":          killCnter,
					"CustomName":         customName,
					"AppearanceId":       appearanceId,
					"AppearanceSeed":     appearanceSeed,
					"MarketBuyTime":      marketBuyTime,
					"SourceType":         sourceType,
					"SuitActive":         suitActive,
					"ShowType":           showType,
					"ShowRarity":         showRarity,
					"ShowAppearanceId":   showAppearanceId,
					"ShowAppearanceSeed": showAppearanceSeed,
				},
			})
		}

	}
	// fmt.Println("---------------sendMap---------------")
	// // fmt.Printf("%#v\n", sendMap)
	// sendList = make([]model.OneSendData, 0)
	// for _, v := range sendMap {
	// 	sendList = append(sendList, v)
	// }

	return
}
func GetOperatorSendList(ctx context.Context, openid string, arr [][]*gameDfPb.IDIPReturnValueDetails) (sendList []model.OneSendData) {
	sendMap := make(map[string]model.OneSendData)
	for _, infoList := range arr {
		// 计算数量
		addSendList := true
		var itemID string
		itemNum := int32(1)
		for _, info := range infoList {
			if info.Parameter == "ItemId" {
				itemID = info.Value
			}
		}
		// checkList := make([]string, 0)
		// checkList = append(checkList, ItemCollectionHorizonList...)
		// checkList = append(checkList, ItemOperatorList...)
		// 发货
		if exists, _ := object.InArray(itemID, ItemOperatorList); !exists {
			continue
		}
		if addSendList {
			_, ok := sendMap[itemID]
			if ok {
				data := sendMap[itemID]
				data.Num += itemNum
				sendMap[itemID] = data
			} else {
				if configData, hasData := ItemToAmsData[itemID]; hasData {
					sendMap[itemID] = model.OneSendData{
						Type: SendPresentType_AMS,
						Num:  itemNum,
						// Seq:      GetGaAmsSerial(),
						AmsId:    configData.AmsID,
						GroupId:  configData.AmsGroupID,
						Priority: configData.Priority,
						ItemName: configData.ItemName,
						ItemID:   itemID,
					}
				}
			}
		}
	}
	sendList = make([]model.OneSendData, 0)
	for _, v := range sendMap {
		sendList = append(sendList, v)
	}

	return
}

func GetCollectionHorizonSendList(ctx context.Context, openid string, arr [][]*gameDfPb.IDIPReturnValueDetails) (sendList []model.OneSendData) {
	sendMap := make(map[string]model.OneSendData)
	for _, infoList := range arr {
		// 计算数量
		addSendList := true
		var itemID string
		var itemNum int32
		for _, info := range infoList {
			if info.Parameter == "ItemId" {
				itemID = info.Value
			}
			if info.Parameter == "ItemNum" {
				itemNum = cast.ToInt32(info.Value)
			}
			// if info.Parameter == "IsCollectibles" && info.Value == "1" {
			// 	addSendList = false
			// }
		}
		// 发货
		if exists, _ := object.InArray(itemID, ItemCollectionHorizonList); !exists {
			continue
		}
		if addSendList {
			_, ok := sendMap[itemID]
			if ok {
				data := sendMap[itemID]
				data.Num += itemNum
				sendMap[itemID] = data
			} else {
				if configData, hasData := ItemToAmsData[itemID]; hasData {
					sendMap[itemID] = model.OneSendData{
						Type: SendPresentType_AMS,
						Num:  itemNum,
						// Seq:      GetGaAmsSerial(),
						AmsId:    configData.AmsID,
						GroupId:  configData.AmsGroupID,
						Priority: configData.Priority,
						ItemName: configData.ItemName,
						ItemID:   itemID,
					}
				}
			}
		}
	}
	sendList = make([]model.OneSendData, 0)
	for _, v := range sendMap {
		sendList = append(sendList, v)
	}

	sendByte, _ := json.Marshal(sendList)
	fmt.Println("---------------sendList---------------")
	fmt.Printf("%#v\n", string(sendByte))
	return
}

func GetArrayEmailList(ctx context.Context, openid string, emailKey string, fileName string, parameter string, parameterType string, description string, arrMap map[string]int32) (emailList []model.OneEmailData) {
	for key, val := range arrMap {
		if val == 0 {
			continue
		}
		emailList = append(emailList, model.OneEmailData{
			Key:      emailKey,
			FileName: fileName,
			Openid:   openid,
			InfoList: []*gameDfPb.IDIPReturnValueDetails{
				&gameDfPb.IDIPReturnValueDetails{
					Parameter:   parameter,
					Type:        parameterType,
					Description: description,
					Value:       fmt.Sprintf("%v:%v", key, val),
				},
			},
		})
	}
	return
}

// func GetArraySendList(ctx context.Context, openid string, arrMap map[string]int32) (sendList []model.OneSendData) {
// 	for itemID, val := range arrMap {
// 		if val == 0 {
// 			continue
// 		}
// 		itemData, ok := ItemToAmsData[itemID]
// 		if ok && val > 0 {
// 			sendList = append(sendList, model.OneSendData{
// 				Type:     SendPresentType_AMS,
// 				Num:      val,
// Seq:      GetGaAmsSerial(),
// 				AmsId:    GA_AMSID,
// 				GroupId:  itemData.AmsGroupID,
// 				Priority: itemData.Priority,
// 			})
// 		}
// 	}

// 	return
// }

func GetRspMap(ctx context.Context, rspstr string) (arrMap map[string]int32, err error) {
	arrList := make([][]*gameDfPb.IDIPReturnValueDetails, 0)

	errJ := json.Unmarshal([]byte(rspstr), &arrList)
	if errJ != nil {
		err = errs.NewCustomError(ctx, code.DFTransferAccountNotMatchKey, "rsp not match %v", errJ)
		return
	}
	arrMap = make(map[string]int32)
	for _, v := range arrList {
		var itemKey string
		var itemValue string
		for _, v2 := range v {
			if v2.Parameter == "ItemId" {
				itemKey = v2.Value
			}
			if v2.Parameter == "ItemNum" {
				itemValue = v2.Value
			}
			arrMap[itemKey] = cast.ToInt32(itemValue)
		}
	}
	return
}
func GetCommercialSkinRspMap(ctx context.Context, rspstr string) (arrMap map[string]int32, emailArrMap map[string]int32, err error) {
	arrList := make([][]*gameDfPb.IDIPReturnValueDetails, 0)

	errJ := json.Unmarshal([]byte(rspstr), &arrList)
	if errJ != nil {
		err = errs.NewCustomError(ctx, code.DFTransferAccountNotMatchKey, "rsp not match %v", errJ)
		return
	}
	arrMap = make(map[string]int32)
	emailArrMap = make(map[string]int32)
	for _, v := range arrList {
		var itemKey string
		var itemRarity string
		fmt.Println("---------------v---------------")
		fmt.Printf("%#v\n", v)
		for _, v2 := range v {
			if v2.Parameter == "PropID" {
				itemKey = v2.Value
			}
			if v2.Parameter == "Rarity" {
				itemRarity = v2.Value
			}
			arrMap[itemKey] = 1
			emailArrMap[fmt.Sprintf("%s:%s", itemKey, itemRarity)] = 1
		}
	}
	fmt.Println("---------------emailArrMap---------------")
	fmt.Printf("%#v\n", emailArrMap)
	return
}
func ConfigureExpCoin(level int32) (expCoin int32) {
	// 	0-10级		高级武器经验币	20
	// 11-20级		高级武器经验币	40
	// 21-30级		高级武器经验币	60
	// 31-40级		高级武器经验币	80
	// 41-50级		高级武器经验币	100
	if level <= 10 {
		return 20
	} else if level < 20 {
		return 40
	} else if level < 30 {
		return 60
	} else if level < 40 {
		return 80
	} else {
		return 100
	}
}
