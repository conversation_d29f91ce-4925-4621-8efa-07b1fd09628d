# PA-NIKKE独立站服务

## 一、项目说明

### 1. 项目介绍

PA-NIKKE独立站服务，提供以下能力：
- （1）发布动态：图文、富文本、视频（youtube、tiktok）
- （2）浏览游戏Creatorhub、官网Event等
- （3）LIP积分服务
- （4）NIKKE小工具

### 2. 快速体验

| 环境 | C 端              | 
| ---- | ----------------------------------------- | 
| 测试 | <> |
| 预发布 | <>  | 
| 正式 | <>      | 

### 3. 技术选型

- 开发语言：GO
- 开发框架：[Trpc-GO](https://iwiki.woa.com/p/279550562)
- 代码扫描：[Codedog]()

## 二、 开始

这些说明将指导你如何在本地机器上获取和运行该项目的副本。请阅读进一步了解如何部署该项目到现场系统。

### 1.环境

在开始之前，你需要安装哪些软件以及如何安装它们。例如：

- Go 1.18+以上
- MySQL (5.7+)
- Redis

### 2.安装

按照以下步骤安装和设置项目：

1. 克隆此仓库

```sh
git clone https://git.woa.com/iegg_distribution/Standalone-Site/standalone-site-backend.git
```

2. 安装依赖

```sh
go mod download git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics
go mod tidy

```

3. 需要到测试环境导出db，然后导入到本地mysql数据库
```
mysqldump -h{{数据库ip}} -u{{数据库用户名}} -p{{数据库密码}} -P{{数据库端口}} db_cnbot --skip-lock-table --tables --default-character-set=utf8 > db_cnbot_test.sql

mysql -h{{数据库ip}} -u{{数据库用户名}} -p{{数据库密码}} -P{{数据库端口}} --default-character-set=utf8mb4 db_cnbot < /root/db_cnbot_test.sql
```

4. 拷贝服务配置文件
测试环境配置文件在：
将配置文件拷贝到代码根目录下的conf目录下
包括：
|- conf
    |- client
         |- client.yaml
    |- config
         |- custom.yaml
         |- trpc_go.yaml
    |- database
         |- database.yaml


### 3. 运行

1. 执行命令

```sh
go run main.go
```

2. 本地调试接口如：
http://localhost:9001/trpc.publishing_application.standalonesite.HttpOperations/CreateUserInfo

## 三、开发

### 1. 分支说明

常驻分支：

- demo：测试分支
- master： 生产分支

分支操作指引：
- 开发： 切 `demo` 分支
- 测试：切 `demo` 分支
- 新需求：从 `demo` 创建 `feat/xxx`分支
- 热修复：从 `master` 创建 `fix/xxx` 分支

### 2. 开发规范

参见：<https://iwiki.woa.com/p/4006766683>

### 3. 开发指引

提供开发方面的操作指引，包括特殊的环境，必须说明的部署等等

## 四、 发布流程

### 测试环境

1. 推送 demo 分支：`git push origin demo`
2. 自动触发流水线：[【TEST】lip-points-svr服务](https://devops.woa.com/console/pipeline/wgwebglobal/p-485f638f09484ed084cdc948c7368224/history)

### 预发布环境

1. 合并 test 到 master，推送 master 分支：`git push origin master`
2. 自动触发流水线：[【PRE-LIP】lip-points-svr服务](https://devops.woa.com/console/pipeline/wgwebglobal/p-ad047fa5755e4cc6b8c42bd7236150b4/history)


### 正式环境

1. 合并 test 到 master
2. 给 master 打 Tag：`v{version}`，这里注意每次打的tag一定是比上一次版本大，如上一次是v0.0.1，这一次就需要v0.0.2。
3. 手动执行流水线：[【PROD-LIP】lip-points-svr服务](https://devops.woa.com/console/pipeline/wgwebglobal/p-3555d589e5b6441eb58874be64f7d958/history)
4. 更新版本文档[LIP-UGC动态社区服务版本](https://doc.weixin.qq.com/sheet/e3_AAcASAYHACkEJh49dWASm008YuADf?scode=AJEAIQdfAAoA9m6R3oAAcASAYHACk&tab=BB08J2&version=4.1.22.6014&platform=win)