package tweet

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	gamePB "git.woa.com/trpcprotocol/publishing_application/stand_alone_site_game_game"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	redisClient "github.com/go-redis/redis/v8"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/logic/plate"
	"trpc.publishing_application.standalonesite/app/logic/privacy"
	"trpc.publishing_application.standalonesite/app/logic/user"
	userService "trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

func GetPostInfo(c context.Context, intlOpenid, postUUID string, language string, isAllLanguage int32) (*pb.GetPostRsp, error) {
	// 先获取缓存数据
	var postFormatted *pb.GetPostRsp
	// postBasesRedisKey := cache.GetPostInfoKeyWithIntlOpenid(intlOpenid, postUUID, language)
	postBasesRedisKey := cache.GetPostInfoKey(postUUID, language)
	postBasesCacheInfo, err := redis.GetClient().Get(c, postBasesRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postBasesCacheInfo), &postFormatted)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo post info cache json.Unmarshal error.postBasesRedisKey: %s, err: %v", postBasesRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetPostDetailJsonUnmarshalError, "Failed to obtain post info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo postBases redis err: %v", err)
		}
		postItem, err := dao.GetPost(postUUID)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetPostsFailed, "GetPostInfo | Failed to get dynamic details")
		}
		postFormatted = &pb.GetPostRsp{
			PostUuid:        postItem.PostUUID,
			IntlOpenid:      postItem.IntlOpenid,
			Type:            postItem.Type,
			IsTop:           postItem.IsTop,
			TopSort:         postItem.TopSort,
			TopOn:           postItem.TopOn,
			LatestRepliedOn: postItem.LatestRepliedOn,
			GameId:          postItem.GameId,
			AreaId:          postItem.AreaId,
			CreatedOn:       postItem.CreatedOn,
			MyUpvote:        &pb.MyUpvote{},
			PlateId:         postItem.PlateID,
			Language:        postItem.Language,
			IsAudit:         postItem.IsAudit,
			PublishOn:       int32(postItem.PublishOn),
			IsOfficial:      postItem.IsOfficial,
			FriendCard:      &pb.FriendCard{},
			GuildCard:       &pb.GuildCard{},
			CreatedOnMs:     postItem.CreatedOnMs,
			IsHide:          postItem.IsHide,
		}

		var postContent *model.PostContent
		postContentList, err := dao.GetPostContentList(postItem.PostUUID, false)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo err, post_uuid:(%s), err=(%v)", postItem.PostUUID, err)
		} else {
			var isExistLanguage bool
			for _, content := range postContentList {
				if content.Language == language {
					postContent = content
					isExistLanguage = true
					break
				}
			}
			if !isExistLanguage {
				// 直接取最靠前的一条数据，从数据库拿出来都是排好序的，第一条就是兜底语言
				postContent = postContentList[0]
			}
			postFormatted.Language = postContent.Language
			postFormatted.IsEssence = postContent.IsEssence
			postFormatted.EssenceOn = postContent.EssenceOn
			postFormatted.IsOriginal = postContent.IsOriginal
			postFormatted.OriginalUrl = postContent.OriginalURL
			postFormatted.OriginalReprint = postContent.OriginalReprint
			postFormatted.Title = postContent.Title
			postFormatted.Content = postContent.Content
			postFormatted.CreatorStatementType = int32(postContent.CreatorStatementType)
			postFormatted.AiContentType = int32(postContent.AiContentType)
			postFormatted.RiskRemindType = int32(postContent.RiskRemindType)
			//postFormatted.PicUrls = strings.Split(postContent.PicUrls, ",")
			postFormatted.ExtInfo = postContent.ExtInfo
			if postItem.Type == constants.POST_TYPE_RICH_TEXT {
				postFormatted.ContentSummary = postContent.ContentSummary
			} else {
				postFormatted.ContentSummary = postContent.Content
			}
			if len(postContent.PicUrls) > 0 {
				postFormatted.PicUrls = strings.Split(postContent.PicUrls, ",")
			}
		}

		if postItem.IsOfficial == 1 {
			// 屏蔽举报
			postFormatted.CanReport = false
		}

		// 处理图片链接，去除为空的
		handlerPostImages(postFormatted)
		// 获取好友卡信息
		if postContent.FriendCardInfo != "" {
			var roleInfo *gamePb.RoleInfo
			err = json.Unmarshal([]byte(postContent.FriendCardInfo), &roleInfo)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo json.Unmarshal FriendCardInfo err: %v", err)
			} else {
				userGamePlayerInfo, err := user.GetOtherUserNikkeBasicInfo(c, postContent.IntlOpenid, "", roleInfo.AreaId, roleInfo.RoleId)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo GetOtherUserNikkeBasicInfo err: %v", err)
				} else {
					postFormatted.FriendCard.ShowFriendCard = true
					postFormatted.FriendCard.RoleName = userGamePlayerInfo.RoleName
					postFormatted.FriendCard.AreaId = userGamePlayerInfo.AreaId
					postFormatted.FriendCard.Icon = userGamePlayerInfo.Icon
					postFormatted.FriendCard.PlayerLevel = userGamePlayerInfo.PlayerLevel
					postFormatted.FriendCard.TeamCombat = userGamePlayerInfo.TeamCombat
				}
			}
		}
		// 获取公会卡片信息
		if postContent.GuildId != "" {
			guildInfoList := strings.Split(postContent.GuildId, "_")
			if len(guildInfoList) == 2 {
				guildId := guildInfoList[0]
				nikkeAreaIdStr := guildInfoList[1]
				nikkeAreaIdI32, err := strconv.ParseInt(nikkeAreaIdStr, 10, 32)
				if err != nil {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo nikkeAreaId ParseInt error,postContent.GuildId:%v, err: %v", postContent.GuildId, err)
				} else {
					req := &gamePB.GetGuildDetailInnerReq{
						NikkeAreaId: int32(nikkeAreaIdI32),
						GuildId:     guildId,
					}
					userGuildInfo, err := gameProxy.GetGuildDetailInner(c, req)
					if err != nil {
						log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo GetGuildDetailInner error,req:%v, err: %v", req, err)
					} else {
						if userGuildInfo.GuildDetail != nil {
							postFormatted.GuildCard.ShowGuildCard = true
							postFormatted.GuildCard.NikkeAreaId = userGuildInfo.GuildDetail.NikkeAreaId
							postFormatted.GuildCard.GuildId = userGuildInfo.GuildDetail.GuildId
							postFormatted.GuildCard.GuildName = userGuildInfo.GuildDetail.GuildName
							postFormatted.GuildCard.GuildDescription = userGuildInfo.GuildDetail.GuildDescription
							postFormatted.GuildCard.GuildIcon = userGuildInfo.GuildDetail.GuildIcon
							postFormatted.GuildCard.GuildLevel = userGuildInfo.GuildDetail.GuildLevel
							postFormatted.GuildCard.GuildRank = userGuildInfo.GuildDetail.GuildRank
							postFormatted.GuildCard.GuildActivity = userGuildInfo.GuildDetail.GuildActivity
							postFormatted.GuildCard.GuildLocale = userGuildInfo.GuildDetail.GuildLocale
							postFormatted.GuildCard.GuildJoinType = userGuildInfo.GuildDetail.GuildJoinType
							postFormatted.GuildCard.GuildEntryLevel = userGuildInfo.GuildDetail.GuildEntryLevel
							postFormatted.GuildCard.GuildCardUuid = userGuildInfo.GuildDetail.GuildCardUuid
						}
					}
				}
			} else {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo postContent.GuildId error,postContent.GuildId: %v", postContent.GuildId)
			}
		}
		// 获取动态关联的话题
		info, err := getPostTagInfo(c, postFormatted.PostUuid, language)
		if err == nil {
			postFormatted.Tags = info
		}
		postDetailByte, err := json.Marshal(postFormatted)
		if err == nil {
			redis.GetClient().SetEX(c, postBasesRedisKey, string(postDetailByte), 2*time.Minute).Result()
			if intlOpenid != "" {
				keysKey := cache.GetUserPostCacheKeysKey(intlOpenid)
				redis.GetClient().SAdd(c, keysKey, postBasesRedisKey).Result()
			}
		}
	}

	// 是否需要返回全部语言
	if isAllLanguage == 1 {
		// 这个是有兜底语言的语言,可以不考虑删除某条数据的问题
		getPostContentAllLanguageFormatted(postFormatted.PostUuid, postFormatted)
	}
	if intlOpenid != postFormatted.IntlOpenid {
		if postFormatted.IsAudit == 2 {
			return nil, errs.NewCustomError(c, code.GetPostsFailed, "GetPostInfo | Failed to obtain dynamics, the audit status cannot be viewed")
		}
		userPrivacySwitch, err := privacy.GetUserPrivacySwitch(c, postFormatted.IntlOpenid)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo GetUserPrivacySwitch err: %v\n", err)
		} else {
			postFormatted.FriendCard.ShowFriendCardDetail = userPrivacySwitch.ShowMyGameCard == 1
		}
	} else {
		postFormatted.FriendCard.ShowFriendCardDetail = true
		// 如果是帖子的发布者查看，则需要查是否有待审核的数据
		postAuditData, err := dao.GetPostEditNotAuditListByPostUUID(postFormatted.PostUuid)
		if err == nil && postAuditData.Model != nil && postAuditData.ID > 0 {
			postFormatted.Type = postAuditData.Type
			postFormatted.Platform = postAuditData.Platform
			postFormatted.Language = postAuditData.Language
			postFormatted.Title = postAuditData.Title
			postFormatted.Content = postAuditData.Content
			postFormatted.ContentSummary = postAuditData.ContentSummary
			if len(postAuditData.PicUrls) > 0 {
				postFormatted.PicUrls = strings.Split(postAuditData.PicUrls, ",")
			}
			postFormatted.ExtInfo = postAuditData.ExtInfo
			if isAllLanguage == 1 {
				for _, contentLanguageItem := range postFormatted.ContentLanguages {
					contentLanguageItem.Platform = postAuditData.Platform
					contentLanguageItem.Language = postAuditData.Language
					contentLanguageItem.Title = postAuditData.Title
					contentLanguageItem.Content = postAuditData.Content
					contentLanguageItem.ContentSummary = postAuditData.ContentSummary
					if len(postAuditData.PicUrls) > 0 {
						contentLanguageItem.PicUrls = strings.Split(postAuditData.PicUrls, ",")
					}
					contentLanguageItem.ExtInfo = postAuditData.ExtInfo
				}
			}
		}
	}
	// 判断好友卡是否可以发起请求
	if postFormatted.FriendCard.ShowFriendCard && intlOpenid != postFormatted.IntlOpenid {
		limitSendNikkeFriendRequestKey := cache.GetSendNikkeFriendRequestKey(intlOpenid, postUUID)
		exists, err := redis.GetClient().Exists(c, limitSendNikkeFriendRequestKey).Result()
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo redis.GetClient().Exists err, limitSendNikkeFriendRequestKey:(%s), err=(%v)", limitSendNikkeFriendRequestKey, err)
		} else {
			postFormatted.FriendCard.IsSendFriendRequest = exists == 1
		}
	}
	// 获取统计数据
	postStats, err := dao.GetPostStatsByPostUuid(postUUID)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo err by get post stats, post_uuid:(%s), err=(%v)", postUUID, err)
	} else {
		upvoteMap := make(map[int64]int64)
		if postStats.UpvoteMap != "" {
			err := json.Unmarshal([]byte(postStats.UpvoteMap), &upvoteMap)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo UpvoteMap json.Unmarshal err: %v, postUUID is %s", err, postUUID)
			}
		}
		postFormatted.PowerNum = postStats.PowerNumFloat
		postFormatted.CommentCount = postStats.CommentCount
		postFormatted.CollectionCount = postStats.CollectionCount
		postFormatted.UpvoteCount = postStats.UpvoteCount
		postFormatted.UpvoteMap = upvoteMap
		postFormatted.BrowseCount = postStats.BrowseCount
		postFormatted.ForwardCount = int32(postStats.ForwardCount)
	}
	// 判断当前用户是否是管理员
	userRedisKey := cache.GetAdminUserKey(1)
	isIamAdmin, _ := redis.GetClient().SIsMember(c, userRedisKey, intlOpenid).Result()
	// 重新判断用户权限,每个用户的权限不是一样的，所以要把这个用户权限单独放到最外层，不可和帖子信息一并缓存
	// 如果值不存在，查询用户标签值
	userInfo, err := user.GetUserDetailInfoByOpenid(c, intlOpenid, postFormatted.IntlOpenid, language)
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostInfo GetUserDetailInfoByOpenid err, intl_openid:(%s), err=(%v)", postFormatted.IntlOpenid, err)
	} else {
		postFormatted.User = userInfo
		// 判断当前官方帖子是否可以编辑， 只有是自己的帖子，并且当前用户是官方认证和机构认证的才行
		// postFormatted.CanEdit = (intlOpenid == userInfo.IntlOpenid && (postFormatted.User.AuthType == 1 || postFormatted.User.AuthType == 3))
		// postFormatted.CanEdit = (intlOpenid == userInfo.IntlOpenid || (isIamAdmin && postFormatted.IsOfficial == 0))
		postFormatted.CanEdit = intlOpenid == userInfo.IntlOpenid
	}

	if intlOpenid != "" {
		if intlOpenid == postFormatted.IntlOpenid {
			// 如果是我自己发的帖子
			postFormatted.CanDelete = true
		} else {

			// 当前用户是管理员且帖子为普通贴才能删除
			postFormatted.CanDelete = isIamAdmin && postFormatted.IsOfficial == 0
		}
	}
	postFormatted.CanReport = (intlOpenid != postFormatted.IntlOpenid && postFormatted.IsOfficial != 1)
	if userInfo != nil {
		postFormatted.IsMine = intlOpenid == postFormatted.IntlOpenid
	}

	postFormatted.CanUpdateTags = (postFormatted.IsOfficial != 1 && isIamAdmin)
	postFormatted.CanEditStatement = (isIamAdmin && postFormatted.IsOfficial != 1)
	postFormatted.CanMove = (isIamAdmin && postFormatted.IsOfficial != 1)

	return postFormatted, nil
}

func GetUserPosts(c context.Context, req *pb.GetUserPostListReq, intlOpenID, language, gameId, areaId string) (*pb.GetUserPostListRsp, error) {
	queryIntlOpenID := req.IntlOpenid
	postListRsp := &pb.GetUserPostListRsp{
		PageInfo: &pb.PageInfo{},
		List:     make([]*pb.GetPostRsp, 0),
	}
	// 查询当前用户信息
	// userInfo, err := dao.GetUserByIntlOpenid(intlOpenID)
	// if err != nil {
	// 	return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to get index user")
	// }

	var err error
	var postList []*model.Post
	var nextPageCursor, previousPageCursor string
	// 这里要避免主态查询自己的帖子列表时出现没有设置隐私开关的bug
	if queryIntlOpenID != intlOpenID && queryIntlOpenID != "" {
		privacySwitch, err := privacy.GetUserPrivacySwitch(c, queryIntlOpenID)
		if err != nil {
			return postListRsp, err
		}
		if privacySwitch.ShowMyPosts == 0 {
			postListRsp.PageInfo.IsFinish = true
			return postListRsp, nil
		}
	}

	limit := int(req.Limit + 1) //写死一个总数值，少于这个就是没有下一页了
	total := 0                  // 捞出出来的值总共有多少条

	// 先获取缓存数据
	postBasesRedisKey := cache.GetUserPostBaseListGuestKey(queryIntlOpenID, req.NextPageCursor, req.Limit, language)
	if intlOpenID == queryIntlOpenID {
		// 主态
		postBasesRedisKey = cache.GetUserPostBaseListHostKey(intlOpenID, req.NextPageCursor, req.Limit, language)
	}
	postBasesCacheInfo, err := redis.GetClient().Get(c, postBasesRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postBasesCacheInfo), &postList)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserPosts postBases cache json.Unmarshal error.postBasesRedisKey: %s, err: %v", postBasesRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetUserPostsBaseJsonUnmarshalError, "Failed to obtain post info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserPosts postBases redis err: %v", err)
		}
		// 查询类型：下一页数据
		if req.PageType == pb.PageType_NEXTPAGE {
			previousPageCursor = req.NextPageCursor
			var createdOnMs int64
			if req.NextPageCursor != "" {
				createdOnMs, err = util.DecryptPageCursorI(req.NextPageCursor)
				if err != nil {
					return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
				}
			}
			conditions := &dao.PostConditions{
				IntlOpenid: queryIntlOpenID,
				Order: []*dao.OrderConditions{
					{
						Column: "created_on_ms",
						IsDesc: true,
					},
				},
			}
			// 普通用户在主态的情况下才需要展示待审核的，官方和机构的不展示待审核的
			if intlOpenID == queryIntlOpenID {
				conditions.CompositeCondition = &dao.CompositeCondition{
					Operator: "OR",
					Conditions: []dao.ConditionInterface{
						dao.Conditions{
							Column: "is_audit",
							Symbol: "=",
							Value:  1,
						},
						dao.Conditions{
							Column: "is_audit",
							Symbol: "=",
							Value:  2,
						},
					},
				}
			} else {
				conditions.IsAudit = 1
				//// 客态访问需要加入语言
				//conditions.Language = []string{language, "all"}
			}
			if createdOnMs > 0 {
				conditions.LtCreatedOnMs = createdOnMs
			}
			// 判断当前用户是否是官方账号，是的话从p_post_language表中查询数据,主态不区分
			authType := formatted.GetUserAuth(req.IntlOpenid)
			if authType == constants.USER_AUTH_TYPE_OFFICIAL && queryIntlOpenID != intlOpenID {
				postList, err = dao.FetchPostByLanguage(conditions, limit, language)
			} else {
				postList, err = dao.FetchPost(conditions, limit)
			}
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to FetchPost")
			}

		} else {
			// 查询类型：上一页数据
			// 如果是尾页
			if req.PreviousPageCursor == "" {

			}

		}
		postListByte, err := json.Marshal(postList)
		if err == nil {
			redis.GetClient().SetEX(c, postBasesRedisKey, string(postListByte), 2*time.Minute).Result()
		}
	}

	// 生成下一页的游标
	if len(postList) > 0 {
		total = len(postList)
		if total > (limit - 1) { //判断是否有10条数据，没有的话直接切割会导致panic
			postList = postList[:(limit - 1)] //截取前10条， 第10条的下标是9
		}
		nextPageCursor, err = util.EncryptPageCursorI(postList[len(postList)-1].CreatedOnMs)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to create nextPageCursor")
		}
	}
	// TODO 这里查询个人页面动态的时候，不需要每条都查用户信息了，只需要优化查一次就够
	postListRsp.List, err = MergePosts(c, intlOpenID, postList, language, false)
	if err != nil {
		return nil, err
	}
	if intlOpenID == queryIntlOpenID {
		var postUUIDs []string
		for _, postItem := range postListRsp.List {
			postUUIDs = append(postUUIDs, postItem.PostUuid)
		}
		// 获取待审核中的数据
		postAuditList, err := dao.GetPostEditNotAuditListByPostUUIDs(postUUIDs)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CmsPostsFrom GetPostEditNotAuditListByPostUUIDs err , postUUIDs:(%v), err=(%v)", postUUIDs, err)
		} else {
			for _, postItem := range postListRsp.List {
				if len(postAuditList) > 0 {
					for _, postAuditItem := range postAuditList {
						if postAuditItem.PostUUID == postItem.PostUuid {
							postItem.Type = postAuditItem.Type
							postItem.Platform = postAuditItem.Platform
							postItem.Title = postAuditItem.Title
							postItem.Content = postAuditItem.Content
							postItem.ContentSummary = postAuditItem.ContentSummary
							if len(postAuditItem.PicUrls) > 0 {
								postItem.PicUrls = strings.Split(postAuditItem.PicUrls, ",")
							}
						}
					}
				}
			}
		}
	}
	if len(postListRsp.List) == 0 || total < limit {
		postListRsp.PageInfo.IsFinish = true
	} else {
		postListRsp.PageInfo.NextPageCursor = nextPageCursor
	}
	postListRsp.PageInfo.PreviousPageCursor = previousPageCursor
	return postListRsp, nil
}

func GetIndexPosts(c context.Context, req *pb.GetPostListReq, intlOpenID, language, gameId, areaId string) (*pb.GetPostListRsp, error) {
	// plateInfo, err := dao.PlateGet(&dao.PlateConditions{
	// 	Id: req.PlateId,
	// })
	// if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetIndexPosts GetPlateList failed, err: %v", err)
	// 	return nil, errs.NewCustomError(c, code.InvalidParamsErr, "GetIndexPosts | Invalid plate id")
	// }
	// ===========即将废弃, start==========
	plateInfo, err := plate.GetPlateInfoById(c, req.PlateId)
	if err != nil {
		return nil, err
	}
	if plateInfo.UniqueIdentifier == constants.PLATE_EVENT && language == "zh" {
		language = "zh-TW"
	}
	// 如果是event或者是creatorhub就直接走特殊查询逻辑
	if plateInfo.UniqueIdentifier == constants.PLATE_EVENT || plateInfo.UniqueIdentifier == constants.PLATE_CREATORHUB {
		return GetExternalPosts(c, req, intlOpenID, language, plateInfo.UniqueIdentifier)
	}
	// ===========即将废弃, end==========

	postListRsp := &pb.GetPostListRsp{
		PageInfo: &pb.PageInfo{},
		List:     make([]*pb.GetPostRsp, 0),
	}
	var postList []*model.Post
	var nextPageCursor, previousPageCursor string

	// 先获取缓存数据
	postBasesRedisKey := cache.GetRecentPostBaseListGuestKey(req.PlateId, language, req.NextPageCursor, req.Limit, req.NeedAllRegion)
	if intlOpenID != "" {
		// 主态
		postBasesRedisKey = cache.GetRecentPostBaseListHostKey(req.PlateId, intlOpenID, language, req.NextPageCursor, req.Limit, req.NeedAllRegion)
	}
	postBasesCacheInfo, err := redis.GetClient().Get(c, postBasesRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postBasesCacheInfo), &postList)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetIndexPosts postBases cache json.Unmarshal error.postBasesRedisKey: %s, err: %v", postBasesRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetRecentPostsBaseJsonUnmarshalError, "Failed to obtain post info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetIndexPosts postBases redis err: %v", err)
		}
		// 查询当前用户信息
		// userInfo, err := dao.GetUserByIntlOpenid(intlOpenID)
		// if err != nil {
		// 	return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to get index user")
		// }
		currentTime := time.Now().Unix()
		var conditions *dao.PostConditions
		// 查询类型：下一页数据
		if req.PageType == pb.PageType_NEXTPAGE {
			previousPageCursor = req.NextPageCursor
			var createdOnMsCursor int64
			// 如果是首页，先查置顶的数据
			if req.NextPageCursor == "" {
				topConditions := &dao.PostConditions{
					PlateId: req.PlateId,
					IsTop:   "1",
					IsAudit: 1,
					Order: []*dao.OrderConditions{
						{
							Column: "created_on_ms",
							IsDesc: true,
						},
					},
				}
				//if !req.NeedAllRegion {
				//	topConditions.Language = []string{language, "all"}
				//}
				topPosts, err := dao.FetchPostLanguage(topConditions, 0, language)
				if err != nil {
					return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to Fetch top Post")
				}
				for _, topPost := range topPosts {
					if topPost.TopOn > currentTime {
						postList = append(postList, topPost)
					}
				}
				// 根据置顶顺序值排序
				sort.Slice(postList, func(i, j int) bool {
					return postList[i].TopSort < postList[j].TopSort
				})
				createdOnMsCursor = 0
			} else {
				createdOnMsCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
				if err != nil {
					return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
				}
			}
			conditions = &dao.PostConditions{
				PlateId: req.PlateId,
				IsTop:   "0",
				Order: []*dao.OrderConditions{
					{
						Column: "created_on_ms",
						IsDesc: true,
					},
				},
			}

			if intlOpenID != "" {
				// 普通用户在主态的情况下才需要展示待审核的，官方和机构的不展示待审核的
				authType := formatted.GetUserAuth(intlOpenID)
				if authType == 2 {
					conditions.CompositeCondition = &dao.CompositeCondition{
						Operator: "OR",
						Conditions: []dao.ConditionInterface{
							&dao.Conditions{
								Column: "is_audit",
								Symbol: "=",
								Value:  1,
							},
							&dao.CompositeCondition{
								Operator: "AND",
								Conditions: []dao.ConditionInterface{
									&dao.Conditions{
										Column: "is_audit",
										Symbol: "=",
										Value:  2,
									},
									&dao.Conditions{
										Column: "intl_openid",
										Symbol: "=",
										Value:  intlOpenID,
									},
								},
							},
						},
					}
				} else {
					conditions.IsAudit = 1
				}
				// 主态需要展示已经被隐藏的
				conditions.PostHideCondition = &dao.CompositeCondition{
					Operator: "OR",
					Conditions: []dao.ConditionInterface{
						&dao.Conditions{
							Column: "is_hide",
							Symbol: "=",
							Value:  0,
						},
						&dao.CompositeCondition{
							Operator: "AND",
							Conditions: []dao.ConditionInterface{
								&dao.Conditions{
									Column: "is_hide",
									Symbol: "=",
									Value:  1,
								},
								&dao.Conditions{
									Column: "intl_openid",
									Symbol: "=",
									Value:  intlOpenID,
								},
							},
						},
					},
				}
			} else {
				conditions.IsAudit = 1
				conditions.IsHide = true
			}
			if createdOnMsCursor > 0 {
				conditions.LtCreatedOnMs = createdOnMsCursor
			}
			var posts []*model.Post
			if !req.NeedAllRegion {
				// 不是获取所有区域的情况下按照语言选择区域
				posts, err = dao.FetchPostLanguage(conditions, int(req.Limit), language)
			} else {
				// 获取所有区域的情况下查询主表
				posts, err = dao.FetchPost(conditions, int(req.Limit))
			}

			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to FetchPost")
			}
			postList = append(postList, posts...)
		} else {
			// 查询类型：上一页数据
			// 如果是尾页
			if req.PreviousPageCursor == "" {

			}
		}

		postListByte, err := json.Marshal(postList)
		if err == nil {
			redis.GetClient().SetEX(c, postBasesRedisKey, string(postListByte), 2*time.Minute).Result()
			if intlOpenID != "" {
				// 将用户添加到缓存
				keysKey := cache.GetUserPostCacheKeysKey(intlOpenID)
				redis.GetClient().SAdd(c, keysKey, postBasesRedisKey).Result()
			}
		}
	}

	postListRsp.List, err = MergePosts(c, intlOpenID, postList, language, false)
	if err != nil {
		return nil, err
	}
	if len(postList) == 0 || len(postList) < int(req.Limit) {
		postListRsp.PageInfo.IsFinish = true
	} else {
		// 生成下一页的游标
		if len(postListRsp.List) > 0 {
			nextPageCursor, err = util.EncryptPageCursorI(postList[len(postList)-1].CreatedOnMs)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to create nextPageCursor")
			}
		}
		postListRsp.PageInfo.NextPageCursor = nextPageCursor
	}
	postListRsp.PageInfo.PreviousPageCursor = previousPageCursor
	return postListRsp, nil
}

/*
*
officialGuaranteeLang: 官方账号是否需要兜底语言
*/
type PostFormattedChannel struct {
	PostInfo *pb.GetPostRsp
	Index    int
}

type CmsPostFormattedChannel struct {
	PostInfo *pb.CmsPostItem
	Index    int
}

func MergePosts(ctx context.Context, myUserIntlOpenid string, posts []*model.Post, language string, isAllLanguage bool) ([]*pb.GetPostRsp, error) {
	postFormatteds := make([]*pb.GetPostRsp, 0, len(posts))
	if len(posts) == 0 {
		return postFormatteds, nil
	}

	var postUUIDsStr string
	var postUUIDs []string
	for _, post := range posts {
		postUUIDsStr = fmt.Sprintf("%s_%s", postUUIDsStr, post.PostUUID)
		postUUIDs = append(postUUIDs, post.PostUUID)
	}

	hash := sha256.New()
	hash.Write([]byte(postUUIDsStr))
	hashValue := hash.Sum(nil)
	postUUIDsMd5Str := hex.EncodeToString(hashValue)

	// 先获取缓存数据
	postDetailsRedisKey := cache.GetPostDetailListKeyWithIntlOpenid(myUserIntlOpenid, postUUIDsMd5Str, language)
	posDetailsCacheInfo, err := redis.GetClient().Get(ctx, postDetailsRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(posDetailsCacheInfo), &postFormatteds)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("MergePosts postDetails cache json.Unmarshal error.postDetailsRedisKey: %s, err: %v", postDetailsRedisKey, err)
			return nil, errs.NewCustomError(ctx, code.GetRecentPostsDetailJsonUnmarshalError, "Failed to obtain post detail info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("MergePosts postDetails redis err: %v", err)
		}

		var wg sync.WaitGroup
		postFormattedChannel := make(chan *PostFormattedChannel, 1)
		// 获取板块多语言信息
		allPlateLangMap, err := plate.GetAllPlateNameMap(ctx)
		if err != nil {
			return postFormatteds, err
		}
		// 判断这些帖子是否都是主态用户发布的，如果是则只需要查询一次用户信息
		var myUserInfo *pbUser.UserInfo
		isAllMyPosts := true
		for _, post := range posts {
			if post.IntlOpenid != myUserIntlOpenid {
				isAllMyPosts = false
			}
		}
		if isAllMyPosts {
			myUserInfo, err = user.GetUserDetailInfoByOpenid(ctx, myUserIntlOpenid, myUserIntlOpenid, language)
			if err != nil {
				log.WithFieldsContext(ctx, "log type", constants.LogType_Standalonesite).Errorf("MergePosts GetUserDetailInfoByOpenid isAllMyPosts err, intl_openid:(%s), err=(%v)", myUserIntlOpenid, err)
			}
		}
		// 提前获取tag数据
		postsTags, err := getPostTagInfos(ctx, postUUIDs, language)
		if err != nil {
			postsTags = make(map[string][]*pb.TagInfo)
		}
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("MergePosts getPostTagInfos postsTags: %v", postsTags)
		// 获取当前登录用户是否是管理员
		isIAmAdmin := formatted.GetUserAdmin(myUserIntlOpenid)
		for i, post := range posts {
			postFormatted := &pb.GetPostRsp{
				PostUuid:        post.PostUUID,
				IntlOpenid:      post.IntlOpenid,
				Type:            post.Type,
				IsTop:           post.IsTop,
				TopSort:         post.TopSort,
				TopOn:           post.TopOn,
				LatestRepliedOn: post.LatestRepliedOn,
				GameId:          post.GameId,
				AreaId:          post.AreaId,
				CreatedOn:       post.CreatedOn,
				PlateId:         post.PlateID,
				Language:        post.Language,
				IsAudit:         post.IsAudit,
				Platform:        post.Platform,
				PublishOn:       int32(post.PublishOn),
				IsOfficial:      post.IsOfficial,
				IsHide:          post.IsHide,
				CanDelete:       isIAmAdmin,
				CanEdit:         myUserIntlOpenid == post.IntlOpenid,
			}
			if plateLangMap, ok := allPlateLangMap[post.PlateID]; ok {
				if plateLang, ok2 := plateLangMap[language]; ok2 {
					postFormatted.PlateName = plateLang
				} else if plateEnName, ok3 := plateLangMap["en"]; ok3 {
					postFormatted.PlateName = plateEnName
				}
			}

			wg.Add(1) // 增加 WaitGroup 的计数器
			go func(postItem *model.Post, lang string, index int, isAllMyPostsBool bool, myUserInfoItem *pbUser.UserInfo, postsTags map[string][]*pb.TagInfo) {
				defer recovery.CatchGoroutinePanic(ctx)
				defer wg.Done() // 函数结束时减少计数器

				postContentList, err := dao.GetPostContentList(postItem.PostUUID, false)
				if err != nil {
					log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("MergePosts err by get post content, post_uuid:(%s), err=(%v)", postItem.PostUUID, err)
				} else {
					var isExistLanguage bool
					var postContent *model.PostContent
					if len(postContentList) > 0 {
						for _, content := range postContentList {
							if content.Language == language {
								postContent = content
								isExistLanguage = true
								break
							}
						}
						if !isExistLanguage {
							// 没有对应的语言直接采用当前下标为0的数据，因为数据都是根据排序取出来的
							postContent = postContentList[0]
						}
						postFormatted.IsEssence = postContent.IsEssence
						postFormatted.EssenceOn = postContent.EssenceOn
						postFormatted.IsOriginal = postContent.IsOriginal
						postFormatted.OriginalUrl = postContent.OriginalURL
						postFormatted.OriginalReprint = postContent.OriginalReprint
						postFormatted.Title = postContent.Title
						postFormatted.ExtInfo = postContent.ExtInfo
						postFormatted.Language = postContent.Language
						if postItem.Type == constants.POST_TYPE_RICH_TEXT {
							postFormatted.ContentSummary = postContent.ContentSummary
						} else {
							postFormatted.ContentSummary = postContent.Content
						}
						postFormatted.Content = postContent.Content
						var picUrls []string
						picUrlList := strings.Split(postContent.PicUrls, ",")
						if len(picUrlList) > 0 {
							for _, picUrl := range picUrlList {
								if picUrl != "" { // 检查字符串是否为空
									picUrls = append(picUrls, picUrl) // 如果不是空字符串，则添加到结果数组中
								}
							}
						}
						postFormatted.PicUrls = picUrls
					}
				}
				if isAllMyPostsBool && myUserInfoItem != nil {
					postFormatted.User = myUserInfoItem
					postFormatted.CanDelete = myUserIntlOpenid == myUserInfoItem.IntlOpenid || postFormatted.CanDelete
					postFormatted.IsMine = myUserIntlOpenid == myUserInfoItem.IntlOpenid
					postFormatted.CanReport = myUserIntlOpenid != myUserInfoItem.IntlOpenid
				} else {
					userInfo, err := user.GetUserDetailInfoByOpenid(ctx, myUserIntlOpenid, postItem.IntlOpenid, language)
					if err != nil {
						log.WithFieldsContext(ctx, "log type", constants.LogType_Standalonesite).Errorf("MergePosts GetUserDetailInfoByOpenid err, intl_openid:(%s), err=(%v)", postItem.IntlOpenid, err)
					} else {
						postFormatted.User = userInfo
						postFormatted.CanDelete = myUserIntlOpenid == userInfo.IntlOpenid || postFormatted.CanDelete
						postFormatted.IsMine = myUserIntlOpenid == userInfo.IntlOpenid
						postFormatted.CanReport = myUserIntlOpenid != userInfo.IntlOpenid
					}
				}
				// tagInfos, err := getPostTagInfo(ctx, postItem.PostUUID, language)
				// if err == nil && len(tagInfos) > 0 {
				// 	postFormatted.Tags = tagInfos
				// }
				if postsTags != nil && postsTags[postItem.PostUUID] != nil && len(postsTags[postItem.PostUUID]) > 0 {
					postFormatted.Tags = postsTags[postItem.PostUUID]
				}

				if postItem.IsOfficial == 1 {
					// 如果是官方动态，屏蔽举报按钮
					postFormatted.CanReport = false
				}

				// 查询官方发的帖子信息
				if isAllLanguage {
					getPostContentAllLanguageFormatted(postItem.PostUUID, postFormatted)
					// 判断当前官方帖子是否可以编辑， 只有是自己的帖子，并且当前用户是官方认证和机构认证的才行
					// postFormatted.CanEdit = (myUserIntlOpenid == postFormatted.IntlOpenid && (postFormatted.User.AuthType == 1 || postFormatted.User.AuthType == 3))
				}
				// 处理为空的图片链接
				handlerPostImages(postFormatted)
				postFormattedChannel <- &PostFormattedChannel{
					PostInfo: postFormatted,
					Index:    index,
				}

			}(post, language, i, isAllMyPosts, myUserInfo, postsTags) // 通过闭包传递参数
		}
		// 等待所有 goroutine 完成
		go func() {
			defer recovery.CatchGoroutinePanic(context.Background())
			wg.Wait()
			close(postFormattedChannel)
		}()
		// 重新赋值,可能某些数据会被剔除
		var list = make([]*PostFormattedChannel, 0)
		for postFormatted := range postFormattedChannel {
			if postFormatted != nil {
				list = append(list, postFormatted)
			}
		}
		// sort.Slice(list, func(i, j int) bool {
		// 	return list[i].Index < list[j].Index
		// })
		for _, item := range list {
			postFormatteds = append(postFormatteds, item.PostInfo)
		}

		sort.Slice(postFormatteds, func(i, j int) bool {
			return postFormatteds[i].CreatedOn > postFormatteds[j].CreatedOn
		})

		postFormattedsByte, err := json.Marshal(postFormatteds)
		if err == nil {
			redis.GetClient().SetEX(ctx, postDetailsRedisKey, string(postFormattedsByte), 2*time.Minute).Result()
			if myUserIntlOpenid != "" {
				keysKey := cache.GetUserPostCacheKeysKey(myUserIntlOpenid)
				redis.GetClient().SAdd(ctx, keysKey, postDetailsRedisKey).Result()
				// 设置过期时间
				redis.GetClient().Expire(ctx, keysKey, 2*time.Minute).Result()
			}
		}
	}

	// 获取统计数据
	postStatsList, err := dao.BatchGetPostStatsByPostUuids(postUUIDs)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetIndexPosts err by get post stats list, postUUIDs:(%v), err=(%v)", postUUIDs, err)
	}
	for _, postItem := range postFormatteds {
		if len(postStatsList) > 0 {
			for _, postStatsItem := range postStatsList {
				if postStatsItem.PostUUID == postItem.PostUuid {
					upvoteMap := make(map[int64]int64)
					if postStatsItem.UpvoteMap != "" {
						err := json.Unmarshal([]byte(postStatsItem.UpvoteMap), &upvoteMap)
						if err != nil {
							log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetIndexPosts UpvoteMap json.Unmarshal err: %v, postUUID is %s", err, postItem.PostUuid)
						}
					}
					postItem.PowerNum = postStatsItem.PowerNumFloat
					postItem.CommentCount = postStatsItem.CommentCount
					postItem.CollectionCount = postStatsItem.CollectionCount
					postItem.UpvoteCount = postStatsItem.UpvoteCount
					postItem.UpvoteMap = upvoteMap
					postItem.BrowseCount = postStatsItem.BrowseCount
					postItem.ForwardCount = int32(postStatsItem.ForwardCount)
				}
			}
		}
	}
	return postFormatteds, nil
}

func getPostTagInfo(ctx context.Context, postUuid string, language string) ([]*pb.TagInfo, error) {
	var res []*pb.TagInfo
	postTagsRedisKey := cache.GetTagIdByPostUUIDKey(postUuid, language)
	postTagsCacheInfo, err := redis.GetClient().Get(ctx, postTagsRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postTagsCacheInfo), &res)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("getPostTagInfo cache json.Unmarshal error.postTagsRedisKey: %s, err: %v", postTagsRedisKey, err)
			// return nil, errs.NewCustomError(ctx, code.GetPostTagsJsonUnmarshalError, "Failed to obtain post tags info, data parsing exception")
		} else {
			return res, nil
		}
	}
	tags, err := dao.GetTagIdByPostUUID(postUuid)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("CMergePosts err, GetTagIdByPostUUID failed, post_uuid:(%s), err=(%v)", postUuid, err)
		return nil, err
	}
	if len(tags) > 0 {
		tagLangs, err := dao.GetTagLanguageByTagIds(tags)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("CMergePosts err, GetTagLanguageByTagIds failed, tags:(%v), err=(%v)", tags, err)
			return nil, err
		}
		for _, tag := range tags {
			var tagInfoItem = &pb.TagInfo{}
			var hasLang bool
			for _, tagLang := range tagLangs {
				if tagLang.Language == language && int64(tagLang.TagID) == tag {
					tagInfoItem = &pb.TagInfo{
						Id:   tag,
						Name: tagLang.TagName,
					}
					hasLang = true
					break
				}
			}
			// 兜底英语
			if !hasLang {
				for _, tagLang := range tagLangs {
					if tagLang.Language == "en" && int64(tagLang.TagID) == tag {
						tagInfoItem = &pb.TagInfo{
							Id:   tag,
							Name: tagLang.TagName,
						}
						break
					}
				}
			}
			if tagInfoItem.Id > 0 {
				res = append(res, tagInfoItem)
			}
		}
	}
	resByte, err := json.Marshal(res)
	if err == nil {
		redis.GetClient().SetEX(ctx, postTagsRedisKey, string(resByte), 2*time.Minute).Result()
	}

	return res, nil
}

// 批量获取帖子标签
func getPostTagInfos(ctx context.Context, postUUIds []string, language string) (map[string][]*pb.TagInfo, error) {
	postsTagas := make(map[string][]*pb.TagInfo)
	postIdMissingCacheList := make([]string, 0)
	for _, postUUid := range postUUIds {
		var curPostUUId []*pb.TagInfo
		// 判断是否在缓存中
		postTagsRedisKey := cache.GetTagIdByPostUUIDKey(postUUid, language)
		postTagsCacheInfo, err := redis.GetClient().Get(ctx, postTagsRedisKey).Result()
		if err == nil {
			err = json.Unmarshal([]byte(postTagsCacheInfo), &curPostUUId)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("getPostTagInfo cache json.Unmarshal error.postTagsRedisKey: %s, err: %v", postTagsRedisKey, err)
				// return nil, errs.NewCustomError(ctx, code.GetPostTagsJsonUnmarshalError, "Failed to obtain post tags info, data parsing exception")
			} else {
				postsTagas[postUUid] = curPostUUId
				// postsTagas = append(postsTagas, curPostUUId)
				continue
			}
		}
		postIdMissingCacheList = append(postIdMissingCacheList, postUUid)
	}
	// 批量获取所有未命中缓存的post的tags
	postTags, err := dao.GetTagIdsByPostUUIds(postIdMissingCacheList)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("getPostTagInfos err, GetTagIdsByPostUUIds failed, post_uuid:(%s), err=(%v)", postIdMissingCacheList, err)
		return nil, err
	}
	// 获取tag的多语言数据
	tagIds := make([]int64, 0)
	for _, postTag := range postTags {
		tagIds = append(tagIds, postTag.TagID)
	}
	// 批量获取多语言
	tagLanguages, err := dao.GetTagLanguageByTagIds(tagIds)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("getPostTagInfos err, GetTagLanguageByTagIds failed, tag_ids:(%s), err=(%v)", tagIds, err)
		return nil, err
	}
	for _, postId := range postIdMissingCacheList {
		postTagsRedisKey := cache.GetTagIdByPostUUIDKey(postId, language)
		var curPostTags []*pb.TagInfo
		for _, postTag := range postTags {
			if postTag.PostUUID == postId {
				tagInfo := &pb.TagInfo{
					Id:   postTag.TagID,
					Name: "",
				}
				for _, tagLanguage := range tagLanguages {
					if tagLanguage.TagID == int(postTag.TagID) {
						if tagLanguage.Language == "en" && tagInfo.Name == "" {
							tagInfo.Name = tagLanguage.TagName
						} else if tagLanguage.Language == language {
							tagInfo.Name = tagLanguage.TagName
							break
						}
					}
				}
				curPostTags = append(curPostTags, tagInfo)
			}
		}
		postsTagas[postId] = curPostTags
		postTagsByte, err := json.Marshal(curPostTags)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("getPostTagInfos err, json.Marshal failed, post_uuid:(%s), err=(%v)", postId, err)
		} else {
			redis.GetClient().Set(ctx, postTagsRedisKey, string(postTagsByte), 2*time.Minute)
		}
	}
	return postsTagas, nil
}
func GetPostListMyIsOperation(c context.Context, posts []*pb.GetPostRsp, intlOpenid string) error {
	if intlOpenid == "" || len(posts) == 0 {
		return nil
	}
	var postUserIdsMap = make(map[string]struct{})

	var postStarIds = make([]string, 0, len(posts))
	// var postCommentIds = make([]string, 0, len(posts))
	// var postCollectionIds = make([]string, 0, len(posts))
	// 是否都是主态发的帖子
	isMyOpenid := true
	for _, post := range posts {
		// 去重
		postUserIdsMap[post.IntlOpenid] = struct{}{}
		if intlOpenid != post.IntlOpenid {
			isMyOpenid = false
		}
		if post.UpvoteCount != 0 {
			postStarIds = append(postStarIds, post.PostUuid)
		}

		// if post.CollectionCount != 0 && !post.IsCollection {
		// 	postCollectionIds = append(postCollectionIds, post.PostUuid)
		// }

		// if post.CommentCount != 0 && !post.IsComment {
		// 	postCommentIds = append(postCommentIds, post.PostUuid)
		// }
		// if intlOpenid == post.IntlOpenid {
		// 	posts[i].IsMine = true
		// }
	}

	var err error
	// if len(postCollectionIds) != 0 {
	// 	// 获取用户是否收藏
	// 	if err = GetPostListMyIsCollection(c, intlOpenid, postCollectionIds, posts); err != nil {
	// 		return err
	// 	}
	// }
	if len(postStarIds) != 0 {
		// 获取用户是否点赞
		if err = GetPostListMyIsUpvote(c, intlOpenid, postStarIds, posts); err != nil {
			return err
		}
	}

	// if len(postCommentIds) != 0 {
	// 	// 获取用户是否评论
	// 	if err = comment.GetPostListMyIsComment(c, intlOpenid, postCommentIds, posts); err != nil {
	// 		return err
	// 	}
	// }

	if !isMyOpenid {
		var postUserFollowIds = make([]string, 0, len(postUserIdsMap))
		for i, _ := range postUserIdsMap {
			postUserFollowIds = append(postUserFollowIds, i)
		}
		// 查看我是否关注
		if err = userService.GetPostListMyIsFollow(c, intlOpenid, postUserFollowIds, posts); err != nil {
			return err
		}
	}

	return nil
}

func getPostContentAllLanguageFormatted(postUuid string, rsp *pb.GetPostRsp) {
	// 查询官方发的帖子信息
	postOfficialData, err := dao.GetPostContentList(postUuid, false)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("postsFrom GetOfficialDataByPostUuid err: %v, postUuid:[%s]", err, postUuid)
	} else {
		for _, officialItem := range postOfficialData {
			var picUrls []string
			if len(officialItem.PicUrls) > 0 {
				picUrls = strings.Split(officialItem.PicUrls, ",")
			}
			rsp.ContentLanguages = append(rsp.ContentLanguages, &pb.CreatePostContent{
				Title:           officialItem.Title,
				Content:         officialItem.Content,
				Platform:        rsp.Platform,
				PicUrls:         picUrls,
				IsOriginal:      rsp.IsOriginal,
				OriginalUrl:     rsp.OriginalUrl,
				OriginalReprint: rsp.OriginalReprint,
				ExtInfo:         officialItem.ExtInfo,
				ContentSummary:  officialItem.ContentSummary,
				Language:        officialItem.Language,
				Id:              int32(officialItem.ID),
			})
		}
	}
	// 屏蔽举报
	rsp.CanReport = false
	return
}

func handlerPostImages(rsp *pb.GetPostRsp) {
	if rsp == nil {
		return
	}
	// 处理图片为空字符串的
	var picUrls []string
	if len(rsp.PicUrls) > 0 {
		for _, picUrl := range rsp.PicUrls {
			if picUrl != "" { // 检查字符串是否为空
				picUrls = append(picUrls, picUrl) // 如果不是空字符串，则添加到结果数组中
			}
		}
		rsp.PicUrls = picUrls
	}
	if len(rsp.ContentLanguages) > 0 {
		for i, language := range rsp.ContentLanguages {
			var picImgs []string
			if len(language.PicUrls) > 0 {
				for _, picUrl := range language.PicUrls {
					if picUrl != "" { // 检查字符串是否为空
						picImgs = append(picImgs, picUrl) // 如果不是空字符串，则添加到结果数组中
					}
				}
				rsp.ContentLanguages[i].PicUrls = picImgs
			}
		}
	}
}

func handlerCmsPostImages(rsp *pb.CmsPostItem) {
	if rsp == nil {
		return
	}
	// 处理图片为空字符串的
	var picUrls []string
	if len(rsp.PicUrls) > 0 {
		for _, picUrl := range rsp.PicUrls {
			if picUrl != "" { // 检查字符串是否为空
				picUrls = append(picUrls, picUrl) // 如果不是空字符串，则添加到结果数组中
			}
		}
		rsp.PicUrls = picUrls
	}
}
