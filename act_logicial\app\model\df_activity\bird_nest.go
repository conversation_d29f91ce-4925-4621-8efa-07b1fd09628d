package df_activity

import "time"

// UserBirdNestReward 用户掏鸟窝活动领奖数据表
type UserBirdNestReward struct {
	ID             int64     `gorm:"column:id;primary_key;AUTO_INCREMENT"`                  // 自增id
	UserId         string    `gorm:"column:user_id;NOT NULL"`                               // 用户id
	FirstClaimTime int64     `gorm:"column:first_claim_time;NOT NULL"`                      // 用户首次领取奖品成功时间
	UpdateTime     time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL"` // 更新时间
	CreateTime     time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL"` // 创建时间
}

// TableName ...
func (m *UserBirdNestReward) TableName() string {
	return "t_user_bird_nest_reward"
}
