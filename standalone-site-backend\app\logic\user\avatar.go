package user

import (
	"context"
	"encoding/json"
	"time"

	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

const (
	// 通用头像代替的游戏id
	AllGameTempID = 1
)

type GetAvatarListRes struct {
	Currency   []string `json:"currency"`
	GameAvatar []string `json:"game_avatar"`
}

func GetAvatarList(c context.Context, gameId string) (*pb.GetUserAvatarsRsp, error) {
	var resp = &pb.GetUserAvatarsRsp{
		Currency:   make([]string, 0),
		GameAvatar: make([]string, 0),
	}
	avatarData, err := getAllAvatarData(c)
	if err != nil {
		return nil, errs.NewCustomError(c, code.AvatarGetListFailed, "GetAvatarList |  Get avatar list failed")
	}
	// 暂时不需要绑定游戏，直接获取全量的头像
	//// 根据game_id捞取对应游戏配置的头像列表
	//if gameId != "" {
	//	if data, ok := avatarData[cast.ToInt64(gameId)]; ok {
	//		resp.GameAvatar = data
	//	}
	//} else {
	//	// 没有game_id的情况下拿出所有游戏的数据
	//	for k, v := range avatarData {
	//		if k == AllGameTempID {
	//			// 屏蔽通用类型
	//			continue
	//		}
	//		resp.GameAvatar = append(resp.GameAvatar, v...)
	//	}
	//}
	//if data, ok := avatarData[AllGameTempID]; ok {
	//	resp.Currency = data
	//}
	if len(avatarData) > 0 {
		resp.Currency = avatarData
	}
	return resp, nil

}

func RemoveAvatarCache(c context.Context) error {
	redisKey := cache.GetUserAvatarAllDataKey()
	redis.GetClient().Del(c, redisKey)
	return nil
}

func getAllAvatarData(c context.Context) ([]string, error) {
	var avatarData = make([]string, 0)
	redisKey := cache.GetUserAvatarAllDataKey()
	// 获取缓存的数据
	if result, cErr := redis.GetClient().Get(c, redisKey).Result(); cErr == nil {
		cErr = json.Unmarshal([]byte(result), &avatarData)
		if cErr != nil {
			return nil, errs.NewCustomError(c, code.AvatarGetListFailedToJsonDecode, "getAllAvatarData | Get avatar binding game list json decode failed")
		}
		return avatarData, nil
	}
	// 先获取所有的通用头像
	allGameAvatars, err := getBindingAllGameList(c)
	if err != nil {
		return nil, err
	}
	if len(allGameAvatars) > 0 {
		avatarData = allGameAvatars
	}
	// 暂时不需要绑定游戏，直接获取全量的头像
	// 获取所有绑定游戏的头像数据
	//list, err := dao.AvatarGameGetList()
	//if err != nil {
	//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetAvatarList binding game err: %v", err)
	//	return nil, errs.NewCustomError(c, code.AvatarGetBindingGameListFailed, "getAllAvatarData | Get avatar binding game list failed")
	//}
	//var avatarIds []int64
	//for _, item := range list {
	//	avatarIds = append(avatarIds, item.AvatarID)
	//}
	//if len(avatarIds) == 0 {
	//	return avatarData, nil
	//}
	//// 捞取全量的绑定部分游戏的数据
	//conditions := &model.ConditionsT{
	//	"status = ?":  constants.AvatarBindingSomeGame,
	//	"ORDER":       "id ASC",
	//	"game_id = ?": "30054",
	//}
	//avatarList, err := dao.GetAvatarList(conditions)
	//if err != nil {
	//	return nil, errs.NewCustomError(c, code.AvatarGetListFailed, "getAllAvatarData | Get avatar list failed")
	//}
	//
	//for _, game := range list {
	//	for _, avatar := range avatarList {
	//		if avatar.ID == game.AvatarID {
	//			avatarData[game.GameIntelID] = append(avatarData[game.GameIntelID], avatar.AvatarURL)
	//			break
	//		}
	//	}
	//}
	//if hasValue(avatarData) {
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		if data, jErr := json.Marshal(avatarData); jErr == nil {
			redis.GetClient().SetEX(context.Background(), redisKey, data, time.Hour*24)
		}
	}()
	//}
	return avatarData, nil
}

func getBindingAllGameList(c context.Context) ([]string, error) {
	var avatars = make([]string, 0)

	// 还要捞取通用头像
	conditions := &dao.AvatarCondition{
		GameId: "30054",
		Order:  make([]*dao.OrderConditions, 0),
	}
	conditions.Order = append(conditions.Order, &dao.OrderConditions{
		Column: "modified_on",
		IsDesc: true,
	})
	list, err := dao.GetAvatarList(conditions)
	if err != nil {
		return nil, errs.NewCustomError(c, code.AvatarGetListFailed, "getBindingAllGameList | Get avatar list failed")
	}
	if len(list) == 0 {
		return avatars, nil
	}
	for _, avatar := range list {
		avatars = append(avatars, avatar.AvatarURL)
	}

	return avatars, nil
}

func hasValue(m map[int64][]string) bool {
	for _, v := range m {
		if len(v) > 0 {
			return true
		}
	}
	return false
}

func CheckUserAvatar(c context.Context, user *model.UserContent, avatar string) error {
	avatarList, err := getBindingAllGameList(c)
	if err != nil {
		return err
	}
	isValidAvatar := false
	for _, avatarItem := range avatarList {
		if avatarItem == avatar {
			isValidAvatar = true
			break
		}
	}
	if !isValidAvatar {
		return errs.NewCustomError(c, code.InvalidAvatarUrlError, "Invalid avatar link, please check")
	}

	return nil
}
