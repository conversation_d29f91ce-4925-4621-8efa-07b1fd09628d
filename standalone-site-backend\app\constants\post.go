package constants

// PostVisibleT 可访问类型，0公开，1私密，2好友
type PostVisibleT uint8

const (
	PostVisitPublic PostVisibleT = iota
	PostVisitPrivate
	PostVisitFriend
	PostVisitInvalid
)

// PostReviewT CMS审批Post类型，1审核通过，2审核不通过，3CMS删除动态，4CMS动态加精，5CMS取消动态加精，6CMS动态置顶，7CMS取消动态置顶，8CMS忽略动态举报, 9 CMS删除举报动态, 10 CMS修改权重, 11 修改帖子是否隐藏还是展示
type PostReviewT uint8

const (
	PostReviewPass PostReviewT = iota + 1
	PostReviewNoPass
	PostReviewDelete
	PostReviewEssenceOn
	PostReviewCancelEssenceOn
	PostReviewTopOn
	PostReviewCancelTopOn
	PostReviewIgnoreReport
	PostReviewDeleteByReport
	PostReviewUpdatePowerNum
	PostReviewUpdateOpenUp
)

// FeedStatusT 资讯修改状态类型，1资讯上架，2资讯下架，3资讯删除
type FeedStatusT uint8

const (
	FeedOpen FeedStatusT = iota + 1
	FeedClose
	FeedDelete
)
const (
	VideoYoutube      = "youtube"
	VideoYoutubeShort = "youtubeshort"
	VideoTiktok       = "tiktok"
)

type PostLikeTye int8

const (
	PostStarLike   PostLikeTye = iota + 1 // 点赞按钮
	postStarStance                        // 表态点赞
)

type PostType int8

const (
	TextPost     PostType = iota + 1 //帖子富文本
	ImgTextPost                      // 图文
	OutVideoPost                     // 外部视频
)

type PostOriginal int8

const (
	IsOriginal PostOriginal = iota + 1
	NotOriginal
)

type TranslateContentType int8

const (
	Post TranslateContentType = iota + 1
	Comment
)

const (
	PostLanguageEn   = "en"
	PostLanguageJa   = "ja"
	PostLanguageKo   = "ko"
	PostLanguageZh   = "zh"
	PostLanguageZhTw = "zh-TW"
)

var AllPostLanguages = []string{PostLanguageEn, PostLanguageJa, PostLanguageKo, PostLanguageZh, PostLanguageZhTw}

// ViolationType 定义违规内容的枚举类型
type ViolationType int

const (
	GarbageContent          ViolationType = iota + 1 // 垃圾内容（与妮姬无关的内容/重复性内容等）
	DisruptiveCommunication                          // 破坏社区和谐沟通（人身攻击/引战/涉政/歧视等）
	IllegalContent                                   // 违法、暴力、色情等各种违法或恶意内容
	GameBalanceImpact                                // 涉及BUG利用/游戏破解、修改等影响游戏平衡的内容
	UnverifiedInfo                                   // 解包内容或散步未经证实的更新资讯、虚假内容等
	AccountTrading                                   // 账号转让/账号买卖/账号估价/代练等交易内容
	Advertising                                      // 广告行为内容
	CopyrightViolation                               // 内容违反社区原创/转载相关规定
	OtherViolations                                  // 其他违规内容
)

const (
	//审核状态 1:未处理;2:已发布;3:已忽略/删除;4:帖子重新编辑审核终止;
	PostAuditUnHandler = iota + 1
	PostAuditPush
	PostAuditIgnore
	PostAuditChangeToInvalid
)

const (
	//审核表帖子操作状态 1:新增帖子审核；2:编辑帖子审核;
	PostAuditActionAdd = iota + 1
	PostAuditActionEdit
)

const (
	// 机器和人审状态，0-未处理1-审核通过2-审核拒绝、异常
	ReviewStatusUnHandler = iota
	ReviewStatusPass
	ReviewStatusReject
)

// PostDeleteStatus 定义帖子删除状态的枚举类型
type PostDeleteStatus int

const (
	UserDeleted         PostDeleteStatus = iota // 0 - 用户自行删除
	CAdminDeleted                               // 1 - C端管理员删除
	BReviewDeleted                              // 2 - B端审核删除
	BAdminDeleted                               // 3 - B端管理删除
	ReportBAdminDeleted                         //4- 举报管理删除
)

// 定义帖子内容筛选状态
type PostContentStatus int

const (
	NormalPost         PostContentStatus = iota + 1 // 正常
	SandboxPost                                     // 沙盒
	UserDeletedPost                                 //用户自己删除
	CAdminDeletedPost                               // c端管理员删除
	BReviewDeletedPost                              // b端审核删除
	BAdminDeletedPost                               // b端管理员删除
	BReportDeletedPost                              // b端举报删除
)

// 定义帖子内容审核状态筛选状态
type PostContentAuditStatus int

const (
	SandboxPostAudit             PostContentAuditStatus = iota + 1 // 沙盒
	NormalPostAudit                                                // 正常，已审核通过
	BAdminDeletedPostAudit                                         // 删除，已审核拒绝
	ReEditingPostAuditTerminated                                   // 帖子重新编辑审核终止
)
