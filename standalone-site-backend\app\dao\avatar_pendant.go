package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetAvatarPendantInfo(avatarPendantId int64) (avatarPendant *model.AvatarPendant, err error) {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.AvatarPendant{}).TableName())
	err = tx.Unscoped().Preload("Languages").Where("id = ?", avatarPendantId).First(&avatarPendant).Error
	if err != nil {
		return
	}
	return
}

// 获取所有挂件，按照order升序，按照修改时间降序
func GetAllAvatarPendanData() (avatarPendant []model.AvatarPendant, err error) {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.AvatarPendant{}).TableName())
	err = tx.Where("is_del = 0").Order("order asc").Order("modified_on desc").Find(&avatarPendant).Error
	if err != nil {
		return
	}
	return
}

// 获取所有挂件，按照order升序，按照修改时间降序
func GetAllAvatarPendanDataWithLanguage(withDelete bool) ([]*model.AvatarPendant, error) {
	avatarPendants := make([]*model.AvatarPendant, 0)
	tx := DB.SelectConnect("db_standalonesite").Table((&model.AvatarPendant{}).TableName())
	if withDelete {
		tx = tx.Unscoped()
	}
	err := tx.Preload("Languages").Order("CASE WHEN `order` = 0 THEN 1 ELSE 0 END, `order` ASC, `modified_on` DESC").Find(&avatarPendants).Error
	if err != nil {
		return nil, err
	}
	return avatarPendants, nil
}
