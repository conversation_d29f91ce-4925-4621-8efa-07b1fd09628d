package model

import (
	"reflect"
	"strings"
)

type PostLanguage struct {
	*Model
	PostUUID          string `json:"post_uuid"`
	IntlOpenid        string `json:"intl_openid"`
	PlateID           int32  `json:"plate_id"` // 板块id
	Type              int32  `json:"type"`     // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	IsTop             int32  `json:"is_top"`
	TopSort           int32  `json:"top_sort"` // 顶置顺序值，越小越靠前
	TopOn             int64  `json:"top_on"`
	IsAudit           int32  `json:"is_audit"`   // 是否已审核 1是 2不是
	Visibility        int32  `json:"visibility"` // 可见性 0公开 1私密 2好友可见
	SocialmediaPostId string `json:"socialmedia_post_id"`
	LatestRepliedOn   int64  `json:"latest_replied_on"`
	GameId            string `json:"game_id"`
	AreaId            string `json:"area_id"`
	CreatedOnMs       int64  `json:"created_on_ms"` //创建时间微秒
	Platform          string `json:"platform"`      //社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
	PublishOn         int64  `json:"publish_on"`    // 定时发布时间
	IsOfficial        int32  `json:"is_official"`   // 是否是官方发帖
}

type PostLanguageTemp struct {
	ID                int64  `json:"id"`
	CreatedOn         int64  `json:"created_on"`
	ModifiedOn        int64  `json:"modified_on"`
	DeletedOn         int64  `json:"deleted_on"`
	IsDel             int    `json:"is_del"`
	PostUUID          string `json:"post_uuid"`
	IntlOpenid        string `json:"intl_openid"`
	PlateID           int32  `json:"plate_id"` // 板块id
	Type              int32  `json:"type"`     // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	IsTop             int32  `json:"is_top"`
	TopSort           int32  `json:"top_sort"` // 顶置顺序值，越小越靠前
	TopOn             int64  `json:"top_on"`
	IsAudit           int32  `json:"is_audit"`   // 是否已审核 1是 2不是
	Visibility        int32  `json:"visibility"` // 可见性 0公开 1私密 2好友可见
	SocialmediaPostId string `json:"socialmedia_post_id"`
	LatestRepliedOn   int64  `json:"latest_replied_on"`
	GameId            string `json:"game_id"`
	AreaId            string `json:"area_id"`
	CreatedOnMs       int64  `json:"created_on_ms"` //创建时间微秒
	Platform          string `json:"platform"`      //社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
	PublishOn         int64  `json:"publish_on"`    // 定时发布时间
	IsOfficial        int32  `json:"is_official"`   // 是否是官方发帖
}

// TableName sets the insert table name for this struct type
func (p *PostLanguage) GetTableName(language string) string {
	switch language {
	case "en":
		return "p_post_en"
	case "zh":
		return "p_post_zh"
	case "ja":
		return "p_post_ja"
	case "zh_tw":
		return "p_post_zh_tw"
	case "ko":
		return "p_post_ko"
	case "zh-TW":
		return "p_post_zh_tw"

	}
	return ""
}

func (p *PostLanguageTemp) GetUpdateColumns() []string {
	excludeFields := []string{"id", "post_uuid"}
	val := reflect.ValueOf(p).Elem()
	typ := val.Type()

	excludeMap := make(map[string]struct{})
	for _, field := range excludeFields {
		excludeMap[field] = struct{}{}
	}

	var jsonFields []string
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		if _, ok := excludeMap[field.Name]; !ok {
			// 获取 JSON 标签
			jsonTag := field.Tag.Get("json")
			if jsonTag != "" {
				// 处理标签中可能的选项，例如 `json:"email,omitempty"`
				jsonFieldName := strings.Split(jsonTag, ",")[0]
				jsonFields = append(jsonFields, jsonFieldName)
			}
		}
	}
	return jsonFields
}
