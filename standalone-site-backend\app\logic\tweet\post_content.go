package tweet

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/spf13/cast"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/attachment"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

type PostContentExtCover struct {
	PostUuid       string
	ExtInfo        string
	Official       bool
	PostOfficialID int64
}

// 视频帖子的ext中视频封面转存
func PostVideoContentCoverTransfer(c context.Context) error {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("PostVideoContentCoverTransfer start, nowTime: %d", time.Now().Unix())
	var wg sync.WaitGroup

	// 定义一个通道，往通道内写入数据，开启一个协程专门读取通道内数据并更新数据，另外一个协程专门往里写数据
	channelPostData := make(chan *PostContentExtCover, 1)
	wg.Add(2)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		defer wg.Done() // 函数结束时减少计数器
		// 同步p_post数据
		posts, err := dao.SyncGetAllPost(0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostVideoContentCoverTransfer SyncGetAllPost err, err=(%v)", err)
			return
		}
		for _, post := range posts {
			// 只有视频类型才需要转换
			if post.Type != int32(constants.OutVideoPost) {
				continue
			}

			// 判断是否是官方贴，官方贴有多语言且内容不一样
			if post.IsOfficial == 1 {
				officialList, err := dao.GetOfficialDataByPostUuid(post.PostUUID)
				if err != nil {
					continue
				}
				for _, item := range officialList {
					if item.ExtInfo == "" {
						continue
					}
					extInfo := GetExtInfo(c, item.ExtInfo, post.GameId, post.AreaId, post.Platform, post.PostUUID)
					if extInfo == "" {
						continue
					}
					channelPostData <- &PostContentExtCover{
						PostUuid:       post.PostUUID,
						ExtInfo:        extInfo,
						Official:       cast.ToBool(post.IsOfficial),
						PostOfficialID: item.ID,
					}
				}
			}

			content, err := dao.PostContentGetIgnoreDelete(post.PostUUID)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostVideoContentCoverTransfer Get post content err, post_uuid: [%s], err=(%v)", post.PostUUID, err)
				continue
			}
			if content.ExtInfo == "" {
				continue
			}
			extInfo := GetExtInfo(c, content.ExtInfo, post.GameId, post.AreaId, post.Platform, post.PostUUID)
			if extInfo == "" {
				continue
			}
			var postExtData = &PostContentExtCover{
				PostUuid: post.PostUUID,
				ExtInfo:  extInfo,
			}
			channelPostData <- postExtData
		}
	}()
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		defer wg.Done() // 函数结束时减少计数器
		var gtId int64
		for {
			// 同步p_post_external数据
			posts, err := dao.GetPostExternalAllDataBySync(1000, gtId)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostVideoContentCoverTransfer SyncGetAllPost err, err=(%v)", err)
				return
			}

			for _, post := range posts {
				// 只有视频类型才需要转换
				if post.Type != int32(constants.OutVideoPost) {
					continue
				}

				content, err := dao.PostContentGetIgnoreDelete(post.PostUUID)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostVideoContentCoverTransfer Get post content err, post_uuid: [%s], err=(%v)", post.PostUUID, err)
					continue
				}
				if content.ExtInfo == "" {
					continue
				}
				var postContentExtInfo *model.PostContentExtInfo
				var postContentExtInfoList []*model.PostContentExtInfo
				var isArray bool
				err = json.Unmarshal([]byte(content.ExtInfo), &postContentExtInfo)
				if err != nil {
					// 可能这个字符串是一个数组的形式.所以这里需要接住这个报错
					err = json.Unmarshal([]byte(content.ExtInfo), &postContentExtInfoList)
					if err != nil {
						// 记录错误
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostVideoContentCoverTransfer json decode post content ext_info failed, post_uuid: [%s], ext_info:[%s],err=(%v)", post.PostUUID, content.ExtInfo, err)
						continue
					}
					if len(postContentExtInfoList) == 0 {
						// 这个转换之后没有数据就不再继续往下走了,直接进入下一次循环
						continue
					}
					postContentExtInfo = postContentExtInfoList[0]
					isArray = true
				}
				previewCover := postContentExtInfo.VideoPreviewUrl
				if previewCover == "" {
					previewCover = postContentExtInfo.VideoCover
					if previewCover == "" {
						// 如果两个类型都没有值的话就退出转存,进入下一次
						continue
					}
				}
				// 转换cover
				image := attachment.TransferVideoImage(c, 3, previewCover, post.GameId, post.AreaId)
				if image == "" {
					//转换失败了,直接退出
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostVideoContentCoverTransfer transfer post content video cover failed, post_uuid: [%s], cover:[%s],err=(%v)", post.PostUUID, postContentExtInfo.VideoPreviewUrl, err)
					continue
				}
				var extInfo []byte
				postContentExtInfo.VideoCover = image
				postContentExtInfo.Platform = content.Platform
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("PostVideoContentCoverTransfer ext_info: %+v, post_uuid: %s, platform: %s", postContentExtInfo, post.PostUUID, content.Platform)
				if isArray {
					postContentExtInfoList[0] = postContentExtInfo
					extInfo, err = json.Marshal(postContentExtInfoList)
				} else {
					extInfo, err = json.Marshal(postContentExtInfo)
				}
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("PostVideoContentCoverTransfer json encode ext_info: %s, post_uuid: %s", postContentExtInfo, post.PostUUID)
				if err != nil {
					// 记录错误
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostVideoContentCoverTransfer json encode post content ext_info failed, post_uuid: [%s], ext_info:[%+v],err=(%v)", post.PostUUID, postContentExtInfo, err)
					continue
				}
				var postExtData = &PostContentExtCover{
					PostUuid: post.PostUUID,
					ExtInfo:  string(extInfo),
				}
				channelPostData <- postExtData
			}
			if len(posts) < 1000 {
				break
			}
			gtId = posts[len(posts)-1].ID
		}
	}()
	// 启动一个协程来等待所有写入完成后关闭通道
	go func() {
		wg.Wait()
		close(channelPostData)
	}()

	for postCover := range channelPostData {
		err := dao.UpdatePostContentInfo(postCover.PostUuid, map[string]interface{}{
			"ext_info": postCover.ExtInfo,
		})
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostVideoContentCoverTransfer update post content ext_info failed, post_uuid: [%s], ext_info:[%s],err=(%v)", postCover.PostUuid, postCover.ExtInfo, err)
			continue
		}
		if postCover.Official && postCover.PostOfficialID > 0 {
			err = dao.UpdatePostOfficialLanguage(postCover.PostUuid, postCover.PostOfficialID, map[string]interface{}{
				"ext_info": postCover.ExtInfo,
			})
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostVideoContentCoverTransfer update post content ext_info failed, post_uuid: [%s], ext_info:[%s],err=(%v)", postCover.PostUuid, postCover.ExtInfo, err)
				continue
			}
		}
		doc := map[string]interface{}{
			"ext_info": postCover.ExtInfo,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postCover.PostUuid, doc)
	}

	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostContentPlatformToPost end, nowTime: %d", time.Now().Unix())
	return nil
}

func GetExtInfo(c context.Context, extInfo string, gameId, areaId, platform, postUuid string) string {
	var postContentExtInfo *model.PostContentExtInfo
	var postContentExtInfoList []*model.PostContentExtInfo
	var isArray bool
	var extInfoData string
	err := json.Unmarshal([]byte(extInfo), &postContentExtInfo)
	if err != nil {
		// 可能这个字符串是一个数组的形式.所以这里需要接住这个报错

		err = json.Unmarshal([]byte(extInfo), &postContentExtInfoList)
		if err != nil {
			// 记录错误
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetExtInfo json decode post content ext_info failed, post_uuid: [%s], ext_info:[%s],err=(%v)", postUuid, extInfo, err)
			return extInfoData
		}
		if len(postContentExtInfoList) == 0 {
			// 这个转换之后没有数据就不再继续往下走了,直接进入下一次循环
			return extInfoData
		}
		postContentExtInfo = postContentExtInfoList[0]
		isArray = true
	}
	previewCover := postContentExtInfo.VideoPreviewUrl
	if previewCover == "" {
		previewCover = postContentExtInfo.VideoCover
		if previewCover == "" {
			// 如果两个类型都没有值的话就退出转存,进入下一次
			return extInfoData
		}
	}
	// 转换cover
	image := attachment.TransferVideoImage(c, 3, previewCover, gameId, areaId)
	if image == "" {
		//转换失败了,直接退出
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetExtInfo transfer post content video cover failed, post_uuid: [%s], cover:[%s],err=(%v)", postUuid, postContentExtInfo.VideoPreviewUrl, err)
		return extInfoData
	}
	var extInfoByte []byte
	postContentExtInfo.VideoCover = image
	postContentExtInfo.Platform = platform
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetExtInfo ext_info: %+v, post_uuid: %s, platform: %s", postContentExtInfo, postUuid, platform)
	if isArray {
		postContentExtInfoList[0] = postContentExtInfo
		extInfoByte, err = json.Marshal(postContentExtInfoList)
	} else {
		extInfoByte, err = json.Marshal(postContentExtInfo)
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetExtInfo json encode ext_info: %s, post_uuid: %s", postContentExtInfo, postUuid)
	if err != nil {
		// 记录错误
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetExtInfo json encode post content ext_info failed, post_uuid: [%s], ext_info:[%+v],err=(%v)", postUuid, postContentExtInfo, err)
		return extInfoData
	}
	extInfoData = string(extInfoByte)
	return extInfoData
}

func GetPostContentInfo(c context.Context, postUuid, language string) (*model.PostContent, error) {
	postContent := &model.PostContent{}
	postContentRedisKey := cache.GetPostContentKey(postUuid, language)
	postContentCacheInfo, err := redis.GetClient().Get(context.Background(), postContentRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postContentCacheInfo), postContent)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostContentInfo cache json.Unmarshal error.postContentRedisKey: %s, err: %v", postContentRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetPostContentJsonUnmarshalError, "Failed to obtain post content info, data parsing exception")
		} else {
			return postContent, nil
		}
	}
	postContent, err = dao.PostContentGet(postUuid, language)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostContentInfo err, PostContentGet failed, post_uuid:(%s), err=(%v)", postUuid, err)
		return nil, err
	}

	postContentByte, err := json.Marshal(postContent)
	if err == nil {
		redis.GetClient().SetEX(context.Background(), postContentRedisKey, string(postContentByte), 2*time.Minute).Result()
	}

	return postContent, err
}
