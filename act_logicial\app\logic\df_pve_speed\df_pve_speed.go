package df_global_challenge

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	deltaversePb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/deltaverse"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	amsPb "git.code.oa.com/trpcprotocol/publishing_marketing/game_ams"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_pve_speed"
	redisOrgin "github.com/go-redis/redis/v8"
	"google.golang.org/protobuf/proto"
	"trpc.act.logicial/app/global"
	model "trpc.act.logicial/app/model/df_pve_speed"
)

// OboRankTotalHttp TODO
type OboRankTotalHttp struct {
	Data OborankTotalType `json:"data"`
}

// OboRanklndHttp TODO
type OboRanklndHttp struct {
	Data RankInfoItem `json:"data"`
}

// OborankTotalType TODO
type OborankTotalType struct {
	Dtstatdate         string `json:"dtstatdate"`
	Izoneareaid        string `json:"izoneareaid"`
	RankListInfo       string `json:"rank_list_info"`
	TotalRankPlayerNum int64  `json:"total_rank_player_num"`
}

// RankInfoItem TODO
type RankInfoItem struct {
	Vopenid      string `json:"vopenid"`
	Roomid       string `json:"roomid"`
	Izoneareaid  string `json:"izoneareaid"`
	Playtime     string `json:"playtime"`
	Dteventtime  string `json:"dteventtime"`
	PlaytimeRank int64  `json:"playtime_rank"`
	Memberid1    string `json:"memberid1"`
	Memberid2    string `json:"memberid2"`
	Memberid3    string `json:"memberid3"`
}

// RankInfoListItem TODO
type RankInfoListItem struct {
	Vopenid      string `json:"vopenid"`
	Roomid       string `json:"roomid"`
	Izoneareaid  string `json:"izoneareaid"`
	Playtime     string `json:"playtime"`
	Dteventtime  string `json:"dteventtime"`
	PlaytimeRank string `json:"playtime_rank"`
	Memberid1    string `json:"memberid1"`
	Memberid2    string `json:"memberid2"`
	Memberid3    string `json:"memberid3"`
}

// IdipInfoItem TODO
type IdipInfoItem struct {
	Name   string
	Avatar string
}

var loc = time.FixedZone("UTC+0", 0)

// SyncPveSpeedData 同步pve竞速排行数据
func SyncPveSpeedData(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	now := time.Now()
	endTime := time.Date(2025, 1, 17, 0, 0, 0, 0, loc).Unix()
	if now.Unix() >= endTime {
		return
	}
	dateStr := now.AddDate(0, 0, -1).Format("********")
	paramMap := make(map[string]interface{})

	paramMap["date"] = dateStr
	paramMap["zoneareaid"] = "66"
	response, err := deltaversePb.SendRequest(scheduleCtx, deltaversePb.SendRequestParam{
		ServiceType:        "projectd_oversea",
		DestinationService: "dmfeature-13568",
		Path:               "/dmfeature/13568/oboRankTotal",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "http_error").Errorf(
			"oboRankTotal SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		return
	}

	log.WithFieldsContext(scheduleCtx, "log_type", "SyncPveSpeedData").Infof("rsp: %v", response)
	var rspData OboRankTotalHttp
	err = json.Unmarshal([]byte(response), &rspData)
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "SyncPveSpeedData").Infof("err: %v", err)
		_ = DelRedis(scheduleCtx, "df-pve-speed-total-num")
		_ = SetRedis(scheduleCtx, "df-pve-speed-total-num", fmt.Sprintf("%v", 0))
		return
	}

	rankList := make([]*RankInfoItem, 0)
	listInfoStr := strings.Split(rspData.Data.RankListInfo, "#")
	for _, itemStr := range listInfoStr {
		log.WithFieldsContext(scheduleCtx, "log_type", "SyncPveSpeedData").Infof("itemStr: %v", itemStr)
		var rankInfoItem *RankInfoListItem
		err = json.Unmarshal([]byte(strings.ReplaceAll(itemStr, "'", "\"")), &rankInfoItem)
		if err != nil {
			log.WithFieldsContext(scheduleCtx, "log_type", "SyncPveSpeedData").Infof("err: %v", err)
			return
		}
		insertInto := false
		for _, haveItem := range rankList {
			if rankInfoItem.Roomid == haveItem.Roomid {
				insertInto = true
			}
		}
		if !insertInto {
			log.WithFieldsContext(scheduleCtx, "log_type", "SyncPveSpeedData").Infof("rankInfoItem: %+v", rankInfoItem)
			rank, err := strconv.ParseInt(rankInfoItem.PlaytimeRank, 10, 64)
			if err != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "SetInfoListByAms_int64").Infof("PlaytimeRank: %v, err: %v",
					rankInfoItem.PlaytimeRank, err)
				return err
			}
			rankList = append(rankList, &RankInfoItem{
				Vopenid:      rankInfoItem.Vopenid,
				Roomid:       rankInfoItem.Roomid,
				Izoneareaid:  rankInfoItem.Izoneareaid,
				Playtime:     rankInfoItem.Playtime,
				Dteventtime:  rankInfoItem.Dteventtime,
				PlaytimeRank: rank,
				Memberid1:    rankInfoItem.Memberid1,
				Memberid2:    rankInfoItem.Memberid2,
				Memberid3:    rankInfoItem.Memberid3,
			})
		}
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "SyncPveSpeedData").Infof("TotalRankPlayerNum: %v",
		rspData.Data.TotalRankPlayerNum)

	err = DelRedis(scheduleCtx, "df-pve-speed-total-num")
	if err != nil {
		return
	}
	err = SetRedis(scheduleCtx, "df-pve-speed-total-num", fmt.Sprintf("%v", rspData.Data.TotalRankPlayerNum))
	if err != nil {
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "SyncPveSpeedData").Infof("rankList: %+v", rankList)
	err = SetUserInfo(scheduleCtx, rankList)
	if err != nil {
		return
	}
	return
}

// SetUserInfo 获取各个队友数据
func SetUserInfo(ctx context.Context, rankList []*RankInfoItem) (err error) {
	scheduleCtx := context.Background()

	var wg sync.WaitGroup
	semaphore := make(chan struct{}, 50)
	for _, item := range rankList {
		wg.Add(1)
		go func(item *RankInfoItem) (err error) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			now := time.Now()
			dateStr := now.AddDate(0, 0, -1).Format("********")
			playTime, err := strconv.ParseInt(item.Playtime, 10, 64)
			if err != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "SetInfoListByAms_int64").Infof("PlaytimeRank: %v, err: %v",
					item.Playtime, err)
				return
			}
			insertInfo := &model.DfPveSpeedRank{
				Memberid1:   item.Memberid1,
				Memberid2:   item.Memberid2,
				Memberid3:   item.Memberid3,
				Rank:        item.PlaytimeRank,
				Playtime:    playTime,
				Dteventtime: item.Dteventtime,
				Dtstatdate:  dateStr,
			}
			for i := 1; i <= 3; i++ {
				openId := item.Memberid1
				if i == 2 {
					openId = item.Memberid2
				}
				if i == 3 {
					openId = item.Memberid3
				}
				info, err := GetInfoByOpenid(scheduleCtx, openId)
				if err != nil {
					return err
				}
				if i == 1 {
					insertInfo.Name1 = info.Name
					insertInfo.Avatar1 = info.Avatar
				}
				if i == 2 {
					insertInfo.Name2 = info.Name
					insertInfo.Avatar2 = info.Avatar
				}
				if i == 3 {
					insertInfo.Name3 = info.Name
					insertInfo.Avatar3 = info.Avatar
				}
			}
			db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfPveSpeedRank{}.TableName()).
				Where("dtstatdate = ? and memberid1 = ? and memberid2 = ? and memberid3 = ? ", insertInfo.Dtstatdate,
					insertInfo.Memberid1, insertInfo.Memberid2,
					insertInfo.Memberid3).
				FirstOrCreate(&insertInfo)
			if db.Error != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", db.Error.Error())
				return
			}
			return
		}(item)
	}
	wg.Wait()
	err = DelRedis(scheduleCtx, "df-pve-speed-rank-list")
	if err != nil {
		return
	}
	return
}

// DelRedis TODO
func DelRedis(ctx context.Context, key string) (err error) {
	redisPrefixKey := global.GetPrefix()
	hashKey := fmt.Sprintf("%v-%v", redisPrefixKey, key)
	scheduleCtx := context.Background()
	keys, errs := redis.GetClient().Del(scheduleCtx, hashKey).Result()
	if errs != nil {
		err = errs
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[df] rank delete redis keys: [%v]",
		keys))
	return
}

// GetInfoByOpenid TODO
func GetInfoByOpenid(ctx context.Context, openId string) (info *IdipInfoItem, err error) {
	info = &IdipInfoItem{}
	amsProxy := amsPb.NewAmsClientProxy()

	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         fmt.Sprintf("29158-%v", openId),
		AccountType: accountPb.AccountType(1),
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    openId,
			GameId:    "29158",
			ChannelId: 131,
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	IdipParam := make([]*amsPb.IdipGetItem, 0)
	IdipParam = append(IdipParam, &amsPb.IdipGetItem{
		Key:   "AreaId",
		Value: "66",
	})
	amsInfo, err := amsProxy.GetInfoListByAms(ctx, &amsPb.GetInfoListByAmsReq{
		SelectParam: &amsPb.IdipDBParam{
			FeatureType: 9,
			GameId:      "29158",
		},
		IdipParam: IdipParam,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "GetInfoListByAms_err", "str_field_1", openId).Infof("err: %v", err)
		return
	}

	for _, item := range amsInfo.Info {
		if item.Key == "roleName" {
			info.Name = item.Value
		}
		if item.Key == "avatar" {
			info.Avatar = item.Value
		}
	}
	return
}

// SetRedis 设置redis缓存
func SetRedis(ctx context.Context, key string, value string) (err error) {
	redisPrefixKey := global.GetPrefix()
	redisKey := fmt.Sprintf("%v-%v", redisPrefixKey, key)

	ok, errR := redis.GetClient().SetNX(ctx, redisKey, value, 0).Result()
	if errR != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "SetNX redis error err=[%v]", errR.Error())
		return
	}
	if !ok {
		log.WithFieldsContext(ctx, "error").Errorf("set redis unsuccess key:[%v], value:[%v]", redisKey,
			value)
		return
	}
	return
}

// GetRedis 获取reids缓存
func GetRedis(ctx context.Context, key string) (result string, err error) {
	redisPrefixKey := global.GetPrefix()
	redisKey := fmt.Sprintf("%v-%v", redisPrefixKey, key)

	resStr, errR := redis.GetClient().Get(ctx, redisKey).Result()
	if errR != nil && errR != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "getRedis redis error err=[%v]",
			errR.Error())
		return
	}

	result = resStr
	return
}

// GetPveSpeedRankRedis 获取redis排行榜列表
func GetPveSpeedRankRedis(ctx context.Context, pageNum int32) (rankList []*pb.PveRankItem, err error) {
	rankList = make([]*pb.PveRankItem, 0)
	redisPrefixKey := global.GetPrefix()
	rankListRedisKey := fmt.Sprintf("%v-df-pve-speed-rank-list", redisPrefixKey)
	result, err := redis.GetClient().HGetAll(ctx, rankListRedisKey).Result()
	if err != nil && err != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			err.Error())
		return
	}

	dataKey := fmt.Sprintf("%v", pageNum)
	if _, ok := result[dataKey]; ok {
		err = json.Unmarshal([]byte(result[dataKey]), &rankList)
		if err != nil {
			// 告警
			errs.NewCustomError(ctx, 611010, "redis get err,redisKey=%v,val=%v,err=%v", dataKey,
				result[dataKey],
				err)
			return
		}
	} else {
		rankList, err = GetPveSpeedRank(ctx, pageNum)
		if err != nil {
			return
		}

		newRankListJsonStr, errJ := json.Marshal(rankList)
		if errJ != nil {
			err = errJ
			return
		}
		result[dataKey] = string(newRankListJsonStr)
		err = redis.GetClient().HMSet(ctx, rankListRedisKey, result).Err()
		if err != nil {
			return
		}
		expiration := 2 * 24 * time.Hour
		err = redis.GetClient().Expire(ctx, rankListRedisKey, expiration).Err()
		if err != nil {
			return
		}
	}

	return
}

// GetPveSpeedRank 获取排行榜列表
func GetPveSpeedRank(ctx context.Context, pageNum int32) (rankList []*pb.PveRankItem, err error) {
	now := time.Now()
	dateStr := now.AddDate(0, 0, -1).Format("********")
	rankList = make([]*pb.PveRankItem, 0)

	speedRankList := make([]*model.DfPveSpeedRank, 0)
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfPveSpeedRank{}.TableName()).
		Where("dtstatdate = ?", dateStr).
		Limit(10).Offset((int(pageNum) - 1) * 10).
		Order("`rank` asc").Find(&speedRankList)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	for _, item := range speedRankList {
		names := make([]string, 0)
		avatars := make([]string, 0)
		names = append(names, item.Name1)
		names = append(names, item.Name2)
		names = append(names, item.Name3)
		avatars = append(avatars, item.Avatar1)
		avatars = append(avatars, item.Avatar2)
		avatars = append(avatars, item.Avatar3)
		rankList = append(rankList, &pb.PveRankItem{
			PlaytimeRank: int32(item.Rank),
			Playtime:     int32(item.Playtime),
			Names:        names,
			Avatars:      avatars,
		})
	}

	return
}

// GetSelfPveSpeedData 获取当前用户排行榜信息
func GetSelfPveSpeedData(ctx context.Context) (selfItem *pb.PveRankItem, err error) {
	selfItem = &pb.PveRankItem{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	now := time.Now()
	dateStr := now.AddDate(0, 0, -1).Format("********")
	paramMap := make(map[string]interface{})

	paramMap["date"] = dateStr
	paramMap["zoneareaid"] = "66"
	paramMap["openid"] = strings.Split(userAccount.Uid, "-")[1]
	response, err := deltaversePb.SendRequest(ctx, deltaversePb.SendRequestParam{
		ServiceType:        "projectd_oversea",
		DestinationService: "dmfeature-13568",
		Path:               "/dmfeature/13568/oboRankInd",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "http_error").Errorf(
			"oboRankTotal SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		return
	}
	var rspData OboRanklndHttp
	err = json.Unmarshal([]byte(response), &rspData)
	if err != nil {
		return
	}
	if rspData.Data.Memberid1 == "" {
		return
	}
	info1, err := GetInfoByOpenid(ctx, rspData.Data.Memberid1)
	if err != nil {
		return
	}
	info2, err := GetInfoByOpenid(ctx, rspData.Data.Memberid2)
	if err != nil {
		return
	}
	info3, err := GetInfoByOpenid(ctx, rspData.Data.Memberid3)
	if err != nil {
		return
	}
	names := make([]string, 0)
	avatars := make([]string, 0)
	names = append(names, info1.Name)
	names = append(names, info2.Name)
	names = append(names, info3.Name)
	avatars = append(avatars, info1.Avatar)
	avatars = append(avatars, info2.Avatar)
	avatars = append(avatars, info3.Avatar)

	selfItem.Names = names
	selfItem.Avatars = avatars
	playTime, err := strconv.ParseInt(rspData.Data.Playtime, 10, 32)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "GetSelfPveSpeedData_int32").Infof("Playtime: %v, err: %v",
			rspData.Data.Playtime, err)
		return
	}
	selfItem.Playtime = int32(playTime)
	selfItem.PlaytimeRank = int32(rspData.Data.PlaytimeRank)

	return
}
