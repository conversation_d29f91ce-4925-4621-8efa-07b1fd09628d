// Package reservation 预约
package reservation

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/mail"
	"regexp"
	"strconv"
	"strings"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	intlgameLib "git.code.oa.com/iegg_distribution/Marketing_group/act.common/intlgame"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_reservation"
	wegamePb "git.woa.com/trpcprotocol/publishing_marketing/account_wegame"
	redisOrgin "github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/global"
	"trpc.act.logicial/app/model/reservation"
)

// ReservationServiceTmp 结构体
type ReservationServiceTmp struct {
	pb.UnimplementedReservation
}

type GetRegisterInfoResponse struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	RequestID string `json:"request_id"`
	Data      Data   `json:"data"`
}

type Data struct {
	Errmsg         string `json:"errmsg"`
	Info           Info   `json:"info"`
	RegisterStatus int    `json:"register_status"`
	Result         int    `json:"result"`
}

type Info struct {
	Email string `json:"email"`
}

type CheckCNCReservationStatusResponse struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	RequestID string `json:"request_id"`
	Data      struct {
		Errmsg       string `json:"errmsg"`
		SmartLinkRsp string `json:"smart_link_rsp"`
		Result       int    `json:"result"`
	} `json:"data"`
}

type SmartLinkRsp struct {
	Code int              `json:"code"`
	Msg  string           `json:"msg"`
	Data SmartLinkRspData `json:"data"`
}

type SmartLinkRspData struct {
	Openid         string   `json:"openid"`
	Lang           string   `json:"lang"`
	SubscribeState int      `json:"subscribe_state"`
	Email          string   `json:"email"`
	From           string   `json:"from"`
	Country        string   `json:"country"`
	Extra          []Extra  `json:"extra"`
	Tag            []string `json:"tag"`
}

type Extra struct {
	FieldName  string `json:"field_name"`
	FieldValue string `json:"field_value"`
	FieldType  string `json:"field_type"`
}

const (
	// NotReviewStatus 未预约
	NotReviewStatus = 0
	// HasReviewStatus 已预约
	HasReviewStatus = 1
)

// CheckCNCReservationStatus 查询CNC官网预约状态
func (s *ReservationServiceTmp) CheckCNCReservationStatus(ctx context.Context, req *pb.CheckCNCReservationStatusReq,
) (rsp *pb.CheckCNCReservationStatusRsp, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	// 邮箱格式校验
	if ok := CheckEmailFormat(ctx, req.Email); req.Email == "" || !ok {
		return rsp, errs.NewCustomError(ctx, code.MailParamError,
			"CheckCNCReservationStatus email err Email:[%v]", req.Email)
	}
	httpHeader := metadata.GetHTTPHeader(ctx)
	uid := httpHeader.Get("X-Uid")
	ticket := httpHeader.Get("X-Ticket")
	gameId := httpHeader.Get("X-Gameid")
	var conf = config.GetConfig()
	urlConf, ok := conf.ReservationMap[gameId]
	if !ok {
		return rsp, errs.NewCustomError(ctx, code.CurrentGameNotExist,
			"CheckCNCReservationStatus CurrentGameNotExist gameId:[%v]", gameId)
	}
	host := urlConf.Host
	uri := urlConf.Uri
	url := fmt.Sprintf("%v%v", host, uri)
	optionOne := httpclient.ClientOption{
		URL: url,
		Header: map[string]string{
			"Content-Type": "application/json",
			"X-Uid":        uid,
			"X-Ticket":     ticket,
			"X-Gameid":     gameId,
		},
		Type: "POST",
		PostData: map[string]interface{}{
			"type":   2,
			"biz_id": 1,
			"value":  req.Email,
		},
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	if resultOption.RequestError != nil {
		// 请求失败
		err = errs.NewCustomError(ctx, code.OfficialNoAppointmentError,
			"CheckCNCReservationStatus http error, \t [URL]:{%v},[Error]:{%v} ", url, resultOption.RequestError)
		return
	}
	response := resultOption.Result
	var rspData CheckCNCReservationStatusResponse
	var smartLinkRsp SmartLinkRsp
	jsonErr := json.Unmarshal([]byte(response), &rspData)
	if jsonErr != nil {
		err = errs.NewCustomError(ctx, code.JsonParseError,
			"parse result CheckCNCReservationStatusResponse err,result=%v, \t [Error]:{%v} ", response, jsonErr)
		return
	}
	jsonErr = json.Unmarshal([]byte(rspData.Data.SmartLinkRsp), &smartLinkRsp)
	if jsonErr != nil {
		err = errs.NewCustomError(ctx, code.JsonParseError,
			"parse result CheckCNCReservationStatusResponse SmartLinkRsp err,result=%v, \t [Error]:{%v} ", rspData.Data, jsonErr)
		return
	}
	// 官网未预约返回{\"code\":13005,\"msg\":\"user no exist\"}
	if smartLinkRsp.Code == 13005 {
		return nil, errs.NewCustomError(ctx, code.OfficialNoAppointmentError, "No official website reservation")
	}
	// 检查账号信息是否对等
	if smartLinkRsp.Data.Openid != userAccount.IntlAccount.OpenId {
		log.WithFieldsContext(ctx, "log_type", "debug").
			Infof("CheckCNCReservationStatusResponse openid not match, smartLinkRsp.Data.Openid:[%v],userAccount.IntlAccount.OpenId:[%v]",
				smartLinkRsp.Data.Openid, userAccount.IntlAccount.OpenId)
		err = errs.NewCustomError(ctx, code.OfficialNoAppointmentError,
			"openid not match")
		return
	}

	if isReservation := smartLinkRsp.Data.SubscribeState == 1 && smartLinkRsp.Data.Email == req.Email &&
		len(smartLinkRsp.Data.Tag) == 1 && smartLinkRsp.Data.Tag[0] == req.Tag; isReservation {
		return &pb.CheckCNCReservationStatusRsp{
			IsReservation: isReservation,
		}, nil
	} else {
		return nil, errs.NewCustomError(ctx, code.OfficialNoAppointmentError, "No official website reservation")
	}
}

// CheckEmailFormat 检查邮箱格式
func CheckEmailFormat(ctx context.Context, email string) bool {
	//  匹配电子邮箱
	match, err := regexp.MatchString(`\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*`, email)
	if err != nil {
		_ = errs.NewCustomError(ctx, code.MailParamError, "CheckEmailFormat email:[%v],err:%v", email, err)
	}
	return match
}

// CheckReservationStatus 查询官网预约状态
func (s *ReservationServiceTmp) CheckReservationStatus(ctx context.Context, req *pb.CheckReservationStatusReq,
) (rsp *pb.CheckReservationStatusRsp, err error) {
	rsp = &pb.CheckReservationStatusRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var conf = config.GetConfig()
	var host = conf.TarisCheckReservationHost
	var uri = conf.TarisCheckReservationUri
	httpHeader := metadata.GetHTTPHeader(ctx)
	uid := httpHeader.Get("X-Uid")
	ticket := httpHeader.Get("X-Ticket")
	gameid := httpHeader.Get("X-Gameid")
	fmt.Println("---------------uid---------------")
	fmt.Printf("%#v\n", uid)
	// 获取用户信息
	accountProxy := wegamePb.NewWegameClientProxy()
	// 如果账号信息不对等 报错
	accountRsp, errP := accountProxy.GetWtAccountInfo(ctx, &wegamePb.GetWtAccountInfoReq{
		Wt:     ticket,
		UserId: uid,
	})
	if errP != nil {
		err = errP
		return
	}
	fmt.Println("---------------accountRsp---------------")
	fmt.Printf("%#v\n", accountRsp)
	if accountRsp.ThirdOpenid != userAccount.IntlAccount.OpenId {
		err = errs.NewCustomError(ctx, code.OfficialNoAppointmentError,
			"openid not match")
		return
	}
	url := fmt.Sprintf("%v%v", host, uri)
	optionOne := httpclient.ClientOption{
		URL: url,
		Header: map[string]string{
			"Content-Type": "application/json",
			"X-Uid":        uid,
			"X-Ticket":     ticket,
			"X-Gameid":     gameid,
		},
		Type: "POST",
		PostData: map[string]interface{}{
			"type": 1,
		},
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	if resultOption.RequestError != nil {
		// 请求失败
		err = errs.NewCustomError(ctx, code.OfficialNoAppointmentError,
			"CheckReservationStatus http error, \t [URL]:{%v},[Error]:{%v} ", url, resultOption.RequestError)
		return
	}
	response := resultOption.Result
	var rspData GetRegisterInfoResponse
	jsonErr := json.Unmarshal([]byte(response), &rspData)
	if jsonErr != nil {
		err = errs.NewCustomError(ctx, code.JsonParseError,
			"parse result,result=%v, \t [Error]:{%v} ", response, jsonErr)
		return
	}
	if isReservation := rspData.Code == 0 && rspData.Data.Info.Email != ""; isReservation {
		return &pb.CheckReservationStatusRsp{
			IsReservation: isReservation,
		}, nil
	} else {
		return nil, errs.NewCustomError(ctx, code.OfficialNoAppointmentError, "No official website reservation")
	}
}

// DoReservation 预约
func (s *ReservationServiceTmp) DoReservation(ctx context.Context, req *pb.DoReservationReq,
) (rsp *pb.DoReservationRsp, err error) {
	rsp = &pb.DoReservationRsp{}
	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var tableName string
	var reservationData reservation.ReservationData
	tableName, err = model.GetTableNameWithAccount(ctx, &userAccount, reservationData.TableName())
	if err != nil {
		return
	}
	// 新增或查询
	AccountLogCondition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"source_id":    req.FsourceId,
	}

	reviewData := reservation.ReservationData{
		Status: 1,
	}
	// 如果已经预约过了就直接返回
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(AccountLogCondition).Attrs(reviewData).
		FirstOrCreate(&reservationData)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	if db.RowsAffected == 0 && reservationData.Status == HasReviewStatus {
		err = errs.NewCustomError(ctx, code.HasReservation, "has reservation")
		return
	}

	if reservationData.Status == NotReviewStatus {
		db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where("id = ?", reservationData.ID).
			Updates(reviewData)
		if db.Error != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}

		if db.RowsAffected == 0 {
			err = errs.NewCustomError(ctx, code.HasReservation, "has reservation")
			return
		}
	}
	log.WithFieldsContext(ctx, "log_type", "reservation", "source_id", req.FsourceId).Infof("do reservation")
	rsp = &pb.DoReservationRsp{
		ReservationResult: true,
	}
	return
}

// HasReservation 是否预约
func (s *ReservationServiceTmp) HasReservation(ctx context.Context, req *pb.HasReservationReq) (
	rsp *pb.HasReservationRsp, err error) {
	rsp = &pb.HasReservationRsp{}
	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	// 查询预约
	var tableName string
	var reservationData reservation.ReservationData

	tableName, err = model.GetTableNameWithAccount(ctx, &userAccount, reservationData.TableName())
	// 新增或查询
	AccountLogCondition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"source_id":    req.FsourceId,
		"status":       HasReviewStatus,
	}
	if errM := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(AccountLogCondition).
		Assign(reservation.ReservationData{
			Status: 1}).First(&reservationData).Error; errM != nil {
		if errors.Is(errM, gorm.ErrRecordNotFound) {
			err = errs.NewCustomError(ctx, code.HasNotReservation, "not reservation")
		} else {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", errM.Error())
		}
		return
	}
	rsp = &pb.HasReservationRsp{
		HasReservation: true,
	}
	return
}

func (s *ReservationServiceTmp) GetReservationNum(ctx context.Context, req *pb.GetReservationNumReq) (rsp *pb.GetReservationNumRsp, err error) {
	rsp = &pb.GetReservationNumRsp{}
	// 从redis 获取，
	redisKey := fmt.Sprintf("%s_%s_%s", global.GetPrefix(), "reservations_", req.FsourceId)
	pipeline := redis.GetClient().Pipeline()
	defer pipeline.Close()
	existsCmd := pipeline.Exists(ctx, redisKey)
	valCmd := pipeline.Get(ctx, redisKey)
	_, errR := pipeline.Exec(ctx)
	if errR != nil && errR != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errR.Error())
		return
	}
	existsVal, _ := existsCmd.Result()
	if existsVal == 0 {

	}
	fmt.Printf("%#v\n", valCmd)
	return
}
func (s *ReservationServiceTmp) ScheduleCreateReservationNum(ctx context.Context, req *pb.ScheduleCreateReservationNumReq) (rsp *pb.ScheduleCreateReservationNumRsp, err error) {
	rsp = &pb.ScheduleCreateReservationNumRsp{}
	return
}

// EmailReservationWithNoLogin 邮件预约
func (s *ReservationServiceTmp) EmailReservationWithNoLogin(ctx context.Context, req *pb.EmailReservationWithNoLoginReq,
) (rsp *pb.EmailReservationWithNoLoginRsp, err error) {
	rsp = &pb.EmailReservationWithNoLoginRsp{}
	_, err = mail.ParseAddress(req.Email)

	// 将提交的选项与总选项取交集
	req.CommitList = Int64GetIntersection(req.CommitList, req.OptionList)
	// 看提交的列表是否包含必选项
	if !Int64RequiredContains(req.CommitList, req.RequiredList) {
		err = errs.NewCustomError(ctx, code.ReservationEamilRequiredOptionError, fmt.Sprintf(
			"Invalid checked list:%v required_list:%v", req.CommitList, req.RequiredList))
		return
	}
	var tableName string
	var reservationData reservation.EmailReservationData
	tableName, err = model.GetTableNameWithEmail(ctx, req.Email, reservationData.TableName())
	if err != nil {
		return
	}
	// 新增或查询
	AccountLogCondition := reservation.EmailReservationData{
		Email:    req.Email,
		SourceID: req.FsourceId,
	}
	commitListStr := Int64SliceToString(req.CommitList)
	reviewData := reservation.EmailReservationData{
		ReservationInfo: commitListStr,
		LangType:        req.LangType,
		Status:          1,
	}
	// 如果已经预约过了就修改当前选项
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(AccountLogCondition).Attrs(reviewData).
		FirstOrCreate(&reservationData)
	if db.Error != nil {
		if strings.Contains(db.Error.Error(), "Error 1062") {
			err = errs.NewCustomError(ctx, code.HasReservation, "has reservation")
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error FirstOrCreate err, \t [Error]:{%v} ", db.Error.Error())
		// 将报错的SQL信息打印出来
		if db.Statement != nil {
			log.WithFieldsContext(ctx, "log_type", "sql_monitor", "source_id", req.FsourceId).
				Infof("EmailReservationWithNoLogin show err SQL,SQL=%s", db.Statement.SQL.String())
		}
		return
	}
	if db.RowsAffected == 0 {
		if reservationData.Status == HasReviewStatus && reservationData.ReservationInfo ==
			commitListStr && reservationData.LangType == req.LangType {
			err = errs.NewCustomError(ctx, code.HasReservation, "has reservation")
			return
		} else {
			dbUpdates := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where("id = ?", reservationData.ID).
				Updates(reviewData)
			if dbUpdates.Error != nil {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error dbUpdates err, \t [Error]:{%v} ", db.Error.Error())
				return
			}
		}
	}

	log.WithFieldsContext(ctx, "log_type", "email_reservation", "source_id", req.FsourceId).
		Infof("email reservation,info=%v", commitListStr)
	rsp = &pb.EmailReservationWithNoLoginRsp{
		Result: true,
	}

	return
}

// DoUndawnReservation undawn预约
func (s *ReservationServiceTmp) DoUndawnReservation(ctx context.Context, req *pb.DoUndawnReservationReq) (
	rsp *pb.DoUndawnReservationRsp, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)

	if err != nil {
		return
	}
	if userAccount.GetAccountType() != accountPb.AccountType_WeGameApp {
		err = errs.NewCustomError(ctx, code.ReservationUndawnAccountTypeWrong, "wrong account_type =%v",
			userAccount.GetAccountType())
		return
	}
	reqData := intlgameLib.IntlGameReq{
		ChannelId: 4,
		GameId:    req.GameId,
		Path:      "/intl/mgw/invoke",
		QueryParam: map[string]interface{}{
			"r": "/cosg.a20230421apreregister.a20230421apreregister_interface/Subscribe",
		},
		BodyParam: map[string]interface{}{
			"mobile": userAccount.GetWegameAppAccount().GetPhone(),
		},
	}

	var respStr string
	respStr, err = intlgameLib.SendIntlGame(ctx, "undawn_order", reqData)
	type respStruct struct {
		Ret  int         `json:"ret"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}
	var resData respStruct
	errM := json.Unmarshal([]byte(respStr), &resData)
	if errM != nil {
		err = errs.NewCustomError(ctx, code.ReservationUndawnError, "error parse faild ,val = %v", errM.Error())
		return
	}
	// 已预约
	if resData.Ret == 3518908 {
		err = errs.NewCustomError(ctx, code.HasReservation, "has reservation")
		return
	}
	if resData.Ret != 0 {
		err = errs.NewCustomError(ctx, code.ReservationUndawnError, "reservation error ret=%v,msg=%v", resData.Ret,
			resData.Msg)
		return
	}
	return
}

// HasNotUndawnReservation undawn是否未预约
func (s *ReservationServiceTmp) HasNotUndawnReservation(ctx context.Context,
	req *pb.HasNotUndawnReservationReq) (rsp *pb.HasNotUndawnReservationRsp, err error) {
	rsp = &pb.HasNotUndawnReservationRsp{}
	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	if userAccount.GetAccountType() != accountPb.AccountType_WeGameApp {
		err = errs.NewCustomError(ctx, code.ReservationUndawnAccountTypeWrong, "wrong account_type =%v",
			userAccount.GetAccountType())
		return
	}
	reqData := intlgameLib.IntlGameReq{
		ChannelId: 4,
		GameId:    req.GameId,
		Path:      "/intl/mgw/invoke",
		QueryParam: map[string]interface{}{
			"r": "/cosg.a20230421apreregister.a20230421apreregister_interface/QuerySubscribe",
		},
		BodyParam: map[string]interface{}{
			"mobile": userAccount.GetWegameAppAccount().GetPhone(),
		},
	}

	var respStr string
	respStr, err = intlgameLib.SendIntlGame(ctx, "undawn_order", reqData)
	type respStruct struct {
		Ret  int         `json:"ret"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}
	var resData respStruct
	errM := json.Unmarshal([]byte(respStr), &resData)
	if errM != nil {
		err = errs.NewCustomError(ctx, code.ReservationUndawnError, "error parse faild ,val = %v", errM.Error())
		return
	}
	// 已预约
	if resData.Ret == 3518908 {
		err = errs.NewCustomError(ctx, code.HasReservation, "has reservation")
		return
	}
	if resData.Ret != 0 {
		err = errs.NewCustomError(ctx, code.ReservationUndawnError, "reservation error ret=%v,msg=%v", resData.Ret,
			resData.Msg)
		return
	}

	return
}

// Int64SliceToString int64切片转字符串
func Int64SliceToString(slice []int64) string {
	var buffer bytes.Buffer
	for i, n := range slice {
		if i > 0 {
			buffer.WriteString(",")
		}
		buffer.WriteString(strconv.FormatInt(n, 10))
	}
	return buffer.String()
}

// Int64RequiredContains int64数组包含
func Int64RequiredContains(a, b []int64) bool {
	set := make(map[int64]bool)
	for _, v := range a {

		set[v] = true
	}
	for _, v := range b {
		if !set[v] {
			return false
		}
	}
	return true
}

// Int64GetIntersection int64 取交集
func Int64GetIntersection(a, b []int64) []int64 {
	m := make(map[int64]bool)
	for _, item := range a {
		m[item] = true
	}
	n := make([]int64, 0)
	for _, item := range b {
		if _, ok := m[item]; ok {
			n = append(n, item)
		}
	}
	return n
}
