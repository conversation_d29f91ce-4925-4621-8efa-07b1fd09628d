package user

import (
	"context"
	"encoding/json"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/dao"
)

type Links struct {
	ChannelName string `json:"channel_name"`
	Url         string `json:"url"`
}

func GetUserLinks(c context.Context, intlOpenid string) (*pbUser.GetUserLinksRsp, error) {
	var userLinks []*Links

	links, err := dao.GetUserLinks(intlOpenid)
	if err != nil {
		return nil, err
	}
	if links.HomePageLinks == "" {
		return &pbUser.GetUserLinksRsp{}, nil
	}
	err = json.Unmarshal([]byte(links.HomePageLinks), &userLinks)
	if err != nil {
		return nil, err
	}
	var resp = &pbUser.GetUserLinksRsp{
		Links: make([]*pbUser.UserLinkItem, 0),
	}
	for _, link := range userLinks {
		resp.Links = append(resp.Links, &pbUser.UserLinkItem{
			ChannelName: link.ChannelName,
			Url:         link.Url,
		})
	}
	return resp, nil
}

func SetUserLinks(c context.Context, intlOpenid string, req *pbUser.SetUserLinksReq) error {
	var userLinks = make([]*Links, 0)
	for _, link := range req.Links {
		userLinks = append(userLinks, &Links{
			ChannelName: link.ChannelName,
			Url:         link.Url,
		})
	}
	marshal, err := json.Marshal(&userLinks)
	if err != nil {
		return err
	}
	err = dao.SetUserLinks(intlOpenid, string(marshal))
	if err != nil {
		return err
	}

	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		// 删除用户缓存
		DeleteUserInfoCache(context.Background(), intlOpenid)

		doc := map[string]interface{}{
			"home_page_links": string(marshal),
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
		dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	}()

	return nil
}
