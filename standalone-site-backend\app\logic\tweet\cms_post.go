package tweet

import (
	"context"
	"fmt"
	"sync"
	"time"

	hotService "trpc.publishing_application.standalonesite/app/logic/hot"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	monitorPb "git.woa.com/trpcprotocol/publishing_application/standalonesite_monitor"
	es7 "github.com/olivere/elastic/v7"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	userService "trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

var (
	monitorProxy = monitorPb.NewMonitorClientProxy()
)

type PostReviewHandlerChannel struct {
	HandlerFunc func()
}

// CMSAddPost 提供给CMS管理端发布动态后，通知过来更新ES数据, isMachineReview：是否是机审
func CMSReviewPost(c context.Context, req *pb.CMSReviewPostReq, isMachineReview bool) error {
	req.PostAuditIds = util.RemoveDuplicateInt64(req.PostAuditIds)
	req.PostUuids = util.RemoveDuplicateString(req.PostUuids)
	if len(req.PostUuids) == 0 && len(req.PostAuditIds) == 0 {
		return nil
	}
	if len(req.PostAuditIds) > 0 {
		return CMSReviewPostV2(c, req, isMachineReview)
	} else {
		return CMSReviewPostV1(c, req, isMachineReview)
	}

}

func CMSReviewPostV1(c context.Context, req *pb.CMSReviewPostReq, isMachineReview bool) error {
	var errData error
	var wg sync.WaitGroup
	posts, errData := dao.GetPostNoIgnoreDels(req.PostUuids)
	if errData != nil {
		return errs.NewCustomError(c, code.GetPostsFailed, "CMSReviewPost | get post failed")
	}
	if len(posts) != len(req.PostUuids) {
		// 传递进来的数据和查询到的数据不对等，需要报错
	}
	// 获取用户数据
	var userIntlOpenidMaps = make(map[string]bool)
	for _, post := range posts {
		userIntlOpenidMaps[post.IntlOpenid] = true
	}
	var userIntlOpenids = make([]string, 0, len(userIntlOpenidMaps))
	for userIntlOpenid, _ := range userIntlOpenidMaps {
		userIntlOpenids = append(userIntlOpenids, userIntlOpenid)
	}
	userStatsList, errData := dao.GetUserStateByUserOpenids(userIntlOpenids)
	if errData != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost get user satte failed, user intl openid: %v, err:[%v]", userIntlOpenids, errData)
		return errs.NewCustomError(c, code.GetUserStateError, "CMSReviewPost | get user stats failed")
	}
	var userStatsHandlerChannel = make(chan *model.UserState, 1)
	for _, postUUID := range req.PostUuids {
		var post *model.Post
		var userState *model.UserState
		for _, postItem := range posts {
			if postUUID == postItem.PostUUID {
				post = postItem
				break
			}
		}
		for _, state := range userStatsList {
			if post.IntlOpenid == state.IntlOpenid {
				userState = state
			}
		}

		wg.Add(1)
		go func(post *model.Post, userState *model.UserState, req *pb.CMSReviewPostReq, isMachineReview bool) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器
			if post.IsDel == 1 {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost ,post has been deleted, postUUID is %s", post.PostUUID)
				return
			}
			if constants.PostReviewT(req.Type) == constants.PostReviewPass {
				err := PostReviewPass(context.Background(), post, req.UpdateUser, req.AuditIntroduce, isMachineReview)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost ,post review pass, postUUID is %s, err: %v", post.PostUUID, err)
					errData = err
					return
				}
				// 更新用户表动态数
				userState.PostNum++
				userStatsHandlerChannel <- userState
			} else {
				err := PostReviewReject(context.Background(), post, userState, req.UpdateUser, req.AuditIntroduce, req.DelReason)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost ,post review reject, postUUID is %s, err: %v", post.PostUUID, err)
					errData = err
					return
				}
				// 更新用户表动态数
				userState.PostNum--
				if userState.PostStarNum < 0 {
					userState.PostNum = 0
				}
				userState.AllPostNum--
				if userState.AllPostNum < 0 {
					userState.AllPostNum = 0
				}
				userStatsHandlerChannel <- userState
			}
			go userService.DeleteUserInfoCache(c, post.IntlOpenid)
			go cache.DeletePostCache(c, post.PostUUID)
		}(post, userState, req, isMachineReview)
	}
	// 启动一个协程来收集结果
	go func() {
		for state := range userStatsHandlerChannel {
			cErr := dao.UpdateUserPostNum(state.IntlOpenid, state.AllPostNum, state.PostNum)
			if cErr != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost update user post num, user intl openid: %s, err: %v", state.IntlOpenid, cErr)
				continue
			}
			userDoc := map[string]interface{}{
				"post_num": state.PostNum,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, state.IntlOpenid, userDoc)
		}
	}()
	wg.Wait()
	close(userStatsHandlerChannel)
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)

	return errData
}
func CMSReviewPostV2(c context.Context, req *pb.CMSReviewPostReq, isMachineReview bool) error {
	var errData error
	var wg sync.WaitGroup
	postAuditList, errData := dao.GetPostAuditListByIDs(req.PostAuditIds)
	if errData != nil {
		return errs.NewCustomError(c, code.GetPostsFailed, "CMSReviewPostV2 | get post audit failed")
	}
	var postUuids []string
	var notLatestId []int64
	for _, postAuditItem := range postAuditList {
		if postAuditItem.Status != constants.PostAuditUnHandler {
			// 如果不是未处理的状态就要返回回去
			notLatestId = append(notLatestId, postAuditItem.ID)
			continue
		}
		postUuids = append(postUuids, postAuditItem.PostUUID)
	}
	if len(notLatestId) > 0 {
		// 目前错误信息中抛出这个切片列表会导致错误信息过长；直接返回成功带不上已处理的数据；
		return errs.NewCustomError(c, code.StatusIsChanged, "review post failed, post audit status abnormal! id: [%v]", notLatestId)
	}
	if len(postUuids) == 0 {
		// 没有需要审核的数据，直接退出
		// todo 这个可能会存在一种情况，cms传递了一份空数据过来也会报这个状态异常
		return errs.NewCustomError(c, code.StatusIsChanged, "review post failed, post is empty!")
	}
	posts, errData := dao.GetPostNoIgnoreDels(postUuids)
	if errData != nil {
		return errs.NewCustomError(c, code.GetPostsFailed, "CMSReviewPostV2 | get post failed")
	}
	// 获取用户数据
	var userIntlOpenidMaps = make(map[string]bool)
	for _, post := range posts {
		userIntlOpenidMaps[post.IntlOpenid] = true
	}
	var userIntlOpenids = make([]string, 0, len(userIntlOpenidMaps))
	for userIntlOpenid, _ := range userIntlOpenidMaps {
		userIntlOpenids = append(userIntlOpenids, userIntlOpenid)
	}
	userStatsList, errData := dao.GetUserStateByUserOpenids(userIntlOpenids)
	if errData != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPostV2 get user satte failed, user intl openid: %v, err:[%v]", userIntlOpenids, errData)
		return errs.NewCustomError(c, code.GetUserStateError, "CMSReviewPostV2 | get user stats failed")
	}
	var userStatsHandlerChannel = make(chan *model.UserState, 1)
	for _, postAuditItem := range postAuditList {
		var post *model.Post
		var userState *model.UserState
		for _, postItem := range posts {
			if postAuditItem.PostUUID == postItem.PostUUID {
				post = postItem
				break
			}
		}
		for _, state := range userStatsList {
			if post.IntlOpenid == state.IntlOpenid {
				userState = state
			}
		}

		wg.Add(1)
		go func(post *model.Post, postAuditInfo *model.PostAudit, userState *model.UserState, req *pb.CMSReviewPostReq, isMachineReview bool) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器
			if post.IsDel == 1 {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPostV2,post has been deleted, postUUID is %s", post.PostUUID)
				return
			}
			if constants.PostReviewT(req.Type) == constants.PostReviewPass {
				err := PostReviewPassV2(context.Background(), post, postAuditInfo, req.UpdateUser, req.AuditIntroduce, isMachineReview)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPostV2,post review pass, postUUID is %s, err: %v", post.PostUUID, err)
					errData = err
					return
				}
				if postAuditInfo.PostActionType == constants.PostAuditActionAdd {
					// 更新用户表动态数
					userState.PostNum++
					userStatsHandlerChannel <- userState
				}
			} else {
				err := PostReviewRejectV2(context.Background(), post, postAuditInfo, userState, req.UpdateUser, req.AuditIntroduce, req.DelReason)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPostV2,post review reject, postUUID is %s, err: %v", post.PostUUID, err)
					errData = err
					return
				}
				// 更新用户表动态数
				userState.PostNum--
				if userState.PostStarNum < 0 {
					userState.PostNum = 0
				}
				userState.AllPostNum--
				if userState.AllPostNum < 0 {
					userState.AllPostNum = 0
				}
				userStatsHandlerChannel <- userState
			}
			go userService.DeleteUserInfoCache(c, post.IntlOpenid)
			go cache.DeletePostCache(c, post.PostUUID)
		}(post, postAuditItem, userState, req, isMachineReview)
	}
	// 启动一个协程来收集结果
	go func() {
		for state := range userStatsHandlerChannel {
			cErr := dao.UpdateUserPostNum(state.IntlOpenid, state.AllPostNum, state.PostNum)
			if cErr != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPostV2 update user post num, user intl openid: %s, err: %v", state.IntlOpenid, cErr)
				continue
			}
			userDoc := map[string]interface{}{
				"post_num": state.PostNum,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, state.IntlOpenid, userDoc)
		}
	}()
	wg.Wait()
	close(userStatsHandlerChannel)
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetAuditIndex)

	return errData
}

// CMSDeletePost 提供给CMS管理端删除动态后，通知过来更新ES数据
func CMSDeletePost(c context.Context, req *pb.CMSReviewPostReq) error {
	req.PostUuids = util.RemoveDuplicateString(req.PostUuids)
	if len(req.PostUuids) == 0 {
		return nil
	}
	//senderUserID, err := strconv.ParseInt(param.GameID, 10, 64)
	//if err != nil {
	//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost get senderUserID err: %v", err)
	//	return errs.NewCustomError(c, code.GetOfficialGameUserError, "CMSDeletePost | Failed to obtain official user account.")
	//}

	// 查询哪些帖子被删除了
	count, err := dao.GetNotDeletePostCount(req.PostUuids)
	if err != nil {
		return errs.NewCustomError(c, code.GetPostFailed, "CMSDeletePost | get post data failed")
	}
	if count != int64(len(req.PostUuids)) {
		return errs.NewCustomError(c, code.StatusIsChanged, "CMSDeletePost | There are posts that have been deleted")
	}

	for _, postUUID := range req.PostUuids {
		if postUUID == "" {
			continue
		}
		var message []*model.Message

		post, err := dao.GetPostNoIgnoreDel(postUUID)
		if err != nil {
			return errs.NewCustomError(c, code.GetPostsFailed, "CMSDeletePost | get post failed")
		}

		if post.IsDel == 1 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost ,post has been deleted, postUUID is %s", postUUID)
			continue
		}
		var postTitle string
		if postContent, err := dao.PostContentGetIgnoreDelete(post.PostUUID); err == nil {
			postTitle = postContent.Title
		}

		userState, err := dao.GetUserStateByUserOpenid(post.IntlOpenid)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost get user info failed, postUUID is %s, user intl openid: %s, err:[%v]", postUUID, post.IntlOpenid, err)
			continue
		}
		var delType = constants.BAdminDeleted
		if req.Type == int32(constants.PostReviewDeleteByReport) {
			delType = constants.ReportBAdminDeleted
			// 获取审核详情
			postReport, err := dao.PostReportGetRowWithIgnoreDelete(&dao.PostReportConditions{
				ContentUuid: postUUID,
				ContentType: 1,
			})
			if err != nil {
				return errs.NewCustomError(c, code.GetPostAuditFailed, "CMSDeletePost | get post report data failed")
			}

			message = append(message, &model.Message{
				Type:                   constants.MsgTypeOfficialDeletePost,
				PostUUID:               post.PostUUID,
				GameID:                 post.GameId,
				AreaID:                 post.AreaId,
				Content:                postTitle, // 因为这条动态被删除了，用户也看不到了，更不可能再去编辑了，直接写死当前动态的标题
				ReceiverUserIntlOpenid: post.IntlOpenid,
				SenderUserIntlOpenid:   fmt.Sprintf("%s", post.GameId),
				ExtInfo:                fmt.Sprintf("{\"del_type\": %d, \"del_reason\": %d}", delType, req.DelReason),
			})

			message = append(message, &model.Message{
				Type:                   constants.MsgTypeReportMessage,
				PostUUID:               post.PostUUID,
				GameID:                 post.GameId,
				AreaID:                 post.AreaId,
				Content:                postTitle, // 因为这条动态被删除了，用户也看不到了，更不可能再去编辑了，直接写死当前动态的标题
				ReceiverUserIntlOpenid: postReport.ReportIntlOpenid,
				SenderUserIntlOpenid:   fmt.Sprintf("%s", post.GameId),
			})
		} else {
			message = append(message, &model.Message{
				Type:                   constants.MsgTypeOfficialDeletePost,
				PostUUID:               post.PostUUID,
				GameID:                 post.GameId,
				AreaID:                 post.AreaId,
				ReceiverUserIntlOpenid: post.IntlOpenid,
				Content:                postTitle, // 因为这条动态被删除了，用户也看不到了，更不可能再去编辑了，直接写死当前动态的标题
				SenderUserIntlOpenid:   fmt.Sprintf("%s", post.GameId),
				ExtInfo:                fmt.Sprintf("{\"del_type\": %d, \"del_reason\": %d}", delType, req.DelReason),
			})
		}
		UpdateTagReferencesToPost(c, postUUID)
		postLanguage := make([]string, 0)
		if post.IsOfficial != 1 {
			// 非官方帖子直接传入对应的语言删除，官方帖子直接删全部语言
			postLanguage = append(postLanguage, post.Language)
		}

		// 更新动态删除状态，更新审核记录状态
		err = dao.DeletePostInfoAndLanguage(postUUID, map[string]interface{}{
			"is_del":     1,
			"deleted_on": time.Now().Unix(),
			"del_reason": req.DelReason,
			"del_type":   delType,
		}, postLanguage)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost DeletePost err: %v, postUUID is %s", err, postUUID)
			//go func(ctx context.Context, err2 error) {
			//	newC := trpc.CloneContext(ctx)
			//	defer recovery.CatchGoroutinePanic(newC)
			//	common.ReportPostDelete(c, 30054, "", "", post, err2)
			//}(c, err)
			return errs.NewCustomError(c, code.DeletePostFailed, "CMSDeletePost | delete post failed")
		}

		//go func(ctx context.Context) {
		//	newC := trpc.CloneContext(ctx)
		//	defer recovery.CatchGoroutinePanic(newC)
		//	common.ReportPostDelete(c, 30054, "", "", post, nil)
		//}(c)

		// 删除审批记录数据
		postAuditUpdateData := map[string]interface{}{
			"status":          constants.PostAuditIgnore,
			"modified_on":     time.Now().Unix(),
			"audit_introduce": req.AuditIntroduce,
		}

		cErr := dao.UpdatePostAuditInfo(post.PostUUID, postAuditUpdateData)
		if cErr != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostAuditInfo err: %v", err)
			return errs.NewCustomError(c, code.UpdatePostAuditErr, "Failed to update post audit info.")
		}
		DeletePostAuditESData(c, post.PostUUID, req.AuditIntroduce)

		// 更新用户表动态数
		userState.PostNum--
		if userState.PostStarNum < 0 {
			userState.PostNum = 0
		}
		userState.AllPostNum--
		if userState.AllPostNum < 0 {
			userState.AllPostNum = 0
		}
		cErr = dao.UpdateUserPostNum(post.IntlOpenid, userState.AllPostNum, userState.PostNum)
		if cErr != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost update user post num, user intl openid: %s, err: %v", userState.IntlOpenid, err)
			return errs.NewCustomError(c, code.UpdatePostAuditErr, "Failed to update post audit info.")
		}
		userDoc := map[string]interface{}{
			"all_post_num": userState.AllPostNum,
			"post_num":     userState.PostNum,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, userState.IntlOpenid, userDoc)

		// 删除动态相关消息通知， 24-10-22产品确定不删除消息
		//msgTypes := []int64{1, 2, 3, 4, 5, 9, 10}
		//err = dao.MessageDeleteByPostIdNew(postUUID, msgTypes)
		//if err != nil {
		//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost DeletePost err: %v, postUUID is %s", err, postUUID)
		//	return errs.NewCustomError(c, code.DeleteMessageByPostIDFailed, "CMSDeletePost | Delete dynamic related message notification exception.")
		//}
		// 删除索引
		// DeleteSearchPost(post)
		// 更新es
		postDoc := map[string]interface{}{
			"is_del":          1,
			"deleted_on":      time.Now().Unix(),
			"audit_introduce": req.AuditIntroduce,
			"del_reason":      req.DelReason,
			"del_type":        delType,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUUID, postDoc)

		postHotRedisKey := cache.GetPostHotKey(post.GameId, post.AreaId)
		// 删除post redis中对应的postid
		redis.GetClient().ZRem(c, postHotRedisKey, post.PostUUID)

		// 删除redis的待审核数据
		if post.IsAudit == 2 {
			redis.GetClient().ZRem(context.Background(), cache.GetNeedAuditPostUUIDKey(), post.PostUUID)
		}

		// 根据parentid将所有未审核的评论设置成已忽略状态
		// reviewParam := thirdplatform.PunishCommentConditionParam{
		// 	ParentID:          fmt.Sprintf("%d", postID),
		// 	UpdateUser:        param.UpdateUser,
		// 	PunishReason:      "管理端删除动态",
		// 	GameID:            post.GameId,
		// 	AreaID:            post.AreaId,
		// 	PunishCommentType: constants.PUNISH_COMMENT_TYPE_PARENTID_NOTREVIEW,
		// }
		// reviewErrCode := thirdplatform.PunishCommentByUid(reviewParam)
		// if reviewErrCode != nil {
		// 	return reviewErrCode
		// }
		go func() {
			defer recovery.CatchGoroutinePanic(context.Background())
			for _, m := range message {
				writemessage.SetUserMessage(m, m.ReceiverUserIntlOpenid, constants.SiteMessageCount)
			}
		}()
		go userService.DeleteUserInfoCache(c, post.IntlOpenid)

		// 删除用户收藏
		go func(postUuid string) {
			defer recovery.CatchGoroutinePanic(context.Background())
			dao.PostCollectionDeleteByPostUuid(postUuid)
			DelOfficialPublishPostByCache(context.Background(), postUuid)
			cache.DeletePostCache(context.Background(), postUuid)
		}(postUUID)

		go cache.DeleteUserPostsCache(post.IntlOpenid, "", 10)

	}
	err = CMSUpdatePostReport(c, req, false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost CMSUpdatePostReport err: %v, req is %s", err, req)
		return errs.NewCustomError(c, code.UpdateContentReportError, "CMSDeletePost | ignore post report failed")
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetAuditIndex)
	return nil
}

// CMSSetPostEssenceOn 提供给CMS管理端动态加精
func CMSSetPostEssenceOn(c context.Context, req *pb.CMSReviewPostReq) error {
	req.PostUuids = util.RemoveDuplicateString(req.PostUuids)
	if len(req.PostUuids) == 0 {
		return nil
	}
	//senderUserID, err := strconv.ParseInt(param.GameID, 10, 64)
	//if err != nil {
	//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost get senderUserID err: %v", err)
	//	return errs.NewCustomError(c, code.GetOfficialGameUserError, "CMSDeletePost | Failed to obtain official user account.")
	//}

	var endTime int64
	if req.Days > 0 {
		// 获取当前时间
		now := time.Now()
		// 计算几天后的时间
		daysLater := now.Add(time.Duration(req.Days*24) * time.Hour)
		// 获取几天后时间的时间戳（以秒为单位）
		endTime = daysLater.Unix()
	}
	for _, postUUID := range req.PostUuids {
		if postUUID == "" {
			continue
		}
		if constants.PostReviewT(req.Type) == constants.PostReviewEssenceOn {
			if endTime == 0 {
				return errs.NewCustomError(c, code.InvalidParams, "CMSSetPostEssenceOn | set post essence failed, empty days, postUUID: %s", postUUID)
			}
			posContentUpdateData := map[string]interface{}{
				"is_essence":  1,
				"essence_on":  endTime,
				"modified_on": time.Now().Unix(),
			}
			err := dao.UpdatePostContentInfo(postUUID, posContentUpdateData)
			if err != nil {
				return errs.NewCustomError(c, code.UpdatePostContentError, "CMSSetPostEssenceOn | set post essence failed, err: %v, postUUID: %s", err, postUUID)
			}

			// 更新es
			doc := map[string]interface{}{
				"is_essence":  1,
				"essence_on":  endTime,
				"modified_on": time.Now().Unix(),
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUUID, doc)
		} else {
			posContentUpdateData := map[string]interface{}{
				"is_essence":  0,
				"essence_on":  0,
				"modified_on": time.Now().Unix(),
			}
			err := dao.UpdatePostContentInfo(postUUID, posContentUpdateData)
			if err != nil {
				return errs.NewCustomError(c, code.UpdatePostContentError, "CMSSetPostEssenceOn | set post essence failed, err: %v, postUUID: %s", err, postUUID)
			}

			// 更新es
			doc := map[string]interface{}{
				"is_essence":  0,
				"essence_on":  0,
				"modified_on": time.Now().Unix(),
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUUID, doc)
		}
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)
	return nil
}

// CMSSetPostTopOn 提供给CMS管理端动态置顶
func CMSSetPostTopOn(c context.Context, req *pb.CMSReviewPostReq) error {
	req.PostUuids = util.RemoveDuplicateString(req.PostUuids)
	if len(req.PostUuids) == 0 {
		return nil
	}
	//senderUserID, err := strconv.ParseInt(param.GameID, 10, 64)
	//if err != nil {
	//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost get senderUserID err: %v", err)
	//	return errs.NewCustomError(c, code.GetOfficialGameUserError, "CMSDeletePost | Failed to obtain official user account.")
	//}

	for _, postUUID := range req.PostUuids {
		if postUUID == "" {
			continue
		}
		if constants.PostReviewT(req.Type) == constants.PostReviewTopOn {
			var endTime int64
			// 只有加置顶才需要这个时间判断，取消置顶不需要
			if req.Days > 0 {
				// 获取当前时间
				now := time.Now()
				// 计算几天后的时间
				daysLater := now.Add(time.Duration(req.Days*24) * time.Hour)
				// 获取几天后时间的时间戳（以秒为单位）
				endTime = daysLater.Unix()
			}
			if endTime == 0 {
				return errs.NewCustomError(c, code.InvalidParams, "CMSSetPostEssenceOn | set post essence failed, empty days, postUUID: %s", postUUID)
			}
			posContentUpdateData := map[string]interface{}{
				"is_top":      1,
				"top_on":      endTime,
				"top_sort":    req.Sort,
				"modified_on": time.Now().Unix(),
			}
			err := dao.UpdatePostInfo(postUUID, posContentUpdateData)
			if err != nil {
				return errs.NewCustomError(c, code.UpdatePostContentError, "CMSSetPostTopOn | set post top failed, err: %v, postUUID: %s", err, postUUID)
			}

			// 更新es
			doc := map[string]interface{}{
				"is_top":      1,
				"top_on":      endTime,
				"top_sort":    req.Sort,
				"modified_on": time.Now().Unix(),
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUUID, doc)
		} else {
			posContentUpdateData := map[string]interface{}{
				"is_top":      0,
				"top_on":      0,
				"top_sort":    0,
				"modified_on": time.Now().Unix(),
			}
			err := dao.UpdatePostInfo(postUUID, posContentUpdateData)
			if err != nil {
				return errs.NewCustomError(c, code.UpdatePostContentError, "CMSSetPostTopOn | set post top failed, err: %v, postUUID: %s", err, postUUID)
			}

			// 更新es
			doc := map[string]interface{}{
				"is_top":      0,
				"top_on":      0,
				"top_sort":    0,
				"modified_on": time.Now().Unix(),
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUUID, doc)
		}
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)
	return nil
}

// CMSUpdatePostReport 提供给CMS管理端删除、忽略动态举报
func CMSUpdatePostReport(c context.Context, req *pb.CMSReviewPostReq, checkReportStatus bool) error {
	req.PostUuids = util.RemoveDuplicateString(req.PostUuids)
	if len(req.PostUuids) == 0 {
		return nil
	}
	status := 2
	if constants.PostReviewT(req.Type) == constants.PostReviewDelete || constants.PostReviewT(req.Type) == constants.PostReviewDeleteByReport {
		status = 3
	}
	if checkReportStatus {
		// 只查询未处理的状态
		reportList, err := dao.GetCommentReportStatus(req.PostUuids, 1)
		if err != nil {
			return err
		}
		// 这里一条帖子会有多个举报记录
		reportUuidMap := make(map[string]int)
		for _, reportItem := range reportList {
			reportUuidMap[reportItem.ContentUuid] += 1
		}
		for _, postUuid := range req.PostUuids {
			if _, ok := reportUuidMap[postUuid]; !ok {
				// 数据对不上，可能是已经被审核了，或者是被删除了
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSUpdatePostCommentReport Data status does not match, content_uuid: %v ", req.PostUuids)
				return errs.NewCustomError(c, code.StatusIsChanged, "Data status does not match")
			}
		}
	}

	for _, postUUID := range req.PostUuids {
		if postUUID == "" {
			continue
		}

		err := dao.UpdatePostReport(postUUID, status)
		if err != nil {
			return errs.NewCustomError(c, code.UpdateContentReportError, "CMSUpdatePostReport | ignore post report failed, err: %v, postUUID: %s", err, postUUID)
		}
		// 更新es
		doc := map[string]interface{}{
			"status":      status,
			"update_user": req.UpdateUser,
			"modified_on": time.Now().Unix(),
		}
		boolQuery := es7.NewBoolQuery()
		contentUuidQuery := es7.NewTermQuery("content_uuid", postUUID)
		contentTypeQuery := es7.NewTermQuery("content_type", 1)
		boolQuery.Must(contentUuidQuery, contentTypeQuery)
		scritpStr := `
            ctx._source.status = params.status;
			ctx._source.update_user = params.update_user;
            ctx._source.modified_on = params.modified_on;
		`
		dao.EsUpdateDocByCondition(config.GetConfig().ElasticSearchSetting.ReportIndex, boolQuery, scritpStr, doc)
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.ReportIndex)
	return nil
}

// CMSUpdatePostPowerNum 提供给CMS管理端更新动态权重
func CMSUpdatePostPowerNum(c context.Context, req *pb.CMSReviewPostReq) error {
	req.PostUuids = util.RemoveDuplicateString(req.PostUuids)
	if len(req.PostUuids) == 0 {
		return nil
	}
	powerNum := float32(req.UpdateValue) / float32(100)

	for _, postUUID := range req.PostUuids {
		if postUUID == "" {
			continue
		}
		// 获取统计数据
		postStats, err := dao.GetPostStatsByPostUuid(postUUID)
		if err != nil {
			// 没有查询到这条数据，直接报错
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSUpdatePostPowerNum | get post stats failed, post_uuid: %s, err:%v", postUUID, err)
			return errs.NewCustomError(c, code.InvalidParamsErr, "get post state failed, not find posts")
		}

		posContentUpdateData := map[string]interface{}{
			"power_num_float": powerNum,
			"modified_on":     time.Now().Unix(),
		}
		err = dao.UpdatePostStatsData(postUUID, posContentUpdateData)
		if err != nil {
			return errs.NewCustomError(c, code.UpdatePostContentError, "CMSUpdatePostPowerNum | set post power_num failed, err: %v, postUUID: %s", err, postUUID)
		}
		postStats.PowerNumFloat = powerNum
		// 计算热度值
		hotNum := hotService.CalculatingPostHotNum(postStats)
		// 更新es
		doc := map[string]interface{}{
			"power_num_float": powerNum,
			"modified_on":     time.Now().Unix(),
			"hot_num":         hotNum,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUUID, doc)
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)
	return nil
}

// CMSUpdatePostOpenUp 提供给CMS管理端更新动态展示与隐藏
func CMSUpdatePostOpenUp(c context.Context, req *pb.CMSReviewPostReq) error {
	req.PostUuids = util.RemoveDuplicateString(req.PostUuids)
	if len(req.PostUuids) == 0 {
		return nil
	}

	postList, err := dao.GetPostList(&dao.PostConditions{PostUuids: req.PostUuids}, 0, 0)
	if err != nil {
		// 没有查询到这条数据，直接报错
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSUpdatePostOpenUp | get post list failed, post_uuids: %v, err:%v", req.PostUuids, err)
		return errs.NewCustomError(c, code.InvalidParamsErr, "get post list failed, not find posts")
	}

	// 判断是否有已经被操作的数据，有的话直接退出
	for _, post := range postList {
		if post.IsHide == int32(req.UpdateValue) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSUpdatePostOpenUp | Data status does not match, post_uuids: %v", req.PostUuids)
			return errs.NewCustomError(c, code.StatusIsChanged, "Data status does not match")
		}
	}

	var postCreatedUserOpenids = make(map[string][]interface{})
	var postCreatedUserOpenidsByCancel = make(map[string][]interface{})

	for _, postItem := range postList {
		// 获取统计数据

		postItem.IsHide = int32(req.UpdateValue)

		err = dao.UpdatePost(postItem)
		if err != nil {
			return errs.NewCustomError(c, code.UpdatePostContentError, "CMSUpdatePostPowerNum | update post open up failed, err: %v, postUUID: %s", err, postItem.PostUUID)
		}

		err := dao.UpdatePostAllLanguage(postItem.PostUUID, map[string]interface{}{
			"modified_on": time.Now().Unix(),
			"is_hide":     postItem.IsHide,
		})
		if err != nil {
			return errs.NewCustomError(c, code.UpdatePostContentError, "CMSUpdatePostPowerNum | update post language table open up failed, err: %v, postUUID: %s", err, postItem.PostUUID)
		}

		// 更新es
		doc := map[string]interface{}{
			"modified_on": time.Now().Unix(),
			"is_hide":     req.UpdateValue,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postItem.PostUUID, doc)

		if postItem.IntlOpenid != "" && postItem.IsHide == 1 {
			if postItem.IsHide == 1 {
				postCreatedUserOpenids[postItem.IntlOpenid] = append(postCreatedUserOpenids[postItem.IntlOpenid], postItem.PostUUID)
			} else {
				postCreatedUserOpenidsByCancel[postItem.IntlOpenid] = append(postCreatedUserOpenidsByCancel[postItem.IntlOpenid], postItem.PostUUID)
			}
		}
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)
	// 防止一个用户多条帖子被隐藏，而其中一条帖子被取消隐藏的时候整个用户下隐藏的帖子都不展示
	if len(postCreatedUserOpenids) > 0 {
		for intlOpenid, postUuids := range postCreatedUserOpenids {
			postHideRedisLKey := cache.PostHideCreatedUserOpenidCacheKeys(intlOpenid)
			redis.GetClient().SAdd(context.Background(), postHideRedisLKey, postUuids...)
		}

	}
	if len(postCreatedUserOpenidsByCancel) > 0 {
		for intlOpenid, postUuids := range postCreatedUserOpenids {
			postHideRedisLKey := cache.PostHideCreatedUserOpenidCacheKeys(intlOpenid)
			redis.GetClient().SRem(context.Background(), postHideRedisLKey, postUuids...)
		}
	}
	return nil
}
