// Package df_global_challenge TODO
package df_global_challenge

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	MissionPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_mission"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_global_challenge"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/global"
	dfLogicial "trpc.act.logicial/app/logic/df_global_challenge"
	"trpc.act.logicial/app/logic/share"
	model "trpc.act.logicial/app/model/df_global_challenge"
)

// DfGlobalChallengeImpl TODO
type DfGlobalChallengeImpl struct {
	pb.UnimplementedDfGlobalChallenge
}

// SaveCountryMsg 保存用户国家信息
func (s *DfGlobalChallengeImpl) SaveCountryMsg(ctx context.Context, req *pb.SaveCountryMsgReq) (
	rsp *pb.SaveCountryMsgRsp, err error) {
	rsp = &pb.SaveCountryMsgRsp{}
	err = dfLogicial.SaveSelfData(ctx, &model.DfGlobalChallengeSelf{
		CountryId: req.CountryId,
		RegionId:  req.RegionId,
		FsourceID: req.FsourceId,
		LangType:  req.LangType,
	})
	if err != nil {
		return
	}
	// err = dfLogicial.AddCountryPointToRedis(ctx, 1)
	// if err != nil {
	// 	return
	// }
	return
}

// SyncCountryPoint 同步国家积分
func (s *DfGlobalChallengeImpl) SyncCountryPoint(ctx context.Context, req *pb.SyncCountryPointReq) (
	rsp *pb.SyncCountryPointRsp, err error,
) {
	rsp = &pb.SyncCountryPointRsp{}
	if req.Stage < 7 {
		err = dfLogicial.AddCountryPointToSql(ctx, req.Stage)
		if err != nil {
			return
		}
		if req.Stage > 1 {
			err = dfLogicial.UpdateCountryPoint(ctx, req.Stage)
			if err != nil {
				return
			}
		}
	}
	return
}

// CheckTeamName TODO
func (s *DfGlobalChallengeImpl) CheckTeamName(ctx context.Context, req *pb.CheckTeamNameReq) (
	rsp *pb.CheckTeamNameRsp, err error,
) {
	rsp = &pb.CheckTeamNameRsp{
		IsOk: false,
	}
	if len(strings.Split(req.TeamName, "")) > 50 {
		err = errs.NewCustomError(ctx, code.DFTeamNameIsLong, "team name is too long")
		return
	}
	sensitive, err := dfLogicial.IsSafeTeamName(ctx, req.TeamName, req.GameId)
	if err != nil {
		return
	}
	if !sensitive || req.TeamName == "" {
		isRepeat, errR := dfLogicial.TeamNameIsRepeat(ctx, strings.TrimSpace(req.TeamName), req.FsourceId)
		if errR != nil {
			err = errR
			return
		}
		if !isRepeat {
			rsp.IsOk = true
		} else {
			err = errs.NewCustomError(ctx, code.DFTeamNameRepeat, "team name is repeat")
		}
	} else {
		err = errs.NewCustomError(ctx, code.IsSensitive, "is sensitive")
	}
	return
}

// SaveTeamName 保存队伍名称
func (s *DfGlobalChallengeImpl) SaveTeamName(ctx context.Context, req *pb.SaveTeamNameReq) (rsp *pb.SaveTeamNameRsp,
	err error) {
	rsp = &pb.SaveTeamNameRsp{}
	if len(req.ShareCode) <= 0 {
		err = errs.NewCustomError(ctx, code.DFTeamShareCodeError, "team share code is error")
		return
	}
	if len(strings.Split(req.TeamName, "")) > 50 {
		err = errs.NewCustomError(ctx, code.DFTeamNameIsLong, "team name is too long")
		return
	}
	sensitive, err := dfLogicial.IsSafeTeamName(ctx, req.TeamName, req.GameId)
	if err != nil {
		return
	}
	if !sensitive || req.TeamName == "" {
		isRepeat, errR := dfLogicial.TeamNameIsRepeat(ctx, strings.TrimSpace(req.TeamName), req.FsourceId)
		if errR != nil {
			err = errR
			return
		}
		if !isRepeat {
			err = dfLogicial.SaveTeamNameDB(ctx, req.TeamName, req.ShareCode, req.FsourceId)
			if err != nil {
				return
			}
		} else {
			err = errs.NewCustomError(ctx, code.DFTeamNameRepeat, "team name is repeat")
		}
	} else {
		err = errs.NewCustomError(ctx, code.IsSensitive, "is sensitive")
	}

	return
}

// GetRegionList 获取国家积分列表
func (s *DfGlobalChallengeImpl) GetRegionList(ctx context.Context, req *pb.GetRegionListReq) (rsp *pb.GetRegionListRsp,
	err error) {
	rsp = &pb.GetRegionListRsp{}
	regionList, err := dfLogicial.GetRegionListByRedis(ctx, req.Stage)
	if err != nil {
		return
	}
	rsp.RegionList = regionList
	return
}

// AddSelfPoint 添加个人积分
func (s *DfGlobalChallengeImpl) AddSelfPoint(ctx context.Context, req *pb.AddSelfPointReq) (rsp *pb.AddSelfPointtRsp,
	err error) {
	rsp = &pb.AddSelfPointtRsp{}
	data, err := dfLogicial.GetSelfData(ctx)
	if err != nil {
		return
	}
	data.Point = req.Point + data.Point
	data.SolPoint = req.SolPoint + data.SolPoint
	data.BattlefieldPoint = req.BattlefieldPoint + data.BattlefieldPoint
	err = dfLogicial.SaveSelfData(ctx, data)
	if err != nil {
		return
	}
	if req.Point != 0 && (req.Stage == 1) {
		err = dfLogicial.AddCountryPointToRedis(ctx, int64(req.Point))
		if err != nil {
			return
		}
	}
	return
}

// GetSelfMsg 获取个人信息
func (s *DfGlobalChallengeImpl) GetSelfMsg(ctx context.Context, req *pb.GetSelfMsgReq) (rsp *pb.GetSelfMsgRsp,
	err error) {
	rsp = &pb.GetSelfMsgRsp{}
	data, err := dfLogicial.GetSelfData(ctx)
	if err != nil {
		return
	}
	rsp.Point = int64(data.Point)
	rsp.SolPoint = int64(data.SolPoint)
	rsp.BattlefieldPoint = int64(data.BattlefieldPoint)
	rsp.CountryId = data.CountryId
	return
}

// GetTeamName 获取小队名称
func (s *DfGlobalChallengeImpl) GetTeamName(ctx context.Context, req *pb.GetTeamNameReq) (rsp *pb.GetTeamNameRsp,
	err error) {
	rsp = &pb.GetTeamNameRsp{
		TeamName: "",
	}
	if req.ShareCode == "" {
		return
	}
	teamName, err := dfLogicial.GetTeamName(ctx, req.ShareCode, req.FsourceId)
	if err != nil {
		return
	}
	rsp.TeamName = teamName

	return
}

// IsMeetsPoint 积分是否可以达到里程碑
func (s *DfGlobalChallengeImpl) IsMeetsPoint(ctx context.Context, req *pb.IsMeetsPointReq) (
	rsp *pb.IsMeetsPointRsp, err error,
) {
	rsp = &pb.IsMeetsPointRsp{
		IsMeets: false,
	}
	data, err := dfLogicial.GetSelfData(ctx)
	if err != nil {
		return
	}
	num, err := strconv.ParseInt(req.TagId, 10, 64)
	if err != nil {
		return
	}
	if num < 22 {
		err = errs.NewCustomError(ctx, code.TagIdError, "tag id is error")
		return rsp, err
	}
	levelMap := map[int]int64{
		1: 2,
		2: 3,
		3: 7,
		4: 10,
		5: 15,
	}
	if data.Point >= levelMap[int(num-22)] {
		rsp.IsMeets = true
	} else {
		err = errs.NewCustomError(ctx, code.IsNotMeetPoint, "point is not meet")
		return rsp, err
	}

	return
}

// SyncSolPoint 同步sol积分
func (s *DfGlobalChallengeImpl) SyncSolPoint(ctx context.Context, req *pb.SyncSolPointReq) (
	rsp *pb.SyncSolPointRsp, err error,
) {
	rsp = &pb.SyncSolPointRsp{}
	go dfLogicial.SyncSolPoint(ctx)
	return
}

// GetSolRankList 获取sol小队积分列表
func (s *DfGlobalChallengeImpl) GetSolRankList(ctx context.Context, req *pb.GetSolRankListReq) (
	rsp *pb.GetSolRankListRsp, err error,
) {
	rsp = &pb.GetSolRankListRsp{
		List:  make([]*pb.RankListItem, 0),
		Total: 0,
		Self:  &pb.RankListItem{},
	}
	pageSize := req.PageSize
	if req.PageSize == 0 {
		pageSize = 10
	}
	pageNum := req.PageNum
	if req.PageNum == 0 {
		pageNum = 1
	}
	redisPrefixKey := global.GetPrefix()
	hashKey := fmt.Sprintf("%v-dfm-sol-rank", redisPrefixKey)
	result, err := redis.GetClient().HGetAll(ctx, hashKey).Result()
	if err != nil {
		return
	}
	dataKey := fmt.Sprintf("%v-%v-%v", pageSize, pageNum, req.RegionId)
	if _, ok := result[dataKey]; ok {

		err = json.Unmarshal([]byte(result[dataKey]), &rsp)
		if err != nil {
			// 告警
			errs.NewCustomError(ctx, 611010, "redis get err,redisKey=%v,val=%v,err=%v", dataKey,
				result[dataKey],
				err)
			return rsp, err
		}
	} else {
		// 设置缓存
		if pageNum == 1 {
			total, err := dfLogicial.GetTeamTotal(ctx, req.RegionId, "sol_point")
			if err != nil {
				return rsp, err
			}
			rsp.Total = int32(total)
		}
		data, err := dfLogicial.GetSolRankList(ctx, pageNum, pageSize, req.RegionId)
		if err != nil {
			return rsp, err
		}
		rsp.List = data
		personJSON, err := json.Marshal(rsp)
		if err != nil {
			return rsp, err
		}
		result[dataKey] = string(personJSON)
		err = redis.GetClient().HMSet(ctx, hashKey, result).Err()
		if err != nil {
			return rsp, err
		}
		expiration := 2 * 24 * time.Hour
		err = redis.GetClient().Expire(ctx, hashKey, expiration).Err()
		if err != nil {
			return rsp, err
		}
	}

	self, err := dfLogicial.GetSelfSolRank(ctx)
	if err != nil {
		return
	}
	rsp.Self = self
	return
}

// SyncBattlefieldPoint 同步大战场积分
func (s *DfGlobalChallengeImpl) SyncBattlefieldPoint(ctx context.Context, req *pb.SyncBattlefieldPointReq) (
	rsp *pb.SyncBattlefieldPointRsp, err error,
) {
	rsp = &pb.SyncBattlefieldPointRsp{}
	go dfLogicial.SyncBattlefieldPoint(ctx)
	return
}

// GetBattlefieldRankList 获取大战场小队积分列表
func (s *DfGlobalChallengeImpl) GetBattlefieldRankList(ctx context.Context, req *pb.GetBattlefieldRankListReq) (
	rsp *pb.GetBattlefieldRankListRsp, err error,
) {
	rsp = &pb.GetBattlefieldRankListRsp{}
	rsp = &pb.GetBattlefieldRankListRsp{
		List:  make([]*pb.RankListItem, 0),
		Total: 0,
		Self:  &pb.RankListItem{},
	}
	pageSize := req.PageSize
	if req.PageSize == 0 {
		pageSize = 10
	}
	pageNum := req.PageNum
	if req.PageNum == 0 {
		pageNum = 1
	}
	redisPrefixKey := global.GetPrefix()
	hashKey := fmt.Sprintf("%v-dfm-battlefield-rank", redisPrefixKey)
	result, err := redis.GetClient().HGetAll(ctx, hashKey).Result()
	if err != nil {
		return
	}
	dataKey := fmt.Sprintf("%v-%v-%v", pageSize, pageNum, req.RegionId)
	if _, ok := result[dataKey]; ok {

		err = json.Unmarshal([]byte(result[dataKey]), &rsp)
		if err != nil {
			// 告警
			errs.NewCustomError(ctx, 611010, "redis get err,redisKey=%v,val=%v,err=%v", dataKey,
				result[dataKey],
				err)
			return rsp, err
		}
	} else {
		// 设置缓存
		if pageNum == 1 {
			total, err := dfLogicial.GetTeamTotal(ctx, req.RegionId, "battlefield_point")
			if err != nil {
				return rsp, err
			}
			rsp.Total = int32(total)
		}
		data, err := dfLogicial.GetBattlefieldRankList(ctx, pageNum, pageSize, req.RegionId)
		if err != nil {
			return rsp, err
		}
		rsp.List = data
		personJSON, err := json.Marshal(rsp)
		if err != nil {
			return rsp, err
		}
		result[dataKey] = string(personJSON)
		err = redis.GetClient().HMSet(ctx, hashKey, result).Err()
		if err != nil {
			return rsp, err
		}
		expiration := 2 * 24 * time.Hour
		err = redis.GetClient().Expire(ctx, hashKey, expiration).Err()
		if err != nil {
			return rsp, err
		}
	}

	self, err := dfLogicial.GetSelfBattlefieldRank(ctx)
	if err != nil {
		return
	}
	rsp.Self = self
	return
}

// AddTeamAddedNumPoint 添加队伍人数增加的对应积分
func (s *DfGlobalChallengeImpl) AddTeamAddedNumPoint(ctx context.Context, req *pb.AddTeamAddedNumPointReq) (
	rsp *pb.AddTeamAddedNumPointRsp, err error,
) {
	rsp = &pb.AddTeamAddedNumPointRsp{}
	// 判断一下昨日小队人数是否大雨等于5
	FsourceId := "pageV3-1841"
	tagIdNum, err := strconv.ParseInt(req.TagId, 10, 32)
	if err != nil {
		return
	}
	nowGroupSize, err := share.GetUserTeamSize(ctx, FsourceId)
	if err != nil {
		return
	}
	if tagIdNum-28 != nowGroupSize {
		err = errs.NewCustomError(ctx, 700004, "param is error")
		return
	}

	yesterdayGroupSize, err := share.GetUserTeamSizeUntilYesterday(ctx, 1, FsourceId)
	if err != nil {
		return
	}

	if int(yesterdayGroupSize) >= 5 {
		err = errs.NewCustomError(ctx, code.MissionDayLimit, "add point is limit")
		return
	}

	addPoint := tagIdNum - 28 - yesterdayGroupSize
	// 29 - 33 代表队伍人数从 1-5
	for i := 29; i <= 33; i++ {
		if i <= int(tagIdNum) {
			// 查一下任务是否完成了如果完成了addPoint就-1
			mission := MissionPb.NewMissionClientProxy()
			MissionHasRecord, err := mission.HasFinishMission(ctx, &MissionPb.HasFinishMissionReq{
				FsourceId:  FsourceId,
				TagId:      fmt.Sprintf("%v", i),
				DayLimit:   1,
				TotalLimit: 1,
				TimeZone:   0,
			})
			delErr := errs.ParseError(ctx, err)
			log.WithFieldsContext(ctx, "log_type").Infof("tag: %v, MissionHasRecord %v, err %v", i, MissionHasRecord,
				delErr.Code)
			if err != nil && delErr.Code != code.MissionHasNotFinish {
				return rsp, err
			}
			if delErr.Code == code.MissionHasNotFinish {
				continue
			}
			if MissionHasRecord.MissionHasDone {
				addPoint = int64(tagIdNum - int64(i))
				log.WithFieldsContext(ctx, "log_type").Infof("MissionHasDone: %v, tag_id: %v, addPoint %v, yesterdayGroupSize: %v",
					MissionHasRecord.MissionHasDone, tagIdNum, addPoint,
					yesterdayGroupSize)
			}
		}
	}
	log.WithFieldsContext(ctx, "log_type").Infof("tag_id: %v, addPoint %v, yesterdayGroupSize: %v", tagIdNum, addPoint,
		yesterdayGroupSize)
	if addPoint <= 0 {
		return
	}
	// 添加个人积分
	data, err := dfLogicial.GetSelfData(ctx)
	if err != nil {
		return
	}
	log.WithFieldsContext(ctx, "log_type").Infof("data.Point: %v, ", data.Point)

	data.Point = addPoint + data.Point
	err = dfLogicial.SaveSelfData(ctx, data)
	if err != nil {
		return
	}
	// 添加国家积分
	// 设置目标时间
	targetTime := time.Date(2024, time.December, 4, 23, 59, 59, 0, time.UTC)

	// 获取当前时间
	currentTime := time.Now().UTC()
	if currentTime.Before(targetTime) && addPoint > 0 {
		err = dfLogicial.AddCountryPointToRedis(ctx, int64(addPoint))
		if err != nil {
			return
		}
	}
	return
}

// CheckRoleSendPresent 查询是否创建角色并发奖
func (s *DfGlobalChallengeImpl) CheckRoleSendPresent(ctx context.Context, req *pb.CheckRoleSendPresentReq) (
	rsp *pb.CheckRoleSendPresentRsp, err error,
) {
	rsp = &pb.CheckRoleSendPresentRsp{}
	err = dfLogicial.CheckRoleSendPresent(ctx, req.FsourceId)
	if err != nil {
		return
	}
	return
}

// AddSendLandMarkPresent 添加里程碑发奖记录
func (s *DfGlobalChallengeImpl) AddSendLandMarkPresent(ctx context.Context, req *pb.AddSendLandMarkPresentReq) (
	rsp *pb.AddSendLandMarkPresentRsp, err error,
) {
	rsp = &pb.AddSendLandMarkPresentRsp{}
	err = dfLogicial.AddSendLandMarkPresent(ctx, req.FsourceId, req.TagId, req.LangType)
	if err != nil {
		return
	}
	return
}

// SyncTwoCountryPoint 将第一阶段数据同步到第二阶段
func (s *DfGlobalChallengeImpl) SyncTwoCountryPoint(ctx context.Context, req *pb.SyncTwoCountryPointReq) (
	rsp *pb.SyncTwoCountryPointRsp, err error,
) {
	rsp = &pb.SyncTwoCountryPointRsp{}
	err = dfLogicial.SyncTwoCountryPoint(ctx)
	if err != nil {
		return
	}
	return
}

// GetTeamShareCode 获取当前用户小队分享码
func (s *DfGlobalChallengeImpl) GetTeamShareCode(ctx context.Context, req *pb.GetTeamShareCodeReq) (
	rsp *pb.GetTeamShareCodeRsp, err error,
) {
	rsp = &pb.GetTeamShareCodeRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	shareCode, err := dfLogicial.GetTeamShareCode(ctx, userAccount.Uid, int32(userAccount.AccountType))
	if err != nil {
		return
	}
	rsp.ShareCode = shareCode
	return
}

// SecondSendPresent 发送二期奖品
func (s *DfGlobalChallengeImpl) SecondSendPresent(ctx context.Context, req *pb.SecondSendPresentReq) (
	rsp *pb.SecondSendPresentRsp, err error,
) {
	rsp = &pb.SecondSendPresentRsp{}
	go dfLogicial.SecondSendPresent(ctx, req.Stage)
	return
}

// UpdateLangType 更新语言
func (s *DfGlobalChallengeImpl) UpdateLangType(ctx context.Context, req *pb.UpdateLangTypeReq) (
	rsp *pb.UpdateLangTypeRsp, err error,
) {
	rsp = &pb.UpdateLangTypeRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeSelf{}.TableName()).
		Where("uid = ?", userAccount.Uid).
		Update("lang_type", req.LangType)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	return
}

// AddTeamSyncTeamPoint TODO
func (s *DfGlobalChallengeImpl) AddTeamSyncTeamPoint(ctx context.Context, req *pb.AddTeamSyncTeamPointReq) (
	rsp *pb.AddTeamSyncTeamPointRsp, err error,
) {
	rsp = &pb.AddTeamSyncTeamPointRsp{}
	err = dfLogicial.AddTeamSyncTeamPoint(ctx, req.ShareCode)
	if err != nil {
		return
	}
	return
}

// SyncTeamLeaderAvatar TODO
func (s *DfGlobalChallengeImpl) SyncTeamLeaderAvatar(ctx context.Context, req *pb.SyncTeamLeaderAvatarReq) (
	rsp *pb.SyncTeamLeaderAvatarRsp, err error,
) {
	rsp = &pb.SyncTeamLeaderAvatarRsp{}
	go dfLogicial.SyncTeamLeaderAvatar(ctx)
	return
}

// GetCountryList TODO
func (s *DfGlobalChallengeImpl) GetCountryList(ctx context.Context, req *pb.GetCountryListReq) (
	rsp *pb.GetCountryListRsp, err error,
) {
	rsp = &pb.GetCountryListRsp{}
	countryList, err := dfLogicial.GetCountryListRedis(ctx, req.Stage)
	if err != nil {
		return
	}
	rsp.CountryList = countryList
	return
}

// CountrySendPresent 按国家发送礼包
func (s *DfGlobalChallengeImpl) CountrySendPresent(ctx context.Context, req *pb.CountrySendPresentReq) (
	rsp *pb.CountrySendPresentRsp, err error,
) {
	rsp = &pb.CountrySendPresentRsp{}
	go dfLogicial.CountrySendPresent(ctx, req.Stage)
	return
}

// SyncPresentExcel TODO
func (s *DfGlobalChallengeImpl) SyncPresentExcel(ctx context.Context, req *pb.SyncPresentExcelReq) (
	rsp *pb.SyncPresentExcelRsp, err error,
) {
	rsp = &pb.SyncPresentExcelRsp{}
	go dfLogicial.SyncPresentExcel(ctx)
	return
}
