package model

// AuditPermission 审核黑白名单表
type AuditPermission struct {
	*Model
	Id           uint64 `json:"id"`            //表id
	Type         uint8  `json:"type"`          //黑白名单: 1:白名单,2:黑名单
	IntlOpenid   string `json:"intl_openid"`   //用户表id
	Status       uint8  `json:"status"`        //包含评论与动态审核处的账号封禁:1、动态封禁:2、评论封禁:3 (白名单不展示)
	ValidOn      uint64 `json:"valid_on"`      //精确到秒，以名单生效开始计算有效时限；到期后状态变为已失效
	Introduce    string `json:"introduce"`     //展示黑名单的封禁原因/白名单开放备注
	ModifiedUser string `json:"modified_user"` //操作用户
	CreatedOn    uint64 `json:"created_on"`    //创建时间
	ModifiedOn   uint64 `json:"modified_on"`   //修改时间
	DeletedOn    uint64 `json:"deleted_on"`    //删除时间
	IsDel        uint8  `json:"is_del"`        //是否删除 0 为未删除、1 为已删除
	GameId       string `json:"game_id"`       //游戏id
	AreaId       string `json:"area_id"`       //大区id
}

type AuditPermissionFormated struct {
	Id           uint64 `json:"id"`            //表id
	Type         uint8  `json:"type"`          //黑白名单: 1:白名单,2:黑名单
	IntlOpenid   string `json:"intl_openid"`   //用户表id
	Status       uint8  `json:"status"`        //包含评论与动态审核处的账号封禁:1、动态封禁:2、评论封禁:3 (白名单不展示)
	ValidOn      uint64 `json:"valid_on"`      //精确到秒，以名单生效开始计算有效时限；到期后状态变为已失效
	Introduce    string `json:"introduce"`     //展示黑名单的封禁原因/白名单开放备注
	ModifiedUser string `json:"modified_user"` //操作用户
	CreatedOn    uint64 `json:"created_on"`    //创建时间
	GameId       string `json:"game_id"`       //游戏id
	AreaId       string `json:"area_id"`       //大区id
}

func (s *AuditPermission) Format() *AuditPermissionFormated {
	if s.Model != nil {
		return &AuditPermissionFormated{
			Id:           s.Id,
			Type:         s.Type,
			IntlOpenid:   s.IntlOpenid,
			Status:       s.Status,
			ValidOn:      s.ValidOn,
			Introduce:    s.Introduce,
			ModifiedUser: s.ModifiedUser,
			CreatedOn:    s.CreatedOn,
			GameId:       s.GameId,
			AreaId:       s.AreaId,
		}
	}

	return nil
}

func (s *AuditPermission) TableName() string {
	return "p_audit_permission"
}
