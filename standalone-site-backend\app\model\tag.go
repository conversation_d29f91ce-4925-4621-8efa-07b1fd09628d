package model

type Tag struct {
	*Model
	Type              int          `json:"type"`               //话题类型：1 活动话题，2 普通话题
	PicURL            string       `json:"pic_url"`            //话题封面
	PostNum           int64        `json:"post_num"`           //帖子数
	QuoteNum          int64        `json:"quote_num"`          //引用数
	ReadNum           int64        `json:"read_num"`           //阅读数
	CommentNum        int          `json:"comment_num"`        //评论量
	KudosNum          int          `json:"kudos_num"`          //点赞量
	FansNum           int          `json:"fans_num"`           //粉丝量
	HotNum            int          `json:"hot_num"`            //热度量
	PowerNum          int          `json:"power_num"`          //权重
	RecommendDeadline int64        `json:"recommend_deadline"` //推荐截止时间(0:永久,其他按时间戳推断状态)
	SortNum           int32        `json:"sort_num"`           //置顶顺序值 越小越前 默认入库最大值
	GameID            string       `json:"game_id"`            //游戏id
	AreaID            string       `json:"area_id"`            //大区id
	Creator           string       `json:"creator"`            //创建人
	Updater           string       `json:"updater"`            //更新人
	Language          *TagLanguage `json:"language" gorm:"-"`
}

type TagFormated struct {
	ID                int64  `json:"id"`
	Type              int    `json:"type"`               //话题类型：1 活动话题，2 普通话题
	PicURL            string `json:"pic_url"`            //话题封面
	QuoteNum          int64  `json:"quote_num"`          //引用数
	ReadNum           int64  `json:"read_num"`           //阅读数
	CommentNum        int    `json:"comment_num"`        //评论量
	KudosNum          int    `json:"kudos_num"`          //点赞量
	FansNum           int    `json:"fans_num"`           //粉丝量
	HotNum            int    `json:"hot_num"`            //热度量
	PowerNum          int    `json:"power_num"`          //权重
	RecommendDeadline int64  `json:"recommend_deadline"` //推荐截止时间(0:永久,其他按时间戳推断状态)
	SortNum           int32  `json:"sort_num"`           //置顶顺序值 越小越前 默认入库最大值
	GameID            string `json:"game_id"`            //游戏id
	AreaID            string `json:"area_id"`            //大区id
	TagName           string `json:"tag_name"`
}

type TagLanguage struct {
	ID       int64  `json:"id"`
	TagID    int    `json:"tag_id"`   //话题 id
	Language string `json:"language"` //语言
	TagName  string `json:"tag_name"` //话题名称
	Creator  string `json:"creator"`  //创建人
	Updater  string `json:"updater"`  //更新人
}

func (t *Tag) Format() *TagFormated {
	if t.Model == nil {
		return &TagFormated{}
	}
	return &TagFormated{
		ID:                t.ID,
		Type:              t.Type,
		PicURL:            t.PicURL,
		QuoteNum:          t.QuoteNum,
		ReadNum:           t.ReadNum,
		CommentNum:        t.CommentNum,
		KudosNum:          t.KudosNum,
		FansNum:           t.FansNum,
		HotNum:            t.HotNum,
		PowerNum:          t.PowerNum,
		RecommendDeadline: t.RecommendDeadline,
		SortNum:           t.SortNum,
		GameID:            t.GameID,
		AreaID:            t.AreaID,
		TagName:           t.Language.TagName,
	}
}

func (t *Tag) TableName() string {
	return "p_tag"
}
func (t *TagLanguage) TableName() string {
	return "p_tag_language"
}
