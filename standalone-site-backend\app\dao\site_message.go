package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/model"
)

func SiteMessageUpdateStatus(status constants.SiteMessagePushStatus, id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.SiteMessage{}).TableName()).Where("id = ?", id).Updates(map[string]interface{}{
		"status": status,
	}).Error
}

func SiteMessageUpdateCreateDataStatus(status constants.CreateDataStatus, id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.SiteMessage{}).TableName()).Where("id = ?", id).Updates(map[string]interface{}{
		"created_data": status,
	}).Error
}

func SiteMessageList(status constants.SiteMessagePushStatus, sendTime int64, gameId string, offset, limit int) ([]*model.SiteMessage, error) {
	var messages []*model.SiteMessage
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.SiteMessage{}).TableName())
	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	db = db.Where("status < ?", status)
	if sendTime > 0 {
		db = db.Where("send_time < ?", sendTime)
	}
	if gameId != "" {
		db = db.Where("game_id = ?", gameId)
	}

	if err = db.Where("is_del = 0").Find(&messages).Error; err != nil {
		return nil, err
	}

	return messages, nil
}

func SiteMessageGet(id int64) (*model.SiteMessage, error) {
	var siteMsg model.SiteMessage
	db := DB.SelectConnect("db_standalonesite").Table((&model.SiteMessage{}).TableName())
	if id > 0 {
		db = db.Where("id = ?", id)
	}

	err := db.Where("is_del = 0").First(&siteMsg).Error
	if err != nil {
		return &siteMsg, err
	}

	return &siteMsg, nil
}

func SiteMessageFirstWithLang(id int64) (*model.SiteMessageWithLang, error) {
	var siteMsgRow model.SiteMessage
	var siteMsgLang []*model.SiteMessageLanguage
	tx := DB.SelectConnect("db_standalonesite").Table((&model.SiteMessage{}).TableName())
	if id > 0 {
		tx = tx.Where("id = ?", id)
	}

	err := tx.Where("is_del = 0").First(&siteMsgRow).Error
	if err != nil {
		return nil, err
	}
	if err = DB.SelectConnect("db_standalonesite").Table((&model.SiteMessageLanguage{}).TableName()).Where("msg_id = ? AND is_del = 0", siteMsgRow.ID).Find(&siteMsgLang).Error; err != nil {
		return nil, err
	}
	return &model.SiteMessageWithLang{
		SiteMessage: siteMsgRow,
		Language:    siteMsgLang,
	}, nil
}

func GetAllSiteMessageMapWithLang() (map[int64]*model.SiteMessageWithLangMap, error) {
	siteMsgMap := make(map[int64]*model.SiteMessageWithLangMap, 0)
	var siteMsgRows []*model.SiteMessage
	tx := DB.SelectConnect("db_standalonesite").Table((&model.SiteMessage{}).TableName())
	err := tx.Unscoped().Find(&siteMsgRows).Error
	if err != nil {
		return nil, err
	}
	if len(siteMsgRows) == 0 {
		return siteMsgMap, nil
	}

	var siteMsgLangs []*model.SiteMessageLanguage
	err = DB.SelectConnect("db_standalonesite").Table((&model.SiteMessageLanguage{}).TableName()).Find(&siteMsgLangs).Error
	if err != nil {
		return nil, err
	}
	for _, siteMsgItem := range siteMsgRows {
		siteMsgMap[siteMsgItem.ID] = &model.SiteMessageWithLangMap{
			SiteMessage: siteMsgItem,
			LanguageMap: make(map[string]*model.SiteMessageLanguage, 0),
		}
		for _, siteMsgLangItem := range siteMsgLangs {
			if siteMsgLangItem.MsgID == siteMsgItem.ID {
				siteMsgMap[siteMsgItem.ID].LanguageMap[siteMsgLangItem.Language] = siteMsgLangItem
			}
		}
	}

	return siteMsgMap, nil
}

func GetSiteMessageListByStatus(status constants.SiteMessagePushStatus) ([]int64, error) {
	var ids []int64
	err := DB.SelectConnect("db_standalonesite").Table((&model.SiteMessage{}).TableName()).Where("status = ?", status).Pluck("id", &ids).Error
	if err != nil {
		return nil, err
	}
	return ids, nil
}

func UpdateSiteMessageStat(msgId int64, sendCount, readCount int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.SiteMessage{}).TableName()).Where("id = ?", msgId).Updates(map[string]interface{}{
		"send_count": sendCount,
		"read_count": readCount,
	}).Error
}
