// Package df_pve_speed TODO
package df_pve_speed

import "trpc.act.logicial/app/model"

// DfPveSpeedRankModel TODO
type DfPveSpeedRankModel struct {
	model.AppModel
}

// TableName TODO
func (DfPveSpeedRankModel) TableName() string {
	return "df_pve_speed_rank"
}

// DfPveSpeedRank TODO
type DfPveSpeedRank struct {
	DfPveSpeedRankModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	Memberid1   string `gorm:"type:varchar(255);column:memberid1;"`
	Memberid2   string `gorm:"type:varchar(255);column:memberid2;"`
	Memberid3   string `gorm:"type:varchar(255);column:memberid3;"`
	Name1       string `gorm:"type:varchar(255);column:name1;"`
	Name2       string `gorm:"type:varchar(255);column:name2;"`
	Name3       string `gorm:"type:varchar(255);column:name3;"`
	Avatar1     string `gorm:"type:varchar(255);column:avatar1;"`
	Avatar2     string `gorm:"type:varchar(255);column:avatar2;"`
	Avatar3     string `gorm:"type:varchar(255);column:avatar3;"`
	Dteventtime string `gorm:"type:varchar(255);column:dteventtime;"`
	Dtstatdate  string `gorm:"type:varchar(255);column:dtstatdate;"`
	Playtime    int64  `gorm:"type:int(11);column:playtime;"`
	Rank        int64  `gorm:"type:int(11);column:rank;"`
}
