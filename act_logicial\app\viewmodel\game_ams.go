package viewmodel

// AbUserGameInfoItem ab游戏用户信息
type AbUserGameInfoItem struct {
	Name       string `json:"roleName"`   // 角色名称
	CreateTime string `json:"createTime"` // 角色创建日期
	Level      string `json:"leadLevel"`  // 角色等级
}

// CagUserGameInfoItem cag游戏用户信息
type CagUserGameInfoItem struct {
	Name       string `json:"roleName"`   // 角色名称
	CreateTime string `json:"createTime"` // 角色创建日期
	Level      string `json:"leadLevel"`  // 角色等级
}
