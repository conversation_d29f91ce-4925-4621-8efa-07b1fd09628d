package model

type Avatar struct {
	*Model
	ID        int64  `gorm:"column:id;primary_key" json:"id"`     //
	GameID    string `gorm:"column:game_id" json:"game_id"`       //游戏id
	AreaID    string `gorm:"column:area_id" json:"area_id"`       //区域id
	AvatarURL string `gorm:"column:avatar_url" json:"avatar_url"` //头像地址
	Status    int64  `gorm:"column:status" json:"status"`         // 0 未绑定 1 绑定通用（全部游戏） 2部分游戏
}

func (a *Avatar) TableName() string {
	return "p_avatar"
}
