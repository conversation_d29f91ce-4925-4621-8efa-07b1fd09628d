package model

import "trpc.publishing_application.standalonesite/app/constants"

type PostReport struct {
	*Model
	ContentUuid        string                       `json:"content_uuid"`
	ContentType        constants.PostReportContentT `json:"content_type"`
	ReportType         constants.PostReportT        `json:"report_type"`
	Reason             string                       `json:"reason"`
	ReportIntlOpenid   string                       `json:"report_intl_openid"`
	ReportedIntlOpenid string                       `json:"reported_intl_openid"`
	GameId             string                       `json:"game_id"`
	AreaId             string                       `json:"area_id"`
	Status             int64                        `json:"status"` // 举报处理状态：1未处理，2已忽略，3已删除
	UpdateUser         string                       `json:"update_user"`
	Language           string                       `json:"language"`
}

func (p *PostReport) TableName() string {
	return "p_post_report"
}

type ESReportInfo struct {
	Id                     int64    `json:"id"`           // 自增id
	ContentType            int64    `json:"content_type"` // 举报的内容类型：1是动态，2是评论，3是评论回复
	ReportType             int64    `json:"report_type"`  // 举报的类型：1是广告，2是涉嫌刷屏，3涉嫌低俗言论，4涉嫌虚假消息，5涉嫌游戏服务或交易，6其他
	ContentUuid            string   `json:"content_uuid"`
	ReportedIntlOpenid     string   `json:"reported_intl_openid"`      // 动态发布者
	ReportedIntlUserOpenid string   `json:"reported_intl_user_openid"` // 动态发布者intl_user_openid
	ReportIntlOpenid       string   `json:"report_intl_openid"`        // 举报者
	ReportIntlUserOpenid   string   `json:"report_intl_user_openid"`   // 举报者intl_user_openid
	PlateID                int32    `json:"plate_id"`                  // 板块id
	Language               string   `json:"language"`
	SubContentType         int32    `json:"sub_content_type"` // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	Title                  string   `json:"title"`
	Content                string   `json:"content"`
	PicUrls                []string `json:"pic_urls"`
	ExtInfo                string   `json:"ext_info"`
	Platform               string   `json:"platform"`
	Tags                   []int64  `json:"tags"`
	Status                 int32    `json:"status"` // 举报处理状态：0未处理，1已忽略，2已删除
	Reason                 string   `json:"reason"`
	CreatedOn              int64    `json:"created_on"`
	ModifiedOn             int64    `json:"modified_on"`
	GameId                 string   `json:"game_id"`
	AreaId                 string   `json:"area_id"`
	UpdateUser             string   `json:"update_user"`
}
