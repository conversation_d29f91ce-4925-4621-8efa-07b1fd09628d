package model

import "gorm.io/plugin/soft_delete"

type PostExternal struct {
	*Model
	PostUUID          string `json:"post_uuid"`
	IntlOpenid        string `json:"intl_openid"`
	PlateID           int32  `json:"plate_id"` // 板块id
	Language          string `json:"language"`
	Type              int32  `json:"type"` // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	IsTop             int32  `json:"is_top"`
	TopSort           int32  `json:"top_sort"` // 顶置顺序值，越小越靠前
	TopOn             int64  `json:"top_on"`
	IsAudit           int32  `json:"is_audit"`   // 是否已审核 1是 2不是
	Visibility        int32  `json:"visibility"` // 可见性 0公开 1私密 2好友可见
	SocialmediaPostId string `json:"socialmedia_post_id"`
	LatestRepliedOn   int64  `json:"latest_replied_on"`
	GameId            string `json:"game_id"`
	AreaId            string `json:"area_id"`
	HotNum            int64  `json:"hot_num"`       // 热度
	CreatedOnMs       int64  `json:"created_on_ms"` //创建时间微秒
	TaskId            string `json:"task_id"`       //原帖子活动任务id
	RankId            int32  `json:"rank_id"`       //赛道id
	Platform          string `json:"platform"`      //社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
}

type PostExternalFormat struct {
	ID                int64                 `gorm:"primary_key" json:"id"`
	CreatedOn         int64                 `json:"created_on"`
	ModifiedOn        int64                 `json:"modified_on"`
	DeletedOn         int64                 `json:"deleted_on"`
	IsDel             soft_delete.DeletedAt `gorm:"softDelete:flag" json:"is_del"`
	PostUUID          string                `json:"post_uuid"`
	IntlOpenid        string                `json:"intl_openid"`
	PlateID           int32                 `json:"plate_id"` // 板块id
	Language          string                `json:"language"`
	Type              int32                 `json:"type"` // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	IsTop             int32                 `json:"is_top"`
	TopSort           int32                 `json:"top_sort"` // 顶置顺序值，越小越靠前
	TopOn             int64                 `json:"top_on"`
	IsAudit           int32                 `json:"is_audit"`   // 是否已审核 1是 2不是
	Visibility        int32                 `json:"visibility"` // 可见性 0公开 1私密 2好友可见
	SocialmediaPostId string                `json:"socialmedia_post_id"`
	LatestRepliedOn   int64                 `json:"latest_replied_on"`
	GameId            string                `json:"game_id"`
	AreaId            string                `json:"area_id"`
	CreatedOnMs       int64                 `json:"created_on_ms"`
	HotNum            int64                 `json:"hot_num"`  // 热度
	TaskId            string                `json:"task_id"`  //原帖子活动任务id
	RankId            int32                 `json:"rank_id"`  //赛道id
	Platform          string                `json:"platform"` //社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
}

// TableName sets the insert table name for this struct type
func (p *PostExternal) TableName() string {
	return "p_post_external"
}
