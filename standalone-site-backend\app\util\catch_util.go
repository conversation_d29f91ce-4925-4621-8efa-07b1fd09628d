package util

import (
	"context"
	"runtime"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/constants"
)

func CatchPanic(ctx context.Context) {
	if err := recover(); err != nil {
		buf := make([]byte, 2048)
		n := runtime.Stack(buf, false)
		err := errs.NewCustomError(ctx, 0, "Util catch panic [Panic]: %v, [Stack]: %s", err, buf[:n])
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("Util catch panic [Panic]: %v, [Stack]: %s", err, buf[:n])
	}
}
