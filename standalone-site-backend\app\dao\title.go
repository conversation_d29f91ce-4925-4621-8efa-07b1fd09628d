package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"time"
	"trpc.publishing_application.standalonesite/app/model"
)

func TitleGet(id int64) (*model.Title, error) {
	var title model.Title
	db := DB.SelectConnect("db_standalonesite")
	if id > 0 {
		db = db.Where("id= ? AND is_del = ?", id, 0)
	}

	db = db.Joins("TitleLanguage")
	err := db.First(&title).Error
	if err != nil {
		return &title, err
	}

	return &title, nil
}

// 获取当前实时有效已分配的用户对应的称号的热度
func ListUserTitleHotInfo() (map[int64]*model.BindTitle, error) {
	var bindInfos []*model.UserBindTitleItem
	var err error
	sql := "select pubt.user_id,pubt.title_id,pt.init_hot from p_user_bind_title pubt left join p_title pt on pubt.title_id = pt.id where pubt.status=? and pubt.is_del=? and pt.up_time<? and pt.down_time>? and pt.status=? and pt.is_del=?"
	currentTime := time.Now().Unix()
	db := DB.SelectConnect("db_standalonesite")
	if err = db.Raw(sql, 1, 0, currentTime, currentTime, 2, 0).Find(&bindInfos).Error; err != nil {
		return nil, err
	}

	retMap := make(map[int64]*model.BindTitle)
	for _, item := range bindInfos {
		title := &model.TitleFormated{
			ID:      item.TitleId,
			InitHot: item.InitHot,
		}
		if retMapItem, isOK := retMap[item.UserId]; isOK {
			retMapItem.Titles = append(retMapItem.Titles, title)
		} else {
			retMap[item.UserId] = &model.BindTitle{
				Titles: []*model.TitleFormated{title},
			}
		}
	}

	return retMap, nil
}
