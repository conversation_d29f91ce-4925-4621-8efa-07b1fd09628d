// Package dragonact0704 龙之谷活动
package dragonact0704

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	redisOrigin "github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/global"
	dragonact0704Model "trpc.act.logicial/app/model/dragonact0704"
)

const (
	// KillDragonCacheKey 缓存Key
	KillDragonCacheKey = "dragon0704_kiil"
	// CacheSecond 缓存时间
	CacheSecond = 2160000 // 15天
)

// RecordDragonRank 记录龙已经击杀
func RecordDragonRank(ctx context.Context, tagId string) (err error) {
	// 更新表
	data := dragonact0704Model.Dragonact0704RankTmp{
		TagId: tagId,
	}
	condition := map[string]interface{}{
		"tag_id": tagId,
	}
	dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Where(condition).FirstOrCreate(&data).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
	}
	// 更新缓存
	_, err = doCacheDragonRank(ctx)

	return
}

// GetDragonRankList 获取击杀龙列表
func GetDragonRankList(ctx context.Context) (list map[string]int32, err error) {
	// 读缓存，没有则从表取
	redisKeyPrefix := global.GetPrefix()
	cacheKey := fmt.Sprintf("%v_%v", redisKeyPrefix, KillDragonCacheKey)
	fmt.Println("---------------cacheKey---------------")
	fmt.Printf("%#v\n", cacheKey)
	res, errR := redis.GetClient("logicial").Get(ctx, cacheKey).Result()
	fmt.Println("---------------res---------------")
	fmt.Printf("%#v\n", res)
	if errR != nil && errR != redisOrigin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errR.Error())
	}
	if res != "" {
		errM := json.Unmarshal([]byte(res), &list)
		if errM != nil {
			err = errs.NewCustomError(ctx, code.CacheDataError, "cache err=%v", errM.Error())
			redis.GetClient("logicial").Del(ctx, cacheKey)
		}
		return
	}
	// 设置成缓存
	list, err = doCacheDragonRank(ctx)
	return
}

func doCacheDragonRank(ctx context.Context) (list map[string]int32, err error) {
	// 从表里取数据
	var dataList []dragonact0704Model.Dragonact0704RankTmp
	db := DB.DefaultConnect().Debug().WithContext(ctx).Order("created_at asc").Find(&dataList)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	// 计算
	list = map[string]int32{
		"1": 0,
		"2": 0,
		"3": 0,
		"4": 0,
	}
	for key, item := range dataList {
		list[item.TagId] = int32(key) + 1
	}
	listByte, errM := json.Marshal(list)
	if errM != nil {
		err = errs.NewCustomError(ctx, code.CacheDataError, "do cache err=%v", errM.Error())
		return
	}
	// 设置成缓存
	redisKeyPrefix := global.GetPrefix()
	cacheKey := fmt.Sprintf("%v_%v", redisKeyPrefix, KillDragonCacheKey)
	_, errR := redis.GetClient().SetEX(ctx, cacheKey, string(listByte), time.Duration(CacheSecond)*time.Second).Result()
	if errR != nil && errR != redisOrigin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errR.Error())
	}
	return
}

// RecordChooseDragon 记录选择一条龙
func RecordChooseDragon(ctx context.Context, tagId string) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	data := dragonact0704Model.Dragonact0704RecordTmp{
		Uid:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		TagId:       tagId,
	}
	dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Where(data).FirstOrCreate(&data).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
	}
	return
}

// UpdateChooseDragonStatus 跟新选择龙的状态
func UpdateChooseDragonStatus(ctx context.Context, tagId string) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"tag_id":       tagId,
	}
	dbErr := DB.DefaultConnect().Model(dragonact0704Model.Dragonact0704RecordTmp{}).WithContext(ctx).Where(condition).
		Update("status", 1).
		Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	return
}

// GetCurDragonTagId 获取当前选择龙ID
func GetCurDragonTagId(ctx context.Context) (tagId string, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"status":       0,
	}
	var data dragonact0704Model.Dragonact0704RecordTmp
	db := DB.DefaultConnect().WithContext(ctx).Debug().Where(condition).First(&data)
	if db.Error != nil {
		if errors.Is(db.Error, gorm.ErrRecordNotFound) {
			tagId = ""
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	tagId = data.TagId
	return
}

// GetChooseDragonList 获取历史选择龙列表
func GetChooseDragonList(ctx context.Context) (tagIdList []string, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
	}
	tagIdList = make([]string, 0)
	var dataList []dragonact0704Model.Dragonact0704RecordTmp
	db := DB.DefaultConnect().Debug().WithContext(ctx).Where(condition).Find(&dataList)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	for _, item := range dataList {
		tagIdList = append(tagIdList, item.TagId)
	}
	return
}

// ReserveChannel 预约渠道
func ReserveChannel(ctx context.Context, channelId string, recordType int32) (err error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	data := dragonact0704Model.Dragonact0704ReserveTmp{
		Uid:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		ChannelId:   channelId,
		Type:        recordType,
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
		"channel_id":   channelId,
		"type":         recordType,
	}

	dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Where(condition).FirstOrCreate(&data).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
	}
	return
}

// HasReserveChannelList 已预约渠道列表
func HasReserveChannelList(ctx context.Context, recordType int32) (channelIds []string, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"type":         recordType,
	}
	channelIds = make([]string, 0)
	var dataList []dragonact0704Model.Dragonact0704ReserveTmp
	db := DB.DefaultConnect().Debug().WithContext(ctx).Where(condition).Find(&dataList)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	for _, item := range dataList {
		channelIds = append(channelIds, item.ChannelId)
	}
	return
}

// RecordPresent 记录礼包
func RecordPresent(ctx context.Context, tagId string, cdkey string) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	data := dragonact0704Model.Dragonact0704PresentTmp{
		Uid:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		TagId:       tagId,
		Cdkey:       cdkey,
	}
	dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Create(&data).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
	}
	return
}

// GetPresentList 已预约渠道列表
func GetPresentList(ctx context.Context) (dataList []dragonact0704Model.Dragonact0704PresentTmp, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
	}

	db := DB.DefaultConnect().Debug().WithContext(ctx).Table((&dragonact0704Model.Dragonact0704PresentTmp{}).TableName()).
		Where(condition).Find(&dataList)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	return
}
