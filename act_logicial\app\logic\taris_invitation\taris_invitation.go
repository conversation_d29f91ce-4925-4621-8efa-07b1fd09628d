// Package tarisinvitation 塔瑞斯人拉人 活动
package tarisinvitation

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/datadump"
	deltaversePb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/deltaverse"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	LotteryPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_lottery"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_taris_invitation"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/model/invitation"
	LotteryModel "trpc.act.logicial/app/model/lottery"
	invitationModel "trpc.act.logicial/app/model/taris_invitation"
)

// RegisterHTTP 注册信息http结构体
type RegisterHTTP struct {
	Data string `json:"data"`
}

// TarisInvitationResult TODO
type TarisInvitationResult struct {
	UID   string `gorm:"column:uid"`
	Total int    `gorm:"column:total"`
}

// GetInvitedPeopleAchieveData TODO
type GetInvitedPeopleAchieveData struct {
	ZoneID    string
	RoleID    string
	RoleName  string
	OpenID    string
	UpdatedAt int64
}

// LoginDaysHTTP 登录多少天的结构体
type LoginDaysHTTP struct {
	Data int32 `json:"data"`
}

// InvitedFriendInfoHTTP 获取用户详细信息结构体
type InvitedFriendInfoHTTP struct {
	Data []ItemInfo `json:"data"`
}

// ItemInfo 具体信息结构体
type ItemInfo struct {
	AfterLevel     string `json:"AfterLevel"`
	RoleGender     string `json:"RoleGender"`
	RoleProfession string `json:"RoleProfession"`
	DtEventTime    string `json:"dtEventTime"`
	VRoleName      string `json:"vRoleName"`
}

// InvitaDataType TODO
type InvitaDataType struct {
	Uid          string `gorm:"column:uid"`
	TotalScore   int    `gorm:"column:total_score"`
	TotalRecords int    `gorm:"column:total_records"`
}

// AsyncInviteTaskStatus 同步邀请人和被邀请人状态
func AsyncInviteTaskStatus(ctx context.Context, fsourceID string) (err error) {
	scheduleCtx := context.Background()
	// 确认查的表
	tableName := getTableName(fsourceID)
	// 分段uid获取 统一加机会
	uids, err := GetAllUid(scheduleCtx, tableName)
	if err != nil {
		return
	}

	maxGoroutines := 70
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	for _, item := range uids {
		wg.Add(1)
		go func(item string) (err error) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			err = TaskStatusByUID(scheduleCtx, tableName, item, fsourceID)
			if err != nil {
				return
			}
			return
		}(item)
		wg.Wait()
	}
	return
}

// SaveTaskPeopleNum 保存所有任务都完成的人数
func SaveTaskPeopleNum(fsourceID string, item *invitationModel.TarisInvitation) (err error) {
	rankTableName := GetRankTableName(fsourceID)
	scheduleCtx := context.Background()
	// var rankItem invitationModel.TarisInvitationRank
	rankItem := &invitationModel.TarisInvitationRank{
		UID: item.UID,
	}

	result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(rankTableName).Find(&rankItem)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 没有找到记录
			db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(rankTableName).
				FirstOrCreate(&rankItem)
			if db.Error != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", db.Error.Error())
				return
			}
			return
		}
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	maxNum := 5
	if fsourceID == "pageV3-1806" {
		maxNum = 10
	}
	if rankItem.Num < int64(maxNum) {
		rankItem.Num = rankItem.Num + 1
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(rankTableName).Where("id", rankItem.ID).
			Updates(rankItem)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
	}
	return
}

// GetRankTableName 获取表名
func GetRankTableName(fsourceID string) (
	tableName string,
) {
	tableName = invitationModel.TarisInvitationRank{}.TableName()
	if fsourceID == "pageV3-1806" {
		tableName = invitationModel.TarisInvitationRankAsia{}.TableName()
	}
	return
}

// GetAllUid 获取所有的uid
func GetAllUid(ctx context.Context, tableName string) (
	uids []string, err error,
) {
	scheduleCtx := context.Background()

	result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).Group("uid").Select("uid").Find(&uids)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	return
}

// GetTableDataByPageSize 获取条数
func GetTableDataByPageSize(ctx context.Context, tableName string, offset int, pageSize int) (
	list []*invitationModel.TarisInvitation, err error,
) {
	list = make([]*invitationModel.TarisInvitation, 0)
	scheduleCtx := context.Background()

	db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).Offset(offset).Limit(pageSize).
		Order("id asc").
		Find(&list)
	if db.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	return
}

// TaskStatusByUID 通过http请求获取是否完成任务
func TaskStatusByUID(ctx context.Context, tableName string, uid string, fsourceID string) (
	err error,
) {
	updateItems := make([]*invitationModel.TarisInvitation, 0)
	scheduleCtx := context.Background()
	db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
		Where("uid = ?", uid).
		Order("id asc").
		Find(&updateItems)
	if db.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	var addNum int32 = 0

	maxGoroutines := 70
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	for _, item := range updateItems {
		wg.Add(1)
		go func(item *invitationModel.TarisInvitation) (err error) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			oldItem := &invitationModel.TarisInvitation{
				Task1: item.Task1,
				Task2: item.Task2,
				Task3: item.Task3,
			}
			// 任务一判断是否已经注册 如果注册的话就完成任务一
			if item.Task1 == 0 {
				registerTime, err1 := GetRegisterTime(scheduleCtx, item.InvitationUID, fsourceID)
				if err1 != nil {
					return err1
				}
				if registerTime != "" {
					item.Task1 = 1
					ReportTlog(scheduleCtx, "pull_p_backup_task_finish_status", "cm_click", 29157, map[string]interface{}{
						"open_id":   item.InvitationUID,
						"task_id":   1,
						"task_name": "create role",
					})
					// AddInviteeOpenidLottery(ctx, fsourceID, strings.Split(item.InvitationUID, "-")[1], 1)
					if item.UID != "" {
						// addNum = addNum + 1
						atomic.AddInt32(&addNum, 1)
						// AddInviteeOpenidLottery(scheduleCtx, fsourceID, item.UID, 1)
					}
				}
			}
			if item.Task2 == 0 {
				info, err2 := GetInvitedInfo(scheduleCtx, item.InvitationUID, fsourceID)
				if err2 != nil {
					return err2
				}

				// 等级大于50
				AfterLevel, err := strconv.ParseInt(info.AfterLevel, 10, 64)
				if err != nil {
					log.WithFieldsContext(scheduleCtx, "log_type", "error").Errorf(fmt.Sprintf(
						"[taris] AfterLevel change int error: [%+v]",
						info.AfterLevel))
				}
				if AfterLevel >= 50 {
					item.Task2 = 1
					ReportTlog(scheduleCtx, "pull_p_backup_task_finish_status", "cm_click", 29157, map[string]interface{}{
						"open_id":   item.InvitationUID,
						"task_id":   2,
						"task_name": "role afterLevel is 50",
					})
					// AddInviteeOpenidLottery(ctx, fsourceID, strings.Split(item.InvitationUID, "-")[1], 1)
					if item.UID != "" {
						// addNum = addNum + 1
						atomic.AddInt32(&addNum, 1)
						// AddInviteeOpenidLottery(scheduleCtx, fsourceID, item.UID, 1)
					}
				}
			}
			if item.Task3 == 0 {
				times, err3 := GetClearanceTimes(scheduleCtx, item.InvitationUID, fsourceID)
				if err3 != nil {
					return err3
				}

				if times != "" {
					num, errS := strconv.Atoi(times)
					if errS != nil {

						log.WithFieldsContext(scheduleCtx, "log_type", "error").Errorf(fmt.Sprintf("[taris] times is error: [%+v]",
							num))
						return
					}

					// 秘境通过2次
					if num >= 2 {
						item.Task3 = 1
						ReportTlog(scheduleCtx, "pull_p_backup_task_finish_status", "cm_click", 29157, map[string]interface{}{
							"open_id":   item.InvitationUID,
							"task_id":   3,
							"task_name": "player realm is 2",
						})
						// AddInviteeOpenidLottery(ctx, fsourceID, strings.Split(item.InvitationUID, "-")[1], 1)
						if item.UID != "" {
							// addNum = addNum + 1
							atomic.AddInt32(&addNum, 1)
							// AddInviteeOpenidLottery(scheduleCtx, fsourceID, item.UID, 1)
						}
					}
				}
			}
			err = UpdateData(scheduleCtx, tableName, item)
			if err != nil {
				return err
			}
			// 如果邀请人的三个任务都完成了就+1给另外一个表
			if item.Task1 == 1 && item.Task2 == 1 && item.Task3 == 1 && (oldItem.Task1 != 1 || oldItem.Task2 != 1 ||
				oldItem.Task3 != 1) {
				// 查询是否有15个了 如果没有就+1 如果有就不+1
				err = SaveTaskPeopleNum(fsourceID, item)
			}
			time.Sleep(time.Second * 1)
			return
		}(item)
	}
	wg.Wait()

	if addNum > 0 {
		AddInviteeOpenidLottery(scheduleCtx, fsourceID, uid, 1, int64(addNum))
	}

	return
}

// UpdateData 更新数据
func UpdateData(ctx context.Context, tableName string, item *invitationModel.TarisInvitation) (
	err error,
) {
	scheduleCtx := context.Background()

	db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).Where("id", item.ID).Updates(item)
	if db.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	return
}

// InvitePeopleList 邀请人任务完成列表
func InvitePeopleList(ctx context.Context, fsourceID string, pageSize int32, pageNum int32) (
	list []*pb.InvitePeopleItem, err error,
) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	dbList := make([]*invitationModel.TarisInvitation, 0)
	list = make([]*pb.InvitePeopleItem, 0)
	// 通过创建时间来排序 然后拿数据
	tableName := getTableName(fsourceID)

	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where("uid = ?", strings.Split(userAccount.Uid, "-")[1]).
		Limit(int(pageSize)).
		Offset(int((pageNum - 1) * pageSize)).
		Find(&dbList)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	for _, item := range dbList {
		list = append(list, &pb.InvitePeopleItem{
			Task_1:             item.Task1,
			Task_2:             item.Task2,
			Task_3:             item.Task3,
			RoleName:           item.RoleName,
			InvitationRoleName: item.InvitationRoleName,
		})
	}
	return
}

// InvitePeopleListTotal 邀请人任务完成列表总数
func InvitePeopleListTotal(ctx context.Context, fsourceID string) (
	total int32, err error,
) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	tableName := getTableName(fsourceID)

	var totalNum int64
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where("uid = ?", strings.Split(userAccount.Uid, "-")[1]).
		Count(&totalNum)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	total = int32(totalNum)
	return
}

// InviteTaskFinishNum 被邀请人各个任务完成数量
func InviteTaskFinishNum(ctx context.Context, fsourceID string) (
	req *pb.InviteTaskFinishNumRsp, err error,
) {
	req = &pb.InviteTaskFinishNumRsp{
		Task_1: 0,
		Task_2: 0,
		Task_3: 0,
	}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	// 查询数据库中uid 为当前用户

	// task_1 为true 的count
	// task_2 为true 的count
	// task_3 为true 的count
	// task_4 为true 的count

	tableName := getTableName(fsourceID)

	var totalNum int64
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where("uid = ? and task_1 = ?", strings.Split(userAccount.Uid, "-")[1], 1).
		Count(&totalNum)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("[taris] get self: [%+v]",
		totalNum))
	req.Task_1 = int32(totalNum)
	totalNum = 0

	db = DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where("uid = ? and task_2 = ?", strings.Split(userAccount.Uid, "-")[1], 1).
		Count(&totalNum)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	req.Task_2 = int32(totalNum)
	totalNum = 0

	db = DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where("uid = ? and task_3 = ?", strings.Split(userAccount.Uid, "-")[1], 1).
		Count(&totalNum)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	req.Task_3 = int32(totalNum)
	totalNum = 0
	return
}

// AddInviteData 添加被邀请人记录
func AddInviteData(ctx context.Context, fsourceID string, inviteeOpenid string) (
	err error,
) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	tableName := getTableName(fsourceID)

	isNewInstall, isActive, err := IsNewInstallOrActive(ctx, userAccount.Uid, fsourceID)
	if err != nil {
		return
	}

	item := &invitationModel.TarisInvitationAsia{
		UID:           inviteeOpenid,
		InvitationUID: userAccount.Uid,
		IsNewInstall:  0,
		IsActive:      0,
		RoleName:      "",
	}

	invitationInfo, err := GetInvitedInfo(ctx, userAccount.Uid, fsourceID)
	if err != nil {
		return
	}
	info, err := GetInvitedInfo(ctx, fmt.Sprintf("29175-%v", inviteeOpenid), fsourceID)
	if err != nil {
		return
	}

	if invitationInfo.VRoleName != "" {
		// 如果有角色名称代表有角色
		item.InvitationRoleName = invitationInfo.VRoleName
		item.Task1 = 1
		// 上报
		ReportTlog(ctx, "pull_p_backup_task_finish_status", "cm_click", 29157, map[string]interface{}{
			"open_id":   strings.Split(userAccount.Uid, "-")[1],
			"task_id":   1,
			"task_name": "create role",
		})
	}

	if info.VRoleName != "" {
		// 拿到邀请人角色名称
		item.RoleName = info.VRoleName
	}

	if isNewInstall {
		item.IsNewInstall = 1
	} else if isActive {
		item.IsActive = 1
	}

	if isActive || isNewInstall {
		// 添加log
		log.WithFieldsContext(ctx, "log_type", "AddInviteData", "str_field_1", fsourceID).
			Infof("AddInviteData")
		db := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
			Where("uid = ? and invitation_uid = ?", item.UID, item.InvitationUID).
			FirstOrCreate(&item)
		if db.Error != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		severId := "EUNA"
		if fsourceID == "pageV3-1806" {
			severId = "ASIA"
		}
		ReportTlog(ctx, "pull_p_backup_invite_person_success", "cm_click", 29157, map[string]interface{}{
			"open_id":     strings.Split(userAccount.Uid, "-")[1],
			"dst_open_id": inviteeOpenid,
			"service_id":  severId,
		})

		// 如果有注册角色了就添加奖励
		if item.RoleName != "" {
			// 给邀请人被邀请人 添加抽奖机会
			// AddInviteeOpenidLottery(ctx, fsourceID, strings.Split(userAccount.Uid, "-")[1], 1)
			AddInviteeOpenidLottery(ctx, fsourceID, inviteeOpenid, 1, 1)
		}
	}
	return
}

// AddInviteeOpenidLottery 给被邀请人添加抽奖机会
func AddInviteeOpenidLottery(ctx context.Context, fsourceID string, inviteeOpenid string, lotteryID int64,
	addNum int64) (err error) {
	scheduleCtx := context.Background()

	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         fmt.Sprintf("29175-%v", inviteeOpenid),
		AccountType: accountPb.AccountType(1),
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    inviteeOpenid,
			ChannelId: 3,
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	// 给邀请人添加发奖机会 inviteeOpenid 添加抽奖机会
	lottery := LotteryPb.NewLotteryClientProxy()

	totalLimit := 15
	if fsourceID == "pageV3-1806" {
		totalLimit = 40
	}
	realAddNum, err := lottery.AddNumsLottery(scheduleCtx, &LotteryPb.AddNumsLotteryReq{
		FsourceId:  fsourceID,
		LotteryId:  lotteryID,
		DayLimit:   0,
		TotalLimit: int64(totalLimit),
		AddNum:     addNum,
	}, callopts...)

	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "error").Errorf(fmt.Sprintf(
			"[taris] add inviteeOpenid lottery add  err:%v",
			err.Error()))
	}
	if realAddNum.RealAddNum == 0 {
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
			"[taris] inviteeOpenid lottery add real num:%v",
			realAddNum.RealAddNum))
	}

	return
}

// getTableName 获取表名
func getTableName(fsourceID string) (
	tableName string,
) {
	tableName = invitationModel.TarisInvitation{}.TableName()
	if fsourceID == "pageV3-1806" {
		tableName = invitationModel.TarisInvitationAsia{}.TableName()
	}
	return
}

// IsNewInstallOrActive 判断用户是新进还是回流
func IsNewInstallOrActive(ctx context.Context, uid string, fsourceID string) (
	isNewInstall bool, isActive bool, err error,
) {
	isNewInstall = false
	isActive = false
	// 调用http接口
	registerTime, err := GetRegisterTime(ctx, uid, fsourceID)
	if err != nil {
		return
	}

	loginDays, err := GetLoginDays(ctx, uid, fsourceID)
	if err != nil {
		return
	}

	if registerTime == "" {
		return
	}
	// 获取注册时间
	layout := "2006-01-02 15:04:05"
	utcTimeStr := "2024-10-21 00:00:00"
	utcTime, err := time.Parse(layout, utcTimeStr)
	if err != nil {
		return
	}
	givenTime, err := time.Parse(layout, registerTime)
	if err != nil {
		return
	}

	utcTime = utcTime.UTC()

	if givenTime.After(utcTime) {
		isNewInstall = true
	}

	now := time.Now()
	eday := now.AddDate(0, 0, -7)

	// 注册时间在7天之前并且近7天活跃天数为0的就是回流用户
	if givenTime.Before(eday) && loginDays == 0 {
		isActive = true
	}

	return
}

// GetRegisterTime 获取注册时间
func GetRegisterTime(ctx context.Context, uid string, fsourceID string) (time string, err error) {
	scheduleCtx := context.Background()

	paramMap := make(map[string]interface{})
	paramMap["openid"] = strings.Split(uid, "-")[1]
	paramMap["region"] = "EUNA"
	if fsourceID == "pageV3-1806" {
		paramMap["region"] = "ASIA"
	}
	response, err := deltaversePb.SendRequest(scheduleCtx, deltaversePb.SendRequestParam{
		ServiceType:        "taris",
		DestinationService: "dmfeature-12762",
		Path:               "/dmfeature/12762/registerTimeByRegion",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "http_error").Errorf(
			"registerTime SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		return
	}
	var respData RegisterHTTP
	err = json.Unmarshal([]byte(response), &respData)
	if err != nil {
		return
	}
	time = respData.Data
	return
}

// GetLoginDays 获取前七天的登录时间
func GetLoginDays(ctx context.Context, uid string, fsourceID string) (days int32, err error) {
	scheduleCtx := context.Background()
	// 获取当前时间
	now := time.Now()
	sday := now.AddDate(0, 0, -7)
	sdayTimestamp := sday.Format("20060102")
	eday := now.AddDate(0, 0, -1)
	edayTimestamp := eday.Format("20060102")
	paramMap := make(map[string]interface{})
	paramMap["openid"] = strings.Split(uid, "-")[1]
	paramMap["sday"] = sdayTimestamp
	paramMap["eday"] = edayTimestamp
	paramMap["region"] = "EUNA"
	if fsourceID == "pageV3-1806" {
		paramMap["region"] = "ASIA"
	}

	response, err := deltaversePb.SendRequest(scheduleCtx, deltaversePb.SendRequestParam{
		ServiceType:        "taris",
		DestinationService: "dmfeature-12762",
		Path:               "/dmfeature/12762/loginDaysByRegion",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "http_error").Errorf(
			"loginDays SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		return
	}
	var respData LoginDaysHTTP
	err = json.Unmarshal([]byte(response), &respData)
	if err != nil {
		return
	}
	days = respData.Data
	return
}

// GetInvitedInfo 获取被邀请人信息
func GetInvitedInfo(ctx context.Context, uid string, fsourceID string) (info *ItemInfo, err error) {
	scheduleCtx := context.Background()
	info = &ItemInfo{
		AfterLevel:     "0",
		RoleGender:     "0",
		RoleProfession: "0",
		DtEventTime:    "",
		VRoleName:      "",
	}
	paramMap := make(map[string]interface{})
	paramMap["keys"] = strings.Split(uid, "-")[1]
	if fsourceID == "pageV3-1806" {
		paramMap["keys"] = fmt.Sprintf("%v|ASIA", paramMap["keys"])
	} else {
		paramMap["keys"] = fmt.Sprintf("%v|EUNA", paramMap["keys"])
	}

	response, err := deltaversePb.SendRequest(scheduleCtx, deltaversePb.SendRequestParam{
		ServiceType:        "taris",
		DestinationService: "dmfeature-12762",
		Path:               "/dmfeature/12762/invitedFriendInfoByRegion",
		Data:               paramMap,
		RequestType:        http.MethodPost,
	})
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "http_error").Errorf(
			"invitedFriendInfo SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		return
	}

	var respData InvitedFriendInfoHTTP
	err = json.Unmarshal([]byte(response), &respData)
	if err != nil {
		return
	}
	infos := respData.Data
	if len(infos) > 0 {
		info = &infos[0]
		decodedBytes, err := base64.StdEncoding.DecodeString(info.VRoleName)
		if err != nil {
			log.WithFieldsContext(scheduleCtx, "log_type", "error").Errorf(fmt.Sprintf("[taris] base64 error: %v",
				info.VRoleName))
		}
		info.VRoleName = string(decodedBytes)
	}
	return
}

// GetClearanceTimes 获取秘境通关次数
func GetClearanceTimes(ctx context.Context, uid string, fsourceID string) (times string, err error) {
	paramMap := make(map[string]interface{})
	scheduleCtx := context.Background()
	paramMap["openid"] = strings.Split(uid, "-")[1]
	paramMap["region"] = "EUNA"
	if fsourceID == "pageV3-1806" {
		paramMap["region"] = "ASIA"
	}

	response, err := deltaversePb.SendRequest(scheduleCtx, deltaversePb.SendRequestParam{
		ServiceType:        "taris",
		DestinationService: "dmfeature-12762",
		Path:               "/dmfeature/12762/playerRealmCountByRegion",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "http_error").Errorf(
			"GetClearanceTimes SendRequest err; openId:[%v],err:[%v]", paramMap["openid"], err)
		return
	}
	var respData RegisterHTTP
	err = json.Unmarshal([]byte(response), &respData)
	if err != nil {
		return
	}
	times = respData.Data
	return
}

// GetNumFinishAllTask 获取多少邀请用户完成了所有任务
func GetNumFinishAllTask(ctx context.Context, fsourceID string) (num int64, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	tableName := getTableName(fsourceID)

	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where("task_1 = ? and task_2 = ? and task_3 = ?", 1, 1, 1).
		// Where("task_1 = ? and task_2 = ? and task_3 = ?", 1, 1, 0).
		Where("uid = ?", strings.Split(userAccount.Uid, "-")[1]).
		Count(&num)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	return
}

// ReportTlog 上报
func ReportTlog(ctx context.Context, action string, subAction string, gameId int32,
	ExtentContent map[string]interface{}) (err error) {
	langType := metadata.GetLangType(ctx)
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: langType,
			XGameId:   cast.ToInt(gameId),
			XSource:   "pc_web",
		},
		Action:         action,
		SubAction:      subAction,
		OriginalGameId: cast.ToString(gameId),
		ExtentContent:  ExtentContent,
	}
	report.ReportTlog(ctx, tlogData)
	log.WithFieldsContext(ctx, "log_type", action).Infof(
		"vote ReportTlog action:[%v],,gameId:[%v],ExtentContent:[%+v] ",
		action, gameId, ExtentContent)
	return
}

// GetInvitedExcel 人拉人活动数据导出
func GetInvitedExcel(ctx context.Context, fsourceID string) (err error) {
	// 先拿邀请码和openid
	// inviation_account_shortcode_09
	scheduleCtx := context.Background()

	data := make([][]string, 1)
	data[0] = []string{
		"area_id", "role_id", "opend_id", "share_code", "invited_num", "task_num", "all_task_num",
	}

	EUNAShortCodes := make([]*invitation.ShortCode, 0)
	result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table("inviation_account_shortcode_09").
		Find(&EUNAShortCodes)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}
	ASIAhortCodes := make([]*invitation.ShortCode, 0)
	result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table("inviation_account_shortcode_08").
		Find(&ASIAhortCodes)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	maxGoroutines := 100
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup

	for _, item := range EUNAShortCodes {
		wg.Add(1)
		go func(item *invitation.ShortCode) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			openID := strings.Split(item.UID, "-")[1]
			accountData, _ := proto.Marshal(&accountPb.UserAccount{
				Uid:         item.UID,
				AccountType: accountPb.AccountType(1),
				IntlAccount: &accountPb.IntlAccount{
					OpenId:    openID,
					ChannelId: 3,
				},
			})
			callopts := []client.Option{
				client.WithMetaData(metadata.UserAccount, accountData),
			}
			// 通过uid查找绑定role_id
			gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: "pageV3-1803"}
			gameProxy := gamePb.NewGameClientProxy()
			gameRoleInfo, roleErr := gameProxy.GetSavedRoleInfo(scheduleCtx, gameReq, callopts...)
			if roleErr != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("get role info error by uid: %v",
					roleErr))
				return
			}

			var resultData InvitaDataType
			tableName := getTableName("pageV3-1803")
			result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
				Where("uid = ?", openID).
				Select("uid, SUM(task_1 + task_2 + task_3) as total_score, COUNT(*) as total_records").
				Group("uid").
				Scan(&resultData)
			if result.Error != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql error, \t [Error]:{%v} ", result.Error)
				return
			}

			var allTaskNum int64
			result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
				Where("uid = ? and task_1 = 1 and task_2 = 1 and task_3 = 1", openID).
				Count(&allTaskNum)
			if result.Error != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql error, \t [Error]:{%v} ", result.Error)
				return
			}

			// "area_id", "opend_id", "share_code", "invited_num", "task_num", "all_task_num",
			data = append(data, []string{
				fmt.Sprintf("%v", gameRoleInfo.ZoneId), gameRoleInfo.RoleId, openID, item.ShareCode, fmt.Sprintf("%v",
					resultData.TotalRecords), fmt.Sprintf("%v", resultData.TotalScore),
				fmt.Sprintf("%v", allTaskNum),
			})

		}(item)

	}
	wg.Wait()

	for _, item := range ASIAhortCodes {
		wg.Add(1)
		go func(item *invitation.ShortCode) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			openID := strings.Split(item.UID, "-")[1]
			accountData, _ := proto.Marshal(&accountPb.UserAccount{
				Uid:         item.UID,
				AccountType: accountPb.AccountType(1),
				IntlAccount: &accountPb.IntlAccount{
					OpenId:    openID,
					ChannelId: 3,
				},
			})
			callopts := []client.Option{
				client.WithMetaData(metadata.UserAccount, accountData),
			}
			// 通过uid查找绑定role_id
			gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: "pageV3-1806"}
			gameProxy := gamePb.NewGameClientProxy()
			gameRoleInfo, roleErr := gameProxy.GetSavedRoleInfo(scheduleCtx, gameReq, callopts...)
			if roleErr != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("get role info error by uid: %v",
					roleErr))
				return
			}

			var resultData InvitaDataType
			tableName := getTableName("pageV3-1806")
			result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
				Where("uid = ?", openID).
				Select("uid, SUM(task_1 + task_2 + task_3) as total_score, COUNT(*) as total_records").
				Group("uid").
				Scan(&resultData)
			if result.Error != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql error, \t [Error]:{%v} ", result.Error)
				return
			}

			var allTaskNum int64
			result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
				Where("uid = ? and task_1 = 1 and task_2 = 1 and task_3 = 1", openID).
				Count(&allTaskNum)
			if result.Error != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql error, \t [Error]:{%v} ", result.Error)
				return
			}

			// "area_id", "opend_id", "share_code", "invited_num", "task_num", "all_task_num",
			data = append(data, []string{
				fmt.Sprintf("%v", gameRoleInfo.ZoneId), gameRoleInfo.RoleId, openID, item.ShareCode, fmt.Sprintf("%v",
					resultData.TotalRecords), fmt.Sprintf("%v", resultData.TotalScore),
				fmt.Sprintf("%v", allTaskNum),
			})

		}(item)

	}
	wg.Wait()
	// 在拿对应的areaid
	// 在拿对应的拉人数和任务完成数，3个任务完成数量
	filepath, err := datadump.CreateExcel(scheduleCtx, "invited-data1.xlsx", data)
	if err != nil {
		return
	}
	mediaId, err := datadump.QWXUploadMedia(ctx, filepath, "268e5d13-f0db-4bf4-afa2-3112dabdd29c", "invited-data1.xlsx")
	if err != nil {
		return
	}
	log.WithFieldsContext(ctx, "log_info").Infof("mediaId: %v", mediaId)
	option := &datadump.SendOption{
		Msgtype: "file",
		Chatid:  "wrkSFfCgAA9d0ZmRiwpSS3Y-xrxLcH4g",
		File: &datadump.FileType{
			MediaId: mediaId,
		},
	}
	_, err = datadump.QWXSend(ctx, option, "268e5d13-f0db-4bf4-afa2-3112dabdd29c")
	if err != nil {
		return
	}

	tableData := make([][]string, 0)
	tableData = append(tableData, []string{
		"邀请人openid", "被邀请人opeind", "任务完成数", "zone_id", "role_id", "role_name",
	})

	EUNAInvitation := make([]*invitationModel.TarisInvitation, 0)
	result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(invitationModel.TarisInvitation{}.TableName()).
		Find(&EUNAInvitation)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	for _, item := range EUNAInvitation {
		wg.Add(1)
		go func(item *invitationModel.TarisInvitation) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			openID := strings.Split(item.InvitationUID, "-")[1]
			accountData, _ := proto.Marshal(&accountPb.UserAccount{
				Uid:         item.InvitationUID,
				AccountType: accountPb.AccountType(1),
				IntlAccount: &accountPb.IntlAccount{
					OpenId:    openID,
					ChannelId: 3,
				},
			})
			callopts := []client.Option{
				client.WithMetaData(metadata.UserAccount, accountData),
			}
			// 通过uid查找绑定role_id
			gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: "pageV3-1803"}
			gameProxy := gamePb.NewGameClientProxy()
			gameRoleInfo, roleErr := gameProxy.GetSavedRoleInfo(scheduleCtx, gameReq, callopts...)
			if roleErr != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("get role info error by uid: %v",
					roleErr))
				return
			}
			tableData = append(tableData, []string{
				item.UID, strings.Split(item.InvitationUID, "-")[1], fmt.Sprintf("%v", item.Task1+item.Task2+item.Task3),
				fmt.Sprintf("%v",
					gameRoleInfo.ZoneId), gameRoleInfo.RoleId, gameRoleInfo.RoleName,
			})
		}(item)
	}
	wg.Wait()
	ASIAInvitation := make([]*invitationModel.TarisInvitationAsia, 0)
	result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(invitationModel.TarisInvitationAsia{}.TableName()).
		Find(&ASIAInvitation)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	for _, item := range ASIAInvitation {
		wg.Add(1)
		go func(item *invitationModel.TarisInvitationAsia) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			openID := strings.Split(item.InvitationUID, "-")[1]
			accountData, _ := proto.Marshal(&accountPb.UserAccount{
				Uid:         item.InvitationUID,
				AccountType: accountPb.AccountType(1),
				IntlAccount: &accountPb.IntlAccount{
					OpenId:    openID,
					ChannelId: 3,
				},
			})
			callopts := []client.Option{
				client.WithMetaData(metadata.UserAccount, accountData),
			}
			// 通过uid查找绑定role_id
			gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: "pageV3-1806"}
			gameProxy := gamePb.NewGameClientProxy()
			gameRoleInfo, roleErr := gameProxy.GetSavedRoleInfo(scheduleCtx, gameReq, callopts...)
			if roleErr != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("get role info error by uid: %v",
					roleErr))
				return
			}
			tableData = append(tableData, []string{
				item.UID, strings.Split(item.InvitationUID, "-")[1], fmt.Sprintf("%v", item.Task1+item.Task2+item.Task3),
				fmt.Sprintf("%v",
					gameRoleInfo.ZoneId), gameRoleInfo.RoleId, gameRoleInfo.RoleName,
			})
		}(item)
	}
	wg.Wait()

	filepath, err = datadump.CreateExcel(scheduleCtx, "invited-data2.xlsx", tableData)
	if err != nil {
		return
	}
	mediaId, err = datadump.QWXUploadMedia(ctx, filepath, "268e5d13-f0db-4bf4-afa2-3112dabdd29c", "invited-data2.xlsx")
	if err != nil {
		return
	}
	log.WithFieldsContext(ctx, "log_info").Infof("mediaId: %v", mediaId)
	option = &datadump.SendOption{
		Msgtype: "file",
		Chatid:  "wrkSFfCgAA9d0ZmRiwpSS3Y-xrxLcH4g",
		File: &datadump.FileType{
			MediaId: mediaId,
		},
	}
	_, err = datadump.QWXSend(ctx, option, "268e5d13-f0db-4bf4-afa2-3112dabdd29c")
	if err != nil {
		return
	}

	return
}

// GetInvitedIsNewinstallOrIsActive 获取邀请人列表和其新进还是回流
func GetInvitedIsNewinstallOrIsActive(ctx context.Context, fsourceID string) (err error) {
	scheduleCtx := context.Background()

	data := make([][]interface{}, 1)
	data[0] = []interface{}{
		"uid", "is_newinstall", "is_active", "invitation_uid", "invitation_is_newinstall", "invitation_is_active",
	}

	list := make([]*invitationModel.TarisInvitationAsia, 0)
	tableName := getTableName(fsourceID)
	result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
		Find(&list)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	for _, item := range list {
		isNewInstall, isActive, err1 := IsNewInstallOrActive(scheduleCtx, fmt.Sprintf("29175-%v", item.UID), fsourceID)
		if err1 != nil {
			return err1
		}

		newInstall := 0
		if isNewInstall {
			newInstall = 1
		}
		active := 0
		if isActive {
			active = 1
		}

		openID := strings.Split(item.InvitationUID, "-")[1]
		data = append(data, []interface{}{
			item.UID, newInstall, active, openID, item.IsNewInstall, item.IsActive,
		})

		f := excelize.NewFile()
		// 将data数据写到excel中
		// 使用循环按行插入数据
		for i, row := range data {
			for j, col := range row {
				cellName := fmt.Sprintf("%c%d", 'A'+j, i+1)
				f.SetCellValue("Sheet1", cellName, col)
			}
		}
		// 保存 Excel 文件
		err = f.SaveAs("invited-active-newinstall-data.xlsx")
		if err != nil {
			log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf("create elxcel err: %v", err))
		}

	}
	return
}

// GetInvitedPeopleAchieve TODO
func GetInvitedPeopleAchieve(ctx context.Context) (err error) {
	scheduleCtx := context.Background()

	var results []TarisInvitationResult
	result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(invitationModel.TarisInvitation{}.TableName()).
		Select("uid, COUNT(*) as total").
		Where("task_1 = ? AND task_2 = ? AND task_3 = ?", 1, 1, 1).
		Group("uid").
		Having("total >= ?", 5).
		Scan(&results)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	data := make([]*GetInvitedPeopleAchieveData, 0)

	for _, item := range results {
		// lottery_account_total_log
		account := accountPb.UserAccount{
			Uid:         fmt.Sprintf("29175-%v", item.UID),
			AccountType: 1,
		}
		var lotteryData []*LotteryModel.LotteryTotalData
		tableName, err1 := model.GetTableNameWithAccount(scheduleCtx, &account, LotteryModel.LotteryTotalData{}.TableName())
		if err1 != nil {
			err = err1
			return
		}
		result = DB.DefaultConnect().WithContext(scheduleCtx).Table(tableName).
			Where("uid = ? and Fsource_id = ? and total_num >= 15", fmt.Sprintf("29175-%v", item.UID), "pageV3-1803").
			Order("updated_at asc").
			Find(&lotteryData)
		if result.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql error, \t [Error]:{%v} ", result.Error)
			return
		}
		if len(lotteryData) > 0 {
			accountData, _ := proto.Marshal(&accountPb.UserAccount{
				Uid:         fmt.Sprintf("29175-%v", item.UID),
				AccountType: accountPb.AccountType(1),
				IntlAccount: &accountPb.IntlAccount{
					OpenId:    item.UID,
					ChannelId: 3,
				},
			})
			callopts := []client.Option{
				client.WithMetaData(metadata.UserAccount, accountData),
			}
			// 通过uid查找绑定role_id
			gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: "pageV3-1803"}
			gameProxy := gamePb.NewGameClientProxy()
			gameRoleInfo, roleErr := gameProxy.GetSavedRoleInfo(scheduleCtx, gameReq, callopts...)
			if roleErr != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("get role info error by uid: %v",
					roleErr))
				return
			}

			data = append(data, &GetInvitedPeopleAchieveData{
				RoleID:    gameRoleInfo.RoleId,
				RoleName:  gameRoleInfo.RoleName,
				ZoneID:    fmt.Sprintf("%v", gameRoleInfo.ZoneId),
				OpenID:    item.UID,
				UpdatedAt: lotteryData[0].CreatedAt,
			})
		}
	}

	var AsiaResults []TarisInvitationResult
	result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(invitationModel.TarisInvitationAsia{}.TableName()).
		Select("uid, COUNT(*) as total").
		Where("task_1 = ? AND task_2 = ? AND task_3 = ?", 1, 1, 1).
		Group("uid").
		Having("total >= ?", 10).
		Scan(&AsiaResults)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	for _, item := range AsiaResults {
		// lottery_account_total_log
		account := accountPb.UserAccount{
			Uid:         fmt.Sprintf("29175-%v", item.UID),
			AccountType: 1,
		}
		var lotteryData []*LotteryModel.LotteryTotalData
		tableName, err1 := model.GetTableNameWithAccount(scheduleCtx, &account, LotteryModel.LotteryTotalData{}.TableName())
		if err1 != nil {
			err = err1
			return
		}
		result = DB.DefaultConnect().WithContext(scheduleCtx).Table(tableName).
			Where("uid = ? and Fsource_id = ? and total_num >= 30", fmt.Sprintf("29175-%v", item.UID), "pageV3-1806").
			Order("updated_at asc").
			Find(&lotteryData)
		if result.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql error, \t [Error]:{%v} ", result.Error)
			return
		}
		if len(lotteryData) > 0 {
			accountData, _ := proto.Marshal(&accountPb.UserAccount{
				Uid:         fmt.Sprintf("29175-%v", item.UID),
				AccountType: accountPb.AccountType(1),
				IntlAccount: &accountPb.IntlAccount{
					OpenId:    item.UID,
					ChannelId: 3,
				},
			})
			callopts := []client.Option{
				client.WithMetaData(metadata.UserAccount, accountData),
			}
			// 通过uid查找绑定role_id
			gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: "pageV3-1806"}
			gameProxy := gamePb.NewGameClientProxy()
			gameRoleInfo, roleErr := gameProxy.GetSavedRoleInfo(scheduleCtx, gameReq, callopts...)
			if roleErr != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("get role info error by uid: %v",
					roleErr))
				return
			}

			data = append(data, &GetInvitedPeopleAchieveData{
				RoleID:    gameRoleInfo.RoleId,
				RoleName:  gameRoleInfo.RoleName,
				ZoneID:    fmt.Sprintf("%v", gameRoleInfo.ZoneId),
				OpenID:    item.UID,
				UpdatedAt: lotteryData[0].CreatedAt,
			})
		}
	}

	excelData := make([][]string, 0)
	excelData = append(excelData, []string{
		"zone_id", "role_id", "role_name", "openid", "UpdatedAt",
	})
	for _, item := range data {
		excelData = append(excelData, []string{
			item.ZoneID, item.RoleID, item.RoleName, item.OpenID, fmt.Sprintf("%v", item.UpdatedAt),
		})
	}
	filepath, err := datadump.CreateExcel(scheduleCtx, "invited-data3.xlsx", excelData)
	if err != nil {
		return
	}
	mediaId, err := datadump.QWXUploadMedia(scheduleCtx, filepath, "268e5d13-f0db-4bf4-afa2-3112dabdd29c",
		"invited-data3.xlsx")
	if err != nil {
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_info").Infof("mediaId: %v", mediaId)
	option := &datadump.SendOption{
		Msgtype: "file",
		Chatid:  "wrkSFfCgAA9d0ZmRiwpSS3Y-xrxLcH4g",
		File: &datadump.FileType{
			MediaId: mediaId,
		},
	}
	_, err = datadump.QWXSend(scheduleCtx, option, "268e5d13-f0db-4bf4-afa2-3112dabdd29c")
	if err != nil {
		return
	}
	return
}
