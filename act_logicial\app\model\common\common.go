// Package common TODO
package common

import "trpc.act.logicial/app/model"

// CommonShareLogModel TODO
type CommonShareLogModel struct {
	model.AppModel
}

// TableName .
func (CommonShareLogModel) TableName() string {
	return "common_share_log"
}

// CommonShareLog TODO
type CommonShareLog struct {
	CommonShareLogModel
	ID        int64  `gorm:"type:int(11);column:id;primary_key"`
	Uid       string `gorm:"type:varchar(50);column:uid;"`
	LockUid   string `gorm:"type:varchar(50);column:lock_uid;"`
	FsourceId string `gorm:"type:varchar(50);column:Fsource_id;"`
	Status    int    `gorm:"type:int(1);column:status;default:0"`
	Time      int64  `gorm:"type:int(64);column:time;default:0"`
	StartTime int64  `gorm:"type:int(11);column:start_time;default:0"`
}
type CommonSendPresentLogModel struct {
	model.AppModel
}

// TableName .
func (CommonSendPresentLogModel) TableName() string {
	return "common_send_present_log"
}

// CommonSendPresentLogModel TODO
type CommonSendPresentLog struct {
	CommonSendPresentLogModel
	ID             int64  `gorm:"type:int(11);column:id;primary_key"`
	Openid         string `gorm:"type:varchar(50);column:openid;"`
	FsourceId      string `gorm:"type:varchar(50);column:Fsource_id;"`
	PresentGroupId string `gorm:"type:varchar(50);column:present_group_id;"`
	PresentId      string `gorm:"type:varchar(50);column:present_id;"`
	RoleId         string `gorm:"type:varchar(50);column:role_id;"`
	LangType       string `gorm:"type:varchar(50);column:lang_type;"`
	GameId         string `gorm:"type:varchar(50);column:game_id;"`
	AreaID         int    `gorm:"type:int(11);column:area_id" comment:"大区id"`
	ZoneID         int    `gorm:"type:int(11);column:zone_id" comment:"小区id"`
	PlatID         int    `gorm:"type:int(11);column:plat_id" comment:"平台id"`
	SendNum        int64  `gorm:"type:int(11);column:send_num" comment:"发送次数"`
}
type CommonSendPresentConfigModel struct {
	model.AppModel
}

// TableName .
func (CommonSendPresentConfigModel) TableName() string {
	return "common_send_present_config"
}

// CommonSendPresentConfigModel TODO
type CommonSendPresentConfig struct {
	CommonSendPresentConfigModel
	ID        int64  `gorm:"type:int(11);column:id;primary_key"`
	FsourceId string `gorm:"type:varchar(50);column:Fsource_id;"`
}
