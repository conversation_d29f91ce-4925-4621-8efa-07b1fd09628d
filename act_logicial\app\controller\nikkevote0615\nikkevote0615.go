// Package nikkevote0615 投票
package nikkevote0615

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	"git.code.oa.com/trpcprotocol/publishing_marketing/logicial_vote"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	nikkevote "git.woa.com/trpcprotocol/publishing_marketing/logicial_nikkevote0615"
	"github.com/go-sql-driver/mysql"
	"google.golang.org/protobuf/proto"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/controller/lottery"
	"trpc.act.logicial/app/logic/vote"
	nikkeModel "trpc.act.logicial/app/model/nikke"
)

// NikkevoteServiceImpl 结构体
type NikkevoteServiceImpl struct{}

// ResponseInfo 返回数据
type ResponseInfo struct {
	Ret  int    `json:"ret"`
	Msg  string `json:"msg"`
	Data map[string]struct {
		Dimensions map[string]struct {
			Metrics nikkevote.MetricsType `json:"metrics"`
		} `json:"dimensions"`
	} `json:"data"`
}

// UserInfo 获取一周年数据struct
type UserInfo struct {
	Izoneareaid string `json:"izoneareaid"`
	Vopenid     string `json:"vopenid"`
	Vroleid     string `json:"vroleid"`
}

// DimensionInfo dimensino请求参数
type DimensionInfo struct {
	Interval    string   `json:"interval"`
	EndInterval int      `json:"endInterval"`
	IntervalAgo int      `json:"intervalAgo"`
	Metrics     []string `json:"metrics"`
	Cmds        []string `json:"cmds"`
}

var gameIDMap = map[string]string{
	"29080": "nikke",     // 全球
	"29157": "nikke_hmt", // 港澳台
}

// 要根据 https://doc.weixin.qq.com/doc/w2_AFUAAwZ0ACk1O2cKAjsRj6vHAk4W3?scode=AJEAIQdfAAolYZIjSOAFUAAwZ0ACk&type=0 文档顺序
var actid = "h5-01"
var dimid = "10000"

// RoleMetrics 获取用户于活动相关指标数据
func (s *NikkevoteServiceImpl) RoleMetrics(ctx context.Context, req *nikkevote.RoleMetricsReq) (
	rsp *nikkevote.MetricsType, err error) {
	rsp = &nikkevote.MetricsType{}
	channelIDMap := map[string]string{
		"29080": "82",  // 全球
		"29157": "129", // 港澳台
	}
	channelID := channelIDMap[req.RoleInfo.GetGameId()]
	ts := time.Now().Unix()

	seq := actid + dimid + gameIDMap[req.RoleInfo.GetGameId()] + strconv.FormatInt(ts, 10)
	postData, err := s.GetPostData(ctx, req)
	postDataJSONString, err := json.Marshal(postData)

	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.JsonParseError,
			"parse result,result=%v, \t [Error]:{%v} ", postData, err)
		return
	}
	sigStr := fmt.Sprintf(
		"%v%v%v%v%v%v%v%v%v",
		"/data/v1/general/dimensions?",
		"channelid="+channelID,
		"&gameid="+req.RoleInfo.GetGameId(),
		"&os=0",
		"&seq="+seq,
		"&source=0",
		"&timezone=0",
		"&ts="+strconv.FormatInt(ts, 10),
		"&version=1.0",
	)
	sinKey := map[string]string{
		"29080": "3d0ef5272bf6bc22fd484c18d96be5c0",
		"29157": "c8960de288716bf11e450e8b1319656d",
	}
	sig := lottery.GetMD5Encode(
		sigStr +
			string(postDataJSONString) +
			sinKey[req.RoleInfo.GetGameId()],
	)
	url := fmt.Sprintf(
		"%v%v%v",
		config.GetConfig().NikkeDataHost,
		sigStr,
		"&sig="+sig,
	)

	optionOne := httpclient.ClientOption{
		URL: url,
		// Timeout: 2 * time.Second,
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		Type:     "POST",
		PostData: postData,
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	if resultOption.RequestError != nil {
		// 请求失败
		err = errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"http error, \t [Error]:{%v} ", url)
		return
	}
	response := resultOption.Result
	var respData ResponseInfo
	err = json.Unmarshal([]byte(response), &respData)
	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.JsonParseError,
			"parse result,result=%v, \t [Error]:{%v} ", response, err)
		return
	}

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	userKey := fmt.Sprintf(
		"%v%v%v",
		userAccount.IntlAccount.OpenId+"|",
		// "268130863772397056|",
		"0|",
		strconv.FormatInt(req.RoleInfo.GetAreaId(), 10),
	)
	metrics := respData.Data[userKey].Dimensions[dimid].Metrics
	rsp = &metrics
	return
}

// GetPostData 获取post参数
func (s *NikkevoteServiceImpl) GetPostData(ctx context.Context, req *nikkevote.RoleMetricsReq) (
	postData map[string]interface{}, err error) {

	userAccount, err := metadata.GetUserAccount(ctx)

	if err != nil {
		return
	}

	metricsReqArr := make([]string, 32)
	cmdsArr := make([]string, 32)

	for i := 1; i <= 32; i++ {
		metricsReqArr[i-1] = "c" + strconv.Itoa(i)
		cmdsArr[i-1] = ""
	}

	postData = map[string]interface{}{
		"actid":    actid,
		"gameName": gameIDMap[req.RoleInfo.GetGameId()],
		"users": []UserInfo{
			{
				Izoneareaid: strconv.FormatInt(req.RoleInfo.GetAreaId(), 10),
				Vroleid:     "0",
				// Vopenid:     "268130863772397056",
				Vopenid: userAccount.IntlAccount.OpenId,
			},
		},
		"dimensions": map[string]DimensionInfo{
			dimid: {
				Interval:    "now", // 1. "now"（代表最新数据，没有时间跨度）, 2. "day"（读取特定天数的数据）
				EndInterval: 0,
				IntervalAgo: 0,
				Metrics:     metricsReqArr,
				Cmds:        cmdsArr,
			},
		},
	}
	return
}

// AddSendLog 记录发货
func (s *NikkevoteServiceImpl) AddSendLog(ctx context.Context, req *nikkevote.AddSendLogReq) (
	rsp *nikkevote.AddSendLogRsp, err error) {
	rsp = &nikkevote.AddSendLogRsp{}

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	sendTempData := nikkeModel.SendTemp{
		FsourceID: req.FsourceId,
		// UID:         "29080-17803888811217356039",
		// AccountType: 1,
		UID:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		Status:      0,
		Tag:         req.Tag,
		LangType:    req.LangType,
	}
	if err != nil {
		return
	}

	tag := req.Tag
	// 判断第6屏，不同tag
	if req.Tag == 6 {

		metricsReq := &nikkevote.RoleMetricsReq{}
		metricsReq.RoleInfo = req.RoleInfo

		fmt.Printf("metricsReq%v", metricsReq)
		metrics, err1 := s.RoleMetrics(ctx, metricsReq)
		if err1 != nil {
			errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.JsonParseError, "RoleMetrics error [error]: {%v}", err1)
			return
		}
		list := strings.Split(metrics.C17, ";")
		newTagMap := map[string]int32{
			"Elysion":  1,
			"Pilgrim":  4,
			"Tetra":    2,
			"Missilis": 3,
		}
		newTag := newTagMap[list[0]]
		if newTag == 0 {
			newTag = 1
		}

		tag = int32(req.Tag)*10 + int32(newTag)

	}

	condition := map[string]interface{}{
		"Fsource_id":   req.FsourceId,
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
		// "uid":          "29080-17803888811217356039",
		// "account_type": 1,
		"tag": tag,
	}
	db := DB.DefaultConnect().WithContext(ctx).Where(condition).FirstOrCreate(&sendTempData)
	if db.Error != nil {
		// 检查 err 是否为重复插入错误
		if isNotDuplicateInsertError(db.Error) {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
	}
	log.WithFieldsContext(ctx, "log_type", "NikkeAddLog", "str_field_1", strconv.Itoa(int(tag))).Infof("AddSendLog")
	rsp.LogHasAdd = true
	return
}

// isNotDuplicateInsertError 是否不是mysql重复插入报错
func isNotDuplicateInsertError(err error) bool {
	mysqlErr, ok := err.(*mysql.MySQLError)
	if !ok {
		return false
	}
	// Error 1062: Duplicate entry for key
	return mysqlErr.Number != 1062
}

// ScheduledSend 定时发货
func (s *NikkevoteServiceImpl) ScheduledSend(ctx context.Context, req *nikkevote.ScheduledSendReq) (
	rsp *nikkevote.ScheduledSendRsp, err error) {
	scheduleCtx := context.Background()
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(">>>>>>>>>>>>>>>>>>ScheduledSend<<<<<<<<<<<<<<<<<<<")

	rsp = &nikkevote.ScheduledSendRsp{}
	tableName := nikkeModel.SendTemp{}.TableName()

	presentIDList := map[string]map[int]string{
		"page-26574": { // 国际服
			0:  "Wand-20230927072346-P15143e105ede", // 首次登录
			1:  "Wand-20230927072421-P11ae1b027a19", // 首次分享
			2:  "Wand-20230927072452-P088e6e7df281", // 首次测试
			3:  "Wand-20230927072507-Pcd625be1a507", // 加载完第三屏
			4:  "Wand-20230927072523-P717a3599f3d5", // 加载完第四屏
			5:  "Wand-20230927072752-P42a604224688", // 加载完第六屏
			7:  "Wand-20230927072820-P6651441c90ff", // 加载完第七屏
			8:  "Wand-20230927072832-Pf848ca74b0ca", // 加载完第八屏
			61: "Wand-20230927072557-P797c5587a8e0", // 第五屏极乐净土礼包
			62: "Wand-20230927072628-Pb2be429bd82d", // 第五屏泰特拉礼包
			63: "Wand-20230927072646-P1ff4cfa29cd4", // 第五屏米西利斯礼包
			64: "Wand-20230927072722-P345af9df314e", // 第五屏朝圣者礼包
		},
		"page-26575": { // 港澳台
			0:  "Wand-20230927072914-Pc21aa7872e87", // 首次登陆
			1:  "Wand-20230927073000-P401461e736b1", // 首次分享
			2:  "Wand-20230927073019-P586f9146c5e8", // 首次测试
			3:  "Wand-20230927073051-Pf68e686b1a8e", // 加载完第三屏
			4:  "Wand-20230927073106-P499a56b05b0b", // 加载完第四屏
			5:  "Wand-20230927073412-Pc064648f291a", // 加载完第五屏
			7:  "Wand-20230927073423-P59ec0836eadd", // 加载完第七屏
			8:  "Wand-20230927073436-P73f7e483ae95", // 加载完第八屏
			61: "Wand-20230927073126-P2d9c24ad3d6f", // 第六屏极乐净土礼包
			62: "Wand-20230927073202-P953f54fc0e1a", // 第六屏泰特拉礼包
			63: "Wand-20230927073314-Pcd3a00e970c5", // 第六屏米西利斯礼包
			64: "Wand-20230927073352-P0c55833dde5e", // 第六屏朝圣者礼包
		},
	}
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"Fsource_id": req.FsourceId,
		"status":     0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(nikkeModel.SendTemp{}.TableName()).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页200条
	pageSize := 200
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))
	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	gameProxy := gamePb.NewGameClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		offset := (pageNumber - 1) * pageSize
		var logData = []nikkeModel.SendTemp{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Where(condition).Offset(offset).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", logData))
		for _, v := range logData {
			wg.Add(1)
			go func(data nikkeModel.SendTemp) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(string(accountData))

				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				// 获取角色信息
				gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: req.FsourceId}

				gameRoleInfo, err := gameProxy.GetSavedRoleInfo(newCtx, gameReq, callopts...)
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("gameRoleInfo：%v", gameRoleInfo))

				if err == nil {
					log.WithFieldsContext(ctx, "ScheduledSend log", "debug").Infof(fmt.Sprintf("v: %#v; PresentId: %#v", v,
						presentIDList[data.FsourceID][int(data.Tag)]))
					// 发送礼包
					sendReq := &presentPb.SendPresentReq{
						FsourceId: req.FsourceId,
						PresentId: presentIDList[data.FsourceID][int(data.Tag)],
						RoleInfo:  gameRoleInfo,
					}
					accountData, _ = proto.Marshal(&accountPb.UserAccount{
						Uid:         data.UID,
						AccountType: accountPb.AccountType(data.AccountType),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							GameId:    gameRoleInfo.GameId,
							ChannelId: 3,
						},
					})
					callopts = []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
						client.WithMetaData(metadata.LangType, []byte(data.LangType)),
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
					}
					if sendErr != nil {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						fmt.Println("---------------sendErr---------------")
						fmt.Printf("%#v\n", sendErr.Error())
						log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err111:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						if strings.Contains(sendErr.Error(), "package limit left not enough") {
							DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						}
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err:%v", sendErr))
					// 修改发货状态
				} else {
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("GetRoleInfo] service err:%v", err))

				}
				wg.Done()
			}(v)

		}
		wg.Wait()

	}
	return
}

// RobotVoteNum 机器人展示票数
func (s *NikkevoteServiceImpl) RobotVoteNum(ctx context.Context, req *nikkevote.RobotVoteNumReq) (
	rsp *nikkevote.RobotVoteNumRsp, err error) {
	rsp = &nikkevote.RobotVoteNumRsp{}
	if time.Now().Unix() > 1688569399 {
		return
	}
	fmt.Println("---------------RobotVoteNum---------------")
	// 获取2个地区实际票数
	list1, err1 := vote.GetTeamListVotesFromTable(ctx, "page-26523")
	list2, err2 := vote.GetTeamListVotesFromTable(ctx, "page-26526")
	if err1 == nil && err2 == nil {
		vote11, _ := list1["1"]
		vote12, _ := list1["2"]
		vote13, _ := list1["3"]
		vote21, _ := list2["1"]
		vote22, _ := list2["2"]
		vote23, _ := list2["3"]
		str := fmt.Sprintf("投票数据：（1）票数：%d,（2）票数：%d,（3）票数：%d", vote11+vote21, vote12+vote22, vote13+vote23)
		options := httpclient.ClientOption{
			URL:     "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=de57a37a-bcd2-4a15-affc-0af0a9b2b20d",
			Timeout: 3 * time.Second,
			Header:  map[string]string{},
			Cookie:  map[string]string{},
			Type:    "POST",
			// PostData: map[string]interface{}{
			// 	"msgtype": "text",
			// 	"text":    fmt.Sprintf("{\"content\":\"%s\"}", str),
			// },
			PostString: fmt.Sprintf("{\"msgtype\":\"text\",\"text\":{\"content\":\"%s\"}}", str),
		}
		httpclient.RequestOne(ctx, options)
	}
	return
}

// ShowVoteNum 展示合并票数
func (s *NikkevoteServiceImpl) ShowVoteNum(ctx context.Context, req *nikkevote.ShowVoteNumReq) (
	rsp *nikkevote.ShowVoteNumRsp, err error) {
	rsp = &nikkevote.ShowVoteNumRsp{}
	// 计算和
	tag1 := &logicial_vote.TeamListVote{
		TagId: "1",
	}
	tag2 := &logicial_vote.TeamListVote{
		TagId: "2",
	}
	tag3 := &logicial_vote.TeamListVote{
		TagId: "3",
	}
	for _, item := range req.HtmlList {
		if item.TagId == "1" {
			tag1.Num = tag1.Num + item.Num
		}
		if item.TagId == "2" {
			tag2.Num = tag2.Num + item.Num
		}
		if item.TagId == "3" {
			tag3.Num = tag3.Num + item.Num
		}
	}
	for _, item := range req.GlobalList {
		if item.TagId == "1" {
			tag1.Num = tag1.Num + item.Num
		}
		if item.TagId == "2" {
			tag2.Num = tag2.Num + item.Num
		}
		if item.TagId == "3" {
			tag3.Num = tag3.Num + item.Num
		}
	}
	voteList := make([]*logicial_vote.TeamListVote, 0)
	voteList = append(voteList, tag1)
	voteList = append(voteList, tag2)
	voteList = append(voteList, tag3)
	rsp.TeamList = voteList
	return
}
