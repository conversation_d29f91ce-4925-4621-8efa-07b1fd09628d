package exobrntmp

import "trpc.act.logicial/app/model"

type SendTempSevenDayModel struct {
	model.AppModel
}

// TableName .
func (SendTempSevenDayModel) TableName() string {
	return "exobrn_sevenday_send_temp"
}

// SendTemp 临时结构
type SendTempSevenDay struct {
	SendTempSevenDayModel
	ID     int64  `gorm:"type:int(11);column:id;primary_key"`
	OpenId string `gorm:"type:varchar(32);column:openid;"`
	Status int    `gorm:"type:int(1);column:status;not null"`
}
