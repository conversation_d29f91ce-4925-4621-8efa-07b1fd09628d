package df_tmp

import (
	"context"
	"errors"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	repo "trpc.act.logicial/app/mysql/df_activity_repo"
)

// GetFirstClaimNestInfo 获取用户首次掏鸟窝成功时间
func GetFirstClaimNestInfo(ctx context.Context,
	req *pb.GetFirstClaimNestInfoReq) (*pb.GetFirstClaimNestInfoRsp, error) {
	log.InfoContextf(ctx, "start GetFirstClaimNestInfo, req: %+v", req)
	account, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "SetFirstClaimNestInfo get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}

	record, err := repo.NestActivityRepoClient.GetUserBirdNestReward(ctx, account.Uid)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 文档不存在 直接返回0
		return &pb.GetFirstClaimNestInfoRsp{FirstTime: 0}, nil
	}

	if err != nil {
		log.ErrorContextf(ctx, "GetUserBirdNestReward error:%v, uid:%v", err, account.Uid)
		return nil, code.ErrSystemError
	}

	return &pb.GetFirstClaimNestInfoRsp{FirstTime: uint64(record.FirstClaimTime)}, nil
}
