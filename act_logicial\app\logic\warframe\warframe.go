// Package warframe TODO
package warframe

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/datadump"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	addressPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_address"
	cnbotPb "git.woa.com/trpcprotocol/publishing_application/cnbot_operations"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_warframe_tmp"
	redisOrgin "github.com/go-redis/redis/v8"
	"github.com/go-sql-driver/mysql"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/global"
	"trpc.act.logicial/app/model/warframe"
)

const (
	// Shared TODO
	Shared = 1

	// NewPlayer TODO
	NewPlayer = 1
	// OldPlayer TODO
	OldPlayer = 2

	// AllUserWishesRecordedCacheKey TODO
	AllUserWishesRecordedCacheKey = "AllUserWishesRecorded"
	// NoGameErrorCode TODO
	NoGameErrorCode = 1000011
	// Wish1 TODO
	Wish1 = "1"
	// Wish2 TODO
	Wish2 = "2"
	// Wish3 TODO
	Wish3 = "3"
	// Wish4 TODO
	Wish4 = "4"
	// Wish5 TODO
	Wish5 = "5"
)

// GetCurrentUserWishes 获取当前用户的愿望列表
func GetCurrentUserWishes(ctx context.Context) (*pb.CanWish, *pb.WishItem, error) {
	var warframeUserWishRecord warframe.WarframeUserWishRecord
	var shareStatus, oldPlayer bool
	canWish := &pb.CanWish{}
	wishItem := &pb.WishItem{}

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return canWish, wishItem, err
	}

	// 获取愿望列表
	db := DB.DefaultConnect().WithContext(ctx).Table(warframe.WarframeUserWishRecordModel{}.TableName()).
		Where("uid = ? and account_type = ?", userAccount.Uid, userAccount.AccountType).Order("id asc").Limit(1).
		Find(&warframeUserWishRecord)
	if err = db.Error; err != nil {
		return canWish, wishItem, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	wishItem.OldPlayer = uint32(warframeUserWishRecord.OldPlayer)
	wishItem.ShareActivity = uint32(warframeUserWishRecord.ShareActivity)
	wishItem.Default = uint32(warframeUserWishRecord.Default)
	// 获取分享状态
	if shareStatus, err = GetCurrentUserHasShared(ctx); err != nil {
		return canWish, wishItem, err
	}
	// 是否是老玩家
	if oldPlayer, err = CheckCurrentUserIsOldPlayer(ctx); err != nil {
		return canWish, wishItem, err
	}
	if shareStatus && wishItem.ShareActivity == 0 {
		canWish.ShareActivity = true
	}
	if oldPlayer && wishItem.OldPlayer == 0 {
		canWish.OldPlayer = true
	}
	if wishItem.Default == 0 {
		canWish.Default = true
	}
	return canWish, wishItem, nil
}

// GetCurrentUserHasShared 获取当前用户分享状态
func GetCurrentUserHasShared(ctx context.Context) (bool, error) {

	var hasShare bool
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return hasShare, err
	}
	var shareStatus int8
	db := DB.DefaultConnect().Table(warframe.WarframeUserDetailModel{}.TableName()).Select("share_status").
		Where("uid = ? and account_type = ?", userAccount.Uid, userAccount.AccountType).Order("id asc").Limit(1).
		Find(&shareStatus)
	if err = db.Error; err != nil {
		return false, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	hasShare = shareStatus == Shared
	return hasShare, nil
}

// RecordsUserShareStatus 记录用户分享状态
func RecordsUserShareStatus(ctx context.Context) error {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	var warframeUserDetail warframe.WarframeUserDetail
	db := DB.DefaultConnect().Table(warframe.WarframeUserDetailModel{}.TableName()).Where("uid = ? and account_type = ?",
		userAccount.Uid, userAccount.AccountType).Assign(&warframe.WarframeUserDetail{
		UID:         userAccount.Uid,
		AccountType: int16(userAccount.AccountType),
		ShareStatus: Shared,
	}).FirstOrCreate(&warframeUserDetail)
	if err = db.Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return nil
}

// RecordsCurrentUserWishes 记录当前用户的愿望
func RecordsCurrentUserWishes(ctx context.Context, userWish *pb.WishItem) error {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	if userWish == nil {
		return errs.NewCustomError(ctx, code.CanNotMakeEnoughWishes, "You can't make current wishes")
	}
	// 获取用户可许愿次数和已记录愿望
	canWish, wishItem, err := GetCurrentUserWishes(ctx)
	if err != nil {
		return err
	}

	if userWish.Default != 0 {
		// 判断当前用户是否可以许愿
		if !canWish.Default {
			return errs.NewCustomError(ctx, code.CanNotMakeEnoughWishes, "You can't make current wishes")
		}
		// 判断重复提交
		if userWish.Default == wishItem.Default || userWish.Default == wishItem.OldPlayer ||
			userWish.Default == wishItem.ShareActivity {
			return errs.NewCustomError(ctx, code.WishesDuplicateSubmission, "Wishes Duplicate Submission")
		}
	}
	if userWish.OldPlayer != 0 {
		if !canWish.OldPlayer {
			return errs.NewCustomError(ctx, code.CanNotMakeEnoughWishes, "You can't make current wishes")
		}

		// 判断重复提交
		if userWish.OldPlayer == wishItem.Default || userWish.OldPlayer == wishItem.OldPlayer ||
			userWish.OldPlayer == wishItem.ShareActivity {
			return errs.NewCustomError(ctx, code.WishesDuplicateSubmission, "Wishes Duplicate Submission")
		}
	}
	if userWish.ShareActivity != 0 {
		if !canWish.ShareActivity {
			return errs.NewCustomError(ctx, code.CanNotMakeEnoughWishes, "You can't make current wishes")
		}

		// 判断重复提交
		if userWish.ShareActivity == wishItem.Default || userWish.ShareActivity == wishItem.OldPlayer ||
			userWish.ShareActivity == wishItem.ShareActivity {
			return errs.NewCustomError(ctx, code.WishesDuplicateSubmission, "Wishes Duplicate Submission")
		}
	}

	// 记录当前愿望
	var warframeUserWishRecord warframe.WarframeUserWishRecord
	db := DB.DefaultConnect().Table(warframe.WarframeUserWishRecordModel{}.TableName()).
		Where("uid = ? and account_type = ?",
			userAccount.Uid, userAccount.AccountType).Assign(&warframe.WarframeUserWishRecord{
		UID:           userAccount.Uid,
		AccountType:   int16(userAccount.AccountType),
		Default:       int16(userWish.Default),
		ShareActivity: int16(userWish.ShareActivity),
		OldPlayer:     int16(userWish.OldPlayer),
	}).FirstOrCreate(&warframeUserWishRecord)
	if err = db.Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}

	// 记录缓存
	redisKey := global.GetRedisKey(AllUserWishesRecordedCacheKey)
	pipeline := redis.GetClient().Pipeline()
	if userWish.OldPlayer != 0 {
		pipeline.HIncrBy(ctx, redisKey, cast.ToString(userWish.OldPlayer), 1)
	}
	if userWish.ShareActivity != 0 {
		pipeline.HIncrBy(ctx, redisKey, cast.ToString(userWish.ShareActivity), 1)
	}
	if userWish.Default != 0 {
		pipeline.HIncrBy(ctx, redisKey, cast.ToString(userWish.Default), 1)
	}
	if _, err = pipeline.Exec(ctx); err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"redis exec error, \t [Error]:{%v} ", err)
	}
	//  记录时间
	// 判断是否有今天的记录如果没有就添加一条
	playTimeForever, err := GetWishedUserPlaytimeForever(ctx, userAccount.Uid, int(userAccount.AccountType))
	if err != nil {
		parseErr := errs.ParseError(ctx, err)
		if parseErr.Code == 1000005 || parseErr.Code == 1000011 {
			err = nil
		} else {
			log.WithFieldsContext(ctx, "log_type", "WithFieldsContext_error").Infof("uid: %v, err: %v", userAccount.Uid, err)
			return err
		}
	}
	err = SetUserPlaytimeForever(ctx, userAccount.Uid, int(userAccount.AccountType), playTimeForever)
	if err != nil {
		return err
	}
	return nil
}

// GetAllWishTotalNumber 获取所有用户每个愿望总数
func GetAllWishTotalNumber(ctx context.Context) ([]int32, error) {
	// 获取缓存
	redisKey := global.GetRedisKey(AllUserWishesRecordedCacheKey)
	result, err := redis.GetClient().HGetAll(ctx, redisKey).Result()
	if err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"redis get data error, \t [Error]:{%v} ", err)
	}

	wishList := []string{
		Wish1, Wish2, Wish3, Wish4, Wish5,
	}
	var allWishCount []int32
	for _, wishTag := range wishList {
		if value, ok := result[wishTag]; !ok {
			// 查表
			var count int32
			count, err = GetWishCountByWishTag(ctx, wishTag)
			if err != nil {
				return nil, err
			}
			allWishCount = append(allWishCount, count)
			// 写缓存
			cacheMap := make(map[string]interface{})
			cacheMap[wishTag] = cast.ToString(count)
			redis.GetClient().HSet(ctx, redisKey, cacheMap)
		} else {
			// 用缓存数据
			allWishCount = append(allWishCount, cast.ToInt32(value))
		}
	}
	return allWishCount, nil
}

// GetWishCountByWishTag TODO
func GetWishCountByWishTag(ctx context.Context, wishTag string) (int32, error) {

	tagNum := cast.ToInt16(wishTag)
	var count int64
	db := DB.DefaultConnect().Table(warframe.WarframeUserWishRecordModel{}.TableName()).
		Where("`default` = ? or `share_activity` = ? or `old_player` = ?",
			tagNum, tagNum, tagNum).Count(&count)
	if err := db.Error; err != nil {
		return 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"GetWishCountByWishTag db error, \t [Error]:{%v} ", err)
	}
	return int32(count), nil
}

// CheckCurrentUserIsOldPlayer 检查当前用户是否为老玩家
func CheckCurrentUserIsOldPlayer(ctx context.Context) (bool, error) {

	// 获取表数据
	var isOldPlayer bool
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return isOldPlayer, err
	}
	var playerType int8
	db := DB.DefaultConnect().WithContext(ctx).Table(warframe.WarframeUserDetailModel{}.TableName()).Select("player_type").
		Where("uid = ? and account_type = ?", userAccount.Uid, userAccount.AccountType).Order("id asc").Limit(1).
		Find(&playerType)
	if err = db.Error; err != nil {
		return false, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}

	log.WithFieldsContext(ctx, "log_type", "CheckCurrentUserIsOldPlayer").Infof(
		"CheckCurrentUserIsOldPlayer, playerType: %v", playerType)
	switch playerType {
	case NewPlayer:
		isOldPlayer = false
	case OldPlayer:
		isOldPlayer = true
	default:
		// 判断是库中是否存在游戏
		proxy := cnbotPb.NewOperationsClientProxy()
		var isUserOwnGameRsp *cnbotPb.IsUserOwnGameRsp
		isUserOwnGameRsp, err = proxy.IsUserOwnGame(ctx, &cnbotPb.IsUserOwnGameReq{})

		if err != nil {
			parseErr := errs.ParseError(ctx, err)
			if parseErr.Code == NoGameErrorCode {
				playerType = NewPlayer
				err = nil
				// return false, nil
			} else {
				log.WithFieldsContext(ctx, "log_type", "debug").Infof("IsUserOwnGame show rsp err:[%v]", err)
				return false, err
			}
		} else {
			if isUserOwnGameRsp == nil {
				return false, errs.NewCustomError(ctx, 100001, "IsUserOwnGame rsp nil")
			} else {
				log.WithFieldsContext(ctx, "log_type", "debug").Infof("IsUserOwnGame show rsp:[%v]", isUserOwnGameRsp)
			}
			if isUserOwnGameRsp.Status {
				playerType = OldPlayer
				isOldPlayer = true
			} else {
				playerType = NewPlayer
			}
		}
		// 写入表数据
		db = DB.DefaultConnect().WithContext(ctx).Table(warframe.WarframeUserDetailModel{}.TableName()).
			Where("uid = ? and account_type = ?",
				userAccount.Uid, userAccount.AccountType).FirstOrCreate(&warframe.WarframeUserDetail{
			UID:         userAccount.Uid,
			AccountType: int16(userAccount.AccountType),
			PlayerType:  playerType,
		})
		if err = db.Error; err != nil {
			return isOldPlayer, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
		}
	}

	return isOldPlayer, nil
}

// ScheduleBindPresentBot TODO
func ScheduleBindPresentBot(ctx context.Context, FsourceId string, presentConfig []*pb.ScheduleBindPresent,
	botUrl string) (err error) {
	addressProxy := addressPb.NewAddressClientProxy()
	tableList := []string{
		"presentsend_account_present_openid_00",
		"presentsend_account_present_openid_01",
		"presentsend_account_present_openid_02",
		"presentsend_account_present_openid_03",
		"presentsend_account_present_openid_04",
		"presentsend_account_present_openid_05",
		"presentsend_account_present_openid_06",
		"presentsend_account_present_openid_07",
		"presentsend_account_present_openid_08",
		"presentsend_account_present_openid_09",
		"presentsend_account_present_openid_10",
		"presentsend_account_present_openid_11",
		"presentsend_account_present_openid_12",
		"presentsend_account_present_openid_13",
		"presentsend_account_present_openid_14",
		"presentsend_account_present_openid_15",
		"presentsend_account_present_openid_16",
		"presentsend_account_present_openid_17",
		"presentsend_account_present_openid_18",
		"presentsend_account_present_openid_19",
		"presentsend_account_present_openid_20",
		"presentsend_account_present_openid_21",
		"presentsend_account_present_openid_22",
		"presentsend_account_present_openid_23",
		"presentsend_account_present_openid_24",
		"presentsend_account_present_openid_25",
		"presentsend_account_present_openid_26",
		"presentsend_account_present_openid_27",
		"presentsend_account_present_openid_28",
		"presentsend_account_present_openid_29",
		"presentsend_account_present_openid_30",
		"presentsend_account_present_openid_31",
		"presentsend_account_present_openid_32",
		"presentsend_account_present_openid_33",
		"presentsend_account_present_openid_34",
		"presentsend_account_present_openid_35",
		"presentsend_account_present_openid_36",
		"presentsend_account_present_openid_37",
		"presentsend_account_present_openid_38",
		"presentsend_account_present_openid_39",
		"presentsend_account_present_openid_40",
		"presentsend_account_present_openid_41",
		"presentsend_account_present_openid_42",
		"presentsend_account_present_openid_43",
		"presentsend_account_present_openid_44",
		"presentsend_account_present_openid_45",
		"presentsend_account_present_openid_46",
		"presentsend_account_present_openid_47",
		"presentsend_account_present_openid_48",
		"presentsend_account_present_openid_49",
		"presentsend_account_present_openid_50",
		"presentsend_account_present_openid_51",
		"presentsend_account_present_openid_52",
		"presentsend_account_present_openid_53",
		"presentsend_account_present_openid_54",
		"presentsend_account_present_openid_55",
		"presentsend_account_present_openid_56",
		"presentsend_account_present_openid_57",
		"presentsend_account_present_openid_58",
		"presentsend_account_present_openid_59",
		"presentsend_account_present_openid_60",
		"presentsend_account_present_openid_61",
		"presentsend_account_present_openid_62",
		"presentsend_account_present_openid_63",
		"presentsend_account_present_openid_64",
		"presentsend_account_present_openid_65",
		"presentsend_account_present_openid_66",
		"presentsend_account_present_openid_67",
		"presentsend_account_present_openid_68",
		"presentsend_account_present_openid_69",
		"presentsend_account_present_openid_70",
		"presentsend_account_present_openid_71",
		"presentsend_account_present_openid_72",
		"presentsend_account_present_openid_73",
		"presentsend_account_present_openid_74",
		"presentsend_account_present_openid_75",
		"presentsend_account_present_openid_76",
		"presentsend_account_present_openid_77",
		"presentsend_account_present_openid_78",
		"presentsend_account_present_openid_79",
		"presentsend_account_present_openid_80",
		"presentsend_account_present_openid_81",
		"presentsend_account_present_openid_82",
		"presentsend_account_present_openid_83",
		"presentsend_account_present_openid_84",
		"presentsend_account_present_openid_85",
		"presentsend_account_present_openid_86",
		"presentsend_account_present_openid_87",
		"presentsend_account_present_openid_88",
		"presentsend_account_present_openid_89",
		"presentsend_account_present_openid_90",
		"presentsend_account_present_openid_91",
		"presentsend_account_present_openid_92",
		"presentsend_account_present_openid_93",
		"presentsend_account_present_openid_94",
		"presentsend_account_present_openid_95",
		"presentsend_account_present_openid_96",
		"presentsend_account_present_openid_97",
		"presentsend_account_present_openid_98",
		"presentsend_account_present_openid_99",
	}
	levelList := []int32{}
	presentConfigMap := make(map[int32]string)
	for _, item := range presentConfig {
		levelList = append(levelList, item.Level)
		presentConfigMap[item.Level] = item.Name
	}
	type UserPresent struct {
		Uid         string
		Level       int32
		PresentName string
		Name        string
		Address     string
		Email       string
		PhoneNumber string
	}
	userList := make([]UserPresent, 0)
	for _, item := range tableList {
		oneUserList := make([]UserPresent, 0)
		// 查询中奖名单
		DB.SelectConnect("present").Table(item).Debug().Where("`Fsource_id` = ? and `level` in (?)", FsourceId, levelList).
			Find(&oneUserList)
		userList = append(userList, oneUserList...)
	}
	// 查询中奖信息
	for key, item := range userList {
		accountData, _ := proto.Marshal(&accountPb.UserAccount{
			Uid:         item.Uid,
			AccountType: accountPb.AccountType_INTL,
			IntlAccount: &accountPb.IntlAccount{
				OpenId: "",
			},
		})

		callopts := []client.Option{
			client.WithMetaData(metadata.UserAccount, accountData),
		}
		userAddress, err := addressProxy.GetAccountAddressInfo(ctx, &addressPb.GetAccountAddressInfoReq{
			FsourceId: FsourceId,
		}, callopts...)
		if err == nil {
			userList[key].PresentName = presentConfigMap[item.Level]
			userList[key].Address = userAddress.Address
			userList[key].Email = userAddress.Email
			userList[key].PhoneNumber = userAddress.PhoneNumber
			userList[key].Name = userAddress.Name
		}
	}

	fmt.Println("---------------userList---------------")
	for _, item := range userList {
		fmt.Println(item)
	}
	// 发送bot
	return
}

// GetRandomNumbers 获取制定数据随机数
func GetRandomNumbers(countPeoples []int, level int32) (result []int, err error) {
	if len(countPeoples) < int(level) {
		return countPeoples, nil
	}

	rand.Seed(time.Now().UnixNano())

	result = make([]int, level)
	for i := 0; i < int(level); i++ {
		j := rand.Intn(len(countPeoples)-i) + i
		countPeoples[i], countPeoples[j] = countPeoples[j], countPeoples[i]
		result[i] = countPeoples[i]
	}

	return result, nil
}

// GetWinfromWishesPool 按奖池等级抽奖
func GetWinfromWishesPool(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	excelData := make([][]string, 0)
	excelData = append(excelData, []string{
		"level", "date", "steam_id", "steam_name", "openid",
	})
	now := time.Now()
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "time-error").Infof("err: %v", err)
		return
	}

	// 将当前时间转换成指定时区的时间
	utc8Time := now.In(loc)
	levelDay := utc8Time.AddDate(0, 0, -1).Format("2006-01-02")
	levelMap := map[int32]int32{
		1: 1,
		2: 2,
		3: 7,
		5: 50,
		4: 28,
	}
	for i := 1; i <= 5; i += 1 {
		tableName := fmt.Sprintf("%v_%v", warframe.WarframeUserWishPoolModel{}.TableName(), i)
		poolData := make([]*warframe.WarframeUserWishPool, 0)
		db := DB.DefaultConnect().WithContext(scheduleCtx).Table(tableName).Where(
			"level_day = ? and is_already_win = 0", levelDay).
			Find(&poolData)
		if err = db.Error; err != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
		people := make([]int, 0)
		for _, item := range poolData {
			isPlay, err := IsPlayYesterDay(scheduleCtx, item.UID, int(item.AccountType))
			if err != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "GetWinfromWishesPool_filter_play").Infof("err: %v", err)
				err = nil
				continue
			}
			if isPlay {
				people = append(people, int(item.ID))
			}
		}

		if len(people) > 0 {
			wins, err := GetRandomNumbers(people, levelMap[int32(i)])
			if err != nil {
				return err
			}
			for _, poolDataItem := range poolData {
				for _, id := range wins {
					if int(poolDataItem.ID) == id {
						excelData = append(excelData, []string{
							fmt.Sprintf("%v", i), levelDay, poolDataItem.SteamId, poolDataItem.SteamName, poolDataItem.UID,
						})
					}
				}
			}
			data := make([]*warframe.WarframeUserWishPool, 0)
			// 代表奖池人数少于中奖人数 所有人都中奖
			db = DB.DefaultConnect().WithContext(scheduleCtx).Table(tableName).
				Where("id in (?)", wins).
				Find(&data)
			if err = db.Error; err != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", err)
				return err
			}
			// data是中奖人
			for _, item := range data {
				item.IsWin = 1
				item.IsAlreadyWin = 1
				// 更新sql代表这一天中奖了
				db = DB.DefaultConnect().WithContext(scheduleCtx).Table(tableName).
					Where("id = ?", item.ID).
					Updates(&warframe.WarframeUserWishPool{
						IsWin:        1,
						IsAlreadyWin: 1,
					})
				if err = db.Error; err != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error, \t [Error]:{%v} ", err)
					return err
				}
				// 在更新所有这个uid的数据isAlreadyWin
				db = DB.DefaultConnect().WithContext(scheduleCtx).Table(tableName).
					Where("uid = ? and account_type = ? and is_win = 0", item.UID, item.AccountType).
					Updates(&warframe.WarframeUserWishPool{
						IsAlreadyWin: 1,
					})
				if err = db.Error; err != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error, \t [Error]:{%v} ", err)
					return err
				}
			}
		}
	}
	filepath, err := datadump.CreateExcel(scheduleCtx, fmt.Sprintf("%v-wins.xlsx", levelDay), excelData)
	if err != nil {
		return
	}
	mediaId, err := datadump.QWXUploadMedia(scheduleCtx, filepath, "87daabdd-c7d3-4aef-ab82-c77d66b63320",
		fmt.Sprintf("%v-wins.xlsx", levelDay))
	if err != nil {
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_info").Infof("mediaId: %v", mediaId)
	option := &datadump.SendOption{
		Msgtype: "file",
		Chatid:  "wrkSFfCgAAlLVoroqN8Xc5kR_7om50Rw",
		File: &datadump.FileType{
			MediaId: mediaId,
		},
	}
	_, err = datadump.QWXSend(scheduleCtx, option, "87daabdd-c7d3-4aef-ab82-c77d66b63320")
	if err != nil {
		return
	}
	return
}

// GetSelfPresentList TODO
func GetSelfPresentList(ctx context.Context) (presentList []*pb.PresentItem, err error) {
	presentList = make([]*pb.PresentItem, 0)
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	now := time.Now()
	loc, err := time.LoadLocation("Asia/Shanghai")

	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "time-error").Infof("err: %v", err)
		return
	}
	utc8Time := now.In(loc)

	levelDay := utc8Time.AddDate(0, 0, -1).Format("2006-01-02")
	for i := 1; i <= 5; i += 1 {
		var data *warframe.WarframeUserWishPool
		tableName := fmt.Sprintf("%v_%v", warframe.WarframeUserWishPoolModel{}.TableName(), i)
		db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
			Where("uid = ? and account_type = ? and is_win = 1", userAccount.Uid, userAccount.AccountType).
			First(&data)
		if err = db.Error; err != nil {
			if db.Error == gorm.ErrRecordNotFound {
				err = nil
				continue
			}
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
		if db.Error != gorm.ErrRecordNotFound {
			if utc8Time.Hour() >= 12 {
				presentList = append(presentList, &pb.PresentItem{
					PresentDay:   data.LevelDay,
					PresentLevel: int32(i),
				})
			} else {
				if data.LevelDay != levelDay {
					presentList = append(presentList, &pb.PresentItem{
						PresentDay:   data.LevelDay,
						PresentLevel: int32(i),
					})
				}
			}
		}
	}
	return
}

// GetAllPresentListRedis TODO
func GetAllPresentListRedis(ctx context.Context) (presentList []*pb.PresentItem, err error) {
	presentList = make([]*pb.PresentItem, 0)
	redisPrefixKey := global.GetPrefix()
	allPresentListRedisKey := fmt.Sprintf("%v-warframe-all-present-list", redisPrefixKey)
	allPresentListJsonStr, err := redis.GetClient().Get(ctx, allPresentListRedisKey).Result()
	if err != nil && err != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			err.Error())
		return
	}

	if allPresentListJsonStr != "" {
		err = json.Unmarshal([]byte(allPresentListJsonStr), &presentList)
		if err != nil {
			// 告警
			errs.NewCustomError(ctx, 611010, "redis get err,redisKey=%v,val=%v,err=%v", allPresentListRedisKey,
				allPresentListJsonStr,
				err)
		}
	} else {
		presentList, err = GetAllPresentList(ctx)
		if err != nil {
			return
		}

		newAllPresentListJsonStr, errJ := json.Marshal(presentList)
		if errJ != nil {
			err = errJ
			return
		}
		ok, errR := redis.GetClient().SetNX(ctx, allPresentListRedisKey, newAllPresentListJsonStr, 12*time.Hour).Result()
		if errR != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis error err=[%v]", errR.Error())
			return
		}
		if !ok {
			log.WithFieldsContext(ctx, "error").Errorf("set redis unsuccess key:[%v], value:[%v]", allPresentListRedisKey,
				newAllPresentListJsonStr)
			return
		}

	}
	return
}

// DelRedis TODO
func DelRedis(ctx context.Context, key string) (err error) {
	redisPrefixKey := global.GetPrefix()
	hashKey := fmt.Sprintf("%v-%v", redisPrefixKey, key)
	scheduleCtx := context.Background()
	keys, errs := redis.GetClient().Del(scheduleCtx, hashKey).Result()
	if errs != nil {
		err = errs
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
		"[warframe] all Present list delete redis keys: [%v]",
		keys))
	return
}

// GetAllPresentList TODO
func GetAllPresentList(ctx context.Context) (presentList []*pb.PresentItem, err error) {
	presentList = make([]*pb.PresentItem, 0)
	now := time.Now()
	// 加载UTC8时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "time-error").Infof("err: %v", err)
		return
	}
	utc8Time := now.In(loc)
	levelDay := utc8Time.AddDate(0, 0, -1).Format("2006-01-02")
	for i := 1; i <= 5; i += 1 {
		data := make([]*warframe.WarframeUserWishPool, 0)
		tableName := fmt.Sprintf("%v_%v", warframe.WarframeUserWishPoolModel{}.TableName(), i)
		db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
			Where("is_win = 1").
			Find(&data)
		if err = db.Error; err != nil && db.Error != gorm.ErrRecordNotFound {
			log.WithFieldsContext(ctx, "log_type", "select_all_present_error").Infof("i: %v, err: %v", i, err)
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
		for _, item := range data {
			insert := false
			log.WithFieldsContext(ctx, "log_type", "select_all_present").Infof("i: %v, item: %+v hour: %v, levelDay: %v, presentList: %+v", i, item, utc8Time.Hour(), levelDay, presentList)
			for _, prsentItem := range presentList {
				if prsentItem.PresentDay == item.LevelDay && int32(i) == prsentItem.PresentLevel {
					// 获取当前时间
					if utc8Time.Hour() >= 12 {
						insert = true
						prsentItem.SteamId = fmt.Sprintf("%v,%v", prsentItem.SteamId, MaskLastFour(item.SteamId))
					} else {
						insert = true
						if item.LevelDay != levelDay {
							prsentItem.SteamId = fmt.Sprintf("%v,%v", prsentItem.SteamId, MaskLastFour(item.SteamId))
						}
					}
				}
			}
			if !insert {
				log.WithFieldsContext(ctx, "log_type", "select_all_present").Infof("i: %v, item: %+v hour: %v, insert: %v, levelDay: %v", i, item, utc8Time.Hour(), insert, levelDay)
				log.WithFieldsContext(ctx, "log_type", "select_all_present").Infof("item.LevelDay: %v, levelDay: %v, hour: %v, result: %v", item.LevelDay, levelDay, utc8Time.Hour(), (item.LevelDay != levelDay || utc8Time.Hour() >= 12))

			}
			if !insert && (item.LevelDay != levelDay || utc8Time.Hour() >= 12) {
				presentList = append(presentList, &pb.PresentItem{
					PresentDay:   item.LevelDay,
					PresentLevel: int32(i),
					SteamId:      MaskLastFour(item.SteamId),
				})
			}
		}
	}
	return
}

// MaskLastFour TODO
func MaskLastFour(s string) string {
	if len(s) <= 4 {
		return strings.Repeat("*", len(s))
	}
	return s[:len(s)-4] + strings.Repeat("*", 4)
}

// JoinWishesPool TODO
func JoinWishesPool(ctx context.Context, level int32) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	openids := make([]string, 0)
	openids = append(openids, strings.Split(userAccount.Uid, "-")[1])
	var GetQQBoundSteamInfoRsp *cnbotPb.GetQQBoundSteamInfoRsp
	proxy := cnbotPb.NewOperationsClientProxy()
	GetQQBoundSteamInfoRsp, err = proxy.GetQQBoundSteamInfo(ctx, &cnbotPb.GetQQBoundSteamInfoReq{
		Openids:  openids,
		WebAppid: "*********",
	})
	if err != nil {
		return
	}

	tableName := fmt.Sprintf("%v_%v", warframe.WarframeUserWishPoolModel{}.TableName(), level)

	now := time.Now()
	// 加载UTC8时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "time-error").Infof("err: %v", err)
		return
	}

	// 将当前时间转换成指定时区的时间
	utc8Time := now.In(loc)
	levelDay := utc8Time.Format("2006-01-02")
	// 先查找之前中过么
	var alreadyData *warframe.WarframeUserWishPool
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where("uid = ? and account_type = ?", userAccount.Uid, userAccount.AccountType).
		First(&alreadyData)
	if err = db.Error; err != nil && db.Error != gorm.ErrRecordNotFound {
		if db.Error == gorm.ErrRecordNotFound {
			err = nil
		} else {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
	}
	isAlreadyWin := 0
	if db.Error != gorm.ErrRecordNotFound {
		isAlreadyWin = int(alreadyData.IsAlreadyWin)
	}
	data := &warframe.WarframeUserWishPool{
		Level:        int16(level),
		UID:          userAccount.Uid,
		AccountType:  int16(userAccount.AccountType),
		LevelDay:     levelDay,
		IsAlreadyWin: int16(isAlreadyWin),
		IsWin:        0,
		SteamName:    GetQQBoundSteamInfoRsp.SteamUserSummaries[0].Nickname,
		SteamId:      GetQQBoundSteamInfoRsp.SteamUserSummaries[0].SteamId,
	}
	db = DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where("uid = ? and level_day = ?", userAccount.Uid, levelDay).
		FirstOrCreate(&data)
	if err = db.Error; err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	return
}

// isNotDuplicateInsertError 是否不是mysql重复插入报错
func isNotDuplicateInsertError(err error) bool {
	mysqlErr, ok := err.(*mysql.MySQLError)
	if !ok {
		return false
	}
	// Error 1062: Duplicate entry for key
	return mysqlErr.Number != 1062
}

// GetWishedUserPlaytimeForever 获取用户游戏时长
func GetWishedUserPlaytimeForever(ctx context.Context, uid string, accountType int) (time int64, err error) {
	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         uid,
		AccountType: accountPb.AccountType(accountType),
		QqConnectAccount: &accountPb.QQConnectAccount{
			Openid: strings.Split(uid, "-")[1],
			AppId:  "*********",
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	proxy := cnbotPb.NewOperationsClientProxy()
	var GetPlayTimeForeverRsp *cnbotPb.GetPlayTimeForeverRsp
	GetPlayTimeForeverRsp, err = proxy.GetPlayTimeForever(ctx, &cnbotPb.GetPlayTimeForeverReq{
		SteamGameId: 230410,
	}, callopts...)
	if err != nil {
		return
	}
	log.WithFieldsContext(ctx, "log_type", "GetWishedUserPlaytimeForever").Infof("uid: %v, GetPlayTimeForeverRsp: %v", uid,
		GetPlayTimeForeverRsp)
	time = GetPlayTimeForeverRsp.PlaytimeForever
	return
}

// SetUserPlaytimeForever 保存用户游戏总时长
func SetUserPlaytimeForever(ctx context.Context, uid string, accountType int, playTimeForever int64) (err error) {
	log.WithFieldsContext(ctx, "log_type", "SetUserPlaytimeForever").Infof("uid: %v, playTimeForever: %v", uid,
		playTimeForever)
	now := time.Now()

	// 加载UTC8时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "time-error").Infof("err: %v", err)
		return
	}

	// 将当前时间转换成指定时区的时间
	utc8Time := now.In(loc)
	// 将时间格式化成yyyy-mm-dd格式
	day := utc8Time.Format("2006-01-02")

	data := &warframe.WarframeUserPlayTimeForever{
		UID:             uid,
		AccountType:     int16(accountType),
		Day:             day,
		PlayTimeForever: int64(playTimeForever),
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(warframe.WarframeUserPlayTimeForever{}.TableName()).
		Where("uid = ? and account_type = ? and day = ?", uid, accountType, day).
		FirstOrCreate(&data)
	if err = db.Error; err != nil && !isNotDuplicateInsertError(err) {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	return
}

// IsPlayYesterDay 是否今天玩过游戏
func IsPlayYesterDay(ctx context.Context, uid string, accountType int) (isPlay bool, err error) {
	isPlay = false
	now := time.Now()

	// 加载UTC8时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "time-error").Infof("err: %v", err)
		return
	}

	// 将当前时间转换成指定时区的时间
	utc8Time := now.In(loc)
	// 查找前一天数据
	toDay := utc8Time.Format("2006-01-02")

	// 计算昨天的日期
	yesterday := utc8Time.AddDate(0, 0, -1)

	// 格式化日期为 yyyy-mm-dd
	prevDay := yesterday.Format("2006-01-02")
	prevData := &warframe.WarframeUserPlayTimeForever{}
	db := DB.DefaultConnect().WithContext(ctx).Table(warframe.WarframeUserPlayTimeForever{}.TableName()).
		Where("uid = ? and account_type = ? and day = ?", uid, accountType, prevDay).
		First(&prevData)
	if err = db.Error; err != nil {
		if db.Error == gorm.ErrRecordNotFound {
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	// 查找今天数据
	data := &warframe.WarframeUserPlayTimeForever{}
	db = DB.DefaultConnect().WithContext(ctx).Table(warframe.WarframeUserPlayTimeForever{}.TableName()).
		Where("uid = ? and account_type = ? and day = ?", uid, accountType, toDay).
		First(&data)
	if err = db.Error; err != nil {
		if db.Error == gorm.ErrRecordNotFound {
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	isPlay = data.PlayTimeForever > prevData.PlayTimeForever

	return
}

// UpdatePlayTimeForever 更新游戏总时长数据
func UpdatePlayTimeForever(ctx context.Context) (err error) {
	// 拉取许愿人uid，拉取奖池中uid，去重然后更新数据
	scheduleCtx := context.Background()
	wishData := make([]*warframe.WarframeUserWishRecord, 0)
	db := DB.DefaultConnect().WithContext(scheduleCtx).Table(warframe.WarframeUserWishRecord{}.TableName()).
		Find(&wishData)
	if err = db.Error; err != nil && !isNotDuplicateInsertError(err) {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}

	needGetData := make([]struct {
		Uid         string
		AccountType int
	}, 0)

	for _, wish := range wishData {
		needGetData = append(needGetData, struct {
			Uid         string
			AccountType int
		}{
			Uid:         wish.UID,
			AccountType: int(wish.AccountType),
		})
	}

	log.WithFieldsContext(scheduleCtx, "log_type", "UpdatePlayTimeForever_count").Infof("count: %v, wishCount: %v",
		len(needGetData), len(wishData))

	now := time.Now()

	// 加载UTC8时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "time-error").Infof("err: %v", err)
		return
	}

	// 将当前时间转换成指定时区的时间
	utc8Time := now.In(loc)
	toDay := utc8Time.Format("2006-01-02")

	var wg sync.WaitGroup
	maxGoroutines := 10
	semaphore := make(chan struct{}, maxGoroutines)

	for _, item := range needGetData {
		wg.Add(1)
		go func(items struct {
			Uid         string
			AccountType int
		}) (err error) {
			semaphore <- struct{}{}
			defer func() {
				time.Sleep(2 * time.Second)
				<-semaphore
				wg.Done()
			}()
			playTimeForever, err := GetWishedUserPlaytimeForever(scheduleCtx, items.Uid, items.AccountType)
			if err != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "GetWishedUserPlaytimeForever_error").Infof("uid: %v, err: %v",
					items.Uid, err)
				return err
			}

			data := &warframe.WarframeUserPlayTimeForever{
				UID:             items.Uid,
				AccountType:     int16(items.AccountType),
				PlayTimeForever: int64(playTimeForever),
				Day:             toDay,
			}
			db = DB.DefaultConnect().WithContext(scheduleCtx).Table(warframe.WarframeUserPlayTimeForever{}.TableName()).
				Create(&data)
			if err = db.Error; err != nil && !isNotDuplicateInsertError(err) {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", err)
				return
			}
			return
		}(item)
	}
	wg.Wait()

	return
}
