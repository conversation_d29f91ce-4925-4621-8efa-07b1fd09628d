package model

type CommentContentAudit struct {
	*Model
	Comment *Comment `json:"-" gorm:"-"`
	//CommentReply *CommentReply `json:"-"`
	Type             int8   `json:"type"` // 评论类型 1:评论;2:回复评论
	CommentUUID      string `json:"comment_uuid"`
	IntlOpenid       string `json:"intl_openid"`
	TextRiskLevel    int    `json:"text_risk_level"` //风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	TextRiskType     int    `json:"text_risk_type"`  //风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	PicRiskLevel     int    `json:"pic_risk_level"`  //风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	PicRiskType      int    `json:"pic_risk_type"`   //风险类别，0：其他恶意；100：正常；101：涉政；102：色情；103：低俗；104：性感；105：招嫖广告；106：业务广告；107：第三方广告；108：二维码；109：暴力血腥；110：侵权；999：其他
	Status           int    `json:"status"`          // 审核状态 1:未处理;2:已发布;3:已忽略
	AuditUser        string `json:"audit_user"`      // 审核人
	AuditOn          int64  `json:"audit_on"`        // 审核时间
	AreaId           string `json:"area_id"`
	GameId           string `json:"game_id"`
	MachineStatus    int    `json:"machine_status"`    //机审状态：0-未处理1-审核通过2-审核异常
	ArtificialStatus int    `json:"artificial_status"` //人审状态：0-未处理1-审核通过2-审核拒绝
	DelType          int32  `json:"del_type"`          // 删除类型，isdel > 0 这个字段才生效
	DelReason        int32  `json:"del_reason"`        // 删除原因
}

type CommentContentAuditToCache struct {
	CommentUUID string `gorm:"column: comment_uuid" json:"comment_uuid"`
	CreatedOn   int64  `gorm:"column: created_on" json:"created_on"`
}

func (c *CommentContentAudit) TableName() string {
	return "p_comment_content_audit"
}
