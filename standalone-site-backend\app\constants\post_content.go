package constants

// 类型，1标题，2文字段落，3图片地址，4视频地址，5语音地址，6链接地址，7附件资源，8收费资源，9富文本文字段落

type PostContentT int

const (
	CONTENT_TYPE_TITLE PostContentT = iota + 1
	CONTENT_TYPE_TEXT
	CONTENT_TYPE_IMAGE
	CONTENT_TYPE_VIDEO
	CONTENT_TYPE_AUDIO
	CONTENT_TYPE_LINK
	CONTENT_TYPE_ATTACHMENT
	CONTENT_TYPE_CHARGE_ATTACHMENT
	CONTENT_TYPE_RICHTEXT_TEXT
)

// 帖子类型  1帖子(富文本) 2图片 3 视频文字
const (
	POST_TYPE_RICH_TEXT = iota + 1
	POST_TYPE_IMAGE_TEXT
	POST_TYPE_VIDEO_TEXT
)

const (
	CommentPosStatusDefault           = 0 // 【评论】普通状态【非置顶或置底状态】 0与3等价
	CommentPosStatusTop               = 1 // 【评论】置顶
	CommentPosStatusBottom            = 2 // 【评论】置底
	CommentPosStatusGeneral           = 3 // 【评论】普通状态【非置顶或置底状态】
	CommentPostStatusModerationTop    = 4 // 【评论】版主置顶
	CommentPostStatusModerationBottom = 5 // 【评论】版主置底
	CommentPostStatusPosterTop        = 6 // 【评论】作者置顶
	CommentPostStatusPosterBottom     = 7 // 【评论】作者置底
)

type ECommentPosStatusString string

const (
	ECommentPosStatusString_Unset  ECommentPosStatusString = "unset"
	ECommentPosStatusString_Top    ECommentPosStatusString = "top"
	ECommentPosStatusString_Bottom ECommentPosStatusString = "bottom"
)

type EOriginalType int32

const (
	EOriginalType_Original EOriginalType = iota + 1 // 原创
	EOriginalType_Not                               // 非原创
)

// 创作声明类型
type ECreatorStatementType int32

const (
	ECreatorStatementType_None                   ECreatorStatementType = iota // 无声明(未打开原创开关&未填写原创链接)
	ECreatorStatementType_FromUrl                                             // 搬运内容
	ECreatorStatementType_FromSelf_DisRepostable                              // 原创 不允许转载
	ECreatorStatementType_FromSelf_Repostable                                 // 原创 允许转载
)

// 风险提醒
type ERiskRemindType int32

const (
	ERiskRemindType_None     ERiskRemindType = iota // 无风险提示
	ERiskRemindType_Spoilers                        // 剧透风险
	ERiskRemindType_Content                         // 内容风险
)

type EAiContentType int32

const (
	EAiContentType_No EAiContentType = iota // 非AI内容
	EAiContentType_Yes
)
