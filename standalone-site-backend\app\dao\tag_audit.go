package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

func TagAuditGet(id, tagId int64, intlOpenID string) (*model.TagAudit, error) {
	var tagAudit model.TagAudit
	db := DB.SelectConnect("db_standalonesite").Table((&model.TagAudit{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if tagId > 0 {
		db = db.Where("tag_id = ?", tagId)
	}
	if intlOpenID != "" {
		db = db.Where("intl_openid =  ?", intlOpenID)
	}

	err := db.First(&tagAudit).Error
	if err != nil {
		return nil, err
	}
	var tag model.Tag
	err = db.Session(&gorm.Session{NewDB: true}).Table((&model.Tag{}).TableName()).Where("id = ? AND is_del = 0", tagAudit.TagID).First(&tag).Error
	if err != nil {
		return nil, err
	}
	tagAudit.Tag = &tag

	return &tagAudit, nil
}

func TagAuditCreate(audit *model.TagAudit) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.TagAudit{}).TableName()).Omit("Tag").Create(&audit).Error

	return err
}

func TagAuditDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.TagAudit{}).TableName()).Omit("Tag").Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}
