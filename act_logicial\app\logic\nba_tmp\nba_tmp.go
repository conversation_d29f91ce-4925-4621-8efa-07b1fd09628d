package nba_tmp

import (
	"context"
	"encoding/json"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
)

const (
	Oceania      = 1001
	SouthAmerica = 5001
	NorthAmerica = 6001
	Asia         = 7001
	Europe       = 8001
)

type RegionRsp struct {
	Code int32 `json:"code"`
	Data Data
}

type Data struct {
	Ret int32 `json:"ret_"`
}

func SendRequestByRegionCode(ctx context.Context, regionCode int32, invitationCode string) (RegionRsp, error) {
	rspData := RegionRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return rspData, err
	}
	regionCodeUrlMap := config.GetConfig().RegionCodeUrlMap
	url, ok := regionCodeUrlMap[cast.ToString(regionCode)]
	if !ok {
		return rspData, errs.NewCustomError(ctx, code.NotCurrentRegionCode,
			"SendRequestByRegionCode not regionCode:[%v]", regionCode)
	}
	openid, err := cast.ToInt64E(userAccount.IntlAccount.OpenId)
	if err != nil {
		return rspData, errs.NewCustomError(ctx, code.CommonParamTypeErr,
			"SendRequestByRegionCode OpenId To Int OpenId:[%v]", userAccount.IntlAccount.OpenId)
	}

	optionOne := httpclient.ClientOption{
		URL: url,
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		Type: "POST",
		PostData: map[string]interface{}{
			"invitation_code_": invitationCode,
			"invitee_gopenid_": openid,
		},
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	if resultOption.RequestError != nil {
		// 请求失败
		err = errs.NewCustomError(ctx, code.OfficialNoAppointmentError,
			"SendRequestByRegionCode http error, \t [Error]:{%v} ", resultOption.RequestError)
		return rspData, err
	}
	response := resultOption.Result
	jsonErr := json.Unmarshal([]byte(response), &rspData)
	if jsonErr != nil {
		err = errs.NewCustomError(ctx, code.JsonParseError,
			"SendRequestByRegionCode Unmarshal err,result=%v, \t [Error]:{%v} ", response, jsonErr)
		return rspData, err
	}
	return rspData, nil

}
