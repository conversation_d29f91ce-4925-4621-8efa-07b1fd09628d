package constants

// PostCommentOptT CMS-TOC资讯接口查询评论列表的操作类型
type PostCommentOptT int

const (
	COMMENT_OPT_TYPE_DEFAULT      PostCommentOptT = iota // 默认根据biz_id,parent_id去查询（如果有登录态或者传了用户信息则同时返回未审核的评论）
	COMMENT_OPT_TYPE_BYUSERHOME                          // 查询主态的评论列表
	COMMENT_OPT_TYPE_BYGUESTUSER                         // 客态查询某个用户的全部评论列表
	COMMENT_OPT_TYPE_BYCOMMENTID                         // 通过评论ID查询
	COMMENT_OPT_TYPE_ALLCOMMENTID                        // 查询指定biz_id,内容id下全部的评论列表
)

// ReviewCommentOptT CMS-TOC资讯接口审批评论的操作类型
type ReviewCommentOptT int

const (
	REVIEW_COMMENT_OPT_TYPE_COMMENTID ReviewCommentOptT = iota // 通过评论ID
	REVIEW_COMMENT_OPT_TYPE_UID                                // 通过uid拒绝未审核的评论
	REVIEW_COMMENT_OPT_TYPE_PARENTID                           // 通过parent_id拒绝未审核的评论
)

// PunishCommentOptT CMS-TOC资讯接口处罚评论的操作类型
type PunishCommentByUidOptT int

const (
	PUNISH_COMMENT_TYPE_UID_ALL            PunishCommentByUidOptT = iota // 评论全部删除
	PUNISH_COMMENT_TYPE_UID_NOTREVIEW                                    // 只删除没审核的评论
	PUNISH_COMMENT_TYPE_PARENTID_NOTREVIEW                               // 只删除没审核的评论，根据parent_id
)
