package address

import "trpc.act.logicial/app/model"

// AccountAddressModel 用户地址表
type AccountAddressModel struct {
	model.AppModel
}

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名
func (AccountAddressModel) TableName() string {
	return "address_account_log"
}

// AddressAccountLog TODO
type AddressAccountLog struct {
	AccountAddressModel
	UID         string `gorm:"type:varchar(64);not null;default:'';column:uid" json:"uid"`
	AccountType int    `gorm:"type:smallint;not null;default:0;column:account_type" json:"account_type"`
	Name        string `gorm:"type:varchar(512);not null;default:''" json:"name"`
	PhoneNumber string `gorm:"type:varchar(512);not null;default:'';column:phone_number" json:"phone_number"`
	Country     string `gorm:"type:varchar(512);not null;default:''" json:"country"`
	Address     string `gorm:"type:text" json:"address"`
	Email       string `gorm:"type:varchar(512);not null;default:''" json:"email"`
	ZipCode     string `gorm:"type:varchar(512);not null;default:'';column:zip_code" json:"zip_code"`
	FsourceID   string `gorm:"type:varchar(64);not null;default:'';column:Fsource_id" json:"Fsource_id"`
	CreatedAt   uint   `gorm:"type:int(11) unsigned;not null;default:0;column:created_at" json:"created_at"`
	UpdatedAt   uint   `gorm:"type:int(11) unsigned;not null;default:0;column:updated_at" json:"updated_at"`
}
