// Package tarisinvitation 塔瑞斯人拉人 活动
package tarisinvitation

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_taris_invitation"
	tarisInvitation "trpc.act.logicial/app/logic/taris_invitation"
)

// TarisInvitationTmpl 结构体
type TarisInvitationTmpl struct {
	pb.UnimplementedTarisInvitation
}

// AsyncInviteTaskStatus 同步被邀请人和邀请人任务完成状态
func (s *TarisInvitationTmpl) AsyncInviteTaskStatus(ctx context.Context, req *pb.AsyncInviteTaskStatusReq) (
	rsp *pb.AsyncInviteTaskStatusRsp, err error,
) {
	rsp = &pb.AsyncInviteTaskStatusRsp{}

	err = tarisInvitation.AsyncInviteTaskStatus(ctx, req.FsourceId)
	if err != nil {
		return
	}
	return
}

// InvitePeopleList 邀请人任务完成列表
func (s *TarisInvitationTmpl) InvitePeopleList(ctx context.Context, req *pb.InvitePeopleListReq) (
	rsp *pb.InvitePeopleListRsp, err error,
) {

	rsp = &pb.InvitePeopleListRsp{
		PeopleList: make([]*pb.InvitePeopleItem, 0),
		Total:      0,
	}

	rsp.PeopleList, err = tarisInvitation.InvitePeopleList(ctx, req.FsourceId, req.PageSize, req.PageNum)
	if err != nil {
		return
	}

	rsp.Total, err = tarisInvitation.InvitePeopleListTotal(ctx, req.FsourceId)
	if err != nil {
		return
	}

	return
}

// InviteTaskFinishNum 邀请人各个任务完成数量
func (s *TarisInvitationTmpl) InviteTaskFinishNum(ctx context.Context, req *pb.InviteTaskFinishNumReq) (
	rsp *pb.InviteTaskFinishNumRsp, err error,
) {
	rsp, err = tarisInvitation.InviteTaskFinishNum(ctx, req.FsourceId)
	if err != nil {
		return
	}
	return
}

// AddInviteData 添加被邀请人记录
func (s *TarisInvitationTmpl) AddInviteData(ctx context.Context, req *pb.AddInviteDataReq) (
	rsp *pb.AddInviteDataRsp, err error,
) {
	rsp = &pb.AddInviteDataRsp{}

	err = tarisInvitation.AddInviteData(ctx, req.FsourceId, req.InviteeOpenid)
	if err != nil {
		return
	}
	return
}

// IsNewInstallOrActive 判断用户是新进还是回流
func (s *TarisInvitationTmpl) IsNewInstallOrActive(ctx context.Context, req *pb.IsNewInstallOrActiveReq) (
	rsp *pb.IsNewInstallOrActiveRsp, err error,
) {
	rsp = &pb.IsNewInstallOrActiveRsp{
		IsNewInstall: false,
		IsActive:     false,
	}

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	isNewInstall, isActive, err := tarisInvitation.IsNewInstallOrActive(ctx, userAccount.Uid,
		req.FsourceId)
	if err != nil {
		return
	}

	rsp.IsNewInstall = isNewInstall
	rsp.IsActive = isActive
	return
}

// GetNumFinishAllTask 获取多少邀请用户完成了所有任务
func (s *TarisInvitationTmpl) GetNumFinishAllTask(ctx context.Context, req *pb.GetNumFinishAllTaskReq) (
	rsp *pb.GetNumFinishAllTaskRsp, err error,
) {
	rsp = &pb.GetNumFinishAllTaskRsp{
		Num: 0,
	}
	Num, err := tarisInvitation.GetNumFinishAllTask(ctx, req.FsourceId)
	if err != nil {
		return
	}

	rsp.Num = Num

	return
}

// GetInvitedPeopleNum 获取邀请用户总数
func (s *TarisInvitationTmpl) GetInvitedPeopleNum(ctx context.Context, req *pb.GetInvitedPeopleNumReq) (
	rsp *pb.GetInvitedPeopleNumRsp, err error,
) {
	rsp = &pb.GetInvitedPeopleNumRsp{
		Num: 0,
	}
	Num, err := tarisInvitation.InvitePeopleListTotal(ctx, req.FsourceId)
	if err != nil {
		return
	}

	rsp.Num = int64(Num)

	return
}

// GetInvitedExcel 导出塔瑞斯人拉人数据
func (s *TarisInvitationTmpl) GetInvitedExcel(ctx context.Context, req *pb.GetInvitedExcelReq) (
	rsp *pb.GetInvitedExcelRsp, err error,
) {
	rsp = &pb.GetInvitedExcelRsp{}
	go tarisInvitation.GetInvitedExcel(ctx, req.FsourceId)

	return
}

// GetInvitedIsNewinstallOrIsActive 获取邀请人列表和其新进还是回流
func (s *TarisInvitationTmpl) GetInvitedIsNewinstallOrIsActive(ctx context.Context,
	req *pb.GetInvitedIsNewinstallOrIsActiveReq) (
	rsp *pb.GetInvitedIsNewinstallOrIsActiveRsp, err error,
) {
	rsp = &pb.GetInvitedIsNewinstallOrIsActiveRsp{}
	go tarisInvitation.GetInvitedIsNewinstallOrIsActive(ctx, req.FsourceId)

	return
}

// GetInvitedPeopleAchieve 导出达到指定数量的邀请人
func (s *TarisInvitationTmpl) GetInvitedPeopleAchieve(ctx context.Context, req *pb.GetInvitedPeopleAchieveReq) (
	rsp *pb.GetInvitedPeopleAchieveRsp, err error,
) {
	rsp = &pb.GetInvitedPeopleAchieveRsp{}
	go tarisInvitation.GetInvitedPeopleAchieve(ctx)
	return
}
