// Package card 卡片
package card

import (
	"context"

	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_card"
)

// CardImpl 结构
type CardImpl struct {
	pb.UnimplementedCard
}

// SendCard 发放卡片
func (s *CardImpl) SendCard(ctx context.Context, req *pb.SendCardReq) (rsp *pb.SendCardRsp, err error) {
	rsp = &pb.SendCardRsp{}
	return
}

// CheckHasCard 判断是否已(未)拥有卡片
func (s *CardImpl) CheckHasCard(ctx context.Context, req *pb.CheckHasCardReq) (rsp *pb.CheckHasCardRsp, err error) {
	rsp = &pb.CheckHasCardRsp{}
	return
}

// HasCardList 已拥有卡片列表
func (s *CardImpl) HasCardList(ctx context.Context, req *pb.HasCardListReq) (rsp *pb.HasCardListRsp, err error) {
	rsp = &pb.HasCardListRsp{}
	return
}
