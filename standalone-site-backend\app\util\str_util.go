package util

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"math/big"
	"math/rand"
	"net/url"
	"regexp"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/uuid"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"golang.org/x/net/html"
	"trpc.publishing_application.standalonesite/app/constants"
)

type StrType int

const (
	NUM   StrType = iota // 数字
	LOWER                // 小写字母
	UPPER                // 大写字母
	ALL                  // 全部
	CLEAR                // 去除部分易混淆的字符
)

var fontKinds = [][]int{{10, 48}, {26, 97}, {26, 65}}
var letters = []byte("*******************************************")

// 字符串的工具类

func MD5(data []byte) string {
	m := md5.New()
	m.Write(data)
	return hex.EncodeToString(m.Sum(nil))
}

func shiftNumber(num int) int64 {
	result := 1
	for i := 0; i < num; i++ {
		result *= 10
	}
	return int64(result)
}

func GetRandomStrByUuid() (uid string, err error) {
	u2, err := uuid.NewV4()
	if err != nil {
		return
	} else {
		billno := u2.String()
		uid = strings.ReplaceAll(billno, "-", "")
	}
	return
}

// 生成随机字符串
// size 个数 kind 模式
func RandStr(size int, kind StrType) []byte {
	ikind, result := kind, make([]byte, size)
	isAll := kind > 2 || kind < 0
	rand.Seed(time.Now().UnixNano())
	for i := 0; i < size; i++ {
		if isAll {
			ikind = StrType(rand.Intn(int(ALL)))
		}
		scope, base := fontKinds[ikind][0], fontKinds[ikind][1]
		result[i] = uint8(base + rand.Intn(scope))
		// 不易混淆字符模式：重新生成字符
		if kind == 4 {
			result[i] = letters[rand.Intn(len(letters))]
		}
	}
	return result
}

// ParamMapToStr map 类型转str
func ParamMapToStr(param map[string]interface{}) (str string) {
	arrTmp := []string{}
	for index, item := range param {
		arrTmp = append(arrTmp, fmt.Sprintf("%v=%v", index, item))
	}
	str = strings.Join(arrTmp, "&")
	return
}

// 对某个可以转成uint类型的数据进行取余
func ExtractSurplusByOpenid(canBeUintStr string, places int) int64 {

	if places > len(canBeUintStr) {
		return toUint64(canBeUintStr)
	}
	suffixStr := canBeUintStr[len(canBeUintStr)-places:]
	return toUint64(suffixStr)
}

func toUint64(str string) int64 {
	num := new(big.Int)
	openidNum, isNum := num.SetString(str, 10)
	if !isNum || !openidNum.IsUint64() {
		// 转换失败直接退出, 避免0这种情况误判
		return -1
	}
	return openidNum.Int64()
}

func SliceToString(slice []int64) string {
	if len(slice) == 0 {
		return ""
	}

	var strArrs []string
	for _, v := range slice {
		strArrs = append(strArrs, fmt.Sprintf("%d", v))
	}
	return strings.Join(strArrs, ",")
}

// 转换一下language字段，因为mysql不支持zh-TW这种写法
func ZHTWLanguageToSql(language string) string {
	if language == "zh-TW" {
		return "zh_tw"
	}
	return language
}

func RemoveEmptyStrings(input []string) []string {
	// 创建一个新的切片来存储非空字符串
	var result []string

	// 遍历输入切片
	for _, str := range input {
		// 只将非空字符串添加到结果切片
		if str != "" {
			result = append(result, str)
		}
	}

	return result
}

// 剔除标签等标签
func TrimHtmlLabel(content string) ([]string, []string, int, error) {
	// 先抽离一遍符号文字
	// 定义正则表达式，匹配所有非字母和非数字字符
	// re := regexp.MustCompile(`[^\p{L}\p{N}\p{Hangul}\p{Han}]`)

	// 使用正则表达式替换匹配的字符为空字符串
	// content = re.ReplaceAllString(content, "")
	// 用于存储从文本字段抽离出来的图片链接、文本的数组、换行次数
	var contentImgLinks []string
	var contentTexts []string
	var lineBreakCount int
	// 兼容处理，如果前端富文本类型没有传抽离的文字内容，则获取富文本中的文字内容和富文本中的所有图片链接
	// 解析 HTML 文档
	htmlStr := strings.Join([]string{"<html><body>", content, "</body></html>"}, "")
	doc, err := html.Parse(strings.NewReader(htmlStr))
	if err != nil {
		return contentImgLinks, contentTexts, lineBreakCount, errors.New("publish failed, the post content is invalid")
	}
	// 查找并提取 div 和 p 标签文本，同时提取 img 标签的链接
	traverse(doc, []string{"div", "p"}, &contentImgLinks, &contentTexts, &lineBreakCount)
	return contentImgLinks, contentTexts, lineBreakCount, nil
}

// 遍历节点，查找特定标签并提取内容
func traverse(n *html.Node, tagNames []string, imgLinks *[]string, texts *[]string, lineBreakCount *int) {
	if n.Type == html.ElementNode {
		// 查找指定标签
		for _, tagName := range tagNames {
			if n.Data == tagName {
				text := extractText(n)
				if strings.TrimSpace(text) != "" {
					*texts = append(*texts, strings.TrimSpace(text))
				}
			}
		}

		// 处理 img 标签
		if n.Data == "img" {
			for _, attr := range n.Attr {
				if attr.Key == "src" {
					// 解析url，判断是否是表情
					parse, err := url.Parse(attr.Val)
					if err == nil {
						queryParams := parse.Query()
						imageType := queryParams.Get("imgtype")
						if imageType == "emoji" {
							break
						}
					}
					*imgLinks = append(*imgLinks, attr.Val)
					break
				}
			}
		}

		if n.Data == "br" {
			*lineBreakCount++
		}
	}

	// 递归处理子节点
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		traverse(c, tagNames, imgLinks, texts, lineBreakCount)
	}
}

// 提取并返回给定节点及其子节点的文本内容
func extractText(n *html.Node) string {
	if n.Type == html.TextNode {
		return n.Data
	}

	var buf bytes.Buffer
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		buf.WriteString(extractText(c))
	}
	return buf.String()
}

func RemoveSymbol(content string) string {
	// 定义正则表达式，匹配所有非字母和非数字字符
	re := regexp.MustCompile(`[^\p{L}\p{N}\p{Hangul}\p{Han}]`)

	// 使用正则表达式替换匹配的字符为空字符串
	contentTemp := re.ReplaceAllString(content, "")
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("RemoveSymbol | replace string: %s", contentTemp)
	return contentTemp
}

func IsPureNumber(s string) bool {
	for _, char := range s {
		if char < '0' || char > '9' {
			return false
		}
	}

	// 确保字符串不为空
	return len(s) > 0
}

// SplitStringByRuneCount 将字符串切割成指定的字符数
func SplitStringByRuneCount(s string, size int) []string {
	var result []string
	runes := []rune(s) // 将字符串转换为rune切片
	length := len(runes)

	for i := 0; i < length; i += size {
		end := i + size
		if end > length {
			end = length
		}
		result = append(result, string(runes[i:end]))
	}

	return result
}

// RandomColor 生成一个随机的颜色代码，格式为 #RRGGBB
func RandomColor() string {
	// 使用当前时间的纳秒数作为随机数种子
	rand.Seed(time.Now().UnixNano())

	// 生成随机的 RGB 值
	r := rand.Intn(256)
	g := rand.Intn(256)
	b := rand.Intn(256)

	// 格式化为 #RRGGBB
	return fmt.Sprintf("#%02X%02X%02X", r, g, b)
}
