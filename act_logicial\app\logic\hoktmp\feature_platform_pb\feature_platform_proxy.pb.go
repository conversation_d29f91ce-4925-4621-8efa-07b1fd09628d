// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.6.1
// source: feature_platform_proxy.proto

package feature_platform_pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EntityType int32

const (
	EntityType_RequestEntityType_None          EntityType = 0 //无效
	EntityType_RequestEntityType_Openid_Zoneid EntityType = 1 //角色维度
	EntityType_RequestEntityType_Openid        EntityType = 2 //玩家维度(openid)
	EntityType_RequestEntityType_Uid           EntityType = 3 //玩家维度(uid)
	EntityType_RequestEntityType_Itemid        EntityType = 4 //道具维度
)

// Enum value maps for EntityType.
var (
	EntityType_name = map[int32]string{
		0: "RequestEntityType_None",
		1: "RequestEntityType_Openid_Zoneid",
		2: "RequestEntityType_Openid",
		3: "RequestEntityType_Uid",
		4: "RequestEntityType_Itemid",
	}
	EntityType_value = map[string]int32{
		"RequestEntityType_None":          0,
		"RequestEntityType_Openid_Zoneid": 1,
		"RequestEntityType_Openid":        2,
		"RequestEntityType_Uid":           3,
		"RequestEntityType_Itemid":        4,
	}
)

func (x EntityType) Enum() *EntityType {
	p := new(EntityType)
	*p = x
	return p
}

func (x EntityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EntityType) Descriptor() protoreflect.EnumDescriptor {
	return file_feature_platform_proxy_proto_enumTypes[0].Descriptor()
}

func (EntityType) Type() protoreflect.EnumType {
	return &file_feature_platform_proxy_proto_enumTypes[0]
}

func (x EntityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EntityType.Descriptor instead.
func (EntityType) EnumDescriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{0}
}

type StringList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []string `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *StringList) Reset() {
	*x = StringList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringList) ProtoMessage() {}

func (x *StringList) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringList.ProtoReflect.Descriptor instead.
func (*StringList) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{0}
}

func (x *StringList) GetItems() []string {
	if x != nil {
		return x.Items
	}
	return nil
}

type FloatList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []float32 `protobuf:"fixed32,1,rep,packed,name=items,proto3" json:"items,omitempty"`
}

func (x *FloatList) Reset() {
	*x = FloatList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatList) ProtoMessage() {}

func (x *FloatList) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatList.ProtoReflect.Descriptor instead.
func (*FloatList) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{1}
}

func (x *FloatList) GetItems() []float32 {
	if x != nil {
		return x.Items
	}
	return nil
}

type Int64List struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []int64 `protobuf:"varint,1,rep,packed,name=items,proto3" json:"items,omitempty"`
}

func (x *Int64List) Reset() {
	*x = Int64List{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64List) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64List) ProtoMessage() {}

func (x *Int64List) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64List.ProtoReflect.Descriptor instead.
func (*Int64List) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{2}
}

func (x *Int64List) GetItems() []int64 {
	if x != nil {
		return x.Items
	}
	return nil
}

type StringFloatMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keys   []string  `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
	Values []float32 `protobuf:"fixed32,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (x *StringFloatMap) Reset() {
	*x = StringFloatMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringFloatMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringFloatMap) ProtoMessage() {}

func (x *StringFloatMap) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringFloatMap.ProtoReflect.Descriptor instead.
func (*StringFloatMap) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{3}
}

func (x *StringFloatMap) GetKeys() []string {
	if x != nil {
		return x.Keys
	}
	return nil
}

func (x *StringFloatMap) GetValues() []float32 {
	if x != nil {
		return x.Values
	}
	return nil
}

type Int64FloatMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keys   []int64   `protobuf:"varint,1,rep,packed,name=keys,proto3" json:"keys,omitempty"`
	Values []float32 `protobuf:"fixed32,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (x *Int64FloatMap) Reset() {
	*x = Int64FloatMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64FloatMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64FloatMap) ProtoMessage() {}

func (x *Int64FloatMap) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64FloatMap.ProtoReflect.Descriptor instead.
func (*Int64FloatMap) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{4}
}

func (x *Int64FloatMap) GetKeys() []int64 {
	if x != nil {
		return x.Keys
	}
	return nil
}

func (x *Int64FloatMap) GetValues() []float32 {
	if x != nil {
		return x.Values
	}
	return nil
}

type StringFloatListMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*StringFloatListMap_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *StringFloatListMap) Reset() {
	*x = StringFloatListMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringFloatListMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringFloatListMap) ProtoMessage() {}

func (x *StringFloatListMap) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringFloatListMap.ProtoReflect.Descriptor instead.
func (*StringFloatListMap) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{5}
}

func (x *StringFloatListMap) GetItems() []*StringFloatListMap_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type StringInt64Map struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keys   []string `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
	Values []int64  `protobuf:"varint,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (x *StringInt64Map) Reset() {
	*x = StringInt64Map{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringInt64Map) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringInt64Map) ProtoMessage() {}

func (x *StringInt64Map) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringInt64Map.ProtoReflect.Descriptor instead.
func (*StringInt64Map) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{6}
}

func (x *StringInt64Map) GetKeys() []string {
	if x != nil {
		return x.Keys
	}
	return nil
}

func (x *StringInt64Map) GetValues() []int64 {
	if x != nil {
		return x.Values
	}
	return nil
}

type BytesList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items [][]byte `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *BytesList) Reset() {
	*x = BytesList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BytesList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BytesList) ProtoMessage() {}

func (x *BytesList) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BytesList.ProtoReflect.Descriptor instead.
func (*BytesList) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{7}
}

func (x *BytesList) GetItems() [][]byte {
	if x != nil {
		return x.Items
	}
	return nil
}

type Int32List struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []int32 `protobuf:"varint,1,rep,packed,name=items,proto3" json:"items,omitempty"`
}

func (x *Int32List) Reset() {
	*x = Int32List{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32List) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32List) ProtoMessage() {}

func (x *Int32List) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32List.ProtoReflect.Descriptor instead.
func (*Int32List) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{8}
}

func (x *Int32List) GetItems() []int32 {
	if x != nil {
		return x.Items
	}
	return nil
}

type Uint32List struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []uint32 `protobuf:"varint,1,rep,packed,name=items,proto3" json:"items,omitempty"`
}

func (x *Uint32List) Reset() {
	*x = Uint32List{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint32List) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint32List) ProtoMessage() {}

func (x *Uint32List) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint32List.ProtoReflect.Descriptor instead.
func (*Uint32List) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{9}
}

func (x *Uint32List) GetItems() []uint32 {
	if x != nil {
		return x.Items
	}
	return nil
}

type Uint64List struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []uint64 `protobuf:"varint,1,rep,packed,name=items,proto3" json:"items,omitempty"`
}

func (x *Uint64List) Reset() {
	*x = Uint64List{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint64List) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint64List) ProtoMessage() {}

func (x *Uint64List) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint64List.ProtoReflect.Descriptor instead.
func (*Uint64List) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{10}
}

func (x *Uint64List) GetItems() []uint64 {
	if x != nil {
		return x.Items
	}
	return nil
}

type DoubleList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []float64 `protobuf:"fixed64,1,rep,packed,name=items,proto3" json:"items,omitempty"`
}

func (x *DoubleList) Reset() {
	*x = DoubleList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleList) ProtoMessage() {}

func (x *DoubleList) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleList.ProtoReflect.Descriptor instead.
func (*DoubleList) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{11}
}

func (x *DoubleList) GetItems() []float64 {
	if x != nil {
		return x.Items
	}
	return nil
}

type BoolList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []bool `protobuf:"varint,1,rep,packed,name=items,proto3" json:"items,omitempty"`
}

func (x *BoolList) Reset() {
	*x = BoolList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoolList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolList) ProtoMessage() {}

func (x *BoolList) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolList.ProtoReflect.Descriptor instead.
func (*BoolList) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{12}
}

func (x *BoolList) GetItems() []bool {
	if x != nil {
		return x.Items
	}
	return nil
}

type ValueList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*Value `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *ValueList) Reset() {
	*x = ValueList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValueList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValueList) ProtoMessage() {}

func (x *ValueList) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValueList.ProtoReflect.Descriptor instead.
func (*ValueList) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{13}
}

func (x *ValueList) GetItems() []*Value {
	if x != nil {
		return x.Items
	}
	return nil
}

type ValueMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items map[string]*Value `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ValueMap) Reset() {
	*x = ValueMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValueMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValueMap) ProtoMessage() {}

func (x *ValueMap) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValueMap.ProtoReflect.Descriptor instead.
func (*ValueMap) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{14}
}

func (x *ValueMap) GetItems() map[string]*Value {
	if x != nil {
		return x.Items
	}
	return nil
}

type Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Kind:
	//	*Value_StringVal
	//	*Value_BoolVal
	//	*Value_FloatVal
	//	*Value_DoubleVal
	//	*Value_BytesVal
	//	*Value_Int32Val
	//	*Value_Uint32Val
	//	*Value_Int64Val
	//	*Value_Uint64Val
	//	*Value_StringListVal
	//	*Value_FloatListVal
	//	*Value_Int64ListVal
	//	*Value_StringFloatMapVal
	//	*Value_Int64FloatMapVal
	//	*Value_StringFloatListMapVal
	//	*Value_StringInt64MapVal
	//	*Value_BytesListVal
	//	*Value_Int32ListVal
	//	*Value_Uint32ListVal
	//	*Value_Uint64ListVal
	//	*Value_DoubleListVal
	//	*Value_BoolListVal
	Kind isValue_Kind `protobuf_oneof:"kind"`
}

func (x *Value) Reset() {
	*x = Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Value) ProtoMessage() {}

func (x *Value) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Value.ProtoReflect.Descriptor instead.
func (*Value) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{15}
}

func (m *Value) GetKind() isValue_Kind {
	if m != nil {
		return m.Kind
	}
	return nil
}

func (x *Value) GetStringVal() string {
	if x, ok := x.GetKind().(*Value_StringVal); ok {
		return x.StringVal
	}
	return ""
}

func (x *Value) GetBoolVal() bool {
	if x, ok := x.GetKind().(*Value_BoolVal); ok {
		return x.BoolVal
	}
	return false
}

func (x *Value) GetFloatVal() float32 {
	if x, ok := x.GetKind().(*Value_FloatVal); ok {
		return x.FloatVal
	}
	return 0
}

func (x *Value) GetDoubleVal() float64 {
	if x, ok := x.GetKind().(*Value_DoubleVal); ok {
		return x.DoubleVal
	}
	return 0
}

func (x *Value) GetBytesVal() []byte {
	if x, ok := x.GetKind().(*Value_BytesVal); ok {
		return x.BytesVal
	}
	return nil
}

func (x *Value) GetInt32Val() int32 {
	if x, ok := x.GetKind().(*Value_Int32Val); ok {
		return x.Int32Val
	}
	return 0
}

func (x *Value) GetUint32Val() uint32 {
	if x, ok := x.GetKind().(*Value_Uint32Val); ok {
		return x.Uint32Val
	}
	return 0
}

func (x *Value) GetInt64Val() int64 {
	if x, ok := x.GetKind().(*Value_Int64Val); ok {
		return x.Int64Val
	}
	return 0
}

func (x *Value) GetUint64Val() uint64 {
	if x, ok := x.GetKind().(*Value_Uint64Val); ok {
		return x.Uint64Val
	}
	return 0
}

func (x *Value) GetStringListVal() *StringList {
	if x, ok := x.GetKind().(*Value_StringListVal); ok {
		return x.StringListVal
	}
	return nil
}

func (x *Value) GetFloatListVal() *FloatList {
	if x, ok := x.GetKind().(*Value_FloatListVal); ok {
		return x.FloatListVal
	}
	return nil
}

func (x *Value) GetInt64ListVal() *Int64List {
	if x, ok := x.GetKind().(*Value_Int64ListVal); ok {
		return x.Int64ListVal
	}
	return nil
}

func (x *Value) GetStringFloatMapVal() *StringFloatMap {
	if x, ok := x.GetKind().(*Value_StringFloatMapVal); ok {
		return x.StringFloatMapVal
	}
	return nil
}

func (x *Value) GetInt64FloatMapVal() *Int64FloatMap {
	if x, ok := x.GetKind().(*Value_Int64FloatMapVal); ok {
		return x.Int64FloatMapVal
	}
	return nil
}

func (x *Value) GetStringFloatListMapVal() *StringFloatListMap {
	if x, ok := x.GetKind().(*Value_StringFloatListMapVal); ok {
		return x.StringFloatListMapVal
	}
	return nil
}

func (x *Value) GetStringInt64MapVal() *StringInt64Map {
	if x, ok := x.GetKind().(*Value_StringInt64MapVal); ok {
		return x.StringInt64MapVal
	}
	return nil
}

func (x *Value) GetBytesListVal() *BytesList {
	if x, ok := x.GetKind().(*Value_BytesListVal); ok {
		return x.BytesListVal
	}
	return nil
}

func (x *Value) GetInt32ListVal() *Int32List {
	if x, ok := x.GetKind().(*Value_Int32ListVal); ok {
		return x.Int32ListVal
	}
	return nil
}

func (x *Value) GetUint32ListVal() *Uint32List {
	if x, ok := x.GetKind().(*Value_Uint32ListVal); ok {
		return x.Uint32ListVal
	}
	return nil
}

func (x *Value) GetUint64ListVal() *Uint64List {
	if x, ok := x.GetKind().(*Value_Uint64ListVal); ok {
		return x.Uint64ListVal
	}
	return nil
}

func (x *Value) GetDoubleListVal() *DoubleList {
	if x, ok := x.GetKind().(*Value_DoubleListVal); ok {
		return x.DoubleListVal
	}
	return nil
}

func (x *Value) GetBoolListVal() *BoolList {
	if x, ok := x.GetKind().(*Value_BoolListVal); ok {
		return x.BoolListVal
	}
	return nil
}

type isValue_Kind interface {
	isValue_Kind()
}

type Value_StringVal struct {
	StringVal string `protobuf:"bytes,1,opt,name=string_val,json=stringVal,proto3,oneof"`
}

type Value_BoolVal struct {
	BoolVal bool `protobuf:"varint,2,opt,name=bool_val,json=boolVal,proto3,oneof"`
}

type Value_FloatVal struct {
	FloatVal float32 `protobuf:"fixed32,3,opt,name=float_val,json=floatVal,proto3,oneof"`
}

type Value_DoubleVal struct {
	DoubleVal float64 `protobuf:"fixed64,4,opt,name=double_val,json=doubleVal,proto3,oneof"`
}

type Value_BytesVal struct {
	BytesVal []byte `protobuf:"bytes,5,opt,name=bytes_val,json=bytesVal,proto3,oneof"`
}

type Value_Int32Val struct {
	Int32Val int32 `protobuf:"varint,6,opt,name=int32_val,json=int32Val,proto3,oneof"`
}

type Value_Uint32Val struct {
	Uint32Val uint32 `protobuf:"varint,7,opt,name=uint32_val,json=uint32Val,proto3,oneof"`
}

type Value_Int64Val struct {
	Int64Val int64 `protobuf:"varint,8,opt,name=int64_val,json=int64Val,proto3,oneof"`
}

type Value_Uint64Val struct {
	Uint64Val uint64 `protobuf:"varint,9,opt,name=uint64_val,json=uint64Val,proto3,oneof"`
}

type Value_StringListVal struct {
	StringListVal *StringList `protobuf:"bytes,10,opt,name=string_list_val,json=stringListVal,proto3,oneof"`
}

type Value_FloatListVal struct {
	FloatListVal *FloatList `protobuf:"bytes,11,opt,name=float_list_val,json=floatListVal,proto3,oneof"`
}

type Value_Int64ListVal struct {
	Int64ListVal *Int64List `protobuf:"bytes,12,opt,name=int64_list_val,json=int64ListVal,proto3,oneof"`
}

type Value_StringFloatMapVal struct {
	StringFloatMapVal *StringFloatMap `protobuf:"bytes,13,opt,name=string_float_map_val,json=stringFloatMapVal,proto3,oneof"`
}

type Value_Int64FloatMapVal struct {
	Int64FloatMapVal *Int64FloatMap `protobuf:"bytes,14,opt,name=int64_float_map_val,json=int64FloatMapVal,proto3,oneof"`
}

type Value_StringFloatListMapVal struct {
	StringFloatListMapVal *StringFloatListMap `protobuf:"bytes,15,opt,name=string_float_list_map_val,json=stringFloatListMapVal,proto3,oneof"`
}

type Value_StringInt64MapVal struct {
	StringInt64MapVal *StringInt64Map `protobuf:"bytes,16,opt,name=string_int64_map_val,json=stringInt64MapVal,proto3,oneof"`
}

type Value_BytesListVal struct {
	BytesListVal *BytesList `protobuf:"bytes,17,opt,name=bytes_list_val,json=bytesListVal,proto3,oneof"`
}

type Value_Int32ListVal struct {
	Int32ListVal *Int32List `protobuf:"bytes,18,opt,name=int32_list_val,json=int32ListVal,proto3,oneof"`
}

type Value_Uint32ListVal struct {
	Uint32ListVal *Uint32List `protobuf:"bytes,19,opt,name=uint32_list_val,json=uint32ListVal,proto3,oneof"`
}

type Value_Uint64ListVal struct {
	Uint64ListVal *Uint64List `protobuf:"bytes,20,opt,name=uint64_list_val,json=uint64ListVal,proto3,oneof"`
}

type Value_DoubleListVal struct {
	DoubleListVal *DoubleList `protobuf:"bytes,21,opt,name=double_list_val,json=doubleListVal,proto3,oneof"`
}

type Value_BoolListVal struct {
	BoolListVal *BoolList `protobuf:"bytes,22,opt,name=bool_list_val,json=boolListVal,proto3,oneof"`
}

func (*Value_StringVal) isValue_Kind() {}

func (*Value_BoolVal) isValue_Kind() {}

func (*Value_FloatVal) isValue_Kind() {}

func (*Value_DoubleVal) isValue_Kind() {}

func (*Value_BytesVal) isValue_Kind() {}

func (*Value_Int32Val) isValue_Kind() {}

func (*Value_Uint32Val) isValue_Kind() {}

func (*Value_Int64Val) isValue_Kind() {}

func (*Value_Uint64Val) isValue_Kind() {}

func (*Value_StringListVal) isValue_Kind() {}

func (*Value_FloatListVal) isValue_Kind() {}

func (*Value_Int64ListVal) isValue_Kind() {}

func (*Value_StringFloatMapVal) isValue_Kind() {}

func (*Value_Int64FloatMapVal) isValue_Kind() {}

func (*Value_StringFloatListMapVal) isValue_Kind() {}

func (*Value_StringInt64MapVal) isValue_Kind() {}

func (*Value_BytesListVal) isValue_Kind() {}

func (*Value_Int32ListVal) isValue_Kind() {}

func (*Value_Uint32ListVal) isValue_Kind() {}

func (*Value_Uint64ListVal) isValue_Kind() {}

func (*Value_DoubleListVal) isValue_Kind() {}

func (*Value_BoolListVal) isValue_Kind() {}

type EntityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	ZoneId string `protobuf:"bytes,2,opt,name=zone_id,json=zoneId,proto3" json:"zone_id,omitempty"`
	Uid    string `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId string `protobuf:"bytes,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
}

func (x *EntityInfo) Reset() {
	*x = EntityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityInfo) ProtoMessage() {}

func (x *EntityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityInfo.ProtoReflect.Descriptor instead.
func (*EntityInfo) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{16}
}

func (x *EntityInfo) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *EntityInfo) GetZoneId() string {
	if x != nil {
		return x.ZoneId
	}
	return ""
}

func (x *EntityInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *EntityInfo) GetItemId() string {
	if x != nil {
		return x.ItemId
	}
	return ""
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       EntityType  `protobuf:"varint,1,opt,name=type,proto3,enum=helloworld.EntityType" json:"type,omitempty"`
	EntityInfo *EntityInfo `protobuf:"bytes,2,opt,name=entity_info,json=entityInfo,proto3" json:"entity_info,omitempty"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{17}
}

func (x *Request) GetType() EntityType {
	if x != nil {
		return x.Type
	}
	return EntityType_RequestEntityType_None
}

func (x *Request) GetEntityInfo() *EntityInfo {
	if x != nil {
		return x.EntityInfo
	}
	return nil
}

type GetFeaturesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Requests []*Request `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
}

func (x *GetFeaturesRequest) Reset() {
	*x = GetFeaturesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeaturesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeaturesRequest) ProtoMessage() {}

func (x *GetFeaturesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeaturesRequest.ProtoReflect.Descriptor instead.
func (*GetFeaturesRequest) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{18}
}

func (x *GetFeaturesRequest) GetRequests() []*Request {
	if x != nil {
		return x.Requests
	}
	return nil
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       EntityType        `protobuf:"varint,1,opt,name=type,proto3,enum=helloworld.EntityType" json:"type,omitempty"`
	EntityInfo *EntityInfo       `protobuf:"bytes,2,opt,name=entity_info,json=entityInfo,proto3" json:"entity_info,omitempty"`
	Features   map[string]*Value `protobuf:"bytes,3,rep,name=features,proto3" json:"features,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 特征名 -> 特征值
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{19}
}

func (x *Response) GetType() EntityType {
	if x != nil {
		return x.Type
	}
	return EntityType_RequestEntityType_None
}

func (x *Response) GetEntityInfo() *EntityInfo {
	if x != nil {
		return x.EntityInfo
	}
	return nil
}

func (x *Response) GetFeatures() map[string]*Value {
	if x != nil {
		return x.Features
	}
	return nil
}

type GetFeaturesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Responses []*Response `protobuf:"bytes,1,rep,name=responses,proto3" json:"responses,omitempty"`
}

func (x *GetFeaturesResponse) Reset() {
	*x = GetFeaturesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeaturesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeaturesResponse) ProtoMessage() {}

func (x *GetFeaturesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeaturesResponse.ProtoReflect.Descriptor instead.
func (*GetFeaturesResponse) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{20}
}

func (x *GetFeaturesResponse) GetResponses() []*Response {
	if x != nil {
		return x.Responses
	}
	return nil
}

type StringFloatListMap_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string    `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value []float32 `protobuf:"fixed32,2,rep,packed,name=value,proto3" json:"value,omitempty"`
}

func (x *StringFloatListMap_Item) Reset() {
	*x = StringFloatListMap_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_platform_proxy_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringFloatListMap_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringFloatListMap_Item) ProtoMessage() {}

func (x *StringFloatListMap_Item) ProtoReflect() protoreflect.Message {
	mi := &file_feature_platform_proxy_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringFloatListMap_Item.ProtoReflect.Descriptor instead.
func (*StringFloatListMap_Item) Descriptor() ([]byte, []int) {
	return file_feature_platform_proxy_proto_rawDescGZIP(), []int{5, 0}
}

func (x *StringFloatListMap_Item) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *StringFloatListMap_Item) GetValue() []float32 {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_feature_platform_proxy_proto protoreflect.FileDescriptor

var file_feature_platform_proxy_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x22, 0x22, 0x0a, 0x0a, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x21,
	0x0a, 0x09, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x02, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x22, 0x21, 0x0a, 0x09, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0x3c, 0x0a, 0x0e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x02, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x22, 0x3b, 0x0a, 0x0d, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x4d, 0x61, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x02, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22,
	0x7f, 0x0a, 0x12, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x39, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c,
	0x64, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x4d, 0x61, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x1a, 0x2e, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x3c, 0x0a, 0x0e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4d,
	0x61, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x21,
	0x0a, 0x09, 0x42, 0x79, 0x74, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x22, 0x21, 0x0a, 0x09, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0x22, 0x0a, 0x0a, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x22, 0x0a, 0x0a, 0x55, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x22, 0x0a, 0x0a,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x01, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x22, 0x20, 0x0a, 0x08, 0x42, 0x6f, 0x6f, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x08, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x22, 0x34, 0x0a, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x27, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x8e, 0x01, 0x0a, 0x08, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x35, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c,
	0x64, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x4b, 0x0a, 0x0a,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x27, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x68, 0x65,
	0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb2, 0x09, 0x0a, 0x05, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x12, 0x1b, 0x0a, 0x08, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x76, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x07, 0x62, 0x6f, 0x6f, 0x6c, 0x56, 0x61,
	0x6c, 0x12, 0x1d, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c,
	0x12, 0x1f, 0x0a, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x09, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61,
	0x6c, 0x12, 0x1d, 0x0a, 0x09, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x08, 0x62, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c,
	0x12, 0x1d, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x12,
	0x1f, 0x0a, 0x0a, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x09, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x12, 0x1d, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x12,
	0x1f, 0x0a, 0x0a, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x09, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c,
	0x12, 0x40, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x68, 0x65, 0x6c, 0x6c,
	0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x61, 0x6c, 0x12, 0x3d, 0x0a, 0x0e, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x5f, 0x76, 0x61, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x6c,
	0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x0c, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61,
	0x6c, 0x12, 0x3d, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x6c, 0x6c,
	0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x69, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c,
	0x12, 0x4d, 0x0a, 0x14, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74,
	0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4d, 0x61, 0x70, 0x48, 0x00, 0x52, 0x11, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x12,
	0x4a, 0x0a, 0x13, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x6d,
	0x61, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68,
	0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x46,
	0x6c, 0x6f, 0x61, 0x74, 0x4d, 0x61, 0x70, 0x48, 0x00, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x12, 0x5a, 0x0a, 0x19, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x70, 0x48, 0x00,
	0x52, 0x15, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x12, 0x4d, 0x0a, 0x14, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72,
	0x6c, 0x64, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4d, 0x61,
	0x70, 0x48, 0x00, 0x52, 0x11, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x12, 0x3d, 0x0a, 0x0e, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x42, 0x79, 0x74, 0x65,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0c, 0x62, 0x79, 0x74, 0x65, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x56, 0x61, 0x6c, 0x12, 0x3d, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x69, 0x73,
	0x74, 0x56, 0x61, 0x6c, 0x12, 0x40, 0x0a, 0x0f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x4c,
	0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x12, 0x40, 0x0a, 0x0f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x55, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x75, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x12, 0x40, 0x0a, 0x0f, 0x64, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x44,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x12, 0x3a, 0x0a, 0x0d, 0x62, 0x6f,
	0x6f, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x62, 0x6f, 0x6f, 0x6c, 0x4c,
	0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x42, 0x06, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x22, 0x69,
	0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x7a, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x22, 0x6e, 0x0a, 0x07, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x37, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72,
	0x6c, 0x64, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x45, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2f, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x22, 0xff, 0x01, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x68, 0x65,
	0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x0b, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c,
	0x64, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x73, 0x1a, 0x4e, 0x0a, 0x0d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x27, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c,
	0x64, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x49, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x68,
	0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x2a, 0xa4, 0x01,
	0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x5f, 0x5a, 0x6f, 0x6e, 0x65, 0x69, 0x64, 0x10, 0x01, 0x12, 0x1c, 0x0a,
	0x18, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x55, 0x69, 0x64, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x74, 0x65, 0x6d,
	0x69, 0x64, 0x10, 0x04, 0x32, 0x6a, 0x0a, 0x16, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x12, 0x50,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x1e, 0x2e,
	0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e,
	0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x43, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x65, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x73, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f, 0x72, 0x6c, 0x64,
	0x5a, 0x23, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x2f, 0x68, 0x65, 0x6c, 0x6c, 0x6f,
	0x77, 0x6f, 0x72, 0x6c, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_feature_platform_proxy_proto_rawDescOnce sync.Once
	file_feature_platform_proxy_proto_rawDescData = file_feature_platform_proxy_proto_rawDesc
)

func file_feature_platform_proxy_proto_rawDescGZIP() []byte {
	file_feature_platform_proxy_proto_rawDescOnce.Do(func() {
		file_feature_platform_proxy_proto_rawDescData = protoimpl.X.CompressGZIP(file_feature_platform_proxy_proto_rawDescData)
	})
	return file_feature_platform_proxy_proto_rawDescData
}

var file_feature_platform_proxy_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_feature_platform_proxy_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_feature_platform_proxy_proto_goTypes = []interface{}{
	(EntityType)(0),                 // 0: helloworld.EntityType
	(*StringList)(nil),              // 1: helloworld.StringList
	(*FloatList)(nil),               // 2: helloworld.FloatList
	(*Int64List)(nil),               // 3: helloworld.Int64List
	(*StringFloatMap)(nil),          // 4: helloworld.StringFloatMap
	(*Int64FloatMap)(nil),           // 5: helloworld.Int64FloatMap
	(*StringFloatListMap)(nil),      // 6: helloworld.StringFloatListMap
	(*StringInt64Map)(nil),          // 7: helloworld.StringInt64Map
	(*BytesList)(nil),               // 8: helloworld.BytesList
	(*Int32List)(nil),               // 9: helloworld.Int32List
	(*Uint32List)(nil),              // 10: helloworld.Uint32List
	(*Uint64List)(nil),              // 11: helloworld.Uint64List
	(*DoubleList)(nil),              // 12: helloworld.DoubleList
	(*BoolList)(nil),                // 13: helloworld.BoolList
	(*ValueList)(nil),               // 14: helloworld.ValueList
	(*ValueMap)(nil),                // 15: helloworld.ValueMap
	(*Value)(nil),                   // 16: helloworld.Value
	(*EntityInfo)(nil),              // 17: helloworld.EntityInfo
	(*Request)(nil),                 // 18: helloworld.Request
	(*GetFeaturesRequest)(nil),      // 19: helloworld.GetFeaturesRequest
	(*Response)(nil),                // 20: helloworld.Response
	(*GetFeaturesResponse)(nil),     // 21: helloworld.GetFeaturesResponse
	(*StringFloatListMap_Item)(nil), // 22: helloworld.StringFloatListMap.Item
	nil,                             // 23: helloworld.ValueMap.ItemsEntry
	nil,                             // 24: helloworld.Response.FeaturesEntry
}
var file_feature_platform_proxy_proto_depIdxs = []int32{
	22, // 0: helloworld.StringFloatListMap.items:type_name -> helloworld.StringFloatListMap.Item
	16, // 1: helloworld.ValueList.items:type_name -> helloworld.Value
	23, // 2: helloworld.ValueMap.items:type_name -> helloworld.ValueMap.ItemsEntry
	1,  // 3: helloworld.Value.string_list_val:type_name -> helloworld.StringList
	2,  // 4: helloworld.Value.float_list_val:type_name -> helloworld.FloatList
	3,  // 5: helloworld.Value.int64_list_val:type_name -> helloworld.Int64List
	4,  // 6: helloworld.Value.string_float_map_val:type_name -> helloworld.StringFloatMap
	5,  // 7: helloworld.Value.int64_float_map_val:type_name -> helloworld.Int64FloatMap
	6,  // 8: helloworld.Value.string_float_list_map_val:type_name -> helloworld.StringFloatListMap
	7,  // 9: helloworld.Value.string_int64_map_val:type_name -> helloworld.StringInt64Map
	8,  // 10: helloworld.Value.bytes_list_val:type_name -> helloworld.BytesList
	9,  // 11: helloworld.Value.int32_list_val:type_name -> helloworld.Int32List
	10, // 12: helloworld.Value.uint32_list_val:type_name -> helloworld.Uint32List
	11, // 13: helloworld.Value.uint64_list_val:type_name -> helloworld.Uint64List
	12, // 14: helloworld.Value.double_list_val:type_name -> helloworld.DoubleList
	13, // 15: helloworld.Value.bool_list_val:type_name -> helloworld.BoolList
	0,  // 16: helloworld.Request.type:type_name -> helloworld.EntityType
	17, // 17: helloworld.Request.entity_info:type_name -> helloworld.EntityInfo
	18, // 18: helloworld.GetFeaturesRequest.requests:type_name -> helloworld.Request
	0,  // 19: helloworld.Response.type:type_name -> helloworld.EntityType
	17, // 20: helloworld.Response.entity_info:type_name -> helloworld.EntityInfo
	24, // 21: helloworld.Response.features:type_name -> helloworld.Response.FeaturesEntry
	20, // 22: helloworld.GetFeaturesResponse.responses:type_name -> helloworld.Response
	16, // 23: helloworld.ValueMap.ItemsEntry.value:type_name -> helloworld.Value
	16, // 24: helloworld.Response.FeaturesEntry.value:type_name -> helloworld.Value
	19, // 25: helloworld.feature_platform_proxy.GetFeatures:input_type -> helloworld.GetFeaturesRequest
	21, // 26: helloworld.feature_platform_proxy.GetFeatures:output_type -> helloworld.GetFeaturesResponse
	26, // [26:27] is the sub-list for method output_type
	25, // [25:26] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_feature_platform_proxy_proto_init() }
func file_feature_platform_proxy_proto_init() {
	if File_feature_platform_proxy_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_feature_platform_proxy_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FloatList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Int64List); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringFloatMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Int64FloatMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringFloatListMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringInt64Map); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BytesList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Int32List); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Uint32List); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Uint64List); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoubleList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoolList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValueList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValueMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeaturesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeaturesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_platform_proxy_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringFloatListMap_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_feature_platform_proxy_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*Value_StringVal)(nil),
		(*Value_BoolVal)(nil),
		(*Value_FloatVal)(nil),
		(*Value_DoubleVal)(nil),
		(*Value_BytesVal)(nil),
		(*Value_Int32Val)(nil),
		(*Value_Uint32Val)(nil),
		(*Value_Int64Val)(nil),
		(*Value_Uint64Val)(nil),
		(*Value_StringListVal)(nil),
		(*Value_FloatListVal)(nil),
		(*Value_Int64ListVal)(nil),
		(*Value_StringFloatMapVal)(nil),
		(*Value_Int64FloatMapVal)(nil),
		(*Value_StringFloatListMapVal)(nil),
		(*Value_StringInt64MapVal)(nil),
		(*Value_BytesListVal)(nil),
		(*Value_Int32ListVal)(nil),
		(*Value_Uint32ListVal)(nil),
		(*Value_Uint64ListVal)(nil),
		(*Value_DoubleListVal)(nil),
		(*Value_BoolListVal)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_feature_platform_proxy_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_feature_platform_proxy_proto_goTypes,
		DependencyIndexes: file_feature_platform_proxy_proto_depIdxs,
		EnumInfos:         file_feature_platform_proxy_proto_enumTypes,
		MessageInfos:      file_feature_platform_proxy_proto_msgTypes,
	}.Build()
	File_feature_platform_proxy_proto = out.File
	file_feature_platform_proxy_proto_rawDesc = nil
	file_feature_platform_proxy_proto_goTypes = nil
	file_feature_platform_proxy_proto_depIdxs = nil
}
