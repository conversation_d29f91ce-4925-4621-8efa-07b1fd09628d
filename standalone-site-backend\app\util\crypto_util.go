package util

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"

	"trpc.publishing_application.standalonesite/app/config"
)

// Padding 函数用于对明文进行填充
func pad(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padtext...)
}

// Unpadding 函数用于去掉填充
func unpad(data []byte) ([]byte, error) {
	length := len(data)
	if length == 0 {
		return nil, fmt.Errorf("data length is zero")
	}
	unpadding := int(data[length-1])
	if unpadding > length {
		return nil, fmt.Errorf("unpadding size is invalid")
	}
	return data[:(length - unpadding)], nil
}

// encrypt 函数用于加密明文
func encrypt(plainText string) (string, error) {
	plainTextByte := []byte(plainText)
	block, err := aes.NewCipher([]byte(config.GetConfig().Crypto.Key))
	if err != nil {
		return "", err
	}

	// 使用固定的 IV
	iv := make([]byte, aes.BlockSize) // 全零IV
	stream := cipher.NewCBCEncrypter(block, iv)

	// 对明文进行填充
	paddedText := pad(plainTextByte, aes.BlockSize)

	// 加密
	cipherText := make([]byte, len(paddedText))
	stream.CryptBlocks(cipherText, paddedText)

	return hex.EncodeToString(cipherText), nil
}

// decrypt 函数用于解密密文
func decrypt(cipherTextHex string) (string, error) {
	cipherText, err := hex.DecodeString(cipherTextHex)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher([]byte(config.GetConfig().Crypto.Key))
	if err != nil {
		return "", err
	}

	// 使用固定的 IV
	iv := make([]byte, aes.BlockSize) // 全零IV
	stream := cipher.NewCBCDecrypter(block, iv)

	// 解密
	plainText := make([]byte, len(cipherText))
	stream.CryptBlocks(plainText, cipherText)

	// 去掉填充
	plainTextByte, err := unpad(plainText)
	if err != nil {
		return "", err
	}
	plainTextStr := string(plainTextByte)
	return plainTextStr, nil
}
func EncryptPageCursorI(cursor int64) (string, error) {
	cursorStr := strconv.FormatInt(cursor, 10)
	return encrypt(cursorStr)
}

// EncryptPageCursorCombination db游标组合索引生成
func EncryptPageCursorCombination(cursor string) (string, error) {
	return encrypt(cursor)
}

func EncryptPageCursorS(cursor string) (string, error) {
	return encrypt(cursor)
}

func DecryptPageCursorS(cursor string) (string, error) {
	if len(cursor) != 32 && len(cursor) != 64 && len(cursor) != 96 && len(cursor) != 128 {
		return "", fmt.Errorf("paging cursor is invalid")
	}
	cursorStr, err := decrypt(cursor)
	if err != nil {
		return "", err
	}
	return cursorStr, nil
}

func DecryptPageCursorI(cursor string) (int64, error) {
	if len(cursor) != 32 && len(cursor) != 64 && len(cursor) != 96 {
		return 0, fmt.Errorf("paging cursor is invalid")
	}
	cursorStr, err := decrypt(cursor)
	if err != nil {
		return 0, err
	}
	cursorI, err := strconv.ParseInt(cursorStr, 10, 64) // 基数为 10，位数为 64
	if err != nil {
		fmt.Println("DecryptPageCursorI Error converting string to int64:", err)
		return 0, err
	}
	return cursorI, nil
}

func DecryptPageCursorCombination(cursor string) ([]string, error) {
	var orderValues = make([]string, 0)
	if len(cursor) != 32 && len(cursor) != 64 && len(cursor) != 96 {
		return orderValues, fmt.Errorf("paging cursor is invalid")
	}
	cursorStr, err := decrypt(cursor)
	if err != nil {
		return orderValues, err
	}
	// 加密的时候必须要带上这个符号，所以解密的时候利用这个切割
	if strings.Contains(cursorStr, "-") {
		orderValues = strings.Split(cursorStr, "-")
	} else {
		orderValues = append(orderValues, cursorStr)
	}
	return orderValues, nil
}
