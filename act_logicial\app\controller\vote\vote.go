// Package vote 投票
package vote

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strconv"
	"time"

	"github.com/spf13/cast"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	configModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	utilModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go/log"

	// accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_vote"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	baseLogic "trpc.act.logicial/app/logic/base"
	generalLogic "trpc.act.logicial/app/logic/general"
	"trpc.act.logicial/app/model/base"
	baseTotalModel "trpc.act.logicial/app/model/base"
	generalModel "trpc.act.logicial/app/model/general"
)

// VoteServiceImpl 结构体
type VoteServiceImpl struct{}

// 页面任务
var storageKey = "vote"

// TagAmount 数量
type TagAmount struct {
	Tag    string  // 标记
	Amount float64 // 总积分
}

// VoteKey 缓存名
var VoteKey = "vote"

// AddUserVote 增加用户票数
func (s *VoteServiceImpl) AddUserVote(ctx context.Context, req *pb.AddUserVoteReq) (rsp *pb.AddUserVoteRsp, err error) {
	rsp = &pb.AddUserVoteRsp{}
	// metadata.SetUserAccount(ctx, accountPb.UserAccount{
	// 	Uid:         "29080-212554473218401583801",
	// 	AccountType: accountPb.AccountType_INTL,
	// 	IntlAccount: &accountPb.IntlAccount{
	// 		OpenId:    "16657983015453317121",
	// 		GameId:    "29080",
	// 		ChannelId: 6,
	// 		Token:     "f7e7475e8f3add68a447b5d4171888f481cac1fb",
	// 	},
	// })

	now := time.Now()
	_, err = metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	addData := baseTotalModel.AddParamStruct{
		FsourceID:  req.FsourceId,
		Type:       2,
		Tag:        req.TagId,
		StorageKey: storageKey,
		TotalLimit: req.TotalLimit,
		DayLimit:   req.DayLimit,
	}
	hasAdd, err := baseLogic.AddData(ctx, addData, req.TimeZone)
	if err != nil {
		return
	}
	rsp.VoteHasAdd = hasAdd
	// 日志
	log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", req.FsourceId).
		Infof("AddUserVote tiem : AddUserVote end time:[%v]", time.Since(now))
	log.WithFieldsContext(ctx, "log_type", "logicial_vote_add", "source_id", req.FsourceId, "tag_id", req.TagId).
		Infof("add vote")
	return
}

// UseVote Req 给某队伍投票
func (s *VoteServiceImpl) UseVote(ctx context.Context, req *pb.UseVoteReq) (rsp *pb.UseVoteRsp, err error) {
	rsp = &pb.UseVoteRsp{}
	// metadata.SetUserAccount(ctx, accountPb.UserAccount{
	// 	Uid:         "29080-212554473218401583801",
	// 	AccountType: accountPb.AccountType_INTL,
	// 	IntlAccount: &accountPb.IntlAccount{
	// 		OpenId:    "16657983015453317121",
	// 		GameId:    "29080",
	// 		ChannelId: 6,
	// 		Token:     "f7e7475e8f3add68a447b5d4171888f481cac1fb",
	// 	},
	// })
	// 初始化用户信息
	now := time.Now()
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	// 限制判断
	// 判断给某队伍投票是否已达总限
	tagId := cast.ToString(req.TagId)
	if req.TotalLimit > 0 || req.DayLimit > 0 {

		amount, errA := generalLogic.GetUserGeneralRecordValSum(ctx, userAccount, req.FsourceId, storageKey, tagId, "")
		if errA != nil {
			err = errA
			return
		}
		if amount >= int(req.TotalLimit) {
			// 超过总限
			err = errs.NewCustomError(ctx, code.VoteTotalLimit,
				"UseVoteReq total limit,tag=%v", req.TagId)
			return
		}
		log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", req.FsourceId).
			Infof("UseVote tiem : UseVote 01 time:[%v]", time.Since(now))
		// 判断给某队伍投票是否已达日限
		currentTime := time.Now()
		duration, _ := time.ParseDuration(fmt.Sprintf("%vh", req.TimeZone))
		currentTime = currentTime.Add(duration)
		startTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())

		dayAmount, errA := generalLogic.GetUserGeneralTimeRecordValSum(ctx, userAccount, req.FsourceId, storageKey, tagId,
			startTime, currentTime)
		if errA != nil {
			err = errA
			return
		}
		if dayAmount >= int(req.DayLimit) {
			// 超过日限
			err = errs.NewCustomError(ctx, code.VoteDayLimit,
				"UseVoteReq day limit tag=%v", req.TagId)
			return
		}
		log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", req.FsourceId).
			Infof("UseVote tiem : UseVote 02 time:[%v]", time.Since(now))
	}
	// 判断是否有票数
	var baseLog baseTotalModel.BaseTotalLogModel

	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLog.TableName())
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
		"storage_key":  storageKey,
		"status":       0,
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Limit(1).Where(condition).Where("total_num > ?",
		gorm.Expr("total_use_num "))
	if dbErr := db.First(&baseLog).Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			// 找不到数据
			err = errs.NewCustomError(ctx, code.VoteDelNumNotEnough, "vote num not enough")
		} else {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", dbErr)
		}
		return
	}
	log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", req.FsourceId).
		Infof("UseVote tiem : UseVote 03 time:[%v]", time.Since(now))
	if baseLog.TotalUseNum+int(req.VoteNum) > int(baseLog.TotalNum) {
		err = errs.NewCustomError(ctx, code.VoteDelNumNotEnough, "vote num not enough")
		return
	}
	// 扣除base表的票数
	updateData := map[string]interface{}{
		"total_use_num": gorm.Expr("total_use_num + ?", req.VoteNum),
		"today_use_num": gorm.Expr("today_use_num + ?", req.VoteNum),
	}
	if rowsAffected := db.Updates(updateData).RowsAffected; rowsAffected == 0 {
		err = errs.NewCustomError(ctx, code.VoteDelNumNotEnough, "vote num not enough")
		return
	}
	log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", req.FsourceId).
		Infof("UseVote tiem : UseVote 04 time:[%v]", time.Since(now))
	// 增加generl表中投票纪录 遍历添加多条
	err = generalLogic.DoCreateGeneralNums(ctx, userAccount, req.FsourceId, storageKey, tagId, "1", int(req.VoteNum))
	if err != nil {
		// 如添加失败，将base数据恢复
		updateData = map[string]interface{}{
			"total_use_num": gorm.Expr("total_use_num - ?", req.VoteNum),
			"today_use_num": gorm.Expr("today_use_num - ?", req.VoteNum),
		}
		db.Updates(updateData)
		return
	}
	// 判断是否更新缓存
	if req.DoUpdateCache {
		generalLogic.DoIncrCache(ctx, VoteKey, req.FsourceId, tagId, int64(req.VoteNum))
	}
	rsp.VoteHasDel = true
	// 日志
	log.WithFieldsContext(ctx, "log_type", "time_debug", "source_id", req.FsourceId).
		Infof("UseVote tiem : UseVote end time:[%v]", time.Since(now))
	log.WithFieldsContext(ctx, "log_type", "logicial_vote_del", "source_id", req.FsourceId, "tag_id", tagId).
		Infof("del vote")
	return
}

// UseVoteByLimitList 给某队伍投票通过限制列表
func (s *VoteServiceImpl) UseVoteByLimitList(ctx context.Context, req *pb.UseVoteByLimitListReq) (rsp *pb.UseVoteRsp,
	err error) {
	rsp = &pb.UseVoteRsp{}
	// metadata.SetUserAccount(ctx, accountPb.UserAccount{
	// 	Uid:         "29080-212554473218401583801",
	// 	AccountType: accountPb.AccountType_INTL,
	// 	IntlAccount: &accountPb.IntlAccount{
	// 		OpenId:    "16657983015453317121",
	// 		GameId:    "29080",
	// 		ChannelId: 6,
	// 		Token:     "f7e7475e8f3add68a447b5d4171888f481cac1fb",
	// 	},
	// })
	// 初始化用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	// 限制判断
	// 判断给某队伍投票是否已达总限
	amount, err := generalLogic.GetUserGeneralRecordValSum(ctx, userAccount, req.FsourceId, storageKey, req.TagId, "")
	if err != nil {
		return
	}

	for _, v := range req.TeamLimitList {
		if v.TagId == req.TagId && amount >= int(v.TotalLimit) && v.TotalLimit != 0 {
			// 超过总限
			err = errs.NewCustomError(ctx, code.VoteTotalLimit,
				"UseVoteReq total limit,tag=%v", req.TagId)
			return
		}
	}

	// 判断给某队伍投票是否已达日限
	currentTime := time.Now()
	duration, _ := time.ParseDuration(fmt.Sprintf("%vh", req.TimeZone))
	currentTime = currentTime.Add(duration)
	startTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())

	dayAmount, err := generalLogic.GetUserGeneralTimeRecordValSum(ctx, userAccount, req.FsourceId, storageKey, req.TagId,
		startTime, currentTime)

	for _, v := range req.TeamLimitList {
		if v.TagId == req.TagId && dayAmount >= int(v.DayLimit) && v.DayLimit != 0 {
			// 超过日限
			err = errs.NewCustomError(ctx, code.VoteDayLimit,
				"UseVoteReq day limit,tag=%v", req.TagId)
			return
		}
	}
	// 判断是否有票数
	var baseLog baseTotalModel.BaseTotalLogModel

	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLog.TableName())
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
		"storage_key":  storageKey,
		"status":       0,
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Limit(1).Where(condition).Where("total_num > ?",
		gorm.Expr("total_use_num "))
	if dbErr := db.First(&baseLog).Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			// 找不到数据
			err = errs.NewCustomError(ctx, code.VoteDelNumNotEnough, "vote num not enough")
		} else {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", dbErr)
		}
		return
	}
	if baseLog.TotalUseNum+int(req.VoteNum) > int(baseLog.TotalNum) {
		err = errs.NewCustomError(ctx, code.VoteDelNumNotEnough, "vote num not enough")
		return
	}
	// 扣除base表的票数
	updateData := map[string]interface{}{
		"total_use_num": gorm.Expr("total_use_num + ?", req.VoteNum),
		"today_use_num": gorm.Expr("today_use_num + ?", req.VoteNum),
	}
	if rowsAffected := db.Updates(updateData).RowsAffected; rowsAffected == 0 {
		err = errs.NewCustomError(ctx, code.VoteDelNumNotEnough, "vote num not enough")
		return
	}
	// 增加generl表中投票纪录 遍历添加多条
	err = generalLogic.DoCreateGeneralNums(ctx, userAccount, req.FsourceId, storageKey, req.TagId, "1", int(req.VoteNum))
	if err != nil {
		// 如添加失败，将base数据恢复
		updateData = map[string]interface{}{
			"total_use_num": gorm.Expr("total_use_num - ?", req.VoteNum),
			"today_use_num": gorm.Expr("today_use_num - ?", req.VoteNum),
		}
		db.Updates(updateData)
		return
	}
	// 判断是否更新缓存
	if req.DoUpdateCache {
		exist, err := generalLogic.DoIncrCache(ctx, VoteKey, req.FsourceId, req.TagId, int64(req.VoteNum))
		if !exist || err != nil {
			log.Error("[UseVoteByLimitList] redis service err: ", err)
		}
	}
	rsp.VoteHasDel = true
	// 日志
	log.WithFieldsContext(ctx, "log_type", "logicial_vote_del", "source_id", req.FsourceId, "tag_id", req.TagId).
		Infof("del vote")
	return
}

// GetTeamListVotes 获取队伍票数列表
func (s *VoteServiceImpl) GetTeamListVotes(ctx context.Context, req *pb.GetTeamListVotesReq) (
	rsp *pb.GetTeamListVotesRsp, err error) {

	rsp = &pb.GetTeamListVotesRsp{}
	if err != nil {
		return
	}
	// 从缓存中取值
	if !req.DoForceCache {
		resultMap, exist, redisErr := generalLogic.GetCacheMapAll(ctx, VoteKey, req.FsourceId)
		if exist && redisErr == nil {
			// 判断队伍是否发生变化
			var isSame = true
			for _, v := range req.TeamList {
				numStr, ok := resultMap[v.TagId]
				// 缓存中取不到对应的key 则更新缓存
				if !ok {
					isSame = false
					break
				}
				num, _ := strconv.ParseInt(numStr, 10, 32)
				team := &pb.TeamListVote{
					TagId: v.TagId,
					Num:   int32(num),
				}
				rsp.TeamList = append(rsp.TeamList, team)
			}
			// 如果缓存中没有则更新
			if isSame {
				return
			}
		}

	}
	// 获取队伍数量ID
	tableName, err := configModel.GetTabNameWithGeneral(ctx, (&generalModel.ConfigModel{}).TableName(), req.FsourceId,
		storageKey,
		(&generalModel.LogModel{}).TableName(), 100)
	if err != nil {
		return
	}
	LogCondition := map[string]interface{}{
		"Fsource_id":  req.FsourceId,
		"storage_key": storageKey,
	}
	var amountList []TagAmount
	// 如果是查询一条
	if queryError := DB.DefaultConnect().WithContext(ctx).Debug().Table(tableName).Where(LogCondition).
		Group("storage_tag").
		Order("amount desc").Select("count(1) as amount,storage_tag as tag").Scan(&amountList).
		Error; queryError != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	redisMap := make(map[string]interface{}, 0)
	for _, item := range req.TeamList {
		var amount float64
		for _, v := range amountList {
			if item.TagId == v.Tag {
				// 遍历写入redis 缓存
				amount = v.Amount
				break
			}
		}
		redisMap[item.TagId] = item.Num + int32(amount)
		team := &pb.TeamListVote{
			TagId: item.TagId,
			Num:   item.Num + int32(amount),
		}
		rsp.TeamList = append(rsp.TeamList, team)
	}
	generalLogic.SetRedisCache(ctx, VoteKey, req.FsourceId, redisMap, time.Duration(req.Second)*time.Second)

	return
}

// GetTeamVoteAmount 获取某队伍的投票数量
func (s *VoteServiceImpl) GetTeamVoteAmount(ctx context.Context, req *pb.GetTeamVoteAmountReq) (
	rsp *pb.GetTeamVoteAmountRsp, err error) {
	rsp = &pb.GetTeamVoteAmountRsp{}
	// 获取队伍数量ID
	tableName, err := configModel.GetTabNameWithSourceId(ctx, (&generalModel.ConfigModel{}).TableName(), req.FsourceId,
		(&generalModel.LogModel{}).TableName(), 100)
	if err != nil {
		return
	}
	LogCondition := map[string]interface{}{
		"Fsource_id":  req.FsourceId,
		"storage_key": storageKey,
		"storage_tag": req.TagId,
	}
	sum := make([]int32, 1)
	// 如果是查询一条
	if queryError := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(LogCondition).
		Pluck("count(1) as amount", &sum).Error; queryError != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}

	rsp.Num = sum[0]
	return
}

// GetVoteNum 获取投票数
func (s *VoteServiceImpl) GetVoteNum(ctx context.Context, req *pb.GetVoteNumReq) (
	rsp *pb.GetVoteNumRsp, err error) {
	// metadata.SetUserAccount(ctx, accountPb.UserAccount{
	// 	Uid:         "29080-212554473218401583801",
	// 	AccountType: accountPb.AccountType_INTL,
	// 	IntlAccount: &accountPb.IntlAccount{
	// 		OpenId:    "16657983015453317121",
	// 		GameId:    "29080",
	// 		ChannelId: 6,
	// 		Token:     "f7e7475e8f3add68a447b5d4171888f481cac1fb",
	// 	},
	// })
	rsp = &pb.GetVoteNumRsp{}
	// 初始化用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var baseLog base.BaseTotalLogModel
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLog.TableName())
	if err != nil {
		return
	}
	uid := userAccount.Uid
	FsourceID := req.FsourceId
	tagId := req.TagId
	duration, _ := time.ParseDuration(fmt.Sprintf("%vh", req.TimeZone))
	todayDateStr := time.Now().Add(duration).Format("2006-01-02")
	accountType := userAccount.AccountType

	condition := map[string]interface{}{
		"uid":          uid,
		"account_type": accountType,
		"Fsource_id":   FsourceID,
		"storage_key":  storageKey,
		"status":       0,
	}

	db := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition)

	if req.IsOnlyToday {
		db = db.Where("Fday = ?", todayDateStr)

		if req.IsOnlyLeft {
			db = db.Select("sum(today_num - today_use_num) as today_left_num")
		} else {
			db = db.Select("sum(today_num) as today_left_num")
		}
	} else {

		if req.IsOnlyLeft {
			db = db.Select("sum(total_num - total_use_num) as total_left_num")
		} else {
			db = db.Select("sum(total_num) as total_left_num")
		}
	}
	if len(tagId) > 0 {
		db = db.Where("storage_tag = ?", req.TagId)
	}
	var data struct {
		TotalLeftNum int `gorm:"column:total_left_num;not null"`
		TodayLeftNum int `gorm:"column:today_left_num;not null"`
	}

	if dbErr := db.Scan(&data).Error; err != nil {
		// 数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr)
		return
	}

	toatalLeft, _ := strconv.ParseFloat(strconv.Itoa(data.TotalLeftNum), 64)
	todayLeft, _ := strconv.ParseFloat(strconv.Itoa(data.TodayLeftNum), 64)

	LeftNum := math.Max(0, toatalLeft)
	if req.IsOnlyToday {
		LeftNum = math.Max(0, todayLeft)
	}
	rsp.VoteNum = int32(LeftNum)
	return
}

// GetUserVoteLog 获取用户投票记录
func (s *VoteServiceImpl) GetUserVoteLog(ctx context.Context, req *pb.GetUserVoteLogReq) (
	rsp *pb.GetUserVoteLogRsp, err error) {
	rsp = &pb.GetUserVoteLogRsp{}

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	dataList, err := generalLogic.GetUserGeneralRecord(ctx, userAccount, req.FsourceId, storageKey)
	if err != nil {
		return
	}
	for _, v := range dataList {
		num, _ := strconv.Atoi(v.StorageVal)
		tagId, err := cast.ToInt32E(v.StorageTag)
		if err != nil {
			errs.NewCustomError(ctx, code.CommonNotMathchStr, "GetUserVoteLog ToInt32E err,StorageTag:[%v]", v.StorageTag)
			continue
		}
		item := &pb.UserVoteLog{
			TagId:     tagId,
			Num:       int32(num),
			Timestamp: v.CreatedAt,
		}
		rsp.VoteLogList = append(rsp.VoteLogList, item)
	}
	return
}
