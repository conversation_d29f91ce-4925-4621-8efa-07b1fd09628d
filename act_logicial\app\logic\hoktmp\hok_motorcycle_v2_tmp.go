package hoktmp

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/object"
	"git.code.oa.com/trpc-go/trpc-go/log"
	amsPb "git.code.oa.com/trpcprotocol/publishing_marketing/game_ams"
	"git.woa.com/gpts/baselib/crypto"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_hok_tmp"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"strconv"
	"time"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/global"
	"trpc.act.logicial/app/model/hok"
)

// HOKMotorcycleLuckyNumberCount 用户幸运号码数量
func HOKMotorcycleLuckyNumberCount(ctx context.Context) (int64, error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return 0, err
	}
	tableName, err := model.GetTableNameWithAccount(ctx, &userAccount, hok.UserLuckyNumRecordModel{}.TableName())
	if err != nil {
		return 0, err
	}
	var count int64
	if err = DB.DefaultConnect().Table(tableName).Where(
		"uid = ?", userAccount.Uid).Count(&count).Error; err != nil {
		return 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return count, nil
}

// HOKMotorcycleLuckyNumberUsedCount 已使用幸运号码数量
func HOKMotorcycleLuckyNumberUsedCount(ctx context.Context) (int64, error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return 0, err
	}
	tableName, err := model.GetTableNameWithAccount(ctx, &userAccount, hok.ActivityPrizeRedemptionRecordsModel{}.TableName())
	if err != nil {
		return 0, err
	}
	var totalExpendNum sql.NullInt64
	if err = DB.DefaultConnect().Table(tableName).Select("SUM(expend_num)").Where(
		"uid = ? AND deleted_at = 0", userAccount.Uid).Scan(&totalExpendNum).Error; err != nil {
		return 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	if !totalExpendNum.Valid {
		totalExpendNum.Int64 = 0
	}
	return totalExpendNum.Int64, nil
}

type CommodityRedemption struct {
	CommodityID uint32 `json:"commodity_id"`
	Count       int64  `json:"count"`
	Owned       bool
}

// GetRedeemedCommodityQuantities 商品已兑换数量
func GetRedeemedCommodityQuantities(ctx context.Context, commodityId int32) ([]CommodityRedemption, error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	tableName, err := model.GetTableNameWithAccount(ctx, &userAccount, hok.ActivityPrizeRedemptionRecordsModel{}.TableName())
	if err != nil {
		return nil, err
	}
	var results []CommodityRedemption
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Select("commodity_id, COUNT(*) as count").
		Where("uid = ? AND deleted_at = 0", userAccount.Uid)
	if commodityId != 0 {
		db = db.Where("commodity_id = ?", commodityId)
	}
	err = db.Group("commodity_id").Scan(&results).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	// 查询皮肤是否已拥有
	skinAlreadyOwnedByUser, err := IsHeroSkinAlreadyOwnedByUser(ctx)
	if err != nil {
		return nil, errs.NewCustomError(ctx, code.HokTmpCheckAlreadyOwnedError, "IsHeroSkinAlreadyOwnedByUser err")
	}
	// 查询表情是否已拥有
	emoteAlreadyOwnedByUser, err := IsEmoteAlreadyOwnedByUser(ctx)
	if err != nil {
		return nil, errs.NewCustomError(ctx, code.HokTmpCheckAlreadyOwnedError, "IsEmoteAlreadyOwnedByUser err")
	}
	log.WithFieldsContext(ctx, "log_type", "hok_Owned_list", "str_field_1", userAccount.Uid).
		Infof("GetRedeemedCommodityQuantities Owned skinAlreadyOwnedByUser: %v emoteAlreadyOwnedByUser: %v",
			skinAlreadyOwnedByUser, emoteAlreadyOwnedByUser)
	// 获取皮肤表情和商品id的映射
	skinEmoteCommodityIdMap := config.GetConfig().HOKMotorcycleEvents.SkinEmoteCommodityIdMap
	commodityUserHasMap := make(map[int32]bool)
	for idipId, v := range skinEmoteCommodityIdMap {
		idipIdStr := cast.ToString(idipId)
		skinExists, _ := object.InArray(idipIdStr, skinAlreadyOwnedByUser)
		emoteExists, _ := object.InArray(idipIdStr, emoteAlreadyOwnedByUser)
		if skinExists || emoteExists {
			commodityUserHasMap[v] = true
		} else {
			commodityUserHasMap[v] = false
		}
		var exist bool
		for idx, commodityRedemptionItem := range results {
			if v == int32(commodityRedemptionItem.CommodityID) {
				exist = true
				results[idx].Owned = commodityUserHasMap[v]
				break
			}
		}
		if !exist {
			results = append(results, CommodityRedemption{
				CommodityID: uint32(v),
				Count:       0,
				Owned:       commodityUserHasMap[v],
			})
		}
	}
	// 补全商品
	motorcycleEventsV2Map := config.GetConfig().HOKMotorcycleEvents.V2ProductItemMap
	for indexId := range motorcycleEventsV2Map {
		var ok bool
		for _, v := range results {
			if indexId == int32(v.CommodityID) {
				ok = true
				break
			}
		}
		if !ok {
			results = append(results, CommodityRedemption{
				CommodityID: uint32(indexId),
				Count:       0,
				Owned:       false,
			})
		}
	}
	return results, nil
}

// CommodityExchange 商品兑换
func CommodityExchange(ctx context.Context, req *pb.CommodityExchangeReq) (string, error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return "", err
	}
	// 未获取到锁报错
	lockKey := global.GetRedisKey(fmt.Sprintf("motorcycle_v2_commodity_exchange_lock_%s", userAccount.Uid))
	locked, err := redis.GetClient().SetNX(ctx, lockKey, "locked", 20*time.Second).Result()
	if err != nil {
		return "", err
	}
	if !locked {
		return "", code.ErrRequestFrequencyExceededLimitError
	}
	defer redis.GetClient().Del(ctx, lockKey)

	// 获取商品配置
	motorcycleEventsV2Map := config.GetConfig().HOKMotorcycleEvents.V2ProductItemMap
	var commodityItem config.MotorcycleProductItem
	var exist bool
	if commodityItem, exist = motorcycleEventsV2Map[req.GetIndexId()]; !exist {
		return "", errs.NewCustomError(ctx, code.HokTmpCommodityNotExist, "The current Commodity does not exist")
	}
	// 获取已兑换商品数量
	quantities, err := GetRedeemedCommodityQuantities(ctx, req.GetIndexId())
	if err != nil {
		return "", err
	}
	if len(quantities) != 0 && quantities[0].Count >= int64(commodityItem.ExchangeLimit) {
		log.WithFieldsContext(ctx, "log_type", "exchange_limit_err", "m", "CommodityExchange").
			Infof("CommodityExchange Exchange limit err; CommodityId:[%v] quantities: %v", req.GetIndexId(), quantities)
		return "", errs.NewCustomError(ctx, code.HokTmpExchangeLimitErr, "Over the exchange limit")
	}
	luckyNumberCount, err := HOKMotorcycleLuckyNumberCount(ctx)
	if err != nil {
		return "", errs.NewCustomError(ctx, code.HokTmpCommodityExchangeErr, "Abnormal exchange of goods")
	}
	usedCount, err := HOKMotorcycleLuckyNumberUsedCount(ctx)
	if err != nil {
		return "", errs.NewCustomError(ctx, code.HokTmpCommodityExchangeErr, "Abnormal exchange of goods")
	}
	// 幸运号码数量是否满足
	if luckyNumberCount-usedCount < int64(commodityItem.ExpendNum) {
		return "", errs.NewCustomError(ctx, code.HokTmpAvailableQuantityInsufficient,
			"The number of lucky numbers available is insufficient")
	}
	// 记录兑换的商品
	tableName, _ := model.GetTableNameWithAccount(ctx, &userAccount, hok.ActivityPrizeRedemptionRecordsModel{}.TableName())
	redemptionRecords := hok.ActivityPrizeRedemptionRecords{
		UID:         userAccount.Uid,
		AccountType: int16(userAccount.AccountType),
		CommodityID: uint32(req.GetIndexId()),
		ExpendNum:   uint32(commodityItem.ExpendNum),
	}
	if err = DB.DefaultConnect().Table(tableName).Create(&redemptionRecords).Error; err != nil {
		_ = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr, "CommodityExchange db error, \t [Error]:{%v} ", err)
		return "", errs.NewCustomError(ctx, code.HokTmpCommodityExchangeErr, "Abnormal exchange of goods")
	}
	if redemptionRecords.ID == 0 {
		return "", errs.NewCustomError(ctx, code.HokTmpCommodityExchangeErr, "Record the goods exchanged err")
	}
	// 返回加密id
	aesUtil := crypto.NewAesCrypto()
	aesSecret := config.GetConfig().Crypto
	idStr := cast.ToString(redemptionRecords.ID)
	encryptId, _ := aesUtil.EncryptBase64([]byte(idStr), []byte(aesSecret))
	return string(encryptId), nil
}

// CommodityExchangeFailRollback 失败回滚
func CommodityExchangeFailRollback(ctx context.Context, encryptId string) error {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}

	aesUtil := crypto.NewAesCrypto()
	aesSecret := config.GetConfig().Crypto
	idStr, err := aesUtil.DecryptBase64([]byte(encryptId), []byte(aesSecret))
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "rollback_err", "m", "CommodityExchangeFailRollback").
			Infof("CommodityExchangeFailRollback DecryptBase64; encryptId:[%v] err: %v", encryptId, err.Error())
		return errs.NewCustomError(ctx, code.HokTmpDataRollbackFailed, "Rollback Failed")
	}
	id, err := cast.ToUint32E(string(idStr))
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "rollback_err", "m", "CommodityExchangeFailRollback").
			Infof("CommodityExchangeFailRollback DecryptBase64; encryptId:[%v],idStr[%v], err: %v",
				encryptId, idStr, err.Error())
		return errs.NewCustomError(ctx, code.HokTmpDataRollbackFailed, "Parameter format exception")
	}
	// 删除数据
	tableName, _ := model.GetTableNameWithAccount(ctx, &userAccount, hok.ActivityPrizeRedemptionRecordsModel{}.TableName())
	if err = DB.DefaultConnect().WithContext(ctx).Debug().Table(tableName).Where("id = ?", id).
		Delete(&hok.ActivityPrizeRedemptionRecords{}).Error; err != nil {
		_ = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"CommodityExchangeFailRollback Delete db error, \t [Error]:{%v} ", err)
		return errs.NewCustomError(ctx, code.HokTmpDataRollbackFailed, "Delete data exception")
	}
	return nil
}

// IsHeroSkinAlreadyOwnedByUser 检查皮肤道具用户是否已拥有
func IsHeroSkinAlreadyOwnedByUser(ctx context.Context) ([]string, error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}

	cmdId := config.GetConfig().HOKMotorcycleEvents.HeroSkinCmdId
	amsProxy := amsPb.NewAmsClientProxy()
	idipParam := make([]*amsPb.IdipGetItem, 0)
	amsInfo, err := amsProxy.GetInfoListByAms(ctx, &amsPb.GetInfoListByAmsReq{
		SelectParam: &amsPb.IdipDBParam{
			CmdId:  cast.ToString(cmdId),
			GameId: HOKGameId,
		},
		IdipParam: idipParam,
	})
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "IsHeroSkinAlreadyOwnedByUser_err", "str_field_1", userAccount.Uid).
			Infof("IsHeroSkinAlreadyOwnedByUser err: %v", err)
		return nil, err
	}
	var hasSkinBitsItemList []string
	var hasHeroItemList []string
	var hasItemList []string
	for _, item := range amsInfo.Info {
		if item.Key == "skinBits" {
			hasSkinBitsItemList = item.List
		}
		if item.Key == "heroId" {
			hasHeroItemList = item.List
		}
	}
	skinBitsIdList := []int32{17503, 15403}
	for idx, v := range hasHeroItemList {
		for _, skinBitsId := range skinBitsIdList {
			firstNDigits := getFirstNDigits(int(skinBitsId), 3)
			// 英雄前缀匹配
			if v == firstNDigits {
				// 检查当前英雄对应皮肤是否存在
				if idx >= len(hasSkinBitsItemList) {
					continue
				}
				s := hasSkinBitsItemList[idx]
				// 判断第四个皮肤是否存在
				nthBitFromRight, err := getNthBitFromRight(s, 4)
				if err != nil {
					continue
				}
				if nthBitFromRight == 1 {
					hasItemList = append(hasItemList, cast.ToString(skinBitsId))
				}
			}
		}
	}

	log.WithFieldsContext(ctx, "log_type", "hok_has_item_list", "str_field_1", userAccount.Uid).
		Infof("IsHeroSkinAlreadyOwnedByUser cmdId:[%v] hasItemList: %v amsInfo: %v", cmdId, hasItemList, amsInfo)
	return hasItemList, nil
}

// IsEmoteAlreadyOwnedByUser 检查表情道具用户是否已拥有
func IsEmoteAlreadyOwnedByUser(ctx context.Context) ([]string, error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}

	cmdId := config.GetConfig().HOKMotorcycleEvents.EmoteCmdId
	amsProxy := amsPb.NewAmsClientProxy()
	idipParam := make([]*amsPb.IdipGetItem, 0)
	idipParam = append(idipParam, &amsPb.IdipGetItem{
		Key:   "ResourceType",
		Value: "46",
	})
	amsInfo, err := amsProxy.GetInfoListByAms(ctx, &amsPb.GetInfoListByAmsReq{
		SelectParam: &amsPb.IdipDBParam{
			GameId: HOKGameId,
			CmdId:  cast.ToString(cmdId),
		},
		IdipParam: idipParam,
	})
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "IsEmoteAlreadyOwnedByUser_err", "str_field_1", userAccount.Uid).
			Infof("IsEmoteAlreadyOwnedByUser err: %v", err)
		return nil, err
	}
	var hasItemList []string
	for _, item := range amsInfo.Info {
		if item.Key != "ResourceID" {
			continue
		}
		hasItemList = item.List
	}
	log.WithFieldsContext(ctx, "log_type", "hok_has_item_list", "str_field_1", userAccount.Uid).
		Infof("IsEmoteAlreadyOwnedByUser; cmdId:[%v] hasItemList: %v amsInfo: %v", cmdId, hasItemList, amsInfo)
	return hasItemList, nil
}

func getFirstNDigits(number int, n int) string {

	numberStr := strconv.Itoa(number)
	if n > len(numberStr) {
		n = len(numberStr)
	}
	// 截取前 n 位数
	resultStr := numberStr[:n]
	return resultStr
}

// getNthBitFromRight 返回字符串 s 转换为二进制后，从右往左数第 n 位的数字
func getNthBitFromRight(s string, n int) (int, error) {

	number, err := strconv.Atoi(s)
	if err != nil {
		return 0, err
	}
	binaryStr := strconv.FormatInt(int64(number), 2)
	// 检查 n 是否在有效范围内
	if n <= 0 || n > len(binaryStr) {
		return 0, fmt.Errorf("n is out of range")
	}
	// 从右往左数第 n 位的索引
	index := len(binaryStr) - n
	// 获取第 n 位的字符并转换为整数
	bit := binaryStr[index] - '0'
	return int(bit), nil
}
