package dao

import (
	"fmt"
	"strings"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

type FetchPostExternalOrder struct {
	ColumnStr string `json:"column"`
	OrderKey  string `json:"order_key"`
}

type FetchPostExternalCondition struct {
	PlateId           int64                     `json:"plate_id"`            // 板块id
	Order             []*FetchPostExternalOrder `json:"order"`               // 排序
	InterOpenid       string                    `json:"inter_openid"`        // 用户id
	IdCursor          int64                     `json:"id_cursor"`           // id 游标
	Language          string                    `json:"language"`            // 语言
	SocialmediaPostId []string                  `json:"socialmedia_post_id"` //社媒的资讯id
	PostUuids         []string                  `json:"post_uuids"`          // 动态的唯一id
	TaskId            string                    `json:"task_id"`             // 原帖子活动id
	RankId            int32                     `json:"rank_id"`             // 赛道id
	TaskIds           []string                  `json:"task_ids"`            // 原帖子活动id
	Platform          string                    `json:"platform"`            //社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
}

// CreatePostExternal 为了可以指定created_on字段
func CreatePostExternal(post *model.PostExternalFormat) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostExternal{}).TableName()).Create(&post).Error
}

func UpdatePostExternal(post *model.PostExternal) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostExternal{}).TableName()).Where("id = ? AND is_del = ?", post.Model.ID, 0).Save(post).Error
}

func UpdatePostExternalData(postUuid string, data map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostExternal{}).TableName()).Where("post_uuid = ? AND is_del = ?", postUuid, 0).Updates(data).Error
}

func GetPostExternalListBySocialMediaId(socialMediaPostIds []string, plateId int64) ([]*model.PostExternal, error) {
	var postList []*model.PostExternal
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostExternal{}).TableName()).Unscoped().Where("socialmedia_post_id in ? and plate_id = ?", socialMediaPostIds, plateId).Find(&postList).Error
	if err != nil {
		return nil, err
	}
	return postList, nil
}

// 删除动态
func DeletePostExternalByTaskId(taskIds []string) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		err := tx.Table((&model.PostExternal{}).TableName()).Where("task_id in ?", taskIds).Updates(map[string]interface{}{
			"deleted_on": time.Now().Unix(),
			"is_del":     1,
		}).Error
		return err
	})
}

// 根据动态uuid重置删除状态
func ResetPostExternalDeleteByTaskIds(taskIds []string) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		err := tx.Table((&model.PostExternal{}).TableName()).Where("task_ids in ?", taskIds).Updates(map[string]interface{}{
			"deleted_on": 0,
			"is_del":     0,
		}).Error
		return err
	})
}

func FetchPostExternal(condition *FetchPostExternalCondition, limit int) ([]*model.PostExternal, error) {
	var posts []*model.PostExternal
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.PostExternal{}).TableName())
	if limit > 0 {
		db = db.Limit(limit)
	}
	if condition.Language != "" {
		db.Where("language = ?", condition.Language)
	}
	if condition.IdCursor > 0 {
		db.Where("created_on_ms < ?", condition.IdCursor)
	}
	if condition.InterOpenid != "" {
		db.Where("is_audit = 1 or (is_audit = 2 and intl_openid = ?)", condition.InterOpenid)
	} else {
		db.Where("is_audit = 1")
	}
	if len(condition.SocialmediaPostId) > 0 {
		db.Where("socialmedia_post_id in ?", condition.SocialmediaPostId)
	}
	if len(condition.PostUuids) > 0 {
		db.Where("post_uuid in ?", condition.PostUuids)
	}
	if condition.TaskId != "" {
		db.Where("task_id = ?", condition.TaskId)
	}
	if len(condition.TaskIds) > 0 {
		db.Where("task_id in ?", condition.TaskIds)
	}
	if condition.RankId > 0 {
		db.Where("rank_id = ?", condition.RankId)
	}
	if condition.Platform != "" {
		db.Where("platform = ?", condition.Platform)
	}
	if len(condition.Order) > 0 {
		var orderStr []string
		for _, order := range condition.Order {
			orderStrTemp := fmt.Sprintf("%s %s", order.ColumnStr, order.OrderKey)
			orderStr = append(orderStr, orderStrTemp)
		}
		if len(orderStr) > 0 {
			db.Order(fmt.Sprintf("%s", strings.Join(orderStr, ",")))
		}
	}

	if err = db.Where("plate_id = ?", condition.PlateId).Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}

func GetPostExternalAllDataBySync(limit int, gtId int64) ([]*model.PostExternal, error) {
	var posts []*model.PostExternal
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.PostExternal{}).TableName())
	if gtId > 0 {
		db.Where("id > ?", gtId)
	}
	if err = db.Order("id asc").Limit(limit).Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}
