package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"time"
	"trpc.publishing_application.standalonesite/app/model"
)

type AuditPermissionConditions struct {
	ValidOn    int64
	Type       int64
	Status     int64
	GameId     string
	AreaId     string
	IntlOpenid string
	Order      []*OrderConditions
}

func AuditPermissionCreate(permission *model.AuditPermission) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.AuditPermission{}).TableName()).Create(&permission).Error

	return err
}

func AuditPermissionDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.AuditPermission{}).TableName()).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func AuditPermissionGet(id int64) (*model.AuditPermission, error) {
	var post model.AuditPermission
	db := DB.SelectConnect("db_standalonesite").Table((&model.AuditPermission{}).TableName())
	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	} else {
		return nil, gorm.ErrRecordNotFound
	}
	err := db.First(&post).Error
	if err != nil {
		return &post, err
	}

	return &post, nil
}

func AuditPermissionList(conditions *AuditPermissionConditions, offset, limit int) ([]*model.AuditPermission, error) {
	var posts []*model.AuditPermission
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.AuditPermission{}).TableName())
	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	if conditions.AreaId != "" {
		db = db.Where("area_id = ?", conditions.AreaId)
	}
	if conditions.GameId != "" {
		db = db.Where("game_id = ?", conditions.GameId)
	}
	if conditions.ValidOn > 0 {
		db = db.Where("valid_on > ?", conditions.ValidOn)
	}
	if conditions.Type > 0 {
		db = db.Where("type = ?", conditions.Type)
	}
	if conditions.Status > 0 {
		db = db.Where("statud = ?", conditions.Status)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}

	if err = db.Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}

func AuditPermissionUpdate(permission *model.AuditPermission) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.AuditPermission{}).TableName()).Where("id = ? AND is_del = ?", permission.ID, 0).Save(permission).Error
}
