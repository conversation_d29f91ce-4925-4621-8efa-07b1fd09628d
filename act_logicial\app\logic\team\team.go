// Package team TODO
package team

import (
	"context"
	"fmt"
	"strings"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/datadump"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	configModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/publishing_marketing/account"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	accountTeam "git.woa.com/trpcprotocol/publishing_marketing/account_team"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_team"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	model "trpc.act.logicial/app/model/team"
	"trpc.act.logicial/app/util"
)

// OpenInfo TODO
type OpenInfo struct {
	Uid         string
	AccountType int32
}

// CreateTeam 创建队伍
func CreateTeam(ctx context.Context, FsourceId string) (teamCode string, err error) {
	// 查询当前用户是否在队伍中了
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	tableName, err := GetTeamTableName(ctx, FsourceId)
	if err != nil {
		return
	}
	var teamAccountCode *model.TeamAccountCode
	selectdb := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where("Fsource_id = ? and is_delete = 0",
		FsourceId).First(&teamAccountCode)
	if selectdb.Error != nil && selectdb.Error != gorm.ErrRecordNotFound {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", selectdb.Error)
		return
	}

	if selectdb.Error != gorm.ErrRecordNotFound {
		if teamAccountCode.Uid == userAccount.Uid {
			teamCode = teamAccountCode.TeamCode
			return
		}
	}

	teamCode, err = util.GetShortShareCode(userAccount.Uid, FsourceId)
	if err != nil {
		return
	}

	// 判断邀请码是否重复，重试十次次
	for i := 0; i < 20; i++ {
		var total int64
		db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where("team_code = ? and Fsource_id = ?", teamCode,
			FsourceId).Count(&total)
		if db.Error != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error)
			return
		}
		if total == 0 {
			break
		}
		if i == 9 && total > 0 {
			err = errs.NewCustomError(ctx, code.GenerateTeamCodeFail, "generate teamCode fail")
			return
		}
		teamCode, err = util.GetShortShareCode(fmt.Sprintf("%v-retry%v", userAccount.Uid, i), FsourceId)
		if err != nil {
			return
		}
	}
	// 插入数据库
	teamCodeData := &model.TeamAccountCode{
		Uid:         userAccount.Uid,
		AccountType: int(userAccount.AccountType),
		FsourceId:   FsourceId,
		TeamCode:    teamCode,
	}
	where := &model.TeamAccountCode{
		FsourceId: FsourceId,
		TeamCode:  teamCode,
		IsDelete:  0,
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(&where).FirstOrCreate(&teamCodeData)
	err = db.Error
	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}

	return
}

// LeaveTeam 退出队伍
func LeaveTeam(ctx context.Context, teamCode string, FsourceId string, isDisband int32) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	tableName, err := GetTeamLogTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	var teamLogData *model.TeamCodeLog
	selectdb := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(
		"invitee_uid = ? and Fsource_id = ? and invitee_account_type = ? and team_code = ? and is_delete = 0",
		userAccount.Uid, FsourceId, userAccount.AccountType, teamCode).First(&teamLogData)
	if selectdb.Error != nil && selectdb.Error != gorm.ErrRecordNotFound {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", selectdb.Error)
		return
	}
	if selectdb.Error == gorm.ErrRecordNotFound {
		// 当前用户不在队伍中
		err = errs.NewCustomError(ctx, code.NoJoinTeam, "no join current team")
		return
	}

	// 有数据
	teamLogData.IsDelete = 1
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Save(&teamLogData)
	if db.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	//  增加队长转移逻辑
	peopleList := make([]*model.TeamCodeLog, 0)
	db = DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(
		"Fsource_id = ? and team_code = ? and is_delete = 0 and invitee_uid != ?",
		FsourceId, teamCode, userAccount.Uid).Find(&peopleList)
	if db.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	if len(peopleList) > 0 {
		codeTableName, err := GetTeamTableName(ctx, FsourceId)
		if err != nil {
			return err
		}
		uid := peopleList[0].InviteeUid
		db := DB.DefaultConnect().WithContext(ctx).Table(codeTableName).Where(
			"Fsource_id = ? and team_code = ? and is_delete = 0",
			FsourceId, teamCode).Update("uid", uid)
		if db.Error != nil {
			// 报错,数据库操作异常
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error)
			return err
		}
	}
	// 在队伍剩下最后一个人的时候退出队伍就解散队伍
	if isDisband == 1 {
		var teamPeopleNum int64
		db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(
			"Fsource_id = ? and team_code = ? and is_delete = 0",
			FsourceId, teamCode).Count(&teamPeopleNum)
		if db.Error != nil {
			// 报错,数据库操作异常
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error)
			return
		}
		if teamPeopleNum == 0 {
			teamTableName, errT := GetTeamTableName(ctx, FsourceId)
			if errT != nil {
				return errT
			}
			db := DB.DefaultConnect().WithContext(ctx).Table(teamTableName).Where(
				"Fsource_id = ? and team_code = ? and is_delete = 0",
				FsourceId, teamCode).Update("is_delete", "1")
			if db.Error != nil {
				// 报错,数据库操作异常
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", db.Error)
				return
			}
		}
	}
	return
}

// JoinTeam 加入队伍
func JoinTeam(ctx context.Context, teamCode string, FsourceId string) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	codeTableName, err := GetTeamTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	var teamCodeInfoCount int64
	selectdb := DB.DefaultConnect().WithContext(ctx).Table(codeTableName).Where(
		"Fsource_id = ? and is_delete = 0 and team_code = ?",
		FsourceId, teamCode).Count(&teamCodeInfoCount)
	if selectdb.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", selectdb.Error)
		return
	}

	if teamCodeInfoCount == 0 {
		err = errs.NewCustomError(ctx, code.TeamCodeIsNotExit, "team code is not exit")
		return
	}

	tableName, err := GetTeamLogTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	var teamLogData *model.TeamCodeLog
	selectdb = DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(
		"invitee_uid = ? and Fsource_id = ? and invitee_account_type = ? and team_code = ?",
		userAccount.Uid, FsourceId, userAccount.AccountType, teamCode).First(&teamLogData)
	if selectdb.Error != nil && selectdb.Error != gorm.ErrRecordNotFound {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", selectdb.Error)
		return
	}
	teamLogData.FsourceId = FsourceId
	teamLogData.InviteeUid = userAccount.Uid
	teamLogData.InviteeAccountType = int(userAccount.AccountType)
	teamLogData.TeamCode = teamCode
	teamLogData.IsDelete = 0

	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(
		"invitee_uid = ? and Fsource_id = ? and invitee_account_type = ? and team_code = ?",
		userAccount.Uid, FsourceId, userAccount.AccountType, teamCode).Save(&teamLogData)
	if db.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	return
}

// GetCurrentTeamNum 获取当前队伍人数
func GetCurrentTeamNum(ctx context.Context, teamCode string, FsourceId string) (teamNum int64, err error) {
	tableName, err := GetTeamLogTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	var teamCurrentNum int64
	selectdb := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(
		"Fsource_id = ? and team_code = ? and is_delete = 0",
		FsourceId, teamCode).Count(&teamCurrentNum)
	if selectdb.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", selectdb.Error)
		return
	}
	teamNum = teamCurrentNum
	return
}

// GetTeamUserInfoList 获取组队信息（通过teamCode）
func GetTeamUserInfoList(ctx context.Context, FsourceId string, openidList []*OpenInfo) (
	userInfoList []*accountTeam.UserInfo, err error,
) {
	userInfoList = make([]*accountTeam.UserInfo, 0)
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         userAccount.Uid,
		AccountType: accountPb.AccountType(1),
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    strings.Split(userAccount.Uid, "-")[1],
			ChannelId: userAccount.IntlAccount.ChannelId,
			GameId:    strings.Split(userAccount.Uid, "-")[0],
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	accountTeamProxy := accountTeam.NewTeamClientProxy()
	users := make([]*account.UserAccount, 0)
	for _, item := range openidList {
		if len(item.Uid) > 0 {
			users = append(users, &account.UserAccount{
				Uid:         item.Uid,
				AccountType: account.AccountType(item.AccountType),
			})
		}
	}
	accountTeamRsp, errG := accountTeamProxy.GetTeamInfoList(ctx, &accountTeam.GetTeamInfoListReq{
		Users:     users,
		FsourceId: FsourceId,
		GameId:    strings.Split(userAccount.Uid, "-")[0],
	}, callopts...)
	if errG != nil {
		err = errG
		return
	}
	userInfoList = accountTeamRsp.Users
	return
}

// GetTeamUserInvitedList 获取队伍邀请人数列表
func GetTeamUserInvitedList(ctx context.Context, FsourceId string, openidList []*OpenInfo) (
	userInfoList []*pb.UserInfo, err error,
) {
	userInfoList = make([]*pb.UserInfo, 0)

	for _, item := range openidList {
		// 查询邀请人数
		tableName, err := util.GetShareTableName(ctx, FsourceId)
		if err != nil {
			return userInfoList, err
		}

		condition := map[string]interface{}{
			"uid":          item.Uid,
			"account_type": item.AccountType,
			"Fsource_id":   FsourceId,
			"status":       1,
			"is_delete":    0,
		}
		var shareNum int64
		err = DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition).Count(&shareNum).Error
		if err != nil {
			return userInfoList, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
		}
		userInfoList = append(userInfoList, &pb.UserInfo{
			InvitedNum: shareNum,
		})
	}
	return
}

// GetTeamOpenidList 通过队伍码获取队伍成员openid
func GetTeamOpenidList(ctx context.Context, FsourceId string, teamCode string) (
	openids []string, err error,
) {
	openids = make([]string, 0)
	tableName, err := GetTeamLogTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	teamLogDatas := make([]*model.TeamCodeLog, 0)
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where(" Fsource_id = ? and team_code = ? and is_delete = 0",
			FsourceId, teamCode).
		Order("id asc").
		Find(&teamLogDatas)
	if db.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	for _, item := range teamLogDatas {
		openids = append(openids, strings.Split(item.InviteeUid, "-")[1])
	}
	return
}

// GetTeamOpenidInfoList 通过队伍码获取队伍成员openid信息
func GetTeamOpenidInfoList(ctx context.Context, FsourceId string, teamCode string) (
	openids []*OpenInfo, err error,
) {
	openids = make([]*OpenInfo, 0)
	tableName, err := GetTeamLogTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	teamLogDatas := make([]*model.TeamCodeLog, 0)
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where(" Fsource_id = ? and team_code = ? and is_delete = 0",
			FsourceId, teamCode).
		Order("id asc").
		Find(&teamLogDatas)
	if db.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	for _, item := range teamLogDatas {
		openids = append(openids, &OpenInfo{
			Uid:         item.InviteeUid,
			AccountType: int32(item.InviteeAccountType),
		})
	}
	return
}

// GetTeamCode 获取当前账号的teamCode
func GetTeamCode(ctx context.Context, FsourceId string) (
	teamCode string, err error,
) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	tableName, err := GetTeamLogTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	var teamLogData *model.TeamCodeLog

	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where("invitee_uid = ? and Fsource_id = ? and invitee_account_type = ? and is_delete = 0",
			userAccount.Uid, FsourceId, userAccount.AccountType).
		First(&teamLogData)
	if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	if db.Error == gorm.ErrRecordNotFound {
		err = errs.NewCustomError(ctx, code.NoJoinTeam, "no join team")
		return
	}

	teamCode = teamLogData.TeamCode
	return
}

// TeamLeaderInfo TODO
func TeamLeaderInfo(ctx context.Context, teamCode string, FsourceId string) (
	userInfo *accountTeam.UserInfo, isLeader bool, err error,
) {
	userInfo = &accountTeam.UserInfo{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	tableName, err := GetTeamTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	var teamAccountCodeData *model.TeamAccountCode

	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where("Fsource_id = ? and team_code = ? and is_delete = 0",
			FsourceId, teamCode).
		First(&teamAccountCodeData)
	if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	if db.Error == gorm.ErrRecordNotFound {
		return
	}
	isLeader = teamAccountCodeData.Uid == userAccount.Uid
	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         teamAccountCodeData.Uid,
		AccountType: accountPb.AccountType(1),
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    strings.Split(teamAccountCodeData.Uid, "-")[1],
			ChannelId: userAccount.IntlAccount.ChannelId,
			GameId:    strings.Split(teamAccountCodeData.Uid, "-")[0],
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	accountTeamProxy := accountTeam.NewTeamClientProxy()
	users := make([]*account.UserAccount, 0)
	users = append(users, &account.UserAccount{
		Uid:         teamAccountCodeData.Uid,
		AccountType: account.AccountType(teamAccountCodeData.AccountType),
	})

	accountTeamRsp, errG := accountTeamProxy.GetTeamInfoList(ctx, &accountTeam.GetTeamInfoListReq{
		Users:     users,
		FsourceId: FsourceId,
		GameId:    strings.Split(teamAccountCodeData.Uid, "-")[0],
	}, callopts...)
	if errG != nil {
		err = errG
		return
	}
	userInfo = accountTeamRsp.Users[0]
	return
}

// CurrentTeamPeopleNum 查询队伍当前人数
func CurrentTeamPeopleNum(ctx context.Context, teamCode string, FsourceId string) (peopleNum int32, err error) {
	tableName, err := GetTeamLogTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	var num int64
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where("team_code = ? and is_delete = 0",
			teamCode).
		Count(&num)
	if db.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	peopleNum = int32(num)
	return
}

// GetTeamCodeByUid TODO
func GetTeamCodeByUid(ctx context.Context, FsourceId string, uid string) (
	teamCode string, err error,
) {
	tableName, err := GetTeamLogTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	var teamLogData *model.TeamCodeLog

	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where("invitee_uid = ? and Fsource_id = ? and is_delete = 0",
			uid, FsourceId).
		First(&teamLogData)
	if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	if db.Error == gorm.ErrRecordNotFound {
		err = errs.NewCustomError(ctx, code.NoJoinTeam, "no join team")
		return
	}

	teamCode = teamLogData.TeamCode
	return
}

// SyncTeamInfo TODO
func SyncTeamInfo(ctx context.Context, FsourceId string) (err error) {
	scheduleCtx := context.Background()
	excelData := make([][]string, 0)
	excelData = append(excelData, []string{
		"openid1", "openid2", "openid3",
	})

	tableName, err := GetTeamTableName(scheduleCtx, FsourceId)
	if err != nil {
		return
	}
	var teamAccountCode []*model.TeamAccountCode
	selectdb := DB.DefaultConnect().WithContext(scheduleCtx).Table(tableName).Where("Fsource_id = ? and is_delete = 0",
		FsourceId).Find(&teamAccountCode)
	if selectdb.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", selectdb.Error)
		return
	}

	for _, item := range teamAccountCode {
		openids, err := GetTeamOpenidList(scheduleCtx, FsourceId, item.TeamCode)
		if err != nil {
			return err
		}

		excelData = append(excelData, openids)
	}

	filepath, err := datadump.CreateExcel(scheduleCtx, fmt.Sprintf("team_info-%v.xlsx", FsourceId), excelData)
	if err != nil {
		return
	}
	mediaId, err := datadump.QWXUploadMedia(scheduleCtx, filepath, "268e5d13-f0db-4bf4-afa2-3112dabdd29c",
		fmt.Sprintf("team_info-%v.xlsx", FsourceId))
	if err != nil {
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_info").Infof("mediaId: %v", mediaId)
	option := &datadump.SendOption{
		Msgtype: "file",
		Chatid:  "wrkSFfCgAA9d0ZmRiwpSS3Y-xrxLcH4g",
		File: &datadump.FileType{
			MediaId: mediaId,
		},
	}
	_, err = datadump.QWXSend(scheduleCtx, option, "268e5d13-f0db-4bf4-afa2-3112dabdd29c")
	if err != nil {
		return
	}

	return
}

// GetTeamTableName TODO
func GetTeamTableName(ctx context.Context, FsourceId string) (tableName string, err error) {
	tableName, err = configModel.GetTabNameWithSourceId(ctx, model.TeamCodeConf{}.TableName(), FsourceId,
		model.TeamAccountCode{}.TableName(), 100)
	if err != nil {
		return "", errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return tableName, nil
}

// GetTeamLogTableName TODO
func GetTeamLogTableName(ctx context.Context, FsourceId string) (tableName string, err error) {
	tableName, err = configModel.GetTabNameWithSourceId(ctx, model.TeamCodeConf{}.TableName(), FsourceId,
		model.TeamCodeLog{}.TableName(), 100)
	if err != nil {
		return "", errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return tableName, nil
}
