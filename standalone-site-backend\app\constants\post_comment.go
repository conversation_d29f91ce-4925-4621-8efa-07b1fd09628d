package constants

// PostCommentT BIZID 3=资讯评论 4=动态评论
type PostCommentT int

const (
	COMMENT_TYPE_LIP_COMMUNITY PostCommentT = iota + 3
	COMMENT_TYPE_LIP_UGC
)

type CommentStarType int

const (
	COMMENT_STAR CommentStarType = iota + 1
	REPLY_STAR
)

type CommentType int

const (
	DYNAMIC_COMMENT CommentType = iota + 1 //评论
	DYNAMIC_REPLY                          //回复
)

// PostReviewT CMS审批动态评论类型，1审核通过，2审核不通过，3CMS删除评论，4CMS忽略评论举报
type PostCommentReviewT uint8

const (
	PostCommentReviewPass PostCommentReviewT = iota + 1
	PostCommentReviewNoPass
	PostCommentReviewDelete
	PostCommentIgnoreReport
	PostCommentReportDelete
)

// 删除原因0-用户自行删除1-c端管理员删除2-b端审核删除3-b端管理员删除4-b端举报删除
type PostCommentContentDelType uint8

const (
	POST_COMMENT_CONTENT_DELETE_TYPE_SELF UserAuditDelType = iota
	POST_COMMENT_CONTENT_DELETE_TYPE_CADMIN
	POST_COMMENT_CONTENT_DELETE_TYPE_BREVIEW
	POST_COMMENT_CONTENT_DELETE_TYPE_BADMIN
	POST_COMMENT_CONTENT_DELETE_TYPE_BREPORT
)

const (
	CommentDelStatusNotDeleted = 0 // 【评论删除状态】 未删除
	CommentDelStatusDeleted    = 1 // 【评论删除状态】 已删除
)

// 1:未处理 2:已发布 3:已忽略
const (
	CommentStatusNotDealed = 1 // 【评论状态】 未处理
	CommentStatusPublished = 2 // 【评论状态】 已发布
	CommentStatusIgnored   = 3 // 【评论状态】 已忽略
)

type ETopBottomAction int32

const (
	ETopBottomAction_CancelTopButtom       ETopBottomAction = 0
	ETopBottomAction_CMSSetTop             ETopBottomAction = 1
	ETopBottomAction_CMSSetBottom          ETopBottomAction = 2
	ETopBottomAction_CancelTopButtom_Alias ETopBottomAction = 3 // 取消状态别名
	ETopBottomAction_ModeratorSetTop       ETopBottomAction = 4
	ETopBottomAction_ModeratorSetBottom    ETopBottomAction = 5
	ETopBottomAction_PosterSetTop          ETopBottomAction = 6
	ETopBottomAction_PosterSetBottom       ETopBottomAction = 7
)
