package model

type CommentReply struct {
	*Model
	CommentID     int64  `json:"comment_id"`
	Reply2replyID int64  `json:"reply2reply_id"`
	IntlOpenid    string `json:"intl_openid"`
	IsAudit       int8   `json:"is_audit"`
	IsParentDel   int    `json:"is_parent_del"`
	AtUserOpenid  string `json:"at_user_openid"`
	Content       string `json:"content"`
	UpvoteCount   int64  `json:"upvote_count"`
	GameId        string `json:"game_id"`
	AreaId        string `json:"area_id"`
	IP            string `json:"ip"`
	IPLoc         string `json:"ip_loc"`
}

type CommentReplyFormated struct {
	ID            int64         `json:"id"`
	CommentID     int64         `json:"comment_id"`
	Reply2replyID int64         `json:"reply2reply_id"`
	IntlOpenid    string        `json:"intl_openid"`
	User          *UserFormated `json:"user"`
	IsAudit       int8          `json:"is_audit"`
	IsStar        bool          `json:"is_star"`
	IsParentDel   int           `json:"is_parent_del"`
	AtUserOpenid  string        `json:"at_user_openid"`
	AtUser        *UserFormated `json:"at_user"`
	GameId        string        `json:"game_id"`
	AreaId        string        `json:"area_id"`
	Content       string        `json:"content"`
	UpvoteCount   int64         `json:"upvote_count"`
	IPLoc         string        `json:"ip_loc"`
	CreatedOn     int64         `json:"created_on"`
	ModifiedOn    int64         `json:"modified_on"`
}

type CommentReplyPager struct {
	Page      int   `json:"page"`
	PageSize  int   `json:"page_size"`
	TotalRows int64 `json:"total_rows"`
}

type CommentReplyFormatedForPage struct {
	DataList []*CommentReplyFormated `json:"list"`
	PageInfo *CommentReplyPager      `json:"pager"`
}

func (c *CommentReply) Format() *CommentReplyFormated {
	if c.Model == nil {
		return &CommentReplyFormated{}
	}

	return &CommentReplyFormated{
		ID:            c.ID,
		CommentID:     c.CommentID,
		Reply2replyID: c.Reply2replyID,
		IntlOpenid:    c.IntlOpenid,
		User:          &UserFormated{},
		IsAudit:       c.IsAudit,
		IsStar:        false,
		IsParentDel:   c.IsParentDel,
		AtUserOpenid:  c.AtUserOpenid,
		GameId:        c.GameId,
		AreaId:        c.AreaId,
		AtUser:        &UserFormated{},
		Content:       c.Content,
		UpvoteCount:   c.UpvoteCount,
		IPLoc:         c.IPLoc,
		CreatedOn:     c.CreatedOn,
		ModifiedOn:    c.ModifiedOn,
	}
}

// CommentReplyForDelete 删除回复的回复的时候获取数据用
type CommentReplyForDelete struct {
	ID            int64 `gorm:"primary_key" json:"id"`
	Reply2replyID int64 `json:"reply2reply_id"`
	IsAudit       int   `json:"is_audit"`
}

func (c *CommentReply) TableName() string {
	return "p_comment_reply"
}
