package model

type EmoticonIcon struct {
	*Model
	Name    string `gorm:"column:name" json:"name"`       //表情包名称
	Icon    string `gorm:"column:icon" json:"icon"`       //表情包表情图
	Order   int    `gorm:"column:order" json:"order"`     //顺序值
	Creator string `gorm:"column:creator" json:"creator"` //创建人
	Updater string `gorm:"column:updater" json:"updater"` //操作人
	GameID  string `gorm:"column:game_id" json:"game_id"` //游戏id
	AreaID  string `gorm:"column:area_id" json:"area_id"` //大区id
}

func (p *EmoticonIcon) TableName() string {
	return "p_emoticon_icon"
}
