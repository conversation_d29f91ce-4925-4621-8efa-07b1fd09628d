package metadatadecode

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"trpc.publishing_application.standalonesite/app/constants"
)

type CommonParam struct {
	GameId     string `json:"game_id"`
	AreaId     string `json:"area_id"`
	Source     string `json:"source"`
	Language   string `json:"language"`
	IntlGameId string `json:"intl_game_id"`
	LipRegion  string `json:"lip_region"`
	Scene      string `json:"data_statistics_scene"`       // 上报参数
	PageID     string `json:"data_statistics_page_id"`     // 上报参数
	ClientType string `json:"data_statistics_client_type"` // 上报参数
	Lang       string `json:"data_statistics_lang"`        // 上报参数
}

type ReportParams struct {
	Scene      string `json:"data_statistics_scene"`       // 上报参数
	PageID     string `json:"data_statistics_page_id"`     // 上报参数
	ClientType string `json:"data_statistics_client_type"` // 上报参数
	Lang       string `json:"data_statistics_lang"`        // 上报参数
}

// 从metadata的header中解析x-common-param
func GetGameIdAndAreaId(c context.Context) (string, string) {
	var gameId, areaId string
	intlGameId := ParseHeaderCookie(c, "game_gameid")
	if intlGameId == "29080" {
		gameId = "16"
		areaId = "global"
	} else if intlGameId == "29157" {
		gameId = "16"
		areaId = "tw"
	}
	if gameId == "" || areaId == "" {
		// 从cookie获取
		gameId = ParseHeaderCookie(c, "game_id")
		areaId = ParseHeaderCookie(c, "area_id")
	}
	if gameId == "" || areaId == "" {
		// 获取metadata中的header
		header := metadata.GetHTTPHeader(c)
		// 注入到当前的context中
		c = ParseHeaderCommonParam(header, c)

		gameIdV := c.Value(constants.GameId)
		if gameIdV != nil {
			gameId = gameIdV.(string)
		}
		areaIdV := c.Value(constants.AreaID)
		if areaIdV != nil {
			areaId = areaIdV.(string)
		}
	}
	return gameId, areaId
}

func GetIntlGameId(c context.Context) string {
	// 获取metadata中的header
	header := metadata.GetHTTPHeader(c)
	// 注入到当前的context中
	c = ParseHeaderCommonParam(header, c)
	intlGameIdV := c.Value(constants.IntlGameId)
	var intlGameId string
	if intlGameIdV != nil {
		intlGameId = intlGameIdV.(string)
	}
	return intlGameId
}

func GetReportParams(c context.Context) *ReportParams {
	var resp = &ReportParams{}
	// 获取metadata中的header
	header := metadata.GetHTTPHeader(c)
	if header == nil {
		return resp
	}
	if data, ok := header["X-Common-Params"]; ok {
		err := json.Unmarshal([]byte(data[0]), &resp)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetReportParams | get report params to json decode failed, header:%v,err: %v", data[0], err)
			return resp
		}
		return resp
	}
	return resp
}

func ParseHeaderCommonParam(header http.Header, c context.Context) context.Context {
	// log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("ParseHeaderCommonParam header: %v\n", header)
	if header == nil {
		return c
	}
	if data, ok := header["X-Common-Params"]; ok {
		// log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("ParseHeaderCommonParam X-Common-Params: %v\n", data)
		if len(data) > 0 {
			var commonParam CommonParam
			err := json.Unmarshal([]byte(data[0]), &commonParam)
			if err != nil {
				return c
			}
			c = context.WithValue(c, constants.GameId, commonParam.GameId)
			c = context.WithValue(c, constants.AreaID, commonParam.AreaId)
			c = context.WithValue(c, constants.Source, commonParam.Source)
			c = context.WithValue(c, constants.IntlGameId, commonParam.IntlGameId)
			c = context.WithValue(c, constants.LipRegion, commonParam.LipRegion)
			c = context.WithValue(c, constants.Scene, commonParam.Scene)
			c = context.WithValue(c, constants.PageID, commonParam.PageID)
			c = context.WithValue(c, constants.ClientType, commonParam.ClientType)
			c = context.WithValue(c, constants.Lang, commonParam.Lang)
		}
	}
	return c
}

// 从metadata的header中解析cookie中特定参数

func ParseHeaderCookie(c context.Context, key string) string {
	return metadata.GetHttpCookie(c, fmt.Sprintf("%s", key))
}
