package formatted

import (
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/model"
)

func ReturnProtoCommentData(comment *model.CommentFormated) *pb.CommentItem {
	var data = &pb.CommentItem{
		//	Id:          comment.ID,
		//	PostId:      comment.PostUUID,
		//	IntlOpenid:  comment.IntlOpenid,
		//	User:        nil,
		//	Contents:    nil,
		//	Replies:     nil,
		//	UpvoteCount: comment.UpvoteCount,
		//	GameId:      comment.GameId,
		//	AreaId:      comment.AreaId,
		//	IsAudit:     cast.ToBool(comment.IsAudit),
		//	IsStar:      comment.IsStar,
		//	IpLoc:       comment.IPLoc,
		//	CreatedOn:   comment.CreatedOn,
		//	ModifiedOn:  comment.ModifiedOn,
		//	CanDelete:   false,
		//	CanReport:   false,
		//}
		//if comment.User != nil {
		//	data.User = ReturnDynamicProtoUserInfoFormatted(comment.User)
		//}
		//if comment.Contents != nil && len(comment.Contents) > 0 {
		//	var commentContents = make([]*pb.CommentContent, 0, len(comment.Contents))
		//	for _, content := range comment.Contents {
		//		commentContents = append(commentContents, &pb.CommentContent{
		//			CommentId:  content.CommentID,
		//			IntlOpenid: content.IntlOpenid,
		//			Content:    content.Content,
		//			Type:       int32(content.Type),
		//			Sort:       content.Sort,
		//		})
		//	}
		//	data.Contents = commentContents
		//}
		//if comment.Replies != nil {
		//	var commentReplies = &pb.Replies{
		//		DataList: make([]*pb.RepliesItem, 0),
		//		Total:    0,
		//	}
		//	if len(comment.Replies.DataList) > 0 {
		//		for _, formated := range comment.Replies.DataList {
		//			commentReplies.DataList = append(commentReplies.DataList, ReturnProtoCommentReplyData(formated))
		//		}
		//		commentReplies.Total = comment.Replies.PageInfo.TotalRows
		//	}
		//	data.Replies = commentReplies
	}
	return data
}

func ReturnProtoCommentDataByComment(comment *model.Comment) *pb.CommentItem {
	var data = &pb.CommentItem{
		//Id:          comment.ID,
		//PostId:      comment.PostUUID,
		//IntlOpenid:  comment.IntlOpenid,
		//User:        nil,
		//Contents:    nil,
		//Replies:     nil,
		//UpvoteCount: comment.UpvoteCount,
		//GameId:      comment.GameId,
		//AreaId:      comment.AreaId,
		//IsAudit:     cast.ToBool(comment.IsAudit),
		//IpLoc:       comment.IPLoc,
		//CreatedOn:   comment.CreatedOn,
		//ModifiedOn:  comment.ModifiedOn,
		//CanDelete:   false,
		//CanReport:   false,
	}
	return data
}

func ReturnProtoCommentReplyData(reply *model.CommentReplyFormated) *pb.RepliesItem {
	return &pb.RepliesItem{
		//Id:            reply.ID,
		//CommentId:     reply.CommentID,
		//Reply2ReplyId: reply.Reply2replyID,
		//IntlOpenid:    reply.IntlOpenid,
		//User:          ReturnDynamicProtoUserInfoFormatted(reply.User),
		//IsAudit:       int32(reply.IsAudit),
		//IsStar:        reply.IsStar,
		//IsParentDel:   int32(reply.IsParentDel),
		//AtUserOpenid:  reply.AtUserOpenid,
		//AtUser:        ReturnDynamicProtoUserInfoFormatted(reply.AtUser),
		//GameId:        reply.GameId,
		//AreaId:        reply.AreaId,
		//Content:       reply.Content,
		//UpvotecCount:  reply.UpvoteCount,
		//IpLoc:         reply.IPLoc,
		//CreatedOn:     reply.CreatedOn,
		//ModifiedOn:    reply.ModifiedOn,
	}
}

func ReturnProtoCommentReplyDataByReplyItem(reply *model.CommentReply) *pb.RepliesItem {
	return &pb.RepliesItem{
		//Id:            reply.ID,
		//CommentId:     reply.CommentID,
		//Reply2ReplyId: reply.Reply2replyID,
		//IntlOpenid:    reply.IntlOpenid,
		//IsAudit:       int32(reply.IsAudit),
		//IsParentDel:   int32(reply.IsParentDel),
		//AtUserOpenid:  reply.AtUserOpenid,
		//GameId:        reply.GameId,
		//AreaId:        reply.AreaId,
		//Content:       reply.Content,
		//UpvotecCount:  reply.UpvoteCount,
		//IpLoc:         reply.IPLoc,
		//CreatedOn:     reply.CreatedOn,
		//ModifiedOn:    reply.ModifiedOn,
	}
}

//func ReturnProtoOtherCommentData(comment *model.CommentMyFormated) *pb.OtherCommentItem {
//	var data = &pb.OtherCommentItem{
//		//Id:         comment.ID,
//		//PostId:     comment.PostUUID,
//		//IntlOpenid: comment.IntlOpenid,
//		//Type:       int32(comment.Type),
//		//IsAudit:    int32(comment.IsAudit),
//		//Post:       ReturnProtoTweetInfo(comment.Post),
//		//User:       ReturnDynamicProtoUserInfoFormatted(comment.User),
//		//Contents:   make([]*pb.CommentContent, 0),
//		//ReplyId:    comment.ReplyId,
//		//Reply:      ReturnProtoCommentReplyDataByReplyItem(comment.Reply),
//		//GameId:     comment.GameId,
//		//AreaId:     comment.AreaId,
//		//IpLoc:      comment.IPLoc,
//		//CreatedOn:  comment.CreatedOn,
//		//ModifiedOn: comment.ModifiedOn,
//	}
//	if comment.Contents != nil && len(comment.Contents) > 0 {
//		for _, content := range comment.Contents {
//			data.Contents = append(data.Contents, &pb.CommentContent{
//				CommentId:  content.CommentID,
//				IntlOpenid: content.IntlOpenid,
//				Content:    content.Content,
//				Type:       int32(content.Type),
//				Sort:       content.Sort,
//			})
//		}
//	}
//	return data
//}
