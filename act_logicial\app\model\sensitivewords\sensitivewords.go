package sensitivewords

type TextCheckResult struct {
	CheckResult  int    `json:"check_result"`
	FilteredText string `json:"filtered_text"`
	Label        int    `json:"label"`
	CheckDesc    string `json:"check_desc"`
}

type Data struct {
	ErrCode         int             `json:"err_code"`
	ErrMsg          string          `json:"err_msg"`
	DataID          string          `json:"data_id"`
	RequestID       string          `json:"request_id"`
	TextCheckResult TextCheckResult `json:"text_check_result"`
}

type Response struct {
	ErrCode int    `json:"err_code"`
	ErrMsg  string `json:"err_msg"`
	Data    []Data `json:"data"`
}
