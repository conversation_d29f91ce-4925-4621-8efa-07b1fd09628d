package thirdplatform

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/pkg/httpclient"
)

const (
	APIXVersionV10 = "APIX1-HMAC-SHA256"
)

/*
GetPostList 调用网红的接口，获取网红内容流数据
参考：
*/
func GetPostList(gameId, areaId string, startTime, endTime int64) (creatorHubResult model.CreatorHubPostResp, errCode error) {
	postData := map[string]interface{}{
		"gameid":     gameId,
		"areaid":     areaId,
		"start_time": startTime,
		"end_time":   endTime,
	}
	postJSON, err := json.Marshal(postData)
	postDataStr := string(postJSON)
	if err != nil {
		errCode = errs.NewCustomError(context.Background(), code.GetCreatorHubDataInvalidParams, "Incorrect request parameters.")
		return
	}
	// 生成签名，调用creatorhub网红平台接口
	conf := config.GetConfig()
	creatorhubConf := conf.CreatorHubSetting
	currentTimeStamp := fmt.Sprintf("%d", time.Now().Unix())
	signature := GenAuthSignV10(creatorhubConf.GetTaskPath, http.MethodPost, "", currentTimeStamp, creatorhubConf.SecretId, creatorhubConf.SecretKey, postJSON)
	authHeaderStr := fmt.Sprintf("%s Signature=%s", APIXVersionV10, signature)
	requestURL := fmt.Sprintf("%s%s", creatorhubConf.ServiceUrl, creatorhubConf.GetTaskPath)

	xCommonParamsStr := fmt.Sprintf("{\"game_id\":\"%s\",\"area_id\":\"%s\",\"time_zone_offset\":-480}", gameId, areaId)

	optionOne := httpclient.ClientOption{
		URL:     requestURL,
		Type:    http.MethodPost,
		Timeout: 3 * time.Second,
		Header: map[string]string{
			"Content-Type":       "application/json; charset=utf-8",
			"Authorization":      authHeaderStr,
			"Host":               creatorhubConf.ServiceHost,
			"X-APIX-Timestamp":   currentTimeStamp,
			"X-APIX-Application": creatorhubConf.SecretId,
			"X-Common-Params":    xCommonParamsStr,
		},
		PostString: postDataStr,
	}

	// 发请求
	resultOption := httpclient.RequestOne(optionOne)
	// 结果判断
	if resultOption.RequestError != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call CreatorHub api http request error, %v", resultOption)
		errCode = errs.NewCustomError(context.Background(), code.GetCreatorHubDataHttpError, "GetPostList | Exception occurred while calling the CreatorHub API.")
		return
	}

	apiResultString := resultOption.Result
	jsonError := json.Unmarshal([]byte(apiResultString), &creatorHubResult)

	if jsonError != nil || creatorHubResult.Ret != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CreatorHub api return error, postDataStr %s", postDataStr)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CreatorHub api return error, %s", apiResultString)
		errCode = errs.NewCustomError(context.Background(), code.GetCreatorHubDataInvalidResp, "GetPostList | Exception occurred while processing the response body of the CreatorHub API call.")
		return
	}

	// 根据发布时间排序排序
	sort.Slice(creatorHubResult.Data.WorkInfos, func(i, j int) bool {
		return creatorHubResult.Data.WorkInfos[i].WorkPublishTime < creatorHubResult.Data.WorkInfos[j].WorkPublishTime
	})

	return
}

// GenAuthSignV10 生成认证签名v10版本
func GenAuthSignV10(uri, method, rawQuery, reqTimeStamp, secretId, secretKey string, requestBody []byte) string {
	if uri == "" || secretKey == "" {
		return ""
	}
	var hashedRequestPayload string
	if len(requestBody) != 0 {
		hashedRequestPayload = hexEncodeSHA256ToLowercase(requestBody)
	}
	canonicalRequest := strings.Join([]string{method, rawQuery, hashedRequestPayload}, "\n")
	hashedCanonicalRequest := hexEncodeSHA256ToLowercase([]byte(canonicalRequest))
	stringToSign := strings.Join([]string{APIXVersionV10, reqTimeStamp, hashedCanonicalRequest}, "\n")
	secretTimestamp := hmacSHA256(secretKey, reqTimeStamp)
	secretSigning := hmacSHA256(secretTimestamp, uri)
	signature := hexEncodeHmacSHA256(secretSigning, stringToSign)
	return signature
}

func hexEncodeSHA256ToLowercase(data []byte) string {
	if len(data) == 0 {
		return ""
	}
	h := sha256.New()
	h.Write(data)
	return strings.ToLower(hex.EncodeToString(h.Sum(nil)))
}

func hmacSHA256(secret, data string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return string(h.Sum(nil))
}

func hexEncodeHmacSHA256(secret, data string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}
