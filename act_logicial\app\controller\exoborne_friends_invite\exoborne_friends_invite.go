package exobornefriendsinvite

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_exoborne_friends_invite"
	"trpc.act.logicial/app/code"
	logic "trpc.act.logicial/app/logic/exoborne_friends_invite"
	teamLogic "trpc.act.logicial/app/logic/team"
)

// ExobrnFriendsInviteImpl TODO
type ExobrnFriendsInviteImpl struct {
	pb.UnimplementedExoborneFriendsInvite
}

// CheckTaskStatus TODO
func (s *ExobrnFriendsInviteImpl) CheckTaskStatus(ctx context.Context, req *pb.CheckTaskStatusReq) (
	rsp *pb.CheckTaskStatusRsp, err error,
) {
	rsp = &pb.CheckTaskStatusRsp{
		Finish: false,
	}
	teamCode, err := teamLogic.GetTeamCode(ctx, req.FsourceId)
	if err != nil {
		return
	}
	openids, err := teamLogic.GetTeamOpenidList(ctx, req.FsourceId, teamCode)
	if err != nil {
		return
	}
	log.WithFieldsContext(ctx, "log_type", "GetTaskStatusByCredentials", "str_field_1", teamCode).Infof("openids: %+v",
		openids)

	times, err := logic.GetTaskStatusByCredentials(ctx, openids)
	if err != nil {
		return
	}

	if req.TagId == "1" {
		rsp.Finish = times >= 1
	}
	if req.TagId == "2" {
		rsp.Finish = times >= 3
	}

	if !rsp.Finish {
		err = errs.NewCustomError(ctx, code.TaskNotFinish, "task not finished")
		return
	}
	return
}

// JoinRank 报名参与排行榜活动
func (s *ExobrnFriendsInviteImpl) JoinRank(ctx context.Context, req *pb.JoinRankReq) (
	rsp *pb.JoinRankRsp, err error,
) {
	rsp = &pb.JoinRankRsp{}
	teamCode, err := teamLogic.GetTeamCode(ctx, req.FsourceId)
	if err != nil {
		return
	}
	openids, err := teamLogic.GetTeamOpenidList(ctx, req.FsourceId, teamCode)
	if err != nil {
		return
	}
	err = logic.JoinRank(ctx, teamCode, openids)
	return
}

// GetJoinStatus 获取报名状态
func (s *ExobrnFriendsInviteImpl) GetJoinStatus(ctx context.Context, req *pb.GetJoinStatusReq) (
	rsp *pb.GetJoinStatusRsp, err error,
) {
	rsp = &pb.GetJoinStatusRsp{}
	teamCode, err := teamLogic.GetTeamCode(ctx, req.FsourceId)
	if err != nil {
		return
	}
	isJoin, err := logic.GetJoinStatus(ctx, teamCode)
	if err != nil {
		return
	}
	rsp.IsJoin = isJoin
	return
}

// IsLoginTeam 是否整个队伍都登录了
func (s *ExobrnFriendsInviteImpl) IsLoginTeam(ctx context.Context, req *pb.IsLoginTeamReq) (
	rsp *pb.IsLoginTeamRsp, err error,
) {
	rsp = &pb.IsLoginTeamRsp{}
	teamCode, err := teamLogic.GetTeamCode(ctx, req.FsourceId)
	if err != nil {
		return
	}
	openids, err := teamLogic.GetTeamOpenidList(ctx, req.FsourceId, teamCode)
	if err != nil {
		return
	}

	isLogin, err := logic.IsLoginByOpenid(ctx, openids)
	if err != nil {
		return
	}

	rsp.IsLogin = isLogin
	if !rsp.IsLogin {
		err = errs.NewCustomError(ctx, code.TeamIsNoLoginToday, "today team is not all login")
	}
	return
}

// ExportJoinList 导出排行榜报名数据
func (s *ExobrnFriendsInviteImpl) ExportJoinList(ctx context.Context, req *pb.ExportJoinListReq) (
	rsp *pb.ExportJoinListRsp, err error,
) {
	rsp = &pb.ExportJoinListRsp{}
	go logic.ExportJoinList(ctx)
	return
}

// UpdateOnlineDuration 更新在线时长
func (s *ExobrnFriendsInviteImpl) UpdateOnlineDuration(ctx context.Context, req *pb.UpdateOnlineDurationReq) (
	rsp *pb.UpdateOnlineDurationRsp, err error,
) {
	rsp = &pb.UpdateOnlineDurationRsp{}

	go logic.UpdateOnlineDuration(ctx)
	return
}

// GetUserName 获取用户角色
func (s *ExobrnFriendsInviteImpl) GetUserName(ctx context.Context, req *pb.GetUserNameReq) (
	rsp *pb.GetUserNameRsp, err error,
) {
	rsp = &pb.GetUserNameRsp{}
	roleName, err := logic.GetUserName(ctx)
	if err != nil {
		return rsp, err
	}

	rsp.RoleName = roleName
	if rsp.RoleName == "" {
		err = errs.NewCustomError(ctx, code.NoRoleName, "no role name")
		return rsp, err
	}
	return
}

// SyncRankJoin 满足队伍人数条件同步报名到排行榜
func (s *ExobrnFriendsInviteImpl) SyncRankJoin(ctx context.Context, req *pb.SyncRankJoinReq) (
	rsp *pb.SyncRankJoinRsp, err error,
) {
	rsp = &pb.SyncRankJoinRsp{}
	go logic.SyncRankJoin(ctx)
	return
}

// SyncUserName 同步用户名称
func (s *ExobrnFriendsInviteImpl) SyncUserName(ctx context.Context, req *pb.SyncUserNameReq) (
	rsp *pb.SyncUserNameRsp, err error,
) {
	rsp = &pb.SyncUserNameRsp{}
	go logic.SyncUserName(ctx)
	return
}

// SyncUserDelopytimes 同步用户对局数量
func (s *ExobrnFriendsInviteImpl) SyncUserDelopytimes(ctx context.Context, req *pb.SyncUserDelopytimesReq) (
	rsp *pb.SyncUserDelopytimesRsp, err error,
) {
	rsp = &pb.SyncUserDelopytimesRsp{}
	go logic.SyncUserDelopytimes(ctx)
	return
}
