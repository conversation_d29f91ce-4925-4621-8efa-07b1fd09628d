package config

import "time"

// Config 统一配置
type Config struct {
	App *AppSettingS `json:"App" mapstructure:"App"`

	COS []*COSSetting `json:"COS" mapstructure:"COS"`

	Security SecuritySetting `json:"Security" mapstructure:"Security"`

	UserLimit UserLimitS `json:"UserLimit" mapstructure:"UserLimit"`

	CommunitySetting *CommunitySettingS `json:"CommunityConf" mapstructure:"CommunityConf"`

	ElasticSearchSetting *ElasticSearchSettingS `json:"ElasticSearch" mapstructure:"ElasticSearch"`

	CreatorHubSetting *CreatorHubSettingS `json:"CreatorHubConf" mapstructure:"CreatorHubConf"`

	LIPPointsSetting *LIPPointsSettingS `json:"LIPPointsConf" mapstructure:"LIPPointsConf"`

	ObjectStorage *ObjectStorageS `json:"ObjectStorage" mapstructure:"ObjectStorage"`

	SiteMessageSetting *SiteMessageSettingS `json:"SiteMessageSetting" mapstructure:"SiteMessage"`

	Report *ReportConfig `json:"Report" mapstructure:"Report"`

	Crypto *CryptoSettingS `json:"Crypto" mapstructure:"Crypto"`

	GoogleConf *GoogleConfig `json:"GoogleConf" mapstructure:"GoogleConf"`

	ShareConf *ShareConfSettingS `json:"ShareConf" mapstructure:"ShareConf"`

	Dynamic *DynamicSettingS `json:"Dynamic" mapstructure:"Dynamic"`

	TranslateConf *TranslateConfSettingS `json:"TranslateConf" mapstructure:"TranslateConf"`

	TasksConf *TasksConfig `json:"TasksConf" mapstructure:"TasksConf"`

	CMSSetting *CMSSettingS `json:"CMSSetting" mapstructure:"CMSSetting"`

	ActivityPostTagSetting []*ActivityPostTagSettingS `json:"ActivityPostTagConf" mapstructure:"ActivityPostTagConf"`
}

type SiteMessageSettingS struct {
	RunTask      int
	TickInterval int64
}

type ReportConfig struct {
	Host          string // 上报Host
	Path          string //上报path
	QWBot         string // 企业微信bot地址
	CheckInTaskId string // 签到任务ID
}

type COSSetting struct {
	CmsGameId    int
	CmsAreaId    string
	CloudType    string
	AppId        string
	SecretID     string
	SecretKey    string
	Region       string
	Bucket       string
	CDNDomain    string
	BucketDomain string
	STSDuration  int
}

type SecuritySetting struct {
	Host      string
	SecretId  string
	SecretKey string
}

// UserLimitS 用户操作限频
type UserLimitS struct {
	Comment struct {
		Minute struct {
			Duration int64
			Count    int64
		}
		Hour struct {
			Duration int64
			Count    int64
		}
		Day struct {
			Duration int64
			Count    int64
		}
	}
	Post struct {
		Minute struct {
			Duration int64
			Count    int64
		}
		Hour struct {
			Duration int64
			Count    int64
		}
		Day struct {
			Duration int64
			Count    int64
		}
	}
}

type AppSettingS struct {
	Env                   string
	MaxCommentCount       int64
	AttachmentIncomeRate  float64
	DefaultContextTimeout time.Duration
	DefaultPageSize       int
	MaxPageSize           int
	ServerTimeOut         time.Duration
	CookieKeyPrefix       string
}

type CommunitySettingS struct {
	IsInnerAPI        int
	ServiceUrl        string
	ServicePathPrefix string
	AppKey            string
	AppSecret         string
}

type ElasticSearchSettingS struct {
	Host                string
	User                string
	Password            string
	Secure              bool
	Sniff               bool
	HealthcheckInterval time.Duration
	TweetIndex          string
	TweetAuditIndex     string
	TweetCommentIndex   string
	UserAuditIndex      string
	UserInfoIndex       string
	ReportIndex         string
}

type CreatorHubSettingS struct {
	ServiceHost                           string
	ServiceUrl                            string
	GetTaskPath                           string
	GetTaskWorkPath                       string
	GetTickDuration                       time.Duration
	GetTimeoutDuration                    time.Duration
	TickInterval                          int64
	SecretId                              string
	SecretKey                             string
	SyncCreatorHubActivityTimeoutDuration time.Duration //同步网红数据流活动的时间间隔
	SyncCreatorHubWorkTimeoutDuration     time.Duration //同步网红数据流活动作品的时间间隔
	CreatorHubActivityGameId              int64         //网红活动跳转详情页配置的gameId
	CreatorHubActivityPageUrl             string        //网红活动跳转详情页配置的url
	CreatorHubActivityGameName            string        //网红活动跳转详情页配置的游戏名称
	CreatorHubTagId                       int64         // 网红话题id
	CreatorHubWorkDetailPath              string        // 网红作品详情页接口
	CreatorHubUserDetailPath              string        // 网红详情页接口
	CreatorHubCheckUserTokenPath          string        // 网红活动校验用户token接口
	CreatorHubGetAuthorWorksList          string        // 获取作者作品列表接口
}

type LIPPointsSettingS struct {
	ServiceUrl         string
	ServicePathPrefix  string
	CommodityDetailUrl string
}

type ObjectStorageS struct {
	RetainInDays int
	TempDir      string
}

type CryptoSettingS struct {
	Key string
}

type GoogleConfig struct {
	APIKey string
}

type ShareConfSettingS struct {
	RewardsIconUrl string
	RewardsPageUrl string
	PostDetailUrl  string
	TagDetailUrl   string
	I18NFileName   string
}

type DynamicSettingS struct {
	HotPostEventTimeoutDuration        time.Duration
	BrowseCountCoefficient             float64
	UpvoteCountCoefficient             float64
	CommentCountCoefficient            float64
	CollectionCountCoefficient         float64
	ShareCountCoefficient              float64
	SyncYoutubeNewsTimeoutDuration     time.Duration
	VideoPreviewUrl                    string
	PostMachineReviewTimeoutDuration   time.Duration
	SyncSiteMessageStatTimeoutDuration time.Duration
	SendFriendRequestLimitDuration     time.Duration
	DemotionNum                        int64
}

type TranslateConfSettingS struct {
	AIGCHost             string
	AIGCPath             string
	WelocAiHost          string
	WelocAiTranslatePath string
	WelocAiSegmentPath   string
	WelocAiAppKey        string
	WelocAiAppSecret     string
	MaxRequestCount      int
}

type TasksConfig struct {
	OfficialFollowTask *OfficialFollowTask
	TempTask           *TempTask
}

type OfficialFollowTask struct {
	OfficialIntlOpenids []string
}

type TempTask struct {
	NewUserRegisterTask *NewUserRegisterTask
}

type NewUserRegisterTask struct {
	StartTime int64
	EndTime   int64
	MsgId     int64
}

type CMSSettingS struct {
	ServiceHost           string
	GetContentByLabelPath string
	GameId                string
	AreaId                string
	Source                string
}

type ActivityPostTagSettingS struct {
	TagCode string
	TagId   int32
}
