package formatted

import (
	"context"
	"encoding/json"
	"errors"
	"html"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	redisClient "github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

func ReturnDynamicProtoUserInfoFormatted(userInfo *model.UserFormated) *pbUser.UserInfo {
	var userBindTitle = ReturnProtoUserTitleData(userInfo.Titles)
	// 获取用户管理员、认证、禁言标识
	authType := GetUserAuth(userInfo.IntlOpenid)
	isAdmin := GetUserAdmin(userInfo.IntlOpenid)
	isMute := GetUserMute(userInfo.IntlOpenid)
	avatarPendant, _ := GetUserCurWearedAvatarPendantIcon(context.Background(), userInfo.IntlOpenid)
	return &pbUser.UserInfo{
		Id:            userInfo.ID,
		Username:      html.UnescapeString(userInfo.Username),
		Status:        int32(userInfo.Status),
		Avatar:        userInfo.Avatar,
		IntlOpenid:    userInfo.IntlOpenid,
		Remark:        html.UnescapeString(userInfo.Remark),
		Language:      userInfo.Language,
		Titles:        userBindTitle,
		IsAdmin:       isAdmin,
		AuthType:      authType,
		IsMute:        isMute,
		Mood:          userInfo.Mood,
		GameTag:       userInfo.GameTag,
		AvatarPendant: avatarPendant,
	}
}

func ReturnDynamicProtoUserInfoFromatedWithLanguage(userInfo *model.UserFormated, language string) *pbUser.UserInfo {
	userWithLanguage := ReturnDynamicProtoUserInfoFormatted(userInfo)
	authDesc := GetUserAuthDesc(context.Background(), userInfo.IntlOpenid, userWithLanguage.AuthType, language)
	userWithLanguage.AuthDesc = authDesc
	return userWithLanguage
}

// 获取用户认证权限，1-官方认证2-创作认证3-机构认证
func GetUserAuth(intlOpenid string) (authType int32) {
	userOfficialAuthRedisKey := cache.GetAuthUserKey(1)
	userWriteAuthRedisKey := cache.GetAuthUserKey(2)
	userAgencyAuthRedisKey := cache.GetAuthUserKey(3)
	sectionManageAuthRedisKey := cache.GetAuthUserKey(4) // 板块管理员认证（仅添加认真标识，无实际权限）
	if res, err := redis.GetClient().SIsMember(context.Background(), userOfficialAuthRedisKey, intlOpenid).Result(); err == nil && res {
		authType = constants.USER_AUTH_TYPE_OFFICIAL //官方认证
		return
	}
	if res, err := redis.GetClient().SIsMember(context.Background(), userAgencyAuthRedisKey, intlOpenid).Result(); err == nil && res {
		authType = constants.USER_AUTH_TYPE_MECHANISM //机构认证
		return
	}
	if res, err := redis.GetClient().SIsMember(context.Background(), userWriteAuthRedisKey, intlOpenid).Result(); err == nil && res {
		authType = constants.USER_AUTH_TYPE_CREATOR //创作认证
		return
	}

	if res, err := redis.GetClient().SIsMember(context.Background(), sectionManageAuthRedisKey, intlOpenid).Result(); err == nil && res {
		authType = constants.USER_AUTH_TYPE_SECTION_MANAGER //板块管理员认证
	}

	return
}

// 获取用户管理员
func GetUserAdmin(intlOpenid string) (isAdmin bool) {
	userAdminRedisKey := cache.GetAdminUserKey(1)
	if res, err := redis.GetClient().SIsMember(context.Background(), userAdminRedisKey, intlOpenid).Result(); err == nil && res {
		isAdmin = true //管理员
	}
	return
}

// 获取用户禁言
func GetUserMute(intlOpenid string) (isMute bool) {
	userMuteRedisKey := cache.GetMuteUserKey(1, intlOpenid)
	muteRes, err := redis.GetClient().Get(context.Background(), userMuteRedisKey).Result()
	if err == nil && muteRes == "1" {
		isMute = true //禁言
	}
	return
}

// 获取用户降权
func GetUserDemotion(intlOpenid string) (isDemotion bool) {
	userMuteRedisKey := cache.GetDemotionUserKey(1, intlOpenid)
	demotionRes, err := redis.GetClient().Get(context.Background(), userMuteRedisKey).Result()
	if err == nil && demotionRes == "1" {
		isDemotion = true //禁言
	}
	return
}

// // 获取认证描述
// func GetUserAuthDesc(IntlOpenid string, actionValue int32, language string) string {
// 	userAuthDescLanguage := cache.GetAuthLangugeDescKey(IntlOpenid, actionValue, language)
// 	res, err := redis.GetClient().Get(context.Background(), userAuthDescLanguage).Result()
// 	if err == nil && res != "" {
// 		return ""
// 	}
// 	return res
// }

// 获取当前穿戴的头像挂件
func GetUserCurWearedAvatarPendantIcon(c context.Context, intlOpenid string) (avatatPendant string, err error) {
	if intlOpenid == "" {
		return "", nil
	}
	userAvatarPendant, err := GetUserCurWearedAvatarPendant(c, intlOpenid)
	if err != nil || userAvatarPendant == nil {
		return "", nil
	}
	return userAvatarPendant.Icon, nil
}

func GetUserCurWearedAvatarPendant(c context.Context, intlOpenid string) (userAvatatPendant *model.AvatarPendant, err error) {
	if intlOpenid == "" {
		return nil, nil
	}
	intlOpenidsKey := cache.GetUserWearAvatarPendantIntlOpenidsKey()
	// 先从缓存中获取
	userWearedAvatarPendantKey := cache.GetUserWearedAvatarPendantKey(intlOpenid)
	avatatPendantStr, err := redis.GetClient().Get(c, userWearedAvatarPendantKey).Result()
	if err == nil {
		if avatatPendantStr == "" {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant not wear, intlOpenid: %s, avatatPendantStr: %s", intlOpenid, avatatPendantStr)
			return nil, nil
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant from cache success, intlOpenid: %s, avatatPendantStr: %s", intlOpenid, avatatPendantStr)
		umErr := json.Unmarshal([]byte(avatatPendantStr), &userAvatatPendant)
		if umErr == nil {
			redis.GetClient().SAdd(c, intlOpenidsKey, intlOpenid)
			return userAvatatPendant, nil
		} else {
			// unmarshal 失败,可能是格式发生变化，重新生成
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, json.Unmarshal failed, intlOpenid: %s err: %v", intlOpenid, umErr)
			// return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetCurWearedCommentBubbleError, "get comment bubble failed")
		}
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetUserCurWearedAvatarPendant failed, redis.GetClient().Get failed, err: %v", err)
	}
	// db获取
	userWearedAvatarPendant, err := dao.GetWearedUserAvatarPendant(intlOpenid)
	if err != nil {
		// 没有有效穿戴
		if errors.Is(err, gorm.ErrRecordNotFound) {
			redis.GetClient().SetNX(c, userWearedAvatarPendantKey, "", 2*time.Minute)
			return nil, nil
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, dao.GetWearedUserAvatarPendant failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetAvatarPendantFailed, "get avatar pendant failed")
	}
	avatarPendant, err := dao.GetAvatarPendantInfo(userWearedAvatarPendant.AvatarPendantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			redis.GetClient().SetNX(c, userWearedAvatarPendantKey, "", 2*time.Minute)
			return nil, nil
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, dao.GetAvatarPendantInfo failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetAvatarPendantFailed, "get avatar pendant  failed")
	}

	avatarPendantByte, err := json.Marshal(avatarPendant)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, json.Marshal failed, intlOpenid: %s err: %v", intlOpenid, err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetAvatarPendantFailed, "get avatar pendant  failed")
	}
	cacheTime := int64(userWearedAvatarPendant.ValidEndAt - time.Now().Unix())
	if cacheTime <= 0 {
		cacheTime = 2 * 60
	} else if cacheTime > 24*60*60 {
		cacheTime = 24 * 60 * 60
	}
	_, err = redis.GetClient().SetEX(c, userWearedAvatarPendantKey, string(avatarPendantByte), time.Duration(cacheTime)*time.Second).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, redis.GetClient().SetEX failed, err: %v", err)
	} else {

		_, err := redis.GetClient().SAdd(c, intlOpenidsKey, intlOpenid).Result()
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, redis.GetClient().SAdd failed,intlOpenidsKey: %s, intlOpenid%s, err: %v", intlOpenidsKey, intlOpenid, err)
		}
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant success, intlOpenid: %s, avatarPendant: %s, id: %d", intlOpenid, string(avatarPendantByte), avatarPendant.ID)
	return avatarPendant, nil
}

// 获取用户认证称号描述多语言信息
func GetUserAuthDesc(c context.Context, intlOpenid string, authType int32, language string) string {
	if authType == 0 {
		authType = GetUserAuth(intlOpenid)
	}
	if authType == 0 {
		return ""
	}
	authDesc, err := GetUserAuthDescByCache(c, intlOpenid, authType, language)
	if err != nil || authDesc == "" {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetUserProfile GetUserAuthDescByCache: intlOpenid: %s, language: %s, err: %v\n", intlOpenid, language, err)
	} else {
		return authDesc
	}
	// 获取默认语言
	if language != "en" {
		if authDesc == "" {
			authDesc, err = GetUserAuthDescByCache(c, intlOpenid, authType, "en")
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetUserProfile GetUserAuthDescByCache: intlOpenid: %s, language: %s, err: %v\n", intlOpenid, language, err)
			}
		}
	}
	return authDesc
}

// 从缓存中获取数据，获取失败时，读db
func GetUserAuthDescByCache(c context.Context, IntlOpenid string, actionValue int32, language string) (string, error) {
	if IntlOpenid == "" || actionValue == 0 || language == "" {
		return "", nil
	}
	// 先从缓存总获取
	userAuthDescLanguage := cache.GetAuthLangugeDescKey(IntlOpenid, actionValue, language)
	res, err := redis.GetClient().Get(context.Background(), userAuthDescLanguage).Result()
	if err != nil || res == "" {
		// 从db中获取
		userPermissionLanguageItem, err := dao.GetUserPermissionLanguages(IntlOpenid, actionValue, language)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAuthDescByCache UserPermissionList error: %s", err.Error)
			return "", errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetUserTitleError, "get user permission language item error")
		}
		redis.GetClient().Set(context.Background(), userAuthDescLanguage, userPermissionLanguageItem.Desc, 0)
		return userPermissionLanguageItem.Desc, nil
	}
	return res, nil
}

// 获取认证用户信息
func GetCertificationUserLanguageInfo(c context.Context, intlOpenid string, language string) ([]*model.CertificationUserLanguage, error) {
	if intlOpenid == "" || language == "" {
		return nil, nil
	}
	rsp := []*model.CertificationUserLanguage{}
	certificationListKey := cache.GetCertificationUserLanguageListCacheKey(intlOpenid)
	contentRedisKey := cache.GetCertificationUserLanguageInfoCacheKey(intlOpenid, language)
	isInCache, err := redis.GetClient().SIsMember(c, certificationListKey, intlOpenid).Result()
	if err != nil && !errors.Is(err, redisClient.Nil) {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetCertificationUserLanguageInfo error: %s", err.Error)
	}
	// 可以从缓存中获取
	if isInCache {
		if contentRedisKey != "" {
			res, err := redis.GetClient().Get(context.Background(), contentRedisKey).Result()
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetCertificationUserLanguageInfo1 error: %s", err.Error)
			} else {
				err := json.Unmarshal([]byte(res), rsp)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetCertificationUserLanguageInfo2 error: %s", err.Error)
				} else {
					// 缓存有数据直接返回
					return rsp, nil
				}
			}
		}
	}
	// 不能从缓存中获取数据，从db中获取
	rsp, err = dao.GetCertificationUserLanguages(intlOpenid, language, constants.GAMEID_30054, constants.AREAID_global)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetCertificationUserLanguageInfo error: %s", err.Error)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetCertificationUserLanguageFailed, "get CertificationUserLanguageInfo failed")
	}
	// 写入缓存
	rspStr, err := json.Marshal(rsp)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetCertificationUserLanguageInfo error: %s", err.Error)
	} else {
		redis.GetClient().Set(c, contentRedisKey, rspStr, 0)
		redis.GetClient().SAdd(c, certificationListKey, intlOpenid)
	}
	return rsp, nil
}
