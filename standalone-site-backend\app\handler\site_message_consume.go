package handler

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/IBM/sarama"
	"trpc.publishing_application.standalonesite/app/common"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/message"
	"trpc.publishing_application.standalonesite/app/model"
)

func SiteMessageConsumer(ctx context.Context, msgArray []*sarama.ConsumerMessage) (err error) {
	var handlers []func() error
	// TODO 如果消息积压超过一小时就告警
	// msgArray.Timestamp
	for _, v := range msgArray {
		value := string(v.Value)
		handlers = append(handlers, func() error {
			err := ConsumeSiteMessage(common.GetCommonCtx(), value)
			return err
		})
	}
	err = trpc.GoAndWait(handlers...)
	return
}

func ConsumeSiteMessage(ctx context.Context, sendStr string) (err error) {
	defer recovery.CatchGoroutinePanic(context.Background())
	siteMsgData := &model.SiteMessageKafkaData{}
	if err = json.Unmarshal([]byte(sendStr), siteMsgData); err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("failed to get kafka user site message req,err:%v\n", err)
		// 消费kafka数据异常失败的，则直接告警，错误数据记录db，不返回错误，让kafka消息队列继续往下消费
		return nil
	}
	err = message.SiteMessageConsumeHandler(ctx, siteMsgData)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("message.SiteMessageConsumeHandler failed,err:%v\n", err)
		// 消费kafka数据异常失败的，则直接告警，错误数据记录db，不返回错误，让kafka消息队列继续往下消费
		return nil
	}
	// 临时加上，用于解决全量推送，cpu打满的问题
	time.Sleep(1 * time.Second)
	return nil
}
