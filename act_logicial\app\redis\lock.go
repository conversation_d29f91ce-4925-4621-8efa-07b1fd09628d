package redis

import (
	"context"
	redis_v8 "github.com/go-redis/redis/v8"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// LockByKey 加锁
func LockByKey(ctx context.Context, key string, lockTime int64) bool {
	res, err := redis.GetClient().SetNX(ctx, key, 1, time.Second*time.Duration(lockTime)).Result()
	if err != nil || !res {
		//log.ErrorContextf(ctx, "LockBy<PERSON><PERSON> fail, err: %v, key: %v, res: %v",
		//	err, key, res)
		return false
	}
	return true
}

// UnLockByKey 解锁
func UnLockByKey(ctx context.Context, key string) error {
	_, err := redis.GetClient().Del(ctx, key).Result()
	if err != nil {
		log.ErrorContextf(ctx, "UnLockByKey fail, err: %v, key: %v",
			err, key)
		return err
	}
	return nil
}

// CheckLockExists 检查锁是否存在
func CheckLockExists(ctx context.Context, key string) (exists bool, err error) {
	_, err = redis.GetClient().Get(ctx, key).Int()
	if redis_v8.Nil == err {
		return false, nil
	}
	if err != nil {
		return
	}
	return true, nil
}

// GetKeyTtl 获取锁的剩余时间
func GetKeyTtl(ctx context.Context, key string) (ttl int64, err error) {
	timeLeft, err := redis.GetClient().TTL(ctx, key).Result()
	if redis_v8.Nil == err {
		return 0, nil
	}
	if err != nil {
		return
	}
	ttl = timeLeft.Milliseconds() / 1000
	return ttl, nil
}
