package lip_points

import (
	"context"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_lip_points"
	"trpc.act.logicial/app/logic/lip"
)

// ReissuePointsImpl 结构
type ReissuePointsImpl struct {
	pb.UnimplementedLipPoints
}

// ReissueUserPoints 补发用户积分
func (s *ReissuePointsImpl) ReissueUserPoints(c context.Context, req *pb.ReissueUserPointsReq) (
	*pb.ReissueUserPointsRsp, error) {

	ctx := context.Background()
	err := lip.ReissueUserPoints(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}
	return &pb.ReissueUserPointsRsp{}, nil
}
