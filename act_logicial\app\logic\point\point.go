// Package point TODO
package point

import (
	"context"
	"fmt"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	configModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_point"
	"gorm.io/gorm"
	model "trpc.act.logicial/app/model/point"
)

// AdjustUserPoints 调整用户积分
func AdjustUserPoints(ctx context.Context, actionType string, point int32, tag string,
	FsourceId string) (totalPoint int32, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	logTableName, err := GetUserPointsLogTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	logData := &model.UserPointLog{
		FsourceId:  FsourceId,
		Point:      point,
		Uid:        userAccount.Uid,
		ActionType: actionType,
		Tag:        tag,
	}
	// 日志表添加记录
	db := DB.DefaultConnect().WithContext(ctx).Table(logTableName).Save(&logData)
	if db.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}

	totalTableName, err := GetUserPointsTotalTableName(ctx, FsourceId)
	if err != nil {
		return
	}
	totalData := &model.UserPointTotal{
		Uid:       userAccount.Uid,
		FsourceId: FsourceId,
		Point:     point,
	}
	db = DB.DefaultConnect().WithContext(ctx).Table(totalTableName).Where(
		"Fsource_id = ? and uid = ?", FsourceId, userAccount.Uid,
	).First(&totalData)
	if db.Error != nil {
		if db.Error == gorm.ErrRecordNotFound {
			// 创建记录
			db = DB.DefaultConnect().WithContext(ctx).Table(totalTableName).Create(&totalData)
			if db.Error != nil {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", db.Error)
				return
			}
			return
		}
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}

	if actionType == "add" {
		totalData.Point += point
	}

	if actionType == "sub" {
		totalData.Point -= point
	}

	totalPoint = totalData.Point

	db = DB.DefaultConnect().WithContext(ctx).Table(totalTableName).Save(&totalData)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}

	return
}

// GetUserPointsList 获取积分列表
func GetUserPointsList(ctx context.Context, FsourceId string, tag string, pageSize int32,
	pageNum int32) (potionsList []*pb.UserPointsItem, total int32, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	where := &model.UserPointLog{
		FsourceId: FsourceId,
		Uid:       userAccount.Uid,
	}
	if tag != "" {
		where.Tag = tag
	}

	logTableName, err := GetUserPointsLogTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	potionsDBList := make([]*model.UserPointLog, 0)
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(logTableName).Where(where).Limit(int(pageSize)).
		Offset(int((pageNum - 1) * pageSize)).Find(&potionsDBList)
	if db.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	for _, item := range potionsDBList {
		potionsList = append(potionsList, &pb.UserPointsItem{
			CreatedAt:  fmt.Sprintf("%v", item.CreatedAt),
			Point:      item.Point,
			ActionType: item.ActionType,
			Tag:        item.Tag,
		})
	}
	return
}

// GetUserPointsTotal 获取用户总积分
func GetUserPointsTotal(ctx context.Context, FsourceId string) (total int32, err error) {
	totalTableName, err := GetUserPointsTotalTableName(ctx, FsourceId)
	if err != nil {
		return
	}

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	totalData := &model.UserPointTotal{}

	db := DB.DefaultConnect().WithContext(ctx).Table(totalTableName).Where(
		"Fsource_id = ? and uid = ?", FsourceId, userAccount.Uid,
	).First(&totalData)
	if db.Error != nil {
		if db.Error == gorm.ErrRecordNotFound {
			return
		}
		// 报错,数据库操作异常
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error)
		return
	}
	total = totalData.Point
	return
}

// GetUserPointsLogTableName 获取积分日志表名
func GetUserPointsLogTableName(ctx context.Context, FsourceId string) (tableName string, err error) {
	tableName, err = configModel.GetTabNameWithSourceId(ctx, model.UserPointConf{}.TableName(), FsourceId,
		model.UserPointLog{}.TableName(), 100)
	if err != nil {
		return "", errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return tableName, nil
}

// GetUserPointsTotalTableName 获取总积分表名
func GetUserPointsTotalTableName(ctx context.Context, FsourceId string) (tableName string, err error) {
	tableName, err = configModel.GetTabNameWithSourceId(ctx, model.UserPointConf{}.TableName(), FsourceId,
		model.UserPointTotal{}.TableName(), 100)
	if err != nil {
		return "", errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return tableName, nil
}
