package socialmedia

import (
	"context"
	"net/url"
	"strings"
	"trpc.publishing_application.standalonesite/app/logic/attachment"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"google.golang.org/api/option"
	"google.golang.org/api/youtube/v3"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
)

func ParseVideoUrl(c context.Context, req *pb.GetVideoInfoByURLReq, gameId, areaId string) (*pb.GetVideoInfoByURLRsp, error) {
	rsp := &pb.GetVideoInfoByURLRsp{}
	var err error

	u, err := url.Parse(req.VideoUrl)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("ParseVideoUrl | Failed to parse video url: %s, platform: %s", req.VideoUrl, req.Platform)
		return nil, errs.NewCustomError(c, code.ParseVideoUrlError, "The video link is illegal and the parsing is abnormal.")
	}
	if req.Platform == constants.VideoYoutube {
		/*	Youtube 视频链接类型：
			- https://youtu.be/Fo00RhjuowE
			- https://www.youtube.com/shorts/unp4aasi27A
			- https://youtube.com/shorts/M_TOtzBpIkA?feature=share
			- https://www.youtube.com/watch?v=7Wbm97-EWiM
		*/
		rsp.Platform = constants.VideoYoutube
		params := u.Query()
		youtubeVideoId := params.Get("v")
		if youtubeVideoId == "" {
			pathSegments := strings.Split(strings.Trim(u.Path, "/"), "/")
			if len(pathSegments) > 0 {
				youtubeVideoId = pathSegments[len(pathSegments)-1]
			}
			if len(pathSegments) > 1 && pathSegments[len(pathSegments)-2] == "shorts" {
				rsp.Platform = constants.VideoYoutubeShort
			}
		}

		videoIds := []string{youtubeVideoId}
		youtubeInfos, err := GetVideoInfoBatch(c, videoIds)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("ParseVideoUrl | GetVideoInfoBatch error:%v, video url: %s, platform: %s", err, req.VideoUrl, req.Platform)
			return nil, errs.NewCustomError(c, code.GetVideoUrlError, "The video ID is illegal and the parsing is abnormal.")
		}
		if len(youtubeInfos) == 0 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("ParseVideoUrl | GetVideoInfoBatch error:%v, video url: %s, platform: %s", err, req.VideoUrl, req.Platform)
			return nil, errs.NewCustomError(c, code.ParseVideoUrlEmpty, "The video parsing content is empty. Video link parsing exception")
		}
		youtubeInfo := youtubeInfos[youtubeVideoId]
		rsp.VideoId = youtubeInfo.Id
		rsp.VideoTitle = youtubeInfo.Snippet.Title
		rsp.VideoDesc = youtubeInfo.Snippet.Description
		rsp.VideoCover = youtubeInfo.Snippet.Thumbnails.High.Url

		if rsp.VideoCover != "" {
			rsp.VideoCover = attachment.TransferVideoImage(c, 3, rsp.VideoCover, gameId, areaId)
		}
	}

	return rsp, nil
}

// 根据youtube的videoIds数组，批量查询youtube视频详情列表
func GetVideoInfoBatch(ctx context.Context, videoIds []string) (map[string]*youtube.Video, error) {
	apiKey := config.GetConfig().GoogleConf.APIKey
	ys, err := youtube.NewService(ctx, option.WithAPIKey(apiKey))
	if err != nil {
		return nil, err
	}

	// 一次传递的vid不能过多，进行分批
	retMap := make(map[string]*youtube.Video)
	for start := 0; start < len(videoIds); start += 10 {
		end := start + 10
		if end > len(videoIds) {
			end = len(videoIds)
		}

		vList, err := ys.Videos.List([]string{"contentDetails", "status", "snippet", "statistics"}).
			Id(videoIds[start:end]...).Do()

		reportParam := report.SocialReportData{
			ProjectID: 83,
			GameID:    16,
			Channel:   "YouTube",
			Api:       "Videos.List",
			ApiDesc:   "返回与 API 请求参数匹配的视频列表",
			ReqType:   1,
			RetCode:   0,
			ErrMsg:    "",
			Env:       trpc.GlobalConfig().Global.EnvName,
		}
		if err != nil {
			reportParam.RetCode = 500
			reportParam.ErrMsg = err.Error()
			go report.SocialReport(reportParam)
			return nil, err
		} else {
			go report.SocialReport(reportParam)
		}
		for _, v := range vList.Items {
			retMap[v.Id] = v
		}
	}
	// TODO 获取视频图片之后，最好加上图片转存逻辑，将图片下载后上传到自己的cos桶，避免跨域、限频等问题
	return retMap, nil
}
