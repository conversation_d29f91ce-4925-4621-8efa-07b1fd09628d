// Package record 记录
package record

import (
	"trpc.act.logicial/app/model"
)

// TableName .
func (RecordConfig) TableName() string {
	return "record_config"
}

// RecordConfig 查询条件
type RecordConfig struct {
	ID         int64  `gorm:"type:int(11);column:id;primary_key"`
	StorageKey string `gorm:"type:varchar(255);column:storage_key;not null"`
	FsourceID  string `gorm:"type:varchar(300);column:Fsource_id;not null"`
}

// TableName 表明
func (Record) TableName() string {
	return "record_log"
}

// Record invitation_log_$id表
type Record struct {
	model.AppModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(64);column:uid"` // 用户ID
	AccountType int32  `gorm:"type:tinyint(4);column:account_type;0"`
	FsourceId   string `gorm:"type:varchar(255);column:Fsource_id;not null"` // 页面id
}
