package user

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/trpcprotocol/publishing_application/stand_alone_site_game_game"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/constants"
)

var (
	siteGameProxy = stand_alone_site_game_game.NewGameClientProxy()
)

// 用户角色

func GetUserRoleInfo(c context.Context, intlOpenid []string) (map[string]*pbUser.UserRoleInfoItem, error) {
	result, err := siteGameProxy.BatchSelectSavedRoleInfoByUIDList(c, &stand_alone_site_game_game.BatchSelectSavedRoleInfoByUIDListReq{Uid: intlOpenid})
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserRoleInfo | get user role info failed, user_intl_openids: %v, err: %v", intlOpenid, err)
		return nil, err
	}
	var userRoles = make(map[string]*pbUser.UserRoleInfoItem)

	for _, item := range result.UserSavedRoleList {
		if item.HasSaved {
			userRoles[item.Uid] = &pbUser.UserRoleInfoItem{
				GameId:   item.RoleInfo.GameId,
				AreaId:   item.RoleInfo.AreaId,
				ZoneId:   item.RoleInfo.ZoneId,
				PlatId:   item.RoleInfo.PlatId,
				RoleId:   item.RoleInfo.RoleId,
				RoleName: item.RoleInfo.RoleName,
				GameName: item.RoleInfo.GameName,
			}
		}

	}
	return userRoles, nil
}
