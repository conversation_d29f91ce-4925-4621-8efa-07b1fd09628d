package cag_tmp

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/constant"
	"trpc.act.logicial/app/service"
	"trpc.act.logicial/app/viewmodel"
)

// checkAbRoleLevel 检查AB游戏角色等级是否满足抽奖条件
func checkAbRoleLevel(ctx context.Context, userId string, allowLevel int) error {
	// 查询用户的AB游戏用户id
	abOpenId, err := service.GetGameOpenId(ctx, constant.GAMEID_AB)
	if nil != err {
		log.ErrorContextf(ctx, "checkAbRoleLevel get ab game openid error:%v", err)
		return err
	}
	if "" == abOpenId {
		log.ErrorContextf(ctx, "checkAbRoleLevel ab openid is empty")
		return code.ErrAbGameNotBind
	}
	areaId := ""
	id, ok := config.GetConfig().GameAreaIds[constant.GAMEID_AB]
	if ok {
		areaId = cast.ToString(id)
	}

	// 查询ab游戏用户角色等级
	gameInfo := &viewmodel.AbUserGameInfoItem{}
	err = service.GetUserGameInfoByOpenid(ctx, constant.GAMEID_AB, constant.IDIP_CMD_AB_GET_USER_INFO, areaId, abOpenId, gameInfo)
	if code.ErrHasNoGameRole == err {
		log.DebugContextf(ctx, "checkAbRoleLevel ab openid has no game role, userId: %v, abOpenId: %v",
			userId, abOpenId)
		return code.ErrAbGameNotBind
	} else if nil != err {
		log.ErrorContextf(ctx, "checkAbRoleLevel get ab game user info error:%v, userId: %v, abOpenId: %v",
			err, userId, abOpenId)
		return err
	}
	if gameInfo.CreateTime == "" {
		log.ErrorContextf(ctx, "checkAbRoleLevel ab game user create time is empty, userId: %v, abOpenId: %v",
			userId, abOpenId)
		return code.ErrAbGameNotBind
	}
	if gameInfo.Level == "" {
		log.ErrorContextf(ctx, "checkAbRoleLevel ab game user level is empty, userId: %v, abOpenId: %v",
			userId, abOpenId)
		return code.ErrAbGameRoleLevelTooLow
	}
	level := cast.ToInt(gameInfo.Level)
	if level < allowLevel {
		log.ErrorContextf(ctx, "checkAbRoleLevel ab game user level too low, userId: %v, abOpenId: %v, level: %v",
			userId, abOpenId, gameInfo.Level)
		return code.ErrAbGameRoleLevelTooLow
	}
	return nil
}

// checkCagRoleRegTime 检查CAG游戏角色注册时间是否满足抽奖条件
func checkCagRoleRegTime(ctx context.Context, gameInfo *viewmodel.CagUserGameInfoItem, userId string, cagOpenId string) error {
	// 判断创建角色时间是否在活动开始之后
	if gameInfo.CreateTime == "" || gameInfo.CreateTime == "0" {
		// TODO 告警
		log.ErrorContextf(ctx, "checkCagRoleRegTime cag game user create time is empty, userId: %v, cagOpenId: %v",
			userId, cagOpenId)
		return code.ErrHasCagRole
	}

	actStartTime := config.GetConfig().CagNewRoleAct.ActStartTimeStamp
	if cast.ToInt64(gameInfo.CreateTime) < actStartTime {
		log.ErrorContextf(ctx, "checkCagRoleRegTime cag user has role before act, userId: %v, cagOpenId: %v, "+
			"createTime: %v, actStartTime: %v",
			userId, cagOpenId, gameInfo.CreateTime, actStartTime)
		return code.ErrHasCagRole
	}
	return nil
}
