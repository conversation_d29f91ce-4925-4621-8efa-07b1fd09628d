package hoktmp

import (
	"context"
	"strconv"
	"trpc.act.logicial/app/config"

	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_hok_tmp"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/logic/hoktmp"
	"trpc.act.logicial/app/util"
)

// HOKMotorcycleLuckyNumberCount 我的幸运号码数量
func (s *HokTmpImpl) HOKMotorcycleLuckyNumberCount(ctx context.Context,
	req *pb.HOKMotorcycleLuckyNumberCountReq) (
	*pb.HOKMotorcycleLuckyNumberCountRsp, error) {
	count, err := hoktmp.HOKMotorcycleLuckyNumberCount(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.HOKMotorcycleLuckyNumberCountRsp{
		Count: int32(count),
	}, nil
}

// HOKMotorcycleLuckyNumberUsedCount 已使用幸运号码数量
func (s *HokTmpImpl) HOKMotorcycleLuckyNumberUsedCount(ctx context.Context,
	req *pb.HOKMotorcycleLuckyNumberUsedCountReq) (
	*pb.HOKMotorcycleLuckyNumberUsedCountRsp, error) {
	usedCount, err := hoktmp.HOKMotorcycleLuckyNumberUsedCount(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.HOKMotorcycleLuckyNumberUsedCountRsp{
		Count: int32(usedCount),
	}, nil
}

// GetRedeemedCommodityQuantities 商品已兑换数量列表
func (s *HokTmpImpl) GetRedeemedCommodityQuantities(ctx context.Context, req *pb.GetRedeemedCommodityQuantitiesReq) (
	*pb.GetRedeemedCommodityQuantitiesRsp, error) {
	quantities, err := hoktmp.GetRedeemedCommodityQuantities(ctx, 0)
	if err != nil {
		return nil, err
	}
	commodityItems := make([]*pb.RedeemedCommodityItem, 0, len(quantities))
	for _, v := range quantities {
		commodityItems = append(commodityItems, &pb.RedeemedCommodityItem{
			CommodityId:        strconv.Itoa(int(v.CommodityID)),
			RedeemedQuantities: int32(v.Count),
			IsOwned:            v.Owned,
		})
	}
	return &pb.GetRedeemedCommodityQuantitiesRsp{
		CommodityRedeemedQuantitiesList: commodityItems,
	}, nil
}

// CommodityExchange 商品兑换
func (s *HokTmpImpl) CommodityExchange(ctx context.Context, req *pb.CommodityExchangeReq) (
	*pb.CommodityExchangeRsp, error) {
	encryptId, err := hoktmp.CommodityExchange(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.CommodityExchangeRsp{
		EncryptId: encryptId,
	}, nil
}

// CommodityExchangeFailRollback 失败回滚
func (s *HokTmpImpl) CommodityExchangeFailRollback(ctx context.Context, req *pb.CommodityExchangeFailRollbackReq) (
	*pb.CommodityExchangeFailRollbackRsp, error) {
	if err := hoktmp.CommodityExchangeFailRollback(ctx, req.EncryptId); err != nil {
		return nil, err
	}
	return &pb.CommodityExchangeFailRollbackRsp{}, nil
}

// HOKMotorcycleInsertLuckyConfigData 生成幸运号码主表
func (s *HokTmpImpl) HOKMotorcycleInsertLuckyConfigData(ctx context.Context,
	req *pb.HOKMotorcycleInsertLuckyConfigDataReq) (
	*pb.HOKMotorcycleInsertLuckyConfigDataRsp, error) {
	if err := hoktmp.HOKMotorcycleInsertLuckyConfigData(ctx); err != nil {
		return nil, err
	}
	return &pb.HOKMotorcycleInsertLuckyConfigDataRsp{}, nil
}

// HOKMotorcycleInsertLuckyLogData 根据传入号段 生成幸运号码子表数据
func (s *HokTmpImpl) HOKMotorcycleInsertLuckyLogData(ctx context.Context,
	req *pb.HOKMotorcycleInsertLuckyLogDataReq) (
	*pb.HOKMotorcycleInsertLuckyLogDataRsp, error) {

	c := context.Background()
	if err := hoktmp.HOKMotorcycleInsertLuckyLogData(c, req.NumberSegment, req.Number); err != nil {
		return nil, err
	}
	return &pb.HOKMotorcycleInsertLuckyLogDataRsp{}, nil
}

// HOKMotorcycleTaskCompleteStatus 北极星IP地址获取测试
func (s *HokTmpImpl) HOKMotorcycleTaskCompleteStatus(ctx context.Context, req *pb.HOKMotorcycleTaskCompleteStatusReq) (
	*pb.HOKMotorcycleTaskCompleteStatusRsp, error) {

	// if err := hoktmp.motorcycleTaskCompleteStatus(ctx, ""); err != nil {
	//	return nil, err
	// }
	//port, err := util.GetServer("micro-service-test-tasksvr1", "Production")
	host := config.GetConfig().PolarisConf.Host
	log.WithFieldsContext(ctx, "log_type", "star_debug", "m", "HOKMotorcycleTaskCompleteStatus").Infof(
		"HOKMotorcycleTaskCompleteStatus star conf host:[%v]", host)
	port, err := util.GetServer("trpc.sgamesvr.sgamerec_gateway.http", "Test")
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "HOKMotorcycleTaskCompleteStatus").Infof(
			"HOKMotorcycleTaskCompleteStatus err:[%v]", err)
	}
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "HOKMotorcycleTaskCompleteStatus").Infof(
		"HOKMotorcycleTaskCompleteStatus show rsp:[%v], err:[%v]", port, err)

	return &pb.HOKMotorcycleTaskCompleteStatusRsp{}, nil
}

// HOKMotorcycleTaskHasFinish 获取任务完成状态
func (s *HokTmpImpl) HOKMotorcycleTaskHasFinish(ctx context.Context, req *pb.HOKMotorcycleTaskHasFinishReq) (
	*pb.HOKMotorcycleTaskHasFinishRsp, error) {

	if err := hoktmp.HOKMotorcycleTaskHasFinish(ctx, req.TaskId); err != nil {
		return nil, err
	}
	return &pb.HOKMotorcycleTaskHasFinishRsp{}, nil
}

// HOKMotorcycleDoneTaskList 获取已完成任务列表
func (s *HokTmpImpl) HOKMotorcycleDoneTaskList(ctx context.Context, req *pb.HOKMotorcycleDoneTaskListReq) (
	*pb.HOKMotorcycleDoneTaskListRsp, error) {

	doneList, err := hoktmp.HOKMotorcycleDoneTaskList(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.HOKMotorcycleDoneTaskListRsp{
		DoneTask_IdList: doneList,
	}, nil
}

// HOKMotorcycleTaskOneClickCollection 摩托车任务一键领取
func (s *HokTmpImpl) HOKMotorcycleTaskOneClickCollection(ctx context.Context,
	req *pb.HOKMotorcycleTaskOneClickCollectionReq) (
	*pb.HOKMotorcycleTaskOneClickCollectionRsp, error) {

	taskOneClickCollection, err := hoktmp.HOKMotorcycleTaskOneClickCollection(ctx, req.FsourceId, req.TimeZone)
	if err != nil {
		return nil, err
	}
	return &pb.HOKMotorcycleTaskOneClickCollectionRsp{
		GetLuckyNumList: taskOneClickCollection,
	}, nil
}

// ScheduledDrawInfo 摩托车定时查询当天开奖信息
func (s *HokTmpImpl) ScheduledDrawInfo(ctx context.Context, req *pb.ScheduledDrawInfoReq) (
	*pb.ScheduledDrawInfoRsp, error) {

	winPrize, luckyNumber, err := hoktmp.ScheduledDrawInfo(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.ScheduledDrawInfoRsp{
		LotteryInformationList: []*pb.LotteryInformation{
			{
				LuckyNum:      luckyNumber,
				IsPrizeWinner: winPrize,
			},
		},
	}, nil
}

// QueryCDKRedemptionListPage 摩托车分页查询CDK兑换列表
func (s *HokTmpImpl) QueryCDKRedemptionListPage(ctx context.Context, req *pb.QueryCDKRedemptionListPageReq) (
	*pb.QueryCDKRedemptionListPageRsp, error) {

	listPage, count, err := hoktmp.QueryCDKRedemptionListPage(ctx, int(req.PageNum), int(req.PageSize))
	if err != nil {
		return nil, err
	}
	infos := make([]*pb.CdkRedemptionInfo, 0, len(listPage))
	for _, v := range listPage {
		infos = append(infos, &pb.CdkRedemptionInfo{
			Cdk:       v.Remark,
			LuckyNum:  v.LuckyNum,
			Timestamp: cast.ToString(v.CreatedAt),
		})
	}
	return &pb.QueryCDKRedemptionListPageRsp{
		CdkRedemptionList: infos,
		Total:             count,
	}, nil
}

// HOKMotorcycleLucyNumList 摩托车我的幸运号码列表
func (s *HokTmpImpl) HOKMotorcycleLucyNumList(ctx context.Context, req *pb.HOKMotorcycleLucyNumListReq) (
	*pb.HOKMotorcycleLucyNumListRsp, error) {

	rsp, err := hoktmp.HOKMotorcycleLucyNumList(ctx, int(req.PageNum), int(req.PageSize))
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// CheckIfDrawWonGrandPrize 摩托车开奖是否中大奖
func (s *HokTmpImpl) CheckIfDrawWonGrandPrize(ctx context.Context, req *pb.CheckIfDrawWonGrandPrizeReq) (
	*pb.CheckIfDrawWonGrandPrizeRsp, error) {

	isWinner, err := hoktmp.CheckIfDrawWonGrandPrize(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CheckIfDrawWonGrandPrizeRsp{
		IsWinner: isWinner,
	}, nil
}

// CheckIfLastDrawWonGrandPrize 上一次开奖是否中大奖
func (s *HokTmpImpl) CheckIfLastDrawWonGrandPrize(ctx context.Context, req *pb.CheckIfLastDrawWonGrandPrizeReq) (
	*pb.CheckIfLastDrawWonGrandPrizeRsp, error) {

	err := hoktmp.CheckIfLastDrawWonGrandPrize(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CheckIfLastDrawWonGrandPrizeRsp{}, nil
}

// GrantLuckyNumberByInvitationCode 摩托车根据邀请码发放幸运号码
func (s *HokTmpImpl) GrantLuckyNumberByInvitationCode(ctx context.Context,
	req *pb.GrantLuckyNumberByInvitationCodeReq) (
	*pb.GrantLuckyNumberByInvitationCodeRsp, error) {

	luckyNumbers, err := hoktmp.GrantLuckyNumberByInvitationCode(ctx, req.ShareCode, req.FsourceId)
	if err != nil {
		return nil, err
	}
	return &pb.GrantLuckyNumberByInvitationCodeRsp{
		GetLuckyNumList: luckyNumbers,
	}, nil
}

// SendLuckyNum 发放幸运号码
func (s *HokTmpImpl) SendLuckyNum(ctx context.Context, req *pb.SendLuckyNumReq) (
	*pb.SendLuckyNumRsp, error) {

	luckyNumbers, err := hoktmp.SendLuckyNumByTask(ctx, int(req.SendCount), cast.ToString(req.TaskId))
	if err != nil {
		return nil, err
	}
	return &pb.SendLuckyNumRsp{
		GetLuckyNumList: luckyNumbers,
	}, nil
}

// SendLuckyNumByCDK 发放幸运号码
func (s *HokTmpImpl) SendLuckyNumByCDK(ctx context.Context, req *pb.SendLuckyNumByCDKReq) (
	*pb.SendLuckyNumByCDKRsp, error) {

	luckyNumbers, err := hoktmp.SendLuckyNumByCDK(ctx, int(req.SendCount), req.Cdkey)
	if err != nil {
		return nil, err
	}
	return &pb.SendLuckyNumByCDKRsp{
		GetLuckyNumList: luckyNumbers,
	}, nil
}

// ScheduledTaskLottery 定时开奖
func (s *HokTmpImpl) ScheduledTaskLottery(ctxOld context.Context, req *pb.ScheduledTaskLotteryReq) (
	*pb.ScheduledTaskLotteryRsp, error) {
	return nil, nil
	ctx := context.Background()
	err := hoktmp.ScheduledTaskLottery(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.ScheduledTaskLotteryRsp{}, nil
}

// GetPreviousWinningRecords 查询往期中奖记录
func (s *HokTmpImpl) GetPreviousWinningRecords(ctx context.Context, req *pb.GetPreviousWinningRecordsReq) (
	*pb.GetPreviousWinningRecordsRsp, error) {

	records, err := hoktmp.GetPreviousWinningRecords(ctx, int(req.PageNum), int(req.PageSize))
	if err != nil {
		return nil, err
	}
	return records, nil
}

// AllWinningRecordList 跑马灯信息
func (s *HokTmpImpl) AllWinningRecordList(ctx context.Context, req *pb.AllWinningRecordListReq) (
	*pb.AllWinningRecordListRsp, error) {

	records, err := hoktmp.GetHomepageMarqueeData(ctx)
	if err != nil {
		return nil, err
	}
	return records, nil
}

// FetchLotteryInfoAndCheckMarquee 检查抽奖礼包写入跑马灯
func (s *HokTmpImpl) FetchLotteryInfoAndCheckMarquee(ctx context.Context, req *pb.FetchLotteryInfoAndCheckMarqueeReq) (
	*pb.FetchLotteryInfoAndCheckMarqueeRsp, error) {

	err := hoktmp.FetchLotteryInfoAndCheckMarquee(ctx, req.PresentData)
	if err != nil {
		return nil, err
	}
	return &pb.FetchLotteryInfoAndCheckMarqueeRsp{}, nil
}

// HOKMotorcycleCdkAlreadyRedeemedByUser 检查CDK用户是否已使用
func (s *HokTmpImpl) HOKMotorcycleCdkAlreadyRedeemedByUser(ctx context.Context,
	req *pb.HOKMotorcycleCdkAlreadyRedeemedByUserReq) (
	*pb.HOKMotorcycleCdkAlreadyRedeemedByUserRsp, error) {

	err := hoktmp.HOKMotorcycleCdkAlreadyRedeemedByUser(ctx, req.Cdkey)
	if err != nil {
		return nil, err
	}
	return &pb.HOKMotorcycleCdkAlreadyRedeemedByUserRsp{}, nil
}
