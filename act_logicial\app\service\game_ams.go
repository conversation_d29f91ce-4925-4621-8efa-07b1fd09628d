package service

import (
	"context"
	"fmt"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gameAmsPb "git.code.oa.com/trpcprotocol/publishing_marketing/game_ams"
	"google.golang.org/protobuf/proto"
	"reflect"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/constant"
)

// GetUserGameInfoByOpenid 通过openid获取游戏用户信息
func GetUserGameInfoByOpenid(ctx context.Context, gameId, cmdId, areaId, openId string, rspInfo interface{}) (err error) {
	// 类型检查
	if reflect.ValueOf(rspInfo).Kind() != reflect.Ptr ||
		reflect.Indirect(reflect.ValueOf(rspInfo)).Kind() != reflect.Struct {
		log.WithFieldsContext(ctx, "log_type", "rspInfo_type_err").Infof("rspInfo类型错误")
		return code.ErrSystemError
	}
	amsProxy := gameAmsPb.NewAmsClientProxy()

	//cag "cmd_id": "********",
	//ab "cmd_id": "********",

	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         fmt.Sprintf("%v-%v", gameId, openId),
		AccountType: accountPb.AccountType(1),
		IntlAccount: &accountPb.IntlAccount{
			OpenId: openId,
			GameId: gameId,
			//ChannelId: 131,
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	IdipParam := make([]*gameAmsPb.IdipGetItem, 0)
	if areaId != "" { // 如果有区服信息，否则不填，使用默认
		IdipParam = append(IdipParam, &gameAmsPb.IdipGetItem{
			Key:   "AreaId",
			Value: areaId,
		})
	}
	log.WithFieldsContext(ctx, "log_type", "GetUserGameInfoByOpenid").Infof("cmdId: %v, gameId: %v, areaId: %v", cmdId, gameId,
		areaId)
	amsInfo, err := amsProxy.GetInfoListByAms(ctx, &gameAmsPb.GetInfoListByAmsReq{
		SelectParam: &gameAmsPb.IdipDBParam{
			CmdId:  cmdId,
			GameId: gameId,
		},
		IdipParam: IdipParam,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "GetInfoListByAms_err", "str_field_1", openId).Infof("err: %v", err)
		errParse := errs.ParseError(ctx, err)
		if errParse.Code == constant.IDIP_GAME_HAS_NO_ROLE {
			log.DebugContextf(ctx, "accountIntlProxy.IsUserHasGameOpenid has no openid err:%v", err)
			return code.ErrHasNoGameRole
		}
		log.ErrorContextf(ctx, "accountIntlProxy.IsUserHasGameOpenid err:%v, gameId: %v", err, gameId)
		return
	}
	log.WithFieldsContext(ctx, "log_type", "GetInfoListByAms").Infof("amsInfo: %+v", amsInfo)

	// 使用反射自动填充结构体
	infoValue := reflect.ValueOf(rspInfo).Elem()
	infoType := infoValue.Type()

	for _, item := range amsInfo.Info {
		for i := 0; i < infoType.NumField(); i++ {
			field := infoType.Field(i)
			if field.Tag.Get("json") == item.Key {
				if fieldValue := infoValue.Field(i); fieldValue.CanSet() {
					fieldValue.SetString(item.Value)
				}
				break
			}
		}
	}
	return
}
