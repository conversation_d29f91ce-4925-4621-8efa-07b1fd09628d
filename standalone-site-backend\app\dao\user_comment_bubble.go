package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"trpc.publishing_application.standalonesite/app/model"
)

// 获取用户拥有的评论气泡
func GetUserOwnedCommentBubbleByUserId(intlOpenids []string, commentBubbleId int64) ([]*model.UserCommentBubble, error) {
	var userCommentBubbles []*model.UserCommentBubble
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserCommentBubble{}).TableName())
	err := tx.Where("intl_openid in ? and comment_bubble_id = ? and is_del = 0 and valid_end_at > ?", intlOpenids, commentBubbleId, time.Now().Unix()).Find(&userCommentBubbles).Error
	return userCommentBubbles, err
}

// 批量更新或创建用户评论气泡
func BatchUpdateOrCreateUserCommentPendants(userCommentBubble []*model.UserCommentBubble) error {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserCommentBubble{}).TableName())
	err := tx.Clauses((clause.OnConflict{
		Columns:   []clause.Column{{Name: "game_id"}, {Name: "area_id"}, {Name: "area_id"}, {Name: "comment_bubble_id"}, {Name: "intl_openid"}},
		DoUpdates: clause.AssignmentColumns([]string{"valid_begin_at", "valid_end_at", "creator", "updater", "modified_on", "deleted_on", "is_del", "deleted_on", "is_weared"}),
	})).Create(&userCommentBubble).Error
	return err
}

// 获取某个用户所有有效的气泡
func GetValidUserCommentBubbles(intlOpenid string, timestamp int64) ([]*model.UserCommentBubble, error) {
	// 获取用户未过期气泡
	userCommentBubbles := make([]*model.UserCommentBubble, 0)
	if intlOpenid == "" {
		return userCommentBubbles, nil
	}
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserCommentBubble{}).TableName())
	err := tx.Where("intl_openid = ? and is_del = 0 and valid_end_at > ? ", intlOpenid, timestamp).Find(&userCommentBubbles).Error
	return userCommentBubbles, err
}

func BatchUpdateOrCreateUserCommentBubbles(userCommentBubbles []*model.UserCommentBubble) error {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserCommentBubble{}).TableName())
	err := tx.Clauses((clause.OnConflict{
		Columns:   []clause.Column{{Name: "game_id"}, {Name: "area_id"}, {Name: "area_id"}, {Name: "comment_bubble_id"}, {Name: "intl_openid"}},
		DoUpdates: clause.AssignmentColumns([]string{"valid_begin_at", "valid_end_at", "creator", "updater", "modified_on", "deleted_on", "is_del", "deleted_on", "is_weared"}),
	})).Create(&userCommentBubbles).Error
	return err
}

func CancleWearUserCommentBubble(intlOpenid string, commentBubbleId int64) (err error) {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserCommentBubble{}).TableName())
	err = tx.Where("intl_openid = ? and comment_bubble_id = ? and is_del = 0", intlOpenid, commentBubbleId).Update("is_weared", 0).Error
	return err
}

func GetUserCommentBubble(intlOpenid string, commentBubbleId int64) (userCommentBubble *model.UserCommentBubble, err error) {
	// 获取用户未过期气泡
	if intlOpenid == "" {
		return userCommentBubble, nil
	}
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserCommentBubble{}).TableName())
	err = tx.Where("intl_openid = ? and comment_bubble_id = ? and is_del = 0", intlOpenid, commentBubbleId).First(&userCommentBubble).Error
	return userCommentBubble, err
}

// 获取当前穿戴的气泡
func GetWearedUserCommentBubble(intlOpenid string) (isWearedCommentBubble *model.UserCommentBubble, err error) {
	// 获取用户未过期气泡
	if intlOpenid == "" {
		return isWearedCommentBubble, nil
	}
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserCommentBubble{}).TableName())
	err = tx.Where("intl_openid = ? and is_weared = 1 and valid_end_at > ?", intlOpenid, time.Now().Unix()).First(&isWearedCommentBubble).Error
	return isWearedCommentBubble, err
}

// 设置穿戴气泡
func SetWearUserCommentBubble(intlOpenid string, commentBubbleId int64) (effectRow int64, err error) {
	curTimestamp := time.Now().Unix()
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserCommentBubble{}).TableName())
	// 获取当前已经穿戴的气泡
	isWearedCommentBubble, err := GetWearedUserCommentBubble(intlOpenid)
	// 如果当前穿戴的气泡和传入的气泡一致，则取消穿戴
	if commentBubbleId == isWearedCommentBubble.ID {
		return 1, nil
	}
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return 0, err
		}
	} else {
		// 取消穿戴
		tx.Where("id = ? ", isWearedCommentBubble.ID).Update("is_weared", 0)
	}
	// 穿戴
	tx = DB.SelectConnect("db_standalonesite").Table((&model.UserCommentBubble{}).TableName())
	updateRes := tx.Where("intl_openid = ? and comment_bubble_id = ? and is_del = 0 and valid_end_at > ?", intlOpenid, commentBubbleId, curTimestamp).Update("is_weared", 1)
	return updateRes.RowsAffected, updateRes.Error
}
