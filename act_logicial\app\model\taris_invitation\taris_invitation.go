// Package tarisinvitation 塔瑞斯人拉人 活动
package tarisinvitation

import "trpc.act.logicial/app/model"

// TarisInvitationModel Model
type TarisInvitationModel struct {
	model.AppModel
}

// TableName .
func (TarisInvitationModel) TableName() string {
	return "taris_invitation"
}

// TarisInvitation 临时结构
type TarisInvitation struct {
	TarisInvitationModel
	ID                 int64  `gorm:"type:int(11);column:id;primary_key"`
	UID                string `gorm:"type:varchar(255);column:uid;"`
	InvitationUID      string `gorm:"type:varchar(255);column:invitation_uid;"`
	Task1              int32  `gorm:"type:varchar(255);column:task_1;"`
	Task2              int32  `gorm:"type:varchar(255);column:task_2;"`
	Task3              int32  `gorm:"type:varchar(255);column:task_3;"`
	IsNewInstall       int32  `gorm:"type:varchar(255);column:is_newinstall;"`
	IsActive           int32  `gorm:"type:varchar(255);column:is_active;"`
	RoleName           string `gorm:"type:varchar(255);column:role_name;"`
	InvitationRoleName string `gorm:"type:varchar(255);column:invitation_role_name;"`
}

// TarisInvitationAsiaModel Model
type TarisInvitationAsiaModel struct {
	model.AppModel
}

// TableName .
func (TarisInvitationAsiaModel) TableName() string {
	return "taris_invitation_asia"
}

// TarisInvitationAsia 临时结构
type TarisInvitationAsia struct {
	TarisInvitationAsiaModel
	ID                 int64  `gorm:"type:int(11);column:id;primary_key"`
	UID                string `gorm:"type:varchar(255);column:uid;"`
	InvitationUID      string `gorm:"type:varchar(255);column:invitation_uid;"` // 被邀请人uid
	Task1              int32  `gorm:"type:varchar(255);column:task_1;"`
	Task2              int32  `gorm:"type:varchar(255);column:task_2;"`
	Task3              int32  `gorm:"type:varchar(255);column:task_3;"`
	IsNewInstall       int32  `gorm:"type:varchar(255);column:is_newinstall;"`
	IsActive           int32  `gorm:"type:varchar(255);column:is_active;"`
	RoleName           string `gorm:"type:varchar(255);column:role_name;"`
	InvitationRoleName string `gorm:"type:varchar(255);column:invitation_role_name;"`
}

// TarisInvitationRankAsiaModel Model
type TarisInvitationRankAsiaModel struct {
	model.AppModel
}

// TableName .
func (TarisInvitationRankAsiaModel) TableName() string {
	return "taris_invitation_temp_asia"
}

// TarisInvitationRankAsia Model 临时结构
type TarisInvitationRankAsia struct {
	TarisInvitationRankAsiaModel
	ID  int64  `gorm:"type:int(11);column:id;primary_key"`
	UID string `gorm:"type:varchar(255);column:uid;"`
	Num int64  `gorm:"type:int(11);column:num;"`
}

// TarisInvitationRankModel Model
type TarisInvitationRankModel struct {
	model.AppModel
}

// TableName .
func (TarisInvitationRankModel) TableName() string {
	return "taris_invitation_temp_asia"
}

// TarisInvitationRank Model 临时结构
type TarisInvitationRank struct {
	TarisInvitationRankModel
	ID  int64  `gorm:"type:int(11);column:id;primary_key"`
	UID string `gorm:"type:varchar(255);column:uid;"`
	Num int64  `gorm:"type:int(11);column:num;"`
}
