package tweet

import (
	"context"
	"encoding/json"
	"errors"
	"sync"
	"time"

	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/logic/formatted"

	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/util"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	redisClient "github.com/go-redis/redis/v8"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetTagList(c context.Context, req *pb.GetTagListReq, language string) (*pb.GetTagListRsp, error) {
	rsp := &pb.GetTagListRsp{
		List:     make([]*pb.GetTagRsp, 0),
		PageInfo: &pb.PageInfo{},
	}
	var err error
	var plateTags []*model.PlateTag
	var nextPageCursor, previousPageCursor string
	conditions := &dao.PlateTagConditions{
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
		PlateId: int64(req.PlateId),
	}
	if req.PageType == pb.PageType_NEXTPAGE {
		var idCursor int64
		// 如果是首页
		if req.NextPageCursor == "" {
			idCursor = 0
		} else {
			previousPageCursor = req.NextPageCursor
			idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
			}
			conditions.LtId = idCursor
		}
		plateTags, err = dao.GetPlateTagList(conditions, 10)
		if len(plateTags) > 0 {
			nextPageCursor, err = util.EncryptPageCursorI(plateTags[len(plateTags)-1].ID)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to create comments nextPageCursor")
			}
		}
		if len(plateTags) == 0 {
			rsp.PageInfo.IsFinish = true
			return rsp, nil
		}
	}
	var wg sync.WaitGroup
	var tagItem = make(chan *pb.GetTagRsp)
	for _, item := range plateTags {
		wg.Add(1)
		go func(plateTagItem *model.PlateTag, lang string) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done()

			tagInfo, err := dao.TagGet(&dao.TagConditions{
				Id: int64(plateTagItem.TagID),
				Order: []*dao.OrderConditions{
					&dao.OrderConditions{
						Column: "sort_num",
						IsDesc: false,
					},
				},
			})
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments GetCommentInfoByUUID err, tag_id:(%d), err=(%v)", plateTagItem.TagID, err)
				return
			}

			tagLanguages, err := dao.GetTagLanguageByTagId(tagInfo.ID)
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments GetCommentInfoByUUID err, tag_id:(%d), err=(%v)", plateTagItem.TagID, err)
				return
			}
			for _, tagLanguage := range tagLanguages {
				if tagLanguage.Language == lang {
					tagInfo.Language = tagLanguage
					break
				}
			}
			if tagInfo.Language == nil {
				for _, tagLanguage := range tagLanguages {
					if tagLanguage.Language == "en" {
						tagInfo.Language = tagLanguage
						break
					}
				}
			}
			tagItem <- &pb.GetTagRsp{
				Id:                tagInfo.ID,
				Type:              int32(tagInfo.Type),
				PicUrl:            tagInfo.PicURL,
				TagName:           tagInfo.Language.TagName,
				CommentNum:        int64(tagInfo.CommentNum),
				QuoteNum:          tagInfo.QuoteNum,
				ReadNum:           tagInfo.ReadNum,
				FansNum:           int64(tagInfo.FansNum),
				HotNum:            int64(tagInfo.HotNum),
				PowerNum:          int64(tagInfo.PowerNum),
				GameId:            tagInfo.GameID,
				AreaId:            tagInfo.AreaID,
				KudosNum:          int64(tagInfo.KudosNum),
				RecommendDeadline: tagInfo.RecommendDeadline,
			}

		}(item, language)
	}

	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		wg.Wait()
		close(tagItem)
	}()

	for item := range tagItem {
		rsp.List = append(rsp.List, item)
	}
	if len(rsp.List) == 0 || len(rsp.List) < int(req.Limit) {
		rsp.PageInfo.IsFinish = true
	} else {
		rsp.PageInfo.NextPageCursor = nextPageCursor
	}
	rsp.PageInfo.PreviousPageCursor = previousPageCursor
	return rsp, nil
}

func CheckTagIsExist(c context.Context, tagIds []int64, plateId int64) error {
	tags, err := dao.TagList(&dao.TagConditions{
		Ids: tagIds,
	}, 0, 0)
	if err != nil || len(tags) != len(tagIds) {
		return errs.NewCustomError(c, code.UpdatePostGetTagFailed, "CheckTagIsExist | The topic is illegal")
	}
	return nil
}

func GetTagListV2(c context.Context, req *pb.GetTagListReq, language string) (*pb.GetTagListRsp, error) {
	rsp := &pb.GetTagListRsp{
		List: make([]*pb.GetTagRsp, 0),
		PageInfo: &pb.PageInfo{
			IsFinish: true,
		},
	}
	var err error

	// 先获取缓存数据
	tagListRedisKey := cache.GetTagListKey(req.PlateId, req.NextPageCursor, req.Limit, language)
	tagListCacheInfo, err := redis.GetClient().Get(c, tagListRedisKey).Result()
	if err == nil {
		if tagListCacheInfo == "" {
			rsp.PageInfo.IsFinish = true
			return rsp, nil
		}
		err = json.Unmarshal([]byte(tagListCacheInfo), rsp)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetTagListV2 cache json.Unmarshal error.tagListRedisKey: %s, err: %v", tagListRedisKey, err)
			return nil, errs.NewCustomError(c, code.GettagsJsonUnmarshalError, "Failed to obtain tag list, data parsing exception")
		} else {
			return rsp, nil
		}
	}

	// 获取所有tag
	tagInfos, err := dao.TagList(&dao.TagConditions{}, 0, 0)
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetTagListV2 TagList err, err=(%v)", err)
		return rsp, err
	}

	// 获取所有tag多语言
	tagLanguageInfos, err := dao.TagLanguageList(&dao.TagLanguageConditions{}, 0, 0)
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetTagListV2 TagLanguageList err, err=(%v)", err)
		return rsp, err
	}

	if req.PlateId > 0 {
		var plateTags []*model.PlateTag
		conditions := &dao.PlateTagConditions{
			Order: []*dao.OrderConditions{
				&dao.OrderConditions{
					Column: "`order`",
					IsDesc: false,
				},
				&dao.OrderConditions{
					Column: "id",
					IsDesc: true,
				},
			},
			PlateId: int64(req.PlateId),
		}
		plateTags, err = dao.GetPlateTagList(conditions, 10)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetTagListV2 GetPlateTagList err, err=(%v)", err)
			return rsp, err
		}
		if len(plateTags) > 0 {
			for _, plateTag := range plateTags {
				tagItem := &pb.GetTagRsp{}
				for _, tagInfo := range tagInfos {
					if plateTag.TagID == int(tagInfo.ID) {
						tagItem.Id = tagInfo.ID
						tagItem.PicUrl = tagInfo.PicURL
						tagItem.Type = int32(tagInfo.Type)
					}
				}
				var hasName bool
				for _, tagLanguageInfo := range tagLanguageInfos {
					if plateTag.TagID == tagLanguageInfo.TagID && language == tagLanguageInfo.Language {
						tagItem.TagName = tagLanguageInfo.TagName
						hasName = true
						break
					}
				}
				if !hasName {
					for _, tagLanguageInfo := range tagLanguageInfos {
						if plateTag.TagID == tagLanguageInfo.TagID && "en" == tagLanguageInfo.Language {
							tagItem.TagName = tagLanguageInfo.TagName
						}
					}
				}
				rsp.List = append(rsp.List, tagItem)
			}

		}

	} else {

		for _, tagInfo := range tagInfos {
			tagItem := &pb.GetTagRsp{
				Id:     tagInfo.ID,
				PicUrl: tagInfo.PicURL,
				Type:   int32(tagInfo.Type),
			}
			var hasName bool
			for _, tagLanguageInfo := range tagLanguageInfos {
				if tagLanguageInfo.Language == language && tagLanguageInfo.TagID == int(tagInfo.ID) {
					tagItem.TagName = tagLanguageInfo.TagName
					hasName = true
				}
			}
			if !hasName {
				for _, tagLanguageInfo := range tagLanguageInfos {
					if tagInfo.ID == int64(tagLanguageInfo.TagID) && "en" == tagLanguageInfo.Language {
						tagItem.TagName = tagLanguageInfo.TagName
						break
					}
				}
			}
			rsp.List = append(rsp.List, tagItem)
		}
	}

	tagListRspByte, err := json.Marshal(rsp)
	if err == nil {
		redis.GetClient().SetEX(c, tagListRedisKey, string(tagListRspByte), 2*time.Minute).Result()
		getTagKeysCacheKey := cache.GetTagListKeysCacheKey()
		redis.GetClient().SAdd(c, getTagKeysCacheKey, tagListRedisKey)
	}

	return rsp, nil
}

func SearchTagList(c context.Context, req *pb.SearchTagReq, language string) (*pb.SearchTagRsp, error) {
	rsp := &pb.SearchTagRsp{
		List: make([]*pb.GetTagRsp, 0),
		PageInfo: &pb.PageInfo{
			IsFinish: false,
		},
	}

	var err error

	needQueryDB := false
	searchTagRedisKey := cache.GetSearchTagKey(req.TagName, language, req.NextPageCursor, req.Limit)
	if searchTagCacheInfo, err := redis.GetClient().Get(c, searchTagRedisKey).Result(); err == nil {
		err = json.Unmarshal([]byte(searchTagCacheInfo), &rsp)
		if err != nil {
			needQueryDB = true
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetMessagesNew json.Unmarshal err: %v", err)
		}
	} else {
		needQueryDB = true
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SearchTagList redis err: %v", err)
		}
	}
	if needQueryDB {
		var tagLanguageInfos []*model.TagLanguage
		var nextPageCursor, previousPageCursor string
		// 查询类型：下一页数据
		if req.PageType == pb.PageType_NEXTPAGE {
			var idCursor int64
			// 如果是首页
			if req.NextPageCursor == "" {
				idCursor = 0
			} else {
				previousPageCursor = req.NextPageCursor
				idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
				if err != nil {
					return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
				}
			}
			// 获取所有tag多语言
			tagLanguageInfos, err = dao.SearchTagIdsByTagName(req.TagName, language, idCursor, int(req.Limit))
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("SearchTagList SearchTagList err, err=(%v)", err)
				return rsp, err
			}
			// 生成下一页的游标
			if len(tagLanguageInfos) > 0 {
				nextPageCursor, err = util.EncryptPageCursorI(tagLanguageInfos[len(tagLanguageInfos)-1].ID)
				if err != nil {
					return nil, errs.NewCustomError(c, code.GetDistrictListError, "SearchTagList | Failed to create district nextPageCursor")
				}
			}
			if len(tagLanguageInfos) == 0 {
				rsp.PageInfo.IsFinish = true
				return rsp, nil
			}
		}

		tagIds := make([]int64, 0, len(tagLanguageInfos))

		for _, item := range tagLanguageInfos {
			tagIds = append(tagIds, int64(item.TagID))
		}
		// 获取所有tag
		tagInfos, err := dao.TagList(&dao.TagConditions{
			Ids: tagIds,
			Order: []*dao.OrderConditions{
				&dao.OrderConditions{
					Column: "id",
					IsDesc: true,
				},
			},
		}, 0, 0)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("SearchTagList TagList err, err=(%v)", err)
			return rsp, err
		}
		for _, tagInfo := range tagInfos {
			tagItem := &pb.GetTagRsp{
				Id:      tagInfo.ID,
				PicUrl:  tagInfo.PicURL,
				Type:    int32(tagInfo.Type),
				PostNum: tagInfo.PostNum,
			}
			for _, tagLanguageInfo := range tagLanguageInfos {
				if tagLanguageInfo.TagID == int(tagInfo.ID) {
					tagItem.TagName = tagLanguageInfo.TagName
				}
			}
			rsp.List = append(rsp.List, tagItem)
		}
		// 重新排序
		// sort.Slice(rsp.List, func(i, j int) bool {
		// 	return rsp.List[i].Id > rsp.List[j].Id
		// })
		if len(tagLanguageInfos) == 0 || len(tagLanguageInfos) < int(req.Limit) {
			rsp.PageInfo.IsFinish = true
		} else {
			rsp.PageInfo.NextPageCursor = nextPageCursor
		}
		rsp.PageInfo.PreviousPageCursor = previousPageCursor
		tagCacheByte, err := json.Marshal(rsp)
		if err == nil {
			redis.GetClient().SetEX(c, searchTagRedisKey, string(tagCacheByte), 2*time.Minute).Result()
			searchKeysKey := cache.GetTagSearchKeysCacheKey()
			redis.GetClient().SAdd(c, searchKeysKey, searchTagRedisKey)
		}
	}
	return rsp, nil
}

func GetTag(c context.Context, req *pb.GetTagReq, language string) (*pb.GetTagRsp, error) {
	// 获取tag
	conditions := &dao.TagConditions{
		Id: int64(req.Id),
	}
	// 先从缓存中获取数据
	tagDetailRedisKey := cache.GetTagDetailCacheKey(int64(req.Id), language)
	tagDetailCacheInfo, err := redis.GetClient().Get(c, tagDetailRedisKey).Result()
	if err == nil && tagDetailCacheInfo != "" {
		rsp := &pb.GetTagRsp{}
		err = json.Unmarshal([]byte(tagDetailCacheInfo), rsp)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetTag cache json.Unmarshal error.tagListRedisKey: %s, err: %v", tagDetailCacheInfo, err)
			return nil, errs.NewCustomError(c, code.GettagsJsonUnmarshalError, "Failed to obtain tag list, data parsing exception")
		} else {
			return rsp, nil
		}
	}

	tagInfo, err := dao.TagGet(conditions)
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetTag err, err=(%v)", err)
		return nil, errs.NewCustomError(c, code.GetTagFailed, "GetTag | Failed to get tag")
	}

	// 获取所有tag多语言
	tagLConditions := &dao.TagLanguageConditions{
		TagId: int64(req.Id),
	}
	tagLanguageInfos, err := dao.TagLanguageList(tagLConditions, 0, 0)
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetTag TagLanguageList err, err=(%v)", err)
		return nil, errs.NewCustomError(c, code.GetTagFailed, "GetTag | Failed to get tag language")
	}

	tagItem := &pb.GetTagRsp{
		Id:     tagInfo.ID,
		PicUrl: tagInfo.PicURL,
		Type:   int32(tagInfo.Type),
	}
	var hasLanguage bool
	for _, tagLanguageInfo := range tagLanguageInfos {
		if tagLanguageInfo.Language == language && tagLanguageInfo.TagID == int(tagInfo.ID) {
			tagItem.TagName = tagLanguageInfo.TagName
			hasLanguage = true
		}
	}
	if !hasLanguage {
		// 使用en兜底
		for _, tagLanguageInfo := range tagLanguageInfos {
			if tagLanguageInfo.Language == "en" && tagLanguageInfo.TagID == int(tagInfo.ID) {
				tagItem.TagName = tagLanguageInfo.TagName
			}
		}
	}
	// 写入缓存
	tagCacheByte, err := json.Marshal(tagItem)
	if err == nil {
		redis.GetClient().SetEX(c, tagDetailRedisKey, string(tagCacheByte), 2*time.Minute).Result()
		tagDetailKeysCacheKey := cache.GetTagDetailKeysCacheKey()
		redis.GetClient().SAdd(c, tagDetailKeysCacheKey, tagDetailRedisKey)
	}
	return tagItem, nil
}

func RemoveTagListCache(c context.Context) error {
	// 删除tag列表缓存
	getTagKeysCacheKey := cache.GetTagListKeysCacheKey()
	keys, err := redis.GetClient().SMembers(c, getTagKeysCacheKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveTagListCache redis getTagKeysCacheKey: %s,  err: %v", getTagKeysCacheKey, err)
			return err
		}
	} else {
		for _, key := range keys {
			redis.GetClient().Del(c, key)
		}
		redis.GetClient().Del(c, getTagKeysCacheKey)
	}
	// 话题搜索缓存
	searchKeysKey := cache.GetTagSearchKeysCacheKey()
	key, err := redis.GetClient().SMembers(c, searchKeysKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveTagListCache redis searchKeysKey: %s, err: %v", searchKeysKey, err)
			return err
		}
	} else {
		for _, k := range key {
			redis.GetClient().Del(c, k)
		}
		redis.GetClient().Del(c, searchKeysKey)
	}
	// 话题详情
	tagDetailKeysCacheKey := cache.GetTagDetailKeysCacheKey()
	key, err = redis.GetClient().SMembers(c, tagDetailKeysCacheKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveTagListCache redis tagDetailKeysCacheKey: %s, err: %v", tagDetailKeysCacheKey, err)
			return err
		}
	} else {
		for _, k := range key {
			redis.GetClient().Del(c, k)
		}
		redis.GetClient().Del(c, tagDetailKeysCacheKey)
	}
	return nil
}

// 更新帖子数
func UpdateTagReferencesToPost(c context.Context, postUUID string) {
	// 话题帖子数减一
	tagIDs, err := dao.GetTagIdByPostUUID(postUUID)
	if err == nil {
		err = dao.BatchDecrTagsPostNum(tagIDs, 1)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost BatchDecrTagsPostNum err: %v, postUUID: %s", err, postUUID)
		}
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost GetTagIdByPostUUID err: %v, postUUID: %s", err, postUUID)
	}
}

// 更新帖子的tag
func UpdatePostWithTag(c context.Context, postUuid string, intlOpenid string, tagIds []int64) error {
	_, err := dao.GetPost(postUuid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UpdatePostWithTag | get post failed, post_uuid: %s, err: %v", postUuid, err)
		return errs.NewCustomError(c, code.UpdatePostWithTagNotFindPost, "post not exist")
	}
	isAdmin := formatted.GetUserAdmin(intlOpenid)
	if !isAdmin {
		return errs.NewCustomError(c, code.NoPermissionToUpdatePost, "not permission, not admin")
	}
	tagIds, err = CheckTag(c, tagIds)
	if err != nil {
		return err
	}
	// 获取动态关联的话题
	tagWithPostList, err := dao.GetTagIdByPostUUID(postUuid)
	if err != nil {
		return errs.NewCustomError(c, code.GetTagFailed, "failed to get tag")
	}
	err = PostUpdateTagBind(c, tagIds, tagWithPostList, postUuid)
	if err == nil {
		go cache.RemoveUserPostCacheKeys(intlOpenid)
		go cache.DeleteUserPostsCache(intlOpenid, "", 10)
		go cache.RemovePostTagsCacheKey(postUuid)
		for _, tagID := range tagIds {
			go cache.DeleteTagPostsCache(tagID)
		}
		for _, targetTagID := range tagWithPostList {
			go cache.DeleteTagPostsCache(targetTagID)
		}
		go cache.DeletePostsListWithOpenidCache(intlOpenid)

	}
	return err
}

// 校验话题
func CheckTag(c context.Context, tags []int64) ([]int64, error) {
	var tagIds = make([]int64, 0, len(tags))
	// 判断标签是否存在
	if len(tags) > 0 {
		// 去重
		uniqueMap := make(map[int64]bool)
		var tagIdList []int64

		for _, tag := range tags {
			if _, exists := uniqueMap[tag]; !exists {
				uniqueMap[tag] = true
				tagIdList = append(tagIdList, tag)
			}
		}
		tagIds = tagIdList
		err := CheckTagIsExist(c, tags, 0)
		if err != nil {
			return nil, err
		}
	}
	return tagIds, nil
}

// 帖子更新话题绑定
func PostUpdateTagBind(c context.Context, tags []int64, targetTag []int64, postUuid string) error {
	// 创建tag-post关联关系
	//if len(tags) == 0 {
	//	return nil
	//}
	var tagPostRelations []*model.TagPost
	var bindTagId []int64
	var newBindTagId []int64
	for _, tagID := range tags {
		isBind := false // 是否已经绑定了
		for _, id := range targetTag {
			if tagID == id {
				isBind = true
				bindTagId = append(bindTagId, id)
				break
			}
		}
		if !isBind {
			tagPostRelations = append(tagPostRelations, &model.TagPost{
				TagID:    tagID,
				PostUUID: postUuid,
			})
			newBindTagId = append(newBindTagId, tagID)
			go cache.DeleteTagPostsCache(tagID)
		}
	}
	if len(tagPostRelations) > 0 {
		if err := dao.BatchSaveTagPostRelation(tagPostRelations); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostUpdateTagBind | BatchSaveTagPostRelation err:%v, data: [%+v]", err, tagPostRelations)
			return errs.NewCustomError(c, code.CreatePostSaveTagFailed, "save tag with tag err")
		}
	}
	if len(newBindTagId) > 0 {
		if err := dao.BatchIncrTagsPostNum(newBindTagId, 1); err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("postUpdateTagBind |  BatchIncrTagsPostNum err: %v", err)
		}
	}
	// 需要找出哪些是解绑的话题
	var unBindTagIds []int64
	for _, id := range targetTag {
		isExist := false
		for _, tagId := range bindTagId {
			if id == tagId {
				isExist = true
				break
			}
		}
		if !isExist {
			unBindTagIds = append(unBindTagIds, id)
			go cache.DeleteTagPostsCache(id)
		}
	}
	if len(unBindTagIds) > 0 {
		if err := dao.BatchDelTagPostRelation(unBindTagIds, postUuid); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostUpdateTagBind | BatchDelTagPostRelation err:%v, unbind tag id: %v, postuuid: %s", err, unBindTagIds, postUuid)
			return errs.NewCustomError(c, code.DeletePostWithTagFailed, "del tag with tag err")
		}
		if err := dao.BatchDecrTagsPostNum(unBindTagIds, 1); err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("postUpdateTagBind |  BatchDecrTagsPostNum err: %v", err)
		}
	}
	go func(tags []int64, postUuid string) {
		doc := map[string]interface{}{
			"tags":        tags,
			"modified_on": time.Now().Unix(),
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUuid, doc)
		dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)
	}(tags, postUuid)
	return nil

}

func GetActivityPostTagList(c context.Context) (*pb.GetActivityPostTagsRsp, error) {
	rsp := &pb.GetActivityPostTagsRsp{
		Tags: make([]*pb.ActivityPostTag, 0),
	}
	activityPostTagConf := (config.GetConfig()).ActivityPostTagSetting
	if activityPostTagConf == nil {
		return rsp, nil
	}
	for _, activityPostTagConfItem := range activityPostTagConf {
		rsp.Tags = append(rsp.Tags, &pb.ActivityPostTag{
			TagCode: activityPostTagConfItem.TagCode,
			TagId:   activityPostTagConfItem.TagId,
		})
	}
	return rsp, nil
}
