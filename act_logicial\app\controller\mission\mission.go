// Package mission 任务
package mission

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"

	// accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_mission"
	"trpc.act.logicial/app/code"
	baseLogic "trpc.act.logicial/app/logic/base"
	baseTotalModel "trpc.act.logicial/app/model/base"
)

// MissionServiceImpl 结构体
type MissionServiceImpl struct{}

// 页面任务
var storageKey = "mission"

// AddFinishMission 记录任务完成
func (s *MissionServiceImpl) AddFinishMission(ctx context.Context, req *pb.AddFinishMissionReq) (
	rsp *pb.AddFinishMissionRsp, err error) {
	rsp = &pb.AddFinishMissionRsp{}

	_, err = metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	addData := baseTotalModel.AddParamStruct{
		FsourceID:  req.FsourceId,
		Type:       2,
		Tag:        req.TagId,
		StorageKey: storageKey,
		TotalLimit: req.TotalLimit,
		DayLimit:   req.DayLimit,
	}
	hasAdd, err := baseLogic.AddData(ctx, addData, req.TimeZone)
	if err != nil {
		return
	}
	rsp.MissionHasRecord = hasAdd
	// 日志
	log.WithFieldsContext(ctx, "log_type", "logicial_mission_add", "source_id", req.FsourceId, "tag_id", req.TagId).
		Infof("finish mission")
	return
}

// HasFinishMission 任务是否完成
func (s *MissionServiceImpl) HasFinishMission(ctx context.Context, req *pb.HasFinishMissionReq) (
	rsp *pb.HasFinishMissionRsp, err error) {
	rsp = &pb.HasFinishMissionRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
		"storage_key":  storageKey,
		"tag_id":       req.TagId,
	}

	hasData, data, err := baseLogic.GetData(ctx, condition)
	if err != nil {
		return
	}
	if !hasData {
		err = errs.NewCustomError(ctx, code.MissionHasNotFinish, fmt.Sprintf(
			"has not  finish mission,,tag=%v,data=%v", req.TagId,
			data))
		return
	}
	currentTime := time.Now()
	duration, _ := time.ParseDuration(fmt.Sprintf("%vh", req.TimeZone))
	currentTime = currentTime.Add(duration)
	todayDateStr := currentTime.Format("2006-01-02")
	if (req.TotalLimit != 0 && data.TotalNum >= req.TotalLimit) || (todayDateStr == data.Fday.Format("2006-01-02") &&
		data.TodayNum >= req.DayLimit) {
		rsp.MissionHasDone = true
		return
	}
	err = errs.NewCustomError(ctx, code.MissionHasNotFinish, fmt.Sprintf(
		"has not finish mission today,tag=%v,data=%v", req.TagId,
		data))
	return
}

// HasFinishMissionList 任务是否完成列表
func (s *MissionServiceImpl) HasFinishMissionList(ctx context.Context, req *pb.HasFinishMissionListReq) (
	rsp *pb.HasFinishMissionListRsp, err error) {
	rsp = &pb.HasFinishMissionListRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	currentTime := time.Now()
	duration, _ := time.ParseDuration(fmt.Sprintf("%vh", req.TimeZone))
	currentTime = currentTime.Add(duration)
	todayDateStr := currentTime.Format("2006-01-02")
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
		"storage_key":  storageKey,
		"status":       0,
	}

	dataList, err := baseLogic.GetAllData(ctx, condition)
	if err != nil {
		return
	}
	for _, v := range req.MissionDoneList {
		mission := &pb.MissionList{
			TagId:          v.TagId,
			DayLimit:       v.DayLimit,
			TotalLimit:     v.TotalLimit,
			MissionHasDone: false,
			TotalValue:     0,
		}
		for _, item := range dataList {
			if v.TagId == item.TagId {
				if (v.TotalLimit != 0 && item.TotalNum >= v.TotalLimit) || (todayDateStr == item.Fday.Format("2006-01-02") &&
					item.TodayNum >= v.DayLimit) {
					mission.MissionHasDone = true
				}
				mission.TotalValue = int32(item.TotalValue)
				break
			}
		}
		rsp.MissionDoneList = append(rsp.MissionDoneList, mission)
	}
	return
}

// CheckTaskCompletionStatus 检查任务列表任务完成状态
func (s *MissionServiceImpl) CheckTaskCompletionStatus(ctx context.Context, req *pb.CheckTaskCompletionStatusReq) (
	*pb.CheckTaskCompletionStatusRsp, error) {
	rsp := &pb.CheckTaskCompletionStatusRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	currentTime := time.Now()
	duration, _ := time.ParseDuration(fmt.Sprintf("%vh", req.TimeZone))
	currentTime = currentTime.Add(duration)
	todayDateStr := currentTime.Format("2006-01-02")
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
		"storage_key":  storageKey,
	}

	dataList, err := baseLogic.GetAllData(ctx, condition)
	if err != nil {
		return nil, err
	}

	missionDoneList := make([]bool, 0, len(req.MissionDoneList))
	missionCount := 0
	for _, v := range req.MissionDoneList {
		// 去掉传入的空值
		if v.TagId == "" {
			continue
		}
		// 计算去掉空值后的任务数量
		missionCount += 1
		for _, item := range dataList {
			if v.TagId == item.TagId {
				if (v.TotalLimit != 0 && item.TotalNum >= v.TotalLimit) || (todayDateStr == item.Fday.Format("2006-01-02") &&
					item.TodayNum >= v.DayLimit) {
					missionDoneList = append(missionDoneList, true)
				} else {
					missionDoneList = append(missionDoneList, false)
				}
				break
			}
		}
	}
	// 查到的任务数量和传入的任务数量不一致则未完成
	if ok := CheckBoolSlice(missionDoneList, req.AllComplete); !ok || missionCount != len(dataList) {
		err = errs.NewCustomError(ctx, code.MissionListHasNotFinish, "has not finish mission list")
		return nil, err
	}
	rsp.MissionListHasDone = true
	return rsp, nil
}

// CheckBoolSlice TODO
func CheckBoolSlice(slice []bool, allComplete bool) bool {

	if allComplete {
		// 要求所有任务都完成,如果一个未完成则失败
		for _, value := range slice {
			if !value {
				return false
			}
		}
		return true
	} else {
		// 所有任务都未完成则失败
		for _, value := range slice {
			if value {
				return true
			}
		}
		return false
	}
}

// AddFinishWeeklyMission 记录周期任务完成
func (s *MissionServiceImpl) AddFinishWeeklyMission(ctx context.Context, req *pb.AddFinishWeeklyMissionReq) (
	*pb.AddFinishWeeklyMissionRsp, error) {
	rsp := &pb.AddFinishWeeklyMissionRsp{}

	addData := baseTotalModel.AddWeeklyMissionParam{
		FsourceID:        req.FsourceId,
		Type:             2,
		Tag:              req.TagId,
		PeriodicTimeList: req.TimeList,
		StorageKey:       baseLogic.PeriodicMissionKey,
	}
	hasAdd, err := baseLogic.AddPeriodicTaskCompletionData(ctx, addData, req.TimeZone)
	if err != nil {
		return nil, err
	}
	rsp.WeeklyMissionHasRecord = hasAdd
	// 日志
	log.WithFieldsContext(ctx, "log_type", "logicial_mission_add", "source_id", req.FsourceId, "tag_id", req.TagId).
		Infof("AddFinishWeeklyMission finish mission; addData:[%v]", addData)
	return rsp, nil
}

// CurrentFinishWeeklyMissionList 当前周期任务完成列表
func (s *MissionServiceImpl) CurrentFinishWeeklyMissionList(ctx context.Context,
	req *pb.CurrentFinishWeeklyMissionListReq) (
	*pb.CurrentFinishWeeklyMissionListRsp, error) {

	tagList, err := baseLogic.GetCurrentPeriodTaskCompletedList(ctx, req.TimeZone, req.TimeList, req.FsourceId)
	if err != nil {
		return nil, err
	}
	return &pb.CurrentFinishWeeklyMissionListRsp{
		TagIdList: tagList,
	}, nil
}
