package cache

import (
	"context"
	"fmt"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"trpc.publishing_application.standalonesite/app/common"
)

func GetKeyExists(ctx context.Context, cacheKey string) (bool, error) {
	isExist, err := redis.GetClient().Exists(ctx, cacheKey).Result()
	if err != nil {
		return false, err
	}
	return isExist != 0, nil
}

func GetCacheBoolValue(ctx context.Context, cacheKey string) (bool, error) {
	value, err := redis.GetClient().Do(ctx, "get", cacheKey).Bool()
	if err != nil {
		return false, err
	}
	return value, nil
}

// 获取redis中tag是否存在
func GetSmartLinkTagByCache(ctx context.Context, key string, openid string) (bool, error) {
	i, err := redis.GetClient().Do(ctx, "sismember", key, openid).Int()
	if err != nil {
		return false, err
	}
	if i > 0 {
		return true, nil
	}
	return false, nil
}

// 获取一个value
func GetValue(ctx context.Context, key string) (res string, err error) {
	timeStr := redis.GetClient().Do(ctx, "get", key).String()
	if timeStr == "" {
		return "", err
	}
	return res, nil
}

func GetGameHotKey(gameId, areaId string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "game_hot", gameId, areaId))
	return redisKey
}

func GetPostHotKey(gameId, areaId string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "post_hot", gameId, areaId))
	return redisKey
}

func GetUserInfoKey(openid string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s", "user_info", openid))
	return redisKey
}

func GetCreateUserInfoKey(openid string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s", "user_add", openid))
	return redisKey
}

func GetUserInfoAllKey(openid, utype string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s", "user_info_all", openid, utype))
	return redisKey
}

// 限制用户5分钟内发送动态不能超过10条
func GetUserCreatePostLimitMinuteKey(openid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "post_add_limit_minute", openid))
	return redisKey
}

// 限制用户1小时内发送动态不能超过20条
func GetUserCreatePostLimitHourKey(openid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "post_add_limit_hour", openid))
	return redisKey
}

// 限制用户1天内发送动态不能超过50条
func GetUserCreatePostLimitDayKey(openid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "post_add_limit_day", openid))
	return redisKey
}

// 限制用户动态点赞
func GetUserStarPostLimitKey(openid, postuuid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "user_star_post", openid, postuuid))
	return redisKey
}

// 限制用户关注其他用户操作
func GetUserFollowOtherUserLimitKey(openid, toOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "user_follow_other", openid, toOpenid))
	return redisKey
}

// 限制用户收藏动态
func GetUserPostCollectionLimitKey(openid, postuuid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "user_post_collection", openid, postuuid))
	return redisKey
}

// 查询用户粉丝列表缓存，分页
func GetUserFansKey(openid string, page string, offset int) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%d", "user_fans", openid, page, offset))
	return redisKey
}

// 查询用户粉丝列表缓存，分页(带语言筛选)
func GetUserFansKeyWithConditionLang(openid string, page string, offset int, condition string, language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%d_%s", "user_fans_with_condition_lang", openid, page, offset, condition))
	return redisKey
}

// 缓存key 列表
func GetUserFansKeysKey(openid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_fans_keys", openid))
	return redisKey
}

func GetPostContentKey(postUuid, language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "post_content", postUuid, language))
	return redisKey
}

// 查询用户关注列表缓存，分页
func GetUserFollowsKey(openid string, page string, offset int) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%d", "user_follows", openid, page, offset))
	return redisKey
}

// 查询用户关注列表缓存，分页(带语言筛选)
func GetUserFollowsKeyWithConditionLang(openid string, page string, offset int, condition string, language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%d_%s", "user_follows_with_condition_lang", openid, page, offset, condition))
	return redisKey
}

// 所有游戏列表缓存
func GetAllGamesInfoKey(language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "all_games", language))
	return redisKey
}

// 所有游戏列表缓存
func GetGamesInfoKey(language string, offset int) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%d", "games_info", language, offset))
	return redisKey
}

// 单个游戏缓存
func GetGameInfoKey(language, gameId string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "game_info", language, gameId))
	return redisKey
}

// 某个用户的消息通知列表
func GetMyMessageInfosWithLangKey(openID, nextPageCursor string, limit int64, searchType int32, language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%d_%d_%s", "msg_info", openID, nextPageCursor, searchType, limit, language))
	return redisKey
}

// 某个用户的消息通知列表
func GetFirstAddPointMsgKey(openID string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "first_add_points_msg", openID))
	return redisKey
}

// 用户未读消息
func GetMessageUnReadKey(openID string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "unread_msg", openID))
	return redisKey
}

// 限制用户1分钟内发送动态评论或资讯评论不能超过10条
func GetUserCreateCommentLimitMinuteKey(openID string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_comment_add_limit_minute", openID))
	return redisKey
}

// 限制发送NIKKE游戏加好友请求
func GetSendNikkeFriendRequestKey(uid, postUuid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "nikke_add_friend", uid, postUuid))
	return redisKey
}

// 限制用户1小时内发送动态评论或资讯评论不能超过50条
func GetUserCreateCommentLimitHourKey(openID string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_comment_add_limit_hour", openID))
	return redisKey
}

// 限制用户1天内发送动态评论或资讯评论不能超过200条
func GetUserCreateCommentLimitDayKey(openID string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_comment_add_limit_day", openID))
	return redisKey
}

// 限制用户评论点赞
func GetUserStarCommentLimitKey(openID, commentUUID string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "user_star_comment", openID, commentUUID))
	return redisKey
}

// 用户账号加了白名单列表中
func GetUserInWhiteListKey(intlOpenID, gameId, areaId string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%s", "user_in_whitelist", intlOpenID, gameId, areaId))
	return redisKey
}

// 用户动态封禁标识
func GetUserPostBanKey(intlOpenID, gameId, areaId string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%s", "user_ban_post", intlOpenID, gameId, areaId))
	return redisKey
}

// 用户评论封禁标识
func GetUserCommentBanKey(intlOpenID, gameId, areaId string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%s", "user_ban_comment", intlOpenID, gameId, areaId))
	return redisKey
}

// 用户账号封禁标识
func GetUserAccounttBanKey(intlOpenID, gameId, areaId string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%s", "user_ban_account", intlOpenID, gameId, areaId))
	return redisKey
}

// 用户信息，带有待审批的数据信息
func GetUserInfoWithAuditKey(intlOpenID string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_info_with_audit", intlOpenID))
	return redisKey
}

// 用户账号在LIP首次注册登录
func GetUserIsFirstRegisterKey(intlOpenID string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_is_first_register", intlOpenID))
	return redisKey
}

// 用户称号信息
func GetUserTitleKey(intlOpenID string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_title", intlOpenID))
	return redisKey
}

// 用户头像缓存通用
func GetUserAvatarAllDataKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("avatar_all_data")
	return redisKey
}

// 站内信协程定时任务redis互斥key
func GetSiteMessageTimerCacheKey(id int64) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d", "site_message_timer_key", id))
	return redisKey
}

// 站内信需要推送的用户号码包
func GetSiteMessageUserNumberDataKey(id int64, intlGameId string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d_%s", "site_message_user_package", id, intlGameId))
	return redisKey
}

// 站内信数据表信息缓存
func GetSiteMessageInfosKey(id int64) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d", "site_message_data", id))
	return redisKey
}

// 用户昵称
func GetUsernameCacheKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("ugc_standalone_username")
	return redisKey
}

// 用户隐私开关
func GetUserPrivacySwitchCacheKey(openid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_privacy_switch_v2", openid))
	return redisKey
}

// 获取用户分享动态的缓存key
func GetPostShareHtmlKey(postuuid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "post_share_html", postuuid))
	return redisKey
}

// 获取用户分享话题的缓存key
func GetTagShareHtmlKey(tag int32, language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d_%s", "tag_share_html", tag, language))
	return redisKey
}

// 获取用户分享话题的缓存key
func GetRewardsShareHtmlKey(language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d_%s", "rewards_share_html", language))
	return redisKey
}

// 获取用户禁言的缓存key
func GetMuteUserKey(actionType int, intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d_%s", "mute_user", actionType, intlOpenid))
	return redisKey
}

// 获取用户降权的缓存key
func GetDemotionUserKey(actionType int, intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d_%s", "demotion_user", actionType, intlOpenid))
	return redisKey
}

// 获取用户管理员的缓存key
func GetAdminUserKey(actionType int) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d", "admin_user", actionType))
	return redisKey
}

// 获取用户认证的缓存key
func GetAuthUserKey(actionType int) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d", "auth_user", actionType))
	return redisKey
}

// 获取用户机构认证缓存key
func GetAgencyUserKey(actionType int) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d", "agency_user", actionType))
	return redisKey
}

// 获取用户nikke游戏信息缓存key
func GetUserNikkeGameInfoKey(openid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_nikke_info", openid))
	return redisKey
}

// 获取用户分享的缓存key
func GetUserSharePostKey(intlOpenid string, postUuid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "user_share_post", intlOpenid, postUuid))
	return redisKey
}

// 获取热度任务计算锁
func GetPostHotCalculationTaskKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("post_hot_task")
	return redisKey
}

// 获取热度任务计算锁
func GetCommentHotCalculationTaskKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("comment_hot_task")
	return redisKey
}

// 获取Event同步任务计算锁
func GetSyncEventPostTaskKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("sync_event_post_task")
	return redisKey
}

// 获取网红活动同步任务计算锁
func GetSyncCreatorHubActivityTaskKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("sync_creator_hub_activity_task")
	return redisKey
}

// 获取网红活动作品同步任务计算锁
func GetSyncCreatorHubWorkTaskKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("sync_creator_hub_work_task")
	return redisKey
}

// 获取上一次网红活动作品同步的时间
func GetSyncCreatorHubWorkKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("sync_creator_hub_work_last_time")
	return redisKey
}

// 获取翻译文本key
func GetTranslateContentKey(contentType int32, contentUuid, language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d_%s_%s", "translate", contentType, contentUuid, language))
	return redisKey
}

// 获取creatorhub activity缓存的key
func GetCreatorHubActivityListKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("get_creator_hub_activity_list")
	return redisKey
}

// 获取某个认证多语言的key
func GetAuthLangugeDescKey(IntlOpenid string, actionType int32, language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%d_%s", "auth_language_desc", IntlOpenid, actionType, language))
	return redisKey
}

// 获取定时发布官方帖子的key
func GetOfficialPublishKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("publish_official_post_timer")
	return redisKey
}

// 获取定时机审的帖子uuid列表的key
func GetNeedAuditPostUUIDKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("need_machine_audit_posts")
	return redisKey
}

// 用户头像挂件缓存列表
func GetUserAvatarPendantsListKey(intlOpenid string, language string, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%s_%d", "user_avatar_pendants_list", intlOpenid, language, nextPageCursor, limit))
	return redisKey
}

// 获取所有头像挂件列表
func GetAvatarPendantsAllDataKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("avatar_pendants_all_data")
	return redisKey
}

// 用户是否在头像挂件缓存列表中
func GetUserIntlOpenidsInAvatarPendantCacheKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("user_intl_openids_in_avatar_pendant_cache")
	return redisKey
}

// 当前用户穿戴的头像挂件
func GetUserWearedAvatarPendantKey(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_weared_avatar_pendant_v2", intlOpenid))
	return redisKey
}

// 用户穿戴的头像挂件用户intlopenid缓存列表
func GetUserWearAvatarPendantIntlOpenidsKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("user_wear_avatar_pendant_intl_openids")
	return redisKey
}

// 扔正用户认证多语言信息缓存
func GetCertificationUserLanguageInfoCacheKey(intlOpenid string, language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "certification_user_language_info", intlOpenid, language))
	return redisKey
}

// 认证用户多语言列表缓存
func GetCertificationUserLanguageListCacheKey(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "certification_user_language_list", intlOpenid))
	return redisKey
}

// 搜索话题列表缓存
func GetSearchTagKey(tagName, language, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%s_%d", "search_tags", tagName, language, nextPageCursor, limit))
	return redisKey
}

// 获取站内信附件码缓存
func GetSiteMessageCodeKey(msgId int64) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d", "site_message_attachment_code", msgId))
	return redisKey
}

// 获取站内信统计发送人数的key
func GetSiteMessageTotalSendKey(msgId int64) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d", "site_message_total_send", msgId))
	return redisKey
}

// 获取站内信统计阅读人数的key
func GetSiteMessageTotalReadKey(msgId int64) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d", "site_message_total_read", msgId))
	return redisKey
}

// 帖子机审定时任务分布式锁key
func GetMachineReviewCacheNxKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("machine_review_cache_nx_v2")
	return redisKey
}

// 同步站内信数据分布式锁key
func GetSiteMessageSyncStatCacheNxKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("site_message_sync_stat_cache_nx")
	return redisKey
}

// 用户是否在评论气泡缓存列表中
func GetUserIntlOpenidsInCommentBubbleCacheKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("user_intl_openids_in_comment_bubble_cache")
	return redisKey
}

// 用户评论气泡缓存列表
func GetUserCommentBubblesListKey(intlOpenid string, language string, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%s_%d", "user_comment_bubbles_list", intlOpenid, language, nextPageCursor, limit))
	return redisKey
}

// 当前用户穿戴的评论气泡
func GetUserWearedCommentBubbleKey(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_weared_comment_bubble", intlOpenid))
	return redisKey
}

func GetUserWearCommentBubbleIntlOpenidsKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("user_wear_comment_bubble_intl_openids")
	return redisKey
}

// 获取所有评论气泡列表缓存Key
func GetAllCommentBubbleListCacheKey(withDeleted bool, withLanguage bool) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%t_%t", "all_comment_bubble_list", withDeleted, withLanguage))
	return redisKey
}

func GetExternalPostsKey(plateUniqueIdentifier string, plateId int64, language string, taskId int32, rankId int32, platform string, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%d_%s_%d_%d_%s_%s_%d", "ext_post", plateUniqueIdentifier, plateId, language, taskId, rankId, platform, nextPageCursor, limit))
	return redisKey
}

// 用户信息机审定时任务分布式锁key
func GetUserInfoMachineReviewCacheNxKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("user_info_machine_review_cache_nx")
	return redisKey
}

// 评论机审定时任务分布式锁key
func GetCommentContentMachineReviewCacheNxKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("comment_content_machine_review_cache_nx_v2")
	return redisKey
}

func GetPlateListKey(nextPageCursor string, limit int64, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%d", "plates", nextPageCursor, language, limit))
	return redisKey
}

func GetTagListKey(plateId int32, nextPageCursor string, limit int64, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%d_%s_%s_%d", "tags", plateId, nextPageCursor, language, limit))
	return redisKey
}

// GetTagListKey key缓存
func GetTagListKeysCacheKey() string {
	redisKey := common.GetRedisKeyWithEnv("tag_list_keys")
	return redisKey
}

// 话题搜索key 的key
func GetTagSearchKeysCacheKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("tag_search_keys")
	return redisKey
}

// tag 详情key
func GetTagDetailCacheKey(tagId int64, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%d", "tag_detail_language", tagId, language))
	return redisKey
}

func GetTagDetailKeysCacheKey() string {
	redisKey := common.GetRedisKeyWithEnv("tag_detail_keys")
	return redisKey
}

func GetDistrictListKey(language, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%d", "districts", language, nextPageCursor, limit))
	return redisKey
}

func GetPlateKey(plateId int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%d", "plate", plateId))
	return redisKey
}

func GetTagIdByPostUUIDKey(postUuid string, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s", "post_tag", postUuid, language))
	return redisKey
}

func GetAllPlateNameKey() string {
	redisKey := common.GetRedisKeyWithEnv("plate_names")
	return redisKey
}

// 主态查询最新帖子列表
func GetRecentPostBaseListHostKey(plateId int64, openid, language, nextPageCursor string, limit int64, needAllRegion bool) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%d_%s_%s_%s_%d_%v", "rpb_hposts", plateId, openid, language, nextPageCursor, limit, needAllRegion))
	return redisKey
}

// 客态查询最新帖子列表
func GetRecentPostBaseListGuestKey(plateId int64, language, nextPageCursor string, limit int64, needAllRegion bool) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%d_%s_%s_%d_%v", "rpb_gposts", plateId, language, nextPageCursor, limit, needAllRegion))
	return redisKey
}

// 帖子列表详细信息
func GetPostDetailListKey(postUUIDsMd5Str, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s", "post_details", postUUIDsMd5Str, language))
	return redisKey
}

// 帖子列表详细信息(用户相关)
func GetPostDetailListKeyWithIntlOpenid(intlOpenid, postUUIDsMd5Str, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%s", "post_details_with_intlopenid", intlOpenid, postUUIDsMd5Str, language))
	return redisKey
}

// 查询帖子列表详细信息key
func GetPostDetailListKeysKey() string {
	redisKey := common.GetRedisKeyWithEnv("post_detail_list_keys")
	return redisKey
}

// 查询话题帖子列表
func GetTagPostBaseListKey(tagId int64, needAllRegion bool, language string, plateId, orderBy int64, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%d_%v_%s_%d_%d_%s_%d", "hpb_posts", tagId, needAllRegion, language, plateId, orderBy, nextPageCursor, limit))
	return redisKey
}

// 查询话题帖子列表带有隐藏用户标识
func GetTagPostBaseListHasPostHideKey(tagId int64, needAllRegion bool, language string, plateId, orderBy int64, nextPageCursor string, limit int64, intlOpenid string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%d_%v_%s_%d_%d_%s_%d_%s", "hpb_posts", tagId, needAllRegion, language, plateId, orderBy, nextPageCursor, limit, intlOpenid))
	return redisKey
}

// 查询热度帖子列表
func GetHotPostBaseListKey(needAllRegion bool, language string, plateId int64, platform, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%v_%s_%d_%s_%s_%d", "hpb_posts", needAllRegion, language, plateId, platform, nextPageCursor, limit))
	return redisKey
}

// 查询热度帖子列表带有隐藏用户标识
func GetHotPostBaseListHasPostHideKey(needAllRegion bool, language string, plateId int64, platform, nextPageCursor string, limit int64, intlOpenid string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%v_%s_%d_%s_%s_%d_%s", "hpb_posts", needAllRegion, language, plateId, platform, nextPageCursor, limit, intlOpenid))
	return redisKey
}

// 搜索帖子列表
func GetContentQueryPostBaseListKey(keyword string, needAllRegion bool, language string, plateId int64, platform string, orderBy int64, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%v_%s_%d_%s_%d_%s_%d", "hpb_posts", keyword, needAllRegion, language, plateId, platform, orderBy, nextPageCursor, limit))
	return redisKey
}

// 搜索帖子列表带有隐藏用户标识
func GetContentQueryPostBaseListHasPostHideKey(keyword string, needAllRegion bool, language string, plateId int64, platform string, orderBy int64, nextPageCursor string, limit int64, intlOpenid string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%v_%s_%d_%s_%d_%s_%d_%s", "hpb_posts", keyword, needAllRegion, language, plateId, platform, orderBy, nextPageCursor, limit, intlOpenid))
	return redisKey
}

// 查询Any帖子列表
func GetAnyQueryPostBaseListKey(needAllRegion bool, language string, plateId int64, platform string, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%v_%s_%d_%s_%s_%d", "hpb_posts", needAllRegion, language, plateId, platform, nextPageCursor, limit))
	return redisKey
}

// 查询帖子详情
func GetPostInfoKey(postuuid, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s", "post_detail", postuuid, language))
	return redisKey
}

// 查询帖子详情
func GetPostInfoHostKey(postuuid, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s", "post_detail_host", postuuid, language))
	return redisKey
}

// 查询帖子详情
func GetPostInfoGuestKey(postuuid, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s", "post_detail_guest", postuuid, language))
	return redisKey
}

func GetPostInfoKeyWithIntlOpenid(intlOpenid, postUuid, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%s", "post_detail_with_intlopenid", intlOpenid, postUuid, language))
	return redisKey
}

// 查询用户主页个人资料信息详情
func GetUserBaseInfoKey(openid string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s", "user_binfo", openid))
	return redisKey
}

// 主态查询用户主页个人资料信息详情
func GetUserHostInfoKey(openid, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s", "user_info_host", openid, language))
	return redisKey
}

// 客态查询用户主页个人资料信息详情
func GetUserGuestInfoKey(openid, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s", "user_info_guest", openid, language))
	return redisKey
}

// 所有评论气泡请求参数cache列表
func GetUserCommentBubbleListCacheParamsCacheKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("user_comment_bubble_list_cache_params")
	return redisKey
}

// 所有评论气泡请求参数cache列表
func GetUserAvatarPendantListCacheParamsCacheKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("user_avatar_pendant_list_cache_params")
	return redisKey
}

// 主态查询帖子最新评论列表
func GetCommentBaseListHostKey(postUuid, openid, nextPageCursor string, limit int64, orderBy int32) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%d_%s_%d", "rcbh_comments", postUuid, openid, orderBy, nextPageCursor, limit))
	return redisKey
}

// 客态查询帖子最新评论列表
func GetCommentBaseListGuestKey(postUuid, nextPageCursor string, limit int64, orderBy int32) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%d_%s_%d", "rcbg_comments", postUuid, orderBy, nextPageCursor, limit))
	return redisKey
}

// 查询单条帖子主表数据
func GetOnePostBaseInfoKey(postUuid string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s", "post_base", postUuid))
	return redisKey
}

// 帖子列表详细信息
func GetCommentDetailListKey(commentUUIDsMd5Str, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s", "comment_details", commentUUIDsMd5Str, language))
	return redisKey
}

// 用户有效评论列表
func GetUserValidCommentBubblesCacheKey(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_valid_comment_bubbles", intlOpenid))
	return redisKey
}

// 主态查询评论最新回复列表
func GetCommentReplyBaseListHostKey(commentUuid, openid, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%s_%d", "rcbh_replys", commentUuid, openid, nextPageCursor, limit))
	return redisKey
}

// 客态查询评论最新回复列表
func GetCommentReplyBaseListGuestKey(commentUuid, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%d", "rcbg_replys", commentUuid, nextPageCursor, limit))
	return redisKey
}

// 用户有效挂件列表
func GetUserValidAvatarPendantsCacheKey(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_valid_avatar_pendants", intlOpenid))
	return redisKey
}

// 获取所有挂件列表
func GetAllAvatarPendantListCacheKey(withDeleted bool, withLanguage bool) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%t_%t", "all_avatar_pendant_list", withDeleted, withLanguage))
	return redisKey
}

// 主态查询单个评论
func GetOneCommentHostKey(commentUuid, openid, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%s", "comment_host", commentUuid, openid, language))
	return redisKey
}

// 客态查询单个评论
func GetOneCommentGuestKey(commentUuid, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s", "comment_guest", commentUuid, language))
	return redisKey
}

// 主态查询某个用户发布的帖子列表
func GetUserPostBaseListHostKey(queryOpenid, nextPageCursor string, limit int64, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%d_%s", "upb_hposts", queryOpenid, nextPageCursor, limit, language))
	return redisKey
}

// 客态查询某个用户发布的帖子列表
func GetUserPostBaseListGuestKey(queryOpenid, nextPageCursor string, limit int64, language string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%d_%s", "upb_gposts", queryOpenid, nextPageCursor, limit, language))
	return redisKey
}

// 主态查询某个用户发布的最新评论列表
func GetUserCommentBaseListHostKey(queryOpenid, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%d", "ucbh_comments", queryOpenid, nextPageCursor, limit))
	return redisKey
}

// 客态查询某个用户发布的最新评论列表
func GetUserCommentBaseListGuestKey(queryOpenid, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%d", "ucbg_comments", queryOpenid, nextPageCursor, limit))
	return redisKey
}

// 获取所有表情列表
func GetAllEmoticonCacheKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("all_emoticon")
	return redisKey
}

// 根据用户名搜索用户信息
func GetSearchUserKey(nickName, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%d", "search_user", nickName, nextPageCursor, limit))
	return redisKey
}

// 用户收藏的帖子列表
func GetUserCollectPostListKey(queryOpenid, nextPageCursor string, limit int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%d", "rpb_gposts", queryOpenid, nextPageCursor, limit))
	return redisKey
}

// 用户收藏帖子列表详细信息
func GetUserCollectPostDetailListKey(postUUIDsMd5Str, language string, intlOpenid string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s_%s_%s", "ucp_details", postUUIDsMd5Str, language, intlOpenid))
	return redisKey
}

// 获取用户缓存key列表
func GetUserPostCacheKeysKey(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s", "user_post_cache_keys", intlOpenid))
	return redisKey
}

// 获取用户评论缓存key列表
func GetUserCommentCacheKeysKey(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s", "user_comment_cache_keys", intlOpenid))
	return redisKey
}

// 官方账号列表缓存key
func GetOfficialAccountsListCacheKey(intlOpenid string, language string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s", "official_accounts_list", intlOpenid, language))
	return redisKey
}

// 是否关注了官方账号缓存key
func HasFollowedOfficialAccountsCacheKey(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "has_followed_official_accounts", intlOpenid))
	return redisKey
}

// 用户关注相关缓存key
func UserFollowsCacheKeyKeys(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_follows_cache_keys", intlOpenid))
	return redisKey
}

// 用户粉丝相关缓存key
func UserFansCacheKeyKeys(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "user_fans_cache_keys", intlOpenid))
	return redisKey
}

// 需要隐藏的帖子发布者用户openid
func PostHideCreatedUserOpenidCacheKeys(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "post_hide_created_user_openid_keys", intlOpenid))
	return redisKey
}

// 评论投递到kafka的key
func WaitingForReviewCommentToKafkaKeys() string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s", "waiting_for_review_commnent_to_kafka_keys"))
	return redisKey
}

// 获取帖子初始热度值
func GetPostInitialHotValueKey(postUUid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "post_initial_hot_value", postUUid))
	return redisKey
}

// 获取评论初始热度值
func GetCommentInitialHotValueKey(commentUUid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "comment_initial_hot_value", commentUUid))
	return redisKey
}

// 获取评论热度任务计算锁
func GetCommentHotCalculationUUIDKey() string {
	redisKey := common.GetRedisKeyWithoutEnv("comment_hot_uuids")
	return redisKey
}

// 获取创作者中心用户绑定信息
func GetCreatorHubUserBoundInfoKey(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "creator_hub_user_info", intlOpenid))
	return redisKey
}

// 网红作者信息
func GetCreatorHubUserInfoKey(uid string, gameid string, areaid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%s", "creator_hub_user_info", uid, gameid, areaid))
	return redisKey
}

// 网红最近活动
func GetCreatorHubRecentTasksKey(limit int32, offset int32) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%d_%d", "creator_hub_recent_tasks", limit, offset))
	return redisKey
}

func GetCreatorHubSubmissionOfUserKey(uid string, gameid string, areaid string, limit int32, offset int32) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%s_%d_%d", "creator_hub_submission_of_user", uid, gameid, areaid, limit, offset))
	return redisKey
}

// 获取作品自动同步重试作品列表
func GetWorkAutoSyncRetryWorksListKey() string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s", "work_auto_sync_retry_works_list"))
	return redisKey
}

// intl game user status
func GetIntlGameUserStatusKey(intlOpenid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "intl_game_user_status", intlOpenid))
	return redisKey
}

// 获取话题详情页缓存key列表
func GetTagPostCacheKeysKey(tagId int64) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%d", "user_comment_cache_keys", tagId))
	return redisKey
}

// 评论置顶置底分布锁
func GetCommentSetTopOrBottomCacheKey(commentUuid string) string {
	return common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "set_comment_top_or_bottom_v1", commentUuid))
}

// 公会应援用户头像列表
func GetGuildSupportUserAvatar(intlOpenidsMd5Str string) string {
	redisKey := common.GetRedisKeyWithEnv(fmt.Sprintf("%s_%s", "guild_support_user", intlOpenidsMd5Str))
	return redisKey
}

// 获取优秀创作者列表key
func GetExcellentCreatorsByLanguageKeyWithRelations(intlOpenid string, language string, limit int) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%s_%d", "excellent_creators_by_language", intlOpenid, language, limit))
	return redisKey
}

// 获取优秀创作者列表key
func GetExcellentCreatorsByLanguageKeyWithoutRelations(language string, limit int) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%d", "excellent_creators_by_language", language, limit))
	return redisKey
}

// 获取精品创作列表key
func GetHighQualityCreationKey(language string, primaryId, secondaryId int64, limit, offset int64) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%d_%d_%d_%d", "high_quality_creation", language, primaryId, secondaryId, limit, offset))
	return redisKey
}

// 公会id查询热度最高的帖子
func GuildHotPostCacheKey(guildId string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "guild_hot_post", guildId))
	return redisKey
}

// 获取批量请求的key限制
func GetTranslateBatchRequestCountKey() string {
	return common.GetRedisKeyWithoutEnv("batch_translate_request_count")
}

// 帖子列表缓存
func GetPostsListCacheKeyWithOpenid(openid string) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s", "posts_list_cache", openid))
	return redisKey
}

// 用户统计
func GetUsersStatsWithOpenidsAndLimit(key string, limit int) string {
	redisKey := common.GetRedisKeyWithoutEnv(fmt.Sprintf("%s_%s_%d", "users_stats_with_openids", key, limit))
	return redisKey
}