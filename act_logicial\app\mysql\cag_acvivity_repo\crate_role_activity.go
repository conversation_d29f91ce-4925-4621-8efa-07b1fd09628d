package cag_acvivity_repo

import (
	"context"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.act.logicial/app/model/cag"
	"trpc.act.logicial/app/mysql/functional"
)

var CreateRoleActivityClient = CreateRoleActivity{}

type CreateRoleActivity struct {
}

// GetUserPrizeRecord 获取用户奖品记录
func (p CreateRoleActivity) GetUserPrizeRecord(ctx context.Context,
	opts []functional.Option) (rlt []*cag.CagRoleLevelPrizeRecord, err error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&cag.CagRoleLevelPrizeRecord{})
	for _, opt := range opts {
		opt(query)
	}
	err = query.Find(&rlt).Error
	if nil != err {
		log.ErrorContextf(ctx, "GetUserPrizeRecord error, err: %v", err)
		return nil, err
	}
	return
}

// UpdateRecordStatus 更新发奖记录状态
func (p CreateRoleActivity) UpdateRecordStatus(ctx context.Context, id int64, userId string,
	status int, originalStatus int) error {
	query := DB.DefaultConnect().WithContext(ctx).Model(&cag.CagRoleLevelPrizeRecord{}).
		Where("id = ?", id).Where("user_id = ?", userId).Where("status = ?", originalStatus)
	// 根据条件更新字段
	updates := map[string]interface{}{
		"status": status,
	}

	res := query.Updates(updates)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateRecordStatus error, id: %v, userId: %v, status: %v, originalStatus: %v, err: %v",
			id, userId, status, originalStatus, res.Error)
		return res.Error
	}
	return nil
}

// UpdateTaskSendPrizeTimes 更新任务发奖次数
func (p CreateRoleActivity) UpdateTaskSendPrizeTimes(ctx context.Context, id int64, userId string,
	times uint32) error {
	query := DB.DefaultConnect().WithContext(ctx).Model(&cag.CagRoleLevelPrizeRecord{}).
		Where("id = ?", id).Where("user_id = ?", userId)

	// 根据条件更新字段
	updates := map[string]interface{}{
		"send_prize_times": times,
	}

	res := query.Updates(updates)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateTaskSendPrizeTimes error, id: %v, userId: %v, err: %v", id, userId, res.Error)
		return res.Error
	}
	return nil
}

// UpdateCheckCreateRoleTimes 检查创建角色次数
func (p CreateRoleActivity) UpdateCheckCreateRoleTimes(ctx context.Context, id int64, userId string,
	times uint32) error {
	query := DB.DefaultConnect().WithContext(ctx).Model(&cag.CagRoleLevelPrizeRecord{}).
		Where("id = ?", id).Where("user_id = ?", userId)

	// 根据条件更新字段
	updates := map[string]interface{}{
		"check_create_role_times": times,
	}

	res := query.Updates(updates)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateCheckCreateRoleTimes error, id: %v, userId: %v, err: %v", id, userId, res.Error)
		return res.Error
	}
	return nil
}

// UpdateInfoById 根据id更新信息
func (p CreateRoleActivity) UpdateInfoById(ctx context.Context, id int64, userId string,
	updates map[string]interface{}) error {
	query := DB.DefaultConnect().WithContext(ctx).Model(&cag.CagRoleLevelPrizeRecord{}).
		Where("id = ?", id).Where("user_id = ?", userId)

	res := query.Updates(updates)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateInfoById error, id: %v, userId: %v, updates: %v, err: %v",
			id, userId, updates, res.Error)
		return res.Error
	}
	return nil
}

// AddPrizeRecord 添加奖品记录
func (p CreateRoleActivity) AddPrizeRecord(ctx context.Context,
	record *cag.CagRoleLevelPrizeRecord) error {
	result := DB.DefaultConnect().WithContext(ctx).Create(record)
	if result.Error != nil {
		log.ErrorContextf(ctx, "AddPrizeRecord error, gameID: %s, "+
			"activityID: %s, userID: %s, openId: %v, prizeType: %v, err: %v", record.GameID,
			record.ActivityID, record.UserID, record.OpenID, record.PrizeType, result.Error)
		return result.Error
	}
	return nil
}
