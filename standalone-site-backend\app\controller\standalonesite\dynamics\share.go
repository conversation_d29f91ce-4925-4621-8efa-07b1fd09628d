package dynamics

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/logic/tweet"
)

func (s *DynamicsImpl) GetPostShareHtml(c context.Context, req *pb.GetPostShareHtmlReq) (*pb.GetPostShareHtmlRsp, error) {
	getPostShareHtmlRsp := &pb.GetPostShareHtmlRsp{}
	if req.PostUuid == "" {
		return getPostShareHtmlRsp, nil
	}
	getPostShareHtmlRsp.HtmlContent = tweet.CreatePostShareHtmlInfo(c, req.PostUuid, "en")
	metadata.SetRspContentType(c, "text/html")
	return getPostShareHtmlRsp, nil
}

func (s *DynamicsImpl) GetTagShareHtml(c context.Context, req *pb.GetTagShareHtmlReq) (*pb.GetTagShareHtmlRsp, error) {
	getTagShareHtmlRsp := &pb.GetTagShareHtmlRsp{}
	if req.TagId <= 0 {
		return getTagShareHtmlRsp, nil
	}
	getTagShareHtmlRsp.HtmlContent = tweet.CreateTagShareHtmlInfo(c, req.TagId, req.Language)
	metadata.SetRspContentType(c, "text/html")
	return getTagShareHtmlRsp, nil
}

func (s *DynamicsImpl) GetRewardsShareHtml(c context.Context, req *pb.GetRewardsShareHtmlReq) (*pb.GetRewardsShareHtmlRsp, error) {
	getRewardsShareHtmlRsp := &pb.GetRewardsShareHtmlRsp{}
	getRewardsShareHtmlRsp.HtmlContent = tweet.CreateRewardsShareHtmlInfo(c, req.Language)
	metadata.SetRspContentType(c, "text/html")
	return getRewardsShareHtmlRsp, nil
}
