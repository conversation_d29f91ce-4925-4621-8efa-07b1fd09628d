// Package team 组队
package team

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_team"
	"trpc.act.logicial/app/code"
	logic "trpc.act.logicial/app/logic/team"
)

// TeamServiceImpl 结构体
type TeamServiceImpl struct{}

// CreateTeam 创建队伍
func (s *TeamServiceImpl) CreateTeam(ctx context.Context, req *pb.CreateTeamReq) (
	rsp *pb.CreateTeamRsp, err error,
) {
	rsp = &pb.CreateTeamRsp{}
	teamCode, err := logic.CreateTeam(ctx, req.FsourceId)
	if err != nil {
		return
	}
	err = logic.JoinTeam(ctx, teamCode, req.FsourceId)
	if err != nil {
		return
	}
	rsp.TeamCode = teamCode
	return
}

// LeaveTeam 退出队伍
func (s *TeamServiceImpl) LeaveTeam(ctx context.Context, req *pb.LeaveTeamReq) (
	rsp *pb.LeaveTeamRsp, err error,
) {
	rsp = &pb.LeaveTeamRsp{}
	teamCode, err := logic.GetTeamCode(ctx, req.FsourceId)
	if err != nil {
		return
	}
	err = logic.LeaveTeam(ctx, teamCode, req.FsourceId, req.IsDisband)
	if err != nil {
		return
	}
	return
}

// JoinTeam 加入队伍
func (s *TeamServiceImpl) JoinTeam(ctx context.Context, req *pb.JoinTeamReq) (
	rsp *pb.JoinTeamRsp, err error,
) {
	rsp = &pb.JoinTeamRsp{}
	if req.MaxTeamNum != 0 {
		currentTeamNum, err := logic.GetCurrentTeamNum(ctx, req.TeamCode, req.FsourceId)
		if err != nil {
			return rsp, err
		}
		if req.MaxTeamNum <= currentTeamNum {
			err = errs.NewCustomError(ctx, code.MaxTeamLimit, "max team people limit")
			return rsp, err
		}
	}
	err = logic.JoinTeam(ctx, req.TeamCode, req.FsourceId)
	if err != nil {
		return
	}

	teamCode, err := logic.GetTeamCode(ctx, req.FsourceId)
	if err != nil {
		return
	}
	rsp.TeamCode = teamCode
	openidList, err := logic.GetTeamOpenidInfoList(ctx, req.FsourceId, teamCode)
	if err != nil {
		return
	}
	userInfoList, err := logic.GetTeamUserInfoList(ctx, req.FsourceId, openidList)
	if err != nil {
		return
	}
	invitedList, err := logic.GetTeamUserInvitedList(ctx, req.FsourceId, openidList)
	if err != nil {
		return
	}
	for index, item := range invitedList {
		rsp.UserInfoList = append(rsp.UserInfoList, &pb.UserInfo{
			UserNick:           userInfoList[index].UserNick,
			UserAvatar:         userInfoList[index].UserAvatar,
			UserDynamicAvatar:  userInfoList[index].UserDynamicAvatar,
			UserMiniAvatar:     userInfoList[index].UserMiniAvatar,
			UserFullAvatar:     userInfoList[index].UserFullAvatar,
			UserFullAvatarLand: userInfoList[index].UserFullAvatarLand,
			UserCircleAvatar:   userInfoList[index].UserCircleAvatar,
			RoleName:           userInfoList[index].RoleName,
			InvitedNum:         item.InvitedNum,
		})
	}
	rsp.LeaderInfo = rsp.UserInfoList[0]
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	rsp.IsLeader = openidList[0].Uid == userAccount.Uid
	return
}

// UserTeamInfoList 队伍信息
func (s *TeamServiceImpl) UserTeamInfoList(ctx context.Context, req *pb.UserTeamInfoListReq) (
	rsp *pb.UserTeamInfoListRsp, err error,
) {
	rsp = &pb.UserTeamInfoListRsp{}
	teamCode := req.TeamCode
	if req.TeamCode == "" {
		teamCode, err = logic.GetTeamCode(ctx, req.FsourceId)
		if err != nil {
			return
		}
	}
	rsp.TeamCode = teamCode
	openidList, err := logic.GetTeamOpenidInfoList(ctx, req.FsourceId, teamCode)
	if err != nil {
		return
	}
	userInfoList, err := logic.GetTeamUserInfoList(ctx, req.FsourceId, openidList)
	if err != nil {
		return
	}
	invitedList, err := logic.GetTeamUserInvitedList(ctx, req.FsourceId, openidList)
	if err != nil {
		return
	}
	for index, item := range invitedList {
		rsp.UserInfoList = append(rsp.UserInfoList, &pb.UserInfo{
			UserNick:           userInfoList[index].UserNick,
			UserAvatar:         userInfoList[index].UserAvatar,
			UserDynamicAvatar:  userInfoList[index].UserDynamicAvatar,
			UserMiniAvatar:     userInfoList[index].UserMiniAvatar,
			UserFullAvatar:     userInfoList[index].UserFullAvatar,
			UserFullAvatarLand: userInfoList[index].UserFullAvatarLand,
			UserCircleAvatar:   userInfoList[index].UserCircleAvatar,
			RoleName:           userInfoList[index].RoleName,
			InvitedNum:         item.InvitedNum,
			Adjective:          userInfoList[index].Adjective,
		})
	}

	rsp.LeaderInfo = rsp.UserInfoList[0]
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	rsp.IsLeader = openidList[0].Uid == userAccount.Uid
	return
}

// IsJoinTeam 是否加入队伍
func (s *TeamServiceImpl) IsJoinTeam(ctx context.Context, req *pb.IsJoinTeamReq) (
	rsp *pb.IsJoinTeamRsp, err error,
) {
	rsp = &pb.IsJoinTeamRsp{}
	teamCode, err := logic.GetTeamCode(ctx, req.FsourceId)
	if err != nil {
		return
	}
	if len(teamCode) > 0 {
		rsp.IsJoin = true
	}
	return
}

// CurrentTeamPeopleNum 当前队伍人数
func (s *TeamServiceImpl) CurrentTeamPeopleNum(ctx context.Context, req *pb.CurrentTeamPeopleNumReq) (
	rsp *pb.CurrentTeamPeopleNumRsp, err error,
) {
	rsp = &pb.CurrentTeamPeopleNumRsp{}
	teamCode, err := logic.GetTeamCode(ctx, req.FsourceId)
	if err != nil {
		return
	}

	peopleNum, err := logic.CurrentTeamPeopleNum(ctx, teamCode, req.FsourceId)
	if err != nil {
		return
	}
	rsp.PeopleNum = peopleNum

	return
}

// GetTeamCodeByUid 通过uid获取队伍码
func (s *TeamServiceImpl) GetTeamCodeByUid(ctx context.Context, req *pb.GetTeamCodeByUidReq) (
	rsp *pb.GetTeamCodeByUidRsp, err error,
) {
	rsp = &pb.GetTeamCodeByUidRsp{}
	teamCode, err := logic.GetTeamCodeByUid(ctx, req.FsourceId, req.Uid)
	if err != nil {
		return
	}
	rsp.TeamCode = teamCode
	return
}

// SyncTeamInfo 导出组队数据信息
func (s *TeamServiceImpl) SyncTeamInfo(ctx context.Context, req *pb.SyncTeamInfoReq) (
	rsp *pb.SyncTeamInfoRsp, err error,
) {
	rsp = &pb.SyncTeamInfoRsp{}
	go logic.SyncTeamInfo(ctx, req.FsourceId)
	return
}
