package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

type TagConditions struct {
	Id    int64
	Order []*OrderConditions
	Ids   []int64
}

type TagLanguageConditions struct {
	TagId int64
}

type PlateTagConditions struct {
	PlateId int64
	Order   []*OrderConditions
	LtId    int64
}

func TagGet(conditions *TagConditions) (*model.Tag, error) {
	var tag model.Tag
	db := DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName())

	if conditions.Id > 0 {
		db = db.Where("id = ?", conditions.Id)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}

	err := db.Where("is_del = ?", 0).First(&tag).Error
	if err != nil {
		return &tag, err
	}

	return &tag, nil
}

func GetTagLanguageByTagId(tagId int64) ([]*model.TagLanguage, error) {
	var tagLangData []*model.TagLanguage
	err := DB.SelectConnect("db_standalonesite").Table((&model.TagLanguage{}).TableName()).Where("tag_id = ? and is_del = 0", tagId).Find(&tagLangData).Error
	if err != nil {
		return nil, err
	}
	return tagLangData, nil

}

func GetTagLanguageByTagIds(tagIds []int64) ([]*model.TagLanguage, error) {
	var tagLangData []*model.TagLanguage
	err := DB.SelectConnect("db_standalonesite").Table((&model.TagLanguage{}).TableName()).Where("tag_id in ? and is_del = 0", tagIds).Find(&tagLangData).Error
	if err != nil {
		return nil, err
	}
	return tagLangData, nil

}

func TagLanguageList(conditions *TagLanguageConditions, offset, limit int) ([]*model.TagLanguage, error) {
	var tagLanguages []*model.TagLanguage
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.TagLanguage{}).TableName())
	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	if conditions.TagId > 0 {
		db = db.Where("tag_id = ?", conditions.TagId)
	}

	if err = db.Find(&tagLanguages).Error; err != nil {
		return nil, err
	}

	return tagLanguages, nil
}

func TagCreate(tag *model.Tag) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName()).Create(&tag).Error

	return err
}

func TagUpdate(tag *model.Tag) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName()).Where("id = ? AND is_del = ?", tag.ID, 0).Save(&tag).Error
}

func BatchIncrTagsPostNum(tagIDs []int64, incrNum int) error {
	if len(tagIDs) == 0 {
		return nil
	}
	return DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName()).Where("id in ?", tagIDs).Update("post_num", gorm.Expr("post_num + ?", incrNum)).Error
}

func BatchDecrTagsPostNum(tagIDs []int64, decrNum int) error {
	if len(tagIDs) == 0 {
		return nil
	}
	return DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName()).Where("id in ?", tagIDs).Update("post_num", gorm.Expr("CASE WHEN post_num - ? < 0 THEN 0 ELSE post_num - ? END", decrNum, decrNum)).Error
}

func TagDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName()).Where("id  ?", id).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func TagList(conditions *TagConditions, offset, limit int) ([]*model.Tag, error) {
	var tags []*model.Tag
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName())
	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	if len(conditions.Ids) > 0 {
		db = db.Where("id in ?", conditions.Ids)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}

	if err = db.Find(&tags).Error; err != nil {
		return nil, err
	}

	return tags, nil
}

func TagsFrom(tags []string) (res []*model.Tag, err error) {
	err = DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName()).Where("tag IN ?", tags).Where("is_audit = ?", 1).Find(&res).Error
	return
}

func UpdateTagHotNum(id int64, hotNum int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName()).Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"hot_num":     hotNum,
	}).Error
}

func SetTagsHotNumZero(tagIds []int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName()).Where("id not in ? ", tagIds).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"hot_num":     0,
	}).Error
}

func GetPlateTagList(conditions *PlateTagConditions, limit int) (plateTags []*model.PlateTag, err error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.PlateTag{}).TableName())
	if conditions.PlateId > 0 {
		db = db.Where("plate_id = ?", conditions.PlateId)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if limit > 0 {
		db.Limit(limit)
	}
	err = db.Find(&plateTags).Error
	return
}

func SearchTagIdsByTagName(tagName, language string, ltID int64, limit int) ([]*model.TagLanguage, error) {
	var tagLanguages []*model.TagLanguage
	if tagName == "" {
		return tagLanguages, nil
	}
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.TagLanguage{}).TableName())
	db = db.Where("is_del = ?", 0).Where("language = ?", language).Where("tag_name LIKE ?", "%"+tagName+"%")
	if limit > 0 {
		db = db.Limit(limit)
	}
	if ltID > 0 {
		db = db.Where("id < ?", ltID)
	}

	db = db.Order("id desc")

	if err = db.Find(&tagLanguages).Error; err != nil {
		return nil, err
	}

	return tagLanguages, nil
}

// UpdateTagPostNum 更新话题动态数量
func UpdateTagPostNum(id int, postCount int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Tag{}).TableName()).Where("id = ?", id).Updates(map[string]interface{}{
		"post_num": time.Now().Unix(),
		"is_del":   1,
	}).Error
}
