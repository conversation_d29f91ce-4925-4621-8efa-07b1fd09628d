package model

import (
	"errors"
	"fmt"

	"trpc.publishing_application.standalonesite/app/util"
)

type User struct {
	*Model
	IntlOpenid string `json:"intl_openid"` //INTLSDK登录的用户openid
}

type UserContent struct {
	*Model
	IntlGameid     string `json:"intl_gameid"`
	Nickname       string `json:"nickname"`               //昵称
	Username       string `json:"username"`               //用户名
	UsernameOn     int64  `json:"username_on"`            //用户名称修改时间
	IntlOpenid     string `json:"intl_openid"`            //INTLSDK登录的用户openid
	Type           int32  `json:"type"`                   //用户类型：1：LIP的intl登录渠道，2是网红平台用户
	Phone          string `json:"phone"`                  //手机号
	Email          string `json:"email"`                  //邮箱地址
	Password       string `json:"password"`               //MD5密码
	Salt           string `json:"salt"`                   //盐值
	Status         int    `json:"status"`                 //状态，1正常使用，2新注册待启用
	Avatar         string `json:"avatar"`                 //用户头像
	AvatarOn       int64  `json:"avatar_on"`              //头像修改时间
	FansNum        int32  `json:"fans_num" gorm:"-"`      //粉丝数
	FollowNum      int32  `json:"follow_num" gorm:"-"`    //关注数
	AllPostNum     int32  `json:"all_post_num" gorm:"-"`  //主态下看到的用户所有动态数（未审批+已审批）
	PostStarNum    int32  `json:"post_star_num" gorm:"-"` //用户所有动态的点赞数总和
	PostNum        int32  `json:"post_num" gorm:"-"`      //已发布生效的动态数
	Remark         string `json:"remark"`                 //备注
	RemarkOn       int64  `json:"remark_on"`              //备注修改时间
	HasSignPrivacy int32  `json:"has_sign_privacy"`       //用户是否有签署隐私协议：0为未签署；1为已签署
	Balance        int64  `json:"balance"`                //用户余额（分）
	Mood           string `json:"mood"`                   // 用户心情
	GameTag        int32  `json:"game_tag"`               // 用户NIKKE游戏标签
	HomePageLinks  string `json:"home_page_links"`        // 用户社媒渠道个人主页链接
	TagId          string `json:"-" gorm:"-"`             // 用户游戏标签
	IsAdmin        bool   `json:"is_admin"`               //是否管理员
	CreatedOn      int64  `json:"created_on"`             //创建时间
	ModifiedOn     int64  `json:"modified_on"`            //修改时间
	DeletedOn      int64  `json:"deleted_on"`             //删除时间
	IsDel          int    `json:"is_del"`                 //是否删除 0 为未删除、1 为已删除
	Language       string `json:"language"`               //用户最后一次选择的语言
	// CertificationUserLanguages string `json:"certification_user_languages"` // 认证用户多语言数据
	ShiftyspadSwitchStr string `json:"shiftyspad_switch_str" gorm:"default:;"` //shiftyspad隐私相关开关json
}

type UserContentTemp struct {
	*Model
	IntlGameid     string `json:"intl_gameid"`
	Nickname       string `json:"nickname"`         //昵称
	Username       string `json:"username"`         //用户名
	UsernameOn     int64  `json:"username_on"`      //用户名称修改时间
	IntlOpenid     string `json:"intl_openid"`      //INTLSDK登录的用户openid
	Type           int32  `json:"type"`             //用户类型：1：LIP的intl登录渠道，2是网红平台用户
	Phone          string `json:"phone"`            //手机号
	Email          string `json:"email"`            //邮箱地址
	Password       string `json:"password"`         //MD5密码
	Salt           string `json:"salt"`             //盐值
	Status         int    `json:"status"`           //状态，1正常使用，2新注册待启用
	Avatar         string `json:"avatar"`           //用户头像
	AvatarOn       int64  `json:"avatar_on"`        //头像修改时间
	FansNum        int32  `json:"fans_num"`         //粉丝数
	FollowNum      int32  `json:"follow_num"`       //关注数
	AllPostNum     int32  `json:"all_post_num"`     //主态下看到的用户所有动态数（未审批+已审批）
	PostStarNum    int32  `json:"post_star_num"`    //用户所有动态的点赞数总和
	PostNum        int32  `json:"post_num"`         //已发布生效的动态数
	Remark         string `json:"remark"`           //备注
	RemarkOn       int64  `json:"remark_on"`        //备注修改时间
	HasSignPrivacy int32  `json:"has_sign_privacy"` //用户是否有签署隐私协议：0为未签署；1为已签署
	Balance        int64  `json:"balance"`          //用户余额（分）
	Mood           string `json:"mood"`             // 用户心情
	GameTag        int32  `json:"game_tag"`         // 用户NIKKE游戏标签
	HomePageLinks  string `json:"home_page_links"`  // 用户社媒渠道个人主页链接
	TagId          string `json:"-" gorm:"-"`       // 用户游戏标签
	IsAdmin        bool   `json:"is_admin"`         //是否管理员
	CreatedOn      int64  `json:"created_on"`       //创建时间
	ModifiedOn     int64  `json:"modified_on"`      //修改时间
	DeletedOn      int64  `json:"deleted_on"`       //删除时间
	IsDel          int    `json:"is_del"`           //是否删除 0 为未删除、1 为已删除
	Language       string `json:"language"`         //用户最后一次选择的语言
	// CertificationUserLanguages string `json:"certification_user_languages"` // 认证用户多语言数据
	ShiftyspadSwitchStr string `json:"shiftyspad_switch_str" gorm:"default:;"` //shiftyspad隐私相关开关json
}

type UserBaseInfo struct {
	*UserContent
	AuthType      int32  `json:"auth_type"`
	IsMute        bool   `json:"is_mute"`
	AvatarPendant string `json:"avatar_pendant"`
	GameTagNum    int32  `json:"game_tag_num"`
}
type UserProFile struct {
	*UserContent
	IsFollow bool `json:"is_follow"`
}

type UserFormated struct {
	ID int64 `json:"id"`
	//Nickname string `json:"nickname"`
	Username   string `json:"username"`
	UsernameON int64  `json:"username_on"`
	Status     int    `json:"status"`
	Avatar     string `json:"avatar"`
	IsAdmin    bool   `json:"is_admin"`
	IntlGameid string `json:"intl_gameid"`
	IntlOpenid string `json:"intl_openid"`
	Remark     string `json:"remark"`
	//HomePageUrl string                 `json:"home_page_url"` // 对应社媒渠道的个人首页链接
	Language string                 `json:"language"` // 用户最后选择的语言
	Titles   *UserBindTitleFormated `json:"titles"`
	Mood     string                 `json:"mood"`     // 用户心情
	GameTag  int32                  `json:"game_tag"` // 用户NIKKE游戏标签
}

type UserContext struct {
	UserInfo  *User              `json:"user_info"`
	AuditInfo []*AuditPermission `json:"audit_info"`
}
type UserInfo struct {
	ID              int64  `json:"id"`
	IntlGameid      string `json:"intl_gameid"`
	IntlOpenid      string `json:"intl_openid"`
	IsFirstRegister int64  `json:"is_first_register"` // 提供给前端判断是否是首次注册登录用户，0不是，1是
	AuditUsername   string `json:"audit_username"`
	AuditRemark     string `json:"audit_remark"`
	AuditAvatar     string `json:"audit_avatar"`
	Username        string `json:"username"`
	UsernameOn      int64  `json:"username_on"`
	Remark          string `json:"remark"`
	RemarkOn        int64  `json:"remark_on"`
	Avatar          string `json:"avatar"`
	AvatarOn        int64  `json:"avatar_on"`
	IsAuditUsername bool   `json:"is_audit_username"`
	IsAuditRemark   bool   `json:"is_audit_remark"`
	IsAuditAvatar   bool   `json:"is_audit_avatar"`
	HasSignPrivacy  bool   `json:"has_sign_privacy"` // LIP社区成年人是否已签署隐私协议
	Language        string `json:"language"`         // 用户最后选择的语言
}

type UserInfoNew struct {
	ID              int64  `json:"id"`
	IntlOpenid      string `json:"intl_openid"`
	AuditUsername   string `json:"audit_username"`
	AuditRemark     string `json:"audit_remark"`
	AuditAvatar     string `json:"audit_avatar"`
	Username        string `json:"username"`
	UsernameOn      int64  `json:"username_on"`
	Nickname        string `json:"nickname"`
	Email           string `json:"email"`
	Remark          string `json:"remark"`
	RemarkOn        int64  `json:"remark_on"`
	Avatar          string `json:"avatar"`
	AvatarOn        int64  `json:"avatar_on"`
	IsAuditUsername bool   `json:"is_audit_username"`
	IsAuditRemark   bool   `json:"is_audit_remark"`
	IsAuditAvatar   bool   `json:"is_audit_avatar"`
	IsFirstRegister int64  `json:"is_first_register"` // 提供给前端判断是否是首次注册登录用户，0不是，1是
	HasSignPrivacy  bool   `json:"has_sign_privacy"`  // LIP社区成年人是否已签署隐私协议：0为未签署；1为已签署
	PostNum         int32  `json:"post_num"`          // 客态下看到的，已审批通过发布的动态数
	AllPostNum      int32  `json:"all_post_num"`      // 主态下看到的当前用户自己所有发布的动态数
	PostStarNum     int32  `json:"post_star_num"`
	FollowNum       int32  `json:"follow_num"`
	FansNum         int32  `json:"fans_num"`
	//HomePageUrl     string                 `json:"home_page_url"` // 对应社媒渠道的个人首页链接
	IsFollow        bool                   `json:"is_follow"`
	AdultStatus     int64                  `json:"adult_status"`
	Titles          *UserBindTitleFormated `json:"titles"`
	Language        string                 `json:"language"`     // 用户最后选择的语言
	Mood            string                 `json:"mood"`         // 用户心情
	GameTag         int32                  `json:"game_tag"`     // 用户NIKKE游戏标签
	GameTagNum      int32                  `json:"game_tag_num"` // 用户NIKKE游戏标签对应的具体游戏值
	ChangeNameCount int                    `json:"change_name_count"`
	HomePageLinks   string                 `json:"home_page_links"`
}

type UserPrivacySwitch struct {
	ShowMyPosts         int32  `json:"show_my_posts"`         //'是否展示我发布的帖子：0-不展示1-展示',
	ShowMyCollection    int32  `json:"show_my_collection"`    //'是否展示我收藏的帖子：0-不展示1-展示 ',
	ShowMyFollow        int32  `json:"show_my_follow"`        //'是否展示我的关注列表：0-不展示1-展示',
	ShowMyFans          int32  `json:"show_my_fans"`          //'是否展示我的粉丝列表：0-不展示1-展示',
	ShowMyGameCard      int32  `json:"show_my_game_card"`     //'是否展示我的游戏卡片：0-不展示1-展示',
	ReceiveTweetEmail   int32  `json:"receive_tweet_email"`   //'是否同意接收资讯邮件：0-不接收1-接收',
	MsgCommentNotify    int32  `json:"msg_comment_notify"`    //'是否通知新的评论和回复：0-不通知1-通知',
	MsgLikeNotify       int32  `json:"msg_like_notify"`       //'是否通知新的点赞：0-不通知1-通知',
	MsgFollowNotify     int32  `json:"msg_follow_notify"`     //'是否通知新的关注：0-不通知1-通知',
	MsgSystemNotify     int32  `json:"msg_system_notify"`     //'是否开启系统通知：0-不通知1-通知',
	MsgActivityNotify   int32  `json:"msg_activity_notify"`   //'是否开启活动通知：0-不通知1-通知',
	ShowMyComment       int32  `json:"show_my_comment"`       // 是否展示我的评论在主页上：0-不展示1-展示
	ShiftyspadSwitchStr string `json:"shiftyspad_switch_str"` //shiftyspad隐私相关开关json

}

type GuildUserInfo struct {
	*Model
	IntlOpenid          string `json:"intl_openid"`           //INTLSDK登录的用户openid
	Avatar              string `json:"avatar"`                //用户头像
	ShowMyPosts         int32  `json:"show_my_posts"`         //'是否展示我发布的帖子：0-不展示1-展示',
	ShowMyCollection    int32  `json:"show_my_collection"`    //'是否展示我收藏的帖子：0-不展示1-展示 ',
	ShowMyFollow        int32  `json:"show_my_follow"`        //'是否展示我的关注列表：0-不展示1-展示',
	ShowMyFans          int32  `json:"show_my_fans"`          //'是否展示我的粉丝列表：0-不展示1-展示',
	ShowMyGameCard      int32  `json:"show_my_game_card"`     //'是否展示我的游戏卡片：0-不展示1-展示',
	ReceiveTweetEmail   int32  `json:"receive_tweet_email"`   //'是否同意接收资讯邮件：0-不接收1-接收',
	MsgCommentNotify    int32  `json:"msg_comment_notify"`    //'是否通知新的评论和回复：0-不通知1-通知',
	MsgLikeNotify       int32  `json:"msg_like_notify"`       //'是否通知新的点赞：0-不通知1-通知',
	MsgFollowNotify     int32  `json:"msg_follow_notify"`     //'是否通知新的关注：0-不通知1-通知',
	MsgSystemNotify     int32  `json:"msg_system_notify"`     //'是否开启系统通知：0-不通知1-通知',
	MsgActivityNotify   int32  `json:"msg_activity_notify"`   //'是否开启活动通知：0-不通知1-通知',
	ShowMyComment       int32  `json:"show_my_comment"`       // 是否展示我的评论在主页上：0-不展示1-展示
	ShiftyspadSwitchStr string `json:"shiftyspad_switch_str"` //shiftyspad隐私相关开关json

}

type UserMsgActivityNotifyData struct {
	MsgActivityNotify int32  `json:"msg_activity_notify"` //'是否开启活动通知：0-不通知1-通知',
	IntlOpenid        string `json:"intl_openid"`
}

type UserLinksConfig struct {
	*Model
	HomePageLinks string `json:"home_page_links"` //links配置校验
}

// user主表
type UserOwner struct {
	*Model
	IntlOpenid     string `gorm:"column:intl_openid;unique;not null" json:"intl_openid"`
	IntlGameid     string `gorm:"column:intl_gameid;unique;not null" json:"intl_gameid"`
	IntlUserOpenid string `gorm:"column:intl_user_openid;" json:"intl_user_openid"`
}

func (u *UserOwner) TableName() string {
	return "p_user"
}

type ESUserInfo struct {
	IntlGameid                 string `json:"intl_gameid"`
	IntlOpenid                 string `json:"intl_openid"`
	Language                   string `json:"language"`
	Nickname                   string `json:"nickname"` // 板块id
	Username                   string `json:"username"`
	UsernameOn                 int32  `json:"username_on"`
	Avatar                     string `json:"avatar"`
	AvatarOn                   int64  `json:"avatar_on"`
	FansNum                    int32  `json:"fans_num"`
	FollowNum                  int32  `json:"follow_num"`
	AllPostNum                 int32  `json:"all_post_num"`
	PostStarNum                int32  `json:"post_star_num"`
	PostNum                    int32  `json:"post_num"`
	Remark                     string `json:"remark"`
	RemarkOn                   int64  `json:"remark_on"`
	HomePageLinks              string `json:"home_page_links"` // 用户社媒渠道个人主页链接
	IsAdmin                    int32  `json:"is_admin"`        // 1为所有板块管理员
	AdminOn                    int64  `json:"admin_on"`
	IsMute                     int32  `json:"is_mute"` // 1为禁言
	MuteOn                     int64  `json:"mute_on"`
	MuteReason                 int64  `json:"mute_reason"`
	AuthType                   int32  `json:"auth_type"` // 1为官方认证，2为创作认证
	AuthOn                     int64  `json:"auth_on"`
	Mood                       string `json:"mood"`
	GameTag                    int32  `json:"game_tag"` // 用户NIKKE游戏标签
	CreatedOn                  int64  `json:"created_on"`
	ModifiedOn                 int64  `json:"modified_on"`
	DeletedOn                  int32  `json:"deleted_on"`
	IsDel                      int    `json:"is_del"`
	AvatarPendant              string `json:"avatar_pendant"`               // 用户头像挂件
	AuthLanguages              string `json:"auth_languages"`               // 认证称号多语言 []*standalonesite_user.LanguageItem
	CertificationUserLanguages string `json:"certification_user_languages"` // 认证用户昵称个签多语言数据 []*standalonesite_user.UserInfoLanguageItem
	IntlUserOpenid             string `json:"intl_user_openid"`
	MuteDays                   int32  `json:"mute_days"`
	IsDemotion                 int32  `json:"is_demotion"`  // 是否降权
	DemotionOff                int64  `json:"demotion_off"` // 降权结束时间
}

type ShiftyspadPrivacySwitch struct {
	ShowDailyInfo    int `json:"show_daily_info"`
	ShowOutpostInfo  int `json:"show_outpost_info"`
	ShowResourceInfo int `json:"show_resource_info"`
	ShowNikkeInfo    int `json:"show_nikke_info"`
}

func (u *UserContent) Format() *UserFormated {
	if u.Model != nil {
		return &UserFormated{
			ID:         u.ID,
			Username:   u.Username,
			UsernameON: u.UsernameOn,
			Status:     u.Status,
			Avatar:     u.Avatar,
			Remark:     u.Remark,
			//HomePageUrl: u.HomePageURL,
			IsAdmin:    u.IsAdmin,
			IntlOpenid: u.IntlOpenid,
			Titles:     &UserBindTitleFormated{},
			Mood:       u.Mood,
			GameTag:    u.GameTag,
		}
	}

	return nil
}

// GetSubMeterTable 计算用户分表
func (u *UserContent) GetUserSubMeterTable(openid string) (string, error) {
	suffixId := util.ExtractSurplusByOpenid(openid, 2)
	if suffixId < 0 {
		return "", errors.New("get user table name failed")
	}
	tableName := fmt.Sprintf("%s_%d", "p_user", suffixId)
	return tableName, nil
}
