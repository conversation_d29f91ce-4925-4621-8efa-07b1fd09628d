package handler

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/IBM/sarama"
	"trpc.publishing_application.standalonesite/app/common"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/tweet"
)

func MachineAuditPostConsumer(ctx context.Context, msgArray []*sarama.ConsumerMessage) (err error) {
	var handlers []func() error
	// TODO 如果消息积压超过一小时就告警
	// msgArray.Timestamp
	for _, v := range msgArray {
		value := string(v.Value)
		handlers = append(handlers, func() error {
			err := ConsumeMachineAuditPostData(common.GetCommonCtx(), value)
			return err
		})
	}
	err = trpc.GoAndWait(handlers...)
	return
}

func ConsumeMachineAuditPostData(ctx context.Context, sendStr string) (err error) {
	defer recovery.CatchGoroutinePanic(context.Background())
	machineAuditPostMsgData := make([]interface{}, 0)
	if err = json.Unmarshal([]byte(sendStr), &machineAuditPostMsgData); err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("ConsumeMachineAuditPostData failed to get kafka req,err:%v\n", err)
		return nil
	}

	machineAuditPostList := make([]string, len(machineAuditPostMsgData))
	for i, datum := range machineAuditPostMsgData {
		machineAuditPostList[i] = fmt.Sprintf("%s", datum)
	}
	err = tweet.MachineAuditPostConsumeHandler(ctx, machineAuditPostList)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("message.SiteMessageConsumeHandler failed,err:%v\n", err)

		// 消费kafka数据异常失败的，则直接告警，错误数据记录db，不返回错误，让kafka消息队列继续往下消费
		return nil
	}
	return nil
}
