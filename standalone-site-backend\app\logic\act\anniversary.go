package act

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"html"
	"sort"
	"strings"
	"time"

	"math/rand"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	activity_pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_activity"
	standalonesite_user "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	redisClient "github.com/go-redis/redis/v8"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/cms"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/model"
)

type StandalonesiteUser []*standalonesite_user.UserInfo

type PostExt struct {
	PostId string `json:"post_id"`
}

// 获取优秀创作者
func GetExcellentCreatorsInternal(ctx context.Context, intlOpenid string, language string, limit int) (StandalonesiteUser, error) {
	if language == "" {
		language = "en"
	}
	rspP, err := getExcellentCreatorsUsers(ctx, language, limit)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetExcellentCreatorsByLanguage err: %v", err)
		return nil, err
	}
	if rspP == nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetExcellentCreatorsByLanguage rsp is nil")
		users := make([]*standalonesite_user.UserInfo, 0)
		return StandalonesiteUser(users), nil
	}
	rsp := *rspP
	// 补充follow数据
	if intlOpenid == "" {
		return rsp, nil
	} else {
		intlOpenids := make([]string, 0)
		for _, userInfo := range rsp {
			intlOpenids = append(intlOpenids, userInfo.IntlOpenid)
		}
		collections, err := user.IsFollowOtherUsers(ctx, intlOpenid, intlOpenids)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetExcellentCreatorsByLanguage IsFollowOtherUsers err: %v", err)
			return nil, err
		}
		if len(collections) != len(rsp) {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetExcellentCreatorsByLanguage collections len: %d is not equal rsp len: %d", len(collections), len(rsp))
		} else {
			for i, user := range rsp {
				if collections[i] || user.IntlOpenid == intlOpenid {
					user.IsFollowed = 1
				}
			}
		}
		return rsp, nil
	}
}

// 获取优质创作
func GetHighQualityCreationInternal(ctx context.Context, primaryId, SecondaryId int64, language string, limit int64, offset int64) (*activity_pb.GetHighQualityCreationRsp, error) {
	if language == "" {
		language = "en"
	}
	rsp := &activity_pb.GetHighQualityCreationRsp{}
	redisKey := cache.GetHighQualityCreationKey(language, primaryId, SecondaryId, limit, offset)
	getHeightQualityCreationsFromDb := func(c context.Context) (interface{}, error) {
		cmsNews, err := cms.GetCMSNewsByColumnIds(ctx, primaryId, SecondaryId, language, limit, offset)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetHighQualityCreationInternal GetCMSNewsByColumnIds err: %v", err)
			return rsp, err
		}
		// 提取ext_info 中的内容
		postUuids := getPostUUidFromCMSContents(ctx, cmsNews.Data.InfoContent)
		// 根据postUuids获取帖子详情
		rsp, err = getPostAndUsersOfExcellenCreations(ctx, postUuids, language)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetHighQualityCreationInternal GetPostAndUsersOfExcellenCreations err: %v", err)
			return rsp, err
		}
		isFinished := 0
		if len(cmsNews.Data.InfoContent) < int(limit) || cmsNews.Data.NextOffset == 0 || cmsNews.Data.IsFinish == 1 {
			isFinished = 1
		}
		rsp.PageInfo = &activity_pb.OffsetPageInfo{
			Total:      cmsNews.Data.TotalNum,
			NextOffset: cmsNews.Data.NextOffset,
			IsFinished: int32(isFinished),
		}
		cache.SetCacheWithMarshal(ctx, redisKey, rsp, 2*time.Minute)
		return rsp, nil
	}
	res, err := cache.GetCacheWithUnmarshal(ctx, redisKey, rsp, nil, &getHeightQualityCreationsFromDb, true)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetHighQualityCreationInternal GetCacheWithUnmarshal err: %v", err)
		return rsp, err
	}
	if res == nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetHighQualityCreationInternal GetCacheWithUnmarshal res is nil")
		return rsp, nil
	}
	return res.(*activity_pb.GetHighQualityCreationRsp), nil

}

func turnLanguage2Country(language string) (in []constants.ECreatorHubCountry, notIn []constants.ECreatorHubCountry) {
	in = make([]constants.ECreatorHubCountry, 0)
	notIn = make([]constants.ECreatorHubCountry, 0)
	switch language {
	case "ja":
		in = []constants.ECreatorHubCountry{constants.CH_COUNTRY_Japan}
		return
	case "ko":
		in = []constants.ECreatorHubCountry{constants.CH_COUNTRY_Korea}
		return
	case "zh-TW":
		in = []constants.ECreatorHubCountry{constants.CH_COUNTRY_HK, constants.CH_COUNTRY_TW, constants.CH_COUNTRY_Macau}
		return
	default:
		notIn = []constants.ECreatorHubCountry{constants.CH_COUNTRY_Japan, constants.CH_COUNTRY_Korea, constants.CH_COUNTRY_HK, constants.CH_COUNTRY_TW, constants.CH_COUNTRY_Macau}
		return
	}
}

func getExcellentCreatorsUsers(ctx context.Context, language string, limit int) (*[]*standalonesite_user.UserInfo, error) {
	redisKey := cache.GetExcellentCreatorsByLanguageKeyWithoutRelations(language, limit)
	rsp := make([]*standalonesite_user.UserInfo, 0)
	rspP := &rsp
	getExcellentCreatorUserFromDb := func(c context.Context) (interface{}, error) {
		// 将language映射为网红的country
		incountries, notInCountries := turnLanguage2Country(language)
		userBoundInfo, err := dao.GetBoundChUserInfoByContries(incountries, notInCountries, true, 0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetExcellentCreatorsByLanguage GetBoundChUserInfoByContries err: %v", err)
			return nil, err
		}
		// 随机选取若干个
		// randUserBoundInfos := getRandUserBoundInfos(userBoundInfo, limit)
		randUserBoundInfos := getFansDescOrderByChUserBind(ctx, userBoundInfo, limit)
		// 根据绑定用户，获取独立站用户信息
		intlOpenids := make([]string, 0)
		for _, userInfo := range randUserBoundInfos {
			intlOpenids = append(intlOpenids, userInfo.IntlOpenid)
		}
		if len(intlOpenids) == 0 {
			return rspP, nil
		}
		users := make([]*standalonesite_user.UserInfo, 0)
		for _, intlOpenidItem := range intlOpenids {
			userBaseInfo, err := user.GetUserBaseInfoByOpenid(c, intlOpenidItem)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetExcellentCreatorsByLanguage GetUserListByOpenid err: %v", err)
				return nil, err
			}
			users = append(users, &standalonesite_user.UserInfo{
				IntlOpenid: userBaseInfo.IntlOpenid,
				Username:   html.UnescapeString(userBaseInfo.Username),
				Remark:     html.UnescapeString(userBaseInfo.Remark),
				Avatar:     userBaseInfo.Avatar,
				AuthDesc:   formatted.GetUserAuthDesc(c, userBaseInfo.IntlOpenid, userBaseInfo.AuthType, language),
				FansNum:    userBaseInfo.FansNum,
			})
		}
		// 记录缓存
		cache.SetCacheWithMarshal(ctx, redisKey, users, 2*time.Minute)
		return &users, nil
	}
	userIfs, err := cache.GetCacheWithUnmarshal(ctx, redisKey, rspP, nil, &getExcellentCreatorUserFromDb, true)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetExcellentCreatorsByLanguage GetCacheWithUnmarshal err: %v", err)
		return nil, err
	}
	if userIfs == nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetExcellentCreatorsByLanguage GetCacheWithUnmarshal userIfs: %v", userIfs)
		return rspP, nil
	}
	rspP = userIfs.(*[]*standalonesite_user.UserInfo)
	return rspP, nil
}

// 独立站粉丝数倒序的绑定用户
func getFansDescOrderByChUserBind(ctx context.Context, userBoundInfos []*model.ChUserBind, limit int) []*model.ChUserBind {

	var userStates []*model.UserState
	sortCHUserBind := func(stats []*model.UserState, limit int) []*model.ChUserBind {
		result := make([]*model.ChUserBind, 0)
		sort.Slice(stats, func(i, j int) bool {
			return stats[i].FansNum > stats[j].FansNum
		})
		for _, userStateItem := range stats {
			for _, userBoundInfo := range userBoundInfos {
				if userBoundInfo.IntlOpenid == userStateItem.IntlOpenid {
					result = append(result, userBoundInfo)
					break
				}
			}
		}
		if limit == 0 {
			return result
		}
		if len(stats) < limit {
			return result
		}
		return result[:limit]
	}
	openids := []string{}
	openidsStr := ""
	for _, userBoundInfo := range userBoundInfos {
		openids = append(openids, userBoundInfo.IntlOpenid)
		openidsStr = fmt.Sprintf("%s_%s", openidsStr, userBoundInfo.IntlOpenid)
	}

	hash := sha256.New()
	hash.Write([]byte(openidsStr))
	hashValue := hash.Sum(nil)
	commentUUIDsMd5Str := hex.EncodeToString(hashValue)
	redisKey := cache.GetUsersStatsWithOpenidsAndLimit(commentUUIDsMd5Str, limit)
	redisStr, err := redis.GetClient().Get(ctx, redisKey).Result()
	if err == nil && redisStr != "" {
		err = json.Unmarshal([]byte(redisStr), userStates)
		if err == nil {
			return sortCHUserBind(userStates, limit)
		} else {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("getFansDescOrderByChUserBind json.Unmarshal err: %v", err)
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("getFansDescOrderByChUserBind Get err: %v", err)
		}
	}
	userStates, err = dao.GetUserStateByUserOpenids(openids)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("getFansDescOrderByChUserBind err: %v", err)
		return getRandUserBoundInfos(userBoundInfos, limit)
	}
	redis.GetClient().Set(ctx, redisKey, userStates, 2*time.Minute)
	return sortCHUserBind(userStates, limit)
}

// 随机选取若干个
func getRandUserBoundInfos(userBoundInfos []*model.ChUserBind, limit int) []*model.ChUserBind {
	rand.Seed(time.Now().UnixNano())
	if len(userBoundInfos) <= limit {
		return userBoundInfos
	}
	copyBoundInfos := append([]*model.ChUserBind(nil), userBoundInfos...)
	for i := len(copyBoundInfos) - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		copyBoundInfos[i], copyBoundInfos[j] = copyBoundInfos[j], copyBoundInfos[i]
	}
	return copyBoundInfos[:limit]
}

// 从资讯数据中获取extinfo中的postid
func getPostUUidFromCMSContents(ctx context.Context, cmsContents []model.CMSNew) []string {
	postUUids := make([]string, 0)
	for _, cmsContent := range cmsContents {
		if cmsContent.ExtInfo == "" {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("getPostUUidFromCMSContents cmsContent.ExtInfo is empty, cmsContent id: %s", cmsContent.ContentId)
			continue
		} else {
			var postext PostExt
			err := json.Unmarshal([]byte(cmsContent.ExtInfo), &postext)
			if err != nil {
				log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("getPostUUidFromCMSContents json.Unmarshal err: %v", err)
				continue
			}
			if postext.PostId != "" {
				postUUids = append(postUUids, postext.PostId)
			} else {
				log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("getPostUUidFromCMSContents postext.PostId is empty, cmsContent id: %s", cmsContent.ContentId)
			}
		}
	}
	return postUUids
}

// 获取优质创作内容
func getPostAndUsersOfExcellenCreations(ctx context.Context, postUuids []string, language string) (*activity_pb.GetHighQualityCreationRsp, error) {
	rsp := &activity_pb.GetHighQualityCreationRsp{}
	postContents, err := dao.BatchGetPostContentsWithLanguage(postUuids)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("getExcellentCreatorsByLanguage FetchPostByLanguage err: %v", err)
		return nil, err
	}
	postUid2PostContents := make(map[string][]*model.PostContent, 0)
	userOpenids := make([]string, 0)
	for _, post := range postContents {
		userOpenids = append(userOpenids, post.IntlOpenid)
		postUid2PostContents[post.PostUUID] = append(postUid2PostContents[post.PostUUID], post)
	}
	userContents, err := dao.GetUserListByOpenid(userOpenids)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("getExcellentCreatorsByLanguage GetUserListByOpenid err: %v", err)
		return nil, err
	}

	for _, posts := range postUid2PostContents {
		var curLanguagePost *model.PostContent
		// 找到当前语种的帖子，首个帖子兜底
		for _, postItem := range posts {
			if postItem.Language == language {
				curLanguagePost = postItem
				break
			} else if curLanguagePost == nil {
				curLanguagePost = postItem
			}
		}
		if curLanguagePost == nil {
			continue
		}
		for _, userContent := range userContents {
			if curLanguagePost.IntlOpenid == userContent.IntlOpenid {
				pics := strings.Split(curLanguagePost.PicUrls, ",")
				postItem := &activity_pb.HighQualityCreationItem{
					Post: &activity_pb.HighQualityCreationPostItem{
						Title:          curLanguagePost.Title,
						Content:        curLanguagePost.Content,
						ContentSummary: curLanguagePost.ContentSummary,
						PicUrls:        pics,
						PostUuid:       curLanguagePost.PostUUID,
						ExtInfo:        curLanguagePost.ExtInfo,
					},
					User: &standalonesite_user.UserInfo{
						Id:         0,
						IntlOpenid: curLanguagePost.IntlOpenid,
						Username:   html.UnescapeString(userContent.Username),
						Avatar:     userContent.Avatar,
						Remark:     html.UnescapeString(userContent.Remark),
					},
				}
				rsp.List = append(rsp.List, postItem)
				break
			}
		}
	}
	return rsp, nil
}
