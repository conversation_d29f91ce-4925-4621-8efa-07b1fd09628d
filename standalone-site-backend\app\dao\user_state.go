package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

func CreateEmptyUserState(intlOpenid string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserState{}).TableName()).Create(&model.UserState{
		IntlOpenid: intlOpenid,
	}).Error
}

func BatchCreateUserState(data []*model.UserState) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserState{}).TableName()).CreateInBatches(data, 100).Error
}

func GetUserStateByUserOpenid(intlOpenid string) (*model.UserState, error) {
	userStateInfo := &model.UserState{
		IntlOpenid: intlOpenid,
	}
	err := DB.SelectConnect("db_standalonesite").Table((&model.UserState{}).TableName()).Where("intl_openid = ?", intlOpenid).FirstOrCreate(&userStateInfo).Error
	if err != nil {
		return nil, err
	}
	return userStateInfo, nil
}

func GetUserStateByUserOpenids(intlOpenids []string) ([]*model.UserState, error) {
	var userStateInfo []*model.UserState
	err := DB.SelectConnect("db_standalonesite").Table((&model.UserState{}).TableName()).Where("intl_openid in ?", intlOpenids).Find(&userStateInfo).Error
	if err != nil {
		return nil, err
	}
	return userStateInfo, nil
}

// UpdateUserFansNum 更新用户粉丝数
func UpdateUserFansNum(intlOpenid string, fansCount int32) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserState{}).TableName()).Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"fans_num":    fansCount,
	}).Error
}

// UpdateUserFollowNum 更新用户关注数
func UpdateUserFollowNum(intlOpenid string, followCount int32) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserState{}).TableName()).Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"follow_num":  followCount,
	}).Error
}

// UpdateUserPostNum 更新用户动态数量
func UpdateUserPostNum(intlOpenid string, allPostCount int32, postCount int32) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserState{}).TableName()).Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Updates(map[string]interface{}{
		"modified_on":  time.Now().Unix(),
		"post_num":     postCount,
		"all_post_num": allPostCount,
	}).Error
}

// UpdateUserPostStarNum 更新用户点赞数
func UpdateUserPostStarNum(intlOpenid string, postStarCount int32) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserState{}).TableName()).Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Updates(map[string]interface{}{
		"modified_on":   time.Now().Unix(),
		"post_star_num": postStarCount,
	}).Error
}

func UpdateUserFollowsFansNumber(tx *gorm.DB, intlOpenid string, followCount int32, fansCount int32) error {
	if tx == nil {
		tx = DB.SelectConnect("db_standalonesite")
	}
	return tx.Table((&model.UserState{}).TableName()).Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"follow_num":  followCount,
		"fans_num":    fansCount,
	}).Error
}
