package news

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/http"
	"time"
	"trpc.publishing_application.standalonesite/app/logic/attachment"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	"google.golang.org/api/googleapi/transport"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	hotService "trpc.publishing_application.standalonesite/app/logic/hot"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/api/youtube/v3"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
)

type PostExt struct {
	Platform   string `json:"platform"`
	VideoCover string `json:"video_cover"`
	VideoDesc  string `json:"video_desc"`
	VideoId    string `json:"video_id"`
	VideoTitle string `json:"video_title"`
}

// SyncYoutubeNews 同步youtube资讯
func SyncYoutubeNews(ctx context.Context) (err error) {
	// 防止panic
	defer recovery.CatchGoroutinePanic(ctx)
	log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("SyncYoutubeNews ticker at %v", time.Now())
	syncEventPostCacheKey := cache.GetSyncEventPostTaskKey()
	if ok, _ := redis.GetClient().SetNX(ctx, syncEventPostCacheKey, "1", config.GetConfig().Dynamic.SyncYoutubeNewsTimeoutDuration*time.Second).Result(); ok {
		// 完成之后删除锁占用
		defer redis.GetClient().Del(context.Background(), syncEventPostCacheKey)
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("SyncYoutubeNews start at %v", time.Now())

		client := &http.Client{
			Transport: &transport.APIKey{Key: config.GetConfig().GoogleConf.APIKey},
		}
		// 初始化一个youtube的客户端
		service, err1 := youtube.New(client)
		if err1 != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("Error creating new YouTube client: %v", err)
			return
		}
		// 获取频道下面所有的视频
		getVideoIds(ctx, service)
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("SyncYoutubeNews end at %v", time.Now())
	}
	return
}

// getVideoIds 拿到所有的video ids
func getVideoIds(ctx context.Context, service *youtube.Service) {
	// 定义多语言
	languageMap := map[string]string{
		"UCLI0h6wX_RpEbDcB11UV_RA": "zh-TW",
		"UCvi81mltkpbF2UEvC_e864g": "ja",
		"UCzoVPjV8FF2om0Zj3XRPwVw": "ko",
		"UCmmgZwsqQRD6PKeo87uq1bA": "en",
	}

	// 通过 https://commentpicker.com/youtube-channel-id.php 获取channel_id
	// 访问链接如：https://www.youtube.com/channel/UCLI0h6wX_RpEbDcB11UV_RA
	response, err := channelsList(service, []string{"contentDetails"}, []string{
		"UCLI0h6wX_RpEbDcB11UV_RA", // TW
		"UCvi81mltkpbF2UEvC_e864g", // japan
		"UCzoVPjV8FF2om0Zj3XRPwVw", // kr
		"UCmmgZwsqQRD6PKeo87uq1bA", // en
	})
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetVideoIds get channels list failed, err: %v", err)
		// 评到获取失败，直接退出
		return
	}
	// 获取板块信息
	plateInfo, err := dao.PlateGet(&dao.PlateConditions{
		UniqueIdentifier: constants.PLATE_EVENT,
	})
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetVideoIds GetPlateList failed, err: %v", err)
		return
	}

	needInsertVideoIds := make([]string, 0)
	for _, channel := range response.Items {
		playlistId := channel.ContentDetails.RelatedPlaylists.Uploads
		log.WithFieldsContext(ctx, "log type", constants.LogType_Standalonesite).Infof("SyncYoutubeNews playlistId:%s, running at %v", playlistId, time.Now())
		// 读取出当前频道映射的id
		var language string
		if lang, ok := languageMap[channel.Id]; ok {
			language = lang
		} else {
			language = "en"
		}
		nextPageToken := ""
		for {
			// Retrieve next set of items in the playlist.
			playlistResponse, getPlayErr := playlistItemsList(service, []string{"snippet", "status"}, playlistId, nextPageToken)
			if getPlayErr != nil {
				log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetVideoIds get play list failed, play list ids: [%v], next page token: [%s], getPlayErr: %v", playlistId, nextPageToken, getPlayErr)
				// 报错直接退出当前循环进入下一个频道，防止死循环
				break
			}

			var videoIds []string
			for _, playlistItem := range playlistResponse.Items {
				// title := playlistItem.Snippet.Title
				// 只有公开的数据才获取
				if playlistItem.Status.PrivacyStatus == "public" {
					videoId := playlistItem.Snippet.ResourceId.VideoId
					videoIds = append(videoIds, videoId)
				}
			}
			// 获取板块下面社媒的动态列表数据
			postList, err := dao.GetPostExternalListBySocialMediaId(videoIds, plateInfo.ID)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetVideoIds GetPostList failed, err: %v", err)
				continue
			}
			// 过滤已经入库的数据，目前只做新增数据的入库
			for _, videoId := range videoIds {
				// 判断是否已存在该视频数据
				isExist := false
				for _, post := range postList {
					if videoId == post.SocialmediaPostId {
						isExist = true
						break
					}
				}
				if !isExist {
					needInsertVideoIds = append(needInsertVideoIds, videoId)
					if len(needInsertVideoIds) > 40 {
						batchHandleVideoIds(ctx, needInsertVideoIds, plateInfo.ID, language, service)
						needInsertVideoIds = make([]string, 0)
					}

				}
			}

			// Set the token to retrieve the next page of results
			// or exit the loop if all results have been retrieved.
			nextPageToken = playlistResponse.NextPageToken
			if nextPageToken == "" {
				break
			}
		}
		// 处理剩下的没到100条的数组的数据，再进入下一个channel的数据处理
		if len(needInsertVideoIds) > 0 {
			batchHandleVideoIds(ctx, needInsertVideoIds, plateInfo.ID, language, service)
			needInsertVideoIds = make([]string, 0)
		}
	}
}

// PlaylistItemsList youtube播放列表:https://developers.google.com/youtube/v3/docs/playlists/list?hl=zh-cn
func playlistItemsList(service *youtube.Service, part []string, playlistId string, pageToken string) (
	response *youtube.PlaylistItemListResponse, err error,
) {
	response = &youtube.PlaylistItemListResponse{}
	call := service.PlaylistItems.List(part)
	call = call.PlaylistId(playlistId).MaxResults(50)
	if pageToken != "" {
		call = call.PageToken(pageToken)
	}
	response, err = call.Do()
	// handleError(err, "")

	reportParam := report.SocialReportData{
		ProjectID: 83,
		GameID:    16,
		Channel:   "YouTube",
		Api:       "PlaylistItems.List",
		ApiDesc:   "返回与 API 请求参数匹配的播放列表集合",
		ReqType:   1,
		RetCode:   0,
		ErrMsg:    "",
		Env:       trpc.GlobalConfig().Global.EnvName,
	}
	if err != nil {
		reportParam.RetCode = 500
		reportParam.ErrMsg = err.Error()
		go report.SocialReport(reportParam)
		return
	} else {
		go report.SocialReport(reportParam)
	}
	return
}

// ChannelsList youtube频道列表:https://developers.google.com/youtube/v3/docs/channels/list?hl=zh-cn
func channelsList(service *youtube.Service, part []string, ids []string) (response *youtube.ChannelListResponse, err error) {
	response = &youtube.ChannelListResponse{}
	call := service.Channels.List(part)
	call = call.Id(ids...)
	response, err = call.Do()
	reportParam := report.SocialReportData{
		ProjectID: 83,
		GameID:    16,
		Channel:   "YouTube",
		Api:       "Channels.List",
		ApiDesc:   "返回符合请求条件的零个或多个 channel 资源的集合",
		ReqType:   1,
		RetCode:   0,
		ErrMsg:    "",
		Env:       trpc.GlobalConfig().Global.EnvName,
	}
	if err != nil {
		reportParam.RetCode = 500
		reportParam.ErrMsg = err.Error()
		go report.SocialReport(reportParam)
		return
	} else {
		go report.SocialReport(reportParam)
	}
	// handleError(err, "")
	return
}

// 分批处理数据，获取视频详情、入库的操作
func batchHandleVideoIds(c context.Context, videoIds []string, plateId int64, language string, service *youtube.Service) {
	if len(videoIds) == 0 {
		return
	}
	// 获取视频的详情
	response, getVideoErr := getVideoDetails(service, []string{"snippet", "statistics"}, videoIds)
	if getVideoErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("batchHandleVideoIds get video detail, video ids:[%v] err: %v", videoIds, getVideoErr)
		return
	}
	for i, item := range response.Items {
		// 创建动态
		if item == nil || item.Snippet == nil {
			continue
		}

		// 获取视频类型
		platform, err := getVideoType(c, item.Id)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("batchHandleVideoIds getVideoType failed, item :%v, err: %v", item, err)
			continue
		}
		// p_post表动态信息
		var createdOn int64
		var createdOnMs int64
		createdOnT, err := time.Parse(time.RFC3339, item.Snippet.PublishedAt)
		if err != nil {
			createdOn = time.Now().Unix()
			createdOnMs = time.Now().UnixMicro()
		} else {
			// 转换为时间戳（秒）
			createdOn = createdOnT.Unix()
			randomThreeDigits := rand.Intn(10000)
			createdOnMs = createdOnT.UnixMicro() + int64(randomThreeDigits)
		}

		eventPostModel := &model.PostExternalFormat{
			CreatedOn:         createdOn,
			ModifiedOn:        0,
			DeletedOn:         0,
			IsDel:             0,
			PostUUID:          util.CreateSnowflakeID(),
			PlateID:           int32(plateId),
			Language:          language,
			Type:              int32(constants.OutVideoPost),
			IsAudit:           1,
			SocialmediaPostId: item.Id,
			GameId:            "16",
			AreaId:            "global",
			CreatedOnMs:       createdOnMs,
			Platform:          platform,
		}

		var commentCount, likeCount, browseCount int64
		if item.Statistics != nil {
			commentCount = int64(item.Statistics.CommentCount)
			likeCount = int64(item.Statistics.LikeCount)
			browseCount = int64(item.Statistics.ViewCount)
		}
		var previewUrl string
		// 转存图片链接
		if item.Snippet.Thumbnails.Default.Url != "" {
			previewUrl = attachment.TransferVideoImage(c, 3, item.Snippet.Thumbnails.Default.Url, "16", "global")
		}
		var postContentExtStr string
		postContentExt := &PostExt{
			Platform:   platform,
			VideoCover: previewUrl,
			VideoDesc:  "",
			VideoId:    item.Id,
			VideoTitle: item.Snippet.Title,
		}
		marshal, err := json.Marshal(postContentExt)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("batchHandleVideoIds json.Marshal failed, item :%v, err: %v", item, getVideoErr)
			continue
		}
		postContentExtStr = string(marshal)

		originalURL := fmt.Sprintf("https://www.youtube.com/watch?v=%s", item.Id)
		if platform == constants.VideoYoutubeShort {
			originalURL = fmt.Sprintf("https://www.youtube.com/shorts/%s", item.Id)
		}
		// p_post_content_xx动态内容信息
		postContentModel := &model.PostContent{
			Model: &model.Model{
				CreatedOn: createdOn,
			},
			PostUUID:        eventPostModel.PostUUID,
			IsOriginal:      int32(constants.NotOriginal),
			OriginalURL:     originalURL,
			Platform:        platform,
			OriginalReprint: 2,
			Title:           item.Snippet.Title,
			Content:         item.Snippet.Description,
			ContentSummary:  item.Snippet.Description,
			PicUrls:         "",
			ExtInfo:         postContentExtStr,
			Language:        language,
			Order:           int32(i + 1),
		}

		postStatsModel := &model.PostStats{
			Model: &model.Model{
				CreatedOn: createdOn,
			},
			PostUUID:     eventPostModel.PostUUID,
			CommentCount: commentCount,
			UpvoteCount:  likeCount,
			UpvoteMap:    "{}",
			BrowseCount:  browseCount,
		}

		// 初始化热度
		hotNum := hotService.CalculatingPostHotNum(postStatsModel)

		eventPostModel.HotNum = hotNum
		// 写入p_post表
		err = dao.CreatePostExternal(eventPostModel)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("batchHandleVideoIds CreatePost failed, item :%v, err: %v", item, err)
			continue
		}

		// 写入p_post_content_xx表
		err = dao.CreatePostContent(postContentModel)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("batchHandleVideoIds CreatePostContent failed, item :%v, err: %v", item, err)
		}

		// 写入p_post_stats表
		err = dao.CreatePostStats(postStatsModel)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("batchHandleVideoIds CreatePostStats failed, item :%v, err: %v", item, err)
		}
		//
		//// 写入es
		//postModel := &model.Post{
		//	Model: &model.Model{
		//		ID:        eventPostModel.ID,
		//		CreatedOn: eventPostModel.CreatedOn,
		//	},
		//	PostUUID:          eventPostModel.PostUUID,
		//	PlateID:           eventPostModel.PlateID,
		//	Language:          eventPostModel.Language,
		//	Type:              eventPostModel.Type,
		//	IsAudit:           1,
		//	SocialmediaPostId: item.Id,
		//	GameId:            "16",
		//	AreaId:            "global",
		//}
		//tweet.PushPostToSearch(postModel, postContentModel, make([]int64, 0), hotNum)
	}
}

func getVideoType(c context.Context, videoId string) (string, error) {
	videoShortURL := fmt.Sprintf("https://www.youtube.com/shorts/%s", videoId)
	var videoType string
	// 发起HTTP GET请求
	client := &http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 如果有重定向，via 切片将包含之前的请求
			if len(via) > 0 {
				fmt.Printf("Redirected to: %s\n", req.URL)
				// 返回 http.ErrUseLastResponse 以防止客户端跟随重定向
				return http.ErrUseLastResponse
			}
			return nil
		},
	}
	resp, err := client.Get(videoShortURL)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getVideoType failed, videoURL :%v, err: %v", videoShortURL, err)
		return videoType, err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 300 && resp.StatusCode < 400 {
		videoType = constants.VideoYoutube
	} else {
		videoType = constants.VideoYoutubeShort
	}

	return videoType, nil
}

// GetVideoDetails 获取video的详情:https://developers.google.com/youtube/v3/docs/videos/list?hl=zh-cn
func getVideoDetails(service *youtube.Service, part []string, ids []string) (
	response *youtube.VideoListResponse, err error,
) {
	response = &youtube.VideoListResponse{}
	call := service.Videos.List(part)
	call = call.Id(ids...)
	response, err = call.Do()

	reportParam := report.SocialReportData{
		ProjectID: 83,
		GameID:    16,
		Channel:   "YouTube",
		Api:       "Videos.List",
		ApiDesc:   "返回与 API 请求参数匹配的视频列表",
		ReqType:   1,
		RetCode:   0,
		ErrMsg:    "",
		Env:       trpc.GlobalConfig().Global.EnvName,
	}
	if err != nil {
		reportParam.RetCode = 500
		reportParam.ErrMsg = err.Error()
		go report.SocialReport(reportParam)
		return
	} else {
		go report.SocialReport(reportParam)
	}
	return
}
