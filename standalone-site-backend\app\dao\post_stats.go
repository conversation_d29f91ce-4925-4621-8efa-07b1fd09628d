package dao

import (
	"encoding/json"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"time"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetPostStatsByPostUuid(postUuid string) (*model.PostStats, error) {
	var postStats *model.PostStats
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).Where("post_uuid = ?", postUuid).Attrs(&model.PostStats{
		PostUUID:  postUuid,
		UpvoteMap: "{}",
	}).FirstOrCreate(&postStats).Error
	if err != nil {
		return nil, err
	}
	return postStats, nil
}

func BatchGetPostStatsByPostUuids(postUuids []string) ([]*model.PostStats, error) {
	var postStats []*model.PostStats
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).Where("post_uuid in ?", postUuids).Find(&postStats).Error
	if err != nil {
		return nil, err
	}
	return postStats, nil
}

// 更新动态数据统计表
func UpdatePostStats(postStats *model.PostStats) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).Updates(&postStats).Error
}

// 创建动态数据统计
func CreatePostStats(postStats *model.PostStats) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).Create(&postStats).Error
}

// 创建动态数据统计
func BatchCreatePostStats(postStats []*model.PostStats) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).CreateInBatches(&postStats, 1000).Error
}

// 更新动态浏览数
func UpdatePostStatsBrowse(postUuid string, browse int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).Where("post_uuid = ? and is_del = 0", postUuid).Update("browse_count", browse).Error
}

// 更新动态收藏数
func UpdatePostStatsCollection(postUuid string, collectionCount int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).Where("post_uuid = ? and is_del = 0", postUuid).Update("collection_count", collectionCount).Error
}

// 更新动态转发数
func UpdatePostStatsForward(postUuid string, forward int32) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).Where("post_uuid = ? and is_del = 0", postUuid).Update("forward_count", forward).Error
}

// 更新动态评论数
func UpdatePostStatsComment(postUuid string, commentCount int32) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).Where("post_uuid = ? and is_del = 0", postUuid).Update("comment_count", commentCount).Error
}

// 更新统计
func UpdatePostStatsData(postUuid string, data map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).Where("post_uuid = ? and is_del = 0", postUuid).Updates(&data).Error
}

// PostStatsUpvoteCountUpdate 更新post点赞数
func PostStatsUpvoteCountUpdate(postUUID string, upvoteMap map[int64]int64) error {
	if postUUID == "" {
		return gorm.ErrRecordNotFound
	}
	tableName := (&model.PostStats{}).TableName()
	// 将 map 编码为 JSON
	jsonData, err := json.Marshal(upvoteMap)
	if err != nil {
		return err
	}
	var upvoteCount int64
	if len(upvoteMap) > 0 {
		for _, upvoteI := range upvoteMap {
			upvoteCount += upvoteI
		}
	}

	return DB.SelectConnect("db_standalonesite").Table(tableName).Where("post_uuid = ? and is_del = 0", postUUID).Updates(map[string]interface{}{
		"upvote_count": upvoteCount,
		"upvote_map":   string(jsonData),
		"modified_on":  time.Now().Unix(),
	}).Error
}

func GetPostStatsByPostUuidIgnoreDel(postUuid string) (*model.PostStats, error) {
	var postStats *model.PostStats
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostStats{}).TableName()).Unscoped().Where("post_uuid = ?", postUuid).First(&postStats).Error
	if err != nil {
		return nil, err
	}
	return postStats, nil
}
