package exobornefriendsinvite

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"cloud.google.com/go/bigquery"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/datadump"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gameAmsPb "git.code.oa.com/trpcprotocol/publishing_marketing/game_ams"
	"google.golang.org/api/iterator"
	"google.golang.org/api/option"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	teamLogic "trpc.act.logicial/app/logic/team"
	model "trpc.act.logicial/app/model/exoborne_friends_invite"
	teamModel "trpc.act.logicial/app/model/team"
)

var bqClient *bigquery.Client
var loc = time.FixedZone("UTC+0", 0)

func getBigQueryClient(ctx context.Context) (*bigquery.Client, error) {
	credentialsConfig := config.GetConfig().CredentialsConfig
	credentialsJSON, err := json.Marshal(credentialsConfig)
	if err != nil {
		err = errs.NewCustomError(ctx, code.BaseConfigError, "credentials config error")
		return nil, err
	}
	if bqClient == nil {
		client, err := bigquery.NewClient(ctx, "tencent-sharkmob", option.WithCredentialsJSON([]byte(credentialsJSON)))
		if err != nil {
			return nil, err
		}
		bqClient = client
	}
	return bqClient, nil
}

// GetTaskStatusByCredentials TODO
func GetTaskStatusByCredentials(ctx context.Context, openids []string) (deploytimes int64, err error) {

	sort.Strings(openids)
	result := strings.Join(openids, ",")
	var item *model.ExobrnFriendsInviteDeplopytimes
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.ExobrnFriendsInviteDeplopytimes{}.TableName()).
		Where("uids = ?", result).
		First(&item)
	if db.Error != nil {
		if db.Error == gorm.ErrRecordNotFound {
			deploytimes = 0
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	deploytimes = item.Delopytimes
	return
}

// GetUserName TODO
func GetUserName(ctx context.Context) (roleName string, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var item *model.ExobrnFriendsInviteName
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.ExobrnFriendsInviteName{}.TableName()).
		Where("openid = ?", strings.Split(userAccount.Uid, "-")[1]).
		First(&item)
	if db.Error != nil {
		if db.Error == gorm.ErrRecordNotFound {
			roleName = ""
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	roleName = item.RoleName
	return
}

// JoinRank TODO
func JoinRank(ctx context.Context, teamCode string, openids []string) (err error) {
	insertData := &model.ExobrnFriendsInviteJoinRank{
		TeamCode: teamCode,
	}
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.ExobrnFriendsInviteJoinRank{}.TableName()).
		Where("team_code = ?", teamCode).FirstOrCreate(&insertData)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return err
	}

	for _, openid := range openids {
		onlineData := &model.ExobrnFriendsInviteOnline{
			TeamCode:       teamCode,
			Openid:         openid,
			IsLogin:        0,
			OnlineDuration: 0,
		}
		db = DB.DefaultConnect().Debug().WithContext(ctx).Table(model.ExobrnFriendsInviteOnline{}.TableName()).
			Where("team_code = ? and openid = ?", teamCode, openid).FirstOrCreate(&onlineData)
		if db.Error != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return err
		}
	}

	return
}

// SyncRankJoin TODO
func SyncRankJoin(ctx context.Context) (err error) {
	scheduleCtx := context.Background()

	FsourceId := "pageV3-1923"
	tableName, err := teamLogic.GetTeamTableName(scheduleCtx, FsourceId)
	if err != nil {
		return
	}
	teamAccountCode := make([]*teamModel.TeamAccountCode, 0)
	selectdb := DB.DefaultConnect().WithContext(scheduleCtx).Table(tableName).Where("Fsource_id = ? and is_delete = 0",
		FsourceId).Find(&teamAccountCode)
	if selectdb.Error != nil {
		// 报错,数据库操作异常
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", selectdb.Error)
		return
	}
	// 拿到所有teamCode
	for _, item := range teamAccountCode {
		logTableName, err := teamLogic.GetTeamLogTableName(scheduleCtx, FsourceId)
		if err != nil {
			return err
		}
		teamLogDatas := make([]*teamModel.TeamCodeLog, 0)

		selectdb := DB.DefaultConnect().WithContext(scheduleCtx).Table(logTableName).Where(
			"Fsource_id = ? and team_code = ? and is_delete = 0",
			FsourceId, item.TeamCode).Find(&teamLogDatas)
		if selectdb.Error != nil {
			// 报错,数据库操作异常
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", selectdb.Error)
			return err
		}

		// 判断每个teamCode是否满足三人 满足就调用joinRank
		if len(teamLogDatas) == 3 {
			err = JoinRank(scheduleCtx, item.TeamCode, []string{
				strings.Split(teamLogDatas[0].InviteeUid, "-")[1],
				strings.Split(teamLogDatas[1].InviteeUid, "-")[1],
				strings.Split(teamLogDatas[2].InviteeUid, "-")[1],
			})
			if err != nil {
				return err
			}
		}
	}
	return
}

// SyncUserDelopytimes 同步对局数
func SyncUserDelopytimes(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	now := time.Now()

	specialTime := time.Date(2025, 3, 3, 13, 5, 0, 0, loc)
	if now.Unix() >= specialTime.Unix() {
		return
	}
	client, err := getBigQueryClient(scheduleCtx)
	if err != nil {
		return
	}
	defer client.Close()
	newTime := time.Now().Format("2006-01-02")
	// newTime := "2025-02-20"
	// sqlRow := "SELECT openidlist, sum(deploytimes) FROM tencent-sharkmob.rebirth.app_rebirth_ct3_friends_invite_5min WHERE date(dt)>='2006-01-02'  GROUP BY openidlist"
	sqlRow := fmt.Sprintf(
		"SELECT count(*) FROM tencent-sharkmob.rebirth.app_rebirth_ct3_friends_invite_5min WHERE date(dt)>='%v'", newTime)
	log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserDelopytimes").Infof("sqlRow: %v", sqlRow)
	q := client.Query(sqlRow)
	log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserDelopytimes").Infof("time")
	// Run the query and print results when the query job is completed.
	job, err := q.Run(scheduleCtx)
	if err != nil {
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserDelopytimes").Infof("run")
	status, err := job.Wait(scheduleCtx)
	if err != nil {
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserDelopytimes").Infof("wait")
	if err = status.Err(); err != nil {
		return
	}
	it, err := job.Read(scheduleCtx)
	if err != nil {
		return
	}
	var total int64
	for {
		var row []bigquery.Value
		err = it.Next(&row)
		if err == iterator.Done {
			err = nil
			break
		}
		if err != nil {
			return
		}
		total = row[0].(int64)
	}
	pageSize := 1000
	var wg sync.WaitGroup
	maxGoroutines := 2
	semaphore := make(chan struct{}, maxGoroutines)
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))

	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		wg.Add(1)
		semaphore <- struct{}{}
		go func(pageNumber int) {
			defer func() {
				<-semaphore
				wg.Done()
			}()
			sqlRow := fmt.Sprintf(
				"SELECT openidlist, max(deploytimes) FROM tencent-sharkmob.rebirth.app_rebirth_ct3_friends_invite_5min WHERE date(dt)>='%v'  GROUP BY openidlist order by openidlist desc limit 1000 offset %v", newTime, (pageNumber-1)*1000)
			log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserDelopytimes").Infof("sqlRow: %v", sqlRow)
			q := client.Query(sqlRow)
			// Run the query and print results when the query job is completed.
			job, err := q.Run(scheduleCtx)
			if err != nil {
				return
			}
			status, err := job.Wait(scheduleCtx)
			if err != nil {
				return
			}
			if err = status.Err(); err != nil {
				return
			}
			it, err := job.Read(scheduleCtx)
			if err != nil {
				return
			}
			data := make([]*model.ExobrnFriendsInviteDeplopytimes, 0)
			for {
				var row []bigquery.Value
				err = it.Next(&row)
				if err == iterator.Done {
					err = nil
					break
				}
				if err != nil {
					return
				}
				data = append(data, &model.ExobrnFriendsInviteDeplopytimes{
					Uids:        row[0].(string),
					Delopytimes: row[1].(int64),
				})
			}
			var owg sync.WaitGroup
			omaxGoroutines := 50
			osemaphore := make(chan struct{}, omaxGoroutines)

			for _, item := range data {
				owg.Add(1)
				go func(item *model.ExobrnFriendsInviteDeplopytimes) {
					osemaphore <- struct{}{}
					defer func() {
						<-osemaphore
						owg.Done()
					}()
					var haveItem *model.ExobrnFriendsInviteDeplopytimes
					db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.ExobrnFriendsInviteDeplopytimes{}.TableName()).
						Where("uids = ?", item.Uids).
						First(&haveItem)
					if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"db error, \t [Error]:{%v} ", db.Error.Error())
						return
					}
					if db.Error == gorm.ErrRecordNotFound {
						db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
							model.ExobrnFriendsInviteDeplopytimes{}.TableName()).
							Where("uids = ?", item.Uids).
							Save(&item)
						if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
							err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
								"db error, \t [Error]:{%v} ", db.Error.Error())
							return
						}
					} else {
						if haveItem.Delopytimes != item.Delopytimes {
							haveItem.Uids = item.Uids
							haveItem.Delopytimes = item.Delopytimes
							db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
								model.ExobrnFriendsInviteDeplopytimes{}.TableName()).
								Where("uids = ?", item.Uids).
								Save(&haveItem)
							if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
								err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
									"db error, \t [Error]:{%v} ", db.Error.Error())
								return
							}
						}
					}

				}(item)
			}
			owg.Wait()
		}(pageNumber)
	}
	wg.Wait()

	return
}

// SyncUserName 同步用户名称
func SyncUserName(ctx context.Context) (err error) {
	specialTime := time.Date(2025, 2, 26, 1, 0, 0, 0, loc)
	now := time.Now()

	if now.Unix() >= specialTime.Unix() {
		return
	}
	scheduleCtx := context.Background()
	client, err := getBigQueryClient(scheduleCtx)
	if err != nil {
		return
	}
	defer client.Close()
	utcMinus5 := time.FixedZone("UTC-5", -5*60*60)
	newTime := time.Now().In(utcMinus5).Format("2006-01-02")
	// newTime := "2025-02-20"
	// sqlRow := "SELECT role_name, account_id FROM tencent-sharkmob.rebirth_dw.dwd_player_register_rti_live WHERE date(dt_eventtime)>='2006-01-02' GROUP BY account_id, role_name"
	sqlRow := fmt.Sprintf(
		"SELECT count(*) FROM tencent-sharkmob.rebirth_dw.dwd_player_register_rti_live WHERE date(dt_eventtime)>='%v' ",
		newTime)
	log.WithFieldsContext(scheduleCtx, "log_type", "GetUserName").Infof("sqlRow: %v", sqlRow)
	q := client.Query(sqlRow)
	// Run the query and print results when the query job is completed.
	job, err := q.Run(scheduleCtx)
	if err != nil {
		return
	}
	status, err := job.Wait(scheduleCtx)
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserName_err").Infof("err: %v", err)
		return
	}
	if err = status.Err(); err != nil {
		return
	}
	it, err := job.Read(scheduleCtx)
	if err != nil {
		return
	}
	var total int64
	for {
		var row []bigquery.Value
		err = it.Next(&row)
		if err == iterator.Done {
			err = nil
			break
		}
		if err != nil {
			log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserName_err").Infof("err: %v", err)
			return
		}
		total = row[0].(int64)
	}
	pageSize := 1000
	maxGoroutines := 5
	semaphore := make(chan struct{}, maxGoroutines)

	var wg sync.WaitGroup
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))

	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		wg.Add(1)

		go func(pageNumber int) (err error) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			sqlRow := fmt.Sprintf(
				"SELECT playername_last, account_id, dt_eventtime  FROM tencent-sharkmob.rebirth_dw.dwd_player_register_rti_live WHERE date(dt_eventtime)>='%v' GROUP BY account_id, playername_last, dt_eventtime order by dt_eventtime desc limit 1000 offset %v", newTime, (pageNumber-1)*1000)
			log.WithFieldsContext(scheduleCtx, "log_type", "GetUserName").Infof("sqlRow: %v", sqlRow)
			q := client.Query(sqlRow)
			// Run the query and print results when the query job is completed.
			job, err := q.Run(scheduleCtx)
			if err != nil {
				return
			}
			status, err := job.Wait(scheduleCtx)
			if err != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserName_err").Infof("err: %v", err)
				return
			}
			if err = status.Err(); err != nil {
				return
			}
			it, err := job.Read(scheduleCtx)
			if err != nil {
				return
			}
			data := make([]*model.ExobrnFriendsInviteName, 0)
			for {
				var row []bigquery.Value
				err = it.Next(&row)
				if err == iterator.Done {
					err = nil
					break
				}
				if err != nil {
					log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserName_err").Infof("err: %v", err)
					return
				}

				data = append(data, &model.ExobrnFriendsInviteName{
					RoleName: row[0].(string),
					Openid:   row[1].(string),
				})
			}

			var owg sync.WaitGroup
			omaxGoroutines := 50
			osemaphore := make(chan struct{}, omaxGoroutines)

			for _, item := range data {
				owg.Add(1)
				go func(item *model.ExobrnFriendsInviteName) (err error) {
					osemaphore <- struct{}{}
					defer func() {
						<-osemaphore
						owg.Done()
					}()
					var haveItem *model.ExobrnFriendsInviteName
					db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.ExobrnFriendsInviteName{}.TableName()).
						Where("openid = ?", item.Openid).
						First(&haveItem)
					if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"db error, \t [Error]:{%v} ", db.Error.Error())
						log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserName_err").Infof("err: %v", err)
						return
					}
					if db.Error == gorm.ErrRecordNotFound {
						db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.ExobrnFriendsInviteName{}.TableName()).
							Where("openid = ?", item.Openid).
							Save(&item)
						if db.Error != nil {
							err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
								"db error, \t [Error]:{%v} ", db.Error.Error())
							log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserName_err").Infof("err: %v", err)
							return
						}
					} else {
						if haveItem.RoleName != item.RoleName {
							haveItem.RoleName = item.RoleName
							db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.ExobrnFriendsInviteName{}.TableName()).
								Where("openid = ?", item.Openid).
								Save(&haveItem)
							if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
								err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
									"db error, \t [Error]:{%v} ", db.Error.Error())
								log.WithFieldsContext(scheduleCtx, "log_type", "SyncUserName_err").Infof("err: %v", err)
								return
							}
						}
					}
					return
				}(item)
			}
			owg.Wait()
			return
		}(pageNumber)
	}
	wg.Wait()
	return
}

// GetJoinStatus TODO
func GetJoinStatus(ctx context.Context, teamCode string) (isJoin bool, err error) {
	var num int64
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.ExobrnFriendsInviteJoinRank{}.TableName()).
		Where("team_code = ?", teamCode).
		Count(&num)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	isJoin = num > 0
	return
}

// IsLoginByOpenid TODO
func IsLoginByOpenid(ctx context.Context, openids []string) (isLogin bool, err error) {

	amsProxy := gameAmsPb.NewAmsClientProxy()

	nowTime := time.Now().Unix()
	startDate1 := time.Date(2025, 2, 26, 1, 30, 0, 0, time.UTC).Unix()
	endDate1 := time.Date(2025, 2, 26, 3, 30, 0, 0, time.UTC).Unix()
	startDate2 := time.Date(2025, 2, 27, 1, 30, 0, 0, time.UTC).Unix()
	endDate2 := time.Date(2025, 2, 27, 3, 30, 0, 0, time.UTC).Unix()

	// log.WithFieldsContext(ctx, "log_type", "IsLoginByOpenid").Infof("now: %v, start1: %v, end1: %v", nowTime, startDate1,
	// 	endDate1)
	isLogin = true
	for _, item := range openids {
		var data *model.ExobrnFriendsInviteOnline
		db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.ExobrnFriendsInviteOnline{}.TableName()).
			Where("openid = ?", item).
			First(&data)
		if db.Error != nil {
			if db.Error == gorm.ErrRecordNotFound {
				err = errs.NewCustomError(ctx, code.TeamNoJoin, "team is not join")
				return
			}
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}

		if (nowTime >= startDate1 && nowTime <= endDate1) || (nowTime >= startDate2 && nowTime <= endDate2) {
			if data.IsLogin == 1 {
				isLogin = true
			} else {
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         fmt.Sprintf("%v-%v", "30190", item),
					AccountType: accountPb.AccountType(1),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    item,
						GameId:    "30190",
						ChannelId: 131,
					},
				})
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
				}
				IdipParam := make([]*gameAmsPb.IdipGetItem, 0)
				amsInfo, err := amsProxy.GetInfoListByAms(ctx, &gameAmsPb.GetInfoListByAmsReq{
					SelectParam: &gameAmsPb.IdipDBParam{
						CmdId:  "********",
						GameId: "30190",
					},
					IdipParam: IdipParam,
				}, callopts...)
				if err != nil {
					return false, err
				}
				for _, item := range amsInfo.Info {
					if item.Key == "online_duration" {
						num, err := strconv.ParseInt(item.Value, 10, 64)
						if err != nil {
							return false, err
						}
						isLogin = num > data.OnlineDuration
						log.WithFieldsContext(ctx, "log_type", "IsLoginByOpenid").Infof(
							"uid: %v, ams: %v, OnlineDuration: %v, isLogin: %v",
							data.Openid, num, data.OnlineDuration, isLogin)
						if !isLogin {
							return isLogin, nil
						}
					}
				}
			}

		} else {
			if data.IsLogin == 0 {
				isLogin = false
				return isLogin, nil
			}
		}
	}
	return
}

// UpdateOnlineDuration TODO
func UpdateOnlineDuration(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	amsProxy := gameAmsPb.NewAmsClientProxy()

	data := make([]*model.ExobrnFriendsInviteOnline, 0)
	db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.ExobrnFriendsInviteOnline{}.TableName()).
		Find(&data)
	if db.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	for _, onlineItem := range data {
		log.WithFieldsContext(scheduleCtx, "log_type", "UpdateOnlineDuration").Infof("onlineItem: %+v", onlineItem)
		accountData, _ := proto.Marshal(&accountPb.UserAccount{
			Uid:         fmt.Sprintf("%v-%v", "30190", onlineItem.Openid),
			AccountType: accountPb.AccountType(1),
			IntlAccount: &accountPb.IntlAccount{
				OpenId:    onlineItem.Openid,
				GameId:    "30190",
				ChannelId: 131,
			},
		})
		callopts := []client.Option{
			client.WithMetaData(metadata.UserAccount, accountData),
		}
		IdipParam := make([]*gameAmsPb.IdipGetItem, 0)
		amsInfo, err := amsProxy.GetInfoListByAms(scheduleCtx, &gameAmsPb.GetInfoListByAmsReq{
			SelectParam: &gameAmsPb.IdipDBParam{
				CmdId:  "********",
				GameId: "30190",
			},
			IdipParam: IdipParam,
		}, callopts...)
		if err != nil {
			err = nil
		} else {
			for _, item := range amsInfo.Info {
				if item.Key == "online_duration" {
					num, err := strconv.ParseInt(item.Value, 10, 64)
					if err != nil {
						return err
					}

					if onlineItem.OnlineDuration == 0 {
						onlineItem.OnlineDuration = num
						db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.ExobrnFriendsInviteOnline{}.TableName()).
							Save(&onlineItem)
						if db.Error != nil {
							err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
								"db error, \t [Error]:{%v} ", db.Error.Error())
							return err
						}
					} else {
						// 比较 num 是否大于 startOfDayUTCPlus2 的时间戳
						if num > onlineItem.OnlineDuration && onlineItem.OnlineDuration > 0 {
							onlineItem.IsLogin = 1
							db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.ExobrnFriendsInviteOnline{}.TableName()).
								Save(&onlineItem)
							if db.Error != nil {
								err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
									"db error, \t [Error]:{%v} ", db.Error.Error())
								return err
							}
						}
					}
				}
			}
		}
	}
	return
}

// ExportJoinList TODO
func ExportJoinList(ctx context.Context) (err error) {
	// 查询数据 然后查询每个队伍里的openid
	scheduleCtx := context.Background()
	excelData := make([][]string, 0)
	excelData = append(excelData, []string{
		"openid1", "openid2", "openid3",
	})

	var data []*model.ExobrnFriendsInviteJoinRank
	db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.ExobrnFriendsInviteJoinRank{}.TableName()).
		Select(&data)
	if db.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	for _, item := range data {
		openids, err := teamLogic.GetTeamOpenidList(scheduleCtx, "pageV3-1923", item.TeamCode)
		if err != nil {
			return err
		}

		excelData = append(excelData, openids)
	}

	filepath, err := datadump.CreateExcel(scheduleCtx, "joinRankList.xlsx", excelData)
	if err != nil {
		return
	}
	mediaId, err := datadump.QWXUploadMedia(scheduleCtx, filepath, "268e5d13-f0db-4bf4-afa2-3112dabdd29c",
		"joinRankList.xlsx")
	if err != nil {
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_info").Infof("mediaId: %v", mediaId)
	option := &datadump.SendOption{
		Msgtype: "file",
		Chatid:  "wrkSFfCgAA9d0ZmRiwpSS3Y-xrxLcH4g",
		File: &datadump.FileType{
			MediaId: mediaId,
		},
	}
	_, err = datadump.QWXSend(scheduleCtx, option, "268e5d13-f0db-4bf4-afa2-3112dabdd29c")
	if err != nil {
		return
	}
	return
}
