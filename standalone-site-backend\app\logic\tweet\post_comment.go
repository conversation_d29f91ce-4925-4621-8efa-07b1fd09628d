package tweet

import (
	"context"
	"fmt"
	"strings"
	"time"

	ES "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/elasticsearch"
	"git.code.oa.com/trpc-go/trpc-go/log"
	jsoniter "github.com/json-iterator/go"
	es7 "github.com/olivere/elastic/v7"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

var JSON = jsoniter.ConfigCompatibleWithStandardLibrary

const (
	DefaultDocType = "_doc"
)

// 获取索引文档类型
func GetDocType() string {
	return DefaultDocType
}

// ES文档结构
type Document struct {
	Index   string // 索引
	Type    string // 文档类型 通常为_doc
	DocId   string // 文档id
	Refresh string // 是否实时刷新文档 支持true, false
	DocData map[string]interface{}
}

func (d *Document) init() {
	if d.Refresh != "false" {
		d.Refresh = "true"
	}
	if d.Type != "_doc" {
		d.Type = "_doc"
	}
}

func getEsClient() *es7.Client {
	return ES.SelectConnect("es_standalonesite")
}

// 单个插入&更新(文档存在时更新) refresh从配置读取
func UpdateSingleEsDoc(ctx context.Context, doc *Document) error {
	doc.init()
	buldSource := ""
	bulk := getEsClient().Bulk()
	// 构造请求
	req := es7.NewBulkUpdateRequest().Index(doc.Index).Type(doc.Type).Id(doc.DocId).
		Doc(doc.DocData).DocAsUpsert(true)

	bulk = bulk.Add(req).Refresh(doc.Refresh)
	// 查看请求内容
	src, _ := req.Source()
	buldSource = fmt.Sprintf("%s%s", buldSource, strings.Join(src, ""))

	log.InfoContextf(ctx, "UpdateSingleEsDoc. req Source:%v", buldSource)
	r, err := bulk.Do(ctx)
	log.InfoContextf(ctx, "UpdateSingleEsDoc. rsp Source:%v", util.ToJson(r))
	if err != nil {
		log.ErrorContextf(ctx, "client.Bulk do error:%s", err)
		return fmt.Errorf("UpdateSingleEsDoc error:%w", err)
	}
	if r.Errors {
		log.ErrorContextf(ctx, "client.Bulk do error rsp:%v", util.ToJson(r))
		return fmt.Errorf("UpdateSingleEsDoc rsp error")
	}
	return nil
}

// QueryByDocIds 批量查询文档
// indexDocIds. key标识index名称，values为相同索引的文档id集合
func QueryByDocIds(ctx context.Context, indexDocIds map[string][]string) ([]*es7.GetResult, error) {
	mget := getEsClient().Mget()
	for index, docIds := range indexDocIds {
		for _, docId := range docIds {
			single := es7.NewMultiGetItem().Index(index).Type(GetDocType()).Id(docId)
			mget = mget.Add(single)
		}
	}
	src, _ := mget.Source()
	log.InfoContextf(ctx, "queryByDocIds json req:%s", util.ToJson(src))
	rsp, err := mget.Do(ctx)
	log.DebugContextf(ctx, "queryByDocIds Mget rsp:%v", util.ToJson(rsp))
	if err != nil {
		log.ErrorContextf(ctx, "client.mget error:%s, indexDocIds:%v", err, indexDocIds)
		return nil, fmt.Errorf("es mget error:%w", err)
	}
	return rsp.Docs, nil
}

// QueryCommentByDocId 单个查询评论文档
func QueryCommentByDocId(ctx context.Context, index, docId string) (*model.ESComment, error) {
	indexDocIds := map[string][]string{
		index: {docId},
	}
	docs, err := QueryByDocIds(ctx, indexDocIds)
	// 批量查询文档接口，会返回同等数量的slice
	// 因此docs的长度和查询文档个数一致
	if err != nil {
		log.ErrorContextf(
			ctx, "QueryByDocIds Index(%s) Id(%s) error:%s, docs:%v", index, docId, err, docs)
		return nil, fmt.Errorf("queryByDocIds error:%w", err)
	}
	if len(docs) == 0 {
		return nil, nil
	}
	result := docs[0]
	if !result.Found {
		log.WarnContextf(ctx, "Index(%s) Id(%s) not found.", index, docId)
		return nil, nil
	}
	// 获取json
	mj, err := result.Source.MarshalJSON()
	if err != nil {
		log.ErrorContextf(ctx, "result.Source.MarshalJSON error:%s, src:%s",
			err, string(result.Source))
		return nil, fmt.Errorf("result.Source.MarshalJSON error:%w", err)
	}
	// 反序列化
	card := &model.ESComment{}
	err = JSON.Unmarshal(mj, card)
	if err != nil {
		log.ErrorContextf(ctx, "json.Unmarshal error:%s, src:%s",
			err, string(result.Source))
		return nil, fmt.Errorf("json.Unmarshal error:%w", err)
	}
	return card, nil
}

// QueryComments 查询 评论列表 基础方法
func QueryComments(ctx context.Context, indices []string,
	source *es7.SearchSource) ([]*model.ESComment, error) {
	client := getEsClient()
	src, _ := source.Source()
	log.InfoContextf(ctx, "QueryComments SearchSource json:%s", util.ToJson(src))
	// 执行查询动作
	result, err := client.Search(indices...).SearchSource(source).Do(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "client.Search error:%s, indices:%v, search source:%v",
			err, strings.Join(indices, ","), util.ToJson(src))
		return nil, fmt.Errorf("client.Search error:%w", err)
	}
	if result == nil || result.Hits == nil || result.Hits.TotalHits == nil {
		log.ErrorContextf(
			ctx, "client.Search result error. result:%s", util.ToJson(result))
		return nil, fmt.Errorf("client.Search error:%w", err)
	}
	// log.DebugContextf(ctx, "client.Search result:%s", util.ToJson(result.Hits.TotalHits))
	// 查询结果解析 若要获取准确的total数值，需要使用 TrackTotalHits(true) 方法
	// total := uint32(result.Hits.TotalHits.Value)
	docs, err := convertToComments(ctx, result.Hits.Hits)
	if err != nil {
		return nil, err
	}
	return docs, nil
}

// 转换为评论结构
func convertToComments(ctx context.Context, hits []*es7.SearchHit) ([]*model.ESComment, error) {
	if len(hits) == 0 {
		return nil, nil
	}
	// 查询结果解析
	docs := make([]*model.ESComment, 0)
	for _, hit := range hits {
		if hit.Source == nil {
			log.WarnContextf(ctx, "hit.Source is nil. hit:%v", hit)
			continue
		}
		mj, err := hit.Source.MarshalJSON()
		if err != nil {
			log.ErrorContextf(ctx, "hit.Source.MarshalJSON error:%s, src:%s", err, string(hit.Source))
			return nil, fmt.Errorf("hit.Source.MarshalJSON error:%w", err)
		}
		comment := &model.ESComment{}
		err = JSON.Unmarshal(mj, comment)
		if err != nil {
			log.ErrorContextf(ctx, "json.Unmarshal error:%s, src:%s", err, string(hit.Source))
			return nil, fmt.Errorf("JSON.Unmarshal error:%w", err)
		}
		comment.Sort = hit.Sort // 赋值排序列表
		docs = append(docs, comment)
	}

	return docs, nil
}

// DeletePostCommentReport 删除举报管理记录，包括mysql+es（软删除）
func DeletePostCommentReport(ctx context.Context, index string, reportType int, reportUUID, updater string) error {
	const (
		PostCommentReporStatusDelete int = 3
	)
	status := PostCommentReporStatusDelete
	err := dao.UpdateCommentReportStatus(reportUUID, status)
	if err != nil {
		return err
	}

	query := es7.NewBoolQuery()
	query = query.Filter(es7.NewTermQuery("content_type", reportType),
		es7.NewTermQuery("content_uuid", reportUUID))
	src, _ := query.Source()
	log.InfoContextf(ctx, "DeletePostCommentReport query json:%s", util.ToJson(src))

	scriptText := `
		ctx._source.status = params.status;
		ctx._source.update_user = params.update_user;
		ctx._source.modified_on = params.modified_on;
	`
	params := map[string]interface{}{
		"status":      status,
		"update_user": updater,
		"modified_on": time.Now().Unix(),
	}
	script := es7.NewScript(scriptText).Params(params)

	client := getEsClient()
	_, err = client.UpdateByQuery().Index(index).Query(query).Script(script).Do(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "DeletePostCommentReport error:%v, src:%v", err, util.ToJson(src))
		return fmt.Errorf("deletePostCommentReport error:%w", err)
	}

	return nil
}
