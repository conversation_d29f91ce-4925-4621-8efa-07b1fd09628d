package code

const (
	// PresentHasBeenCollected 当前礼包已被领取
	PresentHasBeenCollected = 515001
	// HokHotRankListNotData TODO
	HokHotRankListNotData = 515002
	// HokDataAlreadyExistsErr HOK数据已存在
	HokDataAlreadyExistsErr = 515003
	// HOKGeneratedDataException 生成数据异常
	HOKGeneratedDataException = 515004
	// ExecutingLuaScriptError 脚本执行异常
	ExecutingLuaScriptError = 515005
	// DataExceptionError 数据异常
	DataExceptionError = 515006
	// LuckyNumbersSentNumError 幸运号码发送数量异常
	LuckyNumbersSentNumError = 515007
	// GetIssuedLuckyNumberCountError 获取幸运号码数量异常
	GetIssuedLuckyNumberCountError      = 515008
	RandomIntInRangeError               = 515009
	DrawLotteryError                    = 515010
	HokTmpHttpError                     = 515011
	HokTmpGetGameRegionConfDataError    = 515012
	HokTmpDataUnmarshalError            = 515013
	HokTmpMissionHasNotFinish           = 515014
	HokTmpInvitationStatusException     = 515015
	HokTmpInvalidUserType               = 515016
	HokTmpUserNotWinLastTime            = 515017
	HokTmpNoPresentInformationObtained  = 515018
	HokTmpNoPresentLanguage             = 515019
	HokTmpCurrentCDKHasUsed             = 515020
	HokTmpExchangeLimitErr              = 515021 // 超过兑换限制
	HokTmpCommodityExchangeErr          = 515022 // 商品兑换异常
	HokTmpAvailableQuantityInsufficient = 515023 // 可用幸运号码数量不足
	HokTmpDataRollbackFailed            = 515024 // 数据回滚失败
	HokTmpCommodityNotExist             = 515025 // 商品不存在
	HokTmpCheckAlreadyOwnedError        = 515026 // 查询商品已拥有异常
	HokTmpGetDataGroupMetricsError      = 515027 // 获取指标数据异常
	SendHOKBandingLIPPointError         = 515028 // 发送绑定积分失败
	SendHOKBandingLIPTaskPointError     = 515029 // 发送任务积分失败
)
