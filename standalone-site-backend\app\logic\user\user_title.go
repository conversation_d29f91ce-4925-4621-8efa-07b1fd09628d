package user

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	siteUserPB "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

func SetCommentUserTitle(c context.Context, comments []*model.CommentFormated, language string) error {
	if len(comments) == 0 {
		return nil
	}
	var userIdsMap = make(map[string]struct{})

	for _, comment := range comments {
		// 去重
		userIdsMap[comment.IntlOpenid] = struct{}{}
	}

	var err error
	var userIds = make([]string, 0, len(userIdsMap))
	for i, _ := range userIdsMap {
		userIds = append(userIds, i)
	}

	// 设置称号
	if err = SetCommentListMyTitles(c, userIds, comments, language); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetCommentUserTitle err: %v\n", err)
		return err
	}

	return err
}

func SetPostUserTitlePosts(c context.Context, posts []*model.PostFormatted, language string) error {
	if len(posts) == 0 {
		return nil
	}
	var postUserOpenIdsMap = make(map[string]struct{})

	for _, post := range posts {
		// 去重
		postUserOpenIdsMap[post.IntlOpenid] = struct{}{}
	}

	var err error
	var postUserIds = make([]string, 0, len(postUserOpenIdsMap))
	for i, _ := range postUserOpenIdsMap {
		postUserIds = append(postUserIds, i)
	}

	// 设置称号
	if err = SetPostListMyTitles(c, postUserIds, posts, language); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserPostsComments err: %v\n", err)
		return err
	}

	return nil
}

func SetPostUserTitle(c context.Context, user *siteUserPB.UserInfo, language string) error {
	if user == nil {
		return nil
	}

	// 设置称号
	if err := SetPostListMyTitle(c, user, language); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserPostsComments err: %v\n", err)
		return err
	}

	return nil
}

// SetPostListMyTitles 查看用户称号
func SetPostListMyTitles(c context.Context, userOpenids []string, posts []*model.PostFormatted, language string) error {
	if len(userOpenids) == 0 {
		return nil
	}
	conditions := &dao.UserBindTitleConditions{
		IntlOpenids: userOpenids,
		Status:      1,
		Language:    language,
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}

	userTitles, err := dao.UserBindTitleList(conditions, 0, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserPostsComments err: %v\n", err)
		return err
	}

	var userBindTitleFormated *model.UserBindTitleFormated
	for _, post := range posts {
		for _, title := range userTitles {
			if post.User != nil && post.IntlOpenid == title.IntlOpenid {
				userBindTitleFormated = title.Format()
				if title.Title != nil {
					userBindTitleFormated.Title = title.Title.TitleNewListFormated()
					if title.TitleLanguage != nil {
						userBindTitleFormated.Title.TitleLanguage = title.TitleLanguage.Format()
					}
				}
				post.User.Titles = userBindTitleFormated
			}
		}
	}
	return nil
}

// SetMessageListMyTitlesNew 查看用户称号
func SetMessageListMyTitlesNew(c context.Context, userOpenids []string, messages []*model.MessageFormattedNew, language string) error {
	if len(userOpenids) == 0 {
		return nil
	}
	conditions := &dao.UserBindTitleConditions{
		IntlOpenids: userOpenids,
		Status:      1,
		Language:    language,
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}

	userTitles, err := dao.UserBindTitleList(conditions, 0, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserPostsComments err: %v\n", err)
		return err
	}

	var userBindTitleFormated *model.UserBindTitleFormated
	for _, message := range messages {
		for _, title := range userTitles {
			if message.SenderUserIntlOpenid == title.IntlOpenid && message.SenderUser != nil {
				userBindTitleFormated = title.Format()
				if title.Title != nil {
					userBindTitleFormated.Title = title.Title.TitleNewListFormated()
					if title.TitleLanguage != nil {
						userBindTitleFormated.Title.TitleLanguage = title.TitleLanguage.Format()
					}
				}
				message.SenderUser.Titles = userBindTitleFormated
			}
		}
	}
	return nil
}

// SetCommentListMyTitles 查看用户称号
func SetCommentListMyTitles(c context.Context, intlOpenID []string, comments []*model.CommentFormated, language string) error {
	// 我关注的人列表
	conditions := &dao.UserBindTitleConditions{
		IntlOpenids: intlOpenID,
		Status:      1,
		Language:    language,
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}

	userTitles, err := dao.UserBindTitleList(conditions, 0, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserPostsComments err: %v\n", err)
		return errs.NewCustomError(c, code.GetUserBindTitleFailed, "SetCommentListMyTitles | Failed to get user bind title")
	}

	var userBindTitleFormated *model.UserBindTitleFormated
	for _, comment := range comments {
		for _, title := range userTitles {
			if comment.IntlOpenid == title.IntlOpenid && comment.User != nil {
				userBindTitleFormated = title.Format()
				if title.Title != nil {
					userBindTitleFormated.Title = title.Title.TitleNewListFormated()
					if title.TitleLanguage != nil {
						userBindTitleFormated.Title.TitleLanguage = title.TitleLanguage.Format()
					}
				}
				comment.User.Titles = userBindTitleFormated
			}
		}
	}
	return nil
}

// SetPostListMyTitle 查看用户称号
func SetPostListMyTitle(c context.Context, user *siteUserPB.UserInfo, language string) error {
	// 我关注的人列表
	conditions := &dao.UserBindTitleConditions{
		IntlOpenid: user.IntlOpenid,
		Status:     1,
		Language:   language,
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}

	userTitles, err := dao.UserBindTitleList(conditions, 0, 1)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetPostListMyTitle err: %v\n", err)
		return errs.NewCustomError(c, code.GetUserBindTitleFailed, "SetPostListMyTitle | Failed to get user bind title")
	}

	var userBindTitleFormated *siteUserPB.UserBindTitle
	for _, title := range userTitles {
		if user.IntlOpenid == title.IntlOpenid {
			userBindTitleFormated = &siteUserPB.UserBindTitle{
				Id:         title.ID,
				Title:      &siteUserPB.TitleNewList{},
				IntlOpenid: title.IntlOpenid,
				TitleId:    title.TitleId,
				Status:     title.Status,
				GameId:     title.GameId,
				AreaId:     title.AreaId,
			}
			if title.Title != nil {
				userBindTitleFormated.Title = &siteUserPB.TitleNewList{
					Id:         title.Title.ID,
					Language:   &siteUserPB.TitleLanguage{},
					Avatar:     title.Title.Avatar,
					InitHot:    title.Title.InitHot,
					PossessNum: title.Title.PossessNum,
					GameId:     title.Title.GameId,
					AreaId:     title.Title.AreaId,
				}
				if title.TitleLanguage != nil {
					userBindTitleFormated.Title.Language = &siteUserPB.TitleLanguage{
						Id:        title.TitleLanguage.ID,
						Language:  title.TitleLanguage.Language,
						TitleId:   title.TitleLanguage.TitleId,
						Title:     title.TitleLanguage.Title,
						Introduce: title.TitleLanguage.Introduce,
					}
				}
			}
			user.Titles = userBindTitleFormated
		}
	}
	return nil
}

// GetUserTitleNewOne 获取当前实时有效已分配的用户对应的称号的热度——即将废弃的接口
func GetUserTitleNewOne(c context.Context, intlOpenID string, language string) (*model.UserBindTitleFormated, error) {
	userTitleRedisKey := cache.GetUserTitleKey(intlOpenID)
	if userTitleCacheInfo, err := redis.GetClient().Get(c, userTitleRedisKey).Result(); err == nil {
		if userTitleCacheInfo == "" {
			return nil, nil
		}
		userTitleInfo := &model.UserBindTitleFormated{}
		err = json.Unmarshal([]byte(userTitleCacheInfo), userTitleInfo)
		if err == nil {
			return userTitleInfo, nil
		} else {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserTitleNewOne json.Unmarshal err: %v", err)
		}
	}

	conditionsT := &dao.UserBindTitleConditions{
		Status:     1,
		Language:   language,
		IntlOpenid: intlOpenID,
		// "game_id":  c.GetString("GAME_ID"),
		// "area_id":  c.GetString("AREA_ID"),
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}

	userTitles, err := dao.UserBindTitleList(conditionsT, 0, 1)
	// logrus.Debugf("service.GetUserBindTitleList at %v", time.Now())
	if err != nil {
		return nil, err
	}
	if len(userTitles) == 0 {
		return nil, nil
	}
	title := userTitles[0]
	titleFormated := title.Format()
	titleFormated.Title = title.Title.TitleNewListFormated()
	if titleFormated.Title != nil && title.TitleLanguage != nil {
		titleFormated.Title.TitleLanguage = title.TitleLanguage.Format()
	}

	titleFormatedByte, err := json.Marshal(titleFormated)
	if err == nil {
		redis.GetClient().SetEX(c, userTitleRedisKey, string(titleFormatedByte), 1*time.Minute).Result()
	}
	return titleFormated, nil
}
