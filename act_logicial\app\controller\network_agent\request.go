package networkagent

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/url"
	"regexp"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	secapi "git.woa.com/sec-api/go/scurl"
	"trpc.act.logicial/app/code"
)

func isValidURL(urlStr string) bool {
	re := regexp.MustCompile(`^https?://[a-zA-Z0-9\-.]+(\.[a-zA-Z]{2,})+(/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=%]*)?$`)
	return re.MatchString(urlStr)
}

func SendGetRequest(ctx context.Context, url string, timeout int64) (data string, err error) {
	// 验证URL的有效性
	if url == "" || !isValidURL(url) {
		return "", errs.NewCustomError(ctx, code.InvalidRequest, "url invalid")
	}
	// 传递单个 SecOptions 参数
	safeClient := secapi.NewSafeClient(secapi.WithConfTimeout(time.Duration(timeout)))

	// client := &http.Client{
	// 	Timeout: time.Duration(timeout), // 设置超时时间
	// }
	resp, err := safeClient.Get(url)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "Error").Errorf("Failed to get request: %v", err)
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		// log.Fatalf("Failed to read response body: %v", err)
		log.WithFieldsContext(ctx, "log_type", "Error").Errorf("Failed to read response body: %v", err)
		return "", err
	}
	log.WithFieldsContext(ctx, "log_type", "Info").Infof("Response body: %v", string(body))
	return string(body), nil
}

func SendPostFromRequest(ctx context.Context, url string, contentType string, requestBody string, timeout int64) (data string, err error) {
	bodyMap := make(map[string][]string)
	err = json.Unmarshal([]byte(requestBody), &bodyMap)
	if err != nil {
		// log.Fatalf("Failed to unmarshal request body: %v", err)
		log.WithFieldsContext(ctx, "log_type", "Error").Errorf("Failed to unmarshal request body: %v", err)
		return "", err
	}
	ulrValue := mapToValues(bodyMap)
	safeClient := secapi.NewSafeClient(secapi.WithConfTimeout(time.Duration(timeout)))
	// client := &http.Client{
	// 	Timeout: time.Duration(timeout), // 设置超时时间
	// }
	resp, err := safeClient.PostForm(url, ulrValue)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "Error").Errorf("Failed to post request: %v", err)
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		// log.Fatalf("Failed to read response body: %v", err)
		log.WithFieldsContext(ctx, "log_type", "Error").Errorf("Failed to read response body: %v", err)
		return "", err
	}
	log.WithFieldsContext(ctx, "log_type", "Info").Infof("Response body: %v", string(body))
	return string(body), nil
}

func SendPostRequest(ctx context.Context, url string, contentType string, requestBody string, timeout int64) (data string, err error) {
	if url == "" || !isValidURL(url) {
		return "", errs.NewCustomError(ctx, code.InvalidRequest, "invalid url")
	}
	if contentType == "" {
		contentType = "application/json"
	}
	safeClient := secapi.NewSafeClient(secapi.WithConfTimeout(time.Duration(timeout)))
	// client := &http.Client{
	// 	Timeout: time.Duration(timeout), // 设置超时时间
	// }
	// 请求体
	bodyByte := []byte(requestBody)
	if err != nil {
		// log.Fatalf("Failed to marshal request body: %v", err)
		log.WithFieldsContext(ctx, "log_type", "Error").Infof("Failed to marshal request body: %v", err)
		return "", err
	}
	// 发起 HTTPS POST 请求
	resp, err := safeClient.Post(url, contentType, bytes.NewBuffer(bodyByte))
	if err != nil {
		log.Fatalf("Failed to make request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		// log.Fatalf("Failed to read response body: %v", err)
		log.WithFieldsContext(ctx, "log_type", "Error").Infof("Failed to read response body: %v", err)
		return "", err
	}
	return string(body), nil
}

func mapToValues(data map[string][]string) url.Values {
	values := url.Values{}
	for key, valueSlice := range data {
		for _, value := range valueSlice {
			values.Add(key, value)
		}
	}
	return values
}
