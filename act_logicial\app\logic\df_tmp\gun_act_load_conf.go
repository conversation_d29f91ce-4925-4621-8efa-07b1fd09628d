package df_tmp

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"trpc.act.logicial/app/task/cron"
)

// GunActLoadConf 加载拼枪任务配置文件
func GunActLoadConf(ctx context.Context, req *pb.GunActLoadConfReq) (*pb.GunActLoadConfRsp, error) {
	log.DebugContextf(ctx, "GunActLoadConf enter, req: %v", req)
	go func() { // 启动一个协程来处理
		cron.LoadGunActivityConf(context.Background())
	}()
	rsp := &pb.GunActLoadConfRsp{}
	return rsp, nil
}
