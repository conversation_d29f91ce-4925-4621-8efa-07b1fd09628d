package formatted

import (
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/model"
)

func ReturnProtoUserTitleData(userTitle *model.UserBindTitleFormated) *pbUser.UserBindTitle {
	if userTitle == nil {
		return nil
	}
	userTitleData := &pbUser.UserBindTitle{
		Id:         userTitle.ID,
		IntlOpenid: userTitle.IntlOpenid,
		TitleId:    userTitle.TitleId,
		Status:     userTitle.Status,
		GameId:     userTitle.GameId,
		AreaId:     userTitle.AreaId,
	}
	if userTitle.Title != nil {
		userTitleData.Title = &pbUser.TitleNewList{
			Id:         userTitle.Title.ID,
			Avatar:     userTitle.Title.Avatar,
			UpTime:     userTitle.Title.UpTime,
			DownTime:   userTitle.Title.DownTime,
			InitHot:    userTitle.Title.InitHot,
			PossessNum: userTitle.Title.PossessNum,
			Status:     userTitle.Title.Status,
			GameId:     userTitle.Title.GameId,
			AreaId:     userTitle.Title.AreaId,
		}
		if userTitle.Title.TitleLanguage != nil {
			userTitleData.Title.Language = &pbUser.TitleLanguage{
				Id:        userTitle.Title.TitleLanguage.ID,
				Language:  userTitle.Title.TitleLanguage.Language,
				TitleId:   userTitle.Title.TitleLanguage.TitleId,
				Title:     userTitle.Title.TitleLanguage.Title,
				Introduce: userTitle.Title.TitleLanguage.Introduce,
			}
		}
	}
	return userTitleData
}
