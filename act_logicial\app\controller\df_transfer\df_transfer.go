package dftransfer

import (
	"context"
	"fmt"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/object"
	"git.code.oa.com/trpc-go/trpc-go/client"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	gameDfPb "git.woa.com/trpcprotocol/publishing_marketing/game_df"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_account_tranfer"
	"google.golang.org/protobuf/proto"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	logic "trpc.act.logicial/app/logic/df_transfer"
)

const (
	NoRoleInfo = 601008
)

var (
	accountProxy = accountPb.NewAccountClientProxy()
	gameDFProxy  = gameDfPb.NewDfClientProxy()
)

type DfAccountTranferImpl struct {
	pb.UnimplementedDfAccountTranfer
}

// GetTransferNum 获取转移数量
func (s *DfAccountTranferImpl) GetTransferNum(ctx context.Context, req *pb.GetTransferNumReq) (rsp *pb.GetTransferNumRsp, err error) {
	rsp = &pb.GetTransferNumRsp{}
	num, err := logic.GetTransferNum(ctx)
	if err != nil {
		return
	}
	rsp.Num = num
	return
}

// CheckGAAcount 校验GAAccount
func (s *DfAccountTranferImpl) CheckGAAcount(ctx context.Context, req *pb.DoSteamBindGAReq) (rsp *pb.DoSteamBindGARsp, err error) {
	rsp = &pb.DoSteamBindGARsp{}
	_, errA := accountProxy.CheckAccount(ctx, &accountPb.UserAccount{
		AccountType: accountPb.AccountType_INTL,
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    req.GetGaOpenid(),
			ChannelId: req.GetGaChannel(),
			GameId:    req.GetGaGameId(),
			Token:     req.GetGaToken(),
		},
	})
	// 解析错误码
	if errA != nil {
		errParse := errs.ParseError(ctx, errA)
		// 登录态不通过
		if errParse.Code == 300001 {
			err = errs.NewCustomError(ctx, code.GAAccountInvalid, "err=%v", errA.Error())
		} else {
			err = errA
		}
	}
	return
}

// GetSteamAccountRegisterTimestamp 获取Steam用户注册时间戳
func (s *DfAccountTranferImpl) GetSteamAccountRegisterTimestamp(ctx context.Context, req *gamePb.RoleInfo) (rsp *pb.GetSteamAccountRegisterTimestampRsp, err error) {
	rsp = &pb.GetSteamAccountRegisterTimestampRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	registerTime, err := logic.GetFirstLoginTimestamp(ctx, logic.DeltaverseReq{Openid: userAccount.IntlAccount.OpenId, Zoneid: req.GetAreaId()})
	if err != nil {
		return
	}
	rsp.RegisterTimestamp = int32(registerTime)
	return
}

// CheckRegisterInCountery 判断是否在注册国家里
func (s *DfAccountTranferImpl) CheckRegisterInCountery(ctx context.Context, req *gamePb.RoleInfo) (rsp *pb.CheckRegisterInCounteryRsp, err error) {
	rsp = &pb.CheckRegisterInCounteryRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	firstLoginCountry, err := logic.GetFirstLoginCountry(ctx, logic.DeltaverseReq{Openid: userAccount.IntlAccount.OpenId, Zoneid: req.GetAreaId()})
	if err != nil {
		return
	}
	fmt.Println("---------------firstLoginCountry---------------")
	fmt.Printf("%#v\n", firstLoginCountry)
	contryList := config.GetConfig().DfSteamGATransfer.CountryList
	exists, _ := object.InArray(firstLoginCountry, contryList)
	if !exists {
		err = errs.NewCustomError(ctx, code.SteamAccountRegisterNotInCountry, "err=%v", "not register not in")
	}
	return
}

// CheckGAAccountHasRegister 判断GA账号是否注册
func (s *DfAccountTranferImpl) CheckGAAccountHasRegister(ctx context.Context, req *pb.CheckGAAccountHasRegisterReq) (rsp *pb.CheckGAAccountHasRegisterRsp, err error) {
	rsp = &pb.CheckGAAccountHasRegisterRsp{}
	_, err = metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	gaAccountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         fmt.Sprintf("%s-%s", req.GetGaGameId(), req.GetGaOpenid()),
		AccountType: accountPb.AccountType_INTL,
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    req.GetGaOpenid(),
			GameId:    req.GetGaGameId(),
			ChannelId: req.GetGaChannel(),
			Token:     req.GetGaToken(),
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, gaAccountData),
	}
	_, errG := gameDFProxy.GAAccountHasRoleInfo(ctx, &gameDfPb.GAAccountHasRoleInfoReq{
		AreaId: int32(req.GetAreaId()),
	}, callopts...)
	if errG == nil {
		err = errs.NewCustomError(ctx, code.GAAccountHasRoleInfo, "ga account has role")
		return
	}
	errParse := errs.ParseError(ctx, errG)
	if errParse.Code == NoRoleInfo {
		return
	}
	err = errG
	return
}
func (s *DfAccountTranferImpl) GetGAAccountRoleInfo(ctx context.Context, req *pb.CheckGAAccountHasRegisterReq) (rsp *pb.GetGAAccountRoleInfoRsp, err error) {
	rsp = &pb.GetGAAccountRoleInfoRsp{}
	_, err = metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	gaAccountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         fmt.Sprintf("%s-%s", req.GetGaGameId(), req.GetGaOpenid()),
		AccountType: accountPb.AccountType_INTL,
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    req.GetGaOpenid(),
			GameId:    req.GetGaGameId(),
			ChannelId: req.GetGaChannel(),
			Token:     req.GetGaToken(),
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, gaAccountData),
	}
	gameRsp, errG := gameDFProxy.GAAccountHasRoleInfo(ctx, &gameDfPb.GAAccountHasRoleInfoReq{
		AreaId: int32(req.GetAreaId()),
	}, callopts...)
	if errG != nil {
		errParse := errs.ParseError(ctx, errG)
		if errParse.Code == NoRoleInfo {
			return
		}
		err = errG
		return
	}

	rsp = &pb.GetGAAccountRoleInfoRsp{
		HasRole:  true,
		RoleName: gameRsp.GetRoleInfo().GetRoleName(),
	}
	return
}

// CheckSteamAccountInTransfer 检查Steam账号是否在转移中
func (s *DfAccountTranferImpl) CheckSteamAccountInTransfer(ctx context.Context, req *pb.CheckSteamAccountInTransferReq) (rsp *pb.CheckSteamAccountInTransferRsp, err error) {
	rsp = &pb.CheckSteamAccountInTransferRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	err = logic.CheckSteamAccountInTransfer(ctx, &userAccount)
	return
}

// CheckGAAccountInTransfer 检查GA账号是否在转移中
func (s *DfAccountTranferImpl) CheckGAAccountInTransfer(ctx context.Context, req *pb.DoSteamBindGAReq) (rsp *pb.CheckGAAccountInTransferRsp, err error) {
	rsp = &pb.CheckGAAccountInTransferRsp{}
	_, err = metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	err = logic.CheckGAAccountInTransfer(ctx, req.GetGaOpenid())
	return
}

// GetSteamBindGAInfo 获取Steam绑定GA信息
func (s *DfAccountTranferImpl) GetSteamBindGAInfo(ctx context.Context, req *pb.GetSteamBindGAInfoReq) (rsp *pb.GetSteamBindGAInfoRsp, err error) {
	rsp = &pb.GetSteamBindGAInfoRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	hasBind, bindInfo, err := logic.GetSteamBindGAInfo(ctx, &userAccount)
	if err != nil {
		return
	}
	if hasBind {
		rsp = &pb.GetSteamBindGAInfoRsp{
			Status:                  pb.SteamBindGAStatus(bindInfo.Status),
			GaOpenid:                bindInfo.GaOpeinid,
			GaEmail:                 bindInfo.GaEmail,
			UnbindLeftTimestamp:     int32(logic.GetTransDateLeftTimestamp(bindInfo.CalmDate)),
			BindTimestamp:           int32(bindInfo.CreatedAt),
			TransferFinishTimestamp: int32(logic.GetTransDateFinishTimestamp(bindInfo.CalmDate)),
		}
	}
	return
}

// DoSteamBindGA Steam绑定账号GA
func (s *DfAccountTranferImpl) DoSteamBindGA(ctx context.Context, req *pb.DoSteamBindGAReq) (rsp *pb.DoSteamBindGARsp, err error) {
	rsp = &pb.DoSteamBindGARsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	err = logic.DoSteamBindGA(ctx, &userAccount, req.GetGaOpenid(), req.GetGaEmail())
	return
}

// DoSteamUnbindGA Steam解绑账号GA
func (s *DfAccountTranferImpl) DoSteamUnbindGA(ctx context.Context, req *pb.DoSteamUnbindGAReq) (rsp *pb.DoSteamUnbindGARsp, err error) {
	rsp = &pb.DoSteamUnbindGARsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	err = logic.DoSteamUnbindGA(ctx, userAccount.Uid)
	return
}

// ScheduleConfirmAccount 定时确认账户
func (s *DfAccountTranferImpl) ScheduleConfirmAccount(ctx context.Context, req *pb.ScheduleConfirmAccountReq) (rsp *pb.ScheduleConfirmAccountRsp, err error) {
	rsp = &pb.ScheduleConfirmAccountRsp{}
	go func() {
		err = logic.ScheduleConfirmAccount(context.Background(), req.CalmDate, req.DfmAreaId, req.GaAreaId)
		if err != nil {
			return
		}
	}()
	// err = logic.ScheduleConfirmAccount(context.Background(), req.CalmDate, req.DfmAreaId, req.GaAreaId)
	return
}

// ScheduleAccountTransfer 下发道具
func (s *DfAccountTranferImpl) ScheduleAccountTransfer(ctx context.Context, req *pb.ScheduleAccountTransferReq) (rsp *pb.ScheduleAccountTransferRsp, err error) {
	rsp = &pb.ScheduleAccountTransferRsp{}
	go func() {
		err = logic.ScheduleAccountTransfer(context.Background(), req.CalmDate, req.DfmAreaId, req.GaAreaId)
		if err != nil {
			return
		}
	}()
	// err = logic.ScheduleAccountTransfer(context.Background(), req.CalmDate, req.DfmAreaId, req.GaAreaId)
	return
}

// CheckLastTransferIsBanInCalm 解绑
func (s *DfAccountTranferImpl) CheckLastTransferIsBanInCalm(ctx context.Context, req *pb.CheckLastTransferIsBanInCalmReq) (rsp *pb.CheckLastTransferIsBanInCalmRsp, err error) {
	rsp = &pb.CheckLastTransferIsBanInCalmRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var hasBan bool
	hasBan, err = logic.GetLogCalmBan(ctx, &userAccount, req.DfmAreaId)
	if err != nil {
		return
	}
	rsp.HasBan = hasBan
	return
}
