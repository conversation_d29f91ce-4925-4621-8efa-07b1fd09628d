// Package common 通用方法
package common

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"time"

	captchaPb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/captcha"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_common"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/global"
	logic "trpc.act.logicial/app/logic/common"
	"trpc.act.logicial/app/logic/userdata"
	commonModel "trpc.act.logicial/app/model/common"
	"trpc.act.logicial/app/model/invitation"
	"trpc.act.logicial/app/util"
)

// TencentCaptcha 腾讯验证码
type TencentCaptcha struct {
	Ticket  string `json:"ticket"`
	Randstr string `json:"randstr"`
}

// CommonImpl 声明
type CommonImpl struct{}

// FormatTimeRangeBounds 根据条件返回格式化起止时间
func (s *CommonImpl) FormatTimeRangeBounds(ctx context.Context, req *pb.FormatTimeRangeBoundsReq) (
	*pb.FormatTimeRangeBoundsRsp, error) {

	// 参数校验
	if req.TimeType > 4 || req.TimeType < 0 {
		return nil, errs.NewCustomError(ctx, code.InvalidTimeType, "invalid timeType")
	}
	if req.Increment < 1 {
		req.Increment = 1
	}
	if req.TimeZone < -11 || req.TimeZone > 12 {
		return nil, fmt.Errorf("timezone must be between -11 and 12")
	}
	format := req.Format    // 返回时间格式
	n := int(req.Increment) // 时间增量

	// 创建时区
	loc := time.FixedZone("", int(req.TimeZone*3600))
	now := time.Now().In(loc)

	var startTime, endTime time.Time
	switch req.TimeType {
	case 0: // 自定义
		if req.StartTimestamp == 0 || req.EndTimestamp == 0 {
			return nil, errs.NewCustomError(ctx, code.CommonDataError, "The timestamp parameter is missing")
		}
		// 时间戳校验
		if req.StartTimestamp > req.EndTimestamp {
			req.StartTimestamp, req.EndTimestamp = req.EndTimestamp, req.StartTimestamp
		}
		// 时间转换（秒级时间戳转换）
		startTime = time.Unix(req.StartTimestamp, 0).In(loc)
		endTime = time.Unix(req.EndTimestamp, 0).In(loc)
	case 1: // 小时
		currentHour := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, loc)
		startTime = currentHour.Add(-time.Duration(n-1) * time.Hour)
		endTime = currentHour.Add(time.Hour - time.Nanosecond)
	case 2: // 天
		todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc)
		startTime = todayStart.AddDate(0, 0, -(n - 1))
		endTime = todayStart.AddDate(0, 0, 1).Add(-time.Nanosecond)
	case 3: // 周
		// 计算当前周周一
		offset := int(now.Weekday() - time.Monday)
		if offset < 0 {
			offset += 7
		}
		currentMonday := now.AddDate(0, 0, -offset)
		startOfWeek := time.Date(currentMonday.Year(), currentMonday.Month(), currentMonday.Day(), 0, 0, 0, 0, loc)
		startTime = startOfWeek.AddDate(0, 0, -(n-1)*7)
		endTime = startOfWeek.AddDate(0, 0, 7).Add(-time.Nanosecond)
	case 4: // 月
		currentMonthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, loc)
		startTime = currentMonthStart.AddDate(0, -(n - 1), 0)
		endTime = currentMonthStart.AddDate(0, 1, 0).Add(-time.Nanosecond)
	default:
		return nil, errs.NewCustomError(ctx, code.InvalidTimeType, "invalid timeType")
	}

	return &pb.FormatTimeRangeBoundsRsp{
		StartStopTime: []string{
			startTime.Format(format),
			endTime.Format(format),
		},
	}, nil
}

// RequestLimitByIP 通用- 根据IP限频
func (s *CommonImpl) RequestLimitByIP(ctx context.Context, req *pb.RequestLimitByIPReq) (*pb.RequestLimitByIPRsp,
	error) {
	ip := metadata.GetUserIP(ctx)
	if ip == "" {
		return nil, errs.NewCustomError(ctx, code.GetIPFail, "Get IP Fail")
	}
	key := fmt.Sprintf("rate_limit_%s_%s", req.UniqueTag, ip)
	redisKey := global.GetRedisKey(key)
	count, err := redis.GetClient().Incr(ctx, redisKey).Result()
	if err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
			"RequestLimitByIP redis Incr error, \t [Error]:{%v} ", err)
	}

	// 设置过期时间
	if count == 1 {
		_, err = redis.GetClient().Expire(ctx, redisKey, time.Duration(req.LimitSecond)*time.Second).Result()
		if err != nil {
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
				"RequestLimitByIP redis Expire error, \t [Error]:{%v} ", err)
		}
	}

	// 判断计数是否超过限制
	if count > int64(req.LimitNum) {
		return nil, errs.NewCustomError(ctx, code.RequestFrequencyExceededLimit,
			"Request Frequency ExceededLimit")
	}
	return &pb.RequestLimitByIPRsp{}, nil
}

// TencentCaptchaCheck 腾讯验证码校验
func (s *CommonImpl) TencentCaptchaCheck(ctx context.Context, req *pb.TencentCaptchaCheckReq) (
	*pb.TencentCaptchaCheckRsp, error) {
	ip := metadata.GetUserIP(ctx)
	header := metadata.GetHTTPHeader(ctx)
	captchaStr := header.Get("X-Captcha")
	if captchaStr == "" {
		// 报错
		return nil, errs.NewCustomError(ctx, code.CaptchaParameterMissing,
			"The verification code parameter is missing")
	}
	var captcha TencentCaptcha
	errR := json.Unmarshal([]byte(captchaStr), &captcha)
	if errR != nil {
		// 报错
		return nil, errs.NewCustomError(ctx, code.CommonJSONUnmarshalErr, "captcha json format exception")
	}
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "TencentCaptchaCheck", "ip", ip).
		Errorf("TencentCaptchaCheck show captcha and ip:[%v] ip:[%v]", captcha, ip)
	hasPass, err := captchaPb.CheckCaptcha(ctx, captcha.Ticket, captcha.Randstr, ip, req.ConfKey)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "TencentCaptchaCheck", "ip", ip).
			Debugf("TencentCaptchaCheck CheckCaptcha err:[%v]", err)
		return nil, errs.NewCustomError(ctx, code.CheckCaptchaErr, "Check Captcha err")
	}
	if !hasPass {
		return nil, errs.NewCustomError(ctx, code.VerificationCodeFails, "Verification code check failed")
	}
	return &pb.TencentCaptchaCheckRsp{}, nil
}

// HelloWorld 通用-获取首个角色信息
func (s *CommonImpl) HelloWorld(ctx context.Context, req *pb.HelloWorldReq) (
	rsp *pb.HelloWorldRsp, err error) {
	rsp = &pb.HelloWorldRsp{
		Value: "HelloWorld",
	}
	return
}

// SelectFirstRoleInfo 通用-获取首个角色信息
func (s *CommonImpl) SelectFirstRoleInfo(ctx context.Context, req *pb.SelectFirstRoleInfoReq) (
	rsp *pb.SelectFirstRoleInfoRsp, err error) {
	rsp = &pb.SelectFirstRoleInfoRsp{}
	if len(req.RoleList) > 0 {
		rsp = &pb.SelectFirstRoleInfoRsp{
			RoleInfo: req.RoleList[0],
		}
	}
	return
}

// GetRoleListLength 通用-获取角色列表长度
func (s *CommonImpl) GetRoleListLength(ctx context.Context, req *pb.GetRoleListLengthReq) (
	rsp *pb.GetRoleListLengthRsp, err error) {

	return &pb.GetRoleListLengthRsp{
		RoleLen: int32(len(req.RoleList)),
	}, nil
}

// RequestLimit 通用-接口请求限频
func (s *CommonImpl) RequestLimit(ctx context.Context, req *pb.RequestLimitReq) (
	rsp *pb.RequestLimitRsp, err error) {
	rsp = &pb.RequestLimitRsp{}
	// 初始化用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	key := fmt.Sprintf("%s_%s", req.UniqueTag, userAccount.Uid)
	redisKey := global.GetRedisKey(key)

	count, err := redis.GetClient().Incr(ctx, redisKey).Result()
	if err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
			"RequestLimit redis Incr error, \t [Error]:{%v} ", err)
	}

	// 设置过期时间
	if count == 1 {
		_, err = redis.GetClient().Expire(ctx, redisKey, time.Duration(req.LimitSecond)*time.Second).Result()
		if err != nil {
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
				"RequestLimit redis Expire error, \t [Error]:{%v} ", err)
		}
	}

	// 判断计数是否超过限制
	if count > int64(req.LimitNum) {
		return nil, errs.NewCustomError(ctx, code.RequestFrequencyExceededLimit,
			"RequestFrequencyExceededLimit Error,count:[%v]", count)
	}

	return
}

// ElementExistsInArray 通用-判断是否在数组内(字符串)
func (s *CommonImpl) ElementExistsInArray(ctx context.Context, req *pb.ElementExistsInArrayReq) (
	rsp *pb.ElementExistsInArrayRsp, err error) {
	rsp = &pb.ElementExistsInArrayRsp{}
	for _, regionCode := range req.RegionCodeList {
		if regionCode.RegionCode == req.RegionCode {
			rsp.IsInside = true
			return
		}
	}
	return
}

// ReturnsAssignmentErrCode 返回指定错误码
func (s *CommonImpl) ReturnsAssignmentErrCode(ctx context.Context, req *pb.ReturnsAssignmentErrCodeReq) (
	rsp *pb.ReturnsAssignmentErrCodeRsp, err error) {
	rsp = &pb.ReturnsAssignmentErrCodeRsp{}
	err = errs.NewCustomError(ctx, int(req.ErrCode), req.ErrMsg)
	return
}

// ReturnsSpecifiedNum 返回指定的数字
func (s *CommonImpl) ReturnsSpecifiedNum(ctx context.Context, req *pb.ReturnsSpecifiedNumReq) (
	rsp *pb.ReturnsSpecifiedNumRsp, err error) {
	rsp = &pb.ReturnsSpecifiedNumRsp{
		ReturnNum: req.Num,
	}
	return
}

// ShowBoolean 展示布尔值
func (s *CommonImpl) ShowBoolean(ctx context.Context, req *pb.ShowBooleanReq) (rsp *pb.ShowBooleanRsp, err error) {
	rsp = &pb.ShowBooleanRsp{}
	rsp.ShowBoolean = req.ShowBoolean
	return
}

// CompareInt 比较数值
func (s *CommonImpl) CompareInt(ctx context.Context, req *pb.CompareIntReq) (rsp *pb.CompareIntRsp, err error) {

	compareVal := req.CompareValue
	toCompareValue := req.ToCompareValue
	flag := false
	// CompareIntReq_Eq CompareIntReq_Compare = 0 // 相等
	// CompareIntReq_GT CompareIntReq_Compare = 1 // 大于
	// CompareIntReq_GE CompareIntReq_Compare = 2 // 大于等于
	// CompareIntReq_NE CompareIntReq_Compare = 3 // 不等于
	// CompareIntReq_LT CompareIntReq_Compare = 4 // 小于
	// CompareIntReq_LE CompareIntReq_Compare = 5 // 小于等于
	switch req.Compare {
	case pb.CompareIntReq_Eq:
		if compareVal == toCompareValue {
			flag = true
		}
		break
		// 大于
	case pb.CompareIntReq_GT:
		if compareVal > toCompareValue {
			flag = true
		}
		break
	// 大于等于
	case pb.CompareIntReq_GE:
		if compareVal >= toCompareValue {
			flag = true
		}
		break
		// 不等于
	case pb.CompareIntReq_NE:
		if compareVal != toCompareValue {
			flag = true
		}
		break
		// 小于
	case pb.CompareIntReq_LT:
		if compareVal < toCompareValue {
			flag = true
		}
		break
		// 小于等于
	case pb.CompareIntReq_LE:
		if compareVal <= toCompareValue {
			flag = true
		}
		break
	}

	if !flag {
		err = errs.NewCustomError(ctx, int(req.ErrorCode), fmt.Sprintf(" compare fail"))
		return
	}
	rsp = &pb.CompareIntRsp{
		CompareReuslt: flag,
	}
	return
}

// GetObjectValue 获取对象值
func (s *CommonImpl) GetObjectValue(ctx context.Context, req *pb.GetObjectValueReq) (rsp *pb.GetObjectValueRsp,
	err error) {

	objectJson, jsonErr := json.Marshal(req.Object)
	if jsonErr != nil {
		err = errs.NewCustomError(ctx, code.CommonParamJsonError, fmt.Sprintf(" param json error"))
		return
	}
	keyArr := strings.Split(req.Key, ".")
	var keyMap map[string]*anypb.Any
	json.Unmarshal([]byte(objectJson), &keyMap)
	// 输出 字符串数组 中的 字符串
	for k, v := range keyArr {
		if k == len(keyArr)-1 {
			rsp.ObjectValue = keyMap[v]
		} else {
			keyMapJson, _ := json.Marshal(keyMap[v])
			json.Unmarshal([]byte(keyMapJson), &keyMap)
		}
	}
	return
}

// GetMapKeyBoolValue 写一个获取bool类型的接口
func (s *CommonImpl) GetMapKeyBoolValue(ctx context.Context, req *pb.GetMapKeyBoolValueReq) (
	rsp *pb.GetMapKeyBoolValueRsp, err error,
) {
	value, ok := req.Object[req.Key]
	if !ok {
		err = errs.NewCustomError(ctx, code.CommonMapKeyValueDontMatch, "object=[%v],key=[%v]", req.Object, req.Key)
		return

	}
	if req.FalseShowError && !bool(value) {
		return nil, errs.NewCustomError(ctx, code.CommonValueIsFalse, "value is false")
	}
	rsp = &pb.GetMapKeyBoolValueRsp{
		ObjectValue: bool(value),
	}
	return
}

// GetMapKeyValue 获取对象值
func (s *CommonImpl) GetMapKeyValue(ctx context.Context, req *pb.GetMapKeyValueReq) (rsp *pb.GetMapKeyValueRsp,
	err error) {
	value, ok := req.Object[req.Key]
	if !ok {
		err = errs.NewCustomError(ctx, code.CommonMapKeyValueDontMatch, "object=[%v],key=[%v]", req.Object, req.Key)
		return

	}
	rsp = &pb.GetMapKeyValueRsp{
		ObjectValue: value,
	}
	return
}

// GetMapKeyIntValue 获取对象值
func (s *CommonImpl) GetMapKeyIntValue(ctx context.Context, req *pb.GetMapKeyIntValueReq) (rsp *pb.GetMapKeyIntValueRsp,
	err error) {
	value, ok := req.Object[req.Key]
	if !ok {
		err = errs.NewCustomError(ctx, code.CommonMapKeyValueDontMatch, "object=[%v],key=[%v]", req.Object, req.Key)
		return

	}
	rsp = &pb.GetMapKeyIntValueRsp{
		ObjectValue: int32(value),
	}
	return
}

var r *rand.Rand

func init() {
	r = rand.New(rand.NewSource(time.Now().UnixNano()))
}

// GetRateRandomPass 通过概率随机判断是否在概率内
func (s *CommonImpl) GetRateRandomPass(ctx context.Context, req *pb.GetRateRandomPassReq) (rsp *pb.GetRateRandomPassRsp,
	err error) {
	// rand.Seed(time.Now().UnixNano())
	probability := req.Rate
	scale := req.Times
	random := r.Intn(int(scale))
	if random < int(probability) {
		rsp = &pb.GetRateRandomPassRsp{
			HasPass: true,
		}
	} else {
		err = errs.NewCustomError(ctx, code.CommonRateRandomNotPass, "not pass")
	}
	return
}

// GetMatchStr 获取匹配字符串
func (s *CommonImpl) GetMatchStr(ctx context.Context, req *pb.GetMatchStrReq) (rsp *pb.GetMatchStrRsp,
	err error) {
	// fmt.Println("++++++++++++++++++++++")
	// fmt.Println(req.ToMatchValue)
	// fmt.Println("++++++++++++++++++++++")

	for _, v := range req.MatchData {
		// fmt.Println("++++++++ToMatchStr++++++++++++++")
		// fmt.Println(v.ToMatchStr)
		if v.ToMatchStr == req.ToMatchValue {
			rsp = &pb.GetMatchStrRsp{
				MatchValue: v.MatchStr,
			}
			return
		}
	}
	err = errs.NewCustomError(ctx, code.CommonNotMathchStr, " not Match")
	return

}

// ConfigureTwoValue 计算2个数值
func (s *CommonImpl) ConfigureTwoValue(ctx context.Context, req *pb.ConfigureTwoValueReq) (rsp *pb.ConfigureTwoValueRsp,
	err error) {
	// ConfigureTwoValueReq_Add      ConfigureTwoValueReq_Configure = 0 // 加
	// ConfigureTwoValueReq_Subtract ConfigureTwoValueReq_Configure = 1 // 减
	// ConfigureTwoValueReq_Multiply ConfigureTwoValueReq_Configure = 2 // 乘
	// ConfigureTwoValueReq_Divide   ConfigureTwoValueReq_Configure = 3 // 除
	var result float64

	switch req.Configure {
	// 加
	case pb.ConfigureTwoValueReq_Add:
		result = float64(req.ConfigureValue + req.ToConfigureValue)
		break
		// 减
	case pb.ConfigureTwoValueReq_Subtract:
		result = float64(req.ToConfigureValue - req.ConfigureValue)
		break
		// 乘
	case pb.ConfigureTwoValueReq_Multiply:
		result = float64(req.ToConfigureValue * req.ConfigureValue)
		break
		// 除
	case pb.ConfigureTwoValueReq_Divide:
		if req.ConfigureValue == 0 {
			err = errs.NewCustomError(ctx, code.CommonConfigureTwoValueFail,
				"divide fail ToConfigureValue  cant zero ")
			return
		}
		result = float64(req.ToConfigureValue / req.ConfigureValue)
		break
	}
	var value float64
	if req.Decimals > 0 {
		value, _ = decimal.NewFromFloat(result).Round(int32(req.Decimals)).Float64()
	} else {
		value = result
	}
	rsp = &pb.ConfigureTwoValueRsp{
		ConfiguredValue: float32(value),
	}
	return
}

// CommonTGlog 通用TGLog上报
func (s *CommonImpl) CommonTGlog(ctx context.Context, req *pb.CommonTGlogReq) (
	rsp *pb.CommonTGlogRsp, err error) {
	fmt.Println("---------------CommonTGlog1---------------")
	fmt.Printf("%#v\n", req)
	rsp = &pb.CommonTGlogRsp{}
	if !req.JoinSuccess {
		return
	}
	langType := metadata.GetLangType(ctx)
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: langType,
			XGameId:   int(req.XGameId),
			XSource:   "pc_web",
		},
		Action:         req.Action,
		SubAction:      req.SubAction,
		OriginalGameId: req.OriginalGameId,
		ExtentContent:  map[string]interface{}{},
	}
	if len(req.ExtConten) > 0 {
		for _, item := range strings.Split(req.ExtConten, ",") {
			arr := strings.Split(item, ":")
			tlogData.ExtentContent[arr[0]] = arr[1]
		}
	}
	// 被邀请者 用户自己的key
	fmt.Println("---------------1---------------")
	if len(req.InviteeKey) > 0 {
		userAccount, err1 := metadata.GetUserAccount(ctx)
		if err1 != nil {
			return nil, err1
		}
		tlogData.ExtentContent[req.InviteeKey] = userAccount.IntlAccount.OpenId
	}
	fmt.Println("---------------2---------------")
	if len(req.UserKey) > 0 {
		shortTableName, err1 := util.GetShortShareTableName(ctx, req.FsourceId)
		if err1 != nil {
			return
		}
		var intviter invitation.ShortCode
		selectdb := DB.DefaultConnect().WithContext(ctx).Table(shortTableName).Where("Fsource_id = ? and share_code = ?",
			req.FsourceId, req.ShareCode).First(&intviter)
		fmt.Println("---------------3---------------")
		if errors.Is(selectdb.Error, gorm.ErrRecordNotFound) {
			return
		}
		fmt.Println("---------------4---------------")
		if selectdb.Error != nil {
			return
		}
		fmt.Println("---------------5---------------")
		fmt.Printf("%#v\n", intviter)
		userUidList := strings.Split(intviter.UID, "-")
		if len(userUidList) < 2 {
			return
		}
		fmt.Println("---------------6---------------")
		tlogData.ExtentContent[req.UserKey] = userUidList[1]
	}
	report.ReportTlog(ctx, tlogData)
	return
}

// GetMaxOrMinNum 返回两个数中的最大或最小值
func (s *CommonImpl) GetMaxOrMinNum(ctx context.Context, req *pb.GetMaxOrMinNumReq) (rsp *pb.GetMaxOrMinNumRsp,
	err error) {

	if req.ReturnMax == 0 {
		return &pb.GetMaxOrMinNumRsp{
			ReturnNum: int32(math.Max(cast.ToFloat64(req.FirstNumber), cast.ToFloat64(req.SecondNumber))),
		}, nil
	}
	return &pb.GetMaxOrMinNumRsp{
		ReturnNum: int32(math.Min(cast.ToFloat64(req.FirstNumber), cast.ToFloat64(req.SecondNumber))),
	}, err
}

// GetTimeStamp 返回当前服务器时间戳
func (s *CommonImpl) GetTimeStamp(ctx context.Context, req *pb.GetTimeStampReq) (rsp *pb.GetTimeStampRsp, err error) {
	return &pb.GetTimeStampRsp{
		Time: int64(logic.GetCurrentTimestamp()),
	}, nil
}

// GetConfiguredCycleTimestamp 返回配置的周期时间戳
func (s *CommonImpl) GetConfiguredCycleTimestamp(ctx context.Context, req *pb.GetConfiguredCycleTimestampReq) (
	rsp *pb.GetConfiguredCycleTimestampRsp, err error) {

	var periodicTimeList []uint64
	for _, v := range req.TimeCycleList {
		periodicTimeList = append(periodicTimeList, v.CycleTime)
	}
	return &pb.GetConfiguredCycleTimestampRsp{
		TimeList: periodicTimeList,
	}, nil
}

// ContainsChar 当前字符是否在传入切片内
func (s *CommonImpl) ContainsChar(ctx context.Context, req *pb.ContainsCharReq) (rsp *pb.ContainsCharRsp, err error) {

	rsp = &pb.ContainsCharRsp{}
	if !req.ReverseJudgment {
		for _, str := range req.StrList {
			if str == req.Str {
				rsp.IsInside = true
				return
			}
		}
		return nil, errs.NewCustomError(ctx, code.CommonNotMathchStr, "str mismatch")
	} else {
		for _, str := range req.StrList {
			if str == req.Str {
				return nil, errs.NewCustomError(ctx, code.CommonMatchStr, "Match to string")
			}
		}
		rsp.IsInside = true
		return
	}
}

// GetSaveRoleInfoByUid 通过uid获取保存角色信息
func (s *CommonImpl) GetSaveRoleInfoByUid(ctx context.Context, req *pb.GetSaveRoleInfoByUidReq) (
	rsp *pb.GetSaveRoleInfoByUidRsp, err error,
) {

	openID := strings.Split(req.Uid, "-")[1]
	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         req.Uid,
		AccountType: accountPb.AccountType(1),
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    openID,
			ChannelId: 3,
		},
	})
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(string(accountData))

	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}

	gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: req.FsourceId}
	gameProxy := gamePb.NewGameClientProxy()
	gameRoleInfo, roleErr := gameProxy.GetSavedRoleInfo(ctx, gameReq, callopts...)
	if roleErr != nil {
		log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("get role info error by uid: %v",
			roleErr))
		return
	}
	rsp = &pb.GetSaveRoleInfoByUidRsp{
		RoleInfo: gameRoleInfo,
	}
	return
}

// RecordPeriodNumByRedis TODO
func (s *CommonImpl) RecordPeriodNumByRedis(ctx context.Context, req *pb.RecordPeriodNumByRedisReq) (
	rsp *pb.RecordPeriodNumByRedisRsp, err error) {
	rsp = &pb.RecordPeriodNumByRedisRsp{}
	var num int32
	num, err = logic.RecordPeriodNumByRedis(ctx, req.FsourceId, req.PeriodType, req.TimeZone, req.ExpireSecond)
	if err != nil {
		return
	}
	rsp.Num = num
	return
}

// GetPeriodNumByRedis TODO
func (s *CommonImpl) GetPeriodNumByRedis(ctx context.Context, req *pb.GetPeriodNumByRedisReq) (
	rsp *pb.GetPeriodNumByRedisRsp, err error) {
	rsp = &pb.GetPeriodNumByRedisRsp{}
	var num int32
	num, err = logic.GetPeriodNumByRedis(ctx, req.FsourceId, req.PeriodType, req.TimeZone, req.PeriodOffset)
	if err != nil {
		return
	}
	rsp.Num = num
	return
}

// ScheduleUpdatePeriodNum TODO
func (s *CommonImpl) ScheduleUpdatePeriodNum(ctx context.Context, req *pb.ScheduleUpdatePeriodNumReq) (
	rsp *pb.ScheduleUpdatePeriodNumRsp, err error) {
	rsp = &pb.ScheduleUpdatePeriodNumRsp{}
	err = logic.ScheduleUpdatePeriodNum(context.Background(), req.FsourceId, req.PeriodType, req.TimeZone,
		req.ExpireSecond)
	return
}

// GetTimeStage 获取当前时间所在阶段
func (s *CommonImpl) GetTimeStage(ctx context.Context, req *pb.GetTimeStageReq) (
	rsp *pb.GetTimeStageRsp, err error,
) {
	currentTime := int64(logic.GetCurrentTimestamp())
	rsp = &pb.GetTimeStageRsp{}
	stage := int32(0)
	isMatch := false
	for _, item := range req.ConditionList {
		result, err := s.CompareInt(ctx, &pb.CompareIntReq{
			CompareValue:   currentTime,
			Compare:        pb.CompareIntReq_Compare(item.Compare),
			ToCompareValue: item.ToCompareValue,
			ErrorCode:      503001,
		})
		if err != nil {
			// stage = item.IsConditionNotMet
			break
		} else {
			if result.CompareReuslt {
				stage = item.IsConditionMet
				isMatch = true
			} else {
				// stage = item.IsConditionNotMet
				break
			}
		}
	}

	if !isMatch && req.NoMatchToErr {
		err = errs.NewCustomError(ctx, code.NoMatch, "no match")
	}
	rsp.Stage = stage
	return
}

// ReturnNumByTagIdStage 通过输入tag_id返回对应值
func (s *CommonImpl) ReturnNumByTagIdStage(ctx context.Context, req *pb.ReturnNumByTagIdStageReq) (
	rsp *pb.ReturnNumByTagIdStageRsp, err error,
) {
	rsp = &pb.ReturnNumByTagIdStageRsp{
		Num: 0,
	}
	tagId, err := strconv.Atoi(req.TagId)
	if err != nil {
		err = errs.NewCustomError(ctx, code.TagIdIsErr, "tag_id is err")
		return
	}
	isMatch := false
	for _, item := range req.ConditionList {
		result, err := s.CompareInt(ctx, &pb.CompareIntReq{
			CompareValue:   int64(tagId),
			Compare:        pb.CompareIntReq_Compare(item.Compare),
			ToCompareValue: item.ToCompareValue,
		})
		if err != nil {
			// rsp.Num = item.IsConditionNotMet
			break
		} else {
			if result.CompareReuslt {
				isMatch = true
				rsp.Num = item.IsConditionMet
			} else {
				// rsp.Num = item.IsConditionNotMet
				break
			}
		}
	}

	if !isMatch && req.NoMatchToErr {
		err = errs.NewCustomError(ctx, code.NoMatch, "not match")
	}
	return
}

// CommonMapIntToString 通用-根据入参匹配映射
func (s *CommonImpl) CommonMapIntToString(ctx context.Context, req *pb.CommonMapIntToStringReq) (
	rsp *pb.CommonMapIntToStringRsp, err error) {
	for _, v := range req.GetMapItem() {
		if req.GetIndexId() == 0 {
			// 跳过空值
			continue
		}
		if v.GetKey() == req.GetIndexId() {
			return &pb.CommonMapIntToStringRsp{
				Result: v.GetValue(),
			}, nil
		}
	}
	return nil, errs.NewCustomError(ctx, code.CommonMapKeyValueDontMatch, "Map Key Value Dont Match")
}

// AddUsageShareLog 添加用户使用份额记录
func (s *CommonImpl) AddUsageShareLog(ctx context.Context, req *pb.AddUsageShareLogReq) (
	rsp *pb.AddUsageShareLogRsp, err error,
) {
	rsp = &pb.AddUsageShareLogRsp{}
	err = logic.AddUsageShareLog(ctx, req.FsourceId, req.Openid, int(req.Status), req.ShareMaxNum)
	return
}

// LockUsageShare 锁定份额
func (s *CommonImpl) LockUsageShare(ctx context.Context, req *pb.LockUsageShareReq) (
	rsp *pb.LockUsageShareRsp, err error,
) {
	rsp = &pb.LockUsageShareRsp{}
	err = logic.LockUsageShare(ctx, req.FsourceId, req.Openid, int(req.ShareMaxNum), req.Time)
	if err != nil {
		return
	}
	return
}

// CheckUsageShareLog 检查份额记录
func (s *CommonImpl) CheckUsageShareLog(ctx context.Context, req *pb.CheckUsageShareLogReq) (
	rsp *pb.CheckUsageShareLogRsp, err error,
) {
	rsp = &pb.CheckUsageShareLogRsp{}
	isHave, err := logic.CheckUsageShareLog(ctx, req.FsourceId, int(req.Status))
	if err != nil {
		return
	}
	rsp.IsHave = isHave
	return
}

// CheckAllTimes 查询总次数
func (s *CommonImpl) CheckAllTimes(ctx context.Context, req *pb.CheckAllTimesReq) (
	rsp *pb.CheckAllTimesRsp, err error,
) {
	rsp = &pb.CheckAllTimesRsp{}
	allTimes, err := logic.CheckAllTimes(ctx, req.FsourceId)
	if err != nil {
		return
	}
	rsp.AllTimes = int64(allTimes)
	return
}

// AddAllTimes 增加使用次数记录
func (s *CommonImpl) AddAllTimes(ctx context.Context, req *pb.AddAllTimesReq) (
	rsp *pb.AddAllTimesRsp, err error,
) {
	rsp = &pb.AddAllTimesRsp{}

	err = logic.AddAllTimes(ctx, req.FsourceId, int(req.Times))
	if err != nil {
		return
	}
	return
}

// CheckInTime 检查时间范围
func (s *CommonImpl) CheckInTime(ctx context.Context, req *pb.CheckInTimeReq) (rsp *pb.CheckInTimeRsp, err error) {
	rsp = &pb.CheckInTimeRsp{}
	timeNow := time.Now().Unix()
	// 为true  不在时间内报错
	if req.InTime == true {
		if timeNow < int64(req.BeginTimestamp) || timeNow > int64(req.EndTimestamp) {
			err = errs.NewCustomError(ctx, code.NotInCheckTime, "not in check time")
			return
		}
	} else {
		// 为False，在时间内报错
		if timeNow >= int64(req.BeginTimestamp) && timeNow <= int64(req.EndTimestamp) {
			err = errs.NewCustomError(ctx, code.NotOutCheckTime, "not out check time")
			return
		}
	}
	return
}

// AddSendPresentLog 添加发送礼包日志
func (s *CommonImpl) AddSendPresentLog(ctx context.Context, req *pb.AddSendPresentLogReq) (
	rsp *pb.AddSendPresentLogRsp, err error,
) {
	rsp = &pb.AddSendPresentLogRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	item := &commonModel.CommonSendPresentLog{}
	tableName, err := logic.GetAddSendPresentTable(ctx, req.FsourceId)
	if err != nil {
		return
	}

	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where("openid = ? and Fsource_id = ? and present_group_id = ? and present_id = ? ", strings.Split(userAccount.Uid,
			"-")[1], req.FsourceId, req.PresentInfo.PresentGroupId, req.PresentInfo.PresentId).
		First(&item)
	if db.Error != nil {
		// 没有查到就初始化一下
		if db.Error == gorm.ErrRecordNotFound {
			item = &commonModel.CommonSendPresentLog{
				Openid:         strings.Split(userAccount.Uid, "-")[1],
				LangType:       req.LangType,
				PresentGroupId: req.PresentInfo.PresentGroupId,
				PresentId:      req.PresentInfo.PresentId,
				FsourceId:      req.FsourceId,
				GameId:         req.RoleInfo.GameId,
				RoleId:         req.RoleInfo.RoleId,
				ZoneID:         int(req.RoleInfo.ZoneId),
				AreaID:         int(req.RoleInfo.AreaId),
				PlatID:         int(req.RoleInfo.PlatId),
				SendNum:        1,
			}
		} else {
			return rsp, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
		}
	}
	if db.Error == nil {
		item.SendNum += 1
	}
	db = DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where("openid = ? and Fsource_id = ? and present_group_id = ? and present_id = ? ", strings.Split(userAccount.Uid,
			"-")[1], req.FsourceId, req.PresentInfo.PresentGroupId, req.PresentInfo.PresentId).
		Save(&item)
	if db.Error != nil {
		return rsp, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	log.WithFieldsContext(ctx, "log_type", "addSendPresent", "str_field_1", item.FsourceId, "str_field_2", item.PresentId,
		"str_field_3", item.PresentGroupId).Infof("send present")
	return
}

// DownloadSendPresentLog 导出发送礼包日志
func (s *CommonImpl) DownloadSendPresentLog(ctx context.Context, req *pb.DownloadSendPresentLogReq) (
	rsp *pb.DownloadSendPresentLogRsp, err error,
) {
	rsp = &pb.DownloadSendPresentLogRsp{}
	data := make([][]string, 0)
	title := []string{"openid", "area_id", "zone_id", "plat_id", "role_id", "lang_type", "send_num"}
	data = append(data, title)
	tableName, err := logic.GetAddSendPresentTable(ctx, req.FsourceId)
	if err != nil {
		return
	}

	list := make([]*commonModel.CommonSendPresentLog, 0)
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where("Fsource_id = ? and present_group_id = ? and present_id = ? ", req.FsourceId, req.PresentGroupId,
			req.PresentId).
		Find(&list)
	if db.Error != nil {
		return rsp, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}

	for _, item := range list {
		data = append(data, []string{
			item.Openid, fmt.Sprintf("%v", item.AreaID), fmt.Sprintf("%v", item.ZoneID), fmt.Sprintf("%v", item.PlatID),
			item.RoleId, item.LangType, fmt.Sprintf("%v", item.SendNum),
		})
	}

	err = userdata.SendWxBot(ctx, req.FileName, data, req.QywxChatid, req.QywxBotid)

	return
}

// ReturnPrsentIdByTagId 根据tag_id返回对应礼包信息
func (s *CommonImpl) ReturnPrsentIdByTagId(ctx context.Context, req *pb.ReturnPrsentIdByTagIdReq) (
	rsp *pb.ReturnPrsentIdByTagIdRsp, err error,
) {
	rsp = &pb.ReturnPrsentIdByTagIdRsp{}
	tagId, err := strconv.Atoi(req.TagId)
	if err != nil {
		return
	}
	isMatch := false
	for _, item := range req.ConditionList {
		if tagId == int(item.ToCompareValue) {
			isMatch = true
			rsp.PresentGroupId = item.PresentGroupId
			rsp.PresentId = item.PresentId
			return
		}
	}

	if !isMatch && req.NoMatchToErr {
		err = errs.NewCustomError(ctx, code.NoMatch, "no match")
	}
	return
}

// SendPresentByLog 根据发奖记录发奖
func (s *CommonImpl) SendPresentByLog(ctx context.Context, req *pb.SendPresentByLogReq) (rsp *pb.SendPresentByLogRsp,
	err error) {
	rsp = &pb.SendPresentByLogRsp{}
	go logic.SendPresentByLog(ctx, req.FsourceId)

	return
}

// MapStringBoolToIntBool 根据发奖记录发奖
func (s *CommonImpl) MapStringBoolToIntBool(ctx context.Context, req *pb.MapStringBoolToIntBoolReq) (
	rsp *pb.MapStringBoolToIntBoolRsp,
	err error) {
	rsp = &pb.MapStringBoolToIntBoolRsp{}
	rsp.Rsp = make(map[int32]bool)
	for _, matchData := range req.List {
		if val, ok := req.Data[matchData.MatchKey]; ok {
			rsp.Rsp[matchData.MatchValue] = val
		}
	}

	return
}

// IntToString 根据发奖记录发奖
func (s *CommonImpl) IntToString(ctx context.Context, req *pb.IntToStringReq) (rsp *pb.IntToStringRsp,
	err error) {
	rsp = &pb.IntToStringRsp{}
	rsp.Rsp = cast.ToString(req.Req)
	return
}

// CommonSleep TODO
func (s *CommonImpl) CommonSleep(ctx context.Context, req *pb.CommonSleepReq) (rsp *pb.CommonSleepRsp, err error) {
	rsp = &pb.CommonSleepRsp{}
	time.Sleep(time.Duration(req.Second) * time.Second)
	return
}
