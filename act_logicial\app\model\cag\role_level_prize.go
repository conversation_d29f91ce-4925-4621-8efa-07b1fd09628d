package cag

import "time"

// CagRoleLevelPrizeRecord cag创建角色和等级领奖记录表
type CagRoleLevelPrizeRecord struct {
	ID                   int64     `gorm:"column:id;primary_key;AUTO_INCREMENT"`                  // 自增id
	GameID               string    `gorm:"column:game_id;NOT NULL"`                               // 游戏id
	ActivityID           string    `gorm:"column:activity_id;NOT NULL"`                           // 活动id
	UserID               string    `gorm:"column:user_id;NOT NULL"`                               // 用户id
	LipOpenID            string    `gorm:"column:lip_open_id;NOT NULL"`                           // lip的openid
	OpenID               string    `gorm:"column:open_id;NOT NULL"`                               // 游戏openid
	PrizeType            int       `gorm:"column:prize_type;NOT NULL"`                            // 奖励类型(1-创建角色,2-等级)
	Level                int       `gorm:"column:level;NOT NULL"`                                 // 等级，等级奖励该字段才有效
	AmsSerialID          string    `gorm:"column:ams_serial_id;NOT NULL"`                         // 发奖serial_id
	LangType             string    `gorm:"column:lang_type;NOT NULL"`                             // 语言类型
	SendPrizeTimes       uint32    `gorm:"column:send_prize_times;NOT NULL"`                      // 发奖次数
	CheckCreateRoleTimes uint32    `gorm:"column:check_create_role_times;NOT NULL"`               // 检查创建角色次数
	Status               int       `gorm:"column:status;default:0;NOT NULL"`                      // 任务状态(0-等待角色创建 1-等待发奖 2-成功 3-失败)
	HasPrizeFlag         int       `gorm:"column:has_prize_flag;default:0;NOT NULL"`              // 重复发放标记
	AmsPrizeLimit        int       `gorm:"column:ams_prize_limit;default:0;NOT NULL"`             // ams发奖用户是否达到数量限制，0-没有，1-达到限制
	AmsRspCode           int       `gorm:"column:ams_rsp_code;default:0;NOT NULL"`                // ams发奖其他错误码
	UpdateTime           time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL"` // 更新时间
	CreateTime           time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL"` // 创建时间
}

// TableName 获取数据库表名
func (m *CagRoleLevelPrizeRecord) TableName() string {
	return "t_cag_role_level_prize_record"
}
