package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func TitleLanguageGet(id int64) (*model.TitleLanguage, error) {
	var title model.TitleLanguage
	db := DB.SelectConnect("db_standalonesite").Table((&model.TitleLanguage{}).TableName())
	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}

	err := db.First(&title).Error
	if err != nil {
		return &title, err
	}

	return &title, nil
}
