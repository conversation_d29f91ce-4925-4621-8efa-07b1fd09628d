package dao

import (
	"sort"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

type UserCollectionConditions struct {
	IntlOpenid    string
	IntlOpenids   []string
	ToIntlOpenid  string
	ToIntlOpenids []string
	Order         []*OrderConditions
	LtId          int64
}

func UserCollectionGet(id int64, intlOpenID, toUserOpenid string, needDelRecord bool, withUserInfo bool) (*model.UserCollection, error) {
	var userCollection model.UserCollection
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserCollection{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if intlOpenID != "" {
		db = db.Where("intl_openid = ?", intlOpenID)
	}
	if toUserOpenid != "" {
		db = db.Where("to_intl_openid = ?", toUserOpenid)
	}

	if needDelRecord {
		// 查询已删除is_del=1的记录
		err := db.Unscoped().First(&userCollection).Error
		if err != nil {
			return nil, err
		}
	} else {
		// 查询is_del=0的记录
		err := db.First(&userCollection).Error
		if err != nil {
			return nil, err
		}
	}

	if withUserInfo {
		userInfo, err := GetUserByIntlOpenid(userCollection.IntlOpenid)
		if err != nil {
			return nil, err
		}
		userCollection.User = userInfo
	}
	return &userCollection, nil
}

func UserCollectionCreate(collection *model.UserCollection) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.UserCollection{}).TableName()).Omit("User").Create(&collection).Error

	return err
}

func UserCollectionDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserCollection{}).TableName()).Omit("User").Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_mutual":  0,
		"is_del":     1,
	}).Error
}

func UseCollectionUpdate(id int64, isMutual int) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserCollection{}).TableName()).Omit("User").Where("id = ?", id).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"is_mutual":   isMutual,
		"is_del":      0,
	}).Error
}

func UserCollectionList(conditions *UserCollectionConditions, limit int) ([]*model.UserCollection, error) {
	var collections []*model.UserCollection
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserCollection{}).TableName())

	if limit > 0 {
		db = db.Limit(limit)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.ToIntlOpenid != "" {
		db = db.Where("to_intl_openid = ?", conditions.ToIntlOpenid)
	}
	if len(conditions.ToIntlOpenids) > 0 {
		db = db.Where("to_intl_openid in ?", conditions.ToIntlOpenids)
	}
	if len(conditions.IntlOpenids) > 0 {
		db = db.Where("intl_openid in ?", conditions.IntlOpenids)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}

	err = db.Find(&collections).Error
	if err != nil {
		return nil, err
	}
	if collections == nil {
		collections = make([]*model.UserCollection, 0)
	}
	var userOpenids = make([]string, 0, len(collections))
	for _, collection := range collections {
		userOpenids = append(userOpenids, collection.IntlOpenid)
	}
	userInfos, err := GetUserListByOpenid(userOpenids)
	if err != nil {
		return nil, err
	}
	for i, collection := range collections {
		for _, info := range userInfos {
			if info.IntlOpenid == collection.IntlOpenid {
				collections[i].User = info
			}
		}
		if collection.User == nil {
			collection.User = &model.UserContent{
				Model: &model.Model{
					ID: 0,
				},
				IntlOpenid: collection.IntlOpenid,
			}
			// collectionsWithUserInfos = append(collectionsWithUserInfos, collections[i])
		}
	}
	sort.Slice(collections, func(i, j int) bool {
		return collections[i].User.ID > collections[j].User.ID
	})
	return collections, nil
}

func UserCollectionListFollow(conditions *UserCollectionConditions, limit int) ([]*model.UserCollection, error) {
	var collections []*model.UserCollection
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserCollection{}).TableName())
	if limit > 0 {
		db = db.Limit(limit)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.ToIntlOpenid != "" {
		db = db.Where("to_intl_openid = ?", conditions.ToIntlOpenid)
	}
	if len(conditions.ToIntlOpenids) > 0 {
		db = db.Where("to_intl_openid in ?", conditions.ToIntlOpenids)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if len(conditions.IntlOpenids) > 0 {
		db = db.Where("intl_openid in ?", conditions.IntlOpenids)
	}

	err = db.Find(&collections).Error
	if err != nil {
		return nil, err
	}
	var userOpenids = make([]string, 0, len(collections))
	for _, collection := range collections {
		userOpenids = append(userOpenids, collection.ToIntlOpenid)
	}
	userInfos, err := GetUserListByOpenid(userOpenids)
	if err != nil {
		return nil, err
	}
	for i, collection := range collections {
		for _, info := range userInfos {
			if info.IntlOpenid == collection.ToIntlOpenid {
				collections[i].User = info
			}
		}
		if collection.User == nil {
			collection.User = &model.UserContent{
				Model: &model.Model{
					ID: 0,
				},
				IntlOpenid: collection.IntlOpenid,
			}
		}
	}
	// sort.Slice(collections, func(i, j int) bool {
	// 	return collections[i].User.ID > collections[j].User.ID
	// })
	return collections, nil
}

func UserCollectionCount(conditions *UserCollectionConditions) (int32, error) {
	var collections []*model.UserCollection
	db := DB.SelectConnect("db_standalonesite")
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.ToIntlOpenid != "" {
		db = db.Where("to_intl_openid = ?", conditions.ToIntlOpenid)
	}
	if len(conditions.ToIntlOpenids) > 0 {
		db = db.Where("to_intl_openid in ?", conditions.ToIntlOpenids)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if len(conditions.IntlOpenids) > 0 {
		db = db.Where("intl_openid in ?", conditions.IntlOpenids)
	}
	err := db.Table((&model.UserCollection{}).TableName()).Find(&collections).Error
	if err != nil {
		return 0, err
	}
	if len(collections) == 0 {
		return 0, nil
	}
	var userIds = make([]string, 0, len(collections))
	for _, collection := range collections {
		userIds = append(userIds, collection.IntlOpenid)
	}
	openids, err := GetUserListByOpenid(userIds)
	if err != nil {
		return 0, err
	}
	return int32(len(openids)), nil
}

func UserCollectionFollowCount(conditions *UserCollectionConditions) (int64, error) {
	var collections []*model.UserCollection
	db := DB.SelectConnect("db_standalonesite")

	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.ToIntlOpenid != "" {
		db = db.Where("to_intl_openid = ?", conditions.ToIntlOpenid)
	}
	if len(conditions.ToIntlOpenids) > 0 {
		db = db.Where("to_intl_openid in ?", conditions.ToIntlOpenids)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if len(conditions.IntlOpenids) > 0 {
		db = db.Where("intl_openid in ?", conditions.IntlOpenids)
	}
	err := db.Table((&model.UserCollection{}).TableName()).Find(&collections).Error
	if err != nil {
		return 0, err
	}
	if len(collections) == 0 {
		return 0, nil
	}
	var userIds = make([]string, 0, len(collections))
	for _, collection := range collections {
		userIds = append(userIds, collection.ToIntlOpenid)
	}
	openids, err := GetUserListByOpenid(userIds)
	if err != nil {
		return 0, err
	}
	return int64(len(openids)), nil
}

func UserCollectionGetMyFollowUserIDs(userIntlOpenid string) ([]string, error) {
	var myFollowUserIDs []string
	err := DB.SelectConnect("db_standalonesite").Table((&model.UserCollection{}).TableName()).Where("intl_openid = ? AND is_del = ?", userIntlOpenid, 0).Select("to_intl_openid").Find(&myFollowUserIDs).Error
	return myFollowUserIDs, err
}

func UserCollectionsGet(id int64, intlOpenIDs, toUserOpenids []string, needDelRecord bool, withUserInfo bool) ([]*model.UserCollection, error) {
	var userCollections = make([]*model.UserCollection, 0)
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserCollection{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if len(intlOpenIDs) != 0 {
		db = db.Where("intl_openid in ?", intlOpenIDs)
	}
	if len(toUserOpenids) != 0 {
		db = db.Where("to_intl_openid in ?", toUserOpenids)
	}

	if needDelRecord {
		// 查询已删除is_del=1的记录
		err := db.Unscoped().Find(&userCollections).Error
		if err != nil {
			return nil, err
		}
	} else {
		// 查询is_del=0的记录
		err := db.Find(&userCollections).Error
		if err != nil {
			return nil, err
		}
	}

	if withUserInfo {
		userInfos, err := GetUserListByOpenidV2(intlOpenIDs, false)
		if err != nil {
			return nil, err
		}
		for _, userCollection := range userCollections {
			for _, userInfo := range userInfos {
				if userCollection.ToIntlOpenid == userInfo.IntlOpenid {
					userCollection.User = userInfo
				}
			}
		}
	}
	return userCollections, nil
}

// 批量添加
func UserCollectionsCreate(tx *gorm.DB, collection []*model.UserCollection) error {
	if tx == nil {
		tx = DB.SelectConnect("db_standalonesite")
	}
	err := tx.Table((&model.UserCollection{}).TableName()).Omit("User").Create(&collection).Error
	return err
}

// 批量更新状态
func UseCollectionsUpdate(tx *gorm.DB, ids []int64, isMutual int) error {
	if tx == nil {
		tx = DB.SelectConnect("db_standalonesite")
	}
	return tx.Table((&model.UserCollection{}).TableName()).Omit("User").Where("id in ?", ids).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"is_mutual":   isMutual,
		"is_del":      0,
	}).Error
}
