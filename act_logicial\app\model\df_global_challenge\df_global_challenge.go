// Package df_global_challenge TODO
package df_global_challenge

import "trpc.act.logicial/app/model"

// DfGlobalChallengeRegionModel TODO
type DfGlobalChallengeRegionModel struct {
	model.AppModel
}

// TableName .
func (DfGlobalChallengeRegionModel) TableName() string {
	return "df_global_challenge_region"
}

// DfGlobalChallengeRegion TODO
// SendTemp 临时结构
type DfGlobalChallengeRegion struct {
	DfGlobalChallengeRegionModel
	ID            int64  `gorm:"type:int(11);column:id;primary_key"`
	RegionId      int32  `gorm:"type:tinyint(1);column:region_id;0"`
	Country       string `gorm:"type:varchar(32);column:country;"`
	Region        string `gorm:"type:varchar(32);column:region;"`
	CountryPoint1 int64  `gorm:"type:int(11);column:country_point_1;0"`
	CountryPoint2 int64  `gorm:"type:int(11);column:country_point_2;0"`
	CountryPoint3 int64  `gorm:"type:int(11);column:country_point_3;0"`
	CountryRank1  int64  `gorm:"type:int(11);column:country_rank_1;0"`
	CountryRank2  int64  `gorm:"type:int(11);column:country_rank_2;0"`
	CountryRank3  int64  `gorm:"type:int(11);column:country_rank_3;0"`
}

// DfGlobalChallengeSelfModel TODO
type DfGlobalChallengeSelfModel struct {
	model.AppModel
}

// TableName .
func (DfGlobalChallengeSelfModel) TableName() string {
	return "df_global_challenge_self"
}

// DfGlobalChallengeSelf TODO
// SendTemp 临时结构
type DfGlobalChallengeSelf struct {
	DfGlobalChallengeSelfModel
	ID               int64  `gorm:"type:int(11);column:id;primary_key"`
	CountryId        int32  `gorm:"type:tinyint(1);column:country_id;0"`
	RegionId         int32  `gorm:"type:tinyint(1);column:region_id;0"`
	Point            int64  `gorm:"type:int(11);column:point;0"`
	SolPoint         int64  `gorm:"type:int(11);column:sol_point;0"`
	BattlefieldPoint int64  `gorm:"type:int(11);column:battlefield_point;0"`
	UID              string `gorm:"type:varchar(32);column:uid;"`
	FsourceID        string `gorm:"type:varchar(32);column:Fsource_id;"`
	LangType         string `gorm:"type:varchar(32);column:lang_type;"`
}

// DfGlobalChallengeTeamModel TODO
type DfGlobalChallengeTeamModel struct {
	model.AppModel
}

// TableName .
func (DfGlobalChallengeTeamModel) TableName() string {
	return "df_global_challenge_team"
}

// DfGlobalChallengeTeam TODO
// SendTemp 临时结构
type DfGlobalChallengeTeam struct {
	DfGlobalChallengeTeamModel
	ID               int64  `gorm:"type:int(11);column:id;primary_key"`
	FsourceID        string `gorm:"type:varchar(32);column:Fsource_id;"`
	ShareCode        string `gorm:"type:varchar(32);column:share_code;"`
	TeamName         string `gorm:"type:varchar(32);column:team_name;"`
	LeaderAvatar     string `gorm:"type:varchar(32);column:leader_avatar;"`
	LeaderUid        string `gorm:"type:varchar(32);column:leader_uid;"`
	SolPoint         int64  `gorm:"type:int(11);column:sol_point;0"`
	SolRank          int64  `gorm:"type:int(11);column:sol_rank;0"`
	BattlefieldPoint int64  `gorm:"type:int(11);column:battlefield_point;0"`
	BattlefieldRank  int64  `gorm:"type:int(11);column:battlefield_rank;0"`
}

// DfGlobalChallengeSendModel TODO
type DfGlobalChallengeSendModel struct {
	model.AppModel
}

// TableName .
func (DfGlobalChallengeSendModel) TableName() string {
	return "df_global_challenge_send"
}

// DfGlobalChallengeSend TODO
// NikkeMusicSend 临时结构
type DfGlobalChallengeSend struct {
	DfGlobalChallengeSendModel
	ID          uint   `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(255);column:uid;"`
	Status      int    `gorm:"type:int(1);column:status;not null"`
	Tag         string `gorm:"type:varchar(255);column:tag;not null"`
	FsourceID   string `gorm:"type:varchar(255);column:Fsource_id;not null"`
	LangType    string `gorm:"type:varchar(255);column:lang_type;"`
	AccountType int    `gorm:"type:smallint(6);column:account_type" comment:"用户类型"`
}

// DfGlobalChallengeTimeModel TODO
type DfGlobalChallengeTimeModel struct {
	model.AppModel
}

// TableName .
func (DfGlobalChallengeTimeModel) TableName() string {
	return "df_global_challenge_time"
}

// DfGlobalChallengeTime TODO
type DfGlobalChallengeTime struct {
	DfGlobalChallengeTimeModel
	ID   int64  `gorm:"type:int(11);column:id;primary_key"`
	Type string `gorm:"type:varchar(255);column:type;not null"`
	Time int64  `gorm:"column:time;" json:"time"`
}
