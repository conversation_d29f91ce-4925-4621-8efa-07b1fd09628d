package kafka

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-database/kafka"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
)

// Produce 生产Kafka数据
func Produce(ctx context.Context, producerName, key, value string) error {
	// callopts := []client.Option{
	// 	client.WithTarget("kafka://***********:9092?topic=lipass-points-gamelogin&group=lipass-points-consumer&compression=none&batch=100&batchFlush=1000"),
	// 	client.WithProtocol("kafka"),
	// }
	// proxy := kafka.NewClientProxy(producerName, callopts...)
	proxy := kafka.NewClientProxy(producerName)
	err := proxy.Produce(ctx, []byte(key), []byte(value))
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Kafka produce info failed. key : %s, value : %s, err:%v\n", key, value, err)
		err = errs.NewCustomError(ctx, code.SiteMessageProducerErr, "standalone site message producer failed")
		return err
	}
	return nil
}
