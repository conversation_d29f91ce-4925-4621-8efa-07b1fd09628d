// Package general 通用
package general

import (
	"trpc.act.logicial/app/model"
)

// ConfigModel .
type ConfigModel struct {
}

// TableName .
func (ConfigModel) TableName() string {
	return "general_config"
}

// ConfigCondition 查询条件
type ConfigCondition struct {
	ConfigModel
	ID         int64  `gorm:"type:int(11);column:id;primary_key"`
	StorageKey string `gorm:"type:varchar(255);column:storage_key;not null"`
	FsourceID  string `gorm:"type:varchar(300);column:Fsource_id;not null"`
}

// ConfigData 查询条件
type ConfigData struct {
	ConfigModel
	ID         int64  `gorm:"type:int(11);column:id;primary_key"`
	StorageKey string `gorm:"type:varchar(255);column:storage_key;not null"`
	FsourceID  string `gorm:"type:varchar(300);column:Fsource_id;not null"`
}

// LogModel .
type LogModel struct {
	model.AppModel
}

// TableName .
func (LogModel) TableName() string {
	return "general_log"
}

// LogCondition 查询条件
type LogCondition struct {
	LogModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(32);column:uid;''"`
	AccountType int32  `gorm:"type:tinyint(1);column:account_type;0"`
	FsourceID   string `gorm:"type:varchar(255);column:Fsource_id;not null"`
}

// LogData 查询条件
type LogData struct {
	LogModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(32);column:uid;"`
	AccountType int32  `gorm:"type:tinyint(1);column:account_type;0"`
	FsourceID   string `gorm:"type:varchar(255);column:Fsource_id;not null"`
	StorageKey  string `gorm:"type:varchar(2000);column:storage_key;not null"`
	StorageVal  string `gorm:"type:varchar(2000);column:storage_val;not null"`
	StorageTag  string `gorm:"type:varchar(2000);column:storage_tag;not null"`
}
