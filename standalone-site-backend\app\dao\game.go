package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"time"
	"trpc.publishing_application.standalonesite/app/model"
)

type GameConditions struct {
	Language string
}

func GameGet(id int64, language string, gameId string) (*model.Game, error) {
	var game model.Game
	db := DB.SelectConnect("db_standalonesite")
	tn := (&model.Game{}).TableName() + "."
	if id > 0 {
		db = db.Where(tn+"id= ? AND "+tn+"is_del = ?", id, 0)
	} else {
		db = db.Where(tn+"game_id= ? AND "+tn+"is_del = ?", gameId, 0)
	}
	if language != "" {
		db = db.Where("GameLanguage.language = ", language)
	}

	db = db.Joins("GameLanguage")
	err := db.First(&game).Error
	if err != nil {
		return &game, err
	}

	return &game, nil
}

func GameCreate(game *model.Game) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.Game{}).TableName()).Create(game).Error

	return err
}

func GameUpdate(game *model.Game) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Game{}).TableName()).Where("id = ? AND is_del = ?", game.ID, 0).Save(game).Error
}

func GameDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Game{}).TableName()).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func GameList(conditions *GameConditions, offset, limit int) ([]*model.Game, error) {
	var games []*model.Game
	var err error
	db := DB.SelectConnect("db_standalonesite")
	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	if conditions.Language != "" {
		db = db.Where("GameLanguage.langauge = ?", conditions.Language)
	}
	db = db.Joins("GameLanguage").Order("GameLanguage.id DESC")
	if err = db.Find(&games).Error; err != nil {
		return nil, err
	}

	return games, nil
}

func GamesFrom(games []int) (res []*model.Game, err error) {
	err = DB.SelectConnect("db_standalonesite").Table((&model.Game{}).TableName()).Where("game_id IN ?", games).Find(&res).Error
	return
}

func GameListAllPostGameArea(db *gorm.DB) (results []*model.GameAreaItem, err error) {
	err = DB.SelectConnect("db_standalonesite").Table(db.NamingStrategy.TableName("Post")).Distinct("game_id", "area_id").Find(&results).Error
	return
}
