package df_tmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/logic/df_tmp_common"
	"trpc.act.logicial/app/model/df_activity"
	"trpc.act.logicial/app/mysql/df_activity_repo"
	"trpc.act.logicial/app/redis"
)

// ClaimGunActPrizeProc
type ClaimGunActPrizeProc struct {
	userInfo *accountPb.UserAccount
}

// ClaimGunActPrize 领取拼枪活动奖励
func ClaimGunActPrize(ctx context.Context, req *pb.ClaimGunActPrizeReq) (*pb.ClaimGunActPrizeRsp, error) {
	log.DebugContextf(ctx, "ClaimGunActPrize enter, req: %v", req)
	rsp := &pb.ClaimGunActPrizeRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ClaimGunActPrize get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	proc := &ClaimGunActPrizeProc{
		userInfo: &userAccount,
	}
	defer func() {
		if nil != err {
			log.ErrorContextf(ctx, "ClaimGunActPrize rsp error:%v", err)
		}
	}()
	err = proc.Process(ctx, req, rsp)
	if nil != err {
		return nil, err
	}
	return rsp, nil
}
func (p *ClaimGunActPrizeProc) Process(ctx context.Context,
	req *pb.ClaimGunActPrizeReq, rsp *pb.ClaimGunActPrizeRsp) error {
	// 加锁，频率控制以及避免并发
	key := redis.GetGunActClaimPrizeLockKey(p.userInfo.Uid)
	ok := redis.LockByKey(ctx, key, 10)
	if !ok {
		log.ErrorContextf(ctx, "ClaimGunActPrizeProc LockByKey fail, key: %v", key)
		return code.ErrRequestFrequencyExceededLimitError
	}

	// 查询任务
	task, err := p.getTask(ctx, req)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimGunActPrizeProc getTask error: %v, taskId: %v", err, req.TaskId)
		return err
	}

	if task.Status == df_activity.GunTaskStatusProcessing ||
		task.Status == df_activity.GunTaskStatusNotCompleted {
		// 任务未完成
		log.ErrorContextf(ctx, "ClaimGunActPrizeProc task is processing, ignore, taskId: %v", task.TaskId)
		return code.ErrActivityTaskHelpNotComplete
	}
	if task.Status == df_activity.GunTaskStatusCancel {
		// 任务取消
		log.ErrorContextf(ctx, "ClaimGunActPrizeProc task is cancel, ignore, taskId: %v", task.TaskId)
		return code.ErrActivityNotFindTask
	}
	if task.Status != df_activity.GunTaskStatusWaitClaimPrize {
		// 已经领取， 直接返回
		log.DebugContextf(ctx, "ClaimGunActPrizeProc task prize has claim, ignore, "+
			"task status: %v, taskId: %v", task.Status, req.TaskId)
		return nil
	}
	// 更新领取状态
	err = df_activity_repo.GunActivityRepoClient.UpdateTaskStatus(ctx, task.TaskId,
		df_activity.GunTaskStatusWaitDistributePrize, df_activity.GunTaskStatusWaitClaimPrize)
	if nil != err { // 更新失败， 返回
		log.ErrorContextf(ctx, "HelpGunActTaskProc UpdateTaskStatus error:%v, taskId: %v",
			err, task.TaskId)
		// 取消加锁
		redis.UnLockByKey(ctx, key)
		return code.ErrSystemError
	}

	// 自动发奖
	task.Status = df_activity.GunTaskStatusWaitDistributePrize
	err = df_tmp_common.SendGunActTaskPrize(ctx, task)
	if nil != err {
		// 更新失败， 记录日志， 不返回错误，后台会自动重发
		log.ErrorContextf(ctx, "ClaimGunActPrizeProc send prize error:%v, taskId: %v",
			err, task.TaskId)
	}

	return nil
}

func (p *ClaimGunActPrizeProc) getTask(ctx context.Context,
	req *pb.ClaimGunActPrizeReq) (task *df_activity.GunActivityTask, err error) {
	tasks, err := df_activity_repo.GunActivityRepoClient.GetTaskByTaskId(ctx,
		req.TaskId)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimGunActPrizeProc GetTaskByTaskId error: %v", err)
		return nil, code.ErrSystemError
	}
	if tasks == nil || len(tasks) <= 0 {
		log.ErrorContextf(ctx, "ClaimGunActPrizeProc GetTaskByTaskId not find task,"+
			" taskId: %v", req.TaskId)
		return nil, code.ErrActivityNotFindTask
	}
	task = tasks[0]
	return
}
