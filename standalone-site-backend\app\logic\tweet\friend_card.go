package tweet

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	gameGamePB "git.woa.com/trpcprotocol/publishing_application/stand_alone_site_game_game"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

// ps: 先注释语言的变更，后期再改回来，24-11-6
func SendNikkeFriendRequest(c context.Context, req *pb.SendFriendRequestReq, intlOpenid, language string) (rsp *pb.SendFriendRequestRsp, err error) {
	rsp = &pb.SendFriendRequestRsp{}
	limitSendNikkeFriendRequestKey := cache.GetSendNikkeFriendRequestKey(intlOpenid, req.PostUuid)
	exists, err := redis.GetClient().Exists(c, limitSendNikkeFriendRequestKey).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SendNikkeFriendRequest redis.GetClient().Exists err, limitSendNikkeFriendRequestKey:(%s), err=(%v)", limitSendNikkeFriendRequestKey, err)
		return rsp, errs.NewCustomError(c, code.AddNikkeFriendRedisError, "The request to add a NIKKE game friend is sent abnormally.")
	}
	// 已发送，限制24小时
	if exists == 1 {
		return rsp, errs.NewCustomError(c, code.SendFriendRequestLimitError, "Request to add NIKKE game friend has been sent")
	}
	postContentList, err := dao.GetPostContentList(req.PostUuid, false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SendNikkeFriendRequest GetPostContentList err, post_uuid:(%s), err=(%v)", req.PostUuid, err)
		return rsp, errs.NewCustomError(c, code.AddNikkeFriendGetRoleInfoError, "The character information related to the post could not be obtained.")
	}
	var isExistLanguage bool
	var postContent *model.PostContent
	for _, content := range postContentList {
		if content.Language == language {
			postContent = content
			isExistLanguage = true
			break
		}
	}
	if !isExistLanguage {
		// 直接取最靠前的一条数据，从数据库拿出来都是排好序的，第一条就是兜底语言
		postContent = postContentList[0]
	}
	if intlOpenid == postContent.IntlOpenid {
		return rsp, errs.NewCustomError(c, code.AddNikkeFriendMyselfError, "You cannot initiate a NIKKE game friend request to yourself")
	}
	if postContent.FriendCardInfo == "" {
		return rsp, errs.NewCustomError(c, code.AddNikkeFriendGetRoleInfoEmpty, "The character information related to the post could not be obtained.")
	}

	var roleInfo *gamePb.RoleInfo
	err = json.Unmarshal([]byte(postContent.FriendCardInfo), &roleInfo)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SendNikkeFriendRequest json.Unmarshal err: %v", err)
		return rsp, errs.NewCustomError(c, code.AddNikkeFriendUnmarshalRoleInfoError, "The character information related to the post could not be obtained.")
	}

	sendFriendRequestReq := &gameGamePB.SendFriendRequestReq{
		Uid:          intlOpenid,
		FriendUid:    postContent.IntlOpenid,
		FriendAreaId: int32(roleInfo.AreaId),
		FriendRoleId: roleInfo.RoleId,
	}
	_, err = gameProxy.SendFriendRequest(c, sendFriendRequestReq)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SendNikkeFriendRequest gameProxy.SendFriendRequest err: %v", err)
		errCode := code.AddNikkeFriendUnmarshalRoleInfoError
		trpcErr := errs.ParseError(c, err)
		if trpcErr.Code != errs.DefaultErrorCode {
			errCode = int(trpcErr.Code)
		}
		return rsp, errs.NewCustomError(c, errCode, "The request to add a NIKKE game friend was sent abnormally")
	}
	redis.GetClient().SetEX(c, limitSendNikkeFriendRequestKey, 1, time.Duration(config.GetConfig().Dynamic.SendFriendRequestLimitDuration)*time.Minute).Result()
	return &pb.SendFriendRequestRsp{}, nil
}
