package main

import (
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database"
	ES "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/elasticsearch"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	_ "git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/log"
	_ "git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	trpc "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-log-cls"
	_ "git.code.oa.com/trpc-go/trpc-metrics-prometheus"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-skywalking"
	activityPB "git.woa.com/trpcprotocol/publishing_application/standalonesite_activity"
	sitePB "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	siteUserPB "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	local "git.woa.com/wegame_app_go/common/trpc-config-local"
	_ "go.uber.org/automaxprocs"
	"go.uber.org/automaxprocs/maxprocs"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/controller/standalonesite/activity"
	"trpc.publishing_application.standalonesite/app/controller/standalonesite/dynamics"
	"trpc.publishing_application.standalonesite/app/controller/standalonesite/user"

	"trpc.publishing_application.standalonesite/app/handler"
	"trpc.publishing_application.standalonesite/app/logic/message"

	trpcKafka "git.code.oa.com/trpc-go/trpc-database/kafka"
	"trpc.publishing_application.standalonesite/app/pkg/elasticsearch"
)

func changeDefaultConfig() {
	// 默认mysql连接DB
	mysql.DefaultConnectName = "db_standalonesite"
	// 修改redis默认连接
	redis.DefaultConnectName = "standalonesite"
	// es的默认连接
	ES.DefaultConnectName = "es_standalonesite"
	// 修改trpc_go文件路径
	trpc.ServerConfigPath = "./conf/config/trpc_go.yaml"
	// 服务配置路径
	local.ServiceYamlPath = "./conf/"
	// client.yaml路径
	local.ClientYamlName = "client/client.yaml"
}

func main() {
	// 修改默认配置
	changeDefaultConfig()

	s := trpc.NewServer()

	var err error
	// 初始化数据库配置
	err = database.InitConfig()
	if err != nil {
		panic(err)
	}

	maxprocs.Set(maxprocs.Logger(log.Debugf))

	// 初始化配置文件
	_, err = config.Init()
	if err != nil {
		panic(err)
	}

	// 把es的index写入进去
	elasticsearch.CreateIndex()

	// 服务启动重置任务执行锁
	message.ResetSiteMessageTaskRedisLock()

	err = report.InitSocialReport()
	if err != nil {
		panic(err)
	}

	// 注册服务
	sitePB.RegisterDynamicsService(s, &dynamics.DynamicsImpl{})
	siteUserPB.RegisterUserService(s, &user.UserImpl{})
	activityPB.RegisterActivityService(s, &activity.ActImpl{})
	trpcKafka.RegisterBatchHandlerService(s.Service(constants.SiteMessageConsumer), handler.SiteMessageConsumer)
	trpcKafka.RegisterBatchHandlerService(s.Service(constants.MachieAuditCommentConsumer), handler.WaitingForReviewCommentMessageConsumer)
	trpcKafka.RegisterBatchHandlerService(s.Service(constants.MachieAuditPostConsumer), handler.MachineAuditPostConsumer)
	trpcKafka.RegisterBatchHandlerService(s.Service(constants.CreatorHubWorkReviewConsumer), handler.CreatorHubSubmissionConsumer)
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
