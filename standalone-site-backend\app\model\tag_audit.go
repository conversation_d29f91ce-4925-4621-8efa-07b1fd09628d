package model

type TagAudit struct {
	*Model
	Tag        *Tag   `json:"-"`
	TagID      int64  `json:"tag_id"`
	PostUUID   int64  `json:"post_uuid"`
	IntlOpenid string `json:"intl_openid"`
	RiskLevel  int8   `json:"risk_level"` // 风险等级 1:普通;2:高风险
	Status     int    `json:"status"`     // 审核状态 1:未处理;2:已发布;3:已忽略
	AuditOn    int64  `json:"audit_on"`   // 审核时间
	AreaId     string `json:"area_id"`
	GameId     string `json:"game_id"`
}

func (t *TagAudit) TableName() string {
	return "p_tag_audit"
}
