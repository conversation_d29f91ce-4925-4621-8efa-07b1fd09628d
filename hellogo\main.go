// Package main 是由 trpc-go-cmdline v2.8.30 生成的服务端示例代码
// 注意：本文件并非必须存在，而仅为示例，用户应按需进行修改使用，如不需要，可直接删去

package main

import (
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	trpc "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/pyongchen/hellogo/greeter"
	"git.woa.com/pyongchen/hellogo/logic"
	pb "git.woa.com/trpcprotocol/pytest/testdemo_hellogo"
	pb_logic "git.woa.com/trpcprotocol/pytest/testdemo_logic"
)

func main() {
	s := trpc.NewServer()

	impl := &greeter.GreeterImpl{}
	pb.RegisterGreeterService(s, impl)

	impl_logic := &logic.PostsImpl{}
	pb_logic.RegisterLogicService(s, impl_logic)

	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
