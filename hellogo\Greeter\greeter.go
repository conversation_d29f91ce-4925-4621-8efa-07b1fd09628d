package greeter

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"

	pb "git.woa.com/trpcprotocol/pytest/testdemo_hellogo"

	sd "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
)

type GreeterImpl struct {
	pb.UnimplementedGreeter
}

func (s *GreeterImpl) SayHello(ctx context.Context, req *pb.HelloRequest) (*pb.HelloReply, error) {
	var page1 sd.PageType = sd.PageType_NEXTPAGE     // 赋值为下一页
	page2 := sd.PageType_PREVIOUSPAGE                // 赋值为上一页
	msg, info, count := req.Msg, req.Info, req.Count // 获取请求参数（相当于 body 参数）
	log.Info(page1, page2)
	log.Info("Received request:", msg, info, count) // 打印日志

	tag_info := &sd.TagInfo{Name: "test_tag1", Id: 1001}
	rsp := &pb.HelloReply{Msg: "hello go", Num: 10, TagInfo: tag_info}
	return rsp, nil
}
