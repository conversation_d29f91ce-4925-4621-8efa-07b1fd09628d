package games

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetGames(c context.Context, language string, offset, limit int, needRefresh bool) ([]*model.GameFormated, error) {
	gamesRedisKey := cache.GetGamesInfoKey(language, offset)
	if !needRefresh {
		if gamesCacheInfo, err := redis.GetClient().Get(c, gamesRedisKey).Result(); err == nil {
			if gamesCacheInfo == "" {
				return nil, nil
			}
			var gamesFormated []*model.GameFormated
			err = json.Unmarshal([]byte(gamesCacheInfo), &gamesFormated)
			if err == nil {
				return gamesFormated, nil
			} else {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetGames json.Unmarshal err: %v", err)
			}
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetGames redis err: %v", err)
		}
	}

	// 热门标签
	conditions := &dao.GameConditions{
		Language: language,
	}
	games, err := dao.GameList(conditions, offset, limit)
	if err != nil {
		return nil, err
	}

	var gamesFormated []*model.GameFormated
	for _, game := range games {
		gameFormated := game.Format()
		gamesFormated = append(gamesFormated, gameFormated)
	}

	gamesCacheByte, err := json.Marshal(gamesFormated)
	if err == nil {
		redis.GetClient().SetEX(c, gamesRedisKey, string(gamesCacheByte), 2*time.Minute).Result()
	}

	return gamesFormated, nil
}

func GetAllGames(c context.Context, language string, needRefresh bool) ([]*model.GameFormated, error) {
	gamesRedisKey := cache.GetAllGamesInfoKey(language)
	if !needRefresh {
		if gamesCacheInfo, err := redis.GetClient().Get(c, gamesRedisKey).Result(); err == nil {
			if gamesCacheInfo == "" {
				return nil, nil
			}
			var gamesFormated []*model.GameFormated
			err = json.Unmarshal([]byte(gamesCacheInfo), &gamesFormated)
			if err == nil {
				return gamesFormated, nil
			} else {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetAllGames json.Unmarshal err: %v", err)
			}
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetAllGames redis err: %v", err)
		}
	}

	// 热门标签
	conditions := &dao.GameConditions{
		Language: language,
	}
	games, err := dao.GameList(conditions, 0, 0)
	if err != nil {
		return nil, err
	}

	var gamesFormated []*model.GameFormated
	for _, game := range games {
		gameFormated := game.Format()
		gamesFormated = append(gamesFormated, gameFormated)
	}

	gamesCacheByte, err := json.Marshal(gamesFormated)
	if err == nil {
		redis.GetClient().SetEX(c, gamesRedisKey, string(gamesCacheByte), 24*time.Hour).Result()
	}

	return gamesFormated, nil
}

func GetGameByGameId(c context.Context, gameId, areaId string, language string, needRefresh bool) (*model.GameFormated, error) {
	gameRedisKey := cache.GetGameInfoKey(language, gameId)
	if !needRefresh {
		if gameCacheInfo, err := redis.GetClient().Get(c, gameRedisKey).Result(); err == nil {
			if gameCacheInfo == "" {
				return nil, nil
			}
			gameFormated := &model.GameFormated{}
			err = json.Unmarshal([]byte(gameCacheInfo), gameFormated)
			if err == nil {
				return gameFormated, nil
			} else {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetGameByGameId json.Unmarshal err: %v", err)
			}
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetGameByGameId redis err: %v", err)
		}
	}

	gameObj, err := dao.GameGet(0, language, gameId)
	if err != nil {
		return nil, err
	}
	gameFormated := gameObj.Format()
	gameHotRedisKey := cache.GetGameHotKey(gameId, areaId)
	gameHotRedisV := redis.GetClient().Get(c, gameHotRedisKey)
	gameFormated.HotNum, _ = strconv.ParseInt(gameHotRedisV.Val(), 10, 64)

	gameCacheByte, err := json.Marshal(gameFormated)
	if err == nil {
		redis.GetClient().SetEX(c, gameRedisKey, string(gameCacheByte), 2*time.Minute).Result()
	}

	return gameFormated, nil
}

func SetPostsGameName(c context.Context, posts []*pb.GetPostRsp, language string) error {
	if len(posts) == 0 {
		return nil
	}
	gameInfos, err := GetAllGames(c, language, false)
	if err != nil {
		return err
	}

	for _, formatPost := range posts {
		for _, game := range gameInfos {
			if formatPost.GameId == game.GameId {
				formatPost.GameName = game.GameLanguageFormated.Name
			}
		}
	}
	return nil
}

func SetPostGameName(c context.Context, post *pb.GetPostRsp, language string) error {
	if post == nil {
		return nil
	}
	gameInfos, err := GetAllGames(c, language, false)
	if err != nil {
		return err
	}

	for _, game := range gameInfos {
		if post.GameId == game.GameId {
			post.GameName = game.GameLanguageFormated.Name
		}
	}

	return nil
}
