package exobrntmp

import (
	"context"

	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_exobrn_tmp"
	logic "trpc.act.logicial/app/logic/exobrn_tmp"
)

type ExobrnTmpImpl struct {
	pb.UnimplementedExobrnTmp
}

func (s *ExobrnTmpImpl) SendSevenDayPresent(ctx context.Context, req *pb.SendSevenDayPresentReq) (rsp *pb.SendSevenDayPresentRsp, err error) {
	rsp = &pb.SendSevenDayPresentRsp{}
	logic.SendSevenDayPresent(ctx)
	return
}
