package model

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/plugin/soft_delete"
)

// Model 公共Model
type Model struct {
	ID         int64                 `gorm:"primary_key" json:"id"`
	CreatedOn  int64                 `gorm:"column:created_on" json:"created_on"`
	ModifiedOn int64                 `gorm:"column:modified_on" json:"modified_on"`
	DeletedOn  int64                 `gorm:"column:deleted_on" json:"deleted_on"`
	IsDel      soft_delete.DeletedAt `gorm:"softDelete:flag;column:is_del" json:"is_del"`
}

type ConditionsT map[string]interface{}
type Predicates map[string][]interface{}

func (m *Model) BeforeCreate(tx *gorm.DB) (err error) {
	nowTime := time.Now()

	if currentTime, ok := tx.InstanceGet("now_time"); ok {
		nowTime = currentTime.(time.Time)
	}

	tx.Statement.SetColumn("created_on", nowTime.Unix())
	tx.Statement.SetColumn("modified_on", nowTime.Unix())
	return
}

func (m *Model) BeforeUpdate(tx *gorm.DB) (err error) {
	if !tx.Statement.Changed("modified_on") {
		tx.Statement.SetColumn("modified_on", time.Now().Unix())
	}

	return
}
