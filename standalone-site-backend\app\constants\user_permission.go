package constants

const (
	// 用户操作类型: 1：管理员，2：认证，3：白名单，4：禁言; 5: 降权
	UserActionType_Admin = iota + 1
	UserActionType_Auth
	UserActionType_White
	UserActionType_Mute
	UserActionType_Demotion
)

const (
	SetUserMuteAction = iota + 1
	CancelUserMuteAction
)
const (
	CancelUserDemotion = iota // 设置用户降权
	SetUserDemotion           // 取消用户降权
)

type MuteReason int

const (
	MuteReason_VulgarContent         MuteReason = 1  //发布垃圾内容（與妮姬無關的內容/重複性內容等）
	MuteReason_AbuseContent          MuteReason = 2  //破坏社区和谐沟通（人身攻擊/引戰/无端指责等）
	MuteReason_IllegalityContent     MuteReason = 3  //发布违法、暴力、色情等各种违法或恶意内容
	MuteReason_PublishDestroyContent MuteReason = 4  //发布BUG利用/游戏破解、修改等影响游戏平衡的内容
	MuteReason_PoseAsOfficalContent  MuteReason = 5  //冒充官方/管理员/其他用户
	MuteReason_NameOrRemarkContent   MuteReason = 6  //账号昵称或简介违规
	MuteReason_UntruthContent        MuteReason = 7  //发布解包内容或散步未经证实的更新资讯、虚假内容等
	MuteReason_TransferNumberContent MuteReason = 8  //发布账号转让\账号买卖\账号估价\代练等交易内容
	MuteReason_AdvertisementContent  MuteReason = 9  //进行广告行为
	MuteReason_violateRulesContent   MuteReason = 10 //违反社区原创/转载相关规定
)

type ESetAuthType int32

const (
	SetOfficialAuth    ESetAuthType = iota + 1 // 设置官方认证
	CancelOfficialAuth                         // 取消官方认证
	SetWriteAuth                               // 设置创作者认证
	CancelWriteAuth                            // 取消创作者认证
	SetAgencyAuth                              // 设置机构认证
	CancelAgencyAuth                           // 取消机构认证
	SetAdminAuth                               // 设置管理员
	CancelAdminAuth                            // 取消管理员
)

type EActionValue int

const (
	OfficialAuth EActionValue = iota + 1 // 官方认证
	WriteAuth                            // 创作者认证
	AgencyAuth                           // 机构认证
	PlayerAuth                           // 玩家认证
)
