package message

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"trpc.publishing_application.standalonesite/app/kafka"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/gofrs/uuid"
	"github.com/xuri/excelize/v2"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/pkg/storage"
)

// 服务重启时，重置站内信推送任务锁。因为服务重启的时候，多个pod滚动升级，老pod容器正在推送中的任务进程会被kill掉，需要重新拉起。
func ResetSiteMessageTaskRedisLock() {
	// 查询当前所有准备要推送的站内信任务
	siteMsgList, err := GetSiteMessageListByNotSent()
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SiteMessageTicker get site mesage list err:%v", err)
	}
	if len(siteMsgList) == 0 {
		return
	}
	for _, item := range siteMsgList {
		redisKey := cache.GetSiteMessageTimerCacheKey(item.ID)
		// 判断之前是否已经生成了任务执行锁，如果有则重新设置过期时间，因为会有多个pod滚动，所以任务锁设置成10分钟后过期可重新获取
		// 10分钟是个理论值，pod容器滚动一个要2分钟，现网一共4个pod
		ttlVal, cErr := redis.GetClient().TTL(context.Background(), redisKey).Result()
		if cErr != nil {
			// 获取redis锁失败了
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("ResetSiteMessageTaskRedisLock get redis lock failed, err:%v, redis key: %s, site msg data: %v", cErr, redisKey, item)
			continue
		}
		if ttlVal > 600 {
			redis.GetClient().Expire(context.Background(), redisKey, 600*time.Second)
		}
	}
}

// SiteMessageTicker定时触发遍历站内信任务，生成站内信数据到kafka
func SiteMessageTicker() {
	defer recovery.CatchGoroutinePanic(context.Background())
	// 是否启动任务
	if config.GetConfig().SiteMessageSetting.RunTask == 0 {
		return
	}
	ctx := context.Background()

	siteMsgHandler(ctx)
}

// 处理站内信
func siteMsgHandler(c context.Context) {
	siteMsgList, err := GetSiteMessageListByNotSent()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SiteMessageTicker get site mesage list err:%v", err)
	}
	// 没有数据不执行剩下的逻辑
	if len(siteMsgList) == 0 {
		return
	}
	for _, item := range siteMsgList {
		redisKey := cache.GetSiteMessageTimerCacheKey(item.ID)
		// 加上redis锁，方式多个pod同步获取到数据进行投递
		result, cErr := redis.GetClient().SetNX(context.Background(), redisKey, "1", time.Hour*4).Result()
		if cErr != nil {
			// 获取redis锁失败了
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("get redis lock failed, err:%v, redis key: %s, site msg data: %v", cErr, redisKey, item)
			continue
		}
		if !result {
			//已经存在了，那就说明这个是被人抢到了执行权
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf(fmt.Sprintf("already occupiedm, key: %s, site msg data: %v", redisKey, item))
			continue
		}
		// 是否开启协程处理？
		go func(data *model.SiteMessage) {
			defer recovery.CatchGoroutinePanic(context.Background())
			err := siteMessageFlow(data)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Site message flow handler err: %v, siteMsgData: %v", err, data)
			}
		}(item)

	}
}

// 获取站内信未完成的列表
func GetSiteMessageListByNotSent() ([]*model.SiteMessage, error) {
	sent, err := dao.SiteMessageList(constants.SiteMessageSent, time.Now().Unix(), "30054", 0, 0)
	if err != nil {
		return nil, err
	}
	return sent, nil
}

// 站内信处理流程
func siteMessageFlow(siteMsg *model.SiteMessage) error {
	var traceId string
	u2, uuidErr := uuid.NewV4()
	ctx := context.Background()
	traceId = fmt.Sprintf("temp-trace-id-%d", time.Now().Unix())
	if uuidErr != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("siteMessageFlow create uuid error: %v", uuidErr)
		traceId = fmt.Sprintf("temp-trace-id-%d", time.Now().Unix())
	} else {
		traceId = u2.String()
	}

	log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("siteMessageFlow start at: %d,traceId:%s, siteMsg:%v", time.Now().Unix(), traceId, siteMsg)

	defer func() {
		// 删除redis锁
		redisKey := cache.GetSiteMessageTimerCacheKey(siteMsg.ID)
		_, err := redis.GetClient().Del(ctx, redisKey).Result()
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("Release error, lockkey:(%s), err: %v", redisKey, err)
		}
	}()
	// 先改成推送中的状态
	uErr := dao.SiteMessageUpdateStatus(constants.SiteMessageSending, siteMsg.ID)
	if uErr != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("SiteMessage SiteMessageEditStatus err: %v, status: %d, id: %d", uErr, constants.SiteMessageSent, siteMsg.ID)
		return errs.NewCustomError(ctx, code.SiteMessageStatusFailed, "siteMessageFlow | Site message update status failed")
	}
	var err error

	var regionalScopeList []*model.RegionalScope
	err = json.Unmarshal([]byte(siteMsg.RegionalScope), &regionalScopeList)
	if err != nil {
		// 反序列化失败了，记录
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("site message regionalScope to unmarshal error: %v， message: %v", err, siteMsg)
		return err
	}
	for _, regionalScope := range regionalScopeList {
		redisKey := cache.GetSiteMessageUserNumberDataKey(siteMsg.ID, regionalScope.IntlGameId)
		switch constants.MSGPUSHTYPE(regionalScope.PushScope) {
		case constants.ALLUSERPUSH:
			// 全量用户推送
			err = allUserHandler(siteMsg, regionalScope.IntlGameId, redisKey)
			break
		case constants.NUMBERPACKAGEPUSH:
			// 号码包用户推送
			err = packageHandler(siteMsg, traceId, regionalScope.IntlGameId, regionalScope.NumberPackageFileUrl, redisKey)
			break
		case constants.NUMBERPACKAGEJOINCODEPUSH:
			err = readAttachmentCode(ctx, regionalScope.NumberPackageFileUrl, siteMsg.GameID, siteMsg.AreaID, traceId, redisKey, regionalScope.IntlGameId, siteMsg.CreatedData)
			break
		default:
			return errs.NewCustomError(ctx, code.PushTypeNotExists, "siteMessageFlow | push type is not exist")
		}
		if err != nil {
			return err
		}
	}
	// 全程写入集合没有问题, 更新完成状态
	cErr := dao.SiteMessageUpdateCreateDataStatus(constants.CreateDataStatusComplete, siteMsg.ID)
	if cErr != nil {
		return errs.NewCustomError(ctx, code.SiteMsgCreatedDataStatusFailed, "siteMessageFlow | Site message created user data failed")
	}
	// 启动消费把数据往kafka中写入
	for _, regionalScope := range regionalScopeList {
		redisKey := cache.GetSiteMessageUserNumberDataKey(siteMsg.ID, regionalScope.IntlGameId)
		csErr := cacheHandlerSiteMsg(siteMsg.ID, redisKey, regionalScope.PushScope)
		if csErr != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("SiteMessage cacheHandlerSiteMsgNew err: %v,  id: %d", err, siteMsg.ID)
			return csErr
		}
	}
	// 把当前的状态给设置为已完成
	uErr = dao.SiteMessageUpdateStatus(constants.SiteMessageSent, siteMsg.ID)
	if uErr != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("SiteMessage SiteMessageEditStatus err: %v, status: %d, id: %d", uErr, constants.SiteMessageSent, siteMsg.ID)
		return errs.NewCustomError(ctx, code.SiteMessageStatusFailed, "siteMessageFlow | Site message update status failed")
	}

	log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("siteMessageFlow end at: %d,traceId:%s, siteMsg:%v", time.Now().Unix(), traceId, siteMsg)
	return nil
}

// 全量用户推送处理， 先读取用户数量在分页，后期用户表拆分之后读取用户还得适配整改
// 把用户数据塞到redis 集合中
func allUserHandler(siteMsg *model.SiteMessage, intlGameid, redisKey string) error {
	if siteMsg.CreatedData == int(constants.CreateDataStatusComplete) {
		// 用户号码包已经导入的状态
		return nil
	}
	redis.GetClient().Expire(context.Background(), redisKey, 24*7*time.Hour) // 设置这个key一周之后过期
	var limit = 1000
	var lastUserId = 0
	for {
		users, err := dao.GetAllUserIteratively(lastUserId, limit, intlGameid)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("allUserHandlerNew GetAllUserIteratively err: %v, lastUserId: %d, limit: %d", err, lastUserId, limit)
			return errs.NewCustomError(context.Background(), code.GetAllUserListFailed, "Failed to get the site message user info")
		}
		if len(users) > 0 {
			var userIntlOpenids []interface{}
			for _, userItem := range users {
				userIntlOpenids = append(userIntlOpenids, userItem.IntlOpenid)
			}
			redis.GetClient().SAdd(context.Background(), redisKey, userIntlOpenids...)
			lastUserId = int(users[len(users)-1].ID)
		} else {
			break
		}
	}

	return nil

}

// 号码包类型处理
func packageHandler(siteMsg *model.SiteMessage, traceId, intlGameid, numberPackageFileUrl string, redisKey string) error {
	if siteMsg.CreatedData == int(constants.CreateDataStatusComplete) {
		// 用户号码包已经导入的状态
		return nil
	}
	if numberPackageFileUrl == "" {
		// 号码包列表为空的话退出
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s, %s", traceId, "packageHandlerNew is file url empty")
		return nil
	}
	err, file := downloadFile(siteMsg.GameID, siteMsg.AreaID, numberPackageFileUrl, traceId)
	if err != nil {
		return err
	}
	// 读取文件结束关闭
	defer file.Close()

	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("%s, Downloading number packages success, task: %v", traceId, siteMsg)
	scanner := bufio.NewScanner(file)
	redis.GetClient().Expire(context.Background(), redisKey, 24*7*time.Hour) // 设置这个key一周之后过期
	// 逐行写入redis中，使用redis的集合特性去重
	var openidMap = make(map[string]struct{})
	var openidList []interface{}
	for scanner.Scan() {
		if scanner.Text() == "" {
			continue
		}
		openid := scanner.Text()
		openid = fmt.Sprintf("%s-%s", intlGameid, openid)
		if _, ok := openidMap[openid]; !ok {
			openidMap[openid] = struct{}{}
			openidList = append(openidList, openid)
		}
		// 分成一批100条数据，批量写入redis
		if len(openidMap) >= 100 {
			cErr := redis.GetClient().SAdd(context.Background(), redisKey, openidList...).Err()
			openidMap = make(map[string]struct{})
			openidList = openidList[:0]
			if cErr != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,packageHandlerNew  err: %v", traceId, cErr)
			}
		}
	}
	// 最后一批数据
	cErr := redis.GetClient().SAdd(context.Background(), redisKey, openidList...).Err()
	if cErr != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s, packageHandlerNew  err: %v", traceId, cErr)
	}

	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("%s, Parsing number package successfully, task: %v", traceId, siteMsg)
	if sErr := scanner.Err(); sErr != nil {
		return errs.NewCustomError(context.Background(), code.ReadSiteMessageNumberPackageFailed, "packageHandlerNew | Read site message number package failed")
	}
	return nil
}

// 从redis中读取数据并写入kafka
func cacheHandlerSiteMsg(cmsMsgID int64, redisKey string, pushType int) error {
	var total int64 = 100
	var sumTotal int64 = 0
	var siteMsgData = &model.SiteMessageKafkaData{
		MsgID:               cmsMsgID,
		IntlOpenidList:      make([]string, 0),
		PushType:            pushType,
		IntlOpenidJoinCdkey: make([]*model.UserJoinCdkey, 0),
	}
	for {
		if pushType == int(constants.NUMBERPACKAGEJOINCODEPUSH) {
			result, err := redis.GetClient().LPop(context.Background(), redisKey).Result()
			if err != nil {
				if errors.Is(err, redis2.Nil) {
					// 没有读取到数据
					break
				}
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("cacheHandlerSiteMsg to get cdkey user list err: %v", err)
				continue
			}
			// 没有获取到数据
			if len(result) == 0 {
				break
			}
			userCdkey := strings.Split(result, "|||")
			if len(userCdkey) == 2 {
				siteMsgData.IntlOpenidJoinCdkey = append(siteMsgData.IntlOpenidJoinCdkey, &model.UserJoinCdkey{
					IntlOpenid: userCdkey[0],
					Cdkey:      userCdkey[1],
				})
			}
			sumTotal++
		} else {
			resultData := redis.GetClient().SPopN(context.Background(), redisKey, total)
			userOpenids, err := resultData.Result()
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("cacheHandlerSiteMsg to read list err: %v", err)
				return errs.NewCustomError(context.Background(), code.SiteMsgGetCacheFailed, "cacheHandlerSiteMsgNew | Site message get cache failed")
			}
			// 如果是没有数据那就退出循环
			if len(userOpenids) == 0 {
				break
			}
			siteMsgData.IntlOpenidList = userOpenids
			sumTotal += total
			// 如果是读取出来的用户数量小于定义的数量那就是读取完了
			if len(userOpenids) < int(total) {
				break
			}

		}
		// 如果是附件码的推送满足了100条或者是用户id已经满足了100条就触发推送
		if len(siteMsgData.IntlOpenidJoinCdkey) >= 100 || len(siteMsgData.IntlOpenidList) >= 100 {
			bErr := batchToKafka(siteMsgData)
			if bErr != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("batchSaveMessage err: %v, pushData: %+v, cmsMsgID: %v, ", bErr, siteMsgData, cmsMsgID)
				return bErr
			}
			siteMsgData.IntlOpenidJoinCdkey = make([]*model.UserJoinCdkey, 0)
			siteMsgData.IntlOpenidList = make([]string, 0)
		}

	}
	// 判断是否含有数据，兜底
	if len(siteMsgData.IntlOpenidJoinCdkey) > 0 || len(siteMsgData.IntlOpenidList) > 0 {
		bErr := batchToKafka(siteMsgData)
		if bErr != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("batchSaveMessage err: %v, pushData: %+v, cmsMsgID: %v, ", bErr, siteMsgData, cmsMsgID)
			return bErr
		}
		siteMsgData.IntlOpenidJoinCdkey = make([]*model.UserJoinCdkey, 0)
		siteMsgData.IntlOpenidList = make([]string, 0)
	}

	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("cacheHandlerSiteMsg sync total: %d", sumTotal)
	return nil
}

// 批量写入kafka中， users 用户数据， msgId 站内行配置的id
func batchToKafka(siteMsgItem *model.SiteMessageKafkaData) error {
	// push到kafka中
	marshal, err := json.Marshal(siteMsgItem)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("batch write queue json code failed: %v, message: %v", err, siteMsgItem)
		return errs.NewCustomError(context.Background(), code.SiteMsgJsonCodeFailed, "batchToKafka | Site message json code failed")
	}
	key := fmt.Sprintf("site-msg-%d", time.Microsecond)
	err = kafka.Produce(context.Background(), constants.SiteMessageProducer, "", string(marshal))
	if err != nil {
		// 写入kafka失败
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("batch write queue failed: %v, key: %s,message: %v", err, key, siteMsgItem)
		return errs.NewCustomError(context.Background(), code.SiteMsgWriteToQueueFailed, "batchToKafka | Site message write to queue failed")
	}
	return nil
}

// SiteMessageConsume 消费kafka数据
func SiteMessageConsume(c context.Context, value string) error {
	// 是否启动任务
	if config.GetConfig().SiteMessageSetting.RunTask == 0 {
		return nil
	}
	var data model.SiteMessageKafkaData
	err := json.Unmarshal([]byte(value), &data)
	if err != nil {
		// 反序列化失败了，记录
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("site message consume to unmarshal failed: %v， message: %v", err, value)
		return err
	}
	scErr := SiteMessageConsumeHandler(c, &data)
	if scErr != nil {
		// 消息写入失败了，记录
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("site message consume to handler failed: %v, message: %v", err, value)
		return scErr
	}

	return nil
}

// 站内信消费逻辑
func SiteMessageConsumeHandler(c context.Context, data *model.SiteMessageKafkaData) error {
	if data.MsgID == 0 {
		// 没有站内信配置的id那认为这条消息不能被消费
		return nil
	}
	redisKey := cache.GetSiteMessageInfosKey(data.MsgID)
	msgData := redis.GetClient().Get(context.Background(), redisKey)
	var siteMsgData *model.SiteMessageWithLang
	if msg, cErr := msgData.Result(); cErr == nil {
		jErr := json.Unmarshal([]byte(msg), &siteMsgData)
		if jErr != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SiteMessageConsume to json unmarshal err: %v, msgData: %s", jErr, msg)
			return errs.NewCustomError(c, code.SiteMsgJsonDecodeFailed, "SiteMessageConsumeHandler | Site message json decode failed")
		}
	} else {
		siteMsg, err := dao.SiteMessageFirstWithLang(data.MsgID)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("Get site message data err: %v", err)
			return errs.NewCustomError(c, code.GetSiteMessageInfoFailed, "SiteMessageConsumeHandler | Get site message failed")
		}
		siteMsgData = siteMsg
		marshal, err := json.Marshal(siteMsgData)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SiteMessageConsume to json marshal err: %v, sitemsg data: %v", err, siteMsgData)
			return errs.NewCustomError(c, code.SiteMsgJsonCodeFailed, "SiteMessageConsumeHandler | Site message json code failed")
		}
		redis.GetClient().SetEX(context.Background(), redisKey, marshal, 4*time.Hour)
	}
	extInfo, _ := getExtInfoByMsgType(c, siteMsgData)
	userOpenids := make([]string, 0)
	var userJoinCdkey = make(map[string][]string)
	// 判断消息推送类型
	if data.PushType == int(constants.NUMBERPACKAGEJOINCODEPUSH) {
		for _, item := range data.IntlOpenidJoinCdkey {
			userOpenids = append(userOpenids, item.IntlOpenid)
			userJoinCdkey[item.IntlOpenid] = append(userJoinCdkey[item.IntlOpenid], item.Cdkey)
		}
	} else {
		userOpenids = data.IntlOpenidList
	}

	userMessage := make([]*model.Message, 0, len(userOpenids))
	userInfos, err := dao.GetUserMsgActivityNotifySwitch(c, userOpenids)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SiteMessageConsume get user info err:%v, user_intlopenids:%v", err, userOpenids)
		return errs.NewCustomError(c, code.GetUserInfoError, "SiteMessageConsumeHandler | Failed to get the user information, please check")
	}
	if len(userOpenids) != len(userInfos) {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SiteMessageConsume get user info len not equal, user_intlopenids len(%d):%v, user_infos len(%d):%v", len(userOpenids), userOpenids, len(userInfos), userInfos)
	}
	intlOpenids := make([]string, 0)
	// 拼接用户信息
	for _, item := range userInfos {
		userMessageRow := &model.Message{
			Type:                   constants.MsgTypeSiteMessage,
			GameID:                 siteMsgData.GameID,
			AreaID:                 siteMsgData.AreaID,
			CmsMsgID:               siteMsgData.ID,
			Brief:                  siteMsgData.Title,
			SenderUserIntlOpenid:   siteMsgData.GameID,
			ReceiverUserIntlOpenid: item.IntlOpenid,
		}
		if data.PushType == int(constants.NUMBERPACKAGEJOINCODEPUSH) {
			if cdkeyList, ok := userJoinCdkey[item.IntlOpenid]; ok {
				if extInfo == nil {
					extInfo = &model.MsgExtInfo{}
				}
				for _, cdkey := range cdkeyList {
					extInfo.CdKey = cdkey
					tempUserMessage := *userMessageRow
					setUserMessage(c, extInfo, &tempUserMessage, item.IntlOpenid, int(item.MsgActivityNotify))
					userMessage = append(userMessage, &tempUserMessage)
				}
			}
		} else {
			setUserMessage(c, extInfo, userMessageRow, item.IntlOpenid, int(item.MsgActivityNotify))
			userMessage = append(userMessage, userMessageRow)
		}

		intlOpenids = append(intlOpenids, item.IntlOpenid)
	}
	bErr := dao.BatchSaveMessage(userMessage)
	if bErr != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("batchSaveMessage CreateBatchMessage err: %v, messageList: %v", bErr, userMessage)
		return errs.NewCustomError(c, code.CreateSiteMessageFailed, "SiteMessageConsumeHandler | Batch set user message failed")
	}
	err = handleMsgByType(c, siteMsgData, intlOpenids)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SiteMessageConsumeHandler handleMsgByType err: %v", err)
		return err
	}
	// if siteMsgData.Type == constants.SiteMsgTypeAvatarPendant {
	// 	err := user.AddAvatarPendantToUser(intlOpenids, siteMsgData.AvatarPendantID)
	// 	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SiteMessageConsumeHandler AddAvatarPendantToUser err: %v", err)
	// 	return err
	// }
	if len(intlOpenids) > 0 {
		go func(openidList []string) {
			for _, openid := range openidList {
				writemessage.DeleteUserMessageCache(context.Background(), openid, constants.SiteMessageType)
			}
			// 把值设置进redis,等待redis读取
			redis.GetClient().IncrBy(context.Background(), cache.GetSiteMessageTotalSendKey(data.MsgID), int64(len(intlOpenids)))
		}(intlOpenids)
	}
	return nil
}

// 根据消息类型，获取扩展信息
func getExtInfoByMsgType(c context.Context, siteMsgData *model.SiteMessageWithLang) (extInfo *model.MsgExtInfo, err error) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("getExtInfoByMsgType siteMsgData type: %d, AvatarPendantID: %d, CommentBubbleID: %d", siteMsgData.Type, siteMsgData.AvatarPendantID, siteMsgData.CommentBubbleID)
	if siteMsgData.Href != "" {
		extInfo = &model.MsgExtInfo{Href: siteMsgData.Href}
		// extInfo.Href = siteMsgData.Href
	}
	if siteMsgData.Type == constants.SiteMsgTypeAvatarPendant && siteMsgData.AvatarPendantID != 0 {
		if extInfo == nil {
			extInfo = &model.MsgExtInfo{}
		}
		extInfo.SiteMsgSubType = constants.SiteMsgTypeAvatarPendant
		extInfo.AvatarPendantId = siteMsgData.AvatarPendantID
		// 挂件url
		avatarPendant, err := dao.GetAvatarPendantInfo(siteMsgData.AvatarPendantID)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("get avatar pendant info faied: %v", err)
		}
		extInfo.AvatarPendantUrl = avatarPendant.Icon
	} else if siteMsgData.Type == constants.SiteMsgTypeCommentBubble && siteMsgData.CommentBubbleID != 0 {
		if extInfo == nil {
			extInfo = &model.MsgExtInfo{}
		}
		extInfo.CommentBubbleId = siteMsgData.CommentBubbleID
		extInfo.SiteMsgSubType = constants.SiteMsgTypeCommentBubble
	}
	return extInfo, err
}

// 针对不同类型的消息做不同的处理
func handleMsgByType(c context.Context, siteMsgData *model.SiteMessageWithLang, intlOpenids []string) (err error) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("handleMsgByType siteMsgData type: %d, AvatarPendantID: %d, CommentBubbleID: %d", siteMsgData.Type, siteMsgData.AvatarPendantID, siteMsgData.CommentBubbleID)
	if siteMsgData.Type == constants.SiteMsgTypeAvatarPendant {
		err := user.AddAvatarPendantToUser(intlOpenids, siteMsgData.AvatarPendantID)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("handleMsgByType AddAvatarPendantToUser err: %v", err)
			return err
		}
		return nil
	} else if siteMsgData.Type == constants.SiteMsgTypeCommentBubble {
		// 气泡类型
		err := user.AddCommentBubbleToUser(intlOpenids, siteMsgData.CommentBubbleID)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("handleMsgByType AddCommentBubbleToUser err: %v", err)
			return err
		}
		return nil
	}
	return nil
}

func setUserMessage(c context.Context, extInfo *model.MsgExtInfo, userMessageRow *model.Message, intlOpenid string, msgActivityNotify int) {
	var exInfo string
	if extInfo != nil {
		marshal, mErr := json.Marshal(extInfo)
		if mErr != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("batchSaveMessage extInfo json.Marshal err: %v", mErr)
			//return errs.NewCustomError(c, code.AddMsgInvalidExtInfo, "SiteMessageConsumeHandler | Failed to create message, exception in parsing extended field.")
		}
		exInfo = string(marshal)
	}
	if exInfo != "" {
		userMessageRow.ExtInfo = exInfo
	}

	if msgActivityNotify == 0 {
		userMessageRow.IsRead = 1
	}
	// 如果是消息未读的状态就加1，这样的话适配用户自定义开关
	if userMessageRow.IsRead == 0 {
		redis.GetClient().HIncrBy(context.Background(), cache.GetMessageUnReadKey(intlOpenid), constants.SiteMessageCount, 1)
	}
}

// 读取附件码
func readAttachmentCode(c context.Context, fileUrl string, gameId, areaId, traceId string, redisKey string, intlGameId string, isCreatedData int) error {
	if isCreatedData == int(constants.CreateDataStatusComplete) {
		// 用户号码包已经导入的状态
		return nil
	}
	if fileUrl == "" {
		return nil
	}
	err, file := downloadFile(gameId, areaId, fileUrl, traceId)
	if err != nil {
		return err
	}
	// 读取文件结束关闭
	defer file.Close()

	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("%s, Downloading number packages success", traceId)

	fileData, err := excelize.OpenReader(file)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("%s, read file head line failed, file: %s, err: %v", traceId, fileUrl, err)
		return errs.NewCustomError(c, code.ReadSiteMessageHeadCodeFailed, "read file head line failed")
	}
	defer func() {
		if err := fileData.Close(); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("%s, read file failed by close, file: %s, err: %v", traceId, fileUrl, err)
		}
	}()
	// 删除redis的数据,防止重复数据
	err = redis.GetClient().Del(context.Background(), redisKey).Err()
	if err != nil {
		return errs.NewCustomError(c, code.ReadSiteMessageCacheFailed, "del cache failed")
	}
	// 分批读取每个单元格
	list := fileData.GetSheetList()
	for _, sheet := range list {
		rows, err := fileData.Rows(sheet)
		if err != nil {
			// 读取工作表单错误
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("%s, read file failed to work area, file: %s, err: %v", traceId, fileUrl, err)
			return errs.NewCustomError(c, code.ReadSiteMessageHeadCodeByWorkArea, "read file failed to work area")
		}
		var cur int
		var cdkeyList = make([]interface{}, 0)
		for rows.Next() {
			cur++
			if cur == 1 {
				// 减去第一行的数据
				continue
			}
			row, err := rows.Columns()
			// 如果是报错了或者是读到的这一行内容为空就退出
			if err != nil || len(row) == 0 {
				break
			}
			var intlOpenid, cdKey string
			for i, colCell := range row {
				if i > 1 {
					// 只需要读取两列，其他的不用读取
					break
				}
				if colCell == "" {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s , %d 行 %d 列数据为空", traceId, cur, i)
					break
				}
				// 第一列必须是openid
				if i%2 == 0 {
					intlOpenid = fmt.Sprintf("%s-%s", intlGameId, colCell)
					continue
				}
				cdKey = colCell
			}

			cacheValue := fmt.Sprintf("%s|||%s", intlOpenid, cdKey)
			cdkeyList = append(cdkeyList, cacheValue)
			// 判断是否达到100条，是的话放入redis， 然后重置
			if len(cdkeyList) >= 100 {
				cErr := redis.GetClient().RPush(context.Background(), redisKey, cdkeyList...).Err()
				if cErr != nil {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,set user to cache queue err: %v", traceId, cErr)
				}
				cdkeyList = make([]interface{}, 0)

			}

		}
		if len(cdkeyList) > 0 {
			cErr := redis.GetClient().RPush(context.Background(), redisKey, cdkeyList...).Err()
			if cErr != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,set user to cache queue err: %v", traceId, cErr)
			}
			cdkeyList = make([]interface{}, 0)
		}
		rows.Close()
	}
	redis.GetClient().Expire(context.Background(), redisKey, 24*7*time.Hour) // 设置这个key一周之后过期

	return nil
}

// 下载文件
func downloadFile(gameId, areaId string, fileUrl string, traceId string) (error, *os.File) {
	var cosObjectStorage storage.ObjectStorageService
	var fileData *os.File
	cosObjectStorage, err := storage.NewCOSServiceByGameid(gameId, areaId)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s, packageHandlerNew NewCOSServiceByGameid err: %v", traceId, err)
		return errs.NewCustomError(context.Background(), code.FileDownloadFailed, "packageHandlerNew | Download file failed"), fileData
	}
	err, fileData = cosObjectStorage.DownloadFile(fileUrl)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s, packageHandlerNew downloadObject err: %v ", traceId, err)
		return errs.NewCustomError(context.Background(), code.FileDownloadFailed, "packageHandlerNew | Download file failed"), fileData
	}
	return nil, fileData
}
