package model

type CertificationUserLanguage struct {
	*Model
	Language   string `json:"language"` // 多语言
	Content    string `json:"content"`
	Creator    string `json:"creator"`
	Updater    string `json:"updater"`
	GameId     string `json:"game_id"`
	AreaId     string `json:"area_id"`
	IntlOpenid string `json:"intl_openid"`
	Type       int32  `json:"type"` // 1：昵称，2：个签
}

func (p *CertificationUserLanguage) TableName() string {
	return "p_certification_user_language"
}
