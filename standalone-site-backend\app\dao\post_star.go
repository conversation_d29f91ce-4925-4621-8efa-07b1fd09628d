package dao

import (
	"errors"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

type PostStarConditions struct {
	IntlOpenid string
	PostUuids  []string
}

func PostStarGet(postUUID, intlOpenID string, isNeedDel bool) (*model.PostStar, error) {
	var star model.PostStar

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostStar{}).TableName())

	if postUUID != "" {
		db = db.Where("post_uuid = ?", postUUID)
	}
	if intlOpenID != "" {
		db = db.Where("intl_openid =  ?", intlOpenID)
	}
	var err error
	if isNeedDel {
		err = db.First(&star).Error
	} else {
		err = db.Unscoped().First(&star).Error
	}
	if err != nil {
		return nil, err
	}

	// post, err := GetPost(star.PostUUID)
	// if err != nil {
	// 	if errors.Is(err, gorm.ErrRecordNotFound) {
	// 		return &star, nil
	// 	}
	// 	return nil, err
	// }

	// star.Post = post
	return &star, nil
}

func PostStarCreate(star *model.PostStar) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStar{}).TableName()).Omit("Post").Create(&star).Error
}

func PostStarDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStar{}).TableName()).Omit("Post").Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func PostStarRecordRestar(id int64, starType int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostStar{}).TableName()).Omit("Post").Where("id = ?", id).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"star_type":   starType,
		"is_del":      0,
	}).Error
}

func PostStarList(conditions *PostStarConditions, offset, limit int) ([]*model.PostStar, error) {
	var stars []*model.PostStar
	var err error

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostStar{}).TableName())

	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	if len(conditions.PostUuids) > 0 {
		db = db.Where("post_uuid in ?", conditions.PostUuids)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}

	err = db.Where("is_del = 0").Order("id desc").Find(&stars).Error
	if err != nil {
		return nil, err
	}
	postUUIDs := make([]string, 0, len(stars))

	for _, star := range stars {
		postUUIDs = append(postUUIDs, star.PostUUID)
	}

	if len(postUUIDs) > 0 {
		list, err := GetPostList(&PostConditions{
			PostUuids: postUUIDs,
		}, 0, 0)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return stars, nil
			}
			return nil, err
		}
		for i, star := range stars {
			for _, post := range list {
				if star.PostUUID == post.PostUUID {
					stars[i].Post = post
				}
			}
		}
	}
	return stars, nil
}

func PostStarListV2(conditions *PostStarConditions, id, limit int) ([]*model.PostStar, error) {
	var stars []*model.PostStar
	var err error

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostStar{}).TableName())
	err = db.Where("id > ?", id).Limit(limit).Order("id asc").Find(&stars).Error
	if err != nil {
		return nil, err
	}
	return stars, nil
}
