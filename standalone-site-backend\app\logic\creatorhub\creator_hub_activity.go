package creatorhub

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/spf13/cast"
	"gorm.io/gorm"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

type TaskItem struct {
	EndTime             int64             `json:"end_time"`
	ImageUrl            string            `json:"image_url"`
	LipGameId           string            `json:"lip_gameid"`
	Name                string            `json:"name"`
	PublishTime         int64             `json:"publish_time"`
	StartTime           int64             `json:"start_time"`
	TaskId              int64             `json:"task_id"`
	TaskIntro           string            `json:"task_intro"`
	TaskModelType       int32             `json:"task_model_type"`
	TaskStatus          int32             `json:"task_status"`
	TaskType            int32             `json:"task_type"`
	RankRewardRuleList  []RankRewardRule  `json:"rank_reward_rule_list"`
	TaskLangPackageList []TaskLangPackage `json:"task_lang_package_list"`
	ThumbnailUrl        string            `json:"thumbnail_url"` // 任务缩略图
	TaskUrl             string            `json:"task_url"`
}

type RankRewardRule struct {
	RankKey  string `json:"rank_key"`
	RankName string `json:"rank_name"`
}

type TaskLangPackage struct {
	Lang       string    `json:"lang"`
	KeyValList []*KeyVal `json:"key_val_list"`
}

type KeyVal struct {
	Key string `json:"Key"`
	Val string `json:"Val"`
}

type TaskData struct {
	TaskList []TaskItem `json:"task_list"`
}

type TaskRes struct {
	Ret  int32    `json:"ret"`
	Msg  string   `json:"msg"`
	Data TaskData `json:"data"`
}

// 任务状态0-已发布1-等待发布2-已经取消3-已经结束4-等待审批'
type TaskStatus int

const (
	PUBLISHED TaskStatus = iota
	WAITING_TO_PUBLISH
	CANCELLED
	FINISHED
	WAITING_FOR_APPROVAL
)

func SyncCreatorHubActivity(gameId, areaId string) {
	c := context.Background()
	// 防止panic
	defer recovery.CatchGoroutinePanic(c)
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncCreatorHubActivity ticker at %v", time.Now())
	syncCreatorHubActivityCacheKey := cache.GetSyncCreatorHubActivityTaskKey()
	if ok, _ := redis.GetClient().SetNX(c, syncCreatorHubActivityCacheKey, "1", config.GetConfig().CreatorHubSetting.SyncCreatorHubActivityTimeoutDuration*time.Second).Result(); ok {
		// 完成之后删除锁占用
		defer redis.GetClient().Del(context.Background(), syncCreatorHubActivityCacheKey)
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncCreatorHubActivity start at %v", time.Now())
		taskUrl := config.GetConfig().CreatorHubSetting.GetTaskPath
		limit := 10
		offset := 0
		syncActivityIds := make([]int64, 0)
		// 获取全量的数据
		activityIds, err := dao.GetAllActivityID()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubActivity | get db all activity id failed, err: %v", err)
			return
		}
		for {
			postData := map[string]interface{}{
				"gameid": gameId,
				"offset": offset * limit,
				"limit":  limit,
			}
			postJson, _ := json.Marshal(postData)

			resultOption, err := RequestCreateHub(c, gameId, areaId, taskUrl, postJson)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubActivity | request failed, request data: %s, err: %v", string(postJson), err)
				return
			}
			var res TaskRes
			err = json.Unmarshal([]byte(resultOption), &res)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubActivity | json decode failed, result data: %s,err: %v", resultOption, err)
				return
			}
			if res.Ret != 0 {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubActivity | api return failed, result data: %s,err: %v", resultOption, err)
				return
			}
			if len(res.Data.TaskList) == 0 {
				break
			}
			for _, item := range res.Data.TaskList {
				syncActivityIds = append(syncActivityIds, item.TaskId)
			}
			err = getCreatorHubActivity(res.Data.TaskList, gameId)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("getCreatorHubActivity1 err: %v", err)
				return
			}
			if len(res.Data.TaskList) < limit {
				break
			}
			offset++
		}
		// 删除列表的缓存
		go func() {
			defer recovery.CatchGoroutinePanic(context.Background())
			cacheKey := cache.GetCreatorHubActivityListKey()
			redis.GetClient().Del(c, cacheKey)
		}()

		// 处理没有同步过来的活动
		unSyncTaskId := make([]int64, 0)
		for _, id := range activityIds {
			isExist := false
			for _, activityId := range syncActivityIds {
				if id == activityId {
					isExist = true
					break
				}
			}
			if !isExist {
				unSyncTaskId = append(unSyncTaskId, id)
			}
		}
		if len(unSyncTaskId) > 0 {
			// 删除这些未同步过来的数据
			err = dao.DeleteActivityByTaskId(unSyncTaskId)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubActivity | DeleteActivityByTaskId failed, taskId data: %v,err: %v", unSyncTaskId, err)
				return
			}

			// 删除赛道
			list, err := dao.GetActivityWithRankList(unSyncTaskId)
			if err == nil && len(list) > 0 {
				var activityRankId []int64
				for _, ranks := range list {
					for _, rank := range ranks {
						activityRankId = append(activityRankId, rank.ID)
					}
				}
				if len(activityRankId) > 0 {
					err = dao.DeleteActivityWithRank(activityRankId)
					if err != nil {
						log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubActivity | DeleteActivityWithRank failed, activityRankId data: %v,err: %v", activityRankId, err)
						return
					}
				}
			}

			unSyncTaskIdStrArr := make([]string, 0, len(unSyncTaskId))
			for _, id := range unSyncTaskId {
				if id > 0 {
					unSyncTaskIdStrArr = append(unSyncTaskIdStrArr, cast.ToString(id))
				}
			}
			// 删除关联的动态
			deletePostAndWithActivity(unSyncTaskIdStrArr)
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncCreatorHubActivity end at %v", time.Now())
	}
}

// 批量获取活动
func getCreatorHubActivity(taskList []TaskItem, gameId string) error {

	var activityDataMap = make([]*model.CreatorHubActivityLanguageList, 0)
	var activityRank = make(map[int64][]*model.CreatorHubRankLanguageList)
	var taskIds []int64
	for _, taskItem := range taskList {
		imageUrl := taskItem.ThumbnailUrl
		if imageUrl == "" || len(imageUrl) == 0 {
			imageUrl = taskItem.ImageUrl
		}
		// 转存图片
		newImageUrl := uploadCreatorHubImage("lip/ugc/public/image/", imageUrl, gameId, "global", "")
		if newImageUrl == "" {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("createPostByCreatorHubData uploadCreatorHubImage failed: %s", imageUrl)
			newImageUrl = imageUrl
		}
		taskIds = append(taskIds, taskItem.TaskId)
		data := &model.CreatorHubActivityLanguageList{
			CreatorHubActivity: model.CreatorHubActivity{
				Model:       &model.Model{ID: 0},
				TaskID:      int32(taskItem.TaskId),
				EndTime:     taskItem.EndTime,
				StartTime:   taskItem.StartTime,
				PublishTime: taskItem.PublishTime,
				ImageURL:    newImageUrl,
				TaskStatus:  taskItem.TaskStatus,
				TaskType:    taskItem.TaskType,
			},
			Languages: make([]*model.CreatorHubActivityLanguage, 0),
		}
		// 如果活动多语言数据不为空的话可以添加数据
		if taskItem.TaskLangPackageList != nil && len(taskItem.TaskLangPackageList) > 0 {
			for _, lang := range taskItem.TaskLangPackageList {
				for _, val := range lang.KeyValList {
					if val.Key == "task_name" {
						// 转换多语言的枚举值
						language := lang.Lang
						//转换多语言的枚举
						if changeLang, ok := CreatorhubLanguageMap[language]; ok {
							language = changeLang
						}
						data.Languages = append(data.Languages, &model.CreatorHubActivityLanguage{
							Name:       val.Val,
							ActivityID: 0,
							Language:   language,
						})
					}
				}
			}
		}
		// 判断是否有en的数据
		//hasEnData := false
		//for _, list := range data.Languages {
		//	if list.Language == "" {
		//		hasEnData = true
		//		break
		//	}
		//}
		// 活动默认的语言
		if taskItem.Name != "" {
			data.Languages = append(data.Languages, &model.CreatorHubActivityLanguage{
				Name:       taskItem.Name,
				ActivityID: 0,
				Language:   "default",
			})
		}
		activityDataMap = append(activityDataMap, data)
		var rankList = make([]*model.CreatorHubRankLanguageList, 0)
		for _, rule := range taskItem.RankRewardRuleList {
			rankData := &model.CreatorHubRankLanguageList{
				RankKey:   rule.RankKey,
				Languages: make([]*model.CreatorHubRankLanguage, 0),
			}
			// 判断是否有多语言数据
			if taskItem.TaskLangPackageList != nil && len(taskItem.TaskLangPackageList) > 0 {
				for _, lang := range taskItem.TaskLangPackageList {
					for _, val := range lang.KeyValList {
						if val.Key == rule.RankKey {
							language := lang.Lang
							//转换多语言的枚举
							if changeLang, ok := CreatorhubLanguageMap[language]; ok {
								language = changeLang
							}
							rankData.Languages = append(rankData.Languages, &model.CreatorHubRankLanguage{
								Name:     val.Val,
								RankID:   0,
								Language: language,
							})
						}
					}
				}
			}
			// 赛道的默认语言
			if rule.RankName != "" {
				rankData.Languages = append(rankData.Languages, &model.CreatorHubRankLanguage{
					Name:     rule.RankName,
					RankID:   0,
					Language: "default",
				})
			}
			rankList = append(rankList, rankData)
		}
		activityRank[taskItem.TaskId] = rankList

	}
	err := HandleActivity(activityDataMap, activityRank, taskIds)
	return err
}

// 处理活动数据
func HandleActivity(activityData []*model.CreatorHubActivityLanguageList, activityRank map[int64][]*model.CreatorHubRankLanguageList, taskIds []int64) error {
	// 获取数据表中的数据
	activityList, err := dao.GetCreatorHubActivityListByTaskIds(taskIds)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleActivity | get creator hub activity data failed, task ids: %v,err: %v", taskIds, err)
		return err
	}
	if len(activityList) == 0 {
		// 全部不存在直接新增
		err = dao.BatchInsertCreatorHubActivity(activityData)
		if err != nil {
			// 记录日志
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleActivity | create creator hub activity data failed, create data: %+v,err: %v", activityData, err)
			return err
		}
		err = HandleRankWithActivity(activityRank)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleActivity | create creator hub activity by handle rank with activity, params: %+v,err: %v", activityRank, err)
			return err
		}
		return nil
	}
	// 查询得到数据，存在部分新增部分更新或者全部更新
	var updateDatas []*model.CreatorHubActivity
	var insertDatas []*model.CreatorHubActivityLanguageList
	var updateLanguages []*model.CreatorHubActivityLanguage
	var endOfActivityIds []string
	var endToNormalActivityIds []string
	for _, hubActivity := range activityData {
		isExistData := false
		for _, activity := range activityList {
			if hubActivity.TaskID == activity.TaskID {
				hubActivity.ID = activity.ID
			} else {
				// 没有匹配中进入下一次循环
				continue
			}
			isExistData = true
			// 判断是否是需要下线的活动,已经取消的活动需要下线作品
			if hubActivity.TaskStatus == int32(CANCELLED) && hubActivity.TaskStatus != activity.TaskStatus {
				endOfActivityIds = append(endOfActivityIds, cast.ToString(hubActivity.TaskID))
			}
			// 判断是否是需要上线的活动
			if hubActivity.TaskStatus == int32(PUBLISHED) && hubActivity.TaskStatus != activity.TaskStatus {
				endToNormalActivityIds = append(endToNormalActivityIds, cast.ToString(hubActivity.TaskID))
			}
			// 校验当前活动下的语言是否变更
			for _, language := range hubActivity.Languages {
				isExistLang := false
				for _, activityLanguage := range activity.Languages {
					// 存在相同语言的情况下
					if language.Language == activityLanguage.Language {
						isExistLang = true
						if language.Name != activityLanguage.Name {
							// 两个的多语言的值不一致的情况下
							activityLanguage.Name = language.Name
							updateLanguages = append(updateLanguages, activityLanguage)
						}
						break
					}
				}
				// 如果不存在相同语言的情况下，可能是新增的语言
				if !isExistLang {
					language.ActivityID = activity.ID
					updateLanguages = append(updateLanguages, language)
				}

			}
			updateDatas = append(updateDatas, &hubActivity.CreatorHubActivity)
			// 匹配中了直接跳出本次循环
			break
		}
		// 没有匹配中，那是新增的数据
		if !isExistData {
			insertDatas = append(insertDatas, hubActivity)
		}
	}

	// 写入db
	if len(updateDatas) > 0 {
		err = dao.BatchUpdateCreatorHubActivity(updateDatas)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleActivity | update creator hub activity data failed, update data: %+v,err: %v", updateDatas, err)
			return err
		}
	}
	if len(updateLanguages) > 0 {
		err = dao.BatchUpdateCreatorHubActivityLanguage(updateLanguages)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleActivity | update creator hub activity language data failed, update language data: %+v,err: %v", updateLanguages, err)
			return err
		}
	}
	if len(insertDatas) > 0 {
		// 需要新增的数据
		err = dao.BatchInsertCreatorHubActivity(insertDatas)
		// 记录日志
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleActivity | create some creator hub activity data failed, create some data: %+v,err: %v", activityData, err)
			return err
		}
	}
	if len(endOfActivityIds) > 0 {
		updatePostByTaskId(endOfActivityIds, true)
	}
	if len(endToNormalActivityIds) > 0 {
		updatePostByTaskId(endToNormalActivityIds, false)
	}

	err = HandleRankWithActivity(activityRank)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleActivity | create creator hub activity by handle rank with activity, params: %+v,err: %v", activityRank, err)
		return err
	}
	return nil
}

// 处理赛道和活动的关联关系
func HandleRankWithActivity(withData map[int64][]*model.CreatorHubRankLanguageList) error {
	// 获取所有活动下赛道的关系
	var activityIds = make([]int64, 0, len(withData))
	var rankIds []int64
	var activityRankInfos []*model.CreatorHubRankLanguageList
	var existRankIds []int64
	for activityId, _ := range withData {
		activityIds = append(activityIds, activityId)
	}
	// 如果没有数据的话直接返回
	if len(activityIds) == 0 {
		return nil
	}
	activityRank, err := dao.GetActivityWithRankList(activityIds)
	if err != nil {
		return err
	}

	for _, ranks := range activityRank {
		for _, rank := range ranks {
			rankIds = append(rankIds, rank.RankID)
		}
	}

	activityRankInfos, err = dao.GetRankListById(rankIds)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubWork | get rank data failed, rank ids: %v,err: %v", rankIds, err)
	}
	// 组装活动和赛道之间的关系
	var rankInfos = make(map[int64][]*model.CreatorHubRankLanguageList)
	for taskId, ranks := range activityRank {
		for _, info := range activityRankInfos {
			for _, rank := range ranks {
				if info.ID == rank.RankID {
					rankInfos[taskId] = append(rankInfos[taskId], info)
				}
			}
		}
	}
	// 先处理赛道的入库操作
	for activityId, ranks := range withData {
		var insertData []*model.CreatorHubRankLanguageList
		var insertRankLanguageData []*model.CreatorHubRankLanguage
		//var rankIds = make([]int64, 0)
		// 判断当前赛道是否存在数据表中
		var updateData = make([]*model.CreatorHubRankLanguage, 0)
		if rankRows, ok := rankInfos[activityId]; ok && len(rankRows) > 0 {
			for _, hubRank := range ranks {
				var hasData bool
				// 判断那条赛道被删除了，或者多语言更新了
				for _, row := range rankRows {
					// 对应相同的key相同才能继续往下走
					if row.RankKey == hubRank.RankKey {
						hasData = true
						// 当前key下没有多语言,但是新增的有多语言，那就是需要新增的多语言数据
						if len(row.Languages) == 0 && len(hubRank.Languages) > 0 {
							existRankIds = append(existRankIds, row.ID)
							for _, rLang := range hubRank.Languages {
								rLang.RankID = row.ID
							}
							insertRankLanguageData = append(insertRankLanguageData, hubRank.Languages...)
							break
						}
						// 对比多语言数据，确定哪些是要新增的
						for _, language := range hubRank.Languages {
							for _, rankLanguage := range row.Languages {
								if language.Language == rankLanguage.Language {
									if rankLanguage.Name != language.Name {
										// 需要更新的
										rankLanguage.Name = language.Name
										updateData = append(updateData, rankLanguage)

									}
									existRankIds = append(existRankIds, row.ID)
									break
								}
							}
						}
					}
				}
				if !hasData {
					insertData = append(insertData, hubRank)
				}

			}
		} else {
			// 没有这个活动的数据，直接新增即可
			insertData = append(insertData, ranks...)
		}
		if len(insertData) > 0 {
			err = dao.BatchCreateRank(insertData)
			if err != nil {
				// 新增赛道失败，退出当前循环；记录错误
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleRankWithActivity | create creator hub rank failed, params: %+v,err: %v", insertData, err)
				continue
			}
			// 创建关联关系
			var activityWithRank = make([]*model.CreatorHubActivityRank, 0)
			for _, datum := range insertData {
				activityWithRank = append(activityWithRank, &model.CreatorHubActivityRank{
					TaskID: activityId,
					RankID: datum.ID,
				})
			}
			if len(activityWithRank) > 0 {
				err = dao.SaveActivityWithRank(activityWithRank)
				if err != nil {
					// 创建关联关系失败，记录错误
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleRankWithActivity | create creator hub activity with rank failed, params: %+v,err: %v", activityWithRank, err)
					continue
				}
			}
		}
		if len(updateData) > 0 {
			err = dao.BatchUpdateRank(updateData)
			if err != nil {
				// 更新赛道失败，记录错误
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleRankWithActivity | update creator hub rank failed, params: %+v,err: %v", updateData, err)
				continue
			}
		}
		// 创建赛道多语言
		if len(insertRankLanguageData) > 0 {
			err = dao.BatchSaveRankLanguage(insertRankLanguageData)
			if err != nil {
				// 创建赛道多语言失败，记录错误
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleRankWithActivity | batch save rank language creator hub rank failed, params: %+v,err: %v", insertRankLanguageData, err)
				continue
			}
		}
		// 处理不存在的关联关系
		deleteActivityWithRankIds := make([]int64, 0)
		for _, row := range activityRank[activityId] {
			isDel := true
			for _, id := range existRankIds {
				if id == row.RankID {
					isDel = false
					break
				}
			}
			if isDel {
				deleteActivityWithRankIds = append(deleteActivityWithRankIds, row.ID)
			}
		}
		if len(deleteActivityWithRankIds) > 0 {
			err = dao.DeleteActivityWithRank(deleteActivityWithRankIds)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("HandleRankWithActivity | delete creator hub activity with rank failed, params: %+v,err: %v", deleteActivityWithRankIds, err)
				continue
			}
		}

	}
	return nil
}

// 获取活动列表
func GetActivityList(c context.Context, req *pb.GetCreatorHubTaskListReq, language string) (rsp *pb.GetCreatorHubTaskListRsp, err error) {
	rsp = &pb.GetCreatorHubTaskListRsp{
		List: make([]*pb.GetCreatorHubTaskItem, 0),
		PageInfo: &pb.PageInfo{
			PreviousPageCursor: "",
			NextPageCursor:     "",
			IsFinish:           false,
			Total:              0,
		},
	}
	var previousPageCursor, nextPageCursor string
	var gtId int64
	var activities []*model.CreatorHubActivity
	var addLimit = 1
	if req.Limit <= 1 {
		req.Limit = 10
	}
	var limit = int(req.Limit) + addLimit
	var pageTotal int
	var totalCount int64
	// 查询类型：下一页数据
	if req.PageType == pb.PageType_NEXTPAGE {
		var idCursor int64
		// 如果是首页
		if req.NextPageCursor == "" {
			idCursor = 0
		} else {
			previousPageCursor = req.NextPageCursor
			idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
			}
			gtId = idCursor
		}
		nowTime := time.Now().Unix()
		condition := &model.CreatorHubActivityWhere{
			LtId:    gtId,
			Limit:   limit,
			NowTime: nowTime,
			Status:  constants.CreatorhubActivityStatus_Published,
		}
		activities, totalCount, err = dao.GetActivityListByGtId(condition)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetActivityList | failed to get activity list, gtId: %d, limit: %d,err: %v", gtId, req.Limit, err)
			return nil, errs.NewCustomError(c, code.GetCreatorHubListFailed, "Failed to get list")
		}
		// 生成下一页的游标
		if len(activities) > 0 {
			pageTotal = len(activities)
			if pageTotal >= (limit - addLimit) {
				activities = activities[0 : limit-addLimit]
			}
			nextPageCursor, err = util.EncryptPageCursorI(activities[len(activities)-1].ID)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to create comments nextPageCursor")
			}
		}
		// 23/3/26, 放开限制，不管怎样都拿最近已结束活动补齐成6个活动
		//if len(activities) == 0 {
		//	return rsp, nil
		//}
	}

	// 判断是否最后一页，如果是加上最近已结束的6个活动
	if pageTotal < limit {
		// 只查询状态是等待发布的已经结束的
		condition := &model.CreatorHubActivityWhereByStatus{
			Status: []int{constants.CreatorhubActivityStatus_Over},
			Order: map[string]string{
				"publish_time": "desc",
			},
			Limit: 6,
		}
		activitiesList, err := dao.GetAllActivityListByStatus(condition)
		if err == nil {
			activities = append(activities, activitiesList...)
		}
	}

	var activityIds []int64
	var taskIds []int64
	for _, activity := range activities {
		activityIds = append(activityIds, activity.ID)
		taskIds = append(taskIds, int64(activity.TaskID))
	}
	activityLanguages, err := dao.GetActivityLanguageListByActivityIds(activityIds)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetActivityList | failed to get activity language list, activityIds: %v,err: %v", activityIds, err)
		return nil, errs.NewCustomError(c, code.GetCreatorHubLanguageListFailed, "Failed to get lang list")
	}
	rankList, err := dao.GetActivityWithRankList(taskIds)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetActivityList | failed to get activity rank list, activityIds: %v,err: %v", activityIds, err)
		return nil, errs.NewCustomError(c, code.GetCreatorHubRankListFailed, "Failed to get rank list")
	}
	var wg sync.WaitGroup
	for _, activity := range activities {
		var resItem = &pb.GetCreatorHubTaskItem{
			Id:          int32(activity.ID),
			TaskId:      activity.TaskID,
			PublishTime: int32(activity.PublishTime),
			ImageUrl:    activity.ImageURL,
			TaskStatus:  activity.TaskStatus,
			TaskName:    "",
			Ranks:       make([]*pb.GetCreatorHubRankItem, 0),
			TaskPageUrl: fmt.Sprintf("%s?game_id=%d&task_id=%d&game=%s", config.GetConfig().CreatorHubSetting.CreatorHubActivityPageUrl, config.GetConfig().CreatorHubSetting.CreatorHubActivityGameId, activity.TaskID, config.GetConfig().CreatorHubSetting.CreatorHubActivityGameName),
		}
		var isExistLang bool
		for _, lang := range activityLanguages {
			if lang.Language == language && lang.ActivityID == activity.ID {
				resItem.TaskName = lang.Name
				isExistLang = true
				break
			}
		}
		// 兜底语言
		if !isExistLang {
			for _, lang := range activityLanguages {
				if lang.Language == "default" && lang.ActivityID == activity.ID {
					resItem.TaskName = lang.Name
					isExistLang = true
					break
				}
			}
		}
		if ranks, ok := rankList[int64(activity.TaskID)]; ok {
			var rankIds []int64
			for _, rank := range ranks {
				rankIds = append(rankIds, rank.RankID)
			}
			wg.Add(1)
			go func(rankIds []int64, resItem *pb.GetCreatorHubTaskItem) {
				defer recovery.CatchGoroutinePanic(c)
				defer wg.Done()
				if len(rankIds) == 0 {
					return
				}
				rankInfos, err := dao.GetRankListById(rankIds)
				if err != nil {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetActivityList | failed to get activity ranks, rankIds: %v,err: %v", rankIds, err)
					return
				}
				for _, rank := range rankInfos {
					var rankName string
					for _, rankLanguage := range rank.Languages {
						if rankLanguage.RankID == rank.ID && rankLanguage.Language == language {
							rankName = rankLanguage.Name
							break
						}
					}
					// 兜底英语
					if rankName == "" {
						for _, rankLanguage := range rank.Languages {
							if rankLanguage.RankID == rank.ID && rankLanguage.Language == "default" {
								rankName = rankLanguage.Name
								break
							}
						}
					}
					resItem.Ranks = append(resItem.Ranks, &pb.GetCreatorHubRankItem{
						Id:       int32(rank.ID),
						RankName: rankName,
					})
				}
			}(rankIds, resItem)
		}
		rsp.List = append(rsp.List, resItem)
	}
	wg.Wait()
	if len(rsp.List) == 0 || pageTotal < limit {
		rsp.PageInfo.IsFinish = true
	} else {
		rsp.PageInfo.NextPageCursor = nextPageCursor
	}
	rsp.PageInfo.Total = int32(totalCount)
	rsp.PageInfo.PreviousPageCursor = previousPageCursor
	return
}

// 根据活动任务id删除动态
func updatePostByTaskId(taskIds []string, isDeleted bool) error {
	if len(taskIds) == 0 {
		return nil
	}
	if isDeleted {
		deletePostAndWithActivity(taskIds)
	} else {
		resetPostAndWithActivity(taskIds)
	}
	return nil
}

// 删除动态、动态和活动之间的关联关系
func deletePostAndWithActivity(taskIds []string) {
	if len(taskIds) == 0 {
		return
	}
	// 删除动态
	err := dao.DeletePostExternalByTaskId(taskIds)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("deletePostAndWithActivity | expire work delete post failed, task_ids: [%+v],err: %v", taskIds, err)
		return
	}
	return
}

// 恢复动态、动态和活动之间的关联关系
func resetPostAndWithActivity(taskIds []string) {
	if len(taskIds) == 0 {
		return
	}
	// 恢复动态
	err := dao.ResetPostExternalDeleteByTaskIds(taskIds)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("resetPostAndWithActivity | expire work reset post failed, task_ids: [%+v],err: %v", taskIds, err)
		return
	}
	return
}

// 拉取最近task
func GetRecentTasks(ctx context.Context, limit int32, offset int32) (rsp *pb.GetRecentTasksRsp, err error) {
	rsp = &pb.GetRecentTasksRsp{
		TaskList: make([]*pb.CreatorHubTaskItem, 0),
	}
	// // 判断是否绑定
	// if bindInfo, err := GetBoundInfoOfCreatorHub(ctx, intlOpenid); err != nil || bindInfo == nil {
	// 	return rsp, err
	// }
	creatorHubRecentTasksKey := cache.GetCreatorHubRecentTasksKey(limit, offset)
	getCreatorHubRecentTasksByApi := func(c context.Context) (interface{}, error) {
		taskUrl := config.GetConfig().CreatorHubSetting.GetTaskPath
		postData := map[string]interface{}{
			"gameid":      "16",
			"offset":      offset,
			"limit":       limit,
			"sort_type":   1,
			"status_list": []int32{0},
		}
		postJson, _ := json.Marshal(postData)

		resultOption, err := RequestCreateHub(c, "16", "asia", taskUrl, postJson)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetRecentTasks | request failed, request data: %s, err: %v", string(postJson), err)
			return nil, err
		}
		var res TaskRes
		err = json.Unmarshal([]byte(resultOption), &res)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetRecentTasks | json decode failed, result data: %s,err: %v", resultOption, err)
			return nil, err
		}
		if res.Ret != 0 {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetRecentTasks | api return failed, result data: %s,err: %v", resultOption, err)
			return nil, err
		}
		for _, item := range res.Data.TaskList {
			rsp.TaskList = append(rsp.TaskList, &pb.CreatorHubTaskItem{
				EndTime:       item.EndTime,
				ImageUrl:      item.ImageUrl,
				LipGameid:     item.LipGameId,
				Name:          item.Name,
				PublishTime:   item.PublishTime,
				TaskModelType: item.TaskModelType,
				TaskStatus:    item.TaskStatus,
				TaskType:      item.TaskType,
				ThumbnailUrl:  item.ThumbnailUrl,
				TaskUrl:       item.TaskUrl,
				StartTime:     item.StartTime,
			})
		}
		if len(res.Data.TaskList) < int(limit) {
			// 结束
			rsp.NextOffset = 0
		} else {
			rsp.NextOffset = offset + limit
		}
		// 设置缓存
		cache.SetCacheWithMarshal(ctx, creatorHubRecentTasksKey, rsp, time.Minute*5)
		return rsp, nil
	}

	r, err := cache.GetCacheWithUnmarshal(ctx, creatorHubRecentTasksKey, rsp, nil, &getCreatorHubRecentTasksByApi, true)
	// rsp = res.(*pb.GetRecentEventsRsp)
	if err != nil {
		return nil, err
	}
	rsp = r.(*pb.GetRecentTasksRsp)
	return
}
