package model

type CommentState struct {
	*Model
	CommentUUID string `gorm:"column:comment_uuid" json:"comment_uuid"` //动态评论唯一ID
	UpvoteCount int    `gorm:"column:upvote_count" json:"upvote_count"` //点赞数量
	ReplyCount  int    `gorm:"column:reply_count" json:"reply_count"`   //评论的回复数量
}

// TableName sets the insert table name for this struct type
func (c *CommentState) TableName() string {
	return "p_comment_state"
}
