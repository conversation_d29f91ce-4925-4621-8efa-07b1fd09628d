package attachment

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"io"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/pkg/storage"
	"trpc.publishing_application.standalonesite/app/util"

	"trpc.publishing_application.standalonesite/app/config"
)

func CheckAttachment(cmsGameId, cmsAreaId, uri string) error {
	conf := config.GetConfig()
	var cosConfig *config.COSSetting
	for _, v := range conf.COS {
		cmsGameidS := strconv.Itoa(v.CmsGameId)
		if cmsGameidS == cmsGameId && v.CmsAreaId == cmsAreaId {
			cosConfig = v
		}
	}
	if cosConfig.CDNDomain == "" {
		return fmt.Errorf("The game storage information has not been configured. cms game id: %s, area id: %s", cmsGameId, cmsAreaId)
	}
	domain := fmt.Sprintf("https://%s/", cosConfig.CDNDomain)
	if strings.Index(uri, domain) != 0 {
		return fmt.Errorf("The attachment is not a legal resource of the game on this site.")
	}
	return nil
}

// 转存视频封面图片
func TransferVideoImage(ctx context.Context, tryAgainCount int, imageUrl string, intlGameId string, areaId string) string {
	var uploadImageUrl string
	for tryAgainCount > 0 {
		// 转存图片
		uploadImageUrl = UploadImage("lip/ugc/public/image/", imageUrl, intlGameId, areaId, "")
		if uploadImageUrl != imageUrl && uploadImageUrl != "" {
			return uploadImageUrl
		}
		if uploadImageUrl == "" {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("TransferVideoImage | upload image failed, image url: %s", imageUrl)
		}
		tryAgainCount--
	}
	if uploadImageUrl == "" {
		// 使用兜底的图片
		uploadImageUrl = config.GetConfig().Dynamic.VideoPreviewUrl
	}

	return uploadImageUrl
}

// uploadCreatorHubImage 下载Creatorhub网红爬取过来的动态图片并上传到对应游戏的COS桶去
func UploadImage(path, imgUrl, gameId, areaId, imgType string) (newImgUrl string) {
	res, err := http.Get(imgUrl)
	if err != nil || res.StatusCode != 200 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("uploadCreatorHubImage failed, image url: %s, type: %s, error: %v", imgUrl, imgType, err)
		return
	}
	defer res.Body.Close()
	// 获取文件后缀名
	subImgUrl := strings.Split(imgUrl, "?")[0]
	fileExt := filepath.Ext(subImgUrl)
	contentType := getImageContentType(fileExt)
	if contentType == "" {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("uploadCreatorHubImage failed, error content type, image url: %s", imgUrl)
		return
	}
	// 生成随机路径

	imageBody, err := io.ReadAll(res.Body)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("uploadCreatorHubImage failed, error read io body, image url: %s", imgUrl)
		return
	}
	imageMd5 := util.MD5(imageBody)
	ossSavePath := path + imageMd5 + fileExt

	cosObjectStorage, err := storage.NewCOSServiceByGameid(gameId, areaId)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("uploadCreatorHubImage NewCOSServiceByGameid err: %v", err)
		return
	}
	reader := strings.NewReader(string(imageBody))
	newImgUrl, err = cosObjectStorage.PutObject(ossSavePath, reader, res.ContentLength, contentType, false)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("uploadCreatorHubImage putObject err: %v", err)
		return
	}

	return
}

func getImageContentType(imageFileExt string) string {
	switch imageFileExt {
	case ".png":
		return "image/png"
	case ".jpg":
		return "image/jpg"
	case ".jpeg":
		return "image/jpeg"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	default:
		return ""
	}
}
