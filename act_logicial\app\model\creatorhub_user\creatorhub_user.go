package creatorhub_user

func (UserRewardInfo) TableName() string {
	return "user_reward_info"
}

// UserRewardInfo 用户发奖列表
type UserRewardInfo struct {
	ID              int64  `gorm:"type:int(11);column:id;primary_key"`
	GameID          string `gorm:"column:gameid"`
	AreaID          string `gorm:"column:areaid"`
	Uid             string `gorm:"column:uid"`
	ProjectID       string `gorm:"column:project_id"`
	RewardStr       string `gorm:"column:reward_str"`       //奖励信息
	RewardTime      int64  `gorm:"column:reward_time"`      //奖励获取时间，秒级时间戳
	DeliveryCountry string `gorm:"column:delivery_country"` //收货国家
	DeliveryAddr    string `gorm:"column:delivery_addr"`    //收货详细地址
	DeliveryZip     string `gorm:"column:delivery_zip"`     //收货邮编
	DeliveryPhone   string `gorm:"column:delivery_phone"`   //手机号
	DeliveryReciver string `gorm:"column:delivery_reciver"` //收货人
}
