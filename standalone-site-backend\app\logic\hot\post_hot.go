package tweet

import (
	"context"
	"encoding/json"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	es7 "github.com/olivere/elastic/v7"
	"golang.org/x/net/html"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

func HotPostEventExec() {
	c := context.Background()
	defer recovery.CatchGoroutinePanic(context.Background())
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("HotPostEvent ticker at %v", time.Now())
	// 通过redis，先判断定时任务是否还在运行中
	postHotCalculationTask := cache.GetPostHotCalculationTaskKey()
	if ok, _ := redis.GetClient().SetNX(c, postHotCalculationTask, 1, time.Duration(config.GetConfig().Dynamic.HotPostEventTimeoutDuration)*time.Second).Result(); ok {
		defer redis.GetClient().Del(context.Background(), postHotCalculationTask)
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("HotPostEventExec start at %v", time.Now())

		conditions := &dao.PlateConditions{
			UniqueIdentifierList: []string{"outpost", "nikkeart", "creatorhub", "guides"},
		}
		// 查询生效中的outpost、nikkeart、creatorhub的板块
		plates, err := dao.GetPlateList(conditions, 20)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("HotPostEventExec GetPlateList err, err=(%v)", err)
			return
		}
		if len(plates) == 0 {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("HotPostEventExec GetPlateList empty")
			return
		}

		var sortValue []interface{}
		var wg sync.WaitGroup
		for {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Infof("HotPostEventExec running at %v", time.Now())
			postList, lastSortValue, err := GetPostForHotTask(c, plates, sortValue, 100)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("HotPostEventExec GetPostForHotTask err.error: %v", err)
				break
			}
			// 最后一页没有数据了
			if len(postList) == 0 {
				break
			}
			sortValue = lastSortValue

			// 按照每组100个值将数组分成多个小数组
			batchSize := 10
			for i := 0; i < len(postList); i += batchSize {
				end := i + batchSize
				if end > len(postList) {
					end = len(postList)
				}
				batchPosts := postList[i:end]
				wg.Add(1) // 增加 WaitGroup 的计数器
				go func(esPostList []*model.ESPost) {
					defer recovery.CatchGoroutinePanic(context.Background())
					defer wg.Done() // 函数结束时减少计数器
					for _, postItem := range esPostList {
						postContent := &model.PostStats{
							Model: &model.Model{
								CreatedOn: postItem.CreatedOn,
							},
							PostUUID:        postItem.PostUuid,
							BrowseCount:     postItem.BrowseCount,
							UpvoteCount:     postItem.UpvoteCount,
							CommentCount:    postItem.CommentCount,
							CollectionCount: postItem.CollectionCount,
							PowerNumFloat:   postItem.PowerNum,
							DemotionNum:     postItem.DemotionNum,
						}
						hotNum := CalculatingPostHotNum(postContent)
						doc := map[string]interface{}{
							"hot_num": hotNum,
						}
						dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postContent.PostUUID, doc)
					}
				}(batchPosts)
			}
			// 临时加上，用于解决全量推送，cpu打满的问题
			time.Sleep(1 * time.Second)
			// 等待所有 goroutine 完成
			wg.Wait()
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("HotPostEventExec end at %v", time.Now())
	}

}

func GetPostForHotTask(c context.Context, plates []*model.Plate, lastSortValue []interface{}, limit int64) ([]*model.ESPost, []interface{}, error) {
	esPostList := make([]*model.ESPost, 0)
	var lastHitSort []interface{}
	boolQuery := es7.NewBoolQuery()
	isDelQuery := es7.NewTermQuery("is_del", 0)
	isAuditQuery := es7.NewTermQuery("is_audit", 1)
	boolQuery.Must(isDelQuery, isAuditQuery)
	for _, plate := range plates {
		plateQuery := es7.NewTermQuery("plate_id", plate.ID)
		boolQuery.Should(plateQuery)
	}

	var sortBys []es7.Sorter
	sortQuery := es7.NewFieldSort("id").Desc()
	sortBys = append(sortBys, sortQuery)
	resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, boolQuery, sortBys, lastSortValue, limit)
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostForHotTask EsQuery err, err=(%v)", err)
		return esPostList, lastHitSort, err
	}
	if resp == nil || resp.Hits == nil || len(resp.Hits.Hits) == 0 {
		return esPostList, lastHitSort, nil
	}
	for _, hit := range resp.Hits.Hits {
		espost := &model.ESPost{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostForHotTask json.Marshal err, err=(%v)", err)
			continue
		}
		if err = json.Unmarshal(raw, espost); err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostForHotTask json.Unmarshal err, err=(%v)", err)
			continue
		}
		esPostList = append(esPostList, espost)
	}
	lastHitSort = resp.Hits.Hits[len(resp.Hits.Hits)-1].Sort
	return esPostList, lastHitSort, nil
}

func CalculatingPostHotNum(postContent *model.PostStats) int64 {
	if postContent == nil {
		return 0
	}
	// 计算热度
	// 初始权重、是否是白名单用户、是否拥有称号
	// initialValue := post.PowerNum
	var initialValue float64 = calculatingInitialPostHotValue(postContent.PostUUID)

	// 互动热度
	browseCountCoefficient := config.GetConfig().Dynamic.BrowseCountCoefficient
	upvoteCountCoefficient := config.GetConfig().Dynamic.UpvoteCountCoefficient
	commentCountCoefficient := config.GetConfig().Dynamic.CommentCountCoefficient
	collectionCountCoefficient := config.GetConfig().Dynamic.CollectionCountCoefficient
	shareCountCoefficient := config.GetConfig().Dynamic.CollectionCountCoefficient
	// 动态创建时间距离当前时间的秒数
	timeDiff := time.Now().Unix() - postContent.CreatedOn
	if timeDiff <= 0 {
		timeDiff = 1
	}
	timeDiffCoefficient := calculatingTimeCoefficient(timeDiff)
	interactiveHeat := (float64(postContent.BrowseCount)*browseCountCoefficient +
		float64(postContent.UpvoteCount)*upvoteCountCoefficient +
		float64(postContent.CommentCount)*commentCountCoefficient +
		float64(postContent.CollectionCount)*collectionCountCoefficient +
		float64(postContent.ForwardCount)*shareCountCoefficient + initialValue) /
		(math.Pow(float64(((timeDiff)/(60*60))+1), timeDiffCoefficient))
	// 单条动态的总热度 - 降权值
	hotNum := interactiveHeat * float64(postContent.PowerNumFloat)

	// 重新查询一遍p_post_stats,得到降权值
	if postContent.DemotionNum > 0 {
		hotNum -= float64(postContent.DemotionNum)
	}

	if hotNum < 0 {
		hotNum = 0
	}
	return int64(hotNum)
}

// 计算初始热度值：
// -- 图片：一张图片+1000，最多可加3000热度。
// -- 文字：20-50字符+1000，50-200字符+2000，大于200字符+3000
// 新帖一共最多+6000热度
func calculatingInitialPostHotValue(postUUid string) float64 {
	ctx := context.Background()
	var initialValue float64
	if postUUid == "" {
		return 0
	}
	// 缓存中获取
	redisKey := cache.GetPostInitialHotValueKey(postUUid)
	cacheValue, err := redis.GetClient().Get(ctx, redisKey).Result()
	// 缓存中获取成功，直接返回
	if err == nil && cacheValue != "" {
		initialValue, _ = strconv.ParseFloat(cacheValue, 64)
		return initialValue
	}

	logUgc := constants.LogType_Standalonesite
	postDetail, err := dao.GetPost(postUUid)
	if err != nil {
		log.WithFieldsContext(ctx, "log type", logUgc).
			Errorf("calculatingInitialPostHotValue GetPost err, err=(%v)", err)
	}
	// 帖子类型： 1帖子(富文本) 2图文 3 外部平台视频动态
	postType := postDetail.Type

	// 获取文章内容
	postContents, err := dao.GetPostContentList(postUUid, false)
	if err != nil {
		log.WithFieldsContext(ctx, "log type", logUgc).
			Errorf("calculatingInitialPostHotValue GetPostContentList err, err=(%v)", err)
	}
	if len(postContents) == 0 {
		log.WithFieldsContext(ctx, "log type", logUgc).
			Errorf("calculatingInitialPostHotValue GetPostContentList no postContents")
	}
	if len(postContents) > 0 {
		post := postContents[0]
		// 按照帖子类型计算冷启热度值
		initialValue = CalPostHotValue(ctx, int(postType), post)
		log.WithFieldsContext(ctx, "log type", logUgc).Infof("without timediff initial hot value: %.3f", initialValue)
		// 冷启热度值随时间衰减
		initialValue = CalHotValueWithTimeDiff(ctx, post.CreatedOn, initialValue)
	}
	log.WithFieldsContext(ctx, "log type", logUgc).Infof("final initial hot value: %.3f", initialValue)
	// 创作认证贴热启 * 1.5
	authType := formatted.GetUserAuth(postDetail.IntlOpenid)
	if authType == constants.USER_AUTH_TYPE_CREATOR {
		initialValue = initialValue * 1.5
	}
	// 设置缓存
	redis.GetClient().Set(ctx, redisKey, strconv.FormatFloat(initialValue, 'f', 6, 64), 2*time.Hour)
	return initialValue
}

// CalHotValueWithTimeDiff 冷启热度会先进行线性衰减，在24h内将冷启热度降低到0
func CalHotValueWithTimeDiff(ctx context.Context, createTimestamp int64, hotValue float64) float64 {
	diffSeconds := time.Now().Unix() - createTimestamp
	if diffSeconds > 86400 {
		return 0
	}
	coefficient := float64(86400-diffSeconds) / 86400
	result := hotValue * coefficient
	log.WithFieldsContext(ctx, "log type", constants.LogType_Standalonesite).
		Infof("CalHotValueWithTimeDiff coefficient: %.3f, result: %.3f", coefficient, result)
	return result
}

// CalPostHotValue 根据帖子类型计算冷启热度值
func CalPostHotValue(ctx context.Context, postType int, post *model.PostContent) float64 {
	const (
		PostTypeRichText = 1 // 富文本
		PostTypePicture  = 2 // 图片贴
		PostTypeVideo    = 3 // 视频贴
	)
	logUgc := constants.LogType_Standalonesite
	if postType == PostTypePicture {
		// 图片帖
		pictureCnt := len(strings.Split(post.PicUrls, ","))
		hotValue := calPictureHotValue(pictureCnt)
		if utf8.RuneCountInString(post.Title) < 10 {
			hotValue = hotValue * 0.6
		}
		log.WithFieldsContext(ctx, "log type", logUgc).
			Infof("CalPostHotValue PostTypePicture. pictureCnt: %v, hotValue: %.3f", pictureCnt, hotValue)
		return hotValue
	}

	var hotValue float64
	// pictureCnt 图片个数
	var pictureCnt int
	// 获取富文本贴 & 视频贴的字符个数和图片个数
	contentLen, imgUrlLen := calculatingContentLen(post.Content)
	pictureCnt = imgUrlLen

	if postType == PostTypeVideo {
		// 视频贴 等价于图片数量+2
		pictureCnt += 2
	}

	// 图片 热度值
	picHotValue := calPictureHotValue(pictureCnt)
	// 文字 热度值
	textHotValue := float64(0)
	if contentLen <= 100 {
		textHotValue += float64(contentLen * 10)
	} else if contentLen <= 500 {
		textHotValue += float64((contentLen + 100) * 5)
	} else {
		textHotValue += 3000
	}
	// 总热度值 = 图片热度值 + 文字热度值
	hotValue = picHotValue + textHotValue

	log.WithFieldsContext(ctx, "log type", logUgc).
		Infof("CalPostHotValue. contentLen: %v, pictureCnt: %v, picHotValue: %.3f, textHotValue: %.3f, hotValue: %.3f",
			contentLen, pictureCnt, picHotValue, textHotValue, hotValue)
	if hotValue > 6000 {
		hotValue = 6000
	}
	return hotValue
}

func calPictureHotValue(pictureCnt int) float64 {
	var hotValue float64
	hotValue = math.Log10(float64(pictureCnt+1)) * 3000
	if hotValue > 3000 {
		hotValue = 3000
	}
	return hotValue
}

func calculatingContentLen(content string) (int, int) {
	htmlStr := strings.Join([]string{"<html><body>", content, "</body></html>"}, "")
	doc, err := html.Parse(strings.NewReader(htmlStr))
	if err != nil {
		log.WithFieldsContext(context.Background(), "log type", constants.LogType_Standalonesite).Errorf("calculatingInitialPostHotValue Parse err, err=(%v)", err)
		return 0, 0
	}
	// 用于存储从文本字段抽离出来的图片链接、文本的数组、换行次数
	var contentImgLinks []string
	var contentTexts []string
	var lineBreakCount int

	// 查找并提取 div 和 p 标签文本，同时提取 img 标签的链接
	util.Traverse(doc, []string{"div", "p"}, &contentImgLinks, &contentTexts, &lineBreakCount)
	var contentTextLength int
	for _, contentText := range contentTexts {
		contentTextLength += utf8.RuneCountInString(contentText)
	}
	return contentTextLength, len(contentImgLinks)
}

// 1天以内 0.192 1天~3天0.4 3天以后 0.6
func calculatingTimeCoefficient(timeDiff int64) float64 {
	if timeDiff > 0 && timeDiff <= 86400 {
		return 0.192
	} else if timeDiff > 86400 && timeDiff <= 259200 {
		return 0.4
	} else {
		return 0.6
	}
}
