package cag_common

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/ams"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/publishing_marketing/game"
	"git.code.oa.com/trpcprotocol/publishing_marketing/present_ams"
	"github.com/spf13/cast"
	"time"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/constant"
	"trpc.act.logicial/app/model/cag"
	"trpc.act.logicial/app/mysql/cag_acvivity_repo"
	"trpc.act.logicial/app/mysql/functional"
	"trpc.act.logicial/app/redis"
	"trpc.act.logicial/app/service"
	"trpc.act.logicial/app/viewmodel"
)

// HasCagNewRolePrize 检查是否已经获取过cag的角色奖励
func HasCagNewRolePrize(ctx context.Context, userId string, prizeType, level int) error {
	// 判断是否已经领取过奖励
	res, err := GetCagNewRolePrizeRecord(ctx, userId, prizeType, level, 1)
	if err != nil {
		log.ErrorContextf(ctx, "HasCagNewRolePrize GetUserPrizeRecord failed, err:%v, userId: %v", err, userId)
		return code.ErrSystemError
	}
	if len(res) > 0 { // 已经领取过奖励
		log.InfoContextf(ctx, "HasCagNewRolePrize hasGetPrize, userId: %v, prizeType: %v, level: %v, num: %v",
			userId, prizeType, level, len(res))
		return code.ErrHasCagRolePrize
	}
	return nil
}

// GetCagNewRolePrizeRecord 获取角色等级奖励记录
func GetCagNewRolePrizeRecord(ctx context.Context, userId string,
	prizeType, level int, limit int) (res []*cag.CagRoleLevelPrizeRecord, err error) {
	activityId := config.GetConfig().CagNewRoleAct.ActivityId
	opts := []functional.Option{
		functional.WithActivityId(activityId),
		functional.WithGameId(constant.GAMEID_CAG),
		functional.WithUserId(userId),
		functional.WithPrizeType(prizeType),
		functional.WithLimit(limit),
	}
	if level > 0 {
		opts = append(opts, functional.WithLevel(level))
	}
	// 判断是否已经领取过奖励
	res, err = cag_acvivity_repo.CreateRoleActivityClient.GetUserPrizeRecord(ctx, opts)
	if err != nil {
		log.ErrorContextf(ctx, "GetCagNewRolePrizeRecord GetUserPrizeRecord failed, err:%v, userId: %v", err, userId)
		return nil, code.ErrSystemError
	}
	return
}

// ClaimNewRolePrize 领取角色和等级奖励
func ClaimNewRolePrize(ctx context.Context, userId string, lipOpenId string,
	langType string, gameOpenId string, prizeType, level, status int) error {
	activityId := config.GetConfig().CagNewRoleAct.ActivityId
	// 生成奖励记录
	gameName := constant.CAG_PRIZE_GAME_NAME

	record := &cag.CagRoleLevelPrizeRecord{
		GameID:      constant.GAMEID_CAG,
		ActivityID:  activityId,
		UserID:      userId,
		LipOpenID:   lipOpenId,
		OpenID:      gameOpenId,
		PrizeType:   prizeType,
		LangType:    langType,
		Status:      status,
		Level:       level,
		AmsSerialID: ams.CreateAmsSerial(gameName, config.GetConfig().AmsAppId), // 发奖的时候会用到（用于幂等重试）
	}
	err := cag_acvivity_repo.CreateRoleActivityClient.AddPrizeRecord(ctx, record)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimCreateRolePrizeProc claimPrize error: %v", err)
		return code.ErrSystemError
	}
	if status == constant.CagRoleLevelPrizeStatusWaitCreateRole { // 等待创建角色，直接返回，定时任务轮询等角色创建好以后再发奖
		log.InfoContextf(ctx, "ClaimCreateRolePrizeProc waitCreateRole, userId: %v, prizeType: %v",
			userId, prizeType)
		return nil
	}

	// 发放奖励
	err = DoSendNewRolePrize(ctx, record)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimCreateRolePrizeProc claimPrize error: %v", err)
		return code.ErrSystemError
	}
	return nil
}

// CheckNewRoleActTime 检查活动时间
func CheckNewRoleActTime(ctx context.Context) error {
	nowTime := time.Now().Unix()
	startTime := config.GetConfig().CagNewRoleAct.ActStartTimeStamp
	endTime := config.GetConfig().CagNewRoleAct.ActEndTimeStamp
	if nowTime < startTime {
		log.ErrorContextf(ctx, "CheckNewRoleActTime act not start, nowTime: %v, startTime: %v, endTime: %v",
			nowTime, startTime, endTime)
		return code.ErrActivityNotStart
	}
	if nowTime > endTime {
		log.ErrorContextf(ctx, "CheckNewRoleActTime act end, nowTime: %v, startTime: %v, endTime: %v",
			nowTime, startTime, endTime)
		return code.ErrActivityEnd
	}
	return nil
}

// DoSendNewRolePrize 发放角色和等级奖励
func DoSendNewRolePrize(ctx context.Context, record *cag.CagRoleLevelPrizeRecord) (err error) {
	log.InfoContextf(ctx, "DoSendNewRolePrize start, record: %+v", record)
	if record.OpenID == "" {
		log.ErrorContextf(ctx, "DoSendNewRolePrize OpenID is empty, record: %+v", record)
		return code.ErrSystemError
	}
	// 发奖加锁
	key := redis.GetCagSendRoleLevelPrizeLockKey(record.UserID, record.AmsSerialID)
	ok := redis.LockByKey(ctx, key, 10)
	if !ok {
		// TODO 告警
		log.ErrorContextf(ctx, "DoSendNewRolePrize LockByKey fail, key: %v", key)
		return code.ErrRequestFrequencyExceededLimitError
	}

	// 处理完解锁
	defer func() {
		redis.UnLockByKey(ctx, key)
	}()

	// 礼包单号和礼包组id
	amsId := ""
	groupId := ""
	if record.PrizeType == constant.CAG_NEW_ROLE_PRIZE_TYPE_ROLE { // 角色奖励
		amsId = config.GetConfig().CagNewRoleAct.RolePrizeAmsId
		groupId = config.GetConfig().CagNewRoleAct.RolePrizeGroupId
	} else if record.PrizeType == constant.CAG_NEW_ROLE_PRIZE_TYPE_LEVEL { // 等级奖励
		amsId = config.GetConfig().CagNewRoleAct.LevelPrizeAmsId
		switch record.Level {
		case 10:
			groupId = config.GetConfig().CagNewRoleAct.Level10PrizeGroupId
		case 20:
			groupId = config.GetConfig().CagNewRoleAct.Level20PrizeGroupId
		case 30:
			groupId = config.GetConfig().CagNewRoleAct.Level30PrizeGroupId
		}
	}
	if amsId == "" || groupId == "" {
		log.ErrorContextf(ctx, "DoSendNewRolePrize amsId or groupId is empty, amsId: %v, groupId: %v",
			amsId, groupId)
		return code.ErrSystemError
	}

	gameName := constant.CAG_PRIZE_GAME_NAME
	var areaId int64
	id, ok := config.GetConfig().GameAreaIds[constant.GAMEID_CAG]
	if ok {
		areaId = id
	} else {
		log.ErrorContextf(ctx, "DoSendNewRolePrize GetGameAreaIds failed, gameId: %v", constant.GAMEID_CAG)
		return code.ErrSystemError
	}

	// 发放奖励
	defer func() {
		if err != nil {
			// TODO 告警
			log.ErrorContextf(ctx, "DoSendNewRolePrize fail, update status, "+
				"err: %v, id: %v, userId: %v", err, record.ID, record.UserID)
			// 发奖失败，更新记录
			err = cag_acvivity_repo.CreateRoleActivityClient.UpdateRecordStatus(ctx, record.ID, record.UserID,
				constant.CagRoleLevelPrizeStatusSendFail, record.Status)
		}

		if record.SendPrizeTimes >= 3 {
			// TODO 告警
			log.ErrorContextf(ctx, "DoSendNewRolePrize task send prize fail too many times, id: %v, userId: %v, times: %v",
				record.ID, record.UserID, record.SendPrizeTimes)
		}

		// 更新发奖次数
		cag_acvivity_repo.CreateRoleActivityClient.UpdateTaskSendPrizeTimes(ctx, record.ID,
			record.UserID, record.SendPrizeTimes+1)
	}()

	roleInfo := &game.RoleInfo{
		GameId:   constant.GAMEID_CAG,
		AreaId:   areaId,
		GameName: gameName,
	}

	req := &present_ams.SendOpenidAmsPresentReq{
		AmsId:      amsId,
		GroupId:    groupId,
		PackageNum: 1,
		RoleInfo:   roleInfo,
		Serial:     record.AmsSerialID,
		Openid:     record.OpenID,
		LangType:   record.LangType,
	}
	amsPresentProxy := present_ams.NewAmsClientProxy()
	rsp, err := amsPresentProxy.SendOpenidAmsPresent(ctx, req)
	if nil != err {
		log.ErrorContextf(ctx, "DoSendNewRolePrize err: %v, userId: %v, err_code: %v, err_msg: %v",
			err, record.UserID, errs.Code(err), err.Error())

		if errs.Code(err) == constant.SEND_PRIZE_ERR_CODE_HAS_PRIZE {
			log.ErrorContextf(ctx, "DoSendNewRolePrize SendOpenidAmsPresent user already send prize, record: %+v",
				record)
			updates := map[string]interface{}{
				"has_prize_flag": 1,
			}
			cag_acvivity_repo.CreateRoleActivityClient.UpdateInfoById(ctx, record.ID,
				record.UserID, updates)
		} else if errs.Code(err) == constant.SEND_PRIZE_ERR_CODE_LIMIT {
			log.ErrorContextf(ctx, "DoSendNewRolePrize SendOpenidAmsPresent user prize limit, record: %+v",
				record)
			updates := map[string]interface{}{
				"ams_prize_limit": 1,
			}
			cag_acvivity_repo.CreateRoleActivityClient.UpdateInfoById(ctx, record.ID,
				record.UserID, updates)
		} else {
			updates := map[string]interface{}{
				"ams_rsp_code": errs.Code(err),
			}
			cag_acvivity_repo.CreateRoleActivityClient.UpdateInfoById(ctx, record.ID,
				record.UserID, updates)
		}

		return code.ErrSystemError
	}
	if rsp.Serial != record.AmsSerialID {
		log.ErrorContextf(ctx, "DoSendNewRolePrize SendOpenidAmsPresent failed, userId: %v, amsSerialID: %v, rspSerial: %v",
			record.UserID, record.AmsSerialID, rsp.Serial)
		return code.ErrSystemError
	}

	// 更新状态
	err = cag_acvivity_repo.CreateRoleActivityClient.UpdateRecordStatus(ctx, record.ID, record.UserID,
		constant.CagRoleLevelPrizeStatusSendSuccess, record.Status)
	if nil != err { // 更新失败， 返回
		log.ErrorContextf(ctx, "DoSendNewRolePrize UpdateTaskStatus error:%v, id: %v, userId: %v",
			err, record.ID, record.UserID)
		return code.ErrSystemError
	}
	return nil
}

// CheckCagRoleCreate 检查cag角色是否创建
func CheckCagRoleCreate(ctx context.Context, lipOpenId string) (create bool, cagOpenId string, err error) {
	// 查询用户的cag游戏用户id
	cagOpenId, err = service.GetGameOpenIdByLipOpenId(ctx, constant.GAMEID_CAG, lipOpenId)
	if nil != err {
		log.ErrorContextf(ctx, "CheckCagRoleCreate error:%v, lipOpenId: %v", err, lipOpenId)
		return
	}
	if "" == cagOpenId {
		log.DebugContextf(ctx, "CheckCagRoleCreate cag openid is empty, lipOpenId: %v", lipOpenId)
		// 角色未创建
		return false, "", nil
	}

	// 检查cag游戏用户创建角色时间
	areaId := ""
	id, ok := config.GetConfig().GameAreaIds[constant.GAMEID_CAG]
	if ok {
		areaId = cast.ToString(id)
	}

	gameInfo := &viewmodel.CagUserGameInfoItem{}
	err = service.GetUserGameInfoByOpenid(ctx, constant.GAMEID_CAG, constant.IDIP_CMD_CAG_GET_USER_INFO, areaId, cagOpenId, gameInfo)
	if nil != err {
		log.ErrorContextf(ctx, "CheckCagRoleCreate get cag game user info error:%v, lipOpenId: %v, cagOpenId: %v",
			err, lipOpenId, cagOpenId)
		return
	}
	log.DebugContextf(ctx, "CheckCagRoleCreate cag game user info: %+v, lipOpenId: %v", gameInfo, lipOpenId)

	// 判断创建角色时间是否在活动开始之后
	if gameInfo.CreateTime == "" {
		// TODO 告警
		log.ErrorContextf(ctx, "CheckCagRoleCreate cag game user create time is empty, lipOpenId: %v, cagOpenId: %v",
			lipOpenId, cagOpenId)
		return false, cagOpenId, nil
	}

	return true, cagOpenId, nil
}
