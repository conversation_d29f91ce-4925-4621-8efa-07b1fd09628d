package writemessage

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/privacy"
	"trpc.publishing_application.standalonesite/app/model"
)

// 写入用户消息表
func SetUserMessage(msgData *model.Message, userIntlOpenid string, nikkeMessageType string) error {
	// 判断一下，如果用户开启了消息不接收那就is_read = 1
	var isRead = 0
	var searchType = constants.CommentMessageType
	privacySwitch, err := privacy.GetUserPrivacySwitch(context.Background(), userIntlOpenid)
	switch nikkeMessageType {
	case constants.CommentMessageCount:
		searchType = constants.CommentMessageType
		if err == nil && privacySwitch.MsgCommentNotify == 0 {
			isRead = 1
		}
		break
	case constants.FollowMessageCount:
		searchType = constants.FollowMessageType
		if err == nil && privacySwitch.MsgFollowNotify == 0 {
			isRead = 1
		}
		break
	case constants.LikeMessageCount:
		searchType = constants.LikeMessageType
		if err == nil && privacySwitch.MsgLikeNotify == 0 {
			isRead = 1
		}
		break
	case constants.SiteMessageCount:
		searchType = constants.SiteMessageType
		if err == nil && privacySwitch.MsgSystemNotify == 0 {
			isRead = 1
		}
		break
	}
	msgData.IsRead = isRead
	err = dao.MessageCreate(msgData)
	if err != nil {
		return err
	}
	// 把当前用户的评论消息数加1
	if isRead == 0 {
		redis.GetClient().HIncrBy(context.Background(), cache.GetMessageUnReadKey(userIntlOpenid), nikkeMessageType, 1)
	}
	// 删除消息列表缓存
	DeleteUserMessageCache(context.Background(), userIntlOpenid, searchType)
	return nil
}

func DeleteUserMessageCache(c context.Context, openid string, msgListType constants.GetMessageType) {
	//searchType := GetSearchType(int32(msgListType))
	//conditions := &dao.MessageConditions{
	//	ReceiverUserIntlOpenid: openid,
	//	Order: []*dao.OrderConditions{
	//		&dao.OrderConditions{
	//			Column: "id",
	//			IsDesc: true,
	//		},
	//	},
	//	Type: searchType,
	//}
	//_, _, err := dao.GetMessageCountAndID(conditions, openid)
	//if err != nil {
	//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("DeleteUserMessageCache | delete msg list cache failed to get data, openid: %s, msgListType: %d, err: %v", openid, msgListType, err)
	languageList := []string{"en", "ko", "ja", "zh", "zh-TW", ""}
	for _, language := range languageList {
		// 只删除首页
		myMsgRedisKey := cache.GetMyMessageInfosWithLangKey(openid, "", 10, int32(msgListType), language)
		redis.GetClient().Del(context.Background(), myMsgRedisKey)
		return
	}

	//}
	//totalPages := (totalRows / 10) + 1
	//var offset int64
	//for offset = 0; offset < totalPages; offset++ {
	//	var nextPageCursor string
	//	if offset > 0 {
	//		nextPageCursor, err = util.EncryptPageCursorI(ids[(offset*10)-1])
	//		if err != nil {
	//			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("DeleteUserMessageCache | delete msg list cache failed to create cursor, openid: %s, msgListType: %d, ids: %v, offset:%d, err: %v", openid, msgListType, ids, offset, err)
	//			return
	//		}
	//	}
	//	myMsgRedisKey := cache.GetMyMessageInfosKey(openid, nextPageCursor, 10, int32(msgListType))
	//	redis.GetClient().Del(context.Background(), myMsgRedisKey)
	//}

}

// 根据前端传递的类型统计好需要筛选的类型
func GetSearchType(paramType int32) []constants.MessageT {
	switch constants.GetMessageType(paramType) {
	case constants.CommentMessageType:
		return []constants.MessageT{constants.MsgTypeComment, constants.MsgTypeReply}
	case constants.FollowMessageType:
		return []constants.MessageT{constants.MsgTypeFollow}
	case constants.LikeMessageType:
		return []constants.MessageT{constants.MsgTypeCommentStar, constants.MsgTypeCommentReplyStar, constants.MsgTypeStar}
	case constants.SiteMessageType:
		return []constants.MessageT{
			constants.MsgTypeSiteMessage,
			constants.MsgTypeOfficialIgnorePost,
			constants.MsgTypeOfficialDeleteComment,
			constants.MsgTypeReportMessage,
			constants.MsgTypeOfficialDeletePost,
			constants.MsgTypeOfficialIgnoreUserName,
			constants.MsgTypeOfficialDeleteUserName,
			constants.MsgTypeOfficialIgnoreUserRemark,
			constants.MsgTypeOfficialDeleteUserRemark,
			constants.MsgTypeOfficialIgnoreUserName,
			constants.MsgTypeOfficialAccountMute,
		}
	default:
		return []constants.MessageT{constants.MsgTypeComment, constants.MsgTypeReply}
	}
}
