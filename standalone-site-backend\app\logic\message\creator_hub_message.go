package message

import (
	"context"
	"encoding/json"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/creatorhub"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/model"
)

type RetryWork struct {
	Work        *model.CreatorHubWorkKafkaItem
	AuditStatus int
	GameId      string
	AreaId      string
	RetryTime   int
}

//  1. 判断是否审核通过
//  2. 判断当前用户是否已绑定独立站用户
//     2.1. 若已绑定独立站用户，判断同步开关是否已打开 且 账号未冻结
//     2.1.1. 若已打开，同步内容
//     2.1.2. 若未打开，不同步内容
//     2.2. 若未绑定独立站用户，不处理
func CtreatorHubWorkMessageConsumeHandle(c context.Context, data *model.CreatorHubWorkKafkaData, retryTime int) error {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CtreatorHubWorkMessageConsumeHandle data: %+v", data)
	if data == nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CtreatorHubWorkMessageConsumeHandle data is nil")
		return nil
	}
	// 不通过，直接忽略
	if data.AuditStatus != 1 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CtreatorHubWorkMessageConsumeHandle audit status is not 1, data: %+v", data)
		return nil
	}
	// // 预发布环境，只处理10000
	// if trpc.GlobalConfig().Global.EnvName == "pre" {
	// 	if data.GameId != "10000" {
	// 		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CtreatorHubWorkMessageConsumeHandle game id is not 10000, data: %+v", data)
	// 		return nil
	// 	}
	// } else {

	// }
	if data.GameId != "16" {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CtreatorHubWorkMessageConsumeHandle game id is not 16, data: %+v", data)
		return nil
	}
	uids := []string{}
	for _, workItem := range data.WorkList {
		uids = append(uids, workItem.Uid)
	}
	bindInfos, err := dao.GetAllBoundInfosByChUids(uids)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CtreatorHubWorkMessageConsumeHandle get all bound infos by ch uids failed, err: %v, uids: %v", err, uids)
		return nil
	}
	for _, workItem := range data.WorkList {
		uid := workItem.Uid
		var curBindInfo *model.ChUserBind
		for _, bindInfo := range bindInfos {
			if bindInfo.ChUid == uid {
				if bindInfo.IsAutoSync == 1 && bindInfo.IsDel == 0 && bindInfo.AbnormalStatus == constants.CH_USER_STATUS_NORMAL {
					curBindInfo = bindInfo
					break
				}
			}
		}
		if curBindInfo == nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CtreatorHubWorkMessageConsumeHandle not support to sync work: %d uid: %s", workItem.WorkId, uid)
			continue
		} else {
			// 判断当前用户是否已经同步过，同步过直接跳过
			workPostInfo, _ := dao.GetWorkPostInfoByIntlOpenidAndWorkId(curBindInfo.IntlOpenid, workItem.WorkId)
			if workPostInfo != nil && workPostInfo.Model != nil && workPostInfo.Model.ID != 0 {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CtreatorHubWorkMessageConsumeHandle work has been synced, work id: %d, uid: %s", workItem.WorkId, uid)
				continue
			}
			// 判断用户是否被禁言
			// 验证是否被禁言了
			mute := formatted.GetUserMute(workPostInfo.IntlOpenid)
			if mute {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CtreatorHubWorkMessageConsumeHandle user has been muted, work id: %d, uid: %s", workItem.WorkId, uid)
				continue
			}
			retryListKey := cache.GetWorkAutoSyncRetryWorksListKey()
			// 拉取单条workid
			workInfo, err := creatorhub.GetWorkDetailInfoByWorkId(c, workItem.WorkId, "16", "asia")
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CtreatorHubWorkMessageConsumeHandle get work detail info by work id failed, err: %v, work id: %d", err, workItem.WorkId)
				retryWorkStr, err := json.Marshal(RetryWork{
					Work:        workItem,
					RetryTime:   retryTime,
					AuditStatus: data.AuditStatus,
					GameId:      data.GameId,
					AreaId:      data.AreaId,
				})
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CtreatorHubWorkMessageConsumeHandle marshal retry work failed, err: %v, work id: %d", err, workItem.WorkId)
				} else {
					redis.GetClient().SAdd(c, retryListKey, retryWorkStr)
				}
				continue
			} else {
				postUuid, err := creatorhub.PublishContent2DynamicSitePost(c, workInfo, curBindInfo.IntlOpenid, curBindInfo.SyncLanguage, curBindInfo.GameId, curBindInfo.AreaId)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CtreatorHubWorkMessageConsumeHandle publish content to dynamic site post failed, err: %v, work id: %d", err, workItem.WorkId)
					retryWorkStr, err := json.Marshal(RetryWork{
						Work:        workItem,
						RetryTime:   retryTime,
						AuditStatus: data.AuditStatus,
						GameId:      data.GameId,
						AreaId:      data.AreaId,
					})
					if err != nil {
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CtreatorHubWorkMessageConsumeHandle marshal retry work failed, err: %v, work id: %d", err, workItem.WorkId)
					} else {
						redis.GetClient().SAdd(c, retryListKey, retryWorkStr)
					}
					continue
				} else {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CtreatorHubWorkMessageConsumeHandle publish content to dynamic site post success, work id: %d, postuuid: %s", workItem.WorkId, postUuid)
				}
			}
		}
	}
	return nil
}

func RetryFailedWork(c context.Context) error {
	retryListKey := cache.GetWorkAutoSyncRetryWorksListKey()
	kafkaWorkRetryStrs, _ := redis.GetClient().SMembers(c, retryListKey).Result()
	if kafkaWorkRetryStrs == nil || len(kafkaWorkRetryStrs) == 0 {
		return nil
	}
	for _, kafkaWorkRetryStr := range kafkaWorkRetryStrs {
		kafkaWorkRetry := &RetryWork{}
		err := json.Unmarshal([]byte(kafkaWorkRetryStr), &kafkaWorkRetry)
		// err := redis.GetClient().Get(c, kafkaWorkRetryStr).Scan(kafkaWorkRetry)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CtreatorHubWorkMessageConsumeHandle get retry work failed, err: %v, work id: %d", err, kafkaWorkRetry.Work.WorkId)
			continue
		}
		if kafkaWorkRetry.RetryTime > 10 {
			// redis.GetClient().SRem(c, retryListKey, kafkaWorkRetryStr)
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CtreatorHubWorkMessageConsumeHandle retry work failed, work id: %d, retry time: %d", kafkaWorkRetry.Work.WorkId, kafkaWorkRetry.RetryTime)
			continue
		}
		err = CtreatorHubWorkMessageConsumeHandle(c, &model.CreatorHubWorkKafkaData{
			WorkList: []*model.CreatorHubWorkKafkaItem{
				kafkaWorkRetry.Work,
			},
			AuditStatus: kafkaWorkRetry.AuditStatus,
			GameId:      kafkaWorkRetry.GameId,
			AreaId:      kafkaWorkRetry.AreaId,
		}, kafkaWorkRetry.RetryTime+1)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CtreatorHubWorkMessageConsumeHandle retry failed work failed, err: %v, work id: %d", err, kafkaWorkRetry.Work.WorkId)
			continue
		}
		return nil
	}
	redis.GetClient().SRem(c, retryListKey, kafkaWorkRetryStrs)
	return nil
}
