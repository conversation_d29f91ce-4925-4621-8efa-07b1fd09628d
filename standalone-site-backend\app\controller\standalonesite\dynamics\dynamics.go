package dynamics

import (
	"context"
	"fmt"

	"trpc.publishing_application.standalonesite/app/logic/creatorhub"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/cloud"
	"trpc.publishing_application.standalonesite/app/logic/games"
	"trpc.publishing_application.standalonesite/app/logic/plate"
	"trpc.publishing_application.standalonesite/app/logic/report"
	"trpc.publishing_application.standalonesite/app/logic/socialmedia"
	"trpc.publishing_application.standalonesite/app/logic/translate"
	"trpc.publishing_application.standalonesite/app/logic/tweet"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/pkg/metadatadecode"

	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
)

type DynamicsImpl struct {
	pb.UnimplementedDynamics
}

// CreateUserInfo 初始化生成用户记录
func (s *DynamicsImpl) SayHello(ctx context.Context, req *pb.HelloReq) (*pb.HelloRsp, error) {
	rsp := &pb.HelloRsp{}
	return rsp, nil
}

func (s *DynamicsImpl) GetPostList(c context.Context, req *pb.GetPostListReq) (*pb.GetPostListRsp, error) {
	// 默认写死分页类型和分页数
	req.PageType = 0
	if req.Limit < 0 || req.Limit > 100 {
		req.Limit = 10
	}
	var err error
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	myOpenid := userAccount.Uid

	gameId, areaId := "", ""

	//gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	//if gameId == "" || areaId == "" {
	//	return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	//}
	// language := "zh"
	// myOpenid = "29080-12945745392039390084"

	var getPostListRsp *pb.GetPostListRsp

	// 限制只有指定的语言才支持，否则报错不支持
	if language != "en" && language != "ko" && language != "ja" && language != "zh" && language != "zh-TW" {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("The language is not supported language: %v\n", language)
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidLanguageParams, "The language is not supported")
		// return getPostListRsp, nil
	}

	// 如果是event或者是creatorhub就直接走特殊查询逻辑
	if req.PlateUniqueId == constants.PLATE_EVENT || req.PlateUniqueId == constants.PLATE_CREATORHUB {
		if req.PlateUniqueId == constants.PLATE_EVENT && language == "zh" {
			language = "zh-TW"
		}
		return tweet.GetExternalPosts(c, req, myOpenid, language, req.PlateUniqueId)
	}

	if req.SearchType == constants.SEARCH_TYPE_DEFAULT && req.OrderBy == constants.OrderByTypeDefault {
		getPostListRsp, err = tweet.GetIndexPosts(c, req, myOpenid, language, gameId, areaId)
	} else {
		getPostListRsp, err = tweet.GetPostListFromSearch(c, myOpenid, req, language, gameId, areaId)
	}
	if err != nil {
		return nil, err
	}

	// 如果列表为空则直接返回
	if len(getPostListRsp.List) == 0 {
		return getPostListRsp, nil
	}

	// 如果有登陆态则查询当前用户的对应状态：动态列表中的每条动态是否有收藏、点赞等
	if myOpenid != "" {
		if err = tweet.GetPostListMyIsOperation(c, getPostListRsp.List, myOpenid); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetPostListMyIsOperation err: %v\n", err)
		}
	}

	return getPostListRsp, nil
}

func (s *DynamicsImpl) GetUserPostList(c context.Context, req *pb.GetUserPostListReq) (*pb.GetUserPostListRsp, error) {
	// 默认写死分页类型和分页数
	req.PageType = 0
	req.Limit = 10
	var err error
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	var myOpenid string
	if userAccount.Uid != "" {
		myOpenid = userAccount.Uid
	}
	// myOpenid := "29080-12945745392039390084"
	// gameId := "16"
	// areaId := "global"

	gameId, areaId := "", ""

	//gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	//if gameId == "" || areaId == "" {
	//	return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "GetUserPostList x-common-param gameid or areaid Parameter error")
	//}

	var getPostListRsp *pb.GetUserPostListRsp
	getPostListRsp, err = tweet.GetUserPosts(c, req, myOpenid, language, gameId, areaId)

	if err != nil {
		return nil, err
	}

	// 如果列表为空则直接返回
	if len(getPostListRsp.List) == 0 {
		return getPostListRsp, nil
	}

	// 如果有登陆态则查询当前用户的对应状态：动态列表中的每条动态是否有收藏、点赞等
	// 个人中心的话也需要支持查看自己对应的状态
	if myOpenid != "" {
		if err = tweet.GetPostListMyIsOperation(c, getPostListRsp.List, myOpenid); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserPostList service.GetPostListMyIsOperation err: %v\n", err)
		}
	}

	// // 称号下发
	// if err = user.SetPostUserTitlePosts(c, respData.Items, language); err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetPostYserTitle err: %v\n", err)
	// }

	// // 游戏名下发
	// if err = games.SetPostsGameName(c, getPostListRsp.List, language); err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserPostList service.SetPostsGameName err: %v\n", err)
	// }

	return getPostListRsp, nil
}

func (s *DynamicsImpl) GetPost(c context.Context, req *pb.GetPostReq) (*pb.GetPostRsp, error) {
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	myOpenid := userAccount.Uid
	// openid := userAccount.IntlAccount.OpenId
	// myOpenid := "29080-12945745392039390084"

	if req.PostUuid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "post id is required")
	}

	postFormated, err := tweet.GetPostInfo(c, myOpenid, req.PostUuid, language, req.IsAllLanguage)
	if err != nil {
		return nil, err
	}

	if myOpenid != "" {
		// 查看是否点赞
		if postFormated.UpvoteCount != 0 {
			if postStarInfo, err := tweet.GetPostStar(req.PostUuid, myOpenid, true); err == nil && postStarInfo.Model != nil && postStarInfo.ID > 0 {
				postFormated.MyUpvote = &pb.MyUpvote{
					IsStar:     true,
					UpvoteType: int32(postStarInfo.StarType),
				}
			}
		}
		// 查看是否收藏
		if postFormated.CollectionCount != 0 {
			if _, err = tweet.GetPostCollection(req.PostUuid, myOpenid, false); err == nil {
				postFormated.IsCollection = true
			}
		}
		// // 查看是否评论
		// if postFormated.CommentCount != 0 {
		// 	if _, err = comment.GetComment(req.PostUuid, openid); err == nil {
		// 		postFormated.IsComment = true
		// 	}
		// }
		// 查看是否关注
		if collectionInfo, err := user.GetUserCollectionUser(myOpenid, postFormated.IntlOpenid, false); err == nil {
			postFormated.IsFollow = true
			postFormated.IsMutualFollow = collectionInfo.IsMutual == 1
		}

		// 完成任务进度

		tweet.CompletePostRelatedTask(c, 1, language, req.PostUuid)

	}

	// 下发称号
	// if err = user.SetPostUserTitle(c, postFormated.User, language); err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetPostUserTitle err: %v\n", err)
	// }

	// 游戏名下发
	// if err = games.SetPostGameName(c, postFormated, language); err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetPostGameName err: %v\n", err)
	// }

	// 创建浏览量
	go tweet.CreatePostBrowse(c, req.PostUuid)

	return postFormated, nil

}

func (s *DynamicsImpl) GetTagList(c context.Context, req *pb.GetTagListReq) (*pb.GetTagListRsp, error) {
	// 默认写死分页类型和分页数
	req.PageType = 0
	req.Limit = 0

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	tagFormated, err := tweet.GetTagListV2(c, req, language)

	if err != nil {
		return nil, err
	}

	return tagFormated, nil
}

func (s *DynamicsImpl) GetTag(c context.Context, req *pb.GetTagReq) (*pb.GetTagRsp, error) {
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	tagFormated, err := tweet.GetTag(c, req, language)

	if err != nil {
		return nil, err
	}

	return tagFormated, nil
}

func (s *DynamicsImpl) SearchTag(c context.Context, req *pb.SearchTagReq) (*pb.SearchTagRsp, error) {
	// 默认写死分页类型和分页数
	req.PageType = 0
	if req.Limit < 1 || req.Limit > 100 {
		req.Limit = 10
	}

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	tagFormated, err := tweet.SearchTagList(c, req, language)

	if err != nil {
		return nil, err
	}

	return tagFormated, nil
}

func (s *DynamicsImpl) DeletePost(c context.Context, req *pb.DeletePostReq) (*pb.DeletePostRsp, error) {
	var err error
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid
	// openid := userAccount.IntlAccount.OpenId
	// myOpenid := "29080-12945745392039390084"

	if req.PostUuid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "post id is required")
	}

	_, err = tweet.DeletePost(c, req.PostUuid, myOpenid, req.DelReason)

	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.DeletePost err: %v\n", err)
		return nil, err
	}
	go user.DeleteUserInfoCache(c, myOpenid)
	return &pb.DeletePostRsp{}, nil
}

func (s *DynamicsImpl) PostStar(c context.Context, req *pb.PostStarReq) (*pb.PostStarRsp, error) {
	var err error
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}

	// myOpenid := "29080-12945745392039390084"
	// gameId := "16"
	// areaId := "global"

	// 超出范围则设置为默认点赞类型
	if req.Type < 0 || req.Type > 20 {
		req.Type = 0
	}

	if req.PostUuid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "post id is required")
	}

	status, _, err := tweet.UserStarPost(c, req.PostUuid, req.Type, myOpenid, gameId, areaId, constants.PostLikeTye(req.LikeType))

	if err != nil {
		return nil, err
	}

	// 完成任务进度
	tweet.CompletePostRelatedTask(c, 2, "en", req.PostUuid)
	return &pb.PostStarRsp{Status: status}, nil
}

// 转发资讯
func (s *DynamicsImpl) PostForward(c context.Context, req *pb.PostForwardReq) (*pb.PostForwardRsp, error) {
	var err error
	//获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid

	if req.PostUuid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "post id is required")
	}

	//// 创建浏览量
	forwardCount, err := tweet.CreatePostForward(c, req.PostUuid, myOpenid)

	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePostForward err: %v", err)
		return nil, err
	}
	return &pb.PostForwardRsp{ForwardCount: forwardCount}, nil
}

func (s *DynamicsImpl) PostCollection(c context.Context, req *pb.PostCollectionReq) (*pb.PostCollectionRsp, error) {
	var err error
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	// myOpenid := "29080-12945745392039390084"

	if req.PostUuid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "post id is required")
	}

	status, _, err := tweet.UserPostCollection(c, req.PostUuid, myOpenid)

	if err != nil {
		return nil, err
	}
	return &pb.PostCollectionRsp{Status: status}, nil
}

func (s *DynamicsImpl) GetGames(c context.Context, req *pb.GetGamesReq) (*pb.GetGamesRsp, error) {
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	gamesList, err := games.GetGames(c, language, int(req.Offset), int(req.Limit), false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetGame err: %v\n", err)
		return nil, err
	}
	var gamesData = make([]*pb.GetGameRsp, 0, len(gamesList))
	for _, item := range gamesList {
		gamesData = append(gamesData, &pb.GetGameRsp{
			Id: item.ID,
			Language: &pb.GameLanguageFormated{
				Id:        item.GameLanguageFormated.ID,
				GameTId:   int32(item.GameLanguageFormated.GameId),
				Language:  item.GameLanguageFormated.Language,
				Name:      item.GameLanguageFormated.Name,
				Introduce: item.GameLanguageFormated.Introduce,
			},
			GameId:         item.GameId,
			EnAbbreviation: item.EnAbbreviation,
			Avatar:         item.Avatar,
			BgImagePc:      item.BgImagePc,
			BgImageH5:      item.BgImageH5,
			PosterImagePc:  item.PosterImagePc,
			PosterImageH5:  item.PosterImageH5,
			UserNumber:     item.UserNumber,
			HotNum:         item.HotNum,
			Qrcode:         item.QRCode,
		})
	}
	return &pb.GetGamesRsp{Games: gamesData}, nil
}
func (s *DynamicsImpl) GetGame(c context.Context, req *pb.GetGameReq) (*pb.GetGameRsp, error) {
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}
	gameFormated, err := games.GetGameByGameId(c, req.GameId, areaId, language, false)

	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetGame err: %v\n", err)
		return nil, err
	}
	return &pb.GetGameRsp{
		Id: gameFormated.ID,
		Language: &pb.GameLanguageFormated{
			Id:        gameFormated.GameLanguageFormated.ID,
			GameTId:   int32(gameFormated.GameLanguageFormated.GameId),
			Language:  gameFormated.GameLanguageFormated.Language,
			Name:      gameFormated.GameLanguageFormated.Name,
			Introduce: gameFormated.GameLanguageFormated.Introduce,
		},
		GameId:         gameFormated.GameId,
		EnAbbreviation: gameFormated.EnAbbreviation,
		Avatar:         gameFormated.Avatar,
		BgImagePc:      gameFormated.BgImagePc,
		BgImageH5:      gameFormated.BgImageH5,
		PosterImagePc:  gameFormated.PosterImagePc,
		PosterImageH5:  gameFormated.PosterImageH5,
		UserNumber:     gameFormated.UserNumber,
		HotNum:         gameFormated.HotNum,
		Qrcode:         gameFormated.QRCode,
	}, nil

}
func (s *DynamicsImpl) ContentReport(c context.Context, req *pb.ContentReportReq) (*pb.ContentReportRsp, error) {
	var err error
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid

	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}
	// myOpenid := "29080-12945745392039390084"
	// gameId := "16"
	// areaId := "global"
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	err = report.PostReportService(c, req, myOpenid, gameId, areaId, language)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.ContentReport err: %v\n", err)
		return &pb.ContentReportRsp{}, err
	}
	return &pb.ContentReportRsp{}, nil
}

func (s *DynamicsImpl) GetCosSts(c context.Context, req *pb.GetCosStsReq) (*pb.GetCosStsRsp, error) {
	var err error
	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}
	// gameId := "16"
	// areaId := "tw"
	getCosStsRsp, err := cloud.GetCosSts(c, gameId, areaId)
	if err != nil {
		return nil, err
	}
	return getCosStsRsp, nil
}

func (s *DynamicsImpl) GetUserPostCollectionList(c context.Context, req *pb.GetUserPostCollectionListReq) (*pb.GetUserPostCollectionListRsp, error) {

	// 默认写死分页类型和分页数
	req.PageType = 0
	var err error
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	var myOpenid string
	if userAccount.Uid != "" {
		myOpenid = userAccount.Uid
	}
	// openid := "2670566212109452541"
	// myOpenid := "29080-12945745392039390084"
	// gameId := "16"
	// areaId := "global"

	gameId, areaId := "", ""

	//gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	//if gameId == "" || areaId == "" {
	//	return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "GetUserPostCollectionList x-common-param gameid or areaid Parameter error")
	//}

	var getPostListRsp *pb.GetUserPostCollectionListRsp
	getPostListRsp, err = tweet.GetUserPostCollections(c, req, language, gameId, areaId, myOpenid)
	if err != nil {
		return nil, err
	}
	// 如果列表为空则直接返回
	if len(getPostListRsp.List) == 0 {
		return getPostListRsp, nil
	}

	// 如果有登陆态则查询当前用户的对应状态：动态列表中的每条动态是否有收藏、点赞等
	if myOpenid != "" {
		if err = tweet.GetPostListMyIsOperation(c, getPostListRsp.List, myOpenid); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserPostCollectionList service.GetPostListMyIsOperation err: %v\n", err)
		}
	}

	// // 称号下发
	// if err = user.SetPostUserTitlePosts(c, respData.Items, language); err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetPostYserTitle err: %v\n", err)
	// }

	// // 游戏名下发
	// if err = games.SetPostsGameName(c, getPostListRsp.List, language); err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserPostCollectionList service.SetPostsGameName err: %v\n", err)
	// }

	return getPostListRsp, nil
}

func (s *DynamicsImpl) GetPlateList(c context.Context, req *pb.GetPlateListReq) (*pb.GetPlateListRsp, error) {
	// 默认写死分页类型和分页数
	req.PageType = 0
	req.Limit = 10
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	list, err := plate.GetPlateList(c, req, language)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s *DynamicsImpl) GetVideoInfoByURL(ctx context.Context, req *pb.GetVideoInfoByURLReq) (rsp *pb.GetVideoInfoByURLRsp, err error) {
	gameId, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}
	collections, err := socialmedia.ParseVideoUrl(ctx, req, gameId, areaId)
	if err != nil {
		return nil, err
	}
	return collections, nil
}

func (s *DynamicsImpl) MovePost(c context.Context, req *pb.MovePostReq) (rsp *pb.MovePostRsp, err error) {
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	userAccount, _ := metadata.GetUserAccount(c)
	// 获取openid
	// 限制只有指定的语言才支持，否则报错不支持，ps: 24-11-6图片、图文可以移动版本,先写死en, 逻辑里面不修改资讯的语言
	if len(req.Language) > 0 && !(req.Language == "en" || req.Language == "ko" || req.Language == "ja" || req.Language == "zh" || req.Language == "zh-TW") {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MovePost The language is not supported language: %v\n", language)
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidLanguageParams, "The language is not supported")
	}
	rsp, err = tweet.MovePost(c, req, userAccount.Uid)
	return
}

func (s *DynamicsImpl) GetCreatorHubTaskList(c context.Context, req *pb.GetCreatorHubTaskListReq) (rsp *pb.GetCreatorHubTaskListRsp, err error) {
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	list, err := creatorhub.GetActivityList(c, req, language)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s *DynamicsImpl) TranslateContent(c context.Context, req *pb.TranslateContentReq) (rsp *pb.TranslateContentRsp, err error) {
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	if req.Language == "en" || req.Language == "ko" || req.Language == "ja" || req.Language == "zh" || req.Language == "zh-TW" {
		rsp, err = translate.TranslateContent(c, req, language, false)
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("TranslateContent The language is not supported language: %v\n", req.Language)
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidLanguageParams, "The language is not supported")
	}
	return rsp, err
}

func (s *DynamicsImpl) SendFriendRequest(c context.Context, req *pb.SendFriendRequestReq) (rsp *pb.SendFriendRequestRsp, err error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	rsp, err = tweet.SendNikkeFriendRequest(c, req, myOpenid, language)
	return rsp, err
}

func (s *DynamicsImpl) CheckIfUserHasPostedByNIKKEH5Page(c context.Context, req *pb.CheckIfUserHasPostedByNIKKEH5PageReq) (rsp *pb.CheckIfUserHasPostedByNIKKEH5PageRsp, err error) {
	rsp = &pb.CheckIfUserHasPostedByNIKKEH5PageRsp{}
	hasPosts, err := tweet.CheckIfUserHasPostedByNIKKEH5Page(c, req)
	if err != nil {
		return rsp, err
	}
	rsp.Status = hasPosts
	return rsp, nil
}

func (s *DynamicsImpl) GetGuildHotPost(c context.Context, req *pb.GetGuildHotPostReq) (rsp *pb.GetGuildHotPostRsp, err error) {
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	myOpenid := userAccount.Uid

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	guildId := fmt.Sprintf("%s_%d", req.GuildId, req.NikkeAreaId)
	return tweet.QueryGuildHotPostInfo(c, myOpenid, language, guildId)
}

func (s *DynamicsImpl) GetActivityPostTags(c context.Context, req *pb.GetActivityPostTagsReq) (rsp *pb.GetActivityPostTagsRsp, err error) {
	return tweet.GetActivityPostTagList(c)
}
