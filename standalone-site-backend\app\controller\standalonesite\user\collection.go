package user

import (
	"context"
	"trpc.publishing_application.standalonesite/app/dao"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/pkg/metadatadecode"
	"trpc.publishing_application.standalonesite/app/util"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/logic/user"
)

// 用户粉丝和关注列表

func (u *UserImpl) GetUserFans(c context.Context, req *pbUser.GetUserFansReq) (rsp *pbUser.GetUserFansRsp, err error) {
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	openid := userAccount.Uid
	// openid := userAccount.IntlAccount.OpenId
	// openid := "2670566212109452541"

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	if req.IntlOpenid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "user openid is required")
	}

	userInfo, err := user.GetUserInfoByOpenid(c, req.IntlOpenid, false)
	if err != nil || userInfo == nil || userInfo.Model == nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserInfoByOpenid err: %v\n", err)
		return
	}

	var nextPageCursor, previousPageCursor string
	conditions := &dao.UserCollectionConditions{
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}
	var fansList []*model.UserCollectionFormat
	if req.PageType == pbUser.UserPageType_PAGEDOWN {
		var idCursor int64
		// 如果是首页
		if req.NextPageCursor == "" {
			idCursor = 0
		} else {
			previousPageCursor = req.NextPageCursor
			idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
			}
			conditions.LtId = idCursor
		}
		fansList, err = user.GetUserFans(c, openid, userInfo.IntlOpenid, int(req.Limit), language, conditions, req.NextPageCursor)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserCollections err: %v\n", err)
			return
		}
		// 生成下一页的游标
		if len(fansList) > 0 {
			nextPageCursor, err = util.EncryptPageCursorI(fansList[len(fansList)-1].Id)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to create comments nextPageCursor")
			}
		}
	}

	rsp = &pbUser.GetUserFansRsp{
		List: make([]*pbUser.UserCollectionItem, 0, len(fansList)),
		PageInfo: &pbUser.UserPageInfo{
			PreviousPageCursor: previousPageCursor,
		},
	}
	for _, format := range fansList {
		rsp.List = append(rsp.List, &pbUser.UserCollectionItem{
			Id:             format.Id,
			UserInfo:       formatted.ReturnDynamicProtoUserInfoFormatted(format.User),
			IntlOpenid:     format.IntlOpenid,
			ToIntlOpenid:   format.ToIntlOpenid,
			IsMutualFollow: format.IsMutualFollow,
			IsFollow:       format.IsFollow,
		})
	}
	if len(rsp.List) == 0 || len(rsp.List) < int(req.Limit) {
		rsp.PageInfo.IsFinish = true
	} else {
		rsp.PageInfo.NextPageCursor = nextPageCursor
	}
	return
}

func (u *UserImpl) GetUserFollow(c context.Context, req *pbUser.GetUserFollowReq) (rsp *pbUser.GetUserFollowRsp, err error) {
	req.PageType = 0
	//req.Limit = 10
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	openid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	// openid := "2670566212109452541"

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	var addLimit = 1 //加量分页
	var limit = addLimit + int(req.Limit)
	var pageTotal int //当前页面的数量

	if req.IntlOpenid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "user openid is required")
	}

	userInfo, err := user.GetUserInfoByOpenid(c, req.IntlOpenid, false)
	if err != nil || userInfo == nil || userInfo.Model == nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserInfoByOpenid err: %v\n", err)
		return
	}
	var nextPageCursor, previousPageCursor string
	conditions := &dao.UserCollectionConditions{
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}
	var followList []*model.UserCollectionFormat
	if req.PageType == pbUser.UserPageType_PAGEDOWN {
		var idCursor int64
		// 如果是首页
		if req.NextPageCursor == "" {
			idCursor = 0
		} else {
			previousPageCursor = req.NextPageCursor
			idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
			}
			conditions.LtId = idCursor
		}
		followList, err = user.GetUserFollow(c, openid, userInfo.IntlOpenid, limit, language, conditions, req.NextPageCursor)

		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserFollow err: %v\n", err)
			return
		}
		// 生成下一页的游标
		if len(followList) > 0 {
			pageTotal = len(followList)
			if pageTotal == limit {
				// 切割当前评论数据
				followList = followList[0:(limit - addLimit)]
			}
			nextPageCursor, err = util.EncryptPageCursorI(followList[len(followList)-1].Id)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to create comments nextPageCursor")
			}
		}
	}
	rsp = &pbUser.GetUserFollowRsp{
		List:     make([]*pbUser.UserCollectionItem, 0, len(followList)),
		PageInfo: &pbUser.UserPageInfo{},
	}
	for _, format := range followList {
		rsp.List = append(rsp.List, &pbUser.UserCollectionItem{
			Id:             format.Id,
			UserInfo:       formatted.ReturnDynamicProtoUserInfoFormatted(format.User),
			IntlOpenid:     format.IntlOpenid,
			ToIntlOpenid:   format.ToIntlOpenid,
			IsMutualFollow: format.IsMutualFollow,
			IsFollow:       format.IsFollow,
		})
	}
	if len(rsp.List) == 0 || pageTotal < limit {
		rsp.PageInfo.IsFinish = true
	} else {
		rsp.PageInfo.NextPageCursor = nextPageCursor
	}
	rsp.PageInfo.PreviousPageCursor = previousPageCursor
	return
}

func (u *UserImpl) UserCollection(c context.Context, req *pbUser.UserCollectionReq) (rsp *pbUser.UserCollectionRsp, err error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid

	// myOpenid := "29080-12945745392039390084"
	if req.IntlOpenid == myOpenid {
		return
	}
	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}
	// gameId := "16"
	// areaId := "global"

	rsp, err = user.UserFollowOtherUser(c, myOpenid, req.IntlOpenid, gameId, areaId, false)
	if err != nil {
		return
	}

	user.DeleteUserInfoCache(c, req.IntlOpenid)
	user.DeleteUserInfoCache(c, myOpenid)
	//message.DeleteUserMessageCache(c, req.IntlOpenid)

	return rsp, nil
}
