// Package warframe TODO
package warframe

import (
	"context"
	"math"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	cnbotPb "git.woa.com/trpcprotocol/publishing_application/cnbot_operations"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_warframe_tmp"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/logic/warframe"
)

// WarframeImpl TODO
type WarframeImpl struct {
	pb.UnimplementedWarframeTmp
}

// GetCurrentUserWishes 获取当前用户的愿望列表和还可许愿个数
func (s *WarframeImpl) GetCurrentUserWishes(ctx context.Context, req *pb.GetCurrentUserWishesReq) (
	*pb.GetCurrentUserWishesRsp, error) {
	canWish, wishItem, err := warframe.GetCurrentUserWishes(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.GetCurrentUserWishesRsp{
		CanWish: canWish,
		HasWish: wishItem,
	}, nil
}

// GetCurrentUserHasShared 获取当前用户分享状态
func (s *WarframeImpl) GetCurrentUserHasShared(ctx context.Context, req *pb.GetCurrentUserHasSharedReq) (
	*pb.GetCurrentUserHasSharedRsp, error) {
	hasShared, err := warframe.GetCurrentUserHasShared(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.GetCurrentUserHasSharedRsp{
		HasShare: hasShared,
	}, nil
}

// RecordsUserShareStatus 记录用户分享状态
func (s *WarframeImpl) RecordsUserShareStatus(ctx context.Context, req *pb.RecordsUserShareStatusReq) (
	*pb.RecordsUserShareStatusRsp, error) {
	if err := warframe.RecordsUserShareStatus(ctx); err != nil {
		return nil, err
	}
	return &pb.RecordsUserShareStatusRsp{}, nil
}

// RecordsCurrentUserWishes 记录当前用户的愿望
func (s *WarframeImpl) RecordsCurrentUserWishes(ctx context.Context, req *pb.RecordsCurrentUserWishesReq) (
	*pb.RecordsCurrentUserWishesRsp, error) {
	if err := warframe.RecordsCurrentUserWishes(ctx, req.Wish); err != nil {
		return nil, err
	}
	return &pb.RecordsCurrentUserWishesRsp{}, nil
}

// CheckCurrentUserIsOldPlayer TODO
func (s *WarframeImpl) CheckCurrentUserIsOldPlayer(ctx context.Context, req *pb.CheckCurrentUserIsOldPlayerReq) (
	*pb.CheckCurrentUserIsOldPlayerRsp, error) {
	oldPlayer, err := warframe.CheckCurrentUserIsOldPlayer(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CheckCurrentUserIsOldPlayerRsp{
		OldPlayer: oldPlayer,
	}, nil
}

// GetAllWishTotalNumber 许愿选择人数
func (s *WarframeImpl) GetAllWishTotalNumber(ctx context.Context, req *pb.GetAllWishTotalNumberReq) (
	*pb.GetAllWishTotalNumberRsp, error) {
	wishTotalNumber, err := warframe.GetAllWishTotalNumber(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.GetAllWishTotalNumberRsp{
		AllWishItem: wishTotalNumber,
	}, nil
}

// GetShowNum TODO
func (s *WarframeImpl) GetShowNum(ctx context.Context, req *pb.GetShowNumReq) (rsp *pb.GetShowNumRsp, err error) {
	rsp = &pb.GetShowNumRsp{
		ReservationNum: 0,
		WishNumList:    []int32{},
	}
	timeNow := time.Now().Unix()
	hour := math.Floor(float64(timeNow-int64(req.BeginTimestamp)) / 3600)
	reservationNum := int64(hour*3100 + float64(req.FixNum) - float64(req.Multiplier)*hour*hour)
	wishNumList := []int32{0, 0, 0, 0, 0}
	realReservationNum := req.RealReservationNum
	if realReservationNum == 0 {
		realReservationNum = 1
	}
	for key, item := range req.AllWishList {
		wishNumList[key] = item * (int32(reservationNum) / realReservationNum)
	}
	rsp.ReservationNum = int32(reservationNum)
	rsp.WishNumList = wishNumList
	return
}

// ScheduleBindPresentBot TODO
func (s *WarframeImpl) ScheduleBindPresentBot(ctx context.Context, req *pb.ScheduleBindPresentBotReq) (
	rsp *pb.ScheduleBindPresentBotRsp, err error) {
	rsp = &pb.ScheduleBindPresentBotRsp{}
	err = warframe.ScheduleBindPresentBot(ctx, req.FsourceId, req.PresentConfig, req.BotUrl)
	return
}

// GetWinfromWishesPool 奖品池抽奖
func (s *WarframeImpl) GetWinfromWishesPool(ctx context.Context, req *pb.GetWinfromWishesPoolReq) (
	rsp *pb.GetWinfromWishesPoolRsp, err error,
) {
	rsp = &pb.GetWinfromWishesPoolRsp{}
	go warframe.GetWinfromWishesPool(ctx)
	return
}

// JoinWishesPool 加入奖品池
func (s *WarframeImpl) JoinWishesPool(ctx context.Context, req *pb.JoinWishesPoolReq) (
	rsp *pb.JoinWishesPoolRsp, err error,
) {
	rsp = &pb.JoinWishesPoolRsp{}
	proxy := cnbotPb.NewOperationsClientProxy()
	// 添加是否有游戏检验
	_, err = proxy.IsUserOwnGame(ctx, &cnbotPb.IsUserOwnGameReq{})
	if err != nil {
		return
	}
	// proxy := cnbotPb.NewOperationsClientProxy()
	// var IsPlayWarframeTodayRsp *cnbotPb.IsPlayWarframeTodayRsp
	// IsPlayWarframeTodayRsp, err = proxy.IsPlayWarframeToday(ctx, &cnbotPb.IsPlayWarframeTodayReq{})
	// if err != nil {
	// 	return
	// }
	// if !IsPlayWarframeTodayRsp.Status {
	// 	err = errs.NewCustomError(ctx, code.NoPlayWarFrameToday, "You not login warframe today")
	// 	return
	// }
	_, wishItem, err := warframe.GetCurrentUserWishes(ctx)
	if err != nil {
		return
	}
	log.WithFieldsContext(ctx, "log_type", "JoinWishesPool_GetCurrentUserWishes").Infof("wishItem: %v", wishItem)

	if wishItem.Default == 0 && wishItem.OldPlayer == 0 && wishItem.ShareActivity == 0 {
		err = errs.NewCustomError(ctx, code.NoJoinWishes, "You have not wishes")
	}

	if wishItem.Default != 0 {
		err = warframe.JoinWishesPool(ctx, int32(wishItem.Default))
		if err != nil {
			return
		}
	}
	if wishItem.OldPlayer != 0 {
		err = warframe.JoinWishesPool(ctx, int32(wishItem.OldPlayer))
		if err != nil {
			return
		}
	}
	if wishItem.ShareActivity != 0 {
		err = warframe.JoinWishesPool(ctx, int32(wishItem.ShareActivity))
		if err != nil {
			return
		}
	}
	return
}

// GetSelfPresentList 获取用户获奖记录
func (s *WarframeImpl) GetSelfPresentList(ctx context.Context, req *pb.GetSelfPresentListReq) (
	rsp *pb.GetSelfPresentListRsp, err error,
) {
	rsp = &pb.GetSelfPresentListRsp{}
	presentList, err := warframe.GetSelfPresentList(ctx)
	if err != nil {
		return
	}
	rsp.PresentList = presentList
	return
}

// GetAllPresentList 获取用户获奖记录
func (s *WarframeImpl) GetAllPresentList(ctx context.Context, req *pb.GetAllPresentListReq) (
	rsp *pb.GetAllPresentListRsp, err error,
) {
	rsp = &pb.GetAllPresentListRsp{}

	presentList, err := warframe.GetAllPresentListRedis(ctx)
	if err != nil {
		return
	}
	rsp.PresentList = presentList
	return
}

// DelAllPresentListRedis 删除所有奖品列表的redis删除
func (s *WarframeImpl) DelAllPresentListRedis(ctx context.Context, req *pb.DelAllPresentListRedisReq) (
	rsp *pb.DelAllPresentListRedisRsp, err error,
) {
	rsp = &pb.DelAllPresentListRedisRsp{}
	err = warframe.DelRedis(ctx, "warframe-all-present-list")
	if err != nil {
		return
	}
	return
}

// UpdateUserPlayTimeForever 更新许愿和进入奖池人游戏总时长
func (s *WarframeImpl) UpdateUserPlayTimeForever(ctx context.Context, req *pb.UpdateUserPlayTimeForeverReq) (
	rsp *pb.UpdateUserPlayTimeForeverRsp, err error,
) {
	rsp = &pb.UpdateUserPlayTimeForeverRsp{}
	go warframe.UpdatePlayTimeForever(ctx)
	return
}
