package df_tmp_common

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/publishing_marketing/game"
	"git.code.oa.com/trpcprotocol/publishing_marketing/present_ams"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/cache"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/common"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/model/df_activity"
	"trpc.act.logicial/app/mysql/df_activity_repo"
	"trpc.act.logicial/app/redis"
)

const UserHasPrizeCode = 400061 // 用户已经发放过奖励

// CheckGunActivity 检查拼枪活动
func CheckGunActivity(ctx context.Context, gameID string, activityID string) error {
	// 查询活动
	key := fmt.Sprintf("%s_%s", gameID, activityID)
	act, err := cache.GetGunActivityFromCache(ctx, key)
	if err != nil {
		log.ErrorContextf(ctx, "CheckGunActivity GetActivityFromCache err: %v, key: %v",
			err, key)
		return err
	}
	if nil == act { // 活动不存在
		return code.ErrActivityNotExists
	}
	nowTime := uint32(time.Now().Unix())
	if act.StartTimestamp > nowTime {
		return code.ErrActivityNotStart
	}
	if act.EndTimestamp < nowTime {
		return code.ErrActivityEnd
	}
	return nil
}

// GetHelpNumByGunId 获取拼枪助力人数
func GetHelpNumByGunId(gunId string) (int32, error) {
	numInfo := config.GetConfig().GunIdHelpNumMap
	if nil == numInfo {
		return 0, code.ErrSystemError
	}
	num, ok := numInfo[gunId]
	if ok {
		return num, nil
	}
	// 不存在
	return 0, code.ErrParamError
}

// GetMysqlTimeStamp 获取时间戳
func GetMysqlTimeStamp(timeStamp time.Time) int64 {
	// TODO mysql 是cst， 魔法棒服务是utc-0， 查询出来的时候把cst时间转成了相同的utc-0，导致时间戳比实际时间多了8小时
	// 本来应该在mysql连接配置上设置loc来设置，但是担心影响已有的功能。 因此此处手工转换！！！
	return timeStamp.Unix() - (3600 * 8)
}

// GetGunActShareUrl 获取拼枪分享链接
func GetGunActShareUrl(gameId, activityId, taskId string) string {
	shareUrl := config.GetConfig().GunActHelpUrl
	return fmt.Sprintf("%v?game_id=%v&activity_id=%v&help_id=%v", shareUrl, gameId, activityId, taskId)
}

// GetGameName 获取游戏名称
func GetGameName(gameId string) string {
	gameName := "projectd_oversea"
	if config.GetConfig().GameNames == nil {
		return gameName
	}
	name, ok := config.GetConfig().GameNames[gameId]
	if ok {
		gameName = name
	}
	return gameName
}

// SendGunActTaskPrize 发放拼枪任务奖励
func SendGunActTaskPrize(ctx context.Context, task *df_activity.GunActivityTask) (err error) {
	if nil == task {
		log.ErrorContextf(ctx, "SendGunActTaskPrize task is nil")
		return code.ErrSystemError
	}

	// 发奖加锁
	key := redis.GetGunActSendPrizeLockKey(task.TaskId)
	ok := redis.LockByKey(ctx, key, 10)
	if !ok {
		// TODO 告警
		log.ErrorContextf(ctx, "SendGunActTaskPrize LockByKey fail, key: %v", key)
		return code.ErrRequestFrequencyExceededLimitError
	}

	// 处理完解锁
	defer func() {
		redis.UnLockByKey(ctx, key)
	}()

	// 检查任务状态，判断是否允许发奖
	if task.Status != df_activity.GunTaskStatusGetPrizeFail &&
		task.Status != df_activity.GunTaskStatusWaitDistributePrize {
		// TODO 告警
		log.ErrorContextf(ctx, "SendGunActTaskPrize task status not allow to send, "+
			"taskId: %v, status: %v", task.TaskId, task.Status)
		return code.ErrSystemError
	}

	// 发奖
	defer func() {
		if err != nil {
			// TODO 告警
			log.ErrorContextf(ctx, "SendGunActTaskPrize fail, update status, "+
				"err: %v, taskId: %v", err, task.TaskId)
			// 发奖失败，更新记录
			df_activity_repo.GunActivityRepoClient.UpdateTaskStatus(ctx, task.TaskId,
				df_activity.GunTaskStatusGetPrizeFail, task.Status)
		}

		if task.SendPrizeTimes >= 3 {
			// TODO 告警
			log.ErrorContextf(ctx, "SendGunActTaskPrize task send prize fail too many times, taskId: %v, times: %v",
				task.TaskId, task.SendPrizeTimes)
		}

		// 更新发奖次数
		df_activity_repo.GunActivityRepoClient.UpdateTaskSendPrizeTimes(ctx, task.TaskId,
			task.SendPrizeTimes+1)
	}()

	var areaId int64 = 0
	id, ok := config.GetConfig().GameAreaIds[task.GameId]
	if ok {
		areaId = id
	}

	// 调用接口发送奖励
	roleInfo := &game.RoleInfo{
		GameId:   task.GameId,
		AreaId:   areaId,
		GameName: GetGameName(task.GameId),
	}
	// 根据枪械id获取奖品组id
	if nil == config.GetConfig().GunActGunAmsGroupIds {
		log.ErrorContextf(ctx, "SendGunActTaskPrize GunActGunAmsGroupIds is nil, taskId: %v", task.TaskId)
		return code.ErrSystemError
	}
	groupId, ok := config.GetConfig().GunActGunAmsGroupIds[task.GunId]
	if !ok {
		log.ErrorContextf(ctx, "SendGunActTaskPrize gunId prize not exist, "+
			"gunId: %v, taskId: %v", task.GunId, task.TaskId)
		return code.ErrSystemError
	}

	sendPrizeStatus := df_activity.GunTaskStatusGetPrizeFail // 初始化发奖失败
	startSendPrizeTime := config.GetConfig().GunActStartSendPrizeTime
	nowTime := time.Now().Unix()
	if startSendPrizeTime > uint32(nowTime) { // 未到发奖时间，不发奖，记录发奖失败，等到时间后自动重发
		log.DebugContextf(ctx, "SendGunActTaskPrize not "+
			"reach send time. nowTime: %v, startSendPrizeTime: %v, gunId: %v, taskId: %v",
			nowTime, startSendPrizeTime, task.GunId, task.TaskId)
		// 不返回错误， 记录发奖失败，等到到时间后开始发奖
		sendPrizeStatus = df_activity.GunTaskStatusGetPrizeFail
	} else {
		amsPresentProxy := present_ams.NewAmsClientProxy()
		req := &present_ams.SendOpenidAmsPresentReq{
			AmsId:      config.GetConfig().GunActAmsId,
			GroupId:    groupId,
			PackageNum: 1,
			RoleInfo:   roleInfo,
			Serial:     task.AmsSerialId,
			Openid:     task.OpenId,
			LangType:   task.LangType,
		}
		log.DebugContextf(ctx, "start send prize, nowTime: %v, startSendPrizeTime: %v, taskId: %v, "+
			"req: %v", nowTime, startSendPrizeTime, task.TaskId, req)

		rsp, err := amsPresentProxy.SendOpenidAmsPresent(ctx, req)
		if nil != err {
			log.ErrorContextf(ctx, "SendOpenidAmsPresent err: %v, taskId: %v, err_code: %v, err_msg: %v",
				err, task.TaskId, errs.Code(err), err.Error())
			if errs.Code(err) == UserHasPrizeCode {
				log.ErrorContextf(ctx, "SendOpenidAmsPresent user already send prize, taskId: %v, openid: %v, serial: %v",
					task.TaskId, task.OpenId, task.AmsSerialId)
				df_activity_repo.GunActivityRepoClient.UpdateTaskHasPrizeFlag(ctx, task.TaskId,
					df_activity.GunTaskHasPrizeFlagTrue)
			}
			return code.ErrSystemError
		}
		log.DebugContextf(ctx, "SendOpenidAmsPresent rsp, taskId: %v, rsp: %v", task.TaskId, rsp)
		if rsp.Serial != task.AmsSerialId {
			log.ErrorContextf(ctx, "SendOpenidAmsPresent serial not match, taskId: %v, "+
				"req serial: %v, rsp serial: %v", task.TaskId, task.AmsSerialId, rsp.Serial)
			return code.ErrSystemError
		}

		// 发奖成功
		log.DebugContextf(ctx, "SendGunActTaskPrize send prize success. taskId: %v", task.TaskId)
		sendPrizeStatus = df_activity.GunTaskStatusGetPrizeOk
	}

	// 更新状态
	err = df_activity_repo.GunActivityRepoClient.UpdateTaskStatus(ctx, task.TaskId,
		sendPrizeStatus, task.Status)
	if nil != err { // 更新失败， 返回
		log.ErrorContextf(ctx, "HelpGunActTaskProc UpdateTaskStatus error:%v, taskId: %v",
			err, task.TaskId)
		return code.ErrSystemError
	}
	// 上报tlog
	env := trpc.GlobalConfig().Global.EnvName
	extentContent := make(map[string]interface{})
	extentContent["env"] = env
	extentContent["open_id"] = task.OpenId
	extentContent["gun_id"] = task.GunId
	extentContent["ret"] = 0
	action := "moddedfirearms_backup_receive_gift"
	subAction := "cm_click"
	common.ReportTlog(ctx, action, subAction, cast.ToInt32(task.GameId), extentContent)
	return nil
}
