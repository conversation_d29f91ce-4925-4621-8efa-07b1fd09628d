package dao

import (
	"errors"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

func FetchPostLanguage(conditions *PostConditions, limit int, language string) ([]*model.Post, error) {
	var posts []*model.Post
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.PostLanguage{}).GetTableName(language))
	if limit > 0 {
		db = db.Limit(limit)
	}
	if len(conditions.PostUuids) > 0 {
		db = db.Where("post_uuid in ?", conditions.PostUuids)
	}
	if conditions.CompositeCondition != nil {
		db = db.Where(conditions.CompositeCondition.ToSQL())
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if len(conditions.Language) > 0 {
		db = db.Where("language in ?", conditions.Language)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if conditions.IsAudit > 0 {
		db = db.Where("is_audit = ?", conditions.IsAudit)
	}
	if conditions.PlateId > 0 {
		db = db.Where("plate_id = ?", conditions.PlateId)
	}
	if conditions.IsTop != "" {
		db = db.Where("is_top = ?", conditions.IsTop)
	}
	// createdOnMs
	if conditions.LtCreatedOnMs > 0 {
		db = db.Where("created_on_ms < ?", conditions.LtCreatedOnMs)
	}
	if conditions.PostHideCondition != nil {
		db = db.Where(conditions.PostHideCondition.ToSQL())
	}
	if conditions.IsHide {
		db = db.Where("is_hide = ?", 0)
	}

	if err = db.Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}

// 批量新增数据
func BatchInsertPostLanguage(data []*model.PostLanguage, language string) error {
	db := DB.SelectConnect("db_standalonesite").Table((&model.PostLanguage{}).GetTableName(language))
	return db.CreateInBatches(&data, 1000).Error
}

func GetPostLanguageRow(data *model.PostLanguageTemp, language string) (*model.PostLanguageTemp, error) {
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostLanguage{}).GetTableName(language)).Where("post_uuid = ?", data.PostUUID).FirstOrCreate(data).Error
	if err != nil {
		return nil, err
	}
	return data, nil
}

// 更新数据
func UpdatePostAllLanguage(postUuid string, data map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		for _, language := range constants.AllPostLanguages {
			// 这里更新会涉及到一部分表没有数据, 做一个兼容
			db := DB.SelectConnect("db_standalonesite").Table((&model.PostLanguage{}).GetTableName(language))
			var postLanguageData *model.PostLanguage
			err := db.Where("post_uuid = ?", postUuid).First(&postLanguageData).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				// 真的是报错了
				return err
			}
			if errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			err = db.Where("post_uuid = ?", postUuid).Updates(data).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// 更新某种语言下的数据
func UpdatePostLanguage(postUuid string, data map[string]interface{}, language string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostLanguage{}).GetTableName(language)).Where("post_uuid = ?", postUuid).Updates(data).Error
}

// 从某种语言切换到另外一种语言
func ChangePostLanguageUpdate(postUuid string, newLanguage string, oldLanguage string, plateId int32) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		// 查询旧的语言
		var oldPostLanguageInfo *model.PostLanguage
		err := tx.Table((&model.PostLanguage{}).GetTableName(oldLanguage)).Where("post_uuid = ?", postUuid).First(&oldPostLanguageInfo).Error
		if err != nil {
			return err
		}
		// 创建新的数据
		var newPostLanguageInfo = &model.PostLanguageTemp{}
		copier.Copy(&newPostLanguageInfo, oldPostLanguageInfo)
		if plateId > 0 {
			newPostLanguageInfo.PlateID = plateId
		}
		// id 重置
		newPostLanguageInfo.ID = 0
		// // create之前先判断是否存在记录，若存在记录则删除
		// newLanguageTableDB := tx.Table((&model.PostLanguage{}).GetTableName(newLanguage))
		// err = newLanguageTableDB.Where("post_uuid = ?", newPostLanguageInfo.ID).Delete(&model.PostLanguage{}).Error
		// if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// 	return err
		// }
		// 修改post content 数据
		err = ChangePostContentLanguage(postUuid, oldLanguage, newLanguage)
		if err != nil {
			return err
		}
		// post是否在新表中有记录
		newPostLanguageInfoRecord := &model.PostLanguage{}
		err = tx.Table((&model.PostLanguage{}).GetTableName(newLanguage)).Unscoped().Where("post_uuid = ?", postUuid).First(&newPostLanguageInfoRecord).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			// 未找到记录
			err = tx.Table((&model.PostLanguage{}).GetTableName(newLanguage)).Clauses(
				clause.OnConflict{
					Columns:   []clause.Column{{Name: "post_uuid"}, {Name: "is_del"}},
					DoUpdates: clause.AssignmentColumns(util.GetUpdateColumns(&model.PostLanguageTemp{}, "id", "post_uuid")),
				}).Create(&newPostLanguageInfo).Error
			if err != nil {
				return err
			}
		} else {
			// 找到记录
			newPostLanguageInfo.ID = newPostLanguageInfoRecord.ID
			// 赋值为0 无法更新到
			newPostLanguageInfo.IsDel = 0
			err = tx.Table((&model.PostLanguage{}).GetTableName(newLanguage)).Unscoped().Where("id = ?", newPostLanguageInfo.ID).Updates(&newPostLanguageInfo).Error
			err = tx.Table((&model.PostLanguage{}).GetTableName(newLanguage)).Unscoped().Where("id = ?", newPostLanguageInfo.ID).Update("is_del", 0).Error
			// 更新
			if err != nil {
				return err
			}
		}

		// 删除旧的数据
		oldPostLanguageInfo.IsDel = 1
		oldPostLanguageInfo.DeletedOn = time.Now().Unix()
		err = tx.Table((&model.PostLanguage{}).GetTableName(oldLanguage)).Where("post_uuid = ?", postUuid).Updates(&oldPostLanguageInfo).Error
		if err != nil {
			return err
		}
		return nil
	})
}
