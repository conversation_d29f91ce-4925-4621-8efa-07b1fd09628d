package model

import (
	"errors"
	"fmt"

	"trpc.publishing_application.standalonesite/app/util"
)

type Comment struct {
	*Model
	CommentUUID     string `gorm:"column:comment_uuid" json:"comment_uuid"`
	PostUUID        string `gorm:"column:post_uuid" json:"post_uuid"`
	IntlOpenid      string `gorm:"column:intl_openid" json:"intl_openid"`
	IsAudit         int8   `gorm:"column:is_audit" json:"is_audit"` // 是否已审核 1是 2不是
	Type            int32  `gorm:"column:type" json:"type"`         // 评论类型 1: 评论;2:回复
	ReplyUUID       string `gorm:"column:reply_uuid" json:"reply_uuid"`
	Reply2ReplyUUID string `gorm:"column:reply2reply_uuid" json:"reply2reply_uuid"`
	GameId          string `gorm:"column:game_id" json:"game_id"`
	AreaId          string `gorm:"column:area_id" json:"area_id"`
	CreatedOnMs     int64  `gorm:"column:created_on_ms" json:"created_on_ms"` //创建时间微秒
	Language        string `gorm:"column:language" json:"language"`
	CommentBubbleId int64  `gorm:"column:comment_bubble_id" json:"comment_bubble_id"` // 评论气泡id
	PosStatus       int    `gorm:"column:pos_status" json:"pos_status"`               // 位置状态 1:(CMS 操作)置顶, 2:(CMS操作)置底，3: CMS取消置顶/置底状态(已用，先保留)； 4:(版主操作)置顶，5：(版主操作)取消置顶；6: 贴主置顶  7：贴主取消置顶
	PosSetTime      int64  `gorm:"column:pos_set_time" json:"pos_set_time"`           // 操作置顶或置底的秒级时间戳
	HotNum          int64  `gorm:"column:hot_num" json:"hot_num"`                     // 评论的热度值
}

type CommentContent struct {
	ID           int64  `json:"id"`
	CommentUUID  string `json:"comment_uuid"`
	IntlOpenid   string `json:"intl_openid"`
	AtIntlOpenid string `json:"at_intl_openid"`
	Title        string `json:"title"`
	Content      string `json:"content"`
	PicUrls      string `json:"pic_urls"`
	Sort         int64  `json:"sort"`
	UpvoteCount  int64  `json:"upvote_count"`
}

type CommentFormated struct {
	CommentUUID string                       `json:"comment_uuid"`
	PostUUID    string                       `json:"post_uuid"`
	IntlOpenid  string                       `json:"intl_openid"`
	Type        int8                         `json:"type"` // 评论类型 1: 评论;2:回复
	Title       string                       `json:"title"`
	Content     string                       `json:"content"`
	PicUrls     []string                     `json:"pic_urls"`
	UpvoteCount int64                        `json:"upvote_count"`
	GameId      string                       `json:"game_id"`
	AreaId      string                       `json:"area_id"`
	IsAudit     int8                         `json:"is_audit"`
	CreatedOn   int64                        `json:"created_on"`
	ModifiedOn  int64                        `json:"modified_on"`
	User        *UserFormated                `json:"user"`
	Replies     *CommentReplyFormatedForPage `json:"replies"`
	IsStar      bool                         `json:"is_star"`
	CanDelete   bool                         `json:"can_delete"` // 提供给前端判断是否是自己的评论，如果是则不展示删除按钮
	CanReport   bool                         `json:"can_report"` // 提供给前端判断是否是自己的评论，如果是则不展示举报按钮
}

type CommentMessageFormated struct {
	CommentUUID string   `json:"comment_uuid"`
	PostUUID    string   `json:"post_uuid"`
	IntlOpenid  string   `json:"intl_openid"`
	Title       string   `json:"title"`
	Content     string   `json:"content"`
	PicUrls     []string `json:"pic_urls"`
	ReplyId     int64    `json:"reply_id"`
	IsAudit     int8     `json:"is_audit"`
	GameId      string   `json:"game_id"`
	AreaId      string   `json:"area_id"`
	CreatedOn   int64    `json:"created_on"`
	ModifiedOn  int64    `json:"modified_on"`
}

type CommentMyFormated struct {
	CommentUUID string         `json:"comment_uuid"`
	PostUUID    string         `json:"post_uuid"`
	IntlOpenid  string         `json:"intl_openid"`
	Type        int8           `json:"type"`
	IsAudit     int8           `json:"is_audit"`
	Title       string         `json:"title"`
	Content     string         `json:"content"`
	PicUrls     []string       `json:"pic_urls"`
	ReplyId     int64          `json:"reply_id"`
	Reply       *CommentReply  `json:"reply"`
	GameId      string         `json:"game_id"`
	AreaId      string         `json:"area_id"`
	CreatedOn   int64          `json:"created_on"`
	ModifiedOn  int64          `json:"modified_on"`
	Post        *PostFormatted `json:"post"`
	User        *UserFormated  `json:"user"`
}

type MyCommentFormated struct {
	CommentUUID string         `json:"comment_uuid"`
	PostUUID    string         `json:"post_uuid"`
	PostUrl     string         `json:"post_url"`
	IntlOpenid  string         `json:"intl_openid"`
	Type        int8           `json:"type"` // 评论类型 1: 动态评论;2:动态回复;3:资讯评论;4:资讯回复
	Title       string         `json:"title"`
	Content     string         `json:"content"`
	PicUrls     []string       `json:"pic_urls"`
	IsAudit     int8           `json:"is_audit"`     // 是否已审核 1是 2不是
	UpvoteCount int64          `json:"upvote_count"` // 点赞数
	Post        *PostFormatted `json:"post"`
	User        *UserFormated  `json:"user"`
	GameId      string         `json:"game_id"`
	AreaId      string         `json:"area_id"`
	CreatedOn   int64          `json:"created_on"`
	ModifiedOn  int64          `json:"modified_on"`
}

func (c *Comment) TableName() string {
	return "p_comment"
}

type ESComment struct {
	Id               int64         `json:"id"`
	IntlOpenid       string        `json:"intl_openid"`
	CommentUuid      string        `json:"comment_uuid"`
	PostUuid         string        `json:"post_uuid"`
	Type             int32         `json:"type"`            // 评论类型 1: 评论;2:回复
	TextRiskLevel    int64         `json:"text_risk_level"` // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	TextRiskType     int64         `json:"text_risk_type"`  // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	PicRiskLevel     int64         `json:"pic_risk_level"`  // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	PicRiskType      int64         `json:"pic_risk_type"`   // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	Status           int64         `json:"status"`          //状态  1:未处理 2:已发布 3:已忽略
	AuditUser        string        `json:"audit_user"`
	AuditIntroduce   string        `json:"audit_introduce"`
	AuditOn          int64         `json:"audit_on"`
	Title            string        `json:"title"`
	Content          string        `json:"content"`
	PicUrls          string        `json:"pic_urls"`
	CreatedOn        int64         `json:"created_on"`
	ModifiedOn       int64         `json:"modified_on"`
	IsDel            int32         `json:"is_del"`
	DeletedOn        int64         `json:"deleted_on"`
	GameId           string        `json:"game_id"`
	AreaId           string        `json:"area_id"`
	CreatedOnMs      int64         `json:"created_on_ms"` //创建时间微秒
	Language         string        `json:"language"`
	DelReason        int32         `json:"del_reason"`        // 删除原因
	DelType          int32         `json:"del_type"`          // 删除类型
	MachineStatus    int32         `json:"machine_status"`    // 机审状态：0-未处理1-审核通过2-审核异常
	ArtificialStatus int32         `json:"artificial_status"` // 人审状态：0-未处理1-审核通过2-审核拒绝
	PosStatus        int32         `json:"pos_status"`        // 位置状态 1:置顶, 2:置底
	PosSetTime       int64         `json:"pos_set_time"`      // 操作置顶或置底的秒级时间戳
	Sort             []interface{} `json:"-"`                 // 排序列表 无需参与json
}

// GetSubMeterTable 计算用户分表
func (c *Comment) GetCommentSubMeterTable(openid string) (string, error) {
	suffixId := util.ExtractSurplusByOpenid(openid, 2)
	if suffixId < 0 {
		return "", errors.New("get user table name failed")
	}
	tableName := fmt.Sprintf("%s_%d", "p_comment_content", suffixId)
	return tableName, nil
}
