package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/model"
)

// 批量关注
func BatchFollowed(waitingCreatedCollections []*model.UserCollection, waitingReFollowsWithoutMutual []int64, waitingReFollowsWithMutual []int64, collections []*model.UserCollection) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		allUserStatesId := []string{}
		for _, collection := range collections {
			allUserStatesId = append(allUserStatesId, collection.IntlOpenid, collection.ToIntlOpenid)
		}
		allUserStates, err := GetUserStateByUserOpenids(allUserStatesId)
		if err != nil {
			return err
		}
		if len(waitingCreatedCollections) > 0 {
			err = UserCollectionsCreate(tx, waitingCreatedCollections)
			if err != nil {
				return err
			}
		}
		if len(waitingReFollowsWithMutual) > 0 {
			err = UseCollectionsUpdate(tx, waitingReFollowsWithMutual, 1)
			if err != nil {
				return err
			}
		}
		if len(waitingReFollowsWithoutMutual) > 0 {
			err = UseCollectionsUpdate(tx, waitingReFollowsWithoutMutual, 0)
			if err != nil {
				return err
			}
		}
		// return errors.New("some error")
		for _, userState := range allUserStates {
			for _, collection := range collections {
				// 关注其他人
				if userState.IntlOpenid == collection.IntlOpenid && collection.ToIntlOpenid != collection.IntlOpenid {
					// 关注数+1
					userState.FollowNum++
					continue
				}
			}
			for _, collection := range collections {
				// 被关注对象
				if userState.IntlOpenid == collection.ToIntlOpenid && collection.IntlOpenid != collection.ToIntlOpenid {
					// 取消关注数+1
					userState.FansNum++
					continue
				}
			}
			err := UpdateUserFollowsFansNumber(tx, userState.IntlOpenid, userState.FollowNum, userState.FansNum)
			if err != nil {
				return err
			}
		}
		// 更新es
		for _, userState := range allUserStates {
			toDoc := map[string]interface{}{
				"follow_num": userState.FollowNum,
				"fans_num":   userState.FansNum,
			}
			EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, userState.IntlOpenid, toDoc)
		}
		EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
		return nil
	})
}
