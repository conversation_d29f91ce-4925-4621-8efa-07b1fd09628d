package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

type AvatarCondition struct {
	GameId string
	Order  []*OrderConditions
}

func GetAvatarList(conditions *AvatarCondition) ([]*model.Avatar, error) {
	var avatars []*model.Avatar
	tx := DB.SelectConnect("db_standalonesite").Table((&model.Avatar{}).TableName())
	if conditions.GameId != "" {
		tx = tx.Where("game_id = ?", conditions.GameId)
	}
	if len(conditions.Order) > 0 {
		tx = tx.Order(AssembleOrder(conditions.Order))
	}
	err := tx.Where("is_del = 0").Find(&avatars).Error
	if err != nil {
		return nil, err
	}
	return avatars, nil
}

func AvatarGameGetList() ([]*model.AvatarGame, error) {
	var avatars []*model.AvatarGame
	err := DB.SelectConnect("db_standalonesite").Debug().Table((&model.AvatarGame{}).TableName()).Where("is_del = ? ", 0).Order("modified_on DESC").Find(&avatars).Error
	if err != nil {
		return nil, err
	}
	return avatars, nil
}
