// Package base 基础
package base

import (
	"time"

	"gorm.io/plugin/soft_delete"
	"trpc.act.logicial/app/model"
)

// BaseTotalModel 抽奖表
type BaseTotalModel struct {
	model.AppModel
}

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名
func (BaseTotalModel) TableName() string {
	return "base_account_total_log"
}

// BaseTotalLogModel 抽奖记录表
type BaseTotalLogModel struct {
	BaseTotalModel
	ID          int                   `gorm:"type:int(11);column:id;not null"`
	Fday        time.Time             `gorm:"type:date();column:Fday;not null"`
	StorageKey  string                `gorm:"type:varchar(32);column:storage_key;''"`
	Uid         string                `gorm:"type:varchar(32);column:uid;''"`
	AccountType int32                 `gorm:"type:tinyint(1);column:account_type;0"`
	FsourceId   string                `gorm:"type:varchar(32);column:Fsource_id;null"`
	TagId       string                `gorm:"type:varchar(30);column:tag_id;not null"`
	TotalNum    int32                 `gorm:"type:int(11);column:total_num;not null;0"`
	TodayNum    int32                 `gorm:"type:int(11);column:today_num;not null;0"`
	TotalUseNum int                   `gorm:"type:int(11);column:total_use_num;not null;0"`
	TodayUseNum int                   `gorm:"type:int(11);column:today_use_num;not null;0"`
	TotalValue  int                   `gorm:"type:int(11);column:total_value;not null;0"`
	TodayValue  int                   `gorm:"type:int(11);column:today_value;not null;0"`
	Status      int                   `gorm:"type:tinyint(4);column:status;not null"`
	DeletedAt   soft_delete.DeletedAt `gorm:"softDelete:milli;column:deleted_at"`
}

// Invitation invitation_log_$id表
type Invitation struct {
	model.AppModel
	ID                 int64  `gorm:"type:int(11);column:id;primary_key"`
	UID                string `gorm:"type:varchar(64);column:uid"` // 用户ID
	AccountType        int32  `gorm:"type:tinyint(4);column:account_type;0"`
	InviteeUid         string `gorm:"type:varchar(64);column:invitee_uid"` // 被邀请人ID
	InviteeAccountType int32  `gorm:"type:tinyint(4);column:invitee_account_type;0"`
	FsourceId          string `gorm:"type:varchar(255);column:Fsource_id;not null"` // 页面id
	Status             int    `gorm:"type:tinyint(4);column:status;0"`              // 0:log 1:success
}

// AddParamStruct 传入类型
type AddParamStruct struct {
	FsourceID  string // 来源ID
	StorageKey string // 键值
	Type       int    // 存储方式 1：单次可修改；2：日限、总限
	TotalLimit int32  // 总限
	DayLimit   int32  // 日限
	Tag        string // 标记
	Value      int    // 总积分
}

// AddWeeklyMissionParam TODO
type AddWeeklyMissionParam struct {
	FsourceID        string  // 来源ID
	StorageKey       string  // 键值
	Type             int     // 存储方式 1：单次可修改；2：日限、总限
	Tag              string  // 标记
	PeriodicTimeList []int64 // 周期时间
}

// GetParamStruct 参数
type GetParamStruct struct {
	FsourceID  string // 来源ID
	StorageKey string // 键值
	Tag        string // 标记
	Status     int32  // 状态
}
