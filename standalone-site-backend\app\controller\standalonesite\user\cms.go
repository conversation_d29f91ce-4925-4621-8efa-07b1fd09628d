package user

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/user"
)

func (u *UserImpl) CMSGetUserList(c context.Context, req *pb.CMSGetUserListReq) (*pb.CMSGetUserListRsp, error) {
	if req.Limit < 0 || req.Limit > 100 {
		req.Limit = 10
	}
	userList, err := user.CMSGetUserInfoList(c, req)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CMSGetUserInfoList err: %v\n", err)
		return nil, err
	}
	return userList, nil
}

func (u *UserImpl) CMSGetUserAuditList(c context.Context, req *pb.CMSGetUserAuditListReq) (*pb.CMSGetUserAuditListRsp, error) {
	if req.Limit < 0 || req.Limit > 100 {
		req.Limit = 10
	}
	userList, err := user.CMSGetUserAuditInfoList(c, req)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CMSGetUserAuditList err: %v\n", err)
		return nil, err
	}
	return userList, nil
}

func (u *UserImpl) CMSReviewUserInfo(c context.Context, req *pb.CMSReviewUserInfoReq) (*pb.CMSReviewUserInfoRsp, error) {
	err := user.CMSReviewUserInfo(c, req, false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CMSReviewUserInfo err: %v\n", err)
		return &pb.CMSReviewUserInfoRsp{}, err
	}
	return &pb.CMSReviewUserInfoRsp{}, nil
}

func (u *UserImpl) CMSSetMuteUser(c context.Context, req *pb.CMSSetMuteUserReq) (*pb.CMSSetMuteUserRsp, error) {
	err := user.CMSSetMuteUserInfo(c, req)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CMSReviewUserInfo err: %v\n", err)
		return &pb.CMSSetMuteUserRsp{}, err
	}
	return &pb.CMSSetMuteUserRsp{}, nil
}

func (u *UserImpl) CMSSetAdminUser(c context.Context, req *pb.CMSSetAdminUserReq) (*pb.CMSSetAdminUserRsp, error) {
	err := user.CMSSetAdminUserInfo(c, req)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CMSReviewUserInfo err: %v\n", err)
		return &pb.CMSSetAdminUserRsp{}, err
	}
	return &pb.CMSSetAdminUserRsp{}, nil
}

func (u *UserImpl) CMSSetAuthUser(c context.Context, req *pb.CMSSetAuthUserReq) (*pb.CMSSetAuthUserRsp, error) {
	err := user.CMSSetAuthUserInfoV2(c, req)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CMSReviewUserInfo err: %v\n", err)
		return &pb.CMSSetAuthUserRsp{}, err
	}
	return &pb.CMSSetAuthUserRsp{}, nil
}

func (u *UserImpl) CMSUserWhiteList(c context.Context, req *pb.CMSUserWhiteListReq) (*pb.CMSUserWhiteListRsp, error) {

	return &pb.CMSUserWhiteListRsp{}, nil
}

func (u *UserImpl) CMSUpdateAvatarCache(c context.Context, req *pb.CMSUpdateAvatarCacheReq) (*pb.CMSUpdateAvatarCacheRsp, error) {
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		user.RemoveAvatarCache(context.Background())
	}()
	return &pb.CMSUpdateAvatarCacheRsp{}, nil
}

func (u *UserImpl) CMSUpdateAvatarPendantCache(c context.Context, req *pb.CMSUpdateAvatarPendantCacheReq) (*pb.CMSUpdateAvatarPendantCacheRsp, error) {
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		user.RemoveAllUserAvatarPendantCache(context.Background())
	}()
	return &pb.CMSUpdateAvatarPendantCacheRsp{}, nil
}

func (u *UserImpl) CMSSetCertificationUserLanguages(c context.Context, req *pb.CMSSetCertificationUserLanguagesReq) (*pb.CMSSetCertificationUserLanguagesRsp, error) {
	rsp, err := user.SetCertificationUserLanguages(c, req)
	return rsp, err
}

// TODO 评论气泡 CMS更新评论气泡缓存
func (u *UserImpl) CMSUpdateCommentBubbleCache(c context.Context, req *pb.CMSUpdateCommentBubbleCacheReq) (*pb.CMSUpdateCommentBubbleCacheRsp, error) {
	user.GetAllCommentBubbleList(c, true, true)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		user.RemoveAllUserCommentBubbleCache(context.Background())
	}()
	return &pb.CMSUpdateCommentBubbleCacheRsp{}, nil
}

func (u *UserImpl) CMSSetUserDemotion(c context.Context, req *pb.CMSSetUserDemotionReq) (*pb.CMSSetUserDemotionRsp, error) {
	err := user.CMSSetUserDemotion(c, req.IntlOpenids, req.EndTime, int(req.ActionType), req.UpdateUser)
	if err != nil {
		return nil, err
	}
	return &pb.CMSSetUserDemotionRsp{}, nil
}
