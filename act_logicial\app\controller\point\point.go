// Package point TODO
package point

import (
	"context"

	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_point"
	pointLogicial "trpc.act.logicial/app/logic/point"
)

// PointImpl TODO
type PointImpl struct{}

// AdjustUserPoints 调整当前用户积分
func (s *PointImpl) AdjustUserPoints(ctx context.Context, req *pb.AdjustUserPointsReq) (
	rsp *pb.AdjustUserPointsRsp, err error) {
	rsp = &pb.AdjustUserPointsRsp{}
	point, err := pointLogicial.AdjustUserPoints(ctx, req.ActionType, req.Point, req.Tag, req.FsourceId)
	if err != nil {
		return
	}
	rsp.TotalPoint = point
	return
}

// GetUserPointsList 获取当前用户积分记录列表
func (s *PointImpl) GetUserPointsList(ctx context.Context, req *pb.GetUserPointsListReq) (
	rsp *pb.GetUserPointsListRsp, err error) {
	rsp = &pb.GetUserPointsListRsp{}
	pointsList, total, err := pointLogicial.GetUserPointsList(ctx, req.FsourceId, req.Tag, req.PageSize, req.PageNum)
	if err != nil {
		return
	}
	rsp.PointsList = pointsList
	rsp.Total = total
	return
}

// GetUserPointsTotal 获取当前用户总积分
func (s *PointImpl) GetUserPointsTotal(ctx context.Context, req *pb.GetUserPointsTotalReq) (
	rsp *pb.GetUserPointsTotalRsp, err error) {
	rsp = &pb.GetUserPointsTotalRsp{}
	total, err := pointLogicial.GetUserPointsTotal(ctx, req.FsourceId)
	if err != nil {
		return
	}
	rsp.PointTotal = total
	return
}
