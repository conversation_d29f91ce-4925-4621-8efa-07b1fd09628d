package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func TagCollectionCreate(collection *model.TagCollection) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.TagCollection{}).TableName()).Omit("Tag").Create(&collection).Error

	return err
}

func TagCollectionDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.TagCollection{}).TableName()).Omit("Tag").Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}
