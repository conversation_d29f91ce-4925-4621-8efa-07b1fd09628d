package model

import (
	"fmt"
	"strconv"

	"gorm.io/gorm"
	"gorm.io/plugin/soft_delete"
)

type Post struct {
	*Model
	PostUUID          string `json:"post_uuid"`
	IntlOpenid        string `json:"intl_openid"`
	PlateID           int32  `json:"plate_id"` // 板块id
	Language          string `json:"language"`
	Type              int32  `json:"type"` // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	IsTop             int32  `json:"is_top"`
	TopSort           int32  `json:"top_sort"` // 顶置顺序值，越小越靠前
	TopOn             int64  `json:"top_on"`
	IsAudit           int32  `json:"is_audit"`   // 是否已审核 1是 2不是
	Visibility        int32  `json:"visibility"` // 可见性 0公开 1私密 2好友可见
	SocialmediaPostId string `json:"socialmedia_post_id"`
	LatestRepliedOn   int64  `json:"latest_replied_on"`
	GameId            string `json:"game_id"`
	AreaId            string `json:"area_id"`
	CreatedOnMs       int64  `json:"created_on_ms"` //创建时间微秒
	Platform          string `json:"platform"`      //社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
	PublishOn         int64  `json:"publish_on"`    // 定时发布时间
	IsOfficial        int32  `json:"is_official"`   // 是否是官方发帖
	DelType           int32  `json:"del_type"`      // 删除类型，isdel > 0 这个字段才生效
	DelReason         int32  `json:"del_reason"`    // 删除原因
	IsHide            int32  `json:"is_hide"`       // 是否隐藏0-否1-是
}

// 为了可以传创建时间
type EventPost struct {
	ID                int64                 `gorm:"primary_key" json:"id"`
	CreatedOn         int64                 `json:"created_on"`
	ModifiedOn        int64                 `json:"modified_on"`
	DeletedOn         int64                 `json:"deleted_on"`
	IsDel             soft_delete.DeletedAt `gorm:"softDelete:flag" json:"is_del"`
	PostUUID          string                `json:"post_uuid"`
	IntlOpenid        string                `json:"intl_openid"`
	PlateID           int32                 `json:"plate_id"` // 板块id
	Language          string                `json:"language"`
	Type              int32                 `json:"type"` // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	IsTop             int32                 `json:"is_top"`
	TopSort           int32                 `json:"top_sort"` // 顶置顺序值，越小越靠前
	TopOn             int64                 `json:"top_on"`
	IsAudit           int32                 `json:"is_audit"`   // 是否已审核 1是 2不是
	Visibility        int32                 `json:"visibility"` // 可见性 0公开 1私密 2好友可见
	SocialmediaPostId string                `json:"socialmedia_post_id"`
	LatestRepliedOn   int64                 `json:"latest_replied_on"`
	GameId            string                `json:"game_id"`
	AreaId            string                `json:"area_id"`
	PublishOn         int64                 `json:"publish_on"`  // 定时发布时间
	IsOfficial        int32                 `json:"is_official"` // 是否是官方发帖
	IsHide            int32                 `json:"is_hide"`     // 是否隐藏0-否1-是
}

type ESPost struct {
	PostUuid          string  `json:"post_uuid"`
	IntlOpenid        string  `json:"intl_openid"`
	PlateID           int32   `json:"plate_id"` // 板块id
	Language          string  `json:"language"`
	Type              int32   `json:"type"` // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	Title             string  `json:"title"`
	Content           string  `json:"content"`
	ContentSummary    string  `json:"content_summary"`
	PicUrls           string  `json:"pic_urls"`
	CommentCount      int64   `json:"comment_count"`
	CollectionCount   int64   `json:"collection_count"`
	UpvoteCount       int64   `json:"upvote_count"`
	UpvoteMap         string  `json:"upvote_map"`
	BrowseCount       int64   `json:"browse_count"`
	ForwardCount      int32   `json:"forward_count"`
	PowerNum          float32 `json:"power_num_float"`
	IsTop             int32   `json:"is_top"`
	TopSort           int32   `json:"top_sort"`
	TopOn             int64   `json:"top_on"`
	IsEssence         int32   `json:"is_essence"`
	EssenceOn         int64   `json:"essence_on"`
	HotNum            int32   `json:"hot_num"`
	IsOriginal        int32   `json:"is_original"`
	OriginalUrl       string  `json:"original_url"`
	OriginalReprint   int8    `json:"original_reprint"`
	SocialmediaPostId string  `json:"socialmedia_post_id"`
	LatestRepliedOn   int64   `json:"latest_replied_on"`
	CreatedOn         int64   `json:"created_on"`
	ModifiedOn        int64   `json:"modified_on"`
	GameId            string  `json:"game_id"`
	AreaId            string  `json:"area_id"`
	Platform          string  `json:"platform"`
	ExtInfo           string  `json:"ext_info"`
	Tags              []int64 `json:"tags"`
	IsAudit           int32   `json:"is_audit"`        // 是否已审核 1是 2不是
	TextRiskLevel     int64   `json:"text_risk_level"` // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	TextRiskType      int64   `json:"text_risk_type"`  // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	PicRiskLevel      int64   `json:"pic_risk_level"`  // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	PicRiskType       int64   `json:"pic_risk_type"`   // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	AuditStatus       int64   `json:"audit_status"`    //状态  1:未处理 2:已发布 3:已忽略
	AuditUser         string  `json:"audit_user"`
	AuditIntroduce    string  `json:"audit_introduce"`
	AuditOn           int64   `json:"audit_on"`
	IsDel             int32   `json:"is_del"`
	CreatorHubRankID  int32   `json:"creatorhub_rank_id"`
	CreatorHubTaskID  int32   `json:"creatorhub_task_id"`
	CreatedOnMs       int64   `json:"created_on_ms"`     //创建时间微秒
	PublishOn         int64   `json:"publish_on"`        // 定时发布时间
	IsOfficial        int32   `json:"is_official"`       // 是否是官方发帖
	DelReason         int32   `json:"del_reason"`        // 删除原因
	DelType           int32   `json:"del_type"`          // 删除类型
	MachineStatus     int32   `json:"machine_status"`    // 机审状态：0-未处理1-审核通过2-审核异常
	ArtificialStatus  int32   `json:"artificial_status"` // 人审状态：0-未处理1-审核通过2-审核拒绝
	IsHide            int32   `json:"is_hide"`           // 是否隐藏0-否1-是
	DemotionNum       int64   `json:"demotion_num"`      // 降权值
}

type ESPostAudit struct {
	ID               int32   `json:"id"`
	PostActionType   int32   `json:"post_action_type"`
	PostUuid         string  `json:"post_uuid"`
	IntlOpenid       string  `json:"intl_openid"`
	PlateID          int64   `json:"plate_id"` // 板块id
	Language         string  `json:"language"`
	Platform         string  `json:"platform"`
	Type             int32   `json:"type"` // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	Title            string  `json:"title"`
	Content          string  `json:"content"`
	ContentSummary   string  `json:"content_summary"`
	PicUrls          string  `json:"pic_urls"`
	ExtInfo          string  `json:"ext_info"`
	CreatedOn        int64   `json:"created_on"`
	ModifiedOn       int64   `json:"modified_on"`
	GameId           string  `json:"game_id"`
	AreaId           string  `json:"area_id"`
	Tags             []int64 `json:"tags"`
	TextRiskLevel    int64   `json:"text_risk_level"` // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	TextRiskType     int64   `json:"text_risk_type"`  // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	PicRiskLevel     int64   `json:"pic_risk_level"`  // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	PicRiskType      int64   `json:"pic_risk_type"`   // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	Status           int64   `json:"status"`          //状态  1:未处理 2:已发布 3:已忽略
	AuditUser        string  `json:"audit_user"`
	AuditIntroduce   string  `json:"audit_introduce"`
	AuditOn          int64   `json:"audit_on"`
	IsDel            int32   `json:"is_del"`
	MachineStatus    int32   `json:"machine_status"`    // 机审状态：0-未处理1-审核通过2-审核异常
	ArtificialStatus int32   `json:"artificial_status"` // 人审状态：0-未处理1-审核通过2-审核拒绝
}

type PostFormatted struct {
	// ID               int64  `json:"id"`
	PostUUID        string        `json:"post_uuid"`
	IntlOpenid      string        `json:"intl_openid"`
	PlateID         int64         `json:"plate_id"` // 板块id
	Language        string        `json:"language"`
	Type            int32         `json:"type"` // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	IsTop           int32         `json:"is_top"`
	TopSort         int32         `json:"top_sort"` // 顶置顺序值，越小越靠前
	TopOn           int64         `json:"top_on"`
	IsAudit         int32         `json:"is_audit"`
	LatestRepliedOn int64         `json:"latest_replied_on"`
	GameId          string        `json:"game_id"`
	GameName        string        `json:"game_name"`
	AreaId          string        `json:"area_id"`
	PowerNum        int           `json:"power_num"`
	IsEssence       int           `json:"is_essence"`
	EssenceOn       int64         `json:"essence_on"`
	CommentCount    int64         `json:"comment_count"`
	CollectionCount int64         `json:"collection_count"`
	UpvoteCount     int64         `json:"upvote_count"`
	BrowseCount     int64         `json:"browse_count"`
	ForwardCount    int64         `json:"forward_count"`
	IsOriginal      int32         `json:"is_original"`
	OriginalUrl     string        `json:"original_url"`
	OriginalReprint int32         `json:"original_reprint"`
	Title           string        `json:"title"`
	Content         string        `json:"content"`
	PicUrls         []string      `json:"pic_urls"`
	User            *UserFormated `json:"user"`
	IsStar          bool          `json:"is_star"`
	IsComment       bool          `json:"is_comment"`
	IsCollection    bool          `json:"is_collection"`
	IsFollow        bool          `json:"is_follow"`
	PublishOn       int64         `json:"publish_on"`  // 定时发布时间
	IsOfficial      int32         `json:"is_official"` // 是否是官方发帖
	IsHide          int32         `json:"is_hide"`     // 是否隐藏0-否1-是
}

func (p *Post) TableName() string {
	return "p_post"
}

func (p *Post) AfterCreate(tx *gorm.DB) (err error) {
	// 获取当前新增数据的自增ID
	id := p.ID

	// 将ID转换为6位字符串，不足6位前面用0代替，超过6位截取最后6位
	idStr := fmt.Sprintf("%06d", id)
	if len(idStr) > 6 {
		idStr = idStr[len(idStr)-6:]
	}

	// 将AnotherField的后6位替换为6位id数
	createdOnMsStr := fmt.Sprintf("%06d", p.CreatedOnMs)
	if len(createdOnMsStr) > 6 {
		createdOnMsStr = createdOnMsStr[:len(createdOnMsStr)-6] + idStr
	} else {
		createdOnMsStr = idStr
	}

	// 将字符串转换回整数
	anotherFieldInt, err := strconv.ParseInt(createdOnMsStr, 10, 64)
	if err != nil {
		return err
	}

	// 更新AnotherField字段
	p.CreatedOnMs = anotherFieldInt
	tx.Save(p)
	return nil
}
