// Package df_bhd TODO
package df_bhd

import (
	"context"
	"strconv"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_bhd"
	dfBhd "trpc.act.logicial/app/logic/df_bhd"
)

// DfBhdImpl TODO
type DfBhdImpl struct {
	pb.UnimplementedDfBhd
}

// MapIdMap TODO
var MapIdMap = []string{
	"10001", "10003", "10006", "10007", "10005",
	"10001", "10003", "10006", "10007", "10005",
}

// SyncBHDSpeedRank 同步黑鹰排行榜
func (s *DfBhdImpl) SyncBHDSpeedRank(ctx context.Context, req *pb.SyncBHDSpeedRankReq) (
	rsp *pb.SyncBHDSpeedRankRsp, err error,
) {
	rsp = &pb.SyncBHDSpeedRankRsp{}
	go dfBhd.SyncBHDSpeedRank(ctx)
	return
}

// GetBHDSpeedRank 获取黑鹰排行榜
func (s *DfBhdImpl) GetBHDSpeedRank(ctx context.Context, req *pb.GetBHDSpeedRankReq) (
	rsp *pb.GetBHDSpeedRankRsp, err error,
) {
	rsp = &pb.GetBHDSpeedRankRsp{}
	mapId := MapIdMap[req.Level-1]
	rankType := "total"
	if req.Level > 5 {
		rankType = "sol"
	}
	rsp.TotalRankPlayerNum = 0

	totalPlayNum, err := dfBhd.GetRedis(ctx, "df-bhd-speed-total-num-10007-total")
	if err != nil {
		return
	}
	if totalPlayNum != "" {
		num, err := strconv.ParseInt(totalPlayNum, 10, 64)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", "GetBHDSpeedRank_redis_totalPlayNum").Infof("totalPlayNum: %v, err: %v",
				totalPlayNum, err)
			return rsp, err
		}
		rsp.TotalRankPlayerNum = num
	}
	solPlayNum, err := dfBhd.GetRedis(ctx, "df-bhd-speed-total-num-10007-sol")
	if err != nil {
		return
	}
	if solPlayNum != "" {
		num, err := strconv.ParseInt(solPlayNum, 10, 64)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", "GetBHDSpeedRank_redis_solPlayNum").Infof("solPlayNum: %v, err: %v",
				solPlayNum, err)
			return rsp, err
		}
		rsp.TotalRankPlayerNum += num
	}

	rankList, err := dfBhd.GetBHDSpeedRankRedis(ctx, mapId, rankType)
	if err != nil {
		return
	}

	rsp.RankList = rankList

	_, err = metadata.GetUserAccount(ctx)
	if err != nil {
		rsp.SelfItem = &pb.BHDRankItem{}
		err = nil
		return
	}

	dateStr, err := dfBhd.GetDateStr(ctx)
	if err != nil {
		return
	}

	selfItem, err := dfBhd.GetSelfBhdSpeedData(ctx, mapId, dateStr, rankType)
	if err != nil {
		return
	}
	rsp.SelfItem = selfItem
	return
}

// GetSelfBHDSpeedRankNum TODO
func (s *DfBhdImpl) GetSelfBHDSpeedRankNum(ctx context.Context, req *pb.GetSelfBHDSpeedRankNumReq) (
	rsp *pb.GetSelfBHDSpeedRankNumRsp, err error,
) {
	rsp = &pb.GetSelfBHDSpeedRankNumRsp{}
	mapId := MapIdMap[req.Level-1]
	dateStr, err := dfBhd.GetDateStr(ctx)
	if err != nil {
		return
	}
	rankType := "total"
	if req.Level > 5 {
		rankType = "sol"
	}
	selfItem, err := dfBhd.GetSelfBhdSpeedData(ctx, mapId, dateStr, rankType)
	if err != nil {
		return
	}
	rsp.Rank = selfItem.PlaytimeRank
	return
}

// SyncAddLottery TODO
func (s *DfBhdImpl) SyncAddLottery(ctx context.Context, req *pb.SyncAddLotteryReq) (
	rsp *pb.SyncAddLotteryRsp, err error,
) {
	rsp = &pb.SyncAddLotteryRsp{}
	err = dfBhd.SyncAddLottery(ctx)
	if err != nil {
		return
	}
	return
}
