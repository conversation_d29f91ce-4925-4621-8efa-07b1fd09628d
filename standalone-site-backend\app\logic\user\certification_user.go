package user

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	user_pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/model"
)

// 设置认证用户多语言
// 0.判断是否为认证用户，非认证用户无需处理
// 1. 若初始表中不存在，将数据写入初始表中
// 2. 更新数据库内数据
// 3. 更新ES数据
// 4. 更新redis缓存
func SetCertificationUserLanguages(c context.Context, req *user_pb.CMSSetCertificationUserLanguagesReq) (rsp *user_pb.CMSSetCertificationUserLanguagesRsp, err error) {
	rsp = &user_pb.CMSSetCertificationUserLanguagesRsp{}
	// 区分出官方认证、机构认证、创作者认证
	// 创作者认证的帐号不支持修改昵称和个签的多语言版本，cms仅展示。只支持修改头像
	// 只有官方认证和机构认证的帐号支持多语言昵称和个签的编辑能力
	authOpenids, creatorOpenids, err := filterAuthUsers(c, req.IntlOpenids)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetCertificationUserLanguages filterAuthUsers err: %v\n", err)
		return nil, errs.NewCustomError(c, code.SetCertificationUserLanguageFailed, "SetCertificationUserLanguages failed")
	}
	if len(authOpenids) == 0 && len(creatorOpenids) == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetCertificationUserLanguages authOpenids is empty")
		return rsp, nil
	}
	if len(authOpenids) > 0 {
		// 昵称和签名五种语言必填
		if len(req.Languages) != 10 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetCertificationUserLanguages five language data required")
			return nil, errs.NewCustomError(c, code.FiveLanguageDataRequiredError, "SetCertificationUserLanguages failed")
		}
		err = updateDBCertificationUserLanguages(c, req, authOpenids)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetCertificationUserLanguages err: %v\n", err)
			return nil, errs.NewCustomError(c, code.SetCertificationUserLanguageFailed, "SetCertificationUserLanguages failed")
		}
		err = updateESCertificationUserLanguages(c, req, authOpenids)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SetCertificationUserLanguages err: %v\n", err)
			return nil, errs.NewCustomError(c, code.SetCertificationUserLanguageFailed, "SetCertificationUserLanguages failed")
		}
		// 清理用户信息缓存
		for _, intlOpenid := range authOpenids {
			cleanUserInfo(intlOpenid)
		}
	}
	// 处理创作者认证的账号，只修改头像
	if len(creatorOpenids) > 0 {
		for _, creatorOpenid := range creatorOpenids {
			currentTime := time.Now().Unix()
			UpdateUserContentInfo(c, creatorOpenid, "", "", req.Avatar, false)
			// 更新缓存
			cleanUserInfo(creatorOpenid)
			doc := map[string]interface{}{}
			doc["avatar"] = req.Avatar
			doc["avatar_on"] = currentTime
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, creatorOpenid, doc)
		}
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	return rsp, nil
}

// 移除认证用户多语言
// 将用户数据恢复到记录在初始表中的数据
func RemoveCerificationUserLanguages(c context.Context, intlOpenids []string) error {
	if len(intlOpenids) == 0 {
		return nil
	}
	now := time.Now()
	// 1. 先清除数据
	err := dao.DeleteAllCerticationUserLanguages(intlOpenids, constants.GAMEID_30054, constants.AREAID_global)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveCerificationUserLanguages DeleteAllCerticationUserLanguages err: %v\n", err)
		return err
	}
	// 2. 清除es数据
	for _, intlOpenid := range intlOpenids {
		doc := map[string]interface{}{}
		doc["certification_user_languages"] = ""
		doc["avatar"] = ""
		doc["avatar_on"] = ""
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
	}
	// 3. 查询初始表数据
	userInfos, err := dao.GetInitInfosByOpenids(intlOpenids, constants.GAMEID_30054, constants.AREAID_global)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveCerificationUserLanguages GetInitInfosByOpenids err: %v\n", err)
		return err
	}
	if len(userInfos) == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveCerificationUserLanguages GetInitInfosByOpenids userInfo is empty")
		return nil
	}
	// 4. 用初始表赋值数据
	for _, intlOpenid := range intlOpenids {
		for _, userInfo := range userInfos {
			if userInfo.IntlOpenid == intlOpenid {
				// 更新db
				userData, err := GetUserInfoByOpenid(c, intlOpenid, true)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveCerificationUserLanguages GetInitInfosByOpenids userInfo is empty")
				}
				userData.Avatar = userInfo.AvatarUrl
				userData.AvatarOn = now.Unix()
				userData.Remark = userInfo.Remark
				userData.RemarkOn = now.Unix()
				userData.Username = userInfo.Username
				err = UpdateUserInfo(c, userData)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveCerificationUserLanguages GetInitInfosByOpenids userInfo is empty")
				}
				// 更新es
				doc := map[string]interface{}{}
				doc["avatar"] = userInfo.AvatarUrl
				doc["avatar_on"] = now.Unix()
				doc["remark"] = userInfo.Remark
				doc["remark_on"] = now.Unix()
				doc["username"] = userInfo.Username
				dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
			}
		}
	}
	// 清理用户信息缓存
	for _, intlOpenid := range intlOpenids {
		cleanUserInfo(intlOpenid)
	}
	return nil
}

// c端设置单语言
func SetCerificationUserSingleLanguage(c context.Context, intlOpenid string, language string, avatar string, remark string, userName string) error {
	// 1.需要先判断用户是否为认证用户，只有官方认证和机构认证的帐号支持多语言昵称和个签的编辑能力
	// authType := formatted.GetUserAuth(intlOpenid)
	// if authType != constants.USER_AUTH_TYPE_OFFICIAL && authType != constants.USER_AUTH_TYPE_MECHANISM {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetCerificationUserSingleLanguage authType err: %v\n", authType)
	// 	return nil
	// }
	languages := make([]*user_pb.UserInfoLanguageItem, 0)
	languages = append(languages, &user_pb.UserInfoLanguageItem{
		Language: language,
		Type:     1,
		Desc:     userName,
	})
	languages = append(languages, &user_pb.UserInfoLanguageItem{
		Language: language,
		Type:     2,
		Desc:     remark,
	})

	req := &user_pb.CMSSetCertificationUserLanguagesReq{
		IntlOpenids: []string{intlOpenid},
		Avatar:      avatar,
		Languages:   languages,
	}
	err := updateDBCertificationUserLanguages(c, req, []string{intlOpenid})
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetCerificationUserSingleLanguage err: %v\n", err)
		return errs.NewCustomError(c, code.SetCertificationUserLanguageFailed, "SetCertificationUserLanguages failed")
	}
	err = updateESCertificationUserLanguages(c, req, []string{intlOpenid})
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetCerificationUserSingleLanguage err: %v\n", err)
		return errs.NewCustomError(c, code.SetCertificationUserLanguageFailed, "SetCertificationUserLanguages failed")
	}
	return nil
}

func cleanUserInfo(userIntlOpenid string) {
	c := context.Background()
	DeleteUserInfoCache(c, userIntlOpenid)
	// 认证多语言缓存
	certificationKey := cache.GetCertificationUserLanguageListCacheKey(userIntlOpenid)
	// 从缓存列表中移除
	redis.GetClient().SRem(c, certificationKey, userIntlOpenid)
}

// 将认证用户插入至初始认证用户表中
func insertIntoInitialCertificationUserInfo(c context.Context, intlOpenids []string) error {
	initDatas, err := dao.GetInitInfosByOpenids(intlOpenids, "30054", "global")
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("insertIntoInitialCertificationUserInfo err: %v\n", err)
		return err
	}
	// 获取未在初始表中的用户数据，写入
	initUserDatas := make([]*model.InitAuthUserData, 0)
	for _, openid := range intlOpenids {
		// 已存在，不处理
		isFound := false
		for _, initData := range initDatas {
			if initData.IntlOpenid == openid {
				isFound = true
				break
			}
		}
		if !isFound {
			curUserInfo, err := GetUserInfoWithCache(c, openid, true)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("insertIntoInitialCertificationUserInfo err: %v\n", err)
				return err
			}
			initUserDatas = append(initUserDatas, &model.InitAuthUserData{
				IntlOpenid: openid,
				AvatarUrl:  curUserInfo.Avatar,
				Remark:     curUserInfo.Remark,
			})
		}
	}
	if len(initUserDatas) > 0 {
		err = dao.BatchInsertInitAuthUserDatas(initUserDatas)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("insertIntoInitialCertificationUserInfo err: %v\n", err)
			return err
		}
	}
	return nil
}

func filterAuthUsers(c context.Context, intlOpenids []string) ([]string, []string, error) {
	// 1. 过滤出认证用户
	authOpenids := make([]string, 0)
	creatorOpenids := make([]string, 0)
	for _, openid := range intlOpenids {
		authType := formatted.GetUserAuth(openid)
		if authType > 0 {
			if authType == constants.USER_AUTH_TYPE_CREATOR {
				creatorOpenids = append(creatorOpenids, openid)
			} else {
				authOpenids = append(authOpenids, openid)
			}
		}
	}
	return authOpenids, creatorOpenids, nil
}

func isAuthUser(c context.Context, intlOpenid string) (bool, error) {
	authType := formatted.GetUserAuth(intlOpenid)
	return authType > 0, nil
}

// 更新数据库内数据
func updateDBCertificationUserLanguages(c context.Context, req *user_pb.CMSSetCertificationUserLanguagesReq, authIntlOpenids []string) error {
	languages := make([]*model.CertificationUserLanguage, 0)
	now := time.Now()
	for _, openid := range authIntlOpenids {
		// 更新头像
		userData, err := GetUserInfoByOpenid(c, openid, true)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("updateDBCertificationUserLanguages GetUserInfoByOpenid err: %v\n", err)
			return err
		}
		userData.Avatar = req.Avatar
		userData.AvatarOn = now.Unix()
		err = UpdateUserInfo(c, userData)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("updateDBCertificationUserLanguages UpdateUserInfo err: %v\n", err)
			return err
		}
		for _, language := range req.Languages {
			languages = append(languages, &model.CertificationUserLanguage{
				IntlOpenid: openid,
				GameId:     constants.GAMEID_30054,
				AreaId:     constants.AREAID_global,
				Type:       language.Type,
				Content:    language.Desc,
				Language:   language.Language,
			})
		}
		// return nil
	}
	if len(languages) == 0 {
		return nil
	}
	err := dao.UpdateCertificationUserLanguages(languages)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("updateDBCertificationUserLanguages err: %v\n", err)
	}

	return err
}

// 更新ES数据
func updateESCertificationUserLanguages(c context.Context, req *user_pb.CMSSetCertificationUserLanguagesReq, authIntlOpenids []string) error {
	now := time.Now()
	for _, openid := range authIntlOpenids {
		doc := map[string]interface{}{}
		doc["avatar"] = req.Avatar
		doc["avatar_on"] = now.Unix()
		if len(req.Languages) > 0 {
			userLanguagesBytes, _ := json.Marshal(req.Languages)
			doc["certification_user_languages"] = string(userLanguagesBytes)
			for _, languageItem := range req.Languages {
				if languageItem.Language == "en" && languageItem.Type == 1 {
					doc["username_en"] = languageItem.Desc
				}
				if languageItem.Language == "ja" && languageItem.Type == 1 {
					doc["username_ja"] = languageItem.Desc
				}
				if languageItem.Language == "ko" && languageItem.Type == 1 {
					doc["username_ko"] = languageItem.Desc
				}
				if languageItem.Language == "zh" && languageItem.Type == 1 {
					doc["username_zh"] = languageItem.Desc
				}
				if languageItem.Language == "zh-TW" && languageItem.Type == 1 {
					doc["username_zh-TW"] = languageItem.Desc
				}
			}
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, openid, doc)
	}
	return nil
}

// 更新数据库内数据
func UpdateDBCertificationUserLanguagesToC(c context.Context, language, userName, remark string, authIntlOpenid string) error {
	languages := make([]*model.CertificationUserLanguage, 0)
	if userName != "" {
		languages = append(languages, &model.CertificationUserLanguage{
			IntlOpenid: authIntlOpenid,
			GameId:     constants.GAMEID_30054,
			AreaId:     constants.AREAID_global,
			Type:       1,
			Content:    userName,
			Language:   language,
		})
	}
	if remark != "" {
		languages = append(languages, &model.CertificationUserLanguage{
			IntlOpenid: authIntlOpenid,
			GameId:     constants.GAMEID_30054,
			AreaId:     constants.AREAID_global,
			Type:       2,
			Content:    remark,
			Language:   language,
		})
	}

	if len(languages) == 0 {
		return nil
	}
	err := dao.UpdateCertificationUserLanguages(languages)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UpdateDBCertificationUserLanguagesToC UpdateCertificationUserLanguages err: %v\n", err)
		return err
	}

	allUserLanguageInfo, err := dao.GetCertificationUserAllLanguages(authIntlOpenid, constants.GAMEID_30054, constants.AREAID_global)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UpdateDBCertificationUserLanguagesToC GetCertificationUserAllLanguages err: %v\n", err)
		return err
	}
	doc := map[string]interface{}{}
	if len(allUserLanguageInfo) > 0 {
		userLanguages := make([]*user_pb.UserInfoLanguageItem, 0)
		for _, languageItem := range allUserLanguageInfo {
			if languageItem.Language == "en" && languageItem.Type == 1 {
				doc["username_en"] = languageItem.Content
			}
			if languageItem.Language == "ja" && languageItem.Type == 1 {
				doc["username_ja"] = languageItem.Content
			}
			if languageItem.Language == "ko" && languageItem.Type == 1 {
				doc["username_ko"] = languageItem.Content
			}
			if languageItem.Language == "zh" && languageItem.Type == 1 {
				doc["username_zh"] = languageItem.Content
			}
			if languageItem.Language == "zh-TW" && languageItem.Type == 1 {
				doc["username_zh-TW"] = languageItem.Content
			}
			userLanguages = append(userLanguages, &user_pb.UserInfoLanguageItem{
				Language: languageItem.Language,
				Type:     languageItem.Type,
				Desc:     languageItem.Content,
			})
		}
		if len(userLanguages) > 0 {
			userLanguagesBytes, _ := json.Marshal(userLanguages)
			doc["certification_user_languages"] = string(userLanguagesBytes)
		}
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, authIntlOpenid, doc)
	return nil
}
