package dynamics

import (
	"context"

	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/logic/emoticon"
)

func (s *DynamicsImpl) GetAllEmoticons(c context.Context, req *pb.GetAllEmoticonsReq) (
	rsp *pb.GetAllEmoticonsRsp, err error,
) {

	//gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	//if gameId == "" || areaId == "" {
	//	return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	//}
	// 写死game_id 和 area_id的查询
	list, err := emoticon.GetAllEmoticonList(c, req)
	if err != nil {
		return nil, err
	}
	return list, nil
}
