package handler

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/IBM/sarama"
	"trpc.publishing_application.standalonesite/app/common"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/comment"
)

func WaitingForReviewCommentMessageConsumer(ctx context.Context, msgArray []*sarama.ConsumerMessage) (err error) {
	var handlers []func() error
	for _, v := range msgArray {
		value := string(v.Value)
		handlers = append(handlers, func() error {
			err := ConsumeCommentAuditMessage(common.GetCommonCtx(), value)
			return err
		})
	}
	err = trpc.GoAndWait(handlers...)
	return
}

func ConsumeCommentAuditMessage(ctx context.Context, sendStr string) (err error) {
	defer recovery.CatchGoroutinePanic(context.Background())
	consumeMsgData := make([]interface{}, 0)
	if err = json.Unmarshal([]byte(sendStr), &consumeMsgData); err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("failed to get kafka user site message req,err:%v\n", err)
		// 消费kafka数据异常失败的，则直接告警，错误数据记录db，不返回错误，让kafka消息队列继续往下消费
		return nil
	}
	consumeMsgDataByString := make([]string, len(consumeMsgData))
	for i, datum := range consumeMsgData {
		consumeMsgDataByString[i] = fmt.Sprintf("%s", datum)
	}
	err = comment.ConsumeWaitingForReviewComment(ctx, consumeMsgDataByString)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("message.SiteMessageConsumeHandler failed,err:%v\n", err)

		// 消费kafka数据异常失败的，则直接告警，错误数据记录db，不返回错误，让kafka消息队列继续往下消费
		return nil
	}
	return nil
}
