package user

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"

	"gorm.io/plugin/soft_delete"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	es7 "github.com/olivere/elastic/v7"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

type UserInfoReviewReq struct {
	UserAuditIDs []int64 `json:"user_audit_ids" binding:"required"`
	UpdateUser   string  `json:"update_user"`
	GameID       string  `json:"game_id"`
	AreaID       string  `json:"area_id"`
}

// CMSGetUserInfoList 提供给CMS管理端查询用户信息
func CMSGetUserInfoList(c context.Context, req *pbUser.CMSGetUserListReq) (*pbUser.CMSGetUserListRsp, error) {
	boolQuery := es7.NewBoolQuery()
	isDelQuery := es7.NewTermQuery("is_del", 0)
	boolQuery.Must(isDelQuery)
	if req.IntlGameid != "" {
		IntlGameidQuery := es7.NewTermQuery("intl_gameid", req.IntlGameid)
		boolQuery.Must(IntlGameidQuery)
	}
	if req.IntlOpenid != "" {
		intlOpenidQuery := es7.NewTermQuery("intl_openid", req.IntlOpenid)
		boolQuery.Must(intlOpenidQuery)
	}
	if req.IntlUserOpenid != "" {
		if strings.Contains(req.IntlUserOpenid, "-") {
			intlOpenidQuery := es7.NewTermQuery("intl_openid", req.IntlOpenid)
			boolQuery.Must(intlOpenidQuery)
		} else {
			intlUserOpenidQuery := es7.NewTermQuery("intl_user_openid", req.IntlUserOpenid)
			boolQuery.Must(intlUserOpenidQuery)
		}
	}
	if req.IsAdmin > 0 {
		// 是否是管理员：0默认，1非管理员，2管理员
		var isAdmin int64
		if req.IsAdmin == 1 {
			isAdmin = 0
		} else {
			isAdmin = 1
		}
		isAdminQuery := es7.NewTermQuery("is_admin", isAdmin)
		boolQuery.Must(isAdminQuery)
		if isAdmin == 1 {
			isAdminRangeQuery := es7.NewRangeQuery("admin_on").Gte(time.Now().Unix())
			boolQuery.Filter(isAdminRangeQuery)
		}
	}
	if req.IsMute > 0 {
		// 是否是禁言：0默认，1正常，2已禁言
		var isMute int64
		if req.IsMute == 1 {
			isMute = 0
		} else {
			isMute = 1
		}
		isMuteQuery := es7.NewTermQuery("is_mute", isMute)
		boolQuery.Must(isMuteQuery)
		if isMute == 1 {
			isMuteRangeQuery := es7.NewRangeQuery("mute_on").Gte(time.Now().Unix())
			boolQuery.Filter(isMuteRangeQuery)
		}
	}
	if req.AuthType > 0 && req.IsAuthed != 1 {
		// 是否授权认证：0默认，1无授权认证，2官方认证，3创作者认证, 4是机构认证
		// 因为int类型的默认值是0，所以导致筛选无认证用户的时候出现问题，所以这里改成B端+1. 筛选这里-1
		authType := req.AuthType - 1
		if authType < 0 {
			authType = 0
		}
		authTypeQuery := es7.NewTermQuery("auth_type", authType)
		boolQuery.Must(authTypeQuery)
		if authType > 0 {
			// 只有是需要筛选认证情况下才需要这个生效时间
			authTypeRangeQuery := es7.NewRangeQuery("auth_on").Gte(time.Now().Unix())
			boolQuery.Filter(authTypeRangeQuery)
		}
	}
	// 降权筛选
	if req.IsDemotion > 0 {
		// 0-默认 1-正常 2-降权
		if req.IsDemotion == 1 {
			// 不筛选已经降权的
			isDemotionQuery := es7.NewTermQuery("is_demotion", 1)
			boolQuery.MustNot(isDemotionQuery)
		}
		if req.IsDemotion == 2 {
			isDemotionQuery := es7.NewTermQuery("is_demotion", 1)
			boolQuery.Must(isDemotionQuery)
			boolQuery.Filter(es7.NewRangeQuery("demotion_off").Gte(time.Now().Unix()))
		}
	}
	// 所有认证用户
	if req.IsAuthed == 1 {
		boolQuery.Should(es7.NewMatchQuery("auth_type", 1), es7.NewMatchQuery("auth_type", 2), es7.NewMatchQuery("auth_type", 3)).MinimumNumberShouldMatch(1)
	}
	if req.UserName != "" {
		// usernameQuery := es7.NewMatchPhraseQuery("username", req.UserName)
		usernameQuery := es7.NewWildcardQuery("username", "*"+req.UserName+"*")
		boolQuery.Filter(usernameQuery)
	}
	var sortBys []es7.Sorter
	sortQuery := es7.NewFieldSort("created_on").Desc()
	sortBys = append(sortBys, sortQuery)
	var lastSortValue []interface{}
	if req.NextPageCursor != "" {
		cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
		if err != nil {
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
		}
		err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "CMSGetUserInfoList | Failed to get idCursor")
		}
	}
	resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.UserInfoIndex, boolQuery, sortBys, lastSortValue, req.Limit)
	if err != nil {
		return nil, err
	}

	getUserListRsp := &pbUser.CMSGetUserListRsp{
		List:     make([]*pbUser.CmsUserInfo, 0),
		PageInfo: &pbUser.UserPageInfo{},
	}
	if resp == nil || resp.Hits == nil {
		getUserListRsp.PageInfo.IsFinish = true
		return getUserListRsp, nil
	}
	currentTime := time.Now().Unix()
	var userIntlOpenids = make([]string, 0)
	for _, hit := range resp.Hits.Hits {
		esUserInfo := &model.ESUserInfo{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, esUserInfo); err != nil {
			return nil, err
		}

		if esUserInfo.AdminOn < currentTime {
			esUserInfo.IsAdmin = 0
		}
		if esUserInfo.MuteOn < currentTime {
			esUserInfo.IsMute = 0
		}
		if esUserInfo.AuthOn < currentTime {
			esUserInfo.AuthType = 0
		}
		authLanguages := make([]*pbUser.LanguageItem, 0)
		if esUserInfo.AuthLanguages != "" {
			json.Unmarshal([]byte(esUserInfo.AuthLanguages), &authLanguages)
		}
		userInfosLanguages := make([]*pbUser.UserInfoLanguageItem, 0)
		if esUserInfo.CertificationUserLanguages != "" {
			json.Unmarshal([]byte(esUserInfo.CertificationUserLanguages), &userInfosLanguages)
		}
		if esUserInfo.DemotionOff < currentTime {
			esUserInfo.DemotionOff = 0
			esUserInfo.IsDemotion = 0
		}
		item := &pbUser.CmsUserInfo{
			IntlOpenid:         esUserInfo.IntlOpenid,
			Username:           esUserInfo.Username,
			Status:             1,
			Avatar:             esUserInfo.Avatar,
			IsAdmin:            esUserInfo.IsAdmin > 0,
			AuthType:           esUserInfo.AuthType,
			Remark:             esUserInfo.Remark,
			Language:           esUserInfo.Language,
			FansNum:            esUserInfo.FansNum,
			FollowNum:          esUserInfo.FollowNum,
			PostNum:            esUserInfo.PostNum,
			AllPostNum:         esUserInfo.AllPostNum,
			IsMute:             esUserInfo.IsMute > 0,
			Mood:               esUserInfo.Mood,
			HomePageLinks:      esUserInfo.HomePageLinks,
			AuthLanguages:      authLanguages,
			UserInfosLanguages: userInfosLanguages,
			CreatedOn:          int32(esUserInfo.CreatedOn),
			MuteReason:         int32(esUserInfo.MuteReason),
			MuteOn:             int32(esUserInfo.MuteOn),
			Region:             esUserInfo.IntlGameid,
			IntlUserOpenid:     esUserInfo.IntlUserOpenid,
			MuteDays:           esUserInfo.MuteDays,
			IsDemotion:         esUserInfo.IsDemotion,
			DemotionOff:        esUserInfo.DemotionOff,
		}

		userIntlOpenids = append(userIntlOpenids, esUserInfo.IntlOpenid)

		getUserListRsp.List = append(getUserListRsp.List, item)
	}
	// 获取到用户角色
	if len(userIntlOpenids) > 0 {
		roles, err := GetUserRoleInfo(c, userIntlOpenids)
		if err == nil {
			for _, info := range getUserListRsp.List {
				if role, ok := roles[info.IntlOpenid]; ok {
					info.RoleInfo = role
				}
			}
		}
	}
	if len(resp.Hits.Hits) > 0 {
		jsonData, err := json.Marshal(resp.Hits.Hits[len(resp.Hits.Hits)-1].Sort)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSGetUserInfoList json.Marshal err: %v", err)
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "CMSGetUserInfoList | Failed to json.Marshal EncryptPageCursorS")
		}
		if len(resp.Hits.Hits) == 0 || int64(len(resp.Hits.Hits)) < req.Limit {
			getUserListRsp.PageInfo.IsFinish = true
		} else {
			nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to postsFrom EncryptPageCursorS")
			}
			getUserListRsp.PageInfo.NextPageCursor = nextPageCursor
		}
	} else {
		getUserListRsp.PageInfo.IsFinish = true
	}
	getUserListRsp.PageInfo.PreviousPageCursor = req.NextPageCursor

	return getUserListRsp, nil
}

// CMSGetUserAuditInfoList 提供给CMS管理端查询用户审核信息
func CMSGetUserAuditInfoList(c context.Context, req *pbUser.CMSGetUserAuditListReq) (*pbUser.CMSGetUserAuditListRsp, error) {
	boolQuery := es7.NewBoolQuery()
	isDelQuery := es7.NewTermQuery("is_del", 0)
	boolQuery.Must(isDelQuery)
	if req.TextRiskLevel > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		textRiskLevel := req.TextRiskLevel - 1
		if textRiskLevel < 0 {
			textRiskLevel = 0
		}
		itemQuery := es7.NewTermQuery("text_risk_level", textRiskLevel)
		boolQuery.Must(itemQuery)
	}
	if req.TextRiskType > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		textRiskType := req.TextRiskType - 1
		if textRiskType < 0 {
			textRiskType = 0
		}
		itemQuery := es7.NewTermQuery("text_risk_type", textRiskType)
		boolQuery.Must(itemQuery)
	}
	if req.PicRiskLevel > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		picRiskLevel := req.PicRiskLevel - 1
		if picRiskLevel < 0 {
			picRiskLevel = 0
		}
		itemQuery := es7.NewTermQuery("pic_risk_level", picRiskLevel)
		boolQuery.Must(itemQuery)
	}
	if req.PicRiskType > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		picRiskType := req.PicRiskType - 1
		if picRiskType < 0 {
			picRiskType = 0
		}
		itemQuery := es7.NewTermQuery("pic_risk_type", picRiskType)
		boolQuery.Must(itemQuery)
	}
	if req.AuditStatus > 0 {
		itemQuery := es7.NewTermQuery("audit_status", req.AuditStatus)
		boolQuery.Must(itemQuery)
	}
	if req.Type > 0 {
		itemQuery := es7.NewTermQuery("type", req.Type)
		boolQuery.Must(itemQuery)
	}
	if req.Keyword != "" {
		itemQuery := es7.NewWildcardQuery("context", "*"+req.Keyword+"*")
		// itemQuery := es7.NewMatchPhraseQuery("context", req.Keyword)
		boolQuery.Must(itemQuery)
	}
	if req.IntlOpenid != "" {
		itemQuery := es7.NewTermQuery("intl_openid", req.IntlOpenid)
		boolQuery.Must(itemQuery)
	}
	if req.IntlUserOpenid != "" {
		if strings.Contains(req.IntlUserOpenid, "-") {
			itemQuery := es7.NewTermQuery("intl_openid", req.IntlOpenid)
			boolQuery.Must(itemQuery)
		} else {
			itemQuery := es7.NewTermQuery("intl_user_openid", req.IntlUserOpenid)
			boolQuery.Must(itemQuery)
		}
	}
	if req.StartTime > 0 {
		itemQuery := es7.NewRangeQuery("created_on").Gte(req.StartTime)
		boolQuery.Filter(itemQuery)
	}
	if req.EndTime > 0 {
		itemQuery := es7.NewRangeQuery("created_on").Lte(req.EndTime)
		boolQuery.Filter(itemQuery)
	}
	// 筛选语言
	if req.Language != "" {
		itemQuery := es7.NewTermQuery("language", req.Language)
		boolQuery.Must(itemQuery)
	}
	if req.DelType > 0 {
		itemQuery := es7.NewTermQuery("del_type", req.DelType)
		boolQuery.Must(itemQuery)
	}
	// 机审状态查询
	if req.MachineStatus > 0 {
		// b端加1
		machineStatus := req.MachineStatus - 1
		if machineStatus <= 0 {
			machineStatus = 0
		}
		//switch machineStatus {
		//case 0:
		//	// 待机审
		//	itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 1)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//	break
		//case 1:
		//	// 机身通过
		//	itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 2)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//case 2:
		//	// 机审异常
		//	itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 1)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//}
		// 产品需要单个字段可以单独筛选
		itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		boolQuery.Must(itemQuery)
	}
	// 人审状态查询
	if req.ArtificialStatus > 0 {
		// b端加1
		artificialStatus := req.ArtificialStatus - 1
		if artificialStatus <= 0 {
			artificialStatus = 0
		}
		//switch artificialStatus {
		//case 0:
		//	// 待人审
		//	machineQuery := es7.NewTermQuery("machine_status", 2)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 1)
		//	boolQuery.Must(machineQuery, auditStatusQuery)
		//	break
		//case 1:
		//	// 人审通过
		//	itemQuery := es7.NewTermQuery("artificial_status", artificialStatus)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 2)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//case 2:
		//	// 人审拒绝
		//	itemQuery := es7.NewTermQuery("artificial_status", artificialStatus)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 3)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//}
		// 产品需要单个字段可以单独筛选
		itemQuery := es7.NewTermQuery("artificial_status", artificialStatus)
		boolQuery.Must(itemQuery)
	}
	// 内容状态查询
	if req.ContentStatus > 0 {
		var itemQuery *es7.TermQuery
		switch constants.PostContentStatus(req.ContentStatus) {
		case constants.NormalPost:
			itemQuery = es7.NewTermQuery("audit_status", 2)
			break
		case constants.SandboxPost:
			itemQuery = es7.NewTermQuery("audit_status", 1)
			break
		case constants.UserDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.UserDeleted)
			break
		case constants.CAdminDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.CAdminDeleted)
			break
		case constants.BReviewDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.BReviewDeleted)
			break
		case constants.BAdminDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.BAdminDeleted)
		case constants.BReportDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.ReportBAdminDeleted)
			break
		default:
			return nil, errs.NewCustomError(c, code.InvalidParamsErr, "content status enum is required")
		}
		boolQuery.Must(itemQuery)
	}
	var sortBys []es7.Sorter
	if req.AuditStatus == 0 {
		// 【全部tab】 按照操作时间倒序
		sortBys = append(sortBys, es7.NewFieldSort("audit_on").Desc())
	}
	sortBys = append(sortBys, es7.NewFieldSort("audit_status").Asc(),
		es7.NewFieldSort("id").Desc())
	var lastSortValue []interface{}
	if req.NextPageCursor != "" {
		cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
		if err != nil {
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
		}
		err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "CMSGetUserAuditInfoList | Failed to get idCursor")
		}
	}
	resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.UserAuditIndex, boolQuery, sortBys, lastSortValue, req.Limit)
	if err != nil {
		return nil, err
	}

	getUserListRsp := &pbUser.CMSGetUserAuditListRsp{
		List:     make([]*pbUser.CMSGetUserAuditRsp, 0),
		PageInfo: &pbUser.UserPageInfo{},
	}
	if resp == nil || resp.Hits == nil {
		getUserListRsp.PageInfo.IsFinish = true
		return getUserListRsp, nil
	}
	for _, hit := range resp.Hits.Hits {
		esUserAuditInfo := &model.ESUserAuditInfo{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, esUserAuditInfo); err != nil {
			return nil, err
		}

		item := &pbUser.CMSGetUserAuditRsp{
			Id:               esUserAuditInfo.Id,
			IntlOpenid:       esUserAuditInfo.IntlOpenid,
			TextRiskLevel:    esUserAuditInfo.TextRiskLevel,
			TextRiskType:     esUserAuditInfo.TextRiskType,
			PicRiskLevel:     esUserAuditInfo.PicRiskLevel,
			PicRiskType:      esUserAuditInfo.PicRiskType,
			AuditStatus:      esUserAuditInfo.AuditStatus,
			Type:             esUserAuditInfo.Type,
			Context:          esUserAuditInfo.Context,
			AuditUser:        esUserAuditInfo.AuditUser,
			AuditOn:          esUserAuditInfo.AuditOn,
			CreatedOn:        esUserAuditInfo.CreatedOn,
			Language:         esUserAuditInfo.Language,
			ArtificialStatus: esUserAuditInfo.ArtificialStatus,
			MachineStatus:    esUserAuditInfo.MachineStatus,
			DelType:          esUserAuditInfo.DelType,
			DelReason:        esUserAuditInfo.DelReason,
		}

		getUserListRsp.List = append(getUserListRsp.List, item)
	}
	if len(resp.Hits.Hits) > 0 {
		jsonData, err := json.Marshal(resp.Hits.Hits[len(resp.Hits.Hits)-1].Sort)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSGetUserAuditInfoList json.Marshal err: %v", err)
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "CMSGetUserAuditInfoList | Failed to json.Marshal EncryptPageCursorS")
		}
		if len(resp.Hits.Hits) == 0 || int64(len(resp.Hits.Hits)) < req.Limit {
			getUserListRsp.PageInfo.IsFinish = true
		} else {
			nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "CMSGetUserAuditInfoList | Failed to postsFrom EncryptPageCursorS")
			}
			getUserListRsp.PageInfo.NextPageCursor = nextPageCursor
		}
	} else {
		getUserListRsp.PageInfo.IsFinish = true
	}
	getUserListRsp.PageInfo.PreviousPageCursor = req.NextPageCursor

	return getUserListRsp, nil
}

// CMSReviewUserInfo 提供给CMS管理端审批用户信息（昵称、签名）
func CMSReviewUserInfo(c context.Context, req *pbUser.CMSReviewUserInfoReq, isMachineReview bool) error {
	req.UserAuditIds = util.RemoveDuplicateInt64(req.UserAuditIds)
	if len(req.UserAuditIds) == 0 {
		return nil
	}
	// 获取官方消息通知userid
	senderGameIntlGameId := "30054"

	// 根据审批id列表，获取所有审批记录,es搜索条件
	//conditionsT := map[string]*ParamsItem{
	//	"id": {
	//		Value:     param.UserAuditIDs,
	//		MastQuery: true,
	//	},
	//}
	// 数据表搜索
	conditionsT := &dao.UserAuditConditions{
		Ids: req.UserAuditIds,
	}
	// userAudits, err := UserAuditListByDb(conditionsT, 0, 0, false)
	// if err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewUserInfo GetUserAuditsWithDel err: %v", err)
	// 	return errs.NewCustomError(c, code.GetUserAuditInfoListFailed, "CMSReviewUserInfo | Failed to obtain user information audit list.")
	// }

	userAudits, err := dao.UserAuditList(conditionsT, 0, 0, false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewUserInfo GetUserAuditsWithDel err: %v", err)
		return errs.NewCustomError(c, code.GetUserAuditInfoListFailed, "CMSReviewUserInfo | Failed to obtain user information audit list.")
	}

	if (constants.UserAuditActionStatusT(req.Type) == constants.USER_AUDIT_ACTION_TYPE_DELETE || constants.UserAuditActionStatusT(req.Type) == constants.USER_AUDIT_ACTION_TYPE_NOPASS) && len(userAudits) != len(req.UserAuditIds) {
		// 判断长度是否相等，不相等就是存在已经被删除的数据
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewUserInfo GetUserAuditsWithDel There is deleted user information, audit_id: %v", req.UserAuditIds)
		return errs.NewCustomError(c, code.StatusIsChanged, "There is deleted user information.")
	} else {
		// 判断哪些是审核通过的
		var reviewedUserData []int64
		for _, audit := range userAudits {
			if audit.AuditStatus == 2 {
				// 审核通过的状态
				reviewedUserData = append(reviewedUserData, audit.ID)
			}
		}
		if len(reviewedUserData) > 0 {
			// 存在已经审核通过的数据
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewUserInfo GetUserAuditsWithDel There is deleted user information, audit_id: %v", reviewedUserData)
			return errs.NewCustomError(c, code.StatusIsChanged, "There is reviewed user information.")
		}
	}

	currentTime := time.Now().Unix()
	for _, userAudit := range userAudits {
		var needNotify bool
		userDoc := make(map[string]interface{}, 0)
		auditDoc := make(map[string]interface{}, 0)
		if constants.UserAuditActionStatusT(req.Type) == constants.USER_AUDIT_ACTION_TYPE_DELETE {
			// CMS管理端删除已审批的记录，那么判断删除的是不是最新的已审核通过的那条，如果是则发起通知，否则不需要发起消息通知
			// db搜索条件,获取上一条记录
			conditionsT = &dao.UserAuditConditions{
				IntlOpenid:  userAudit.IntlOpenid,
				Type:        userAudit.Type,
				AuditStatus: constants.USER_AUDIT_STATUS_TYPE_PASS,
				GtId:        userAudit.ID,
				Order: []*dao.OrderConditions{
					&dao.OrderConditions{
						Column: "id",
						IsDesc: false,
					},
				},
			}
			userAudits, err := UserAuditListByDb(conditionsT, 0, 1, false)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewUserInfo userAudits1 err: %v, conditionsT: %v", err, conditionsT)
				return errs.NewCustomError(c, code.GetUserAuditInfoListFailed, "CMSReviewUserInfo | Failed to obtain user information audit list.")
			}
			// 查询是否有比当前被删除的已审批记录大的生效中的已审批记录id，如果没有，则删的是最新的已审批生效中的记录
			if len(userAudits) == 0 {
				needNotify = true
				// 更新用户用户信息，将昵称或备注换成上一次审核通过的内容，如果昵称没有上一次审核通过的记录则设置为初始值，如果签名没有上一次审核记录则设置为空
				user, err := dao.GetUserByIntlOpenid(userAudit.IntlOpenid)
				if err != nil {
					return errs.NewCustomError(c, code.GetUserInfoError, "CMSReviewUserInfo | Failed to get the user information, please check")
				}
				// 获取用户上一条已审核通过的记录
				var latestUserAudit *model.UserAuditAndUserFormatted
				conditionsT = &dao.UserAuditConditions{
					IntlOpenid:  userAudit.IntlOpenid,
					Type:        userAudit.Type,
					AuditStatus: constants.USER_AUDIT_STATUS_TYPE_PASS,
					LtId:        userAudit.ID,
					Order: []*dao.OrderConditions{
						&dao.OrderConditions{
							Column: "id",
							IsDesc: false,
						},
					},
				}
				latestUserAudits, err := UserAuditListByDb(conditionsT, 0, 1, false)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewUserInfo userAudits2 err: %v, conditionsT: %v", err, conditionsT)
					return errs.NewCustomError(c, code.GetUserAuditInfoListFailed, "CMSReviewUserInfo | Failed to obtain user information audit list.")
				}
				if len(latestUserAudits) > 0 {
					latestUserAudit = latestUserAudits[0]
				}
				if constants.UserAuditT(userAudit.Type) == constants.USER_AUDIT_TYPE_NAME {
					// 从redis中删除用户昵称记录，还原回去原有的
					if latestUserAudit != nil && latestUserAudit.Context != "" {
						user.Username = latestUserAudit.Context
					} else {
						// 如果没有上一条已审核通过的记录则用初始值
						user.Username = user.Nickname
					}
					// 把最新的这个用户昵称删除
					if userAudit.Context != "" {
						redis.GetClient().SRem(c, cache.GetUsernameCacheKey(), userAudit.Context)
					}
					user.UsernameOn = currentTime
					if err := dao.UserUpdate(user); err != nil {
						return errs.NewCustomError(c, code.UpdateUserInfoError, "CMSReviewUserInfo | Failed to update user information")
					}
					auditDoc = map[string]interface{}{
						"username":    user.Username,
						"username_on": user.UsernameOn,
					}
					userDoc["username"] = user.Username
					userDoc["username_on"] = user.UsernameOn

				} else if constants.UserAuditT(userAudit.Type) == constants.USER_AUDIT_TYPE_REMARK {
					if latestUserAudit != nil && latestUserAudit.Context != "" {
						user.Remark = latestUserAudit.Context
					} else {
						// 如果没有上一条已审核通过的记录则设置签名为空值
						user.Remark = ""
					}
					user.RemarkOn = currentTime
					if err := dao.UpdateUserRemark(user); err != nil {
						return errs.NewCustomError(c, code.UpdateUserRemarkFailed, "CMSReviewUserInfo | Failed to update user remark")
					}
					auditDoc = map[string]interface{}{
						"remark":    user.Remark,
						"remark_on": user.RemarkOn,
					}
					userDoc["remark"] = user.Remark
					userDoc["remark_on"] = user.RemarkOn
				}

				// 更新db
				userAudit.IsDel = 1
				userAudit.DeletedOn = currentTime
				userAudit.AuditUser = req.UpdateUser
				userAudit.DelReason = req.DelReason
				userAudit.DelType = int32(constants.USER_AUDIT_DELETE_TYPE_BADMIN)

				// 更新ES审批数据
				auditDoc = map[string]interface{}{
					"is_del":          1,
					"audit_user":      userAudit.AuditUser,
					"deleted_on":      userAudit.AuditOn,
					"audit_introduce": userAudit.AuditIntroduce,
					"del_type":        int32(constants.USER_AUDIT_DELETE_TYPE_BADMIN),
					"del_reason":      req.DelReason,
				}
			}
		} else if constants.UserAuditActionStatusT(req.Type) == constants.USER_AUDIT_ACTION_TYPE_NOPASS {
			userAudit.AuditStatus = 3
			userAudit.AuditIntroduce = req.AuditIntroduce
			userAudit.AuditOn = currentTime
			userAudit.ModifiedOn = currentTime
			userAudit.AuditUser = req.UpdateUser
			userAudit.ArtificialStatus = 2 //人工审核拒绝
			userAudit.IsDel = 1
			userAudit.DeletedOn = time.Now().Unix()
			userAudit.DelReason = req.DelReason
			userAudit.DelType = int32(constants.USER_AUDIT_DELETE_TYPE_BREVIEW)
			// 如果是用户名审核被拒需要把当前修改的给剔除
			if constants.UserAuditT(userAudit.Type) == constants.USER_AUDIT_TYPE_NAME {
				// 把用户修改没通过的昵称给删除
				redis.GetClient().SRem(c, cache.GetUsernameCacheKey(), userAudit.Context)
			}

			// 如果是审核不通过，则只需要发起消息通知
			needNotify = true
			auditDoc = map[string]interface{}{
				"audit_status":      3,
				"audit_user":        userAudit.AuditUser,
				"audit_on":          userAudit.AuditOn,
				"audit_introduce":   userAudit.AuditIntroduce,
				"del_type":          int32(constants.USER_AUDIT_DELETE_TYPE_BREVIEW),
				"del_reason":        req.DelReason,
				"artificial_status": 2,
			}
		} else if constants.UserAuditActionStatusT(req.Type) == constants.USER_AUDIT_ACTION_TYPE_PASS {
			userAudit.AuditStatus = 2
			userAudit.AuditIntroduce = req.AuditIntroduce
			userAudit.AuditOn = currentTime
			userAudit.ModifiedOn = currentTime
			userAudit.AuditUser = req.UpdateUser

			if !isMachineReview {
				userAudit.ArtificialStatus = 1
			}

			// 如果是审批通过，则更新用户信息为最新审批通过的信息
			user, err := dao.GetUserByIntlOpenid(userAudit.IntlOpenid)
			if err != nil {
				return errs.NewCustomError(c, code.GetUserInfoError, "CMSReviewUserInfo | Failed to get the user information, please check")
			}
			if constants.UserAuditT(userAudit.Type) == constants.USER_AUDIT_TYPE_NAME {
				// 把用户旧昵称给删除
				redis.GetClient().SRem(c, cache.GetUsernameCacheKey(), user.Username)
				user.Username = userAudit.Context
				user.UsernameOn = currentTime
				userDoc["username"] = user.Username
				userDoc["username_on"] = user.UsernameOn
				for _, langItem := range constants.AllPostLanguages {
					userKeyStr := fmt.Sprintf("username_%s", langItem)
					userDoc[userKeyStr] = user.Username
				}
				// 先注释，这个貌似是没有用到
				//auditDoc = map[string]interface{}{
				//	"username":    userAudit.Context,
				//	"username_on": user.RemarkOn,
				//}
			} else if constants.UserAuditT(userAudit.Type) == constants.USER_AUDIT_TYPE_REMARK {
				user.Remark = userAudit.Context
				user.RemarkOn = currentTime
				userDoc["remark"] = userAudit.Context
				userDoc["remark_on"] = user.RemarkOn
				// 先注释，这个貌似是没有用到
				//auditDoc = map[string]interface{}{
				//	"remark":    userAudit.Context,
				//	"remark_on": user.RemarkOn,
				//}
			}
			if err := dao.UserUpdate(user); err != nil {
				return errs.NewCustomError(c, code.UpdateUserInfoError, "CMSReviewUserInfo | Failed to update user information")
			}

			auditDoc = map[string]interface{}{
				"audit_status":    2,
				"audit_user":      userAudit.AuditUser,
				"audit_on":        userAudit.AuditOn,
				"audit_introduce": userAudit.AuditIntroduce,
			}
			if !isMachineReview {
				auditDoc["artificial_status"] = 1
			}
		} else {
			return errs.NewCustomError(c, code.InvalidParamsErr, "CMSReviewUserInfo | Invalid type")
		}

		// 更新用户信息审核表db
		err = dao.UserAuditUpdate(userAudit)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewUserInfo uFailed to update user audit info err: %v, userAudit: %v", err, userAudit)
			return errs.NewCustomError(c, code.UpdateUserAuditInfoError, "CMSReviewUserInfo | Failed to update user audit info")
		}

		// 更新用户es
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, userAudit.IntlOpenid, userDoc)
		// 更新用户审批es
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserAuditIndex, fmt.Sprintf("%d", userAudit.ID), auditDoc)

		if needNotify {
			var brief string
			var msgType constants.MessageT
			var exInfo string
			switch constants.UserAuditT(userAudit.Type) {
			case constants.USER_AUDIT_TYPE_NAME:
				msgType = constants.MsgTypeOfficialIgnoreUserName
				if userAudit.IsDel == 1 {
					msgType = constants.MsgTypeOfficialDeleteUserName
					exInfo = fmt.Sprintf("{\"del_type\": %d, \"del_reason\": %d}", userAudit.DelType, userAudit.DelReason)
				}
			case constants.USER_AUDIT_TYPE_REMARK:
				msgType = constants.MsgTypeOfficialIgnoreUserRemark
				if userAudit.IsDel == 1 {
					msgType = constants.MsgTypeOfficialDeleteUserRemark
					exInfo = fmt.Sprintf("{\"del_type\": %d, \"del_reason\": %d}", userAudit.DelType, userAudit.DelReason)
				}
			default:
				msgType = constants.MsgTypeOfficialIgnoreUserName
			}

			go writemessage.SetUserMessage(&model.Message{
				Type:                   msgType,
				Brief:                  brief,
				GameID:                 "30054",
				AreaID:                 "global",
				SenderUserIntlOpenid:   senderGameIntlGameId,
				ReceiverUserIntlOpenid: userAudit.User.IntlOpenid,
				ExtInfo:                exInfo,
			}, userAudit.User.IntlOpenid, constants.SiteMessageCount)
		}
		go DeleteUserInfoCache(c, userAudit.IntlOpenid)
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserAuditIndex)

	return nil
}

// CMSSetMuteUserInfo
func CMSSetMuteUserInfo(c context.Context, req *pbUser.CMSSetMuteUserReq) error {
	if len(req.IntlOpenids) == 0 {
		return nil
	}

	var intlOpenIdList []interface{}
	if req.Type == 1 {
		// 获取哪些是已经有数据了的，就不重复操作
		conditionsT := &dao.UserPermissionConditions{
			IntlOpenids: req.IntlOpenids,
			ActionType:  constants.UserActionType_Mute,
			ValidOn:     time.Now().Unix(), //查询当前时间之后的数据
		}
		userPermissionList, err := dao.UserPermissionList(conditionsT, 0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetMuteUserInfo UserPermissionList err: %v", err)
			return errs.NewCustomError(c, code.CMSSetMuteUserInfoError, "CMSSetMuteUserInfo | Failed to mute user.")
		}
		// 校验是否存在已经禁言的用户，存在直接退出
		for _, intlOpenid := range req.IntlOpenids {
			for _, userPermission := range userPermissionList {
				if intlOpenid == userPermission.IntlOpenid {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetMuteUserInfo There are banned users")
					return errs.NewCustomError(c, code.StatusIsChanged, "CMSSetMuteUserInfo | There are banned users")
				}
			}
		}
		// 设置用户禁言
		// 获取当前时间
		now := time.Now()
		// 计算几天后的时间
		threeDaysLater := now.Add(time.Duration(req.Days*24) * time.Hour)
		// 获取几天后时间的时间戳（以秒为单位）
		endTime := threeDaysLater.Unix()
		var userPermissions []*model.UserPermission
		var message []*model.Message
		for _, intlOpenid := range req.IntlOpenids {
			needBreak := false
			for _, userPermission := range userPermissionList {
				if intlOpenid == userPermission.IntlOpenid {
					needBreak = true
				}
			}
			// 已存在的数据就直接过滤
			if needBreak {
				continue
			}
			userPermission := &model.UserPermission{
				IntlOpenid:  intlOpenid,
				ActionType:  constants.UserActionType_Mute,
				ActionValue: int(req.MuteReason),
				ValidOn:     endTime,
				Creator:     req.UpdateUser,
				GameId:      "30054",
				AreaId:      "global",
			}
			userPermissions = append(userPermissions, userPermission)
			intlOpenIdList = append(intlOpenIdList, intlOpenid)

			// 更新es
			doc := map[string]interface{}{
				"intl_openid": intlOpenid,
				"is_mute":     1,
				"mute_on":     endTime,
				"mute_reason": req.MuteReason,
				"mute_days":   req.Days,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
			message = append(message, &model.Message{
				Type:                   constants.MsgTypeOfficialAccountMute,
				GameID:                 "30054",
				AreaID:                 "global",
				ReceiverUserIntlOpenid: intlOpenid,
				Content:                "",
				SenderUserIntlOpenid:   "30054",
				ExtInfo:                fmt.Sprintf("{\"days\": %d, \"reason_type\": %d}", req.Days, req.MuteReason),
			})
		}
		if len(intlOpenIdList) == 0 {
			return nil
		}
		// 更新db
		if err := dao.BatchAddUserPermission(userPermissions); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetMuteUserInfo BatchAddUserPermission err: %v", err)
			return errs.NewCustomError(c, code.CMSSetMuteUserInfoError, "CMSSetMuteUserInfo | Failed to mute user.")
		}
		for _, permission := range userPermissions {
			userRedisKey := cache.GetMuteUserKey(constants.SetUserMuteAction, permission.IntlOpenid)

			// 更新redis
			cErr := redis.GetClient().SetEX(context.Background(), userRedisKey, "1", time.Duration(endTime-time.Now().Unix())*time.Second).Err()
			if cErr != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetMuteUserInfo err: %v", permission.IntlOpenid, cErr)
				return errs.NewCustomError(c, code.CMSSetMuteUserInfoError, "CMSSetMuteUserInfo | Failed to mute user cache.")
			}
			// 删除用户缓存
			DeleteUserInfoCache(context.Background(), permission.IntlOpenid)
			// 修改用户网红绑定同步状态
			dao.ChangeSyncStatus(permission.IntlOpenid, 0, "")
			cache.DeleteCreatorHubUserBoundInfoCache(c, permission.IntlOpenid)
		}
		go func() {
			defer recovery.CatchGoroutinePanic(context.Background())
			for _, m := range message {
				writemessage.SetUserMessage(m, m.ReceiverUserIntlOpenid, constants.SiteMessageCount)
			}
		}()

	} else {
		// 取消用户禁言
		if err := dao.BatchUserPermissionDelete(req.IntlOpenids, constants.UserActionType_Mute, 0); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetMuteUserInfo BatchAddUserPermission err: %v", err)
			return errs.NewCustomError(c, code.CMSDelMuteUserInfoError, "CMSSetMuteUserInfo | Failed to mute user.")
		}
		//删除redis
		for _, intlOpenid := range req.IntlOpenids {
			intlOpenIdList = append(intlOpenIdList, intlOpenid)
			// 更新es
			doc := map[string]interface{}{
				"intl_openid": intlOpenid,
				"is_mute":     0,
				"mute_on":     0,
				"mute_reason": 0,
				"mute_days":   0,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)

			userRedisKey := cache.GetMuteUserKey(constants.SetUserMuteAction, intlOpenid)

			// 更新redis
			cErr := redis.GetClient().Del(c, userRedisKey).Err()

			if cErr != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetMuteUserInfo cancel mute err: %v", intlOpenid, cErr)
			}
			// 删除用户缓存
			DeleteUserInfoCache(context.Background(), intlOpenid)
		}
	}

	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	return nil
}

// CMSSetAdminUserInfo
func CMSSetAdminUserInfo(c context.Context, req *pbUser.CMSSetAdminUserReq) error {
	if len(req.IntlOpenids) == 0 {
		return nil
	}
	userRedisKey := cache.GetAdminUserKey(1)
	var intlOpenIdList []interface{}
	if req.Type == 1 {
		// 获取哪些是已经有数据了的，就不重复操作
		conditionsT := &dao.UserPermissionConditions{
			IntlOpenids: req.IntlOpenids,
			ActionType:  1,
		}
		userPermissionList, err := dao.UserPermissionList(conditionsT, 0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAdminUserInfo UserPermissionList err: %v", err)
			return errs.NewCustomError(c, code.CMSSetAdminUserInfoError, "CMSSetAdminUserInfo | Failed to admin user.")
		}
		// 校验哪些用户已经是管理员了，是的话直接退出
		for _, intlOpenid := range req.IntlOpenids {
			for _, userPermission := range userPermissionList {
				if intlOpenid == userPermission.IntlOpenid {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAdminUserInfo There is a user who is already an administrator ")
					return errs.NewCustomError(c, code.StatusIsChanged, "CMSSetAdminUserInfo | There is a user who is already an administrator")
				}
			}
		}
		// 设置用户管理员
		var userPermissions []*model.UserPermission
		for _, intlOpenid := range req.IntlOpenids {
			needBreak := false
			for _, userPermission := range userPermissionList {
				if intlOpenid == userPermission.IntlOpenid {
					needBreak = true
				}
			}
			// 已存在的数据就直接过滤
			if needBreak {
				continue
			}
			userPermission := &model.UserPermission{
				IntlOpenid:  intlOpenid,
				ActionType:  1,
				ActionValue: 1,
				ValidOn:     2100000000, // 2099-01-01  00:00:00
				Creator:     req.UpdateUser,
				GameId:      "30054",
				AreaId:      "global",
			}
			userPermissions = append(userPermissions, userPermission)
			intlOpenIdList = append(intlOpenIdList, intlOpenid)

			// 更新es
			doc := map[string]interface{}{
				"intl_openid": intlOpenid,
				"is_admin":    1,
				"admin_on":    2100000000,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
			// 删除用户缓存
			DeleteUserInfoCache(context.Background(), intlOpenid)
		}
		if len(intlOpenIdList) == 0 {
			return nil
		}
		if err := dao.BatchAddUserPermission(userPermissions); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAdminUserInfo BatchAddUserPermission err: %v", err)
			return errs.NewCustomError(c, code.CMSSetAdminUserInfoError, "CMSSetAdminUserInfo | Failed to set admin user.")
		}
		// 更新redis
		cErr := redis.GetClient().SAdd(context.Background(), userRedisKey, intlOpenIdList...).Err()
		if cErr != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetAdminUserInfo err: %v", cErr)
			return errs.NewCustomError(c, code.CMSSetAdminUserInfoError, "CMSSetAdminUserInfo | Failed to admin user cache.")
		}
	} else {
		// 取消用户管理员
		if err := dao.BatchUserPermissionDelete(req.IntlOpenids, constants.UserActionType_Admin, 1); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAdminUserInfo BatchUserPermissionDelete err: %v", err)
			return errs.NewCustomError(c, code.CMSDelAdminUserInfoError, "CMSSetAdminUserInfo | Failed to set admin user.")
		}
		//删除redis
		for _, intlOpenid := range req.IntlOpenids {
			intlOpenIdList = append(intlOpenIdList, intlOpenid)
			// 更新es
			doc := map[string]interface{}{
				"intl_openid": intlOpenid,
				"is_admin":    0,
				"admin_on":    0,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
			// 删除用户缓存
			DeleteUserInfoCache(context.Background(), intlOpenid)
		}
		cErr := redis.GetClient().SRem(context.Background(), userRedisKey, intlOpenIdList...).Err()
		if cErr != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetAdminUserInfo err: %v", cErr)
			return errs.NewCustomError(c, code.CMSDelAdminUserInfoError, "CMSSetAdminUserInfo | Failed to delete admin user cache.")
		}
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)

	return nil
}

// CMSSetAuthUserInfo
// Deprecated: 该接口即将废弃,使用CMSSetAuthUserInfoV2代理
func CMSSetAuthUserInfo(c context.Context, req *pbUser.CMSSetAuthUserReq) error {
	if len(req.IntlOpenids) == 0 {
		return nil
	}
	var intlOpenIdList []interface{}
	if req.Type == 1 || req.Type == 3 {
		actionValue := 1
		if req.Type == 3 {
			actionValue = 2
		}
		userRedisKey := cache.GetAuthUserKey(actionValue)

		// 获取哪些是已经有数据了的，就不重复操作
		conditionsT := &dao.UserPermissionConditions{
			IntlOpenids: req.IntlOpenids,
			ActionType:  2,
			ActionValue: actionValue,
		}
		userPermissionList, err := dao.UserPermissionList(conditionsT, 0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo UserPermissionList err: %v", err)
			return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to admin user.")
		}

		// 设置用户管理员
		var userPermissions []*model.UserPermission
		for _, intlOpenid := range req.IntlOpenids {
			needBreak := false
			for _, userPermission := range userPermissionList {
				if intlOpenid == userPermission.IntlOpenid {
					needBreak = true
				}
			}
			// 已存在的数据就直接过滤
			if needBreak {
				continue
			}
			userPermission := &model.UserPermission{
				IntlOpenid:  intlOpenid,
				ActionType:  2,
				ActionValue: actionValue,
				ValidOn:     2100000000, // 2099-01-01  00:00:00
				Creator:     req.UpdateUser,
				GameId:      "30054",
				AreaId:      "global",
			}
			userPermissions = append(userPermissions, userPermission)
			intlOpenIdList = append(intlOpenIdList, intlOpenid)

			// 更新es
			doc := map[string]interface{}{
				"intl_openid": intlOpenid,
				"auth_type":   actionValue,
				"auth_on":     2100000000,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
		}
		if len(intlOpenIdList) == 0 {
			return nil
		}
		if err := dao.BatchAddUserPermission(userPermissions); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo BatchAddUserPermission err: %v", err)
			return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to set auth user.")
		}
		// 更新redis
		cErr := redis.GetClient().SAdd(context.Background(), userRedisKey, intlOpenIdList...).Err()
		if cErr != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetAuthUserInfo err: %v", cErr)
			return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to auth user cache.")
		}
	} else {
		// 取消用户认证
		actionValue := 1
		if req.Type == 4 {
			actionValue = 2
		}
		userRedisKey := cache.GetAuthUserKey(actionValue)

		if err := dao.BatchUserPermissionDelete(req.IntlOpenids, constants.UserActionType_Auth, actionValue); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo BatchUserPermissionDelete err: %v", err)
			return errs.NewCustomError(c, code.CMSDelAuthUserInfoError, "CMSSetAuthUserInfo | Failed to set auth user.")
		}
		// 更新es
		for _, intlOpenid := range req.IntlOpenids {
			intlOpenIdList = append(intlOpenIdList, intlOpenid)
			// 更新es
			doc := map[string]interface{}{
				"intl_openid": intlOpenid,
				"auth_type":   0,
				"auth_on":     0,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
		}
		//删除redis
		cErr := redis.GetClient().SRem(context.Background(), userRedisKey, intlOpenIdList...).Err()
		if cErr != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetAuthUserInfo err: %v", cErr)
			return errs.NewCustomError(c, code.CMSDelAuthUserInfoError, "CMSSetAuthUserInfo | Failed to delete admin user cache.")
		}
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)

	return nil
}

// v2版本
func CMSSetAuthUserInfoV2(c context.Context, req *pbUser.CMSSetAuthUserReq) error {
	log.InfoContextf(c, "CMSSetAuthUserInfoV2 req: %v", req)
	if len(req.IntlOpenids) == 0 {
		return nil
	}
	var intlOpenIdList []interface{}
	// 1- 添加官方认证 3-添加创作认证 5-添加机构认证 7-添加管理员认证 **认证只能存在一个，不会存在多个认证
	if req.Type == 1 || req.Type == 3 || req.Type == 5 || req.Type == 7 {
		actionValue := 1
		if req.Type == 3 {
			actionValue = 2
		}
		if req.Type == 5 {
			actionValue = 3
		}
		if req.Type == 7 {
			actionValue = 4
		}
		userRedisKey := cache.GetAuthUserKey(actionValue)

		// 获取哪些是已经有数据了的，就不重复操作
		conditionsT := &dao.UserPermissionConditions{
			IntlOpenids: req.IntlOpenids,
			ActionType:  2,
			ActionValue: actionValue,
		}
		userPermissionList, err := dao.UserPermissionList(conditionsT, 0)
		log.InfoContextf(c, "CMSSetAuthUserInfoV2 UserPermissionList req")
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo UserPermissionList err: %v", err)
			return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to admin user.")
		}

		// 设置用户管理员
		var userPermissions []*model.UserPermission
		userPermissionLanguages := []*model.UserPermissionLanguage{}
		for _, intlOpenid := range req.IntlOpenids {
			needBreak := false
			for _, userPermission := range userPermissionList {
				userPermissionLanguages = append(userPermissionLanguages, &model.UserPermissionLanguage{})
				if intlOpenid == userPermission.IntlOpenid {
					needBreak = true
				}
			}
			// 已存在的数据就直接过滤
			authDesc, err := json.Marshal(req.Languages)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfoV2 json.Marshal err: %v", err)
				return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfoV2 | Failed to json.Marshal Languages")
			}
			if needBreak {
				// 更新认证类型和认证称号多语言数据
				doc := map[string]interface{}{
					"intl_openid":    intlOpenid,
					"auth_type":      actionValue,
					"auth_on":        2100000000,
					"auth_languages": string(authDesc),
				}
				dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
				// 一个用户只会有一个认证
				break
			}
			userPermission := &model.UserPermission{
				IntlOpenid:  intlOpenid,
				ActionType:  2,
				ActionValue: actionValue,
				ValidOn:     2100000000, // 2099-01-01  00:00:00
				Creator:     req.UpdateUser,
				GameId:      "30054",
				AreaId:      "global",
			}
			userPermissions = append(userPermissions, userPermission)
			intlOpenIdList = append(intlOpenIdList, intlOpenid)

			// 更新es
			doc := map[string]interface{}{
				"intl_openid":    intlOpenid,
				"auth_type":      actionValue,
				"auth_on":        2100000000,
				"auth_languages": string(authDesc),
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
		}
		if len(intlOpenIdList) != 0 {
			if err := dao.BatchAddUserPermission(userPermissions); err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo BatchAddUserPermission err: %v", err)
				return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to set auth user.")
			}
			// 更新redis
			cErr := redis.GetClient().SAdd(context.Background(), userRedisKey, intlOpenIdList...).Err()
			if cErr != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetAuthUserInfo err: %v", cErr)
				return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to auth user cache.")
			}
		}
		log.InfoContextf(c, "CMSSetAuthUserInfoV2 RemoveOtherUserPermission before")
		err = RemoveOtherUserPermission(c, req.IntlOpenids, 2, actionValue)
		log.InfoContextf(c, "CMSSetAuthUserInfoV2 RemoveOtherUserPermission after")

		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo RemoveOtherUserPermission err: %v", err)
			return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to auth user cache.")
		}
	} else if req.Type == 0 {
		// // 取消用户认证
		// actionValue := 1
		// if req.Type == 4 {
		// 	actionValue = 2
		// }
		// // 取消机构认证
		// if req.Type == 6 {
		// 	actionValue = 3
		// }
		userRedisKey_1 := cache.GetAuthUserKey(1)
		userRedisKey_2 := cache.GetAuthUserKey(2)
		userRedisKey_3 := cache.GetAuthUserKey(3)
		// 管理员认证
		userRedisKey_4 := cache.GetAuthUserKey(4)

		if err := dao.BatchDeleteAllUserPermission(req.IntlOpenids, 2); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo BatchUserPermissionDelete err: %v", err)
			return errs.NewCustomError(c, code.CMSDelAuthUserInfoError, "CMSSetAuthUserInfo | Failed to set auth user.")
		}
		// 更新es
		for _, intlOpenid := range req.IntlOpenids {
			intlOpenIdList = append(intlOpenIdList, intlOpenid)
			// 更新es
			doc := map[string]interface{}{
				"intl_openid":    intlOpenid,
				"auth_type":      0,
				"auth_on":        0,
				"auth_languages": "",
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
		}
		//删除redis
		cErr := redis.GetClient().SRem(context.Background(), userRedisKey_1, intlOpenIdList...).Err()
		if cErr != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetAuthUserInfo err: %v", cErr)
			return errs.NewCustomError(c, code.CMSDelAuthUserInfoError, "CMSSetAuthUserInfo | Failed to delete admin user cache: action value 1.")
		}
		cErr = redis.GetClient().SRem(context.Background(), userRedisKey_2, intlOpenIdList...).Err()
		if cErr != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetAuthUserInfo err: %v", cErr)
			return errs.NewCustomError(c, code.CMSDelAuthUserInfoError, "CMSSetAuthUserInfo | Failed to delete admin user cache: action value 2.")
		}
		cErr = redis.GetClient().SRem(context.Background(), userRedisKey_3, intlOpenIdList...).Err()
		if cErr != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetAuthUserInfo err: %v", cErr)
			return errs.NewCustomError(c, code.CMSDelAuthUserInfoError, "CMSSetAuthUserInfo | Failed to delete admin user cache: action value 3.")
		}
		cErr = redis.GetClient().SRem(context.Background(), userRedisKey_4, intlOpenIdList...).Err()
		if cErr != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetAuthUserInfo err: %v", cErr)
			return errs.NewCustomError(c, code.CMSDelAuthUserInfoError, "CMSSetAuthUserInfo | Failed to delete admin user cache: action value 4.")
		}
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	// 更新多语言（认证描述多语言）
	err := setCMSAuthUserLanguages(c, req)
	cache.BatchDeleteUserInfosCache(c, req.IntlOpenids)
	// 移除认证用户多语言数据(用户信息多语言)
	// RemoveCerificationUserLanguages(c, req.IntlOpenids)
	return err
}

// remove 其他的认证
func RemoveOtherUserPermission(c context.Context, intlOpenids []string, keepActionType int, keepActionValue int) error {
	// 数据库移除
	err := dao.BatchDeleteOtherPermission(intlOpenids, keepActionType, keepActionValue)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo RemoveOtherUserPermission BatchDeleteOtherPermission err: %v", err)
		return err
	} else {
		intlOpenidInterfaces := make([]interface{}, len(intlOpenids))
		for i, v := range intlOpenids {
			intlOpenidInterfaces[i] = v
		}
		for i := 1; i <= 3; i++ {
			if i == keepActionValue {
				continue
			}
			userRedisKey := cache.GetAuthUserKey(i)
			cErr := redis.GetClient().SRem(context.Background(), userRedisKey, intlOpenidInterfaces...).Err()
			if cErr != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetAuthUserInfo err: %v", cErr)
				return cErr
			}
		}
	}
	return nil
}

func SetUserCommentBanCache(c context.Context, userOpenId string, gameID, areaID string) error {
	redisKey := cache.GetUserCommentBanKey(userOpenId, gameID, areaID)
	err := setUserPermAuditCache(c, userOpenId, 2, 2, redisKey, gameID, areaID)
	return err
}

func DeleteUserCommentBanCache(c context.Context, intlOpenID string, gameID, areaID string) {
	redisKey := cache.GetUserCommentBanKey(intlOpenID, gameID, areaID)
	redis.GetClient().Del(c, redisKey)
}

func DeleteUserPostBanCache(c context.Context, intlOpenID string, gameID, areaID string) {
	redisKey := cache.GetUserPostBanKey(intlOpenID, gameID, areaID)
	redis.GetClient().Del(c, redisKey)
}

func DeleteUserAccountBanCache(c context.Context, intlOpenID string, gameID, areaID string) {
	redisKey := cache.GetUserAccounttBanKey(intlOpenID, gameID, areaID)
	redis.GetClient().Del(c, redisKey)
}

// 设置认证多语言的单独接口
func setCMSAuthUserLanguages(c context.Context, req *pbUser.CMSSetAuthUserReq) error {
	userPermissionLanguages := make([]*model.UserPermissionLanguage, 0)
	// 添加
	var actionValue int32 = 1
	var isDel soft_delete.DeletedAt
	// 创作认证
	if req.Type == 3 || req.Type == 4 {
		actionValue = 2
	}
	// 机构认证
	if req.Type == 5 || req.Type == 6 {
		actionValue = 3
	}
	// 管理员认证
	if req.Type == 7 || req.Type == 8 {
		actionValue = 4
	}
	if req.Type == 1 || req.Type == 3 || req.Type == 5 || req.Type == 7 {
		isDel = 0
	} else if req.Type == 2 || req.Type == 4 || req.Type == 6 || req.Type == 8 {
		// 删除
		isDel = 1
	}
	conditionsT := &dao.UserPermissionConditions{
		IntlOpenids: req.IntlOpenids,
		ActionType:  2,
		ActionValue: int(actionValue),
	}
	if req.Type == 0 {
		conditionsT = &dao.UserPermissionConditions{
			IntlOpenids: req.IntlOpenids,
			ActionType:  2,
		}
	}
	userPermissionList, err := dao.UserPermissionList(conditionsT, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo UserPermissionList err: %v", err)
		return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to admin user.")
	}
	for _, userPermission := range userPermissionList {
		for _, languageItem := range req.Languages {
			userPermissionLanguage := &model.UserPermissionLanguage{
				UserPermissionId: userPermission.ID,
				Language:         languageItem.Language,
				Desc:             languageItem.Desc,
				IntlOpenid:       userPermission.IntlOpenid,
				GameId:           userPermission.GameId,
				AreaId:           userPermission.AreaId,
				Creator:          userPermission.Creator,
				Updater:          req.UpdateUser,
				ActionValue:      userPermission.ActionValue,
				Model: &model.Model{
					IsDel: isDel,
				},
			}
			userPermissionLanguages = append(userPermissionLanguages, userPermissionLanguage)
		}
	}
	if len(userPermissionLanguages) == 0 {
		return nil
	}
	// 移除已有多语言
	userPermissions, err := dao.GetAlreadyExistUserPermissionLanguages(req.IntlOpenids, actionValue)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("CMSSetAuthUserInfo GetAlreadyExistUserPermissionLanguages err: %v", err)
	}
	for _, userPermission := range userPermissions {
		redisKey := cache.GetAuthLangugeDescKey(userPermission.IntlOpenid, actionValue, userPermission.Language)
		err := redis.GetClient().Del(context.Background(), redisKey).Err()
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo Del redisKey err: %v", err)
			return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to admin user.")
		}
	}
	err = dao.UpdateUserPermissionLanguages(userPermissionLanguages)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo UpdateUserPermissionLanguages err: %v", err)
		return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to admin user.")
	}
	// 设置redis缓存
	if isDel == 1 {
		for _, userPermissionLanguage := range userPermissionLanguages {
			redisKey := cache.GetAuthLangugeDescKey(userPermissionLanguage.IntlOpenid, actionValue, userPermissionLanguage.Language)
			err := redis.GetClient().Del(c, redisKey).Err()
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo Del redisKey err: %v", err)
				return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to admin user.")
			}
		}
	} else {
		for _, userPermissionLanguage := range userPermissionLanguages {
			redisKey := cache.GetAuthLangugeDescKey(userPermissionLanguage.IntlOpenid, actionValue, userPermissionLanguage.Language)
			err := redis.GetClient().Set(context.Background(), redisKey, userPermissionLanguage.Desc, 0).Err()
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetAuthUserInfo Set redisKey err: %v", err)
				return errs.NewCustomError(c, code.CMSSetAuthUserInfoError, "CMSSetAuthUserInfo | Failed to admin user.")
			}
		}
	}
	return nil
}
