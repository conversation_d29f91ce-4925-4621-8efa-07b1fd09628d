package model

type CommentBubble struct {
	*Model
	Languages      []CommentBubbleLanguage `gorm:"foreignKey:CommentBubbleID;references:ID" json:"languages"`
	ID             int64                   `gorm:"column:id;primary_key" json:"id"`               //
	GameID         string                  `gorm:"column:game_id" json:"game_id"`                 //游戏id
	AreaID         string                  `gorm:"column:area_id" json:"area_id"`                 //区域id
	Icon           string                  `gorm:"column:icon" json:"icon"`                       //图标
	BgColor        string                  `gorm:"column:bg_color" json:"bg_color"`               //背景色
	Order          int                     `gorm:"column:order" json:"order"`                     //排序
	SecondDuration int                     `gorm:"column:second_duration" json:"second_duration"` //持续天数
	JumpURL        string                  `gorm:"column:jump_url" json:"jump_url"`               //跳转链接
	Creator        string                  `gorm:"column:creator" json:"creator"`                 //创建人
	Updater        string                  `gorm:"column:updater" json:"updater"`                 //更新人
	IsPermanent    int32                   `gorm:"column:is_permanent" json:"is_permanent"`       //是否永久
}

func (c *CommentBubble) TableName() string {
	return "p_comment_bubble"
}
