package df_tmp

import (
	"context"
	"errors"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	repo "trpc.act.logicial/app/mysql/df_activity_repo"
)

// GetUserNestLimit 判断用户距离首次成功掏鸟窝是否超过限制
func GetUserNestLimit(ctx context.Context,
	req *pb.GetUserNestLimitReq) (*pb.GetUserNestLimitRsp, error) {
	log.InfoContextf(ctx, "start GetUserNestLimit, req: %+v", req)
	if req.Uid == "" {
		return nil, code.ErrParamError
	}

	currentTime := time.Now().Unix()
	record, err := repo.NestActivityRepoClient.GetUserBirdNestReward(ctx, req.Uid)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 文档不存在 返回对应错误码
		return nil, errs.NewCustomError(ctx, code.ActivityUserNestInfoNotFound,
			"user nest info not found")
	}
	if err != nil {
		log.ErrorContextf(ctx, "GetUserBirdNestReward error:%v, uid:%v", err, req.Uid)
		return nil, code.ErrSystemError
	}
	maxDiff := int64(24 * 60 * 60)
	var exceedLimit bool
	diff := currentTime - record.FirstClaimTime
	if diff > maxDiff {
		exceedLimit = true
	}
	rsp := &pb.GetUserNestLimitRsp{
		FirstTime:   uint64(record.FirstClaimTime),
		DiffTimie:   diff,
		ExceedLimit: exceedLimit,
	}
	// 超过限制直接返回错误即可，不通过返回字段进行判断
	if exceedLimit {
		return nil, errs.NewCustomError(ctx, code.ActivityUserExceedTimeRangeLimit,
			"user exceed activity time range")
	}
	return rsp, nil
}
