package util

import (
	"io"
	"os"
)

func WriteFile(ioData io.ReadCloser, filePattern string) (error, *os.File) {
	tmpFile, err := os.CreateTemp(os.TempDir(), filePattern)
	if err != nil {
		return err, nil
	}
	defer os.Remove(tmpFile.Name())

	bodyBytes, _ := io.ReadAll(ioData)
	_, err = tmpFile.Write(bodyBytes)
	if err != nil {
		return err, nil
	}
	// 打开并解析Excel文件
	excelFileObj, err := os.OpenFile(tmpFile.Name(), os.O_RDWR|os.O_CREATE, 0755)
	if err != nil {
		return err, nil
	}
	return nil, excelFileObj
}