// Package signup 报名
package signup

import (
	"context"

	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_signup"
	"trpc.act.logicial/app/logic/signup"
)

// SignupImpl 报名结构
type SignupImpl struct {
	pb.UnimplementedSignup
}

// DoSignUp 添加报名记录
func (s *SignupImpl) DoSignUp(
	ctx context.Context,
	req *pb.SignUpReq,
) (*pb.SignUpRsp, error) {
	rsp := &pb.SignUpRsp{}

	err := signup.DoSignUp(ctx, req.FsourceId)
	if err != nil {
		return rsp, err
	}

	return rsp, nil
}

// HasSignUp 是否报名
func (s *SignupImpl) HasSignUp(
	ctx context.Context,
	req *pb.HasSignUpReq,
) (*pb.HasSignUpRsp, error) {
	rsp := &pb.HasSignUpRsp{
		HasSignUp: false,
	}

	has, err := signup.HasSignUp(ctx, req.FsourceId)
	if err != nil {
		return rsp, err
	}
	rsp.HasSignUp = has

	return rsp, nil
}
