// Package dragonact0704 龙之谷2活动
package dragonact0704

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_dragonact0704"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	"google.golang.org/protobuf/proto"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	dragonact0704Logic "trpc.act.logicial/app/logic/dragonact0704"
	"trpc.act.logicial/app/logic/general"
	generalModel "trpc.act.logicial/app/model/general"
)

const (
	// VoteKey 取数据Key
	VoteKey = "vote"
)

// Dragonact0704Impl 结构
type Dragonact0704Impl struct {
	pb.UnimplementedDragonact0704
}

// ChooseDragon 选择一个龙
func (s *Dragonact0704Impl) ChooseDragon(ctx context.Context, req *pb.ChooseDragonReq) (rsp *pb.ChooseDragonRsp,
	err error) {
	rsp = &pb.ChooseDragonRsp{}
	// 选择一个龙
	// 1、获取当前选择的龙
	var tagId string
	tagId, err = dragonact0704Logic.GetCurDragonTagId(ctx)
	if err != nil {
		return
	}
	if tagId != "" {
		// 2、判断选择的龙死没死
		result, hasResult, errG := general.GetCacheMapField(ctx, VoteKey, req.FsourceId, tagId)
		if errG != nil {
			err = errG
			return
		}
		if hasResult == false {
			err = errs.NewCustomError(ctx, code.NoCacheData, "no cache data")
			return
		}
		voteNum, _ := strconv.Atoi(result)
		checkList := config.GetConfig().Dragonact0704CheckList
		hasKill, errC := CheckHasKill(ctx, checkList, voteNum, tagId)
		if errC != nil {
			err = errC
			return
		}
		if hasKill == false {
			err = errs.NewCustomError(ctx, code.LastChooseAlive, "last choose alive")
			return
		}
	}
	// 3、没有则选择一条龙
	if tagId == req.TagId {
		err = errs.NewCustomError(ctx, code.NoCacheData, "no cache data")
		return
	}
	// 4、要选择的龙没死
	result, hasResult, errG := general.GetCacheMapField(ctx, VoteKey, req.FsourceId, req.TagId)
	if errG != nil {
		err = errG
		return
	}
	if hasResult == false {
		err = errs.NewCustomError(ctx, code.NoCacheData, "no cache data")
		return
	}
	voteNum, _ := strconv.Atoi(result)
	checkList := config.GetConfig().Dragonact0704CheckList
	hasKill, errC := CheckHasKill(ctx, checkList, voteNum, req.TagId)
	if errC != nil {
		err = errC
		return
	}
	if hasKill == true {
		// 更新龙状态
		dragonact0704Logic.RecordDragonRank(ctx, req.TagId)
		err = errs.NewCustomError(ctx, code.ChooseNotAlive, "choose not alive")
		return
	}
	// 记录选择一条龙
	err = dragonact0704Logic.RecordChooseDragon(ctx, req.TagId)
	return
}

// HasChooseDragonList 选择龙的列表
func (s *Dragonact0704Impl) HasChooseDragonList(ctx context.Context, req *pb.HasChooseDragonListReq) (
	rsp *pb.HasChooseDragonListRsp, err error) {
	rsp = &pb.HasChooseDragonListRsp{}
	var list []string
	list, err = dragonact0704Logic.GetChooseDragonList(ctx)
	if err != nil {
		return
	}
	rsp.List = list
	return
}

// CurChooseDragonIndex 当前选择龙ID
func (s *Dragonact0704Impl) CurChooseDragonIndex(ctx context.Context, req *pb.CurChooseDragonIndexReq) (
	rsp *pb.CurChooseDragonIndexRsp, err error) {
	rsp = &pb.CurChooseDragonIndexRsp{}
	// 1、获取当前选择的龙
	var tagId string
	tagId, err = dragonact0704Logic.GetCurDragonTagId(ctx)

	if err != nil {
		return
	}
	if tagId == "" {
		return
	}

	// 获取当前投票列表 （从redis取）
	result, hasResult, errG := general.GetCacheMapField(ctx, VoteKey, req.FsourceId, tagId)

	if errG != nil {
		err = errG
		return
	}
	if hasResult == false {
		err = errs.NewCustomError(ctx, code.NoCacheData, "no cache data")
		return
	}
	// 取表里数据对比
	voteNum, _ := strconv.Atoi(result)
	checkList := config.GetConfig().Dragonact0704CheckList
	hasKill, errC := CheckHasKill(ctx, checkList, voteNum, tagId)
	if errC != nil {
		err = errC
		return
	}

	// 如果选择且龙死了就更新表，并返回让选择龙
	if hasKill == true {
		err = dragonact0704Logic.UpdateChooseDragonStatus(ctx, tagId)
		if err != nil {
			return
		}
		tagId = ""
		return
	}
	// 如果没死就返回当前数值
	rsp.TagId = tagId
	return
}

// IsCurChooseThisDragon 判断是否是当前选择的龙
func (s *Dragonact0704Impl) IsCurChooseThisDragon(ctx context.Context, req *pb.IsCurChooseThisDragonReq) (
	rsp *pb.IsCurChooseThisDragonRsp, err error) {
	rsp = &pb.IsCurChooseThisDragonRsp{}
	// 取表里选择数值
	var tagId string
	tagId, err = dragonact0704Logic.GetCurDragonTagId(ctx)
	if err != nil {
		return
	}
	// 从前端传入数据对比
	if tagId != req.TagId {
		err = errs.NewCustomError(ctx, code.NotCurChoose, "no choose dragon")
	}
	rsp.IsChoose = true
	return
}

// GetDragonList 获取龙状态列表
func (s *Dragonact0704Impl) GetDragonList(ctx context.Context, req *pb.GetDragonListReq) (rsp *pb.GetDragonListRsp,
	err error) {
	rsp = &pb.GetDragonListRsp{}
	// 从req取上限
	resultMap, exists, errG := general.GetCacheMapAll(ctx, VoteKey, req.FsourceId)
	if errG != nil {
		return
	}
	if !exists {
		err = errs.NewCustomError(ctx, code.NoCacheData, "no cache data")
		return
	}
	// 从表取数据 数量
	rankList, errG := dragonact0704Logic.GetDragonRankList(ctx)
	if errG != nil {
		err = errG
	}

	// 数据整合
	rspList := make([]*pb.DragonStatus, 0)
	checkList := config.GetConfig().Dragonact0704CheckList

	for key, item := range checkList {
		oneData := &pb.DragonStatus{
			TagId: key,
			Total: item,
		}
		if cur, ok := resultMap[key]; ok {
			voteNum, _ := strconv.Atoi(cur)
			oneData.Cur = oneData.Total - int32(voteNum)
		}
		if rank, ok := rankList[key]; ok {
			oneData.Rank = rank
		}
		rspList = append(rspList, oneData)
		// 判断如果 血量为0  但是没有排名 更新龙状态
		if oneData.Cur == 0 && oneData.Rank == 0 {
			dragonact0704Logic.RecordDragonRank(ctx, key)
		}
	}
	rsp.List = rspList
	return
}

// CheckDragonAlive 检查龙是否存活
func (s *Dragonact0704Impl) CheckDragonAlive(ctx context.Context, req *pb.CheckDragonAliveReq) (
	rsp *pb.CheckDragonAliveRsp, err error) {
	rsp = &pb.CheckDragonAliveRsp{}
	// 从req取数据 数量
	result, hasResult, errG := general.GetCacheMapField(ctx, VoteKey, req.FsourceId, req.TagId)
	if errG != nil {
		err = errG
		return
	}
	if hasResult == false {
		err = errs.NewCustomError(ctx, code.NoCacheData, "no cache data")
		return
	}
	voteNum, _ := strconv.Atoi(result)
	checkList := config.GetConfig().Dragonact0704CheckList
	hasKill, errC := CheckHasKill(ctx, checkList, voteNum, req.TagId)
	if errC != nil {
		err = errC
		return
	}
	// 数据整合
	if hasKill {
		dragonact0704Logic.RecordDragonRank(ctx, req.TagId)
		err = errs.NewCustomError(ctx, code.ChooseNotAlive, "choose not alive")
		return
	} else {
		rsp.IsAlive = true
	}
	return
}

// DoReserveChannel 检查龙是否存活
func (s *Dragonact0704Impl) DoReserveChannel(ctx context.Context, req *pb.DoReserveChannelReq) (
	rsp *pb.DoReserveChannelRsp, err error) {
	rsp = &pb.DoReserveChannelRsp{}
	err = dragonact0704Logic.ReserveChannel(ctx, req.ChannelId, req.Type)
	return
}

// HasReserveChannelList 检查龙是否存活
func (s *Dragonact0704Impl) HasReserveChannelList(ctx context.Context, req *pb.HasReserveChannelListReq) (
	rsp *pb.HasReserveChannelListRsp, err error) {
	rsp = &pb.HasReserveChannelListRsp{}
	var channeIds []string
	channeIds, err = dragonact0704Logic.HasReserveChannelList(ctx, req.Type)
	rsp.ChannelIds = channeIds
	return
}

// SetPresentLevel  检查龙是否存活
func (s *Dragonact0704Impl) SetPresentLevel(ctx context.Context, req *pb.SetPresentLevelReq) (
	rsp *pb.SetPresentLevelRsp, err error) {
	rsp = &pb.SetPresentLevelRsp{}
	// 根据tag 设置礼包抽奖等级
	var lotteryLevel int64
	switch req.TagId {
	case "1":
		lotteryLevel = 1
		break
	case "2":
		lotteryLevel = 2
		break
	case "3":
		lotteryLevel = 3
		break
	case "4":
		lotteryLevel = 4
		break
	default:
		err = errs.NewCustomError(ctx, code.DragonPresentErr, "present_err")
		return
	}
	metadata.SetPresentLotteryLevelList(ctx, req.PresentId, []int64{lotteryLevel})
	return
}

// RecordSendPresent   记录发送礼包
func (s *Dragonact0704Impl) RecordSendPresent(ctx context.Context, req *pb.RecordSendPresentReq) (
	rsp *pb.RecordSendPresentRsp, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	fmt.Println("--------------GetUserAccount----------------")
	fmt.Printf("%#v\n", userAccount)
	fmt.Printf("%#v\n", err)
	rsp = &pb.RecordSendPresentRsp{}
	// 根据tag 和礼包记录
	list := req.PresentData.PresentList
	if len(list) == 0 {
		err = errs.NewCustomError(ctx, code.DragonPresentErr, "present_err")
		return
	}
	cdkey := list[0].Detail.Cdkey
	err = dragonact0704Logic.RecordPresent(ctx, req.TagId, cdkey)
	return
}

// GetPresentList  已发送礼包列表
func (s *Dragonact0704Impl) GetPresentList(ctx context.Context, req *pb.GetPresentListReq) (
	rsp *pb.GetPresentListRsp, err error) {
	rsp = &pb.GetPresentListRsp{}
	// 获得记录列表

	presentList, errM := dragonact0704Logic.GetPresentList(ctx)
	if errM != nil {
		err = errM
	}
	var dataList []*pb.Present
	for _, item := range presentList {
		data := &pb.Present{
			Cdkey:     item.Cdkey,
			Timestamp: int32(item.CreatedAt),
			TagId:     item.TagId,
		}
		dataList = append(dataList, data)
	}
	rsp.PresentList = dataList
	return
}

// CheckHasKill 检查是否被击杀
func CheckHasKill(ctx context.Context, checkList map[string]int32, voteNum int, tagId string) (hasKill bool,
	err error) {
	var isExists bool
	for key, item := range checkList {
		if key == tagId {
			isExists = true
			if item <= int32(voteNum) {
				hasKill = true
				break
			}
		}
	}
	if !isExists {
		err = errs.NewCustomError(ctx, code.ConfigError, "config error")
	}
	return
}

// ReissuePresent 补发日本地区礼包
func (s *Dragonact0704Impl) ReissuePresent(ctx context.Context, req *pb.Empty) (rsp *pb.Empty, err error) {
	return
	rsp = &pb.Empty{}
	ctxBackground := context.Background()

	// 查出投票总数 ，分页查出 投票人员
	condition := map[string]interface{}{
		"storage_key": "vote",
		"Fsource_id":  "page-26527",
		"storage_tag": "3",
	}
	var num int64
	DB.DefaultConnect().Debug().WithContext(ctxBackground).Table("general_log_03").Where(condition).Where("created_at > ?",
		**********).Count(&num)
	if num == 0 {
		return
	}
	for page := 1; page <= int(num/100)+1; page++ {
		var dataList []generalModel.LogData
		DB.DefaultConnect().Debug().WithContext(ctxBackground).Table("general_log_03").Where(condition).
			Where("created_at > ?", **********).
			Order("created_at asc").Offset((page - 1) * 100).Limit(100).Find(&dataList)
		// 发货
		for _, item := range dataList {
			condition := map[string]interface{}{
				"uid":          item.UID,
				"account_type": 2,
			}
			DB.DefaultConnect().Debug().WithContext(ctxBackground).Table("dragonact0704_present_tmp").Where(condition).Limit(1).
				Update("account_type", 1)
		}
	}
	return
}

// ReissuePresentOld 补发日本地区礼包
func (s *Dragonact0704Impl) ReissuePresentOld(ctx context.Context, req *pb.Empty) (rsp *pb.Empty, err error) {
	return
	fmt.Println("---------------ReissuePresent---------------")
	rsp = &pb.Empty{}
	ctxBackground := context.Background()
	presentProxy := presentPb.NewPresentClientProxy()
	draggon2Proxy := pb.NewDragonact0704ClientProxy()
	// 查出投票总数 ，分页查出 投票人员
	condition := map[string]interface{}{
		"storage_key": "vote",
		"Fsource_id":  "page-26527",
		"storage_tag": "3",
	}
	var num int64
	DB.DefaultConnect().Debug().WithContext(ctxBackground).Table("general_log_03").Where(condition).Where("created_at > ?",
		**********).Count(&num)
	if num == 0 {
		return
	}
	for page := 1; page <= int(num/100)+1; page++ {
		var dataList []generalModel.LogData
		DB.DefaultConnect().Debug().WithContext(ctxBackground).Table("general_log_03").Where(condition).
			Where("created_at > ?", **********).
			Order("created_at asc").Offset((page - 1) * 100).Limit(100).Find(&dataList)
		// 发货
		for _, item := range dataList {
			ctxOne := context.Background()
			// 发放绑定礼包
			sendReq := &presentPb.SendPresentReq{
				FsourceId: "page-26527",
				PresentId: "Wand-**************-Pd1c2aeadab81",
			}
			uidList := strings.Split(item.UID, "-")
			if len(uidList) < 2 {
				continue
			}
			userAccount := &accountPb.UserAccount{
				Uid:         item.UID,
				AccountType: accountPb.AccountType_INTL,
				IntlAccount: &accountPb.IntlAccount{
					OpenId: uidList[1],
				},
			}
			accountData, _ := proto.Marshal(userAccount)
			callopts := []client.Option{
				// 获取账号数据
				client.WithMetaData(metadata.UserAccount, accountData),
			}
			presentData, err := presentProxy.SendPresent(ctxOne, sendReq, callopts...)
			//  更新DB
			if err != nil {
				fmt.Println("---------------err---------------")
				fmt.Printf("%#v\n", err)
				continue
			}
			fmt.Printf("%#v\n", presentData)
			recordReq := &pb.RecordSendPresentReq{
				TagId:       "3",
				PresentData: presentData,
			}

			_, err = draggon2Proxy.RecordSendPresent(ctxOne, recordReq, callopts...)
			fmt.Println("---------------err 1---------------")
			fmt.Printf("%#v\n", err)
		}
	}
	return
}
