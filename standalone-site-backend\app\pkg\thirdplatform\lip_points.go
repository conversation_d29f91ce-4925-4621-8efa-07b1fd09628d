package thirdplatform

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/pkg/httpclient"
)

const (
	GetUserTotalPointsUrl = "/inner_direct/lipass/Points/GetUserTotalPointsInner"
)

func GetUserTotalPoints(openid string) (totalPoints int64, errCode error) {
	postData := map[string]interface{}{
		"openid": openid, // LIP动态
	}
	apiResultString, errCode := callLIPPointsApi(postData, GetUserTotalPointsUrl)
	if errCode != nil {
		return
	}
	var pointsResult model.GetUserTotalPointsResp
	jsonError := json.Unmarshal([]byte(apiResultString), &pointsResult)

	if jsonError != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserTotalPoints return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserTotalPoints return error, %s", apiResultString)
		errCode = errs.NewCustomError(context.Background(), code.GetUserTotalPointsInvalidResp, "GetUserTotalPoints | Exception occurred while processing the response body of the GetUserTotalPoints API call.")
		return
	}
	if pointsResult.Code != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserTotalPoints return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserTotalPoints return error, %s", apiResultString)
		errCode = errs.NewCustomError(context.Background(), code.GetUserTotalPointsReturnError, "GetUserTotalPoints | Call the LIP points module interface to obtain the user's total points information and return an error message.")
		return
	}
	totalPoints = pointsResult.Data.TotalPoints
	return
}

func callLIPPointsApi(postData interface{}, apiPath string) (apiResultString string, errCode error) {
	postJSON, err := json.Marshal(postData)
	postDataStr := string(postJSON)
	if err != nil {
		errCode = errs.NewCustomError(context.Background(), code.GetLipPointsInvalidParams, "callLIPPointsApi | Incorrect request parameters.")
		return
	}
	conf := config.GetConfig()
	requestURL := fmt.Sprintf("%s%s", conf.LIPPointsSetting.ServiceUrl, apiPath)
	optionOne := httpclient.ClientOption{
		URL:     requestURL,
		Type:    http.MethodPost,
		Timeout: 3 * time.Second,
		Header: map[string]string{
			"Content-Type": "application/json; charset=utf-8",
		},
		PostString: postDataStr,
	}

	// 发请求
	resultOption := httpclient.RequestOne(optionOne)
	// 结果判断
	if resultOption.RequestError != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call Lip points api return error, url %s", requestURL)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call Lip points api return error, postDataStr %s", postDataStr)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call Lip points api http request error, %v", resultOption.RequestError)
		errCode = errs.NewCustomError(context.Background(), code.GetLipPointsDataHttpError, "GetUserTotalPoints | Exception occurred while calling the Lip Points API.")
		return
	}

	apiResultString = resultOption.Result
	return
}
