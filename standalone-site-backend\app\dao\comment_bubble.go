package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetCommentBubbleInfo(commentBubbleId int64) (commentBubble *model.CommentBubble, err error) {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.CommentBubble{}).TableName())
	err = tx.Unscoped().Preload("Languages").Where("id = ?", commentBubbleId).First(&commentBubble).Error
	if err != nil {
		return
	}
	return
}

// 获取所有气泡，按照order升序，按照修改时间降序
func GetAllCommentBubbleDataWithLanguage(withDeleted bool) ([]*model.CommentBubble, error) {
	commentBubbles := make([]*model.CommentBubble, 0)
	if withDeleted {
		tx := DB.SelectConnect("db_standalonesite").Table((&model.CommentBubble{}).TableName())
		err := tx.Unscoped().Preload("Languages").Order("CASE WHEN `order` = 0 THEN 1 ELSE 0 END, `order` ASC, `modified_on` DESC").Find(&commentBubbles).Error
		if err != nil {
			return nil, err
		}
		return commentBubbles, nil
	} else {
		tx := DB.SelectConnect("db_standalonesite").Table((&model.CommentBubble{}).TableName())
		err := tx.Preload("Languages").Order("CASE WHEN `order` = 0 THEN 1 ELSE 0 END, `order` ASC, `modified_on` DESC").Find(&commentBubbles).Error
		if err != nil {
			return nil, err
		}
		return commentBubbles, nil
	}
}

// 获取所有气泡，按照id升序，按照修改时间降序
func GetAllCommentBubbleDataWithoutLanguage(withDeleted bool) ([]*model.CommentBubble, error) {
	commentBubbles := make([]*model.CommentBubble, 0)
	if withDeleted {
		tx := DB.SelectConnect("db_standalonesite").Table((&model.CommentBubble{}).TableName())
		err := tx.Unscoped().Order("id ASC").Find(&commentBubbles).Error
		if err != nil {
			return nil, err
		}
		return commentBubbles, nil
	} else {
		tx := DB.SelectConnect("db_standalonesite").Table((&model.CommentBubble{}).TableName())
		err := tx.Order("id ASC").Find(&commentBubbles).Error
		if err != nil {
			return nil, err
		}
		return commentBubbles, nil
	}
}
