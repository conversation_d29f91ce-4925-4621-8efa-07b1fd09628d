// Package vote 投票
package vote

import (
	"context"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	configModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	generalModel "trpc.act.logicial/app/model/general"
)

const storageKey = "vote"

// GetTeamListVotesFromTable 从表中获取数据
func GetTeamListVotesFromTable(ctx context.Context, sourceId string) (list map[string]int64, err error) {
	// 获取队伍数量ID
	tableName, err := configModel.GetTabNameWithGeneral(ctx, (&generalModel.ConfigModel{}).TableName(), sourceId,
		storageKey,
		(&generalModel.LogModel{}).TableName(), 100)
	if err != nil {
		return
	}
	LogCondition := map[string]interface{}{
		"Fsource_id":  sourceId,
		"storage_key": storageKey,
	}
	list = make(map[string]int64)
	type TagAmount struct {
		Tag    string  // 标记
		Amount float64 // 总积分
	}
	var amountList []TagAmount
	// 如果是查询一条
	if queryError := DB.DefaultConnect().WithContext(ctx).Debug().Table(tableName).Where(LogCondition).
		Group("storage_tag").
		Order("amount desc").Select("count(1) as amount,storage_tag as tag").Scan(&amountList).
		Error; queryError != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	for _, item := range amountList {
		list[item.Tag] = int64(item.Amount)
	}
	return
}
