package model

type PostAudit struct {
	*Model
	Post             *Post  `json:"-" gorm:"-"`
	PostActionType   int32  `json:"post_action_type"` // 帖子操作类型：1=新增，2=编辑
	PostUUID         string `json:"post_uuid"`        //动态唯一ID
	IntlOpenid       string `json:"intl_openid"`      //INTLSDK登录的用户openid
	Type             int32  `json:"type"`             // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
	Language         string `json:"language"`
	Platform         string `gorm:"column:platform" json:"platform"` //社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
	Title            string `gorm:"column:title" json:"title"`       //标题
	Content          string `gorm:"column:content" json:"content"`   //内容
	ContentSummary   string `json:"content_summary"`
	PicUrls          string `gorm:"column:pic_urls" json:"pic_urls"` //图片链接，逗号分隔
	ExtInfo          string `json:"ext_info"`
	TextRiskLevel    int    `json:"text_risk_level"`   //风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	TextRiskType     int    `json:"text_risk_type"`    //风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	PicRiskLevel     int    `json:"pic_risk_level"`    //风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	PicRiskType      int    `json:"pic_risk_type"`     //风险类别，0：其他恶意；100：正常；101：涉政；102：色情；103：低俗；104：性感；105：招嫖广告；106：业务广告；107：第三方广告；108：二维码；109：暴力血腥；110：侵权；999：其他
	Status           int    `json:"status"`            //审核状态 1:未处理;2:已发布;3:已忽略;4:重新编辑审核终止
	AuditUser        string `json:"audit_user"`        //审核用户
	AuditOn          int64  `json:"audit_on"`          //审核时间
	AuditIntroduce   string `json:"audit_introduce"`   //审核介绍
	GameID           string `json:"game_id"`           //游戏id
	AreaID           string `json:"area_id"`           //大区id
	MachineStatus    int    `json:"machine_status"`    //机审状态：0-未处理1-审核通过2-审核异常
	ArtificialStatus int    `json:"artificial_status"` //人审状态：0-未处理1-审核通过2-审核拒绝
}

func (p *PostAudit) TableName() string {
	return "p_post_audit"
}

type PostAuditKafkaData struct {
	PostUuids []string `json:"post_uuids"`
}
