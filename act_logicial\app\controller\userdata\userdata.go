package userdata

import (
	"context"

	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_userdata"
	logic "trpc.act.logicial/app/logic/userdata"
)

type UserdataImpl struct {
	pb.UnimplementedUserdata
}

func (s *UserdataImpl) GetUserAddressInfo(ctx context.Context, req *pb.GetUserAddressInfoReq) (rsp *pb.GetUserAddressInfoRsp, err error) {
	rsp = &pb.GetUserAddressInfoRsp{}
	logic.GetUserAddressInfo(context.Background(), req)
	return
}

func (s *UserdataImpl) GetUserPresentInfo(ctx context.Context, req *pb.GetUserPresentInfoReq) (rsp *pb.GetUserPresentInfoRsp, err error) {
	rsp = &pb.GetUserPresentInfoRsp{}
	logic.GetUserPresentInfo(context.Background(), req)
	return
}

func (s *UserdataImpl) GetUserPresentV2Info(ctx context.Context, req *pb.GetUserPresentInfoReq) (rsp *pb.GetUserPresentInfoRsp, err error) {
	rsp = &pb.GetUserPresentInfoRsp{}
	logic.GetUserPresentV2Info(context.Background(), req)
	return
}
