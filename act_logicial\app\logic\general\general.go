// Package general 通用
package general

import (
	"context"
	"fmt"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	configModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	"gorm.io/gorm"
	"trpc.act.logicial/app/global"
	general "trpc.act.logicial/app/model/general"
)

// GetUserGeneralRecordValSum 获取 storage_val 的和 并 过滤空条件
func GetUserGeneralRecordValSum(ctx context.Context, account accountPb.UserAccount, FsourceID string, StorageKey string,
	StorageTag string, storageVal string) (amount int, err error) {

	tableName, err := configModel.GetTabNameWithGeneral(ctx, (&general.ConfigModel{}).TableName(), FsourceID, StorageKey,
		(&general.LogModel{}).TableName(), 100)
	if err != nil {
		return
	}

	LogCondition := map[string]interface{}{
		"Fsource_id":   FsourceID,
		"uid":          account.Uid,
		"account_type": account.AccountType,
		"storage_key":  StorageKey,
		"storage_tag":  StorageTag,
		"storage_val":  storageVal,
	}
	if len(StorageTag) <= 0 {
		delete(LogCondition, "storage_tag")
	}
	if len(storageVal) <= 0 {
		delete(LogCondition, "storage_val")
	}
	sum := make([]int, 1)
	// 如果是查询一条
	if queryError := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(LogCondition).
		Pluck("count(1) as amount", &sum).Error; queryError != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	amount = sum[0]
	return amount, nil
}

// GetUserGeneralTimeRecordValSum 获取时间范围内 storage_val 的和 并 过滤空条件
func GetUserGeneralTimeRecordValSum(ctx context.Context, account accountPb.UserAccount, FsourceID string,
	StorageKey string, StorageTag string, startTime time.Time, endTime time.Time) (amount int, err error) {

	tableName, err := configModel.GetTabNameWithGeneral(ctx, (&general.ConfigModel{}).TableName(), FsourceID, StorageKey,
		(&general.LogModel{}).TableName(), 100)
	if err != nil {
		return
	}

	if err != nil {
		return amount, err
	}
	LogCondition := map[string]interface{}{
		"Fsource_id":   FsourceID,
		"uid":          account.Uid,
		"account_type": account.AccountType,
		"storage_key":  StorageKey,
		"storage_tag":  StorageTag,
	}
	if len(StorageTag) <= 0 {
		delete(LogCondition, "storage_tag")
	}
	sum := make([]int, 1)
	// 如果是查询一条
	if queryError := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(LogCondition).Where("created_at >= ?",
		startTime).Where("created_at <= ?", endTime).Pluck("count(1) as amount", &sum).
		Error; queryError != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	amount = sum[0]
	return amount, nil
}

// GetUserGeneralRecord 获取用户记录
func GetUserGeneralRecord(ctx context.Context, account accountPb.UserAccount, FsourceID string,
	StorageKey string) (LogDataList []general.LogData, err error) {
	// 如果验证完毕就去判断是否写入过
	tableName, err := configModel.GetTabNameWithGeneral(ctx, (&general.ConfigModel{}).TableName(), FsourceID, StorageKey,
		(&general.LogModel{}).TableName(), 100)
	if err != nil {
		return
	}
	LogCondition := map[string]interface{}{
		"Fsource_id":   FsourceID,
		"uid":          account.Uid,
		"account_type": account.AccountType,
		"storage_key":  StorageKey,
	}

	if queryError := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(LogCondition).Find(&LogDataList).
		Error; queryError != nil {
		if queryError == gorm.ErrRecordNotFound {
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return

}

// DoCreateGeneral 每次创建一条数据
func DoCreateGeneral(ctx context.Context, account accountPb.UserAccount, FsourceID string, StorageKey string,
	StorageTag string, StorageVal string) (int64, error) {

	LogCondition := map[string]interface{}{
		"Fsource_id":   FsourceID,
		"uid":          account.Uid,
		"account_type": account.AccountType,
		"storage_key":  StorageKey,
		"storage_tag":  StorageTag,
	}

	LogData := general.LogData{
		UID:         account.Uid,
		AccountType: int32(account.AccountType),
		StorageKey:  StorageKey,
		StorageVal:  StorageVal,
		StorageTag:  StorageTag,
		FsourceID:   FsourceID,
	}

	// 如果验证完毕就去判断是否写入过
	tableName, err := configModel.GetTabNameWithGeneral(ctx, (&general.ConfigModel{}).TableName(), FsourceID, StorageKey,
		(&general.LogModel{}).TableName(), 100)
	if err != nil {
		return 0, err
	}
	if createError := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(LogCondition).Create(&LogData).
		Error; createError != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return 0, err
	}

	// create
	return LogData.ID, nil
}

// DoCreateGeneralNums 每次创建多条条数据
func DoCreateGeneralNums(ctx context.Context, account accountPb.UserAccount, FsourceID string, StorageKey string,
	StorageTag string, StorageVal string, storageNums int) (err error) {

	LogCondition := map[string]interface{}{
		"Fsource_id":   FsourceID,
		"uid":          account.Uid,
		"account_type": account.AccountType,
		"storage_key":  StorageKey,
		"storage_tag":  StorageTag,
	}
	createDataList := make([]general.LogData, 0)
	for num := 1; num <= storageNums; num++ {
		LogData := general.LogData{
			UID:         account.Uid,
			AccountType: int32(account.AccountType),
			StorageKey:  StorageKey,
			StorageVal:  StorageVal,
			StorageTag:  StorageTag,
			FsourceID:   FsourceID,
		}
		createDataList = append(createDataList, LogData)
	}

	// 如果验证完毕就去判断是否写入过
	tableName, err := configModel.GetTabNameWithGeneral(ctx, (&general.ConfigModel{}).TableName(), FsourceID, StorageKey,
		(&general.LogModel{}).TableName(), 100)
	if err != nil {
		return
	}
	if createError := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(LogCondition).Create(&createDataList).
		Error; createError != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}

	// create
	return
}

// DoIncrCache 增加某个map field 的数量
func DoIncrCache(ctx context.Context, key string, FsourceID string, tag string, num int64) (exist bool, err error) {
	cacheKey := getRedisKey(ctx, key, FsourceID)
	isExist, err := redis.GetClient().Exists(ctx, cacheKey).Result()
	exist = true
	if isExist == 0 {
		exist = false
		return
	}
	err = redis.GetClient().HIncrBy(ctx, cacheKey, tag, num).Err()
	return
}

// GetCacheMapAll 获取Map所有的field值
func GetCacheMapAll(ctx context.Context, key string, FsourceID string) (resultMap map[string]string, exist bool,
	err error) {
	cacheKey := getRedisKey(ctx, key, FsourceID)
	isExist, err := redis.GetClient().Exists(ctx, cacheKey).Result()
	exist = true
	if isExist == 0 {
		exist = false
		return
	}
	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	resultMap, err = redis.GetClient().HGetAll(ctx, cacheKey).Result()
	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	return
}

// GetCacheMapField 获取单个field值
func GetCacheMapField(ctx context.Context, key string, FsourceID string, field string) (result string, exist bool,
	err error) {
	cacheKey := getRedisKey(ctx, key, FsourceID)
	exist, err = redis.GetClient().HExists(ctx, cacheKey, field).Result()
	if !exist {
		return
	}
	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	result, err = redis.GetClient().HGet(ctx, cacheKey, field).Result()
	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return
}

// SetRedisCache 写入redis 一个map缓存 并加上过期时间
func SetRedisCache(ctx context.Context, key string, FsourceID string, value interface{},
	expire time.Duration) (err error) {
	cacheKey := getRedisKey(ctx, key, FsourceID)
	// 写入redis缓存
	if err = redis.GetClient().HSet(ctx, cacheKey, value).Err(); err != nil { // set error
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}

	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}

	if expire > 0 {
		// 为哈希键设置过期时间
		if err = redis.GetClient().Expire(ctx, cacheKey, expire).Err(); err != nil { // Expire error
			err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
	}
	return
}
func getRedisKey(ctx context.Context, key string, FsourceID string) (redisKey string) {
	redisKeyPrefix := global.GetPrefix()
	redisKey = fmt.Sprintf("%s_%s_%s", redisKeyPrefix, key, FsourceID)
	return
}
