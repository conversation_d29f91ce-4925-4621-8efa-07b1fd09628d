package kafka

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-database/kafka"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

const (
	// NIKKEAnniversaryVoteProducer  生产者
	NIKKEAnniversaryVoteProducer = "trpc.kafka.nikke_anniversary_vote_producer.service"
	// NIKKE 2.5 周年生产者
	NIKKEAnniversary25thVoteProducer = "trpc.kafka.nikke_2Point5_anniversary_vote_producer.service"
)

// Produce 生产数据
func Produce(ctx context.Context, producerName, key, value string) error {
	proxy := kafka.NewClientProxy(producerName)
	err := proxy.Produce(ctx, []byte(key), []byte(value))
	if err != nil {
		log.ErrorContextf(ctx, "produce failed. key : %s, value : %s", key, value)
		return err
	}
	return nil
}
