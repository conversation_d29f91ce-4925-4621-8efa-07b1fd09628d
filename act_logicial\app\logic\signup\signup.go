// Package signup 报名
package signup

import (
	"context"
	"fmt"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.act.logicial/app/code"
	generalModel "trpc.act.logicial/app/model/general"
)

const (
	// SignUpStorageKey 报名key
	SignUpStorageKey = "signup"
)

// DoSignUp 添加报名记录
func DoSignUp(ctx context.Context, sourceID string) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)

	if err != nil {
		return
	}

	tableName, err := model.GetTabNameWithGeneral(ctx, (&generalModel.ConfigModel{}).TableName(), sourceID,
		SignUpStorageKey,
		(&generalModel.LogModel{}).TableName(), 100)
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("select data tableName: %v, %v, %v", tableName,
		userAccount.Uid, sourceID))
	if err != nil {
		return
	}

	condition := generalModel.LogData{
		UID:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		FsourceID:   sourceID,
		StorageKey:  SignUpStorageKey,
		StorageVal:  "1",
		StorageTag:  SignUpStorageKey,
	}

	dbErr := DB.DefaultConnect().WithContext(ctx).Table(tableName).FirstOrCreate(&condition, condition).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}

	log.WithFieldsContext(ctx, "log_type", "sign_up", "source_id", sourceID).Infof("sign up")

	return
}

// HasSignUp 查询是否报名
func HasSignUp(ctx context.Context, sourceID string) (has bool, err error) {

	userAccount, err := metadata.GetUserAccount(ctx)

	if err != nil {
		return false, err
	}

	tableName, err := model.GetTabNameWithGeneral(ctx, (&generalModel.ConfigModel{}).TableName(), sourceID,
		SignUpStorageKey,
		(&generalModel.LogModel{}).TableName(), 100)
	if err != nil {
		return false, err
	}

	condition := generalModel.LogData{
		UID:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		FsourceID:   sourceID,
		StorageKey:  SignUpStorageKey,
	}

	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("select data tableName: %v, %v, %v", tableName,
		userAccount.Uid, sourceID))

	var data generalModel.LogData

	dbErr := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition).Find(&data).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return false, err
	}

	if data.UID != "" {
		return true, nil
	}

	err = errs.NewCustomError(ctx, code.HasNotSignUp, "not signUp")
	return false, err
}
