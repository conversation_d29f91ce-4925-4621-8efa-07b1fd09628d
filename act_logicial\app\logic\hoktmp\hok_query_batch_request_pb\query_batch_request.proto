syntax = "proto3";

package hok_request;

option go_package = "git.code.oa.com/examples/hok_request";
option java_package = "com.trpc.examples.hok_request";

message TargetInfo {
  string TargetID = 1; // 指标ID
  string OpenID = 2;
  uint32 ZoneID = 3;
  uint32 HeroID = 4; // 英雄id
  string StartDay = 5; // 闭区间
  string EndDay = 6; // 闭区间
  string HeroIDList = 7; // 英雄id列表,逗号分隔
  string SkinIDList = 8; // 皮肤id列表,逗号分隔
  uint32 Wave = 9;
  uint32 CountryID = 10; // 国家id
  uint32 SkinID = 11; // 皮肤id
}

message DataInfo {
  int32 Code = 1; // 指标是否计算成功，0成功1失败
  string Msg = 2;
  TargetInfo Target = 3; // 指标ID
  string TargetValue = 4; // 指标值
}

message QueryBatchRequest {
  string ActivityID = 1; // 活动ID
  repeated TargetInfo Targets = 2; // 指标ID
}

message QueryBatchResponse {
  int32 Code = 1; // 0成功1失败
  string Msg = 2;
  repeated DataInfo Datas = 3;
}

service QueryBatch {
  rpc GetFeatures(QueryBatchRequest) returns(QueryBatchResponse) {}
}