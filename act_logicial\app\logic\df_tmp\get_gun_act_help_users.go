package df_tmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/mysql/df_activity_repo"
)

// GetGunActTaskHelpUsersProc
type GetGunActTaskHelpUsersProc struct {
	userInfo *accountPb.UserAccount
}

// GetGunActTaskHelpUsers 获取拼枪任务助力用户列表
func GetGunActTaskHelpUsers(ctx context.Context, req *pb.GetGunActTaskHelpUsersReq) (*pb.GetGunActTaskHelpUsersRsp, error) {
	log.DebugContextf(ctx, "GetGunActTaskHelpUsers enter, req: %v", req)
	rsp := &pb.GetGunActTaskHelpUsersRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "GetGunActTaskHelpUsers get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	proc := &GetGunActTaskHelpUsersProc{
		userInfo: &userAccount,
	}
	defer func() {
		if nil != err {
			log.ErrorContextf(ctx, "GetGunActTaskHelpUsers rsp error:%v", err)
		}
	}()
	err = proc.Process(ctx, req, rsp)
	if nil != err {
		return nil, err
	}
	return rsp, nil
}
func (p *GetGunActTaskHelpUsersProc) Process(ctx context.Context,
	req *pb.GetGunActTaskHelpUsersReq, rsp *pb.GetGunActTaskHelpUsersRsp) error {
	// 获取任务
	records, err := df_activity_repo.GunActivityRepoClient.GetTaskHelpUsersByTaskId(ctx,
		req.TaskId)
	if nil != err {
		log.ErrorContextf(ctx, "GetGunActTaskHelpUsersProc GetTaskHelpUsersByTaskId error: %v", err)
		return code.ErrSystemError
	}
	if records == nil || len(records) <= 0 {
		log.DebugContextf(ctx, "GetGunActTaskHelpUsersProc GetTaskHelpUsersByTaskId not find help users,"+
			" taskId: %v", req.TaskId)
		return nil
	}
	for _, record := range records {
		if record.HelpType == int(pb.GunActHelpType_FollowCommunity) { // 助力好友列表，不返回关注社群增加的助力值
			continue
		}
		rsp.Users = append(rsp.Users, &pb.GetGunActTaskHelpUsersRsp_Users{
			Email:    record.Email,
			HelpTime: uint32(record.CreateTime.Unix()),
		})
	}
	num := len(rsp.Users)
	log.DebugContextf(ctx, "GetGunActTaskHelpUsersProc GetTaskHelpUsersByTaskId success, help_num: %v", num)
	return nil
}
