package storage

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strconv"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	awsConfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/tencentyun/cos-go-sdk-v5"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
)

const CloudTypeTencentCloud string = "tencentcloud"
const CloudTypeAwsS3 string = "awss3"

// ObjectStorageService storage service interface that implement base AliOSS、MINIO or other
type ObjectStorageService interface {
	OssCreateService
	OssDeleteService
	OssDownloadService

	SignURL(objectKey string, expiredInSec int64) (string, error)
	ObjectURL(objetKey string) string
	ObjectKey(cUrl string) string
}

// OssCreateService Object Storage System Object Create service
type OssCreateService interface {
	PutObject(objectKey string, reader io.Reader, objectSize int64, contentType string, persistance bool) (string, error)
	PersistObject(objectKey string) error
	// DownloadFile(objectKey string)
}

// OssCreateService Object Storage System Object Delete service
type OssDeleteService interface {
	DeleteObject(objectKey string) error
	DeleteObjects(objectKeys []string) error
	IsObjectExist(objectKey string) (bool, error)
}

type OssDownloadService interface {
	DownloadFile(objectKey string) (error, *os.File)
}

// NewCOSServiceByGameid 根据传入的cmsgameid和cmsareaid判断，返回腾讯云COS桶或AWS-S3的操作对象
func NewCOSServiceByGameid(cmsGameId, cmsAreaId string) (ObjectStorageService, error) {
	conf := config.GetConfig()
	var cosConfig config.COSSetting
	for _, v := range conf.COS {
		cmsGameidS := strconv.Itoa(v.CmsGameId)
		if cmsGameidS == cmsGameId && v.CmsAreaId == cmsAreaId {
			cosConfig = *v
		}
	}
	if cosConfig.SecretID == "" || cosConfig.SecretKey == "" {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("storage.NewCOSServiceByGameid COS config empty , gameid: %s, areaid: %s", cmsGameId, cmsAreaId)

		return nil, errs.NewCustomError(context.Background(), code.COSConfigIsEmptyError, "The configuration information of the object storage function is empty")
	}

	var obj ObjectStorageService
	if cosConfig.CloudType == CloudTypeTencentCloud {
		obj = NewTencentCloudServiceByGameid(cosConfig)
	} else if cosConfig.CloudType == CloudTypeAwsS3 {
		obj = NewAWSS3ServiceByGameid(cosConfig)
	} else {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("storage.NewCOSServiceByGameid COS illegal cloud type , gameid: %s, areaid: %s", cmsGameId, cmsAreaId)

		return nil, errs.NewCustomError(context.Background(), code.IllegalCloudObjectType, "Illegal cloud object storage service type")
	}
	return obj, nil
}

// NewTencentCloudServiceByGameid 返回腾讯云COS桶操作对象
func NewTencentCloudServiceByGameid(cosConfig config.COSSetting) ObjectStorageService {
	u, _ := url.Parse(fmt.Sprintf("https://%s.cos.%s.myqcloud.com", cosConfig.Bucket, cosConfig.Region))
	su, _ := url.Parse(fmt.Sprintf("https://cos.%s.myqcloud.com", cosConfig.Region))

	client := cos.NewClient(&cos.BaseURL{BucketURL: u, ServiceURL: su}, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  cosConfig.SecretID,
			SecretKey: cosConfig.SecretKey,
		},
	})

	domain := "https://" + cosConfig.CDNDomain + "/"
	var cs OssCreateService
	//conf := config.GetConfig()
	//if config.CfgIf("OSS:TempDir") {
	//	cs = &cosCreateTempDirServant{
	//		client:    client,
	//		domain:    domain,
	//		bucketUrl: fmt.Sprintf("%s.cos.%s.myqcloud.com/", cosConfig.Bucket, cosConfig.Region),
	//		tempDir:   conf.ObjectStorage.TempDirSlash(),
	//	}
	//} else {
	cs = &cosCreateServant{
		client: client,
		domain: domain,
	}
	//}

	obj := cosServant{
		OssCreateService: cs,
		client:           client,
		domain:           domain,
	}
	return &obj
}

func NewAWSS3ServiceByGameid(awsS3Config config.COSSetting) ObjectStorageService {
	sdkConfig, err := awsConfig.LoadDefaultConfig(context.TODO())
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Error("storage.NewAWSS3ServiceByGameid Couldn't load default configuration")
	}
	client := s3.NewFromConfig(sdkConfig)

	domain := "https://" + awsS3Config.CDNDomain + "/"
	var cs OssCreateService
	cs = &awsS3CreateServant{
		client:     client,
		domain:     domain,
		bucketName: awsS3Config.Bucket,
	}

	obj := &awsS3Servant{
		OssCreateService: cs,
		client:           client,
		domain:           domain,
		bucketName:       awsS3Config.Bucket,
	}
	return obj
}
