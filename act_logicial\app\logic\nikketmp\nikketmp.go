// Package nikketmp TODO
// nikke 临时玩法
package nikketmp

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/object"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	lipPointsPB "git.woa.com/trpcprotocol/publishing_application/lipass_points"
	pointPb "git.woa.com/trpcprotocol/publishing_application/lipass_points"
	aigcPb "git.woa.com/trpcprotocol/publishing_marketing/aigc_sentiment"
	"github.com/go-sql-driver/mysql"
	"golang.org/x/net/context"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/model/nikke"
)

const (
	// TickerMillSecond 定时器触发时间
	TickerMillSecond = 1000
	// TickerCount 定时器触发数量
	TickerCount = 30
)

var (
	aigcProxy = aigcPb.NewSentimentClientProxy()
	// 创建一个channel用于存储合并后的请求数据
	requestChan = make(chan *SentimentRequest)
)

// SentimentRequest TODO
type SentimentRequest struct {
	data         *aigcPb.SentimentReqData
	ResponseChan chan *SentimentResponse
	HasResponse  bool
}

// SentimentResponse TODO
type SentimentResponse struct {
	Data *aigcPb.SentimentRspData
	Err  error
}

// init 定时器启动
func init() {
	// fmt.Println("----------------init sentiment---------------")
	// go handleRequests(requestChan)
}

const (
	// Level1 TODO
	Level1 = int32(iota + 1)
	// Level2 TODO
	Level2
	// Level3 TODO
	Level3
	// Level4 TODO
	Level4
	// Level5 TODO
	Level5
)

// RemoveSurroundingSymbols TODO
func RemoveSurroundingSymbols(str string) string {
	trimmed := strings.TrimFunc(str, func(r rune) bool {
		return unicode.IsSymbol(r) || unicode.IsPunct(r)
	})
	return trimmed
}

// CheckNikkeMessageEasterEggs 检查是否是彩蛋
func CheckNikkeMessageEasterEggs(ctx context.Context, message string) (level int32, err error) {
	// 字符串匹配，未匹配返回 level = 0
	// 将字符串转换为小写
	messageLowerStr := strings.ToLower(RemoveSurroundingSymbols(message))

	// 根据空格切分字符串并去除多余空格
	words := strings.Fields(messageLowerStr)
	// 获取配置字符串
	levelList := config.GetConfig().PriorityList

	// 逐个优先级匹配
	for _, levelItem := range levelList {
		matchMinIndex := MatchPriorityChar(ctx, words, levelItem.Keyword, int32(levelItem.Priority))
		if matchMinIndex == 0 {
			// 当前优先级未匹配到数据
			continue
		}
		return matchMinIndex, nil
	}
	return 0, nil
}

// MatchPriorityChar 根据传入的字符串匹配对应优先级字符
func MatchPriorityChar(ctx context.Context, message []string, matchChar string, level int32) int32 {

	if len(message) == 0 {
		return 0
	}

	matchCharLowerStr := strings.ToLower(matchChar)
	matchChars := strings.Split(matchCharLowerStr, "/")
	var exist bool
	// charIdxList := make([]int32, 0)
	switch level {
	case Level2, Level3, Level4, Level5:

		charIndexMap := GetUniqueCharAndMinIndex(message)
		withSpaceOrDashStrList := make([]string, 0)
		for _, matchCharValue := range matchChars {
			// 优先匹配单个关键词
			if exit := strings.ContainsAny(matchCharValue, " -"); !exit {
				if _, exist = charIndexMap[matchCharValue]; exist {
					// 某个字符存在, 返回当前优先级
					return level
				}
			} else {
				withSpaceOrDashStrList = append(withSpaceOrDashStrList, matchCharValue)
			}
		}
		// 再匹配存在空格或-的关键字
		for _, withSpaceOrDashStr := range withSpaceOrDashStrList {
			// 存在空格或-的关键字的index
			if _, exist = charIndexMap[withSpaceOrDashStr]; exist {
				// 直接整体匹配到
				return level
			}
			// 否则检查拆开都匹配到
			allExist := true
			if exist = strings.Contains(withSpaceOrDashStr, "-"); exist {
				matchCharList := strings.Split(withSpaceOrDashStr, "-")
				// 检验拆分后的单词是否都存在
				for _, matchCharV := range matchCharList {
					if _, exist = charIndexMap[matchCharV]; !exist {
						// 某个字符不存在
						allExist = false
						break
					}
				}
				// 都存在
				if allExist {
					return level
				}
			} else {
				matchCharList := strings.Split(withSpaceOrDashStr, " ")
				for _, matchCharV := range matchCharList {
					if _, exist = charIndexMap[matchCharV]; !exist {
						// 某个字符不存在
						allExist = false
						break
					}
				}
				// 都存在
				if allExist {
					return level
				}
			}
		}
		return 0
	case Level1:
		charIndexMap := GetUniqueCharAndMinIndex(message)
		// 匹配关键字
		for _, matchCharValue := range matchChars {
			if _, exist = charIndexMap[matchCharValue]; !exist {
				// 某个字符不存在
				return 0
			}
		}
		return Level1

	default:
		// 未匹配到
		return 0
	}
}

// GetUniqueCharAndMinIndex TODO
func GetUniqueCharAndMinIndex(message []string) map[string]int32 {
	if len(message) == 0 {
		return nil
	}
	charIndexMap := make(map[string]int32)
	// 字符串去重并将最小的idx放入map
	for idx, char := range message {
		if _, ok := charIndexMap[char]; ok {
			continue
		} else {
			charIndexMap[char] = int32(idx + 1)
		}
	}
	return charIndexMap
}

// GetSentimentType 获取情感类型
func GetSentimentType(ctx context.Context, message string) (sentimentType string, err error) {
	// 产生uuid
	// newUUID, errU := uuid.NewRandom()
	// if errU != nil {
	// 	err = errs.NewCustomError(ctx, code.GenerateUUIDErr, "err = %v", errU)
	// 	return
	// }
	requestResponseChan := make(chan *SentimentResponse)
	defer close(requestResponseChan)
	req := SentimentRequest{
		data: &aigcPb.SentimentReqData{
			Id:      "",
			Message: message,
		},
		ResponseChan: requestResponseChan,
	}
	requestChan <- &req
	resp := <-requestResponseChan
	if resp.Err != nil {
		err = errs.NewCustomError(ctx, code.GetSentimentTypeErr, "err = %v", resp.Err)
		return
	}
	sentimentType = resp.Data.SentimentType
	exists, _ := object.InArray(sentimentType, []string{"positive", "negative"})
	if exists == false {
		sentimentType = "negative"
	}
	return
}

func handleRequests(requestChan chan *SentimentRequest) {
	var requests []*SentimentRequest
	var count int
	ticker := time.NewTicker(TickerMillSecond * time.Millisecond)
	ctx := context.Background()
	for {
		select {
		case <-ticker.C:
			if count > 0 {
				log.WithFieldsContext(ctx, "log_type", "nikke_sentiment_trigger", "trigger_type", "ticker", "count", string(count)).
					Infof("trigger by ticker")
				go callAPI(requests)
			}
			requests = nil
			count = 0
			// 重置定时器
			ticker.Stop()
			ticker = time.NewTicker(TickerMillSecond * time.Millisecond)

		case req := <-requestChan:
			// 这里接收到一个请求
			requests = append(requests, req)
			count++
			if count >= TickerCount {
				log.WithFieldsContext(ctx, "log_type", "nikke_sentiment_trigger", "trigger_type", "count", "count", string(count)).
					Infof("trigger by count")
				go callAPI(requests)
				requests = nil
				count = 0
				// 重置定时器
				ticker.Stop()
				ticker = time.NewTicker(TickerMillSecond * time.Millisecond)
			}
		}
	}
}

func callAPI(sentimentList []*SentimentRequest) {
	// 模拟调用API，这里只是简单地将合并的数据原样返回
	reqList := []*aigcPb.SentimentReqData{}
	reqMap := make(map[string]*SentimentRequest)
	for index, item := range sentimentList {
		id := strconv.Itoa(index + 1)
		reqList = append(reqList, &aigcPb.SentimentReqData{
			Id:      id,
			Message: item.data.Message,
		})
		reqMap[id] = item
	}
	GetSentimentReq := &aigcPb.GetSentimentByListReq{
		List: reqList,
	}
	ctx := context.Background()
	rsp, errRsp := aigcProxy.GetSentimentByList(ctx, GetSentimentReq)
	if errRsp != nil {
		for _, item := range sentimentList {
			// 将错误信息发送给对应的response channel
			item.ResponseChan <- &SentimentResponse{Err: errRsp}
		}
		return
	}

	for _, item := range rsp.List {
		// 将错误信息发送给对应的response channel
		reqMap[item.Id].ResponseChan <- &SentimentResponse{Data: item}
		reqMap[item.Id].HasResponse = true
	}
	//  如果没有返回结果
	for _, item := range sentimentList {
		if item.HasResponse == false {
			err := errs.NewCustomError(ctx, code.GetSentimentTypeNotResponse, "aigc not response")
			item.ResponseChan <- &SentimentResponse{Err: err}
		}
	}
	return
}

// AddSendLog0101 TODO
func AddSendLog0101(ctx context.Context, FsourceId string, presentId string, langType string) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var sendTempData nikke.SendTemp0101
	// 判断表里是否插入
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
		"present_id":   presentId,
		"Fsource_id":   FsourceId,
	}
	db := DB.DefaultConnect().WithContext(ctx).Where(condition).Attrs(&nikke.SendTemp0101{
		LangType: langType,
	}).FirstOrCreate(&sendTempData)
	if db.Error != nil {
		// 检查 err 是否为重复插入错误
		if isNotDuplicateInsertError(db.Error) {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
	}
	return
}

// AddSendLogValentine nikke情人节活动添加发货记录
func AddSendLogValentine(ctx context.Context, FsourceId string, tag int32, langType string) (err error) {
	// 判断一下今天是否有插入过数据
	send := false
	if tag > 2 && tag < 13 {
		err, send = IsSendValentineToday(ctx, FsourceId)
		if send {
			return
		}
	}
	userAccount, err := metadata.GetUserAccount(ctx)

	if err != nil {
		return
	}
	presentIDList := map[string]map[int]string{
		"page-26601": { // 国际服
			1:  "Wand-**************-Pee5c570e099c", // NIKKE-情人节国际服--首次登录
			2:  "Wand-**************-P95f289c195ae", // NIKKE-情人节国际服--首次分享
			3:  "Wand-**************-Pd94a63c0cc3f", // NIKKE-情人节国际服--拿铁咖啡
			4:  "Wand-**************-Pf59a1cb7f796", // NIKKE-情人节国际服--奇怪的咖哩饭
			5:  "Wand-**************-P4fd0ba99b65f", // NIKKE-情人节国际服--黑森林蛋糕
			6:  "Wand-**************-Pa7b5b423e489", // NIKKE-情人节国际服--炸鸡
			7:  "Wand-**************-Pe8209b0d443a", // NIKKE-情人节国际服--热巧克力
			8:  "Wand-**************-Pbfe8291e4837", // NIKKE-情人节国际服--爱心蛋包饭
			9:  "Wand-**************-P4a119c7124e6", // NIKKE-情人节国际服--南瓜团子汤
			10: "Wand-**************-P99a15a12dc2b", // NIKKE-情人节国际服--哈密瓜芒果苏打
			11: "Wand-20240111085216-P22507be9afd2", // NIKKE-情人节国际服--豪华芭菲
			12: "Wand-20240111084638-P0fc288a2faae", // NIKKE-情人节国际服--草莓奶油饼干
			13: "Wand-20240115015547-Pd4fbff6bba28", // NIKKE-情人节国际服--收集完全部料理
		},
		"page-26602": { // 港澳台
			1:  "Wand-20240111083249-P3da4cf4d929c", // NIKKE-情人节港澳台--首次登录
			2:  "Wand-20240111083429-Pee54424cee51", // NIKKE-情人节港澳台--首次分享
			3:  "Wand-20240111083451-Pc936b59d0b2b", // NIKKE-情人节港澳台--拿铁咖啡
			4:  "Wand-20240111083526-Pbc25af007e52", // NIKKE-情人节港澳台--奇怪的咖哩饭
			5:  "Wand-20240111083544-P6adea21ddc89", // NIKKE-情人节港澳台--黑森林蛋糕
			6:  "Wand-20240111083601-Pc3356d628435", // NIKKE-情人节港澳台--炸鸡
			7:  "Wand-20240111083620-Pcf7564faf6b8", // NIKKE-情人节港澳台--热巧克力
			8:  "Wand-20240111083642-P90b00931c4c4", // NIKKE-情人节港澳台--爱心蛋包饭
			9:  "Wand-20240111083718-P32a8a49e06bc", // NIKKE-情人节港澳台--南瓜团子汤
			10: "Wand-**************-P0f7e181e333b", // NIKKE-情人节港澳台--哈密瓜芒果苏打
			11: "Wand-**************-P83534e4b2814", // NIKKE-情人节港澳台--豪华芭菲
			12: "Wand-**************-P04b3e4f01566", // NIKKE-情人节港澳台--草莓奶油饼干
			13: "Wand-**************-P0b3183c108c3", // NIKKE-情人节国际服--收集完全部料理
		},
	}
	var sendTempData nikke.SendTempValentine
	// 判断表里是否插入
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
		// "uid":          "29080-17803888811217356039",
		// "account_type": 1,
		"present_id": presentIDList[FsourceId][int(tag)],
		"Fsource_id": FsourceId,
		"tag":        tag,
	}
	db := DB.DefaultConnect().WithContext(ctx).Where(condition).Attrs(&nikke.SendTempValentine{
		LangType: langType,
	}).FirstOrCreate(&sendTempData)
	if db.Error != nil {
		// 检查 err 是否为重复插入错误
		if isNotDuplicateInsertError(db.Error) {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
	}
	log.WithFieldsContext(ctx, "log_type", "NikkeValentineAddLog", "str_field_1", presentIDList[FsourceId][int(tag)]).
		Infof("NikkeValentineAddLogAddSendLog")
	return
}

// isNotDuplicateInsertError 是否不是mysql重复插入报错
func isNotDuplicateInsertError(err error) bool {
	mysqlErr, ok := err.(*mysql.MySQLError)
	if !ok {
		return false
	}
	// Error 1062: Duplicate entry for key
	return mysqlErr.Number != 1062
}

// ScheduledSend TODO
func ScheduledSend(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	tableName := nikke.SendTemp0101{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(nikke.SendTemp0101{}.TableName()).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页50条
	pageSize := 50
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	gameProxy := gamePb.NewGameClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		offset := (pageNumber - 1) * pageSize
		var logData = []nikke.SendTemp0101{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Where(condition).Offset(offset).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", logData))
		for _, v := range logData {
			wg.Add(1)
			go func(data nikke.SendTemp0101) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(string(accountData))

				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				// 获取角色信息
				gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: v.FsourceID}

				gameRoleInfo, err := gameProxy.GetSavedRoleInfo(newCtx, gameReq, callopts...)
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("gameRoleInfo：%v", gameRoleInfo))

				if err == nil {
					log.WithFieldsContext(ctx, "ScheduledSend log", "debug").Infof(fmt.Sprintf("v: %#v", v))
					// 发送礼包
					sendReq := &presentPb.SendPresentReq{
						FsourceId: v.FsourceID,
						PresentId: v.PresentID,
						RoleInfo:  gameRoleInfo,
					}
					accountData, _ = proto.Marshal(&accountPb.UserAccount{
						Uid:         data.UID,
						AccountType: accountPb.AccountType(data.AccountType),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							GameId:    gameRoleInfo.GameId,
							ChannelId: 3,
						},
					})
					callopts = []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
						client.WithMetaData(metadata.LangType, []byte(data.LangType)),
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
					}
					if sendErr != nil {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						fmt.Println("---------------sendErr---------------")
						fmt.Printf("%#v\n", sendErr.Error())
						log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err111:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						if strings.Contains(sendErr.Error(), "package limit left not enough") {
							DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						}
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err:%v", sendErr))
					// 修改发货状态
				} else {
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("GetRoleInfo] service err:%v", err))

				}
				wg.Done()
			}(v)

		}
		wg.Wait()

	}
	return
}

// ScheduledSendValentine TODO
func ScheduledSendValentine(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	tableName := nikke.SendTempValentine{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(nikke.SendTempValentine{}.TableName()).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页50条
	pageSize := 50
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	gameProxy := gamePb.NewGameClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		offset := (pageNumber - 1) * pageSize
		var logData = []nikke.SendTempValentine{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Where(condition).Offset(offset).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", logData))
		for _, v := range logData {
			wg.Add(1)
			go func(data nikke.SendTempValentine) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(string(accountData))

				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				// 获取角色信息
				gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: data.FsourceID}

				gameRoleInfo, err := gameProxy.GetSavedRoleInfo(newCtx, gameReq, callopts...)
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("gameRoleInfo：%v", gameRoleInfo))

				if err == nil {
					log.WithFieldsContext(ctx, "ScheduledSend log", "debug").Infof(fmt.Sprintf("v: %#v", data))
					// 发送礼包
					sendReq := &presentPb.SendPresentReq{
						FsourceId: data.FsourceID,
						PresentId: data.PresentID,
						RoleInfo:  gameRoleInfo,
					}
					accountData, _ = proto.Marshal(&accountPb.UserAccount{
						Uid:         data.UID,
						AccountType: accountPb.AccountType(data.AccountType),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							GameId:    gameRoleInfo.GameId,
							ChannelId: 3,
						},
					})
					callopts = []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
						client.WithMetaData(metadata.LangType, []byte(data.LangType)),
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
					}
					delErr := errs.ParseError(ctx, sendErr)
					// 达到个人日志
					if delErr.Code == 400018 || delErr.Code == 400021 {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						fmt.Println("---------------sendErr---------------")
						fmt.Printf("%#v\n", sendErr.Error())
						log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err111:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						// if strings.Contains(sendErr.Error(), "package limit left not enough") {
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// }
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err:%v", sendErr))
					// 修改发货状态
				} else {
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("GetRoleInfo] service err:%v", err))

				}
				wg.Done()
			}(v)

		}
		wg.Wait()

	}
	return
}

// IsSendValentineToday TODO
func IsSendValentineToday(ctx context.Context, FsourceId string) (err error, make bool) {
	make = false

	// 如果过了配置的时间就不限制次数
	str := config.GetConfig().NikkeValentineNoCheckMakeDate
	layout := "2006/01/02 15:04:05"
	location, _ := time.LoadLocation("Asia/Tokyo") // 设置时区为 UTC+9
	t, err := time.ParseInLocation(layout, str, location)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	// 将 time.Time 类型转换为时间戳
	timestamp := t.Unix()
	// 获取当前时间
	now := time.Now()
	nowInUTC9 := now.In(location)
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()

	// 获取当前时间的时间戳
	currentTimestamp := nowInUTC9.Unix()

	if currentTimestamp > timestamp {
		return
	}

	// 查询是否当天已经发送
	userAccount, err := metadata.GetUserAccount(ctx)
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
		// "uid":          "29080-17803888811217356039",
		// "account_type": 1,
		"Fsource_id": FsourceId,
	}
	var sendLogs []nikke.SendTempValentine
	db := DB.DefaultConnect().WithContext(ctx).Table(nikke.SendTempValentine{}.TableName()).
		Where(condition).Find(&sendLogs)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	for _, value := range sendLogs {
		if value.Tag > 2 && value.CreatedAt >= startOfDay {
			make = true
			break
		}
	}
	return
}

// ValentineLogs 插损发送礼包记录
func ValentineLogs(ctx context.Context, FsourceId string) (err error, sendLogs []int32) {
	// 查询数据库 获取已经发奖的列表
	userAccount, err := metadata.GetUserAccount(ctx)
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
		// "uid":          "29080-17803888811217356039",
		// "account_type": 1,
		"Fsource_id": FsourceId,
	}
	var dbLogs []nikke.SendTempValentine
	db := DB.DefaultConnect().WithContext(ctx).Table(nikke.SendTempValentine{}.TableName()).
		Where(condition).Find(&dbLogs)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	for _, value := range dbLogs {
		sendLogs = append(sendLogs, int32(value.Tag))
	}
	return
}

// LipPoint25thKafka TODO
// lip2.5骤减累充
func LipPoint25thKafka(ctx context.Context, param *lipPointsPB.UserMidasPaySyncReq) (err error) {
	proxy := pointPb.NewPointsClientProxy()

	reqParam := &pointPb.GetPaymentPointsReq{
		Scene:      param.Scene,
		Amount:     param.Amount,
		IntlGameId: int32(param.IntlGameId),
		LipOpenid:  param.LipPointsOpenid,
	}
	resultRsp, err := proxy.GetPaymentPoints(ctx, reqParam)
	if err != nil {
		return
	}
	extentContent := make(map[string]interface{})
	extentContent["order_id"] = param.Serial
	extentContent["amount"] = param.Amount
	extentContent["game_openid"] = param.GameOpenid
	extentContent["num"] = resultRsp.Points
	extentContent["currency"] = param.Currency
	extentContent["open_id"] = param.GameOpenid
	extentContent["lip_open_id"] = param.LipPointsOpenid

	tlogData := report.ReportTlogData{
		Action:         "backup_nikke_outside_pay_detail",
		SubAction:      "cm_click",
		OriginalGameId: fmt.Sprintf("%v", param.IntlGameId),
		ExtentContent:  extentContent,
		Header: report.ReportTlogHeader{
			XLanguage: "en",
			XGameId:   int(param.IntlGameId),
			XAreaid:   "",
			XSource:   "pc_web",
		},
	}
	report.ReportTlog(ctx, tlogData)

	var data *nikke.LipPointNikke2Point5
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(nikke.LipPointNikke2Point5{}.TableName()).
		Where("uid = ?", fmt.Sprintf("%v-%v", param.IntlGameId, param.LipPointsOpenid)).
		First(&data)
	if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	// 如果没有就存储 有的话就添加在存储
	data.UID = fmt.Sprintf("%v-%v", param.IntlGameId, param.LipPointsOpenid)
	data.Point = resultRsp.Points + data.Point
	db = DB.DefaultConnect().Debug().WithContext(ctx).Table(nikke.LipPointNikke2Point5{}.TableName()).
		Where("uid = ?", fmt.Sprintf("%v-%v", param.IntlGameId, param.LipPointsOpenid)).
		Save(&data)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	return
}

// GetLipPointNikke2Point5 TODO
func GetLipPointNikke2Point5(ctx context.Context, gameId string) (point int32, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var data *nikke.LipPointNikke2Point5
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(nikke.LipPointNikke2Point5{}.TableName()).
		Where("uid = ?", fmt.Sprintf("%v-%v", gameId, strings.Split(userAccount.Uid, "-")[1])).
		First(&data)
	if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	point = data.Point
	return
}
