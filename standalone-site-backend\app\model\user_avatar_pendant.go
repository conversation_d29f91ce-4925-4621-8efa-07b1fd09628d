package model

type UserAvatarPendant struct {
	*Model
	ID              int64  `gorm:"column:id;primary_key" json:"id"`                   //
	GameID          string `gorm:"column:game_id" json:"game_id"`                     //游戏id
	AreaID          string `gorm:"column:area_id" json:"area_id"`                     //区域id
	AvatarPendantID int64  `gorm:"column:avatar_pendant_id" json:"avatar_pendant_id"` //挂件id
	IntlOpenid      string `gorm:"column:intl_openid" json:"intl_openid"`             //国际openid
	ValidBeginAt    int64  `gorm:"column:valid_begin_at" json:"valid_begin_at"`       //有效开始时间
	// DayDuration     int    `gorm:"column:day_duration" json:"day_duration"`           //有效天数
	ValidEndAt int64  `gorm:"column:valid_end_at" json:"valid_end_at"` //有效结束时间
	Creator    string `gorm:"column:creator" json:"creator"`           //创建人
	Updater    string `gorm:"column:updater" json:"updater"`           //更新人
	IsWeared   int32  `gorm:"column:is_weared" json:"is_weared"`       //是否佩戴 0否 1是
}

func (a *UserAvatarPendant) TableName() string {
	return "p_user_avatar_pendant"
}
