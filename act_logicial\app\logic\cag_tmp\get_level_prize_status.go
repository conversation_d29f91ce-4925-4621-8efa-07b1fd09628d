package cag_tmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_cag_tmp"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/constant"
	"trpc.act.logicial/app/logic/cag_common"
	"trpc.act.logicial/app/service"
	"trpc.act.logicial/app/viewmodel"
)

// GetLevelPrizeStatusProc
type GetLevelPrizeStatusProc struct {
	userInfo *accountPb.UserAccount
}

// GetLevelPrizeStatus 查询等级奖励获取状态
func GetLevelPrizeStatus(ctx context.Context, req *pb.GetLevelPrizeStatusReq) (*pb.GetLevelPrizeStatusRsp, error) {
	log.DebugContextf(ctx, "GetLevelPrizeStatus enter, req: %v", req)
	rsp := &pb.GetLevelPrizeStatusRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "GetLevelPrizeStatus get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	proc := &GetLevelPrizeStatusProc{
		userInfo: &userAccount,
	}
	defer func() {
		if nil != err {
			log.ErrorContextf(ctx, "GetLevelPrizeStatus rsp error:%v", err)
		}
	}()
	err = proc.Process(ctx, rsp)
	if nil != err {
		return nil, err
	}
	return rsp, nil
}

func (p *GetLevelPrizeStatusProc) Process(ctx context.Context,
	rsp *pb.GetLevelPrizeStatusRsp) error {
	// 查询奖励记录
	res, err := cag_common.GetCagNewRolePrizeRecord(ctx, p.userInfo.Uid, constant.CAG_NEW_ROLE_PRIZE_TYPE_LEVEL, 0, 10)
	if err != nil {
		log.ErrorContextf(ctx, "GetLevelPrizeStatus getCagNewRolePrizeRecord failed, err:%v, userId: %v",
			err, p.userInfo.Uid)
		return code.ErrSystemError
	}
	hasLevel := make(map[uint32]bool) // 已经领取的等级
	for _, v := range res {
		hasLevel[uint32(v.Level)] = true
	}
	if config.GetConfig().CagNewRoleAct.PressureTest { // 压测
		for _, level := range []uint32{10, 20, 30} {
			hasGet := false
			if _, ok := hasLevel[level]; ok {
				hasGet = true
			}
			rsp.Prizes = append(rsp.Prizes, &pb.PrizeInfo{
				Type:   cast.ToString(constant.CAG_NEW_ROLE_PRIZE_TYPE_LEVEL),
				Level:  level,
				HasGet: hasGet,
			})
		}
		log.DebugContextf(ctx, "GetLevelPrizeStatus pressure test, return")
		return nil
	}

	// 检查ab等级是否达到领奖条件
	err = checkAbRoleLevel(ctx, p.userInfo.Uid, 5)
	if nil != err {
		log.ErrorContextf(ctx, "GetLevelPrizeStatus checkAbRoleLevel failed, err:%v, userId: %v", err, p.userInfo.Uid)
		return err
	}

	// 查询用户的cag游戏用户id
	cagOpenId, err := service.GetGameOpenId(ctx, constant.GAMEID_CAG)
	if nil != err {
		log.ErrorContextf(ctx, "GetCreateRolePrizeStatus GetCagOpenId error:%v, userId: %v", err, p.userInfo.Uid)
		return err
	}
	gameInfo := &viewmodel.CagUserGameInfoItem{}

	if "" == cagOpenId {
		log.DebugContextf(ctx, "GetCreateRolePrizeStatus GetCagOpenId cag openid is empty, userId: %v", p.userInfo.Uid)
		// 角色未创建
	} else {
		// 查询游戏角色等级
		areaId := ""
		id, ok := config.GetConfig().GameAreaIds[constant.GAMEID_CAG]
		if ok {
			areaId = cast.ToString(id)
		}
		err = service.GetUserGameInfoByOpenid(ctx, constant.GAMEID_CAG, constant.IDIP_CMD_CAG_GET_USER_INFO, areaId, cagOpenId, gameInfo)
		if code.ErrHasNoGameRole == err {
			log.DebugContextf(ctx, "ClaimLevelPrize cag openid has no game role, userId: %v, cagOpenId: %v",
				p.userInfo.Uid, cagOpenId)
		} else if nil != err {
			log.ErrorContextf(ctx, "ClaimLevelPrize get cag game user info error:%v, userId: %v, cagOpenId: %v",
				err, p.userInfo.Uid, cagOpenId)
			return err
		}
	}

	for _, level := range []uint32{10, 20, 30} {
		hasGet := false
		if _, ok := hasLevel[level]; ok {
			hasGet = true
		}
		rsp.Prizes = append(rsp.Prizes, &pb.PrizeInfo{
			Type:   cast.ToString(constant.CAG_NEW_ROLE_PRIZE_TYPE_LEVEL),
			Level:  level,
			HasGet: hasGet,
		})
	}

	if gameInfo.Level != "" {
		rsp.CagLevel = cast.ToUint32(gameInfo.Level)
	} else {
		rsp.CagLevel = 0
	}
	return nil
}
