package code

import "git.code.oa.com/trpc-go/trpc-go/errs"

const (
	// AbGameNotBind lip账号没有绑定暗区突围游戏账号
	AbGameNotBind = 520001
	// AbGameRoleLevelTooLow 暗黑游戏等级太低
	AbGameRoleLevelTooLow = 520002
	// HasCagRolePrize 已经领取过奖励
	HasCagRolePrize = 520003
	// HasCagRole cag角色已经在活动开始前创建，不发放创建奖励
	HasCagRole = 520004
	// HasNoCagRole cag角色不存在，不发放等级奖励
	HasNoCagRole = 520005
	// NotReachLevel 等级不够，不发放等级奖励
	NotReachLevel = 520006
	// HasNoGameRole 游戏角色不存在
	HasNoGameRole = 520007
)

var (
	ErrAbGameNotBind         = errs.New(AbGameNotBind, "not bind game")
	ErrAbGameRoleLevelTooLow = errs.New(AbGameRoleLevelTooLow, "level too low")
	ErrHasCagRolePrize       = errs.New(HasCagRolePrize, "already has prize")
	ErrHasCagRole            = errs.New(HasCagRole, "already has role")
	ErrHasNoCagRole          = errs.New(HasNoCagRole, "has no role")
	ErrNotReachLevel         = errs.New(NotReachLevel, "not reach level")
	ErrHasNoGameRole         = errs.New(HasNoGameRole, "has no game role")
)
