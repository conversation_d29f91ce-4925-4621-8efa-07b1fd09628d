package activity

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_activity"
	"trpc.publishing_application.standalonesite/app/logic/act"
)

type ActImpl struct {
	pb.UnimplementedActivity
}

// GetExcellentCreator 获取优秀创作者
func (s *ActImpl) GetExcellentCreator(ctx context.Context, req *pb.GetExcellentCreatorReq) (*pb.GetExcellentCreatorRsp, error) {
	rsp := &pb.GetExcellentCreatorRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	// if err != nil {
	// 	return nil, err
	// }
	intlOpenid := userAccount.Uid
	language := metadata.GetLangType(ctx)
	users, err := act.GetExcellentCreatorsInternal(ctx, intlOpenid, language, 10)
	rsp.List = users
	return rsp, err
}

// GetHighQualityCreation 获取优质创作
func (s *ActImpl) GetHighQualityCreation(ctx context.Context, req *pb.GetHighQualityCreationReq) (*pb.GetHighQualityCreationRsp, error) {
	language := metadata.GetLangType(ctx)
	rsp, err := act.GetHighQualityCreationInternal(ctx, req.PrimaryLabelId, req.SecondaryLabelId, language, 20, 0)
	return rsp, err
}
