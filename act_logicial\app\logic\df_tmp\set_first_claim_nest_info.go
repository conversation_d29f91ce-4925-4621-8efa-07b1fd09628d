package df_tmp

import (
	"context"
	"errors"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	model "trpc.act.logicial/app/model/df_activity"
	repo "trpc.act.logicial/app/mysql/df_activity_repo"
)

// SetFirstClaimNestInfo 设置用户首次掏鸟窝成功时间
func SetFirstClaimNestInfo(ctx context.Context,
	req *pb.SetFirstClaimNestInfoReq) (*pb.SetFirstClaimNestInfoRsp, error) {
	log.InfoContextf(ctx, "start SetFirstClaimNestInfo, req: %+v", req)
	account, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "SetFirstClaimNestInfo get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}

	// 查询用户活动信息是否已存在
	dbRecord, err := repo.NestActivityRepoClient.GetUserBirdNestReward(ctx, account.Uid)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// 报错 且 非文档不存在错误
		log.ErrorContextf(ctx, "GetUserBirdNestReward error:%v, uid:%v", err, account.Uid)
		return nil, code.ErrSystemError
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		// 文档存在
		return &pb.SetFirstClaimNestInfoRsp{FirstTime: uint64(dbRecord.FirstClaimTime)}, nil
	}

	// 文档不存在
	currentTime := time.Now().Unix()
	record := &model.UserBirdNestReward{
		UserId:         account.Uid,
		FirstClaimTime: currentTime,
	}

	err = repo.NestActivityRepoClient.AddUserBirdNestReward(ctx, record)
	if err != nil {
		log.ErrorContextf(ctx, "AddUserBirdNestReward error:%v, record:%+v", err, record)
		return nil, code.ErrSystemError
	}
	return &pb.SetFirstClaimNestInfoRsp{FirstTime: uint64(currentTime)}, nil
}
