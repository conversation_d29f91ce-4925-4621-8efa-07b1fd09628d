// Package dragonact0704 龙之谷活动
package dragonact0704

import (
	"trpc.act.logicial/app/model"
)

// Dragonact0704RecordTmpModel model
type Dragonact0704RecordTmpModel struct {
	model.AppModel
}

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名
func (Dragonact0704RecordTmpModel) TableName() string {
	return "dragonact0704_record_tmp"
}

// Dragonact0704RecordTmp 表结构
type Dragonact0704RecordTmp struct {
	Dragonact0704RecordTmpModel
	Id          int    `gorm:"column:id;type:int(11) unsigned;primary_key;AUTO_INCREMENT;comment:primary key" json:"id"`
	Uid         string `gorm:"column:uid;type:varchar(64);NOT NULL" json:"uid"`
	AccountType int32  `gorm:"column:account_type;type:smallint(6);default:0;NOT NULL" json:"account_type"`
	TagId       string `gorm:"column:tag_id;type:varchar(64);default:;comment:龙序号;NOT NULL" json:"tag_id"`
	Status      int32  `gorm:"column:status;type:tinyint(1);default:0;comment:当前状态0:当前选择;1:历史选择;NOT NULL" json:"status"`
}

// Dragonact0704RankTmpModel model
type Dragonact0704RankTmpModel struct {
	model.AppModel
}

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名
func (Dragonact0704RankTmpModel) TableName() string {
	return "dragonact0704_rank_tmp"
}

// Dragonact0704RankTmp 表结构
type Dragonact0704RankTmp struct {
	Dragonact0704RankTmpModel
	Id    int    `gorm:"column:id;type:int(11) unsigned;primary_key;AUTO_INCREMENT;comment:primary key" json:"id"`
	TagId string `gorm:"column:tag_id;type:varchar(64);default:;comment:龙序号;NOT NULL" json:"tag_id"`
}

// Dragonact0704ReserveTmpModel model
type Dragonact0704ReserveTmpModel struct {
	model.AppModel
}

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名
func (Dragonact0704ReserveTmpModel) TableName() string {
	return "dragonact0704_reserve_tmp"
}

// Dragonact0704ReserveTmp 表结构
type Dragonact0704ReserveTmp struct {
	Dragonact0704ReserveTmpModel
	Id          int    `gorm:"column:id;type:int(11) unsigned;primary_key;AUTO_INCREMENT;comment:primary key" json:"id"`
	Uid         string `gorm:"column:uid;type:varchar(64);NOT NULL" json:"uid"`
	AccountType int32  `gorm:"column:account_type;type:smallint(6);default:0;NOT NULL" json:"account_type"`
	Type        int32  `gorm:"column:type;type:smallint(6);default:0;NOT NULL" json:"type"`
	ChannelId   string `gorm:"column:channel_id;type:varchar(64);default:;comment:预约频道;NOT NULL" json:"channel_id"`
}

// Dragonact0704PresentTmpModel model
type Dragonact0704PresentTmpModel struct {
	model.AppModel
}

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名
func (Dragonact0704PresentTmpModel) TableName() string {
	return "dragonact0704_present_tmp"
}

// Dragonact0704PresentTmp 表结构
type Dragonact0704PresentTmp struct {
	Dragonact0704PresentTmpModel
	Id          int    `gorm:"column:id;type:int(11) unsigned;primary_key;AUTO_INCREMENT;comment:primary key" json:"id"`
	Uid         string `gorm:"column:uid;type:varchar(64);NOT NULL" json:"uid"`
	AccountType int32  `gorm:"column:account_type;type:smallint(6);default:0;NOT NULL" json:"account_type"`
	TagId       string `gorm:"column:tag_id;type:varchar(64);default:tag_id;comment:龙序号;NOT NULL" json:"channel_id"`
	Cdkey       string `gorm:"column:cdkey;type:varchar(64);default:cdkey;comment:预约频道;NOT NULL" json:"cdkey"`
}
