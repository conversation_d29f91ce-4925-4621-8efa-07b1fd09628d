// Package df_bhd TODO
package df_bhd

import "trpc.act.logicial/app/model"

// DfBhdSpeedRankModel TODO
type DfBhdSpeedRankModel struct {
	model.AppModel
}

// TableName TODO
func (DfBhdSpeedRankModel) TableName() string {
	return "df_bhd_speed_rank"
}

// DfBhdSpeedRank TODO
type DfBhdSpeedRank struct {
	DfBhdSpeedRankModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	Memberid1   string `gorm:"type:varchar(50);column:memberid1;"`
	Memberid2   string `gorm:"type:varchar(50);column:memberid2;"`
	Memberid3   string `gorm:"type:varchar(50);column:memberid3;"`
	Memberid4   string `gorm:"type:varchar(50);column:memberid4;"`
	Name1       string `gorm:"type:varchar(255);column:name1;"`
	Name2       string `gorm:"type:varchar(255);column:name2;"`
	Name3       string `gorm:"type:varchar(255);column:name3;"`
	Name4       string `gorm:"type:varchar(255);column:name4;"`
	Avatar1     string `gorm:"type:varchar(255);column:avatar1;"`
	Avatar2     string `gorm:"type:varchar(255);column:avatar2;"`
	Avatar3     string `gorm:"type:varchar(255);column:avatar3;"`
	Avatar4     string `gorm:"type:varchar(255);column:avatar4;"`
	Dteventtime string `gorm:"type:varchar(255);column:dteventtime;"`
	Dtstatdate  string `gorm:"type:varchar(8);column:dtstatdate;"`
	Playtime    int64  `gorm:"type:int(11);column:playtime;"`
	Rank        int64  `gorm:"type:int(11);column:rank;"`
	MapId       string `gorm:"type:string;column:map_id;"`
	RankType    string `gorm:"type:string;column:rank_type;"`
}

// DfBhdSelfPassTimesModel TODO
type DfBhdSelfPassTimesModel struct {
	model.AppModel
}

// TableName TODO
func (DfBhdSelfPassTimesModel) TableName() string {
	return "df_bhd_self_pass_times"
}

// DfBhdSelfPassTimes TODO
type DfBhdSelfPassTimes struct {
	DfBhdSelfPassTimesModel
	ID         int64  `gorm:"type:int(11);column:id;primary_key"`
	Uid        string `gorm:"type:varchar(50);column:uid;"`
	Times      int32  `gorm:"type:int(11);column:times;"`
	Dtstatdate string `gorm:"type:varchar(8);column:dtstatdate;"`
}
