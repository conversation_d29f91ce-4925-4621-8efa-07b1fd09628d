package networkagent

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.act.logicial/app/code"
)

const securityKey = "Zzkgv911Bmbt6na6zfUk"

func ValidateSecurityKey(ctx context.Context, secondTimestamp int64, key string) (err error) {
	log.WithFieldsContext(ctx, "log_type", "Debug").Infof("sencondTimestamp: %d, key: %s", secondTimestamp, key)
	// return nil
	nowTimestamp := time.Now().Unix()
	if nowTimestamp-secondTimestamp > 60 {
		log.WithFieldsContext(ctx, "log_type", "Error").Errorf("time expired: secondTimestamp: %d, nowTimestamp: %d", secondTimestamp, nowTimestamp)
		return errs.NewCustomError(ctx, code.RequestExpired, "headers  miss client ip")
	}
	rawKey := fmt.Sprintf("%d_%s", secondTimestamp, securityKey)
	encryptedKey := []byte(securityKey)
	h := hmac.New(sha256.New, encryptedKey)
	h.Write([]byte(rawKey))
	hashBytes := h.Sum(nil)
	// 将哈希值转换为十六进制字符串
	hashString := hex.EncodeToString(hashBytes)
	res := (hashString == key)
	if !res {
		log.WithFieldsContext(ctx, "log_type", "Error").Errorf("valid failed: hashString: %s, key: %s", hashString, key)
		return errs.NewCustomError(ctx, code.InvalidRequest, "headers  miss client ip")
	}
	return nil
}
