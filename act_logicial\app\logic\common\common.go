// Package common TODO
package common

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	userAccountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	"git.code.oa.com/trpcprotocol/publishing_marketing/game"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_common"
	"git.woa.com/trpcprotocol/publishing_marketing/present_present_v2"
	redisOrgin "github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/global"
	commonModel "trpc.act.logicial/app/model/common"
)

// PostParam TODO
type PostParam struct {
	Domain      string
	Uri         string
	Body        string
	QueryParams map[string]string
	SecretId    string
	SecretKey   string
}

// AccountInfo TODO
type AccountInfo struct {
	Account  string `json:"account"`
	RoleName string `json:"role_name"`
	PlatId   string `json:"plat_id"` // 玩家平台id。0 : IOS 1: andriod 2: 无效的值，不使用 3: PC
}

// CheckDataItemTask TODO
type CheckDataItemTask struct {
	SceneId int         `json:"scene_id"`
	Text    string      `json:"text"`
	Account AccountInfo `json:"account"`
}

// TasksList TODO
type TasksList struct {
	Tasks []*CheckDataItemTask `json:"tasks"`
}

// GetUUID TODO
func GetUUID() string {
	// 生成随机的 UUID
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}

// GetHash TODO
func GetHash(input string) string {
	// 使用 MD5 哈希算法
	hash := md5.Sum([]byte(input))

	// 使用 SHA256 哈希算法
	// hash := sha256.Sum256([]byte(input))

	// 将哈希值转换为十六进制字符串
	hashString := hex.EncodeToString(hash[:])

	return hashString
}

// SortInt32Slice TODO
func SortInt32Slice(numbers []int32) []int32 {
	sort.Slice(numbers, func(i, j int) bool {
		return numbers[i] < numbers[j]
	})
	return numbers
}

// SplitAndTrim TODO
func SplitAndTrim(str string) []string {
	// 根据空格和连字符切分字符串
	split := strings.FieldsFunc(str, func(r rune) bool {
		return r == ' ' || r == '-'
	})

	// 去除切分后字符串中的多余空格
	trimmed := make([]string, len(split))
	for i, s := range split {
		trimmed[i] = strings.TrimSpace(s)
	}

	return trimmed
}

// RecordPeriodNumByRedis TODO
func RecordPeriodNumByRedis(ctx context.Context, FsourceID string, PeriodType pb.PeriodType, timeZone int32,
	expireSecond int32) (num int32, err error) {
	reidsPrefix := global.GetPrefix()
	periodVal, err := GetPeriodType(ctx, PeriodType, timeZone, 0)
	if err != nil {
		return
	}
	key := fmt.Sprintf("%s_%s_%s", reidsPrefix, FsourceID, periodVal)

	pipeLine := redis.GetClient().Pipeline()
	existsCmd := pipeLine.Exists(ctx, key)
	resultCmd := pipeLine.Incr(ctx, key)
	_, errR := pipeLine.Exec(ctx)
	// result, errR := redis.GetClient().Incr(ctx, key).Result()
	if errR != nil && errR != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis error, error = %v",
			errR.Error())
		return

	}
	existsVal, err1 := existsCmd.Result()
	if err1 != nil && err1 != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis error, error = %v",
			err1.Error())
		return
	}
	result, err2 := resultCmd.Result()
	if err2 != nil && err2 != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis error, error = %v",
			err2.Error())
		return
	}
	// 如果不存在，将上一个周期数据加到里面
	if existsVal == 0 {
		lastPeriodVal, _ := GetPeriodType(ctx, PeriodType, timeZone, -1)
		lastPeriodkey := fmt.Sprintf("%s_%s_%s", reidsPrefix, FsourceID, lastPeriodVal)
		lastVal, _ := redis.GetClient().Get(ctx, lastPeriodkey).Int()
		redis.GetClient().IncrBy(ctx, key, int64(lastVal))
		result = result + int64(lastVal)
		if expireSecond > 0 {
			redis.GetClient().Expire(ctx, key, time.Duration(expireSecond)*time.Second)
		}
	}
	num = int32(result)
	return
}

// GetPeriodNumByRedis TODO
func GetPeriodNumByRedis(ctx context.Context, FsourceID string, PeriodType pb.PeriodType, timeZone int32,
	periodOffset int32) (num int32, err error) {
	reidsPrefix := global.GetPrefix()
	periodVal, err := GetPeriodType(ctx, PeriodType, timeZone, periodOffset)
	if err != nil {
		return
	}
	key := fmt.Sprintf("%s_%s_%s", reidsPrefix, FsourceID, periodVal)
	result, errR := redis.GetClient().Get(ctx, key).Int()
	if errR != nil && errR != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis error, error = %v",
			errR.Error())
		return
	}
	num = int32(result)
	return
}

// ScheduleUpdatePeriodNum TODO
func ScheduleUpdatePeriodNum(ctx context.Context, FsourceID string, PeriodType pb.PeriodType, timeZone int32,
	expireSecond int32) (err error) {
	reidsPrefix := global.GetPrefix()
	periodVal, err := GetPeriodType(ctx, PeriodType, timeZone, 0)
	if err != nil {
		return
	}
	key := fmt.Sprintf("%s_%s_%s", reidsPrefix, FsourceID, periodVal)

	result, errR := redis.GetClient().SetNX(ctx, key, 0, time.Duration(expireSecond)*time.Second).Result()
	if errR != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis error, error = %v",
			errR.Error())
		return
	}
	if result == true {
		lastPeriodVal, _ := GetPeriodType(ctx, PeriodType, timeZone, -1)
		lastPeriodkey := fmt.Sprintf("%s_%s_%s", reidsPrefix, FsourceID, lastPeriodVal)
		lastVal, _ := redis.GetClient().Get(ctx, lastPeriodkey).Int()
		redis.GetClient().IncrBy(ctx, key, int64(lastVal))
	}
	return
}

// GetPeriodType TODO
func GetPeriodType(ctx context.Context, PeriodType pb.PeriodType, timeZone int32, periodOffset int32) (periodVal string,
	err error) {
	var dateFormat string
	switch PeriodType {
	case pb.PeriodType_Day:
		dateFormat = "20060102"
	case pb.PeriodType_Hour:
		dateFormat = "2006010215"
	default:
		err = errs.NewCustomError(ctx, code.PeriodTypeError, "period type not support")
		return
	}
	periodVal = time.Now().Add(time.Duration(timeZone+periodOffset) * time.Hour).Format(dateFormat)
	return
}

// RandomIntInRange 返回 1 到 max 之间的随机整数
func RandomIntInRange(max int64) (int64, error) {
	if max < 1 {
		return 0, fmt.Errorf("max must be greater than or equal to 1")
	}
	// 使用当前时间的纳秒数作为种子，确保每次运行时生成不同的随机数
	rand.Seed(time.Now().UnixNano())
	// 生成 1 到 max 之间的随机整数
	return rand.Int63n(max) + 1, nil
}

// GenerateUUID generates a random UUID
func GenerateUUID() (string, error) {
	u, err := uuid.NewRandom()
	if err != nil {
		return "", err
	}
	return u.String(), nil
}

// LockUsageShare 锁定份额
func LockUsageShare(ctx context.Context, Fsource_id string, openid string, shareMaxNum int, setTime int64) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var lockNum int64
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(commonModel.CommonShareLog{}.TableName()).
		Where("Fsource_id = ? and lock_uid = ? ", Fsource_id, userAccount.Uid).
		Count(&lockNum)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	if lockNum > 0 {
		err = errs.NewCustomError(ctx, code.AlreadyLockShare, "already lock share num")
		return
	}

	redisPrefixKey := global.GetPrefix()
	redisKey := fmt.Sprintf("%v-%v-%v", redisPrefixKey, Fsource_id, openid)
	count, err := redis.GetClient().Incr(ctx, redisKey).Result()
	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"LockUsageShare redis Incr error, \t [key]: {%v} \t[Error]:{%v} ", redisKey, err)
		return
	}
	if int(count) > shareMaxNum {
		// 代表已经超出了
		err = errs.NewCustomError(ctx, code.LimitShareNum, "Exceed the share")
		_, errR := redis.GetClient().Decr(ctx, redisKey).Result()
		if errR != nil {
			_ = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
				"LockUsageShare redis Decr error, \t [key]: {%v} \t[Error]:{%v} ", redisKey, err)
			return
		}
		return
	}
	// 已经可以锁定份额 插入sql

	// 获取当前时间
	currentTime := time.Now()

	// 将时间截断到分钟
	truncatedTime := currentTime.Truncate(time.Minute)
	data := &commonModel.CommonShareLog{
		FsourceId: Fsource_id,
		Uid:       openid,
		LockUid:   userAccount.Uid,
		Status:    0,
		Time:      setTime,
		StartTime: truncatedTime.Unix(),
	}

	db = DB.DefaultConnect().Debug().WithContext(ctx).Table(commonModel.CommonShareLog{}.TableName()).
		Where("Fsource_id = ? and uid = ? and lock_uid = ? and status = 0", Fsource_id, openid, userAccount.Uid).
		FirstOrCreate(&data)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	return
}

// AddUsageShareLog 更改占用份额状态
func AddUsageShareLog(ctx context.Context, FsourceId string, openid string, status int, shareMaxNum int32) (err error) {
	var errS error
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	// 保证占用的唯一性
	var data *commonModel.CommonShareLog

	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(commonModel.CommonShareLog{}.TableName()).
		Where("Fsource_id = ? and lock_uid = ?", FsourceId, userAccount.Uid).
		First(&data)
	if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	if status == 0 && data.Status == 2 {
		redisPrefixKey := global.GetPrefix()
		redisKey := fmt.Sprintf("%v-%v-%v", redisPrefixKey, FsourceId, openid)
		count, err := redis.GetClient().Incr(ctx, redisKey).Result()
		if err != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
				"LockUsageShare redis Incr error, \t [key]: {%v} \t[Error]:{%v} ", redisKey, err)
			return err
		}
		log.WithFieldsContext(ctx, "log_type", "AddUsageShareLog").Infof("redisKey: %v, count: %v", redisKey, count)
		if int(count) > int(shareMaxNum) {
			// 代表已经超出了
			_, errR := redis.GetClient().Decr(ctx, redisKey).Result()
			if errR != nil {
				_ = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
					"LockUsageShare redis Decr error, \t [key]: {%v} \t[Error]:{%v} ", redisKey, err)
				return err
			}
			err = errs.NewCustomError(ctx, code.LimitShareNum, "Exceed the share")
			return err
		}
	}

	data.Status = status
	data.FsourceId = FsourceId
	data.Uid = openid
	data.LockUid = userAccount.Uid
	if status == 0 {
		// 获取当前时间
		currentTime := time.Now()
		// 将时间截断到分钟
		truncatedTime := currentTime.Truncate(time.Minute)
		data.StartTime = truncatedTime.Unix()
	}

	if status == 1 {
		currentTime := time.Now()
		// 将时间截断到分钟
		truncatedTime := currentTime.Truncate(time.Minute)
		if truncatedTime.Unix()-data.StartTime > data.Time*60 {
			status = 2
			data.Status = 2
			errS = errs.NewCustomError(ctx, code.TimeOutLock, "time out")
		}
	}

	db = DB.DefaultConnect().Debug().WithContext(ctx).Table(commonModel.CommonShareLog{}.TableName()).
		Where("Fsource_id = ? and lock_uid = ?", FsourceId, userAccount.Uid).
		Save(&data)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	if status == 2 {
		// 代表没有完成扣除
		redisPrefixKey := global.GetPrefix()
		redisKey := fmt.Sprintf("%v-%v-%v", redisPrefixKey, FsourceId, openid)
		_, err = redis.GetClient().Decr(ctx, redisKey).Result()
		if err != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
				"AddUsageShareLog redis Incr error, \t [key]: {%v} \t[Error]:{%v} ", redisKey, err)
			return
		}
		allTimesRedisKey := fmt.Sprintf("%v-all-times--%v", redisPrefixKey, FsourceId)
		_, err = redis.GetClient().Decr(ctx, allTimesRedisKey).Result()
		if err != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
				"AddUsageShareLog redis Incr error, \t [key]: {%v} \t[Error]:{%v} ", redisKey, err)
			return
		}
	}

	if errS != nil {
		return errS
	}
	return
}

// CheckUsageShareLog TODO
func CheckUsageShareLog(ctx context.Context, FsourceId string, status int) (isHave bool, err error) {
	isHave = false
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	var num int64
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(commonModel.CommonShareLog{}.TableName()).
		Where("Fsource_id = ? and lock_uid = ? and status = ?", FsourceId, userAccount.Uid, status).
		Count(&num)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	if num > 0 {
		isHave = true
	} else {
		err = errs.NewCustomError(ctx, code.NoLockShareLog, "no lock share log")
	}
	return
}

// CheckAllTimes TODO
func CheckAllTimes(ctx context.Context, FsourceId string) (allTimes int, err error) {
	redisPrefixKey := global.GetPrefix()
	redisKey := fmt.Sprintf("%v-all-times--%v", redisPrefixKey, FsourceId)
	allTimes, errR := redis.GetClient().Get(ctx, redisKey).Int()
	if errR != nil && errR != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis error, error = %v",
			errR.Error())
		return
	}

	if errR == redisOrgin.Nil {
		allTimes = 0
	}

	return
}

// AddAllTimes TODO
func AddAllTimes(ctx context.Context, FsourceId string, times int) (err error) {
	redisPrefixKey := global.GetPrefix()
	redisKey := fmt.Sprintf("%v-all-times--%v", redisPrefixKey, FsourceId)
	errR := redis.GetClient().IncrBy(ctx, redisKey, int64(times)).Err()
	if errR != nil && errR != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis error, error = %v",
			errR.Error())
		return
	}
	return
}

// StartsWithNumber 判断字符串是否以指定整数开头
func StartsWithNumber(s string, num int) bool {
	// 将数字转换为字符串
	numStr := strconv.Itoa(num)
	return strings.HasPrefix(s, numStr)
}

// SendPresentByLog 发送礼包
func SendPresentByLog(ctx context.Context, FsourceId string) (err error) {
	scheduleCtx := context.Background()

	list := make([]*commonModel.CommonSendPresentLog, 0)
	tableName, err := GetAddSendPresentTable(scheduleCtx, FsourceId)
	if err != nil {
		return
	}

	db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
		Where("Fsource_id = ? ", FsourceId).
		Find(&list)
	if db.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}
	presentV2Proxy := present_present_v2.NewPresentV2ClientProxy()

	var wg sync.WaitGroup
	maxGoroutines := 100
	semaphore := make(chan struct{}, maxGoroutines)

	for _, item := range list {
		wg.Add(1)
		go func(item *commonModel.CommonSendPresentLog) (err error) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			accountData, _ := proto.Marshal(&userAccountPb.UserAccount{
				Uid:         fmt.Sprintf("%v-%v", item.GameId, item.Openid),
				AccountType: userAccountPb.AccountType_INTL,
				IntlAccount: &userAccountPb.IntlAccount{
					OpenId:    item.Openid,
					ChannelId: 131,
				},
			})
			callopts := []client.Option{
				client.WithMetaData(metadata.LangType, []byte(item.LangType)),
				client.WithMetaData(metadata.UserAccount, accountData),
			}
			gameRoleInfo := &game.RoleInfo{
				GameId: item.GameId,
				AreaId: int64(item.AreaID),
				ZoneId: int64(item.ZoneID),
				PlatId: int64(item.PlatID),
				RoleId: item.RoleId,
			}
			_, err = presentV2Proxy.SendPresent(scheduleCtx, &present_present_v2.SendPresentDataReq{
				FsourceId:      FsourceId,
				PresentId:      item.PresentId,
				PresentGroupId: item.PresentGroupId,
				RoleInfo:       gameRoleInfo,
				// SerialNo:       serialId,
			}, callopts...)
			if err != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "SendPresentByLogError", "str_field_1", fmt.Sprintf("%v-%v",
					item.GameId,
					item.Openid)).Infof(
					"SendPresentByLogError SendPresent err; err：[%v]", err)
			} else {
				log.WithFieldsContext(scheduleCtx, "log_type", "SendPresentByLogSuccess", "str_field_1", fmt.Sprintf("%v-%v",
					item.GameId,
					item.Openid)).Infof("send present success")
			}
			return
		}(item)
	}
	wg.Wait()
	return
}

// GetAddSendPresentTable 获取记录表名
func GetAddSendPresentTable(ctx context.Context, FsourceId string) (tableName string, err error) {
	// 从redis缓存取
	cacheKey := fmt.Sprintf("GetTabNameWithSourceId-%s-%s-%s-100", commonModel.CommonSendPresentConfig{}.TableName(),
		FsourceId, commonModel.CommonSendPresentLog{}.TableName())
	configJson, err := redis.GetClient().Get(ctx, cacheKey).Result()
	var redisConfig *commonModel.CommonSendPresentConfig

	if err == nil && configJson != "" {
		err = json.Unmarshal([]byte(configJson), &redisConfig)
		if err == nil {
			return getTableName(redisConfig.ID, 100, commonModel.CommonSendPresentLog{}.TableName()), nil
		}
	}

	var configItem *commonModel.CommonSendPresentConfig
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(commonModel.CommonSendPresentConfig{}.TableName()).
		Where("Fsource_id = ?", FsourceId).
		Attrs(commonModel.CommonSendPresentConfig{
			FsourceId: FsourceId,
		}).FirstOrCreate(&configItem)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}

	// 写入redis缓存
	configByte, err := json.Marshal(&configItem)
	if err == nil {
		if err := redis.GetClient().Set(ctx, cacheKey, string(configByte), 24*time.Hour).Err(); err != nil { // set error
			log.Error("[GetTabNameWithSourceId] redis service err: ", err)
		}
	}
	return getTableName(configItem.ID, 100, commonModel.CommonSendPresentLog{}.TableName()), nil
}

func getTableName(id int64, tableNum int, tablePrefix string) string {
	tableID := fmt.Sprintf("%02d", id%int64(tableNum))
	return fmt.Sprintf("%s_%s", tablePrefix, tableID)
}
