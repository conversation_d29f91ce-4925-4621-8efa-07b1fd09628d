// Package nikkembti nikke mbti测试
package nikkembti

import (
	"context"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_mbti"
	"github.com/go-sql-driver/mysql"
	"google.golang.org/protobuf/proto"
	mbtiModel "trpc.act.logicial/app/model/nikke_mbti"
)

// NikkeMbtiAddLog 添加发奖记录
func NikkeMbtiAddLog(ctx context.Context, req *pb.NikkeMbtiAddLogReq) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var nikkeMbtiAddData mbtiModel.NikkeMbtiSend

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
		"tag":          req.Tag,
		"Fsource_id":   req.FsourceId,
	}

	db := DB.DefaultConnect().WithContext(ctx).Where(condition).Attrs(&mbtiModel.NikkeMbtiSend{
		LangType: req.LangType,
	}).FirstOrCreate(&nikkeMbtiAddData)
	if db.Error != nil {
		// 检查 err 是否为重复插入错误
		if isNotDuplicateInsertError(db.Error) {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
	}
	log.WithFieldsContext(ctx, "log_type", "NikkeMbtiAddLog", "str_field_1", req.Tag).
		Infof("NikkeMbtiAddLogAddSendLog")
	return
}

// isNotDuplicateInsertError 是否不是mysql重复插入报错
func isNotDuplicateInsertError(err error) bool {
	mysqlErr, ok := err.(*mysql.MySQLError)
	if !ok {
		return false
	}
	// Error 1062: Duplicate entry for key
	return mysqlErr.Number != 1062
}

// ScheduledSendMbti nikkembti发奖
func ScheduledSendMbti(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	tableName := mbtiModel.NikkeMbtiSend{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(mbtiModel.NikkeMbtiSend{}.TableName()).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页50条
	pageSize := 50
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	gameProxy := gamePb.NewGameClientProxy()
	var lastId int64
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		// offset := (pageNumber - 1) * pageSize
		var logData = []mbtiModel.NikkeMbtiSend{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Where(condition).Where("id > ?", lastId).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", logData))
		if len(logData) == 0 {
			break
		}
		lastId = logData[len(logData)-1].ID
		for _, v := range logData {
			wg.Add(1)
			go func(data mbtiModel.NikkeMbtiSend) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(string(accountData))

				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				// 获取角色信息
				gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: data.FsourceID}

				gameRoleInfo, err := gameProxy.GetSavedRoleInfo(newCtx, gameReq, callopts...)
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("gameRoleInfo：%v", gameRoleInfo))

				if err == nil {
					log.WithFieldsContext(scheduleCtx, "ScheduledSend log", "debug").Infof(fmt.Sprintf("v: %#v", data))
					// 发送礼包
					sendReq := &presentPb.SendPresentReq{
						FsourceId: data.FsourceID,
						PresentId: data.Tag,
						RoleInfo:  gameRoleInfo,
					}
					accountData, _ = proto.Marshal(&accountPb.UserAccount{
						Uid:         data.UID,
						AccountType: accountPb.AccountType(data.AccountType),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							GameId:    gameRoleInfo.GameId,
							ChannelId: 3,
						},
					})
					callopts = []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
						client.WithMetaData(metadata.LangType, []byte(data.LangType)),
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
					}
					delErr := errs.ParseError(ctx, sendErr)

					if sendErr != nil || delErr.Code == 400018 || delErr.Code == 400021 {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err111:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						// if strings.Contains(sendErr.Error(), "package limit left not enough") {
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// }
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err:%v", sendErr))
					// 修改发货状态
				} else {
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("GetRoleInfo] service err:%v", err))

				}
				wg.Done()
			}(v)

		}
		wg.Wait()

	}
	return
}
