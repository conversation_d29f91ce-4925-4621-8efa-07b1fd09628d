package nikketmp

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"trpc.act.logicial/app/model/general"

	kafkaDB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/kafka"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	utilModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_nikke_tmp"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	redisOrigin "github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/global"
	"trpc.act.logicial/app/kafka"
	"trpc.act.logicial/app/logic/common"
	"trpc.act.logicial/app/model/base"
	baseTotalModel "trpc.act.logicial/app/model/base"
	"trpc.act.logicial/app/model/nikke"
)

const (
	GuessTagId = 1 // 竞猜
	VoteTagId  = 2 // 投票
)
const VoteListRedisKey = "NikkeVoteList"
const (
	// 每日礼包
	DailyGiftGlobal = "Wand-**************-P912bf9d48dc7" // 全球
	DailyGiftHKMT   = "Wand-**************-Pe0dca436afc6" // 港澳台

	// 其它礼包- 全球
	VoteGlobal       = "Wand-**************-P9a7033af635c" // 投票
	VoteShareGlobal  = "Wand-20240318020634-P2739a419ca04" // 投票分享
	GuessShareGlobal = "Wand-20240318015648-Pef800cacf501" // 竞猜分享
	GuessGlobal      = "Wand-20240315095938-Pe8cb710fcee3" // 竞猜
	// 其它礼包- 港澳台
	VoteHMT       = "Wand-20240322093827-P3261482112cc" // 投票
	VoteShareHMT  = "Wand-**************-P80801a80ce5b" // 投票分享
	GuessShareHMT = "Wand-**************-Pecf9e744af36" // 竞猜分享
	GuessHMT      = "Wand-**************-P200eabed1043" // 竞猜
)

func getRedisKey(key string) (redisKey string) {
	redisKeyPrefix := global.GetPrefix()
	return fmt.Sprintf("%s_%s", redisKeyPrefix, key)
}

// RecordGuessOrVote 记录投票或竞猜
func RecordGuessOrVote(ctx context.Context, voteList []int32, tagId int) error {

	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	// 检查当前用户今天是否已竞猜或投票
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
	}
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.UserVotingHistoryModel{}.TableName()).Where(condition)
	voteListStr := int32SliceToStr(voteList)
	switch tagId {
	case GuessTagId:
		// 记录或更新竞猜数据
		userVotingHistory := nikke.UserVotingHistory{}
		if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.UserVotingHistoryModel{}.TableName()).Where(nikke.UserVotingHistory{
			UID:         userAccount.Uid,
			AccountType: int(userAccount.AccountType),
			TagID:       tagId,
		}).Assign(nikke.UserVotingHistory{
			VoteIDList: voteListStr,
		}).FirstOrCreate(&userVotingHistory).Error; err != nil {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"RecordGuessOrVote Create db error, \t [Error]:{%v} ", err)
		}
	case VoteTagId:
		// 判断今天是否已参与投票
		startOfDay := common.GetStartOfDay()
		currentTimestamp := common.GetCurrentTimestamp()
		tx = tx.Where("tag_id = ? and created_at >= ? and created_at <= ?", tagId, startOfDay, currentTimestamp)
		var count int64
		if err = tx.Count(&count).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"RecordGuessOrVote Count db error, \t [Error]:{%v} ", err)
		}
		if count != 0 {
			// 已参与过投票
			return errs.NewCustomError(ctx, code.NikkeVoteDayLimit, "RecordGuessOrVote VoteDayLimit count:[%v]", count)
		}
		if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.UserVotingHistoryModel{}.TableName()).
			Create(&nikke.UserVotingHistory{
				UID:         userAccount.Uid,
				AccountType: int(userAccount.AccountType),
				TagID:       tagId,
				VoteIDList:  voteListStr,
			}).Error; err != nil {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"RecordGuessOrVote Create db error, \t [Error]:{%v} ", err)
		}
	default:
		return errs.NewCustomError(ctx, code.TagTypeErr, "RecordGuessOrVote not TagId Type Err; tagId:[%v]", tagId)
	}

	// 如果是投票则调用kafka更新总票数数据
	if tagId == VoteTagId {
		// 调用kafka
		voteItems := make([]*pb.VoteItem, 0, len(voteList))
		voteMap := make(map[int32]int32)
		for _, voteId := range voteList {
			if voteNum, ok := voteMap[voteId]; !ok {
				voteMap[voteId] = 1
			} else {
				voteMap[voteId] = voteNum + 1
			}
		}
		for voteId, voteNum := range voteMap {
			voteItems = append(voteItems, &pb.VoteItem{
				VoteId:  voteId,
				VoteNum: voteNum,
			})
		}
		if err = SendToAnniversaryVote(ctx, voteItems); err != nil {
			return err
		}
	}
	return nil
}

func int32SliceToStr(list []int32) string {
	strList := make([]string, len(list))
	for i, v := range list {
		strList[i] = strconv.Itoa(int(v))
	}
	return strings.Join(strList, ",")
}

func GetVoteRecords(ctx context.Context, tagId int) ([]int32, error) {

	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}

	// 获取投票or竞猜记录
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.UserVotingHistoryModel{}.TableName()).Select("vote_id_list").
		Where("uid = ? and account_type = ?", userAccount.Uid, userAccount.AccountType)
	switch tagId {
	case GuessTagId:
		// 竞猜
		tx = tx.Where("tag_id = ?", tagId)
	case VoteTagId:
		// 投票只查当天
		startOfDay := common.GetStartOfDay()
		currentTimestamp := common.GetCurrentTimestamp()
		tx = tx.Where("tag_id = ? and created_at >= ? and created_at <= ?", tagId, startOfDay, currentTimestamp)
	default:
		return nil, errs.NewCustomError(ctx, code.TagTypeErr, "not TagId Type Err; tagId:[%v]", tagId)
	}

	var voteListStr string
	if err = tx.Limit(1).Find(&voteListStr).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"GetVoteRecords First db error, \t [Error]:{%v} ", err)
	}
	if voteListStr == "" {
		// 没有投票或竞猜信息
		return nil, nil
	}
	// 格式化投票信息
	voteListSli := strings.Split(voteListStr, ",")
	voteList := make([]int32, len(voteListSli))
	for i, voteId := range voteListSli {
		intVal, err := strconv.Atoi(voteId)
		if err != nil {
			return nil, errs.NewCustomError(ctx, code.VoteIdTypeErr, "Vote Id Type Err; tagId:[%v], err:[%v]", tagId, err)
		}
		voteList[i] = int32(intVal)
	}
	return voteList, nil
}

// RecordGift 记录礼包
func RecordGift(ctx context.Context, presentId string, roleInfo *gamePb.RoleInfo, tagId int32, voteList []int32, guessList []int32) error {

	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	langType := metadata.GetLangType(ctx)
	// 是否已获取礼包
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeGiftDistributionRecordModel{}.TableName()).
		Where("uid = ? and account_type = ?", userAccount.Uid, int(userAccount.AccountType))
	switch presentId {
	case DailyGiftGlobal, DailyGiftHKMT:
		// 日常礼包
		startOfDay := common.GetStartOfDay()
		currentTimestamp := common.GetCurrentTimestamp()
		tx = tx.Where("present_id = ? and created_at >= ? and created_at <= ?", presentId, startOfDay, currentTimestamp)

		// 主投票or特别投票上报
		if tagId != 0 {
			// 特殊投票
			ReportTlog(ctx, "year_half_backup_special_vote_ret", cast.ToInt32(roleInfo.AreaId), cast.ToInt32(roleInfo.GameId), userAccount.Uid, 0, tagId, presentId)
		}
		if len(voteList) != 0 {
			// 主投票
			ReportTlog(ctx, "year_half_backup_main_vote_ret", cast.ToInt32(roleInfo.AreaId), cast.ToInt32(roleInfo.GameId), userAccount.Uid, 0, voteList, presentId)
		}

	default:
		// 其它礼包
		tx = tx.Where("present_id = ?", presentId)

		// 竞猜结果上报
		if len(guessList) != 0 && (presentId == GuessGlobal || presentId == GuessHMT) {
			// 竞猜
			ReportTlog(ctx, "year_half_backup_guess_ret", cast.ToInt32(roleInfo.AreaId), cast.ToInt32(roleInfo.GameId), userAccount.Uid, 0, guessList, presentId)
		}
	}
	var count int64
	if err = tx.Count(&count).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RecordGift Count db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		//return errs.NewCustomError(ctx, code.PresentHasObtained, "The current package has been obtained")
		return nil
	}
	// 记录礼包信息
	info := buildRoleInfo(roleInfo)
	roleInfoStr, err := json.Marshal(info)
	if err != nil {
		return errs.NewCustomError(ctx, code.CommonParamJsonError, fmt.Sprintf("roleInfo json Marshal error, err:[%v]", err))
	}
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeGiftDistributionRecordModel{}.TableName()).
		Create(&nikke.NikkeGiftDistributionRecord{
			UID:         userAccount.Uid,
			AccountType: int(userAccount.AccountType),
			RoleInfo:    string(roleInfoStr),
			PresentID:   presentId,
			LangType:    langType,
			Status:      0, // 未发货
		}).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RecordGift Create db error, \t [Error]:{%v} ", err)
	}
	return nil
}

func buildRoleInfo(info *gamePb.RoleInfo) nikke.RoleInfo {
	return nikke.RoleInfo{
		GameId:   info.GameId,
		AreaId:   info.AreaId,
		ZoneId:   info.ZoneId,
		PlatId:   info.PlatId,
		RoleId:   info.RoleId,
		RoleName: info.RoleName,
		GameName: info.GameName,
	}
}

func sortVoteItemsByVoteNum(voteItems []*pb.VoteItem) []*pb.VoteItem {
	sort.Slice(voteItems, func(i, j int) bool {
		return voteItems[i].VoteNum > voteItems[j].VoteNum
	})
	return voteItems
}

// ShowVoteList 展示投票列表
func ShowVoteList(ctx context.Context) ([]*pb.VoteItem, error) {
	now := time.Now()
	// redis 获取投票列表数据
	cacheKey := getRedisKey(VoteListRedisKey)
	exist, err := redis.GetClient().Exists(ctx, cacheKey).Result()
	if err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"ShowVoteList db error, \t [Error]:{%v} ", err)
	}
	if exist != 0 {
		// 取缓存数据
		resultMap, err := redis.GetClient().HGetAll(ctx, cacheKey).Result()
		if err != nil {
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
				"ShowVoteList HGetAll db error, \t [Error]:{%v} ", err)
		}
		voteItem := make([]*pb.VoteItem, 0, len(resultMap))
		for voteIdStr, voteNumStr := range resultMap {
			voteId := cast.ToInt32(voteIdStr)
			voteNum := cast.ToInt32(voteNumStr)
			voteItem = append(voteItem, &pb.VoteItem{
				VoteId:  voteId,
				VoteNum: voteNum,
			})
		}
		fmt.Println("sortVoteItemsByVoteNum before time:", time.Since(now))
		// 排序
		voteItemsDesc := sortVoteItemsByVoteNum(voteItem)
		fmt.Println("sortVoteItemsByVoteNum after time:", time.Since(now))
		return voteItemsDesc, nil
	}
	// todo 超过指定时间后不返回
	currentTimestamp := common.GetCurrentTimestamp()
	if currentTimestamp >= 1712934000 {
		return []*pb.VoteItem{}, nil
	}

	// 调用同步接口,同步数据至redis中并返回数据
	voteListCache, err := CountVoteList(ctx, true, false)
	if err != nil {
		// 同步数据异常
		return nil, err
	}
	fmt.Println("sortVoteItemsByVoteNum all time:", time.Since(now))
	return voteListCache, nil
}

type SpecialVoteItem struct {
	StorageTag string `json:"storage_tag"`
	CountNum   int    `json:"count_num"`
}

func CountSpecialVote(ctx context.Context, tableName string, fsourceId string) ([]*SpecialVoteItem, error) {

	var specialVoteList []*SpecialVoteItem
	sel := "storage_tag, COUNT(storage_tag) AS count_num"
	if err := DB.DefaultConnect().WithContext(ctx).Table(tableName).Select(sel).Where(
		"Fsource_id = ? and storage_key = ?", fsourceId, "vote").Group("storage_tag").
		Order("count_num desc").Find(&specialVoteList).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"CountSpecialVote Find db error, \t [Error]:{%v} ", err)
	}
	return specialVoteList, nil
}

// CountVoteList 计算票数
func CountVoteList(ctx context.Context, syncCache bool, getNewData bool) ([]*pb.VoteItem, error) {

	// 根据当前时间获取数据库数据
	voteList := make([]*nikke.VoteItem, 0)
	sel := "vote_id, SUM(vote_num) AS vote_num"
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeAllVotingRecordModel{}.TableName()).Select(sel)
	// 判断时间在18点之前获取昨天天之前的总票数,18点之后获取当前最新的总票数
	// currentTimestamp := common.GetCurrentTimestamp()
	// currentTimestampStr := common.GetCurrentTimestampStr()
	// if timestampAt18 := common.GetTimestampAt18(); currentTimestamp < timestampAt18 && !getNewData {
	// 	tx = tx.Where("Fday < ?", currentTimestampStr)
	// } else {
	// 	tx = tx.Where("Fday <= ?", currentTimestampStr)
	// }
	if err := tx.Group("vote_id").Order("vote_num desc").Find(&voteList).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"SyncVoteListCache Find db error, \t [Error]:{%v} ", err)
	}
	if len(voteList) == 0 {
		return []*pb.VoteItem{}, nil
	}
	if !syncCache {
		// 不写缓存
		return buildVoteItem(voteList), nil
	}
	// 写入缓存
	voteListMap := make(map[string]interface{})
	for _, voteItem := range voteList {
		voteListMap[cast.ToString(voteItem.VoteID)] = voteItem.VoteNum
	}
	cacheKey := getRedisKey(VoteListRedisKey)
	if err := redis.GetClient().HSet(ctx, cacheKey, voteListMap).Err(); err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
			"SyncVoteListCache redis HMSet error, \t [Error]:{%v}", err)
	}
	// 设置超时时间/7天
	expiration := 168 * time.Hour
	if err := redis.GetClient().Expire(ctx, cacheKey, expiration).Err(); err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
			"SyncVoteListCache redis HMSet Expire error, \t [Error]:{%v}", err)
	}

	return buildVoteItem(voteList), nil
}

func buildVoteItem(voteList []*nikke.VoteItem) []*pb.VoteItem {

	voteItems := make([]*pb.VoteItem, 0, len(voteList))
	for _, v := range voteList {
		voteItems = append(voteItems, &pb.VoteItem{
			VoteId:  v.VoteID,
			VoteNum: v.VoteNum,
		})
	}
	return voteItems
}

// RecordUserActivity 记录参加活动的用户
func RecordUserActivity(ctx context.Context) error {

	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}

	// 记录参与活动的用户
	var userParticipation nikke.UserParticipation
	if err = DB.DefaultConnect().WithContext(ctx).Where(nikke.UserParticipation{
		UID:         userAccount.Uid,
		AccountType: int(userAccount.AccountType),
	}).FirstOrCreate(&userParticipation).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RecordGuessOrVote FirstOrCreate db error, \t [Error]:{%v} ", err)
	}
	return nil
}

// SendToAnniversaryVote 写入kafka
func SendToAnniversaryVote(ctx context.Context, voteList []*pb.VoteItem) (err error) {
	kafkaByte, _ := json.Marshal(voteList)
	errK := kafka.Produce(ctx, kafka.NIKKEAnniversaryVoteProducer, "", string(kafkaByte))
	if errK != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeKafka, kafkaDB.KafkaConnectErr, "kafka produce error, error = %v",
			errK.Error())
	}
	return
}

// AddAnniversaryVoteFromKafka 获取kafka
func AddAnniversaryVoteFromKafka(ctx context.Context, voteList []*pb.VoteItem) (err error) {
	fmt.Println("---------------voteList---------------")
	fmt.Printf("%#v\n", voteList)
	currentTimestampStr := common.GetCurrentTimestampStr()
	for _, voteItem := range voteList {
		condition := map[string]interface{}{
			"vote_id": voteItem.VoteId,
			"Fday":    currentTimestampStr,
		}
		updateData := map[string]interface{}{
			"vote_num": gorm.Expr("vote_num + ?", voteItem.VoteNum),
		}
		tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeAllVotingRecordModel{}.TableName()).Limit(1).
			Where(condition).Updates(updateData)
		if err = tx.Error; err != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"AddAnniversaryVoteFromKafka Updates db error, \t [Error]:{%v} ", err)
			continue
		}
		if tx.RowsAffected == 0 {
			err = errs.NewCustomError(ctx, code.FailToVote, "AddAnniversaryVoteFromKafka Updates num not enough")
			continue
		}
	}

	return
}

// InitializeDatabase 初始化数据库
func InitializeDatabase(ctx context.Context) error {

	var count int64
	if err := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeAllVotingRecordModel{}.TableName()).Count(&count).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"InitializeDatabase Count db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		// 已初始化过
		return nil
	}
	// 获取当前时间及以后15天的时间标识
	days := common.GetNextNumDays(15)
	insertMap := make([]map[string]interface{}, 0, len(days))
	// nikkeId 1-98
	for voteId := 1; voteId <= 98; voteId++ {
		for _, day := range days {
			insertMap = append(insertMap, map[string]interface{}{
				"Fday":    day,
				"vote_id": voteId,
			})
		}
	}
	if err := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeAllVotingRecordModel{}.TableName()).Create(insertMap).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"InitializeDatabase Create db error, \t [Error]:{%v} ", err)
	}
	return nil
}

// CountNumberParticipantsRegularly 定时查询参与活动人数，大于50W触发接口
func CountNumberParticipantsRegularly(ctx context.Context) error {

	var count int64
	// 设置redis锁
	redisKey := getRedisKey("CountNumberParticipantsRegularly-lock")
	result, err := redis.GetClient().Get(ctx, redisKey).Result()
	if err != nil && err != redisOrigin.Nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"CountNumberParticipantsRegularly get redis error, \t [Error]:{%v} ", err)
	}
	// 锁定存在期间只触发一次
	if result != "" {
		return nil
	}

	if err := DB.DefaultConnect().WithContext(ctx).Table(nikke.UserParticipationModel{}.TableName()).Count(&count).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"CountNumberParticipantsRegularly count db error, \t [Error]:{%v} ", err)
	}
	if count >= 500000 {
		// todo 触发接口
		botUrl := config.GetConfig().NikkeAnniversaryBotUrl
		sendMsg := fmt.Sprintf("**参与活动人数已达到: %d 人**", count)
		// 推送企微消息
		if err := SendMarkdownToWxWorkRobot(ctx, botUrl, sendMsg); err != nil {
			return err
		}

		// 设置redis锁
		//redisKey := getRedisKey("CountNumberParticipantsRegularly-lock")
		lookTime := 10 * 24 * time.Hour
		isGetLock, err := redis.GetClient().SetNX(ctx, redisKey, "1", lookTime).Result()
		if err != nil {
			return errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
				"isGetLock db error, \t [Error]:{%v} ", err)
		}
		if !isGetLock {
			return errs.NewCustomError(ctx, code.HasTriggeredInterface, "The current interface has been triggered error not get lock skip")
		}
	}
	return nil
}

// VotesTalliedHourlySendBot 每小时统计票数, 触发企微机器人接口
func VotesTalliedHourlySendBot(ctx context.Context) error {
	timestamp := common.GetCurrentTimestamp()

	log.WithFieldsContext(ctx, "log_type", "debug").Infof("VotesTalliedHourlySendBot req")
	botUrl := config.GetConfig().NikkeAnniversaryBotUrl
	voteListConf := config.GetConfig().NikkeVoteRoleList
	specialVoteConf := config.GetConfig().NikkeSpecialVote

	// 特殊投票 - 计算票数
	var voteMap map[string]string
	var fsourceId string
	var storageKey = "vote"
	switch {
	case timestamp > 1712674810 && timestamp <= 1712761210:
		// 第一天
		voteMap = specialVoteConf[0].VoteMap
		fsourceId = "page-26617-teshu1"
		break
	case timestamp > 1712761210 && timestamp <= 1712847610:
		// 第二天
		voteMap = specialVoteConf[1].VoteMap
		fsourceId = "page-26617-teshu2"
		break
	case timestamp > 1712847610 && timestamp <= 1712934010:
		// 第三天
		voteMap = specialVoteConf[2].VoteMap
		fsourceId = "page-26617-teshu3"
		break
	case timestamp > 1712934010 && timestamp <= 1713020410:
		// 第四天
		voteMap = specialVoteConf[3].VoteMap
		fsourceId = "page-26617-teshu4"
		break
	case timestamp > 1713020410 && timestamp <= 1713106810:
		// 第五天
		voteMap = specialVoteConf[4].VoteMap
		fsourceId = "page-26617-teshu5"
		break
	}

	tableName, err := utilModel.GetTabNameWithGeneral(ctx, (&general.ConfigModel{}).TableName(), fsourceId, storageKey,
		(&general.LogModel{}).TableName(), 100)
	if err != nil {
		return err
	}

	if len(voteMap) != 0 && tableName != "" && fsourceId != "" {
		specialVote, err := CountSpecialVote(ctx, tableName, fsourceId)
		if err != nil {
			errs.NewCustomError(ctx, code.NikkeCountSpecialVoteError, "Nikke CountSpecialVote Error, tableName:[%v],fsourceId:[%v]", tableName, fsourceId)
		}
		specialVoteStr := make([]string, 0, len(voteMap))
		specialVoteStr = append(specialVoteStr, "**特殊投票:**\n")
		for idx, v := range specialVote {
			if roleName, ok := voteMap[v.StorageTag]; !ok {
				errs.NewCustomError(ctx, code.CurrentRoleNotExist, "Current Role Not Exist, VoteId:[%v]", v.StorageTag)
			} else {
				ranking := idx + 1
				var msg string
				if ranking%4 == 0 {
					msg = fmt.Sprintf("(%d)%s:%d\n", ranking, roleName, v.CountNum)
				} else {
					msg = fmt.Sprintf("(%d)%s:%d\t", ranking, roleName, v.CountNum)
				}
				specialVoteStr = append(specialVoteStr, msg)
			}
		}
		sendMsg := strings.Join(specialVoteStr, "")
		// 推送特殊投票企微消息
		if err = SendMarkdownToWxWorkRobot(ctx, botUrl, sendMsg); err != nil {
			return err
		}
	}

	// 主投票-计算票数
	voteList, err := CountVoteList(ctx, false, true)
	if err != nil {
		return err
	}
	voteStr := make([]string, 0, len(voteList))
	voteStr = append(voteStr, "**普通投票:**\n")
	for idx, vote := range voteList {
		if roleName, ok := voteListConf[cast.ToString(vote.VoteId)]; !ok {
			return errs.NewCustomError(ctx, code.CurrentRoleNotExist, "Current Role Not Exist, VoteId:[%v]", vote.VoteId)
		} else {
			ranking := idx + 1
			var msg string
			if ranking%4 == 0 {
				msg = fmt.Sprintf("(%d)%s:%d\n", ranking, roleName, vote.VoteNum)
			} else {
				msg = fmt.Sprintf("(%d)%s:%d\t", ranking, roleName, vote.VoteNum)
			}
			voteStr = append(voteStr, msg)
		}
	}
	sendMsg := strings.Join(voteStr, "")
	// 推送主投票企微消息
	if err = SendMarkdownToWxWorkRobot(ctx, botUrl, sendMsg); err != nil {
		return err
	}
	return nil
}

type MarkdownMessage struct {
	ChatId   string            `json:"chatid,omitempty"`
	MsgType  string            `json:"msgtype"`
	Markdown map[string]string `json:"markdown"`
}

// SendMarkdownToWxWorkRobot 发送企微消息
func SendMarkdownToWxWorkRobot(ctx context.Context, webhookUrl string, markdownContent string) error {

	msg := MarkdownMessage{
		MsgType: "markdown",
		Markdown: map[string]string{
			"content": markdownContent,
		},
	}
	msgBytes, err := json.Marshal(msg)
	if err != nil {
		return errs.NewCustomError(ctx, code.CommonParamJsonError, "SendMarkdownToWxWorkRobot Marshal err:[%v]", err)
	}
	_, err = SendMessage(ctx, webhookUrl, string(msgBytes))
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError, "SendMarkdownToWxWorkRobot post send err:[%v]", err)
	}
	return nil
}

// SendResponse SendMessage response
type SendResponse struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

// SendMessage 发送企业微信消息
func SendMessage(ctx context.Context, url, postString string) (res SendResponse, err error) {
	// 构建请求
	optionOne := httpclient.ClientOption{
		URL:     url,
		Timeout: 5 * time.Second,
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		Type:       http.MethodPost,
		PostString: postString,
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	// 结果
	var result = resultOption.Result

	fmt.Println(fmt.Sprintf("SendMessage 发送企业微信消息 url=%v, postData=%v, result=%v", url, optionOne.PostString, result))

	if err = resultOption.RequestError; err != nil {
		err = errs.NewCustomError(ctx, code.SendWXBotErr, "SendMessage string(result) =%v， err:%v", result, resultOption.RequestError)
		return
	}
	// 解析
	if err = json.Unmarshal([]byte(result), &res); err != nil {
		// 解析失败
		err = errs.NewCustomError(ctx, code.CommonParamJsonError, "SendMessage json.Unmarshal error, SendMessage = %v, error:%v", result, err)
		return
	}

	return
}

func VoteResultsUpdateCache(ctx context.Context) error {
	// 调用同步接口,同步数据至redis中并返回数据
	_, err := CountVoteList(ctx, true, false)
	if err != nil {
		// 同步数据异常
		return err
	}
	return nil
}

func buildPBRoleInfo(roleInfoStr string) (*gamePb.RoleInfo, error) {
	var roleInfo nikke.RoleInfo
	if err := json.Unmarshal([]byte(roleInfoStr), &roleInfo); err != nil {
		return nil, err
	}

	return &gamePb.RoleInfo{
		GameId:   roleInfo.GameId,
		AreaId:   roleInfo.AreaId,
		ZoneId:   roleInfo.ZoneId,
		PlatId:   roleInfo.PlatId,
		RoleId:   roleInfo.RoleId,
		RoleName: roleInfo.RoleName,
		GameName: roleInfo.GameName,
	}, nil
}

// ScheduledSendAnniversary 1.5周年活动定时发货
func ScheduledSendAnniversary(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	tableName := nikke.NikkeGiftDistributionRecordModel{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"ScheduledSendAnniversary db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("ScheduledSendAnniversary:[%v]", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页200条
	pageSize := 200
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		offset := (pageNumber - 1) * pageSize
		var logData = []nikke.NikkeGiftDistributionRecord{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Where(condition).Offset(offset).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"ScheduledSendAnniversary Find db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("ScheduledSendAnniversary logData:[%v]", logData))
		for _, v := range logData {
			wg.Add(1)
			go func(data nikke.NikkeGiftDistributionRecord) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(string(accountData))
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				// 获取角色信息
				var gameRoleInfo *gamePb.RoleInfo
				if data.RoleInfo == "" {
					err = errs.NewCustomError(ctx, code.CurrentUserRoleAbnormal, "ScheduledSendAnniversary CurrentUserRoleAbnormal err, data:[%v]", data)
				} else {
					gameRoleInfo, err = buildPBRoleInfo(data.RoleInfo)
				}
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf(
					"ScheduledSendAnniversary buildPBRoleInfo：%v", gameRoleInfo))

				if err == nil {
					log.WithFieldsContext(ctx, "ScheduledSendAnniversary log", "debug").Infof(fmt.Sprintf("ScheduledSendAnniversary dataInfo: %#v", data))
					// 发送礼包
					sendReq := &presentPb.SendPresentReq{
						PresentId: data.PresentID,
						RoleInfo:  gameRoleInfo,
					}
					accountData, _ = proto.Marshal(&accountPb.UserAccount{
						Uid:         data.UID,
						AccountType: accountPb.AccountType(data.AccountType),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							GameId:    gameRoleInfo.GameId,
							ChannelId: 3,
						},
					})
					callopts = []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
						client.WithMetaData(metadata.LangType, []byte(data.LangType)),
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
					action := "year_half_backup_reward_send_ret"
					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// 发奖数据上报
						ReportTlog(ctx, action, cast.ToInt32(gameRoleInfo.AreaId), cast.ToInt32(gameRoleInfo.GameId), openID, 0, "", data.PresentID)
						// 记录日志
						//log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
						//	"ScheduledSendAnniversary ReportTlog succ: action:[%v],AreaId:[%v],GameId:[%v],openID:[%v],PresentID:[%v]",
						//	action, gameRoleInfo.AreaId, gameRoleInfo.GameId, openID, data.PresentID))
					} else {
						// 发奖失败数据上报
						ReportTlog(ctx, action, cast.ToInt32(gameRoleInfo.AreaId), cast.ToInt32(gameRoleInfo.GameId), openID, -1, "", data.PresentID)
						// 记录日志
						//log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
						//	"ScheduledSendAnniversary ReportTlog fail: action:[%v],AreaId:[%v],GameId:[%v],openID:[%v],PresentID:[%v]",
						//	action, gameRoleInfo.AreaId, gameRoleInfo.GameId, openID, data.PresentID))
					}
					delErr := errs.ParseError(ctx, sendErr)
					if delErr.Code == 400018 || delErr.Code == 400042 {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						fmt.Println("---------------sendErr---------------")
						fmt.Printf("%#v\n", sendErr.Error())
						log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err222:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						// if strings.Contains(sendErr.Error(), "package limit left not enough") {
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// }
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[ScheduledSendAnniversary SendPresent] service err:%v", sendErr))
					// 修改发货状态
				} else {
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("ScheduledSendAnniversary GetRoleInfo] service err:%v", err))
				}
				wg.Done()
			}(v)

		}
		wg.Wait()

	}
	return
}

// ReportTlog 上报
func ReportTlog(ctx context.Context, action string, areaId, gameId int32, openId string, ret int, roleList, giftId interface{}) (err error) {
	langType := metadata.GetLangType(ctx)
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: langType,
			XGameId:   cast.ToInt(gameId),
			XSource:   "pc_web",
			XAreaid:   cast.ToString(areaId),
		},
		Action:         action,
		OriginalGameId: cast.ToString(gameId),
		ExtentContent: map[string]interface{}{
			"game_id":   gameId,
			"open_id":   openId,
			"server_id": areaId,
			"ret":       ret,
			"role_list": roleList,
			"gift_id":   giftId,
		},
	}
	report.ReportTlog(ctx, tlogData)
	log.WithFieldsContext(ctx, "log_type", action).Infof(
		"vote ReportTlog action:[%v],areaId:[%v],gameId:[%v],openId:[%v], ret:[%v], roleList:[%v], giftId:[%v]",
		action, areaId, gameId, openId, ret, roleList, giftId)
	return
}

// DelUserVotingHistory 根据类型删除
func DelUserVotingHistory(ctx context.Context, delType int32, openId string) error {

	if trpc.GlobalConfig().Global.EnvName == "prod" {
		return nil
	}

	if openId == "" {
		return errs.NewCustomError(ctx, 10000002, "no del openId err")
	}
	startOfDay := common.GetStartOfDay()
	currentTimestamp := common.GetCurrentTimestamp()
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.UserVotingHistoryModel{}.TableName()).
		Where("uid = ?", openId)
	switch delType {
	case 1:
		// 竞猜
		tx = tx.Where("tag_id = ?", 1)
	case 2:
		// 投票
		tx = tx.Where("tag_id = ? and created_at >= ? and created_at <= ?", 2, startOfDay, currentTimestamp)
	case 3:
		// 删除特殊投票
		err := delSpecialVote(ctx, openId)
		return err
	default:
		// 不处理
		return errs.NewCustomError(ctx, ********, "no del type err")
	}
	if err := tx.Delete(nil).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"DelUserVotingHistory Delete db error, \t [Error]:{%v} ", err)
	}
	return nil
}

func delSpecialVote(ctx context.Context, openId string) error {

	account := accountPb.UserAccount{
		Uid:         openId,
		AccountType: 1,
	}

	var err error
	err = DB.DefaultConnect().WithContext(ctx).Table("general_log_06").Where("uid = ? and Fsource_id = ? and storage_key = ?", openId, "page-26617-teshu1", "vote").Delete(nil).Error
	if err != nil {
		return err
	}
	err = DB.DefaultConnect().WithContext(ctx).Table("general_log_07").Where("uid = ? and Fsource_id = ? and storage_key = ?", openId, "page-26617-teshu3", "vote").Delete(nil).Error
	if err != nil {
		return err
	}
	err = DB.DefaultConnect().WithContext(ctx).Table("general_log_08").Where("uid = ? and Fsource_id = ? and storage_key = ?", openId, "page-26617-teshu2", "vote").Delete(nil).Error
	if err != nil {
		return err
	}
	err = DB.DefaultConnect().WithContext(ctx).Table("general_log_09").Where("uid = ? and Fsource_id = ? and storage_key = ?", openId, "page-26617-teshu4", "vote").Delete(nil).Error
	if err != nil {
		return err
	}
	err = DB.DefaultConnect().WithContext(ctx).Table("general_log_10").Where("uid = ? and Fsource_id = ? and storage_key = ?", openId, "page-26617-teshu5", "vote").Delete(nil).Error
	if err != nil {
		return err
	}
	var baseLogTable base.BaseTotalModel
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &account, baseLogTable.TableName())
	if err != nil {
		return err
	}
	err = DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where("uid = ? and storage_key = ?", openId, "vote").
		Where("Fsource_id = ? or Fsource_id = ? or Fsource_id = ? or Fsource_id = ? or Fsource_id = ?",
			"page-26617-teshu1", "page-26617-teshu2", "page-26617-teshu3", "page-26617-teshu4", "page-26617-teshu5").Delete(nil).Error
	return err
}

func RefreshCache(ctx context.Context) error {

	// 根据当前时间获取数据库数据
	_, err := CountVoteList(ctx, true, true)

	return err
}

func MovedForwardOneDay(ctx context.Context, openId string) error {

	if trpc.GlobalConfig().Global.EnvName == "prod" {
		return nil
	}

	// 修改投票数据时间
	updateData := map[string]interface{}{
		"created_at": gorm.Expr("created_at - ?", 86400),
	}
	if err := DB.DefaultConnect().WithContext(ctx).Table(nikke.UserVotingHistoryModel{}.TableName()).
		Where("uid = ? and tag_id = 2", openId).Updates(updateData).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"MovedForwardOneDay Updates db error, \t openId:[%v],[Error]:{%v}", openId, err)
	}
	return nil
}

func UserGiftCollectionRecord(ctx context.Context, info *gamePb.RoleInfo) (*pb.UserGiftCollectionRecordRsp, error) {

	if info == nil || info.AreaId == 0 {
		return nil, errs.NewCustomError(ctx, code.NikkeNotRoleInfo, "UserGiftCollectionRecord roleInfo is empty,info:[%v]", info)
	}

	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	// 每日礼包
	dailyGift := map[string]interface{}{
		DailyGiftGlobal: struct{}{},
		DailyGiftHKMT:   struct{}{},
	}
	// 获取当前用户已获取礼包
	var giftDistributionRecord []*nikke.NikkeGiftDistributionRecord
	sel := "present_id,created_at,role_info"
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeGiftDistributionRecordModel{}.TableName()).Select(sel).
		Where("uid = ? AND account_type = ?", userAccount.Uid, int32(userAccount.AccountType)).
		Find(&giftDistributionRecord).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"UserGiftCollectionRecord find db error, \t Uid:[%v],AccountType:[%v],[Error]:{%v}", userAccount.Uid, userAccount.AccountType, err)
	}
	rsp := &pb.UserGiftCollectionRecordRsp{}
	if len(giftDistributionRecord) == 0 {
		return rsp, nil
	}
	// 起始时间
	startOfDay := common.GetStartOfDay()
	for _, v := range giftDistributionRecord {
		// 角色解析
		var roleInfo nikke.RoleInfo
		if err = json.Unmarshal([]byte(v.RoleInfo), &roleInfo); err != nil {
			return nil, errs.NewCustomError(ctx, code.NikkeNotCurrentPresentId, "UserGiftCollectionRecord Unmarshal err, RoleInfo:[%v]", v.RoleInfo)
		}
		if roleInfo.AreaId != info.AreaId {
			continue
		}
		if _, ok := dailyGift[v.PresentID]; ok {
			if v.CreatedAt >= startOfDay {
				rsp.DailyVotePresent = true
			}
			continue
		}
		switch v.PresentID {
		case VoteGlobal, VoteHMT:
			// 投票礼包
			rsp.VotePresent = true
		case VoteShareGlobal, VoteShareHMT:
			// 投票分享礼包
			rsp.VoteSharePresent = true
		case GuessShareGlobal, GuessShareHMT:
			// 竞猜分享礼包
			rsp.GuessSharePresent = true
		case GuessGlobal, GuessHMT:
			// 竞猜礼包
			rsp.GuessPresent = true
		default:
			return nil, errs.NewCustomError(ctx, code.NikkeNotCurrentPresentId, "UserGiftCollectionRecord NotCurrentPresentId, PresentID:[%v]", v.PresentID)
		}
	}
	return rsp, nil
}

// DelTestUserVotingHistory 压测删除投票数据
func DelTestUserVotingHistory(ctx context.Context, delType int32) error {

	if trpc.GlobalConfig().Global.EnvName == "prod" {
		return nil
	}

	switch delType {
	case 1:
		// 删除普通投票数据
		if err := DB.DefaultConnect().WithContext(ctx).Table(nikke.UserVotingHistoryModel{}.TableName()).
			Where("account_type = ? and tag_id = ?", 9999, 2).Delete(nil).Error; err != nil {
			return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"DelTestUserVotingHistory vote del db error, \t[Error]:{%v}", err)
		}
	case 2:
		// 删除特殊投票数据
		start := 100000
		end := 300000
		tableNameMap := make(map[string][]string, 100)
		for i := start; i <= end; i++ {
			openid := fmt.Sprintf("29080-%d", i)
			account := accountPb.UserAccount{
				AccountType: accountPb.AccountType_TEST,
				Uid:         openid,
			}
			var baseLog baseTotalModel.BaseTotalLogModel
			accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &account, baseLog.TableName())
			if err != nil {
				errs.NewCustomError(ctx, ********, "DelTestUserVotingHistory err,err:[%v]", err)
				continue
			}
			if _, ok := tableNameMap[accountTableName]; !ok {
				tableNameMap[accountTableName] = make([]string, 0, 10000)
			}
			tableNameMap[accountTableName] = append(tableNameMap[accountTableName], openid)
		}
		// 删除压测特殊投票票数数据
		var wg sync.WaitGroup
		for tableName, openids := range tableNameMap {
			wg.Add(1)
			go func(tname string, ids []string) {
				defer wg.Done()
				if err := DB.DefaultConnect().WithContext(ctx).Table(tname).Where("uid in (?) and storage_key = ?", ids, "vote").
					Where("Fsource_id = ? or Fsource_id = ? or Fsource_id = ? or Fsource_id = ? or Fsource_id = ?",
						"page-26617-teshu1", "page-26617-teshu2", "page-26617-teshu3", "page-26617-teshu4", "page-26617-teshu5").Delete(nil).Error; err != nil {
					errs.NewCustomError(ctx, ********, "DelTestUserVotingHistory del db err,err:[%v]", err)
					return
				}
			}(tableName, openids)
		}
		wg.Wait()
	default:
		// 不处理
		return errs.NewCustomError(ctx, ********, "no del type err")
	}
	return nil
}

func CountVoteByAreaId(countVoteCtx context.Context) error {

	var err error
	ctx := context.Background()
	now := time.Now()
	// 添加一个锁，只允许调用一次
	redisKey := getRedisKey("NikkeCountVoteByAreaId-lock")
	result, err := redis.GetClient().Get(ctx, redisKey).Result()
	if err != nil && !errors.Is(err, redisOrigin.Nil) {
		return errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"CountVoteByAreaId get redis error, \t [Error]:{%v} ", err)
	}
	// 锁定存在期间只触发一次
	if result != "" {
		return nil
	}

	// 获取用户投票表总数
	tableName := nikke.UserVotingHistoryModel{}.TableName()
	// 获取普通投票数据
	condition := map[string]interface{}{
		"tag_id": 2,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"CountVoteByAreaId db error, \t [Error]:{%v} ", countdb.Error.Error())
		return err
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("GetVoteCountByAreaId totalRecords:[%v]", totalRecords))
	if totalRecords == 0 {
		return nil
	}
	// 分批每次100条获取用户投票
	pageSize := int64(100)
	totalPages := totalRecords / pageSize
	if totalRecords%pageSize != 0 {
		totalPages++
	}
	sel := "uid,account_type,vote_id_list"
	for page := int64(1); page <= totalPages; page++ {
		// 分页查询用户投票数据
		offset := (page - 1) * pageSize
		var userVotingList []*nikke.UserVotingHistory
		if err := DB.DefaultConnect().WithContext(ctx).Table(nikke.UserVotingHistoryModel{}.TableName()).Select(sel).
			Where(condition).Offset(int(offset)).Limit(int(pageSize)).Order("id asc").Find(&userVotingList).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"CountVoteByAreaId Find db error, \t [Error]:{%v} ", err.Error())
			return err
		}
		// uid去重
		uidRoleMap := getUidRoleInfoMap(ctx, userVotingList, totalRecords)
		areaVoteMap := make(map[int64]map[int]int64)
		// 分区计票
		for _, v := range userVotingList {
			roleInfo, ok := uidRoleMap[v.UID]
			if !ok {
				log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("GetVoteCountByAreaId uidRoleMap not current uid:[%v]", v.UID))
				continue
			}
			// 计算区服下对应角色增加的票数
			if _, ok2 := areaVoteMap[roleInfo.AreaId]; !ok2 {
				areaVoteMap[roleInfo.AreaId] = make(map[int]int64)
			}
			voteStrList := strings.Split(v.VoteIDList, ",")
			for _, voteIdStr := range voteStrList {
				voteId := cast.ToInt(voteIdStr)
				areaVoteMap[roleInfo.AreaId][voteId] += 1
			}
		}
		// 遍历大区下对应角色的票数计算票数写入数据库
		for areaId, voteMap := range areaVoteMap {
			for voteId, addVoteNum := range voteMap {
				areaVotingCountCondition := map[string]interface{}{
					"area_id":   areaId,
					"vote_type": 2, // 固定普通投票
					"vote_id":   voteId,
				}
				updateData := map[string]interface{}{
					"count_num": gorm.Expr("count_num + ?", addVoteNum),
				}
				tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.AreaVotingCountModel{}.TableName()).Limit(1).
					Where(areaVotingCountCondition).Updates(updateData)
				if err := tx.Error; err != nil {
					err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"CountVoteByAreaId Updates db error, \tareaVotingCountCondition:[%v],updateData:[%v], [Error]:{%v} ",
						areaVotingCountCondition, updateData, err)
					continue
				}
			}
		}
	}

	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("CountVoteByAreaId end time:[%v]", time.Since(now)))
	// 限制调用一次
	lookTime := 15 * 24 * time.Hour
	isGetLock, err := redis.GetClient().SetNX(ctx, redisKey, "1", lookTime).Result()
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"GetVoteCountByAreaId setnx db error, \t [Error]:{%v} ", err)
	}
	if !isGetLock {
		return errs.NewCustomError(ctx, code.HasTriggeredInterface, "The current interface has been triggered error not get lock skip")
	}
	return nil
}

func getUidRoleInfoMap(scheduleCtx context.Context, userVotingList []*nikke.UserVotingHistory, totalRecords int64) map[string]*gamePb.RoleInfo {
	uidMap := make(map[string]*gamePb.RoleInfo, len(userVotingList))
	for _, v := range userVotingList {
		uidMap[v.UID] = &gamePb.RoleInfo{}
	}
	// 根据uid获取区服角色信息
	for uid := range uidMap {
		gameId := strings.Split(uid, "-")[0]
		var fSourceId string
		switch gameId {
		case "29080":
			// 全球
			fSourceId = "page-26617"
		case "29157":
			// 港澳台
			fSourceId = "page-26618"
		default:
			log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("GetVoteCountByAreaId totalRecords:[%v]", totalRecords))
			continue
		}
		// 获取角色信息
		roleInfo, err := GetGameRoleInfo(scheduleCtx, uid, fSourceId, 1)
		if err != nil {
			log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
				"GetVoteCountByAreaId GetGameRoleInfo uid:[%v],fSourceId:[%v] err:[%v]", uid, fSourceId, err))
			continue
		}
		uidMap[uid] = roleInfo
	}
	return uidMap
}

func GetGameRoleInfo(ctx context.Context, uid, fSourceId string, accountType int32) (*gamePb.RoleInfo, error) {

	openID := strings.Split(uid, "-")[1]
	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         uid,
		AccountType: accountPb.AccountType(accountType),
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    openID,
			ChannelId: 3,
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	// 获取角色信息
	gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: fSourceId}
	gameProxy := gamePb.NewGameClientProxy()
	gameRoleInfo, err := gameProxy.GetSavedRoleInfo(ctx, gameReq, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("GetGameRoleInfo GetSavedRoleInfo err:[%v]", err))
		return nil, err
	}
	return gameRoleInfo, nil
}

func InitializeAreaVotingCountDb(ctx context.Context) error {

	var count int64
	if err := DB.DefaultConnect().WithContext(ctx).Table(nikke.AreaVotingCountModel{}.TableName()).Count(&count).
		Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"InitializeAreaVotingCountDb Count db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		// 已初始化过
		return nil
	}
	insertMap := make([]map[string]interface{}, 0)
	areaIdLsit := []string{
		"81", "82", "83", "84", "85", "91",
	}
	// nikkeId 1-98
	voteListConf := config.GetConfig().NikkeVoteRoleList
	for voteId := 1; voteId <= 98; voteId++ {
		for _, areaId := range areaIdLsit {
			insertMap = append(insertMap, map[string]interface{}{
				"area_id":   areaId,
				"vote_id":   voteId,
				"vote_type": 2,
				"vote_name": voteListConf[cast.ToString(voteId)],
			})
		}
	}
	if err := DB.DefaultConnect().WithContext(ctx).Table(nikke.AreaVotingCountModel{}.TableName()).Create(insertMap).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"InitializeAreaVotingCountDb Create db error, \t [Error]:{%v} ", err)
	}
	return nil
}
