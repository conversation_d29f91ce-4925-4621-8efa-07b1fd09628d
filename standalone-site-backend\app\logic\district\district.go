package district

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

var nextPageCirsor = ""
var limit = int64(500)

func GetDistrictList(c context.Context, req *pb.GetDistrictListReq, language string) (*pb.GetDistrictListRsp, error) {
	rsp := &pb.GetDistrictListRsp{
		List:     make([]*pb.DistrictItem, 0),
		PageInfo: &pb.PageInfo{},
	}

	var err error
	// 全量请求
	req.NextPageCursor = nextPageCirsor
	req.Limit = limit
	// 先获取缓存数据
	districtListRedisKey := cache.GetDistrictListKey(language, req.NextPageCursor, req.Limit)
	districtListCacheInfo, err := redis.GetClient().Get(c, districtListRedisKey).Result()
	if err == nil {
		if districtListCacheInfo == "" {
			rsp.PageInfo.IsFinish = true
			return rsp, nil
		}
		err = json.Unmarshal([]byte(districtListCacheInfo), rsp)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetDistrictList cache json.Unmarshal error.districtListRedisKey: %s, err: %v", districtListRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetDistrictsJsonUnmarshalError, "Failed to obtain district list, data parsing exception")
		} else {
			return rsp, nil
		}
	}

	var districtList []*model.District
	var nextPageCursor, previousPageCursor string
	conditions := &dao.DistrictConditions{
		GameId: "30054",  // 写死这个查询
		AreaId: "global", // 写死这个查询
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}
	// 查询类型：下一页数据
	if req.PageType == pb.PageType_NEXTPAGE {
		var idCursor int64
		// 如果是首页
		if req.NextPageCursor == "" {
			idCursor = 0
		} else {
			previousPageCursor = req.NextPageCursor
			idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
			}
			conditions.GtId = idCursor
		}
		districtList, err = dao.GetDistrictList(conditions, int(req.Limit))
		// 生成下一页的游标
		if len(districtList) > 0 {
			nextPageCursor, err = util.EncryptPageCursorI(districtList[len(districtList)-1].ID)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetDistrictListError, "GetDistrictList | Failed to create district nextPageCursor")
			}
		}
		if len(districtList) == 0 {
			rsp.PageInfo.IsFinish = true
			return rsp, nil
		}
	}
	districtIds := make([]int64, 0, len(districtList))

	for _, item := range districtList {
		districtIds = append(districtIds, item.ID)
	}
	list, err := dao.GetDistrictLanguageList(districtIds)
	if err != nil {
		return nil, errs.NewCustomError(c, code.GetDistrictListError, "GetDistrictList | Failed to get district language list")
	}
	for i, item := range districtList {
		hasLang := false
		for _, districtLanguage := range list {
			if item.ID == districtLanguage.DistrictID && districtLanguage.Language == language {
				districtList[i].ToolName = districtLanguage.ToolName
				hasLang = true
				break
			}
		}
		if !hasLang {
			for _, districtLanguage := range list {
				if item.ID == districtLanguage.DistrictID && districtLanguage.Language == "en" {
					districtList[i].ToolName = districtLanguage.ToolName
					hasLang = true
					break
				}
			}
		}
		rsp.List = append(rsp.List, &pb.DistrictItem{
			ToolName: item.ToolName,
			Icon:     item.Icon,
			JumpUrl:  item.JumpURL,
			Order:    int64(item.Order),
			ExtInfo:  item.ExtInfo,
		})
	}
	if len(rsp.List) == 0 || len(rsp.List) < int(req.Limit) {
		rsp.PageInfo.IsFinish = true
	} else {
		rsp.PageInfo.NextPageCursor = nextPageCursor
	}
	rsp.PageInfo.PreviousPageCursor = previousPageCursor

	districtListRspByte, err := json.Marshal(rsp)
	if err == nil {
		redis.GetClient().SetEX(c, districtListRedisKey, string(districtListRspByte), 2*time.Minute).Result()
	}

	return rsp, nil
}
