package lip

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名
func (IntegralTransferErrorRecord) TableName() string {
	return "integral_transfer_error_records"
}

// IntegralTransferErrorRecord 独立站积分传错区服记录表
type IntegralTransferErrorRecord struct {
	ID        int    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`           // 主键ID
	OpenID    string `gorm:"column:openid;not null" json:"openid"`                   // 用户OpenID
	PackageID string `gorm:"column:package_id;not null" json:"package_id"`           // 包ID
	PointsNum int    `gorm:"column:points_num;not null;default:0" json:"points_num"` // 积分数
	Status    int8   `gorm:"column:status;not null;default:0" json:"status"`         // 当前状态 0:未发送 1:已发送
	Seq       string `gorm:"column:seq;not null" json:"seq"`                         // 序列号
}
