package tweet

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"trpc.publishing_application.standalonesite/app/constants"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
)

// ps: 先注释语言的变更，后期再改回来，24-11-6
func MovePost(c context.Context, req *pb.MovePostReq, uuid string) (rsp *pb.MovePostRsp, err error) {
	// 获取动态信息
	postItem, err := dao.GetPost(req.PostUuid)
	if err != nil {
		return nil, errs.NewCustomError(c, code.GetPostsFailed, "MovePost | Failed to get dynamic details")
	}
	oldPostPlateId := postItem.PlateID
	oldPostLanguage := postItem.Language

	isAdmin := formatted.GetUserAdmin(uuid)
	if !isAdmin {
		return nil, errs.NewCustomError(c, code.NoPermission, "MovePost | not permission")
	}

	// 非本人官贴无法移动
	if postItem.IsOfficial != 0 && postItem.IntlOpenid != uuid {
		return nil, errs.NewCustomError(c, code.NoPermission, "MovePost | not permission")
	}

	//// 判断是否本人创建
	//if uuid != postItem.IntlOpenid {
	//	return nil, errs.NewCustomError(c, code.NoPermission, "MovePost | not permission")
	//}
	// 迁移动态
	if req.PlateId > 0 {
		// 获取目标板块信息
		plateInfo, err := dao.PlateGet(&dao.PlateConditions{Id: int64(req.PlateId)})
		if err != nil {
			return nil, errs.NewCustomError(c, code.InvalidParamsErr, "Plate not found")
		}
		if plateInfo.UniqueIdentifier == constants.PLATE_OFFICIAL && postItem.IsOfficial != 1 {
			return nil, errs.NewCustomError(c, code.FolkPostCanNotMoveToOfficialPlate, "Folk post can not move to official plate")
		}
		postItem.PlateID = req.PlateId
	}
	// 管贴不能移动语言
	isMoveLanguage := len(req.Language) > 0 && postItem.IsOfficial != 1
	var oldLanguge = postItem.Language
	if isMoveLanguage {
		postItem.Language = req.Language
	}
	err = dao.UpdatePost(postItem)
	if err != nil {
		return nil, errs.NewCustomError(c, code.MovePostErr, "MovePost | Failed to move post")
	}
	if postItem.IsOfficial == 1 {
		// 官方帖子的话不能移动语言，需要把语言表中的数据更新plate_id
		err = dao.UpdatePostAllLanguage(postItem.PostUUID, map[string]interface{}{
			"plate_id": req.PlateId,
		})
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("update post language failed, err:%v", err)
			return nil, errs.NewCustomError(c, code.MovePostErr, "MovePost | Failed to move post")
		}
	} else {
		// 非官方帖子的话,可以移动到当前帖子没有的语言下，所以需要判断是不是新的语言
		if oldLanguge != req.Language {
			// 切换了新的语言
			err := dao.ChangePostLanguageUpdate(postItem.PostUUID, req.Language, oldLanguge, req.PlateId)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("change post language failed, err:%v", err)
				// 报错回滚
				postItem.PlateID = oldPostPlateId
				postItem.Language = oldPostLanguage
				dao.UpdatePost(postItem)
				return nil, errs.NewCustomError(c, code.MovePostErr, "Failed to move post by change language")
			}
		} else {
			// 还是旧的语言
			if req.PlateId > 0 {
				err := dao.UpdatePostLanguage(postItem.PostUUID, map[string]interface{}{
					"plate_id": req.PlateId,
				}, oldLanguge)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("update post plate failed, err:%v", err)
					// 报错回滚
					postItem.PlateID = oldPostPlateId
					postItem.Language = oldPostLanguage
					dao.UpdatePost(postItem)
					return nil, errs.NewCustomError(c, code.MovePostErr, "Failed to move post by update post plate")
				}
			}
		}

	}

	//postContent, err := dao.PostContentGet(req.PostUuid)
	//if err != nil {
	//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MovePost err, post_uuid:(%s), err=(%v)", req.PostUuid, err)
	//	return nil, errs.NewCustomError(c, code.GetPostContentsFailed, "MovePost | Failed to get dynamic details")
	//} else {
	//}
	// 更新es
	doc := map[string]interface{}{
		// "plate_id": req.PlateId,
		//"language":    req.Language,
		"modified_on": time.Now().Unix(),
	}

	if req.PlateId > 0 {
		doc["plate_id"] = req.PlateId
	}
	// 移动语言
	if isMoveLanguage {
		doc["language"] = req.Language
		languages := doc["post_languages"]
		if languages == nil {
			languages = []string{}
		}
		newLanguages := make([]string, 0)
		for _, v := range languages.([]string) {
			if v == oldLanguge {
				continue
			} else if v != req.Language {
				newLanguages = append(newLanguages, v)
			}
		}
		newLanguages = append(newLanguages, req.Language)
		doc["post_languages"] = newLanguages
		//区分设置每个语言的汇总内容，用于前端搜索。这里是因为ElasticSearch的一个字段只能配置一种分词器
		oldAllContantLangKey := fmt.Sprintf("all_content_lang_%s", postItem.Language)
		doc[oldAllContantLangKey] = ""
		newAllContantLangKey := fmt.Sprintf("all_content_lang_%s", req.Language)
		var newAllContantLangVal string
		var postContentStr string
		postContent, err := dao.PostContentGet(req.PostUuid, postItem.Language)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MovePost err, post_uuid:(%s), err=(%v)", req.PostUuid, err)
			return nil, errs.NewCustomError(c, code.GetPostContentsFailed, "MovePost | Failed to get post content")
		}
		if postContent.ContentSummary != "" {
			postContentStr = postContent.ContentSummary
		} else {
			postContentStr = postContent.Content
		}
		// 目前只配置了这几种语言的字段，后续要新增再继续追加
		if req.Language == "en" || req.Language == "ja" || req.Language == "ko" || req.Language == "zh" || req.Language == "zh-TW" {
			newAllContantLangVal = fmt.Sprintf("%s|||%s", postContent.Title, postContentStr)
			doc[newAllContantLangKey] = newAllContantLangVal
		}
	}

	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postItem.PostUUID, doc)
	go cache.DeletePostCache(c, postItem.PostUUID)
	go cache.RemoveUserPostCacheKeys(uuid)
	return &pb.MovePostRsp{}, nil
}
