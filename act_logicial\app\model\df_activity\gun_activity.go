package df_activity

import "time"

const (
	ActivityStatusOK     = 0 // 活动有效
	ActivityStatusDelete = 1 // 活动删除
)

const (
	// 任务状态
	GunTaskStatusProcessing          = 0 // 进行中
	GunTaskStatusWaitClaimPrize      = 1 // 等待领取
	GunTaskStatusWaitDistributePrize = 2 // 奖品发放中
	GunTaskStatusGetPrizeOk          = 3 // 发奖成功
	GunTaskStatusGetPrizeFail        = 4 // 发奖失败
	GunTaskStatusCancel              = 5 // 任务取消
	GunTaskStatusNotCompleted        = 6 // 任务超时未完成
)
const (
	// 重复发放奖励状态
	GunTaskHasPrizeFlagTrue = 1 // 任务关联的用户已经有过奖励了，重复发放
)

// GunActivityConf 拼枪活动配置表
type GunActivityConf struct {
	ID             int64     `gorm:"column:id;primary_key;AUTO_INCREMENT"`                  // 自增id
	GameId         string    `gorm:"column:game_id;NOT NULL"`                               // 游戏id
	ActivityId     string    `gorm:"column:activity_id;NOT NULL"`                           // 活动id
	Status         int       `gorm:"column:status;default:0;NOT NULL"`                      // 活动状态，0-有效，1-删除
	StartTimestamp uint32    `gorm:"column:start_timestamp;default:0;NOT NULL"`             // 活动开始时间
	EndTimestamp   uint32    `gorm:"column:end_timestamp;default:0;NOT NULL"`               // 活动结束时间
	UpdateTime     time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL"` // 更新时间
	CreateTime     time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL"` // 创建时间
}

// TableName ...
func (m *GunActivityConf) TableName() string {
	return "t_gun_activity_conf"
}

// GunActivityTask 拼枪活动任务表
type GunActivityTask struct {
	ID             int64     `gorm:"column:id;primary_key;AUTO_INCREMENT"`                  // 自增id
	GameId         string    `gorm:"column:game_id;NOT NULL"`                               // 游戏id
	ActivityId     string    `gorm:"column:activity_id;NOT NULL"`                           // 活动id
	UserId         string    `gorm:"column:user_id;NOT NULL"`                               // 用户id
	OpenId         string    `gorm:"column:open_id;NOT NULL"`                               // openid，发奖会用到
	TaskId         string    `gorm:"column:task_id;NOT NULL"`                               // 任务id
	GunId          string    `gorm:"column:gun_id;NOT NULL"`                                // 枪械id
	LangType       string    `gorm:"column:lang_type;NOT NULL"`                             // 语言类型
	AmsSerialId    string    `gorm:"column:ams_serial_id;NOT NULL"`                         // 发奖serial_id,发奖使用
	SendPrizeTimes uint32    `gorm:"column:send_prize_times;NOT NULL"`                      // 奖品发放次数，因为可能重试
	Status         int       `gorm:"column:status;default:0;NOT NULL"`                      // 任务状态，0-进行中，1-等待领取奖励，2-奖励发放中， 3-发奖成功，4-发奖失败， 5-任务取消
	HasPrizeFlag   int       `gorm:"column:has_prize_flag;default:0;NOT NULL"`              // 是否重复发放导致失败，0-没有，1-已经给用户发放过导致失败
	UpdateTime     time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL"` // 更新时间
	CreateTime     time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL"` // 创建时间
}

// TableName ...
func (m *GunActivityTask) TableName() string {
	return "t_gun_activity_task"
}

// GunActivityTaskHelp 拼枪活动任务助力表
type GunActivityTaskHelp struct {
	ID         int64     `gorm:"column:id;primary_key;AUTO_INCREMENT"`                  // 自增id
	GameId     string    `gorm:"column:game_id;NOT NULL"`                               // 游戏id
	ActivityId string    `gorm:"column:activity_id;NOT NULL"`                           // 活动id
	UserId     string    `gorm:"column:user_id;NOT NULL"`                               // 用户id
	TaskId     string    `gorm:"column:task_id;NOT NULL"`                               // 任务id
	HelpType   int       `gorm:"column:help_type;NOT NULL"`                             // 助力类型，0-普通助力，1-关注社群自动增加助力值
	Status     int       `gorm:"column:status;default:0;NOT NULL"`                      // 助力状态，0-助力成功，1-无效
	Email      string    `gorm:"column:email;NOT NULL"`                                 // 用户打码后的email
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL"` // 更新时间
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL"` // 创建时间
}

// TableName ...
func (m *GunActivityTaskHelp) TableName() string {
	return "t_gun_activity_task_help"
}
