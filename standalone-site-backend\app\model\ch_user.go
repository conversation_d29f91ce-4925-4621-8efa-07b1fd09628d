package model

import "trpc.publishing_application.standalonesite/app/constants"

type ThirdChannel struct {
	ChannelName string                       `json:"channel_name"`
	ChannelType constants.ECreatorHubChannel `json:"channel_type"`
}

type CreatorHubUserInfo struct {
	Email         string                          `json:"email"`
	Status        constants.ECreatorHubUserStatus `json:"status"` // 用户状态 2=正常, 3=审核不通过, 4=账号冻结, 5=永久冻结, 6=特殊封禁, 7=仅允许提现
	ThirdChannels []ThirdChannel                  `json:"third_channels"`
	UserId        string                          `json:"user_id"`
	UserName      string                          `json:"user_name"`
	Country       string                          `json:"country"`
}
