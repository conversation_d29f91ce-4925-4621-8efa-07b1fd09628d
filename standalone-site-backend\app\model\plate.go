package model

type Plate struct {
	*Model
	Type             int32          `gorm:"column:type" json:"type"`           //feed流类型：1 双列瀑布流，2 单列卡片流
	JSONData         string         `gorm:"column:json_data" json:"json_data"` //置顶多语言数据
	Status           int            `gorm:"column:status" json:"status"`       //状态：1、不可见，2、可见
	Order            int            `gorm:"column:order" json:"order"`         //顺序值
	Creator          string         `gorm:"column:creator" json:"creator"`     //创建人
	Updater          string         `gorm:"column:updater" json:"updater"`     //操作人
	GameID           string         `gorm:"column:game_id" json:"game_id"`     //游戏id
	AreaID           string         `gorm:"column:area_id" json:"area_id"`     //大区id\
	UniqueIdentifier string         `json:"unique_identifier"`                 //板块唯一标识
	Language         *PlateLanguage `json:"language" gorm:"-"`
}

// TableName sets the insert table name for this struct type
func (p *Plate) TableName() string {
	return "p_plate"
}

type PlateLanguage struct {
	*Model
	ID        int    `gorm:"column:id;primary_key" json:"id"`     //
	PlateID   int    `gorm:"column:plate_id" json:"plate_id"`     //话题 id
	Language  string `gorm:"column:language" json:"language"`     //语言
	PlateName string `gorm:"column:plate_name" json:"plate_name"` //板块名称
	Creator   string `gorm:"column:creator" json:"creator"`       //创建人
	Updater   string `gorm:"column:updater" json:"updater"`       //更新人
}

// TableName sets the insert table name for this struct type
func (p *PlateLanguage) TableName() string {
	return "p_plate_language"
}
