package nikketmp

import (
	"context"
	"encoding/json"
	"fmt"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"math"
	"strings"
	"sync"
	"time"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/model/nikke"
)

// BunnyGirlRecordGift 兔女郎礼包记录
func BunnyGirlRecordGift(ctx context.Context, presentId string, roleInfo *gamePb.RoleInfo) (bool, error) {
	var isFirst bool
	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return isFirst, err
	}
	langType := metadata.GetLangType(ctx)
	// 是否已获取礼包
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeBunnyGirlGiftDistributionRecordModel{}.TableName()).
		Where("uid = ? and account_type = ?", userAccount.Uid, int(userAccount.AccountType)).
		Where("present_id = ?", presentId)
	var count int64
	if err = tx.Count(&count).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return isFirst, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"BunnyGirlRecordGift Count db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		return isFirst, nil
	}
	// 首次记录
	isFirst = true
	// 记录礼包信息
	info := buildRoleInfo(roleInfo)
	roleInfoStr, err := json.Marshal(info)
	if err != nil {
		return isFirst, errs.NewCustomError(ctx, code.CommonParamJsonError, fmt.Sprintf("roleInfo json Marshal error, err:[%v]", err))
	}
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeBunnyGirlGiftDistributionRecordModel{}.TableName()).
		Create(&nikke.NikkeBunnyGirlGiftDistributionRecord{
			UID:         userAccount.Uid,
			AccountType: int(userAccount.AccountType),
			RoleInfo:    string(roleInfoStr),
			PresentID:   presentId,
			LangType:    langType,
			Status:      0, // 未发货
		}).Error; err != nil {
		return isFirst, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"BunnyGirlRecordGift Create db error, \t [Error]:{%v} ", err)
	}
	return isFirst, nil

}

// ScheduledSendBunnyGirl 兔女郎活动定时发货
func ScheduledSendBunnyGirl(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	tableName := nikke.NikkeBunnyGirlGiftDistributionRecordModel{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"ScheduledSendBunnyGirl db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("ScheduledSendBunnyGirl:[%v]", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页200条
	pageSize := 200
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		offset := (pageNumber - 1) * pageSize
		var logData = []nikke.NikkeBunnyGirlGiftDistributionRecord{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).Where(condition).Offset(offset).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"ScheduledSendBunnyGirl Find db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("ScheduledSendBunnyGirl logData:[%v]", logData))
		for _, v := range logData {
			wg.Add(1)
			go func(data nikke.NikkeBunnyGirlGiftDistributionRecord) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(string(accountData))
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				// 获取角色信息
				var gameRoleInfo *gamePb.RoleInfo
				if data.RoleInfo == "" {
					err = errs.NewCustomError(ctx, code.CurrentUserRoleAbnormal, "ScheduledSendBunnyGirl CurrentUserRoleAbnormal err, data:[%v]", data)
				} else {
					gameRoleInfo, err = buildPBRoleInfo(data.RoleInfo)
				}
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf(
					"ScheduledSendBunnyGirl buildPBRoleInfo：%v", gameRoleInfo))

				if err == nil {
					log.WithFieldsContext(ctx, "ScheduledSendBunnyGirl log", "debug").Infof(fmt.Sprintf("ScheduledSendBunnyGirl dataInfo: %#v", data))
					// 发送礼包
					sendReq := &presentPb.SendPresentReq{
						PresentId: data.PresentID,
						RoleInfo:  gameRoleInfo,
					}
					accountData, _ = proto.Marshal(&accountPb.UserAccount{
						Uid:         data.UID,
						AccountType: accountPb.AccountType(data.AccountType),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							GameId:    gameRoleInfo.GameId,
							ChannelId: 3,
						},
					})
					callopts = []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
						client.WithMetaData(metadata.LangType, []byte(data.LangType)),
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
					action := "year_half_backup_reward_send_ret"
					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// 记录日志
						log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
							"ScheduledSendBunnyGirl ReportTlog succ: action:[%v],AreaId:[%v],GameId:[%v],openID:[%v],PresentID:[%v]",
							action, gameRoleInfo.AreaId, gameRoleInfo.GameId, openID, data.PresentID))
					} else {
						log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
							"ScheduledSendBunnyGirl ReportTlog fail: action:[%v],AreaId:[%v],GameId:[%v],openID:[%v],PresentID:[%v]",
							action, gameRoleInfo.AreaId, gameRoleInfo.GameId, openID, data.PresentID))
					}
					delErr := errs.ParseError(ctx, sendErr)
					if delErr.Code == 400018 || delErr.Code == 400042 {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[ScheduledSendBunnyGirl SendPresent] service err222:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// }
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[ScheduledSendBunnyGirl SendPresent] service err:%v", sendErr))
					// 修改发货状态
				} else {
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("ScheduledSendBunnyGirl GetRoleInfo] service err:%v", err))
				}
				wg.Done()
			}(v)

		}
		wg.Wait()

	}
	return
}
