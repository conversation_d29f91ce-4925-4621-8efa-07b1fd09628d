package tweet

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	redisClient "github.com/go-redis/redis/v8"
	es7 "github.com/olivere/elastic/v7"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/plate"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

type QueryRespTemp struct {
	Items []*model.Post
	Total int64
}

type QueryResp struct {
	Items              []*model.PostFormatted
	PreviousPageCursor string
	NextPageCursor     string
}

func GetPostListFromSearch(c context.Context, intlOpenID string, req *pb.GetPostListReq, language, gameId, areaId string) (*pb.GetPostListRsp, error) {
	// userContentInfo, err := dao.GetUserByIntlOpenid(intlOpenID)
	// if err != nil {
	// 	return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to GetPostListFromSearch GetUserByIntlOpenid")
	// }
	// var userInfo = &model.User{
	// IntlOpenid: userContentInfo.IntlOpenid,
	// }
	resp, err := EsSearch(c, intlOpenID, req, language, gameId, areaId)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func EsSearch(c context.Context, intlOpenID string, req *pb.GetPostListReq, language, gameId, areaId string) (*pb.GetPostListRsp, error) {
	if req.PlateId > 0 {
		plateInfo, err := plate.GetPlateInfoById(c, req.PlateId)
		if err != nil {
			return nil, err
		}
		// 如果是event板块，则简体中文用繁体中文内容代替
		if plateInfo.UniqueIdentifier == constants.PLATE_EVENT && language == "zh" {
			language = "zh-TW"
		}
		// 如果是event或者是creatorhub就直接走特殊查询逻辑
		if plateInfo.UniqueIdentifier == constants.PLATE_EVENT || plateInfo.UniqueIdentifier == constants.PLATE_CREATORHUB {
			return GetExternalPosts(c, req, intlOpenID, language, plateInfo.UniqueIdentifier)
		}
	}

	var err error
	var resp *pb.GetPostListRsp
	if req.SearchType == constants.SEARCH_TYPE_TAG && req.TagId > 0 {
		resp, err = queryByTag(c, intlOpenID, req, language, gameId, areaId)
	} else if req.SearchType == constants.SEARCH_TYPE_KEYWORD && req.Keyword != "" {
		resp, err = queryByContent(c, intlOpenID, req, language, gameId, areaId)
	} else if req.OrderBy == constants.OrderByTypeHot {
		resp, err = queryHot(c, intlOpenID, req, language, gameId, areaId)
	} else {
		resp, err = queryAny(c, intlOpenID, req, language, gameId, areaId)
	}
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("esTweetSearchServant.search searchType:%d query:%v error:%v", req.SearchType, req, err)
		return resp, err
	}

	// logrus.Debugf("esTweetSearchServant.Search type:%s query:%s resp Hits:%d NbHits:%d offset: %d limit:%d ", q.Type, q.Query, len(resp.Items), resp.Total, offset, limit)
	return resp, nil
}

// 参考：https://docs.elasticsearch.dev/api-es-compatible/search/types/#prefix
func queryByTag(c context.Context, intlOpenID string, req *pb.GetPostListReq, language, gameId, areaId string) (*pb.GetPostListRsp, error) {
	// 先获取缓存数据
	var hits []*es7.SearchHit
	// 先判断这个用户是否是有被隐藏的帖子
	var hasPostHide int64
	postHideCreatedUserKey := cache.PostHideCreatedUserOpenidCacheKeys(intlOpenID)
	hasPostHide, err := redis.GetClient().SCard(c, postHideCreatedUserKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("queryByTag hide user get cache redis err: %v", err)
		}
	}
	postBasesRedisKey := cache.GetTagPostBaseListKey(req.TagId, req.NeedAllRegion, language, req.PlateId, req.OrderBy, req.NextPageCursor, req.Limit)
	if hasPostHide > 0 {
		// 说明还有帖子被隐藏了
		postBasesRedisKey = cache.GetTagPostBaseListHasPostHideKey(req.TagId, req.NeedAllRegion, language, req.PlateId, req.OrderBy, req.NextPageCursor, req.Limit, intlOpenID)
	}
	postBasesCacheInfo, err := redis.GetClient().Get(c, postBasesRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postBasesCacheInfo), &hits)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("queryByTag es postBases cache json.Unmarshal error.postBasesRedisKey: %s, err: %v", postBasesRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetTagPostsJsonUnmarshalError, "Failed to obtain posts info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("queryByTag postBases redis err: %v", err)
		}
		boolQuery := es7.NewBoolQuery()
		tagsQuery := es7.NewTermQuery("tags", req.TagId)
		languageQuery := es7.NewTermQuery("post_languages", language)
		//gameidQuery := es7.NewTermQuery("game_id", gameId)
		//areaidQuery := es7.NewTermQuery("area_id", areaId)
		isauditQuery := es7.NewTermQuery("is_audit", 1)
		isDelQuery := es7.NewTermQuery("is_del", 0)
		//boolQuery.Must(tagsQuery, gameidQuery, areaidQuery, isauditQuery, isDelQuery)
		boolQuery.Must(tagsQuery, isauditQuery, isDelQuery)
		if !req.NeedAllRegion && language != "" {
			boolQuery.Must(languageQuery)
			// languageBoolQuery := es7.NewBoolQuery()
			// languageBoolQuery.Should(es7.NewTermQuery("language", language), es7.NewTermQuery("language", "all"))
			// languageBoolQuery.MinimumNumberShouldMatch(1) //至少满足其中一个条件
			// //languageQuery := es7.NewTermQuery("language", language)

			// boolQuery.Must(languageBoolQuery)
		}
		if hasPostHide > 0 {
			// 存在用户登录的情况下需要展示用户隐藏的动态
			openUpQuery := es7.NewTermQuery("is_hide", 0)
			hideQuery := es7.NewTermQuery("is_hide", 1)
			intlOpenIDQuery := es7.NewTermQuery("intl_openid", intlOpenID)
			orQuery := (&es7.BoolQuery{}).Should(openUpQuery, (es7.NewBoolQuery()).Must(hideQuery, intlOpenIDQuery))
			boolQuery.Must(orQuery)
		} else {
			openUpQuery := es7.NewTermQuery("is_hide", 0)
			boolQuery.Must(openUpQuery)
		}
		if req.PlateId > 0 {
			platIdQuery := es7.NewTermQuery("plate_id", req.PlateId)
			boolQuery.Must(platIdQuery)
		}
		var sortBys []es7.Sorter
		if req.OrderBy == constants.OrderByTypeHot {
			hotSortQuery := es7.NewFieldSort("hot_num").Desc()
			sortBys = append(sortBys, hotSortQuery)
		}
		sortByCreatedOnQuery := es7.NewFieldSort("created_on_ms").Desc()
		// sortByIdQuery := es7.NewFieldSort("id").Desc()
		sortBys = append(sortBys, sortByCreatedOnQuery)
		var lastSortValue []interface{}
		if req.NextPageCursor != "" {
			cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
			}
			err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "queryByTag2 | Failed to get idCursor")
			}
		}
		resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, boolQuery, sortBys, lastSortValue, req.Limit)
		if err != nil {
			return nil, err
		}
		if resp != nil && resp.Hits != nil && len(resp.Hits.Hits) > 0 {
			hits = resp.Hits.Hits
		}
		postListByte, err := json.Marshal(hits)
		if err == nil {
			redis.GetClient().SetEX(c, postBasesRedisKey, string(postListByte), 2*time.Minute).Result()
		}
		// 统一记录所有这个话题的redis缓存key，用于清理缓存
		keyKeys := cache.GetTagPostCacheKeysKey(req.TagId)
		redis.GetClient().SAdd(c, keyKeys, postBasesRedisKey)
		redis.GetClient().Expire(c, keyKeys, 2*time.Minute)
	}
	return postsFrom(c, intlOpenID, hits, req, language, 0)
}

// 参考：https://docs.elasticsearch.dev/api-es-compatible/search/types/#prefix
func queryByContent(c context.Context, intlOpenID string, req *pb.GetPostListReq, language, gameId, areaId string) (*pb.GetPostListRsp, error) {
	// 先获取缓存数据
	var hits []*es7.SearchHit
	// 先判断这个用户是否是有被隐藏的帖子
	var hasPostHide int64
	postHideCreatedUserKey := cache.PostHideCreatedUserOpenidCacheKeys(intlOpenID)
	hasPostHide, err := redis.GetClient().SCard(c, postHideCreatedUserKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("queryByTag hide user get cache redis err: %v", err)
		}
	}
	postBasesRedisKey := cache.GetContentQueryPostBaseListKey(req.Keyword, req.NeedAllRegion, language, req.PlateId, req.Platform, req.OrderBy, req.NextPageCursor, req.Limit)
	if hasPostHide > 0 {
		// 说明还有帖子被隐藏了
		postBasesRedisKey = cache.GetContentQueryPostBaseListHasPostHideKey(req.Keyword, req.NeedAllRegion, language, req.PlateId, req.Platform, req.OrderBy, req.NextPageCursor, req.Limit, intlOpenID)
	}
	postBasesCacheInfo, err := redis.GetClient().Get(c, postBasesRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postBasesCacheInfo), &hits)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("queryByContent es postBases cache json.Unmarshal error.postBasesRedisKey: %s, err: %v", postBasesRedisKey, err)
			return nil, errs.NewCustomError(c, code.QueryContentPostsJsonUnmarshalError, "Failed to obtain posts info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("queryByContent postBases redis err: %v", err)
		}
		boolQuery := es7.NewBoolQuery()
		//gameidQuery := es7.NewTermQuery("game_id", gameId)
		//areaidQuery := es7.NewTermQuery("area_id", areaId)
		isauditQuery := es7.NewTermQuery("is_audit", 1)
		isDelQuery := es7.NewTermQuery("is_del", 0)
		//boolQuery.Must(gameidQuery, areaidQuery, isauditQuery, isDelQuery)
		languageQuery := es7.NewTermQuery("post_languages", language)
		boolQuery.Must(isauditQuery, isDelQuery)
		queryKey := fmt.Sprintf("all_content_lang_%s", language)
		contentQuery := es7.NewMatchQuery(queryKey, req.Keyword).Fuzziness("AUTO")
		// contentQuery := es7.NewMatchPhraseQuery("content", req.Keyword)
		boolQuery.Must(contentQuery)
		if !req.NeedAllRegion && language != "" {
			boolQuery.Must(languageQuery)
			// // 根据当前用户语言搜索
			// languageBoolQuery := es7.NewBoolQuery()
			// languageBoolQuery.Should(es7.NewTermQuery("language", language), es7.NewTermQuery("language", "all"))
			// languageBoolQuery.MinimumNumberShouldMatch(1) //至少满足其中一个条件
			// //languageQuery := es7.NewTermQuery("language", language)

			// boolQuery.Must(languageBoolQuery)
		}
		if req.PlateId > 0 {
			platIdQuery := es7.NewTermQuery("plate_id", req.PlateId)
			boolQuery.Must(platIdQuery)
		} else {
			platIdQuery := es7.NewTermQuery("plate_id", 41)
			boolQuery.MustNot(platIdQuery)
		}
		if req.Platform != "" {
			platIdQuery := es7.NewTermQuery("platform", req.Platform)
			boolQuery.Filter(platIdQuery)
		}

		if hasPostHide > 0 {
			// 存在用户登录的情况下需要展示用户隐藏的动态
			openUpQuery := es7.NewTermQuery("is_hide", 0)
			hideQuery := es7.NewTermQuery("is_hide", 1)
			intlOpenIDQuery := es7.NewTermQuery("intl_openid", intlOpenID)
			orQuery := (&es7.BoolQuery{}).Should(openUpQuery, (es7.NewBoolQuery()).Must(hideQuery, intlOpenIDQuery))
			boolQuery.Must(orQuery)
		} else {
			openUpQuery := es7.NewTermQuery("is_hide", 0)
			boolQuery.Must(openUpQuery)
		}

		var sortBys []es7.Sorter
		if req.OrderBy == constants.OrderByTypeHot {
			hotSortQuery := es7.NewFieldSort("hot_num").Desc()
			sortBys = append(sortBys, hotSortQuery)
		}
		sortByCreatedOnQuery := es7.NewFieldSort("created_on_ms").Desc()
		// sortByIdQuery := es7.NewFieldSort("id").Desc()
		sortBys = append(sortBys, sortByCreatedOnQuery)
		var lastSortValue []interface{}
		if req.NextPageCursor != "" {
			cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
			}
			err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "queryByContent2 | Failed to get idCursor")
			}
		}
		resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, boolQuery, sortBys, lastSortValue, req.Limit)
		if err != nil {
			return nil, err
		}
		if resp != nil && resp.Hits != nil && len(resp.Hits.Hits) > 0 {
			hits = resp.Hits.Hits
		}
		postListByte, err := json.Marshal(hits)
		if err == nil {
			redis.GetClient().SetEX(c, postBasesRedisKey, string(postListByte), 2*time.Minute).Result()
		}
	}
	return postsFrom(c, intlOpenID, hits, req, language, 0)
}

// queryHot 根据动态的热度来排序
func queryHot(c context.Context, intlOpenID string, req *pb.GetPostListReq, language, gameId, areaId string) (*pb.GetPostListRsp, error) {
	// 先获取缓存数据
	var hits []*es7.SearchHit
	// 先判断这个用户是否是有被隐藏的帖子
	var hasPostHide int64
	postHideCreatedUserKey := cache.PostHideCreatedUserOpenidCacheKeys(intlOpenID)
	hasPostHide, err := redis.GetClient().SCard(c, postHideCreatedUserKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("queryHot hide user get cache redis err: %v", err)
		}
	}
	postBasesRedisKey := cache.GetHotPostBaseListKey(req.NeedAllRegion, language, req.PlateId, req.Platform, req.NextPageCursor, req.Limit)
	if hasPostHide > 0 {
		postBasesRedisKey = cache.GetHotPostBaseListHasPostHideKey(req.NeedAllRegion, language, req.PlateId, req.Platform, req.NextPageCursor, req.Limit, intlOpenID)

	}
	postBasesCacheInfo, err := redis.GetClient().Get(c, postBasesRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postBasesCacheInfo), &hits)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("queryHot es postBases cache json.Unmarshal error.postBasesRedisKey: %s, err: %v", postBasesRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetHotPostsJsonUnmarshalError, "Failed to obtain posts info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("queryHot postBases redis err: %v", err)
		}
		var err error
		//gameidQuery := es7.NewTermQuery("game_id", gameId)
		//areaidQuery := es7.NewTermQuery("area_id", areaId)
		//languageQuery := es7.NewTermQuery("language", language)

		var hotTopResp *es7.SearchResult
		var hotNotTopResp *es7.SearchResult
		var hotMustQuery *es7.BoolQuery
		isAuditQuery := es7.NewTermQuery("is_audit", 1)
		isDelQuery := es7.NewTermQuery("is_del", 0)
		languageQuery := es7.NewTermQuery("post_languages", language)
		var sortBys []es7.Sorter
		var sortTopBys []es7.Sorter
		sortByTopSortQuery := es7.NewFieldSort("top_sort").Asc()
		sortByHotNumQuery := es7.NewFieldSort("hot_num").Desc()
		sortByCreateOnQuery := es7.NewFieldSort("created_on_ms").Desc()
		sortByIdQuery := es7.NewFieldSort("id").Desc()
		if hasPostHide > 0 {
			// 存在用户登录的情况下需要展示用户隐藏的动态
			openUpQuery := es7.NewTermQuery("is_hide", 0)
			hideQuery := es7.NewTermQuery("is_hide", 1)
			intlOpenIDQuery := es7.NewTermQuery("intl_openid", intlOpenID)
			orQuery := (&es7.BoolQuery{}).Should(openUpQuery, (es7.NewBoolQuery()).Must(hideQuery, intlOpenIDQuery))
			hotMustQuery = (&es7.BoolQuery{}).Must(orQuery)
		} else {
			openUpQuery := es7.NewTermQuery("is_hide", 0)
			hotMustQuery = (&es7.BoolQuery{}).Must(openUpQuery)
		}
		var lastSortValue []interface{}
		if req.PageType == pb.PageType_NEXTPAGE && req.NextPageCursor == "" {
			// 如果是第一页，则先查询置顶的
			hotIsTopQuery := es7.NewBoolQuery()
			isTopQuery := es7.NewTermQuery("is_top", 1)
			topRangeQuery := es7.NewRangeQuery("top_on").Gte(time.Now().Unix())
			//hotIsTopQuery.Filter(gameidQuery, areaidQuery, isAuditQuery, isTopQuery, topRangeQuery, isDelQuery)
			hotIsTopQuery.Must(isAuditQuery, isTopQuery, isDelQuery)
			if !req.NeedAllRegion && language != "" {
				hotIsTopQuery.Must(languageQuery)
			}
			if req.PlateId > 0 {
				platIdQuery := es7.NewTermQuery("plate_id", req.PlateId)
				hotIsTopQuery.Must(platIdQuery)
			}
			if req.Platform != "" {
				platIdQuery := es7.NewTermQuery("platform", req.Platform)
				hotIsTopQuery.Must(platIdQuery)
			}
			if req.RankId > 0 {
				rankIdQuery := es7.NewTermQuery("creatorhub_rank_id", req.RankId)
				hotIsTopQuery.Must(rankIdQuery)
			}
			if req.TaskId > 0 {
				taskIdQuery := es7.NewTermQuery("creatorhub_task_id", req.TaskId)
				hotIsTopQuery.Must(taskIdQuery)
			}
			hotIsTopQuery.Filter(topRangeQuery, hotMustQuery)
			sortTopBys = append(sortTopBys, sortByTopSortQuery, sortByHotNumQuery, sortByCreateOnQuery, sortByIdQuery)
			hotTopResp, err = dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, hotIsTopQuery, sortTopBys, lastSortValue, 100)
			if err != nil {
				return nil, err
			}
		}

		// 查找非置顶的动态，并按照热度排
		hotIsNotTopQuery := es7.NewBoolQuery()
		isNotTopQuery := es7.NewTermQuery("is_top", 0)
		isNotTopRangeQuery := es7.NewRangeQuery("top_on").Lt(time.Now().Unix())
		hotIsNotTopQuery.Should(isNotTopQuery)
		//hotIsNotTopQuery.Filter(gameidQuery, areaidQuery, isAuditQuery, isNotTopRangeQuery, isDelQuery)
		hotIsNotTopQuery.Must(isAuditQuery, isDelQuery)
		if !req.NeedAllRegion && language != "" {
			hotIsNotTopQuery.Must(languageQuery)
		}
		if req.PlateId > 0 {
			platIdQuery := es7.NewTermQuery("plate_id", req.PlateId)
			hotIsNotTopQuery.Must(platIdQuery)
		}
		if req.Platform != "" {
			platIdQuery := es7.NewTermQuery("platform", req.Platform)
			hotIsNotTopQuery.Must(platIdQuery)
		}
		if req.RankId > 0 {
			rankIdQuery := es7.NewTermQuery("creatorhub_rank_id", req.RankId)
			hotIsNotTopQuery.Must(rankIdQuery)
		}
		if req.TaskId > 0 {
			taskIdQuery := es7.NewTermQuery("creatorhub_task_id", req.TaskId)
			hotIsNotTopQuery.Must(taskIdQuery)
		}
		hotIsNotTopQuery.Filter(isNotTopRangeQuery, hotMustQuery)
		sortBys = append(sortBys, sortByHotNumQuery, sortByCreateOnQuery, sortByIdQuery)
		if req.NextPageCursor != "" {
			cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
			}
			err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "queryByContent2 | Failed to get idCursor")
			}
		}

		hotNotTopResp, err = dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, hotIsNotTopQuery, sortBys, lastSortValue, req.Limit)
		if err != nil {
			return nil, err
		}
		if hotTopResp != nil && hotTopResp.Hits != nil && len(hotTopResp.Hits.Hits) > 0 {
			hits = append(hits, hotTopResp.Hits.Hits...)
		}
		if hotNotTopResp != nil && hotNotTopResp.Hits != nil && len(hotNotTopResp.Hits.Hits) > 0 {
			hits = append(hits, hotNotTopResp.Hits.Hits...)
		}
		postListByte, err := json.Marshal(hits)
		if err == nil {
			redis.GetClient().SetEX(c, postBasesRedisKey, string(postListByte), 2*time.Minute).Result()
		}
	}

	queryResp1, err := postsFrom(c, intlOpenID, hits, req, language, 0)
	if err != nil {
		return nil, err
	}
	queryResp1.List = append(queryResp1.List)
	queryResp1.PageInfo = queryResp1.PageInfo
	return queryResp1, nil
}

func queryAny(c context.Context, intlOpenID string, req *pb.GetPostListReq, language, gameId, areaId string) (*pb.GetPostListRsp, error) { // 先获取缓存数据
	var hits []*es7.SearchHit
	postBasesRedisKey := cache.GetAnyQueryPostBaseListKey(req.NeedAllRegion, language, req.PlateId, req.Platform, req.NextPageCursor, req.Limit)
	postBasesCacheInfo, err := redis.GetClient().Get(c, postBasesRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postBasesCacheInfo), &hits)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("queryAny es postBases cache json.Unmarshal error.postBasesRedisKey: %s, err: %v", postBasesRedisKey, err)
			return nil, errs.NewCustomError(c, code.QueryAnyPostsJsonUnmarshalError, "Failed to obtain posts info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("queryAny postBases redis err: %v", err)
		}
		boolQuery := es7.NewBoolQuery()
		allQuery := es7.NewMatchAllQuery()
		//gameidQuery := es7.NewTermQuery("game_id", gameId)
		//areaidQuery := es7.NewTermQuery("area_id", areaId)
		isauditQuery := es7.NewTermQuery("is_audit", 1)
		isDelQuery := es7.NewTermQuery("is_del", 0)
		boolQuery.Must(allQuery)
		//boolQuery.Filter(gameidQuery, areaidQuery, isauditQuery, isDelQuery)
		boolQuery.Filter(isauditQuery, isDelQuery)
		languageQuery := es7.NewTermQuery("post_languages", language)
		if !req.NeedAllRegion && language != "" {
			//languageQuery := es7.NewTermQuery("language", language)
			//languageQuery := es7.NewTermQuery("language", language)
			boolQuery.Filter(languageQuery)
		}
		if req.Platform != "" {
			platIdQuery := es7.NewTermQuery("platform", req.Platform)
			boolQuery.Filter(platIdQuery)
		}
		if req.PlateId > 0 {
			platIdQuery := es7.NewTermQuery("plate_id", req.PlateId)
			boolQuery.Filter(platIdQuery)
		}
		var sortBys []es7.Sorter
		sortQuery := es7.NewFieldSort("created_on_ms").Desc()
		sortByIdQuery := es7.NewFieldSort("id").Desc()
		sortBys = append(sortBys, sortQuery, sortByIdQuery)
		var lastSortValue []interface{}
		if req.NextPageCursor != "" {
			cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
			}
			err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "queryByContent2 | Failed to get idCursor")
			}
		}
		resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, boolQuery, sortBys, lastSortValue, req.Limit)
		if err != nil {
			return nil, err
		}
		if resp != nil && resp.Hits != nil && len(resp.Hits.Hits) > 0 {
			hits = resp.Hits.Hits
		}
		postListByte, err := json.Marshal(hits)
		if err == nil {
			redis.GetClient().SetEX(c, postBasesRedisKey, string(postListByte), 2*time.Minute).Result()
		}
	}
	return postsFrom(c, intlOpenID, hits, req, language, 0)
}

// 参考：https://docs.elasticsearch.dev/api-es-compatible/search/types/#prefix
func CMSQueryPostList(c context.Context, req *pb.CMSGetPostListReq, language, gameId, areaId string) (*pb.CMSGetPostListRsp, error) {
	boolQuery := es7.NewBoolQuery()
	//gameidQuery := es7.NewTermQuery("game_id", gameId)
	//areaidQuery := es7.NewTermQuery("area_id", areaId)
	//boolQuery.Must(gameidQuery, areaidQuery)
	currentTime := time.Now().Unix()
	if req.PostType == 1 {
		itemQuery := es7.NewTermQuery("is_essence", 1)
		boolQuery.Must(itemQuery)
		itemQuery2 := es7.NewRangeQuery("essence_on").Gte(currentTime)
		boolQuery.Filter(itemQuery2)
	}
	if req.IsDel >= 0 {
		itemQuery := es7.NewTermQuery("is_del", req.IsDel)
		boolQuery.Must(itemQuery)
	}
	if req.PostType == 2 {
		itemQuery := es7.NewTermQuery("is_top", 1)
		boolQuery.Must(itemQuery)
		itemQuery2 := es7.NewRangeQuery("top_on").Gte(currentTime)
		boolQuery.Filter(itemQuery2)
	}
	if req.IsAudit > 0 {
		itemQuery := es7.NewTermQuery("is_audit", req.IsAudit)
		boolQuery.Must(itemQuery)
	}
	if req.TagId > 0 {
		itemQuery := es7.NewTermQuery("tags", req.TagId)
		boolQuery.Must(itemQuery)
	}
	if req.PostUuid != "" {
		itemQuery := es7.NewTermQuery("post_uuid", req.PostUuid)
		boolQuery.Must(itemQuery)
	}
	if req.Keyword != "" {
		itemQuery := es7.NewWildcardQuery("content", "*"+req.Keyword+"*")
		titleQuery := es7.NewWildcardQuery("title", "*"+req.Keyword+"*")
		boolQuery.Must(es7.NewBoolQuery().Should(itemQuery, titleQuery))
	}
	if req.IntlOpenid != "" {
		itemQuery := es7.NewTermQuery("intl_openid", req.IntlOpenid)
		boolQuery.Must(itemQuery)
	}

	if req.IntlUserOpenid != "" {
		if strings.Contains(req.IntlUserOpenid, "-") {
			// 如果传递进来的用户id包含了区服信息的，需要使用另外一个字段
			itemQuery := es7.NewTermQuery("intl_openid", req.IntlUserOpenid)
			boolQuery.Must(itemQuery)
		} else {
			itemQuery := es7.NewTermQuery("intl_user_openid", req.IntlUserOpenid)
			boolQuery.Must(itemQuery)
		}
	}
	weightRangeType := req.GetWeightRangeType()
	if weightRangeType == 1 {
		// 小于1
		boolQuery.Must(es7.NewRangeQuery("power_num_float").Lt(1))
	}
	if weightRangeType == 2 {
		// 等于1 字段为浮点型，存在误差范围
		boolQuery.Must(es7.NewRangeQuery("power_num_float").Gt(1 - 0.00001).Lt(1 + 0.00001))
	}
	if weightRangeType == 3 {
		// 大于1
		boolQuery.Must(es7.NewRangeQuery("power_num_float").Gt(1))
	}

	if req.IsOfficial > 0 {
		// B端加1 这里减一
		isOfficial := req.IsOfficial - 1
		if isOfficial >= 0 {
			itemQuery := es7.NewTermQuery("is_official", isOfficial)
			boolQuery.Must(itemQuery)
		}
	}
	if req.Language != "" {
		itemQuery := es7.NewTermQuery("post_languages", req.Language)
		boolQuery.Must(itemQuery)
	}
	if req.ContentStatus > 0 {
		var itemQuery *es7.TermQuery
		switch constants.PostContentStatus(req.ContentStatus) {
		case constants.NormalPost:
			itemQuery = es7.NewTermQuery("is_audit", 1)
			break
		case constants.SandboxPost:
			itemQuery = es7.NewTermQuery("is_audit", 2)
			break
		case constants.UserDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.UserDeleted)
			break
		case constants.CAdminDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.CAdminDeleted)
			break
		case constants.BReviewDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.BReviewDeleted)
			break
		case constants.BAdminDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.BAdminDeleted)
		case constants.BReportDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.ReportBAdminDeleted)
			break
		default:
			return nil, errs.NewCustomError(c, code.InvalidParamsErr, "content status enum is required")
		}
		boolQuery.Must(itemQuery)
	}
	querySrc, _ := boolQuery.Source()
	log.InfoContextf(c, "CMSQueryPostList boolQuery source json:%v", util.ToJson(querySrc))

	// 生成排序规则
	sortBys := genSorters(req.GetSorter())

	var lastSortValue []interface{}
	if req.NextPageCursor != "" {
		cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
		if err != nil {
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
		}
		err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "queryByContent2 | Failed to get idCursor")
		}
	}
	// 查询数量加1，避免最后一页和分页数相同
	addLimit := 1
	limit := req.Limit + int64(addLimit)
	resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, boolQuery, sortBys, lastSortValue, limit)
	if err != nil {
		return nil, err
	}
	getPostListReq := &pb.GetPostListReq{
		Limit:          req.Limit,
		NextPageCursor: req.NextPageCursor,
	}
	var hits []*es7.SearchHit
	if resp != nil && resp.Hits != nil && len(resp.Hits.Hits) > 0 {
		hits = resp.Hits.Hits
	}
	getPostListRsp, err := CmsPostsFrom(c, "", hits, getPostListReq, language, addLimit)
	if err != nil {
		return nil, err
	}
	cMSGetPostListRsp := &pb.CMSGetPostListRsp{
		List:     getPostListRsp.List,
		PageInfo: getPostListRsp.PageInfo,
	}

	return cMSGetPostListRsp, nil
}

func genSorters(reqSorter string) []es7.Sorter {
	var defaultSorters, sorters []es7.Sorter
	defaultSorters = append(defaultSorters, es7.NewFieldSort("created_on_ms").Desc())
	defaultSorters = append(defaultSorters, es7.NewFieldSort("id").Desc())

	// 解析请求的排序方式
	splits := strings.Split(reqSorter, " ")
	if len(splits) != 2 {
		// 不符合规则，使用默认排序
		return defaultSorters
	}
	field, orderMethod := splits[0], splits[1]
	if field != "hot_num" {
		// 不符合规则，使用默认排序
		return defaultSorters
	}
	if orderMethod == "asc" {
		sorters = append(sorters, es7.NewFieldSort(field).Asc())
	} else {
		sorters = append(sorters, es7.NewFieldSort(field).Desc())
	}
	// 兜底按照文档id排序
	sorters = append(sorters, es7.NewFieldSort("id").Desc())
	return sorters
}

// 参考：https://docs.elasticsearch.dev/api-es-compatible/search/types/#prefix
func CMSQueryPostAuditList(c context.Context, req *pb.CMSGetPostAuditListReq, language, gameId, areaId string) (*pb.CMSGetPostAuditListRsp, error) {
	boolQuery := es7.NewBoolQuery()
	//gameidQuery := es7.NewTermQuery("game_id", gameId)
	//areaidQuery := es7.NewTermQuery("area_id", areaId)
	//boolQuery.Must(gameidQuery, areaidQuery)
	if req.IsDel >= 0 {
		itemQuery := es7.NewTermQuery("is_del", req.IsDel)
		boolQuery.Must(itemQuery)
	}
	if req.TextRiskLevel > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		textRiskLevel := req.TextRiskLevel - 1
		if textRiskLevel < 0 {
			textRiskLevel = 0
		}
		itemQuery := es7.NewTermQuery("text_risk_level", textRiskLevel)
		boolQuery.Must(itemQuery)
	}
	if req.TextRiskType > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		textRiskType := req.TextRiskType - 1
		if textRiskType < 0 {
			textRiskType = 0
		}
		itemQuery := es7.NewTermQuery("text_risk_type", textRiskType)
		boolQuery.Must(itemQuery)
	}
	if req.PicRiskLevel > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		picRiskLevel := req.PicRiskLevel - 1
		if picRiskLevel < 0 {
			picRiskLevel = 0
		}
		itemQuery := es7.NewTermQuery("pic_risk_level", picRiskLevel)
		boolQuery.Must(itemQuery)
	}
	if req.PicRiskType > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		picRiskType := req.PicRiskType - 1
		if picRiskType < 0 {
			picRiskType = 0
		}
		itemQuery := es7.NewTermQuery("pic_risk_type", picRiskType)
		boolQuery.Must(itemQuery)
	}
	if req.Status > 0 {
		itemQuery := es7.NewTermQuery("status", req.Status)
		boolQuery.Must(itemQuery)
	}
	if req.Keyword != "" {
		// itemQuery := es7.NewMatchPhraseQuery("content", req.Keyword)
		itemQuery := es7.NewWildcardQuery("content", "*"+req.Keyword+"*")
		titleQuery := es7.NewWildcardQuery("title", "*"+req.Keyword+"*")
		boolQuery.Must(es7.NewBoolQuery().Should(itemQuery, titleQuery))
	}
	if req.IntlOpenid != "" {
		itemQuery := es7.NewTermQuery("intl_openid", req.IntlOpenid)
		boolQuery.Must(itemQuery)
	}
	if req.IntlUserOpenid != "" {
		if strings.Contains(req.IntlUserOpenid, "-") {
			// 如果传递进来的用户id包含了区服信息的，需要使用另外一个字段
			itemQuery := es7.NewTermQuery("intl_openid", req.IntlUserOpenid)
			boolQuery.Must(itemQuery)
		} else {
			itemQuery := es7.NewTermQuery("intl_user_openid", req.IntlUserOpenid)
			boolQuery.Must(itemQuery)
		}
	}
	if req.StartTime > 0 {
		itemQuery := es7.NewRangeQuery("created_on").Gte(req.StartTime)
		boolQuery.Filter(itemQuery)
	}
	if req.EndTime > 0 {
		itemQuery := es7.NewRangeQuery("created_on").Lte(req.EndTime)
		boolQuery.Filter(itemQuery)
	}
	// 机审状态查询
	if req.MachineStatus > 0 {
		// b端加1
		machineStatus := req.MachineStatus - 1
		if machineStatus <= 0 {
			machineStatus = 0
		}
		//switch machineStatus {
		//case 0:
		//	// 待机审
		//	itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 1)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//	break
		//case 1:
		//	// 机身通过
		//	itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 2)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//case 2:
		//	// 机审异常
		//	itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 1)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//}
		// 产品需要单个字段可以单独筛选
		itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		boolQuery.Must(itemQuery)

	}
	// 人审状态查询
	if req.ArtificialStatus > 0 {
		// b端加1
		artificialStatus := req.ArtificialStatus - 1
		if artificialStatus <= 0 {
			artificialStatus = 0
		}
		//switch artificialStatus {
		//case 0:
		//	// 待人审
		//	machineQuery := es7.NewTermQuery("machine_status", 2)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 1)
		//	boolQuery.Must(machineQuery, auditStatusQuery)
		//	break
		//case 1:
		//	// 人审通过
		//	itemQuery := es7.NewTermQuery("artificial_status", artificialStatus)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 2)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//case 2:
		//	// 人审拒绝
		//	itemQuery := es7.NewTermQuery("artificial_status", artificialStatus)
		//	auditStatusQuery := es7.NewTermQuery("audit_status", 3)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//}
		// 产品需要单个字段可以单独筛选
		itemQuery := es7.NewTermQuery("artificial_status", artificialStatus)
		boolQuery.Must(itemQuery)
	}
	// 内容状态查询
	if req.ContentStatus > 0 {
		itemQuery := es7.NewTermQuery("status", req.ContentStatus)
		boolQuery.Must(itemQuery)
	}
	// 筛选语言
	if req.Language != "" {
		itemQuery := es7.NewTermQuery("language", req.Language)
		boolQuery.Must(itemQuery)
	}
	// 动态id筛选
	if req.PostUuid != "" {
		itemQuery := es7.NewTermQuery("post_uuid", req.PostUuid)
		boolQuery.Must(itemQuery)
	}

	var sortBys []es7.Sorter
	if req.Status == 0 {
		// 【全部tab】 按照操作时间倒序
		sortBys = append(sortBys, es7.NewFieldSort("audit_on").Desc())
	}
	sortBys = append(sortBys, es7.NewFieldSort("status").Asc(),
		es7.NewFieldSort("id").Desc())
	var lastSortValue []interface{}
	if req.NextPageCursor != "" {
		cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
		if err != nil {
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
		}
		err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "queryByContent2 | Failed to get idCursor")
		}
	}
	// 查询数量加1，避免最后一页和分页数相同
	addLimit := 1
	limit := req.Limit + int64(addLimit)
	resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetAuditIndex, boolQuery, sortBys, lastSortValue, limit)
	if err != nil {
		return nil, err
	}
	getPostAuditListRsp, err := postsAuditFrom(c, "", resp, req, language, addLimit)
	if err != nil {
		return nil, err
	}
	return getPostAuditListRsp, nil
}

// addLimit 加量查询的值，在返回用户数据的时候需要减去
func postsFrom(c context.Context, intlOpenID string, hits []*es7.SearchHit, req *pb.GetPostListReq, language string, addLimit int) (*pb.GetPostListRsp, error) {
	getPostListRsp := &pb.GetPostListRsp{
		List:     make([]*pb.GetPostRsp, 0),
		PageInfo: &pb.PageInfo{},
	}
	if len(hits) == 0 {
		getPostListRsp.PageInfo.IsFinish = true
		return getPostListRsp, nil
	}
	currentTime := time.Now().Unix()

	// 裁剪返回结果的数据，需要减去加量查询的值
	if len(hits) > int(req.Limit) {
		hits = hits[0:(len(hits) - addLimit)]
	}

	var postUUIDsStr string
	var postUUIDs []string
	for _, hit := range hits {
		espost := &model.ESPost{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, espost); err != nil {
			return nil, err
		}
		postUUIDsStr = fmt.Sprintf("%s_%s", postUUIDsStr, espost.PostUuid)
		postUUIDs = append(postUUIDs, espost.PostUuid)
	}
	hash := sha256.New()
	hash.Write([]byte(postUUIDsStr))
	hashValue := hash.Sum(nil)
	postUUIDsMd5Str := hex.EncodeToString(hashValue)

	// 先获取缓存数据
	postFormatteds := make([]*pb.GetPostRsp, 0, len(hits))
	postDetailsRedisKey := cache.GetPostDetailListKeyWithIntlOpenid(intlOpenID, postUUIDsMd5Str, language)
	posDetailsCacheInfo, err := redis.GetClient().Get(c, postDetailsRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(posDetailsCacheInfo), &postFormatteds)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("postsFrom postDetails cache json.Unmarshal error.postDetailsRedisKey: %s, err: %v", postDetailsRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetHotPostsDetailJsonUnmarshalError, "Failed to obtain post detail info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetIndexPosts postDetails redis err: %v", err)
		}

		var wg sync.WaitGroup
		postFormattedChannel := make(chan *PostFormattedChannel, 1)
		for i, hit := range hits {
			espost := &model.ESPost{}
			raw, err := json.Marshal(hit.Source)
			if err != nil {
				return nil, err
			}
			if err = json.Unmarshal(raw, espost); err != nil {
				return nil, err
			}

			if espost.EssenceOn < currentTime {
				espost.IsEssence = 0
			}
			if espost.TopOn < currentTime {
				espost.IsTop = 0
			}
			upvoteMap := make(map[int64]int64)
			if espost.UpvoteMap != "" {
				err := json.Unmarshal([]byte(espost.UpvoteMap), &upvoteMap)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("ES postsFrom UpvoteMap json.Unmarshal err: %v, postUUID is %s", err, espost.PostUuid)
				}
			}

			item := &pb.GetPostRsp{
				PostUuid:        espost.PostUuid,
				IntlOpenid:      espost.IntlOpenid,
				Title:           espost.Title,
				Content:         espost.Content,
				ContentSummary:  espost.ContentSummary,
				PicUrls:         strings.Split(espost.PicUrls, ","),
				CommentCount:    espost.CommentCount,
				CollectionCount: espost.CollectionCount,
				UpvoteCount:     espost.UpvoteCount,
				UpvoteMap:       upvoteMap,
				BrowseCount:     espost.BrowseCount,
				ForwardCount:    espost.ForwardCount,
				IsTop:           espost.IsTop,
				TopSort:         espost.TopSort,
				TopOn:           espost.TopOn,
				IsEssence:       espost.IsEssence,
				EssenceOn:       espost.EssenceOn,
				Type:            espost.Type,
				IsOriginal:      espost.IsOriginal,
				OriginalUrl:     espost.OriginalUrl,
				OriginalReprint: int32(espost.OriginalReprint),
				HotNum:          espost.HotNum,
				LatestRepliedOn: espost.LatestRepliedOn,
				CreatedOn:       espost.CreatedOn,
				ModifiedOn:      espost.ModifiedOn,
				GameId:          espost.GameId,
				AreaId:          espost.AreaId,
				Platform:        espost.Platform,
				ExtInfo:         espost.ExtInfo,
				PlateId:         espost.PlateID,
				Language:        espost.Language,
				IsAudit:         espost.IsAudit,
				IsDel:           espost.IsDel,
				PowerNum:        espost.PowerNum,
				RankInfo: &pb.CreatorHubActivityRank{
					Id:       espost.CreatorHubRankID,
					RankName: "",
				},
				TaskInfo: &pb.CreatorHubActivityInfo{
					Id:       0,
					TaskId:   espost.CreatorHubTaskID,
					TaskName: "",
				},
				PublishOn:  int32(espost.PublishOn),
				IsOfficial: espost.IsOfficial,
				DelReason:  espost.DelReason,
				DelType:    espost.DelType,
				IsHide:     espost.IsHide,
			}

			if espost.Type != constants.POST_TYPE_RICH_TEXT {
				item.ContentSummary = item.Content
			}

			wg.Add(1) // 增加 WaitGroup 的计数器
			go func(postItem *pb.GetPostRsp, lang string, index int) {
				defer recovery.CatchGoroutinePanic(context.Background())
				defer wg.Done() // 函数结束时减少计数器

				userInfo, err := user.GetUserDetailInfoByOpenid(c, intlOpenID, postItem.IntlOpenid, lang)
				if err != nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("postsFrom GetUserDetailInfoByOpenid err, intl_openid:(%s), err=(%v)", postItem.IntlOpenid, err)
				} else {
					postItem.User = userInfo

					// 获取登录用户是否是管理员
					isAdmin := formatted.GetUserAdmin(intlOpenID)
					postItem.CanDelete = intlOpenID == userInfo.IntlOpenid || isAdmin
					postItem.CanReport = intlOpenID != userInfo.IntlOpenid
					postItem.IsMine = intlOpenID == userInfo.IntlOpenid
				}
				tagInfos, err := getPostTagInfo(c, espost.PostUuid, lang)
				if err == nil && len(tagInfos) > 0 {
					postItem.Tags = tagInfos
				}

				if postItem.IsOfficial == 1 {
					postItem.CanReport = false
				}

				// 当前语言不符合需要的语言，查询多语言, 同时判读语言不为空，如果为空可能是cms超管查询
				if postItem.Language != lang && lang != "" {
					// postContent, err := dao.PostContentGet(postItem.PostUuid, language)
					postContent, err := GetPostContentInfo(c, postItem.PostUuid, lang)
					if err == nil {
						postItem.IsOriginal = postContent.IsOriginal
						postItem.OriginalUrl = postContent.OriginalURL
						postItem.OriginalReprint = postContent.OriginalReprint
						postItem.Platform = postContent.Platform
						postItem.Title = postContent.Title
						postItem.Content = postContent.Content
						postItem.ContentSummary = postContent.ContentSummary
						postItem.ExtInfo = postContent.ExtInfo
						postItem.PicUrls = strings.Split(postContent.PicUrls, ",")
					} else {
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostContentInfo | queryHot params: post_uuid: %s, err: %v", postItem.PostUuid, err)
					}
				}
				handlerPostImages(postItem)

				postFormattedChannel <- &PostFormattedChannel{
					PostInfo: postItem,
					Index:    index,
				}

			}(item, language, i) // 通过闭包传递参数

			//getPostListRsp.List = append(getPostListRsp.List, item)
		}

		// 等待所有 goroutine 完成
		go func() {
			defer recovery.CatchGoroutinePanic(context.Background())
			wg.Wait()
			close(postFormattedChannel)
		}()
		// 重新赋值,可能某些数据会被剔除
		var list = make([]*PostFormattedChannel, 0)
		for postFormatted := range postFormattedChannel {
			if postFormatted != nil {
				list = append(list, postFormatted)
			}
		}
		sort.Slice(list, func(i, j int) bool {
			return list[i].Index < list[j].Index
		})
		for _, item := range list {
			postFormatteds = append(postFormatteds, item.PostInfo)
		}
		//sort.Slice(postFormatteds, func(i, j int) bool {
		//	return postFormatteds[i].CreatedOn > postFormatteds[j].CreatedOn
		//})

		postFormattedsByte, err := json.Marshal(postFormatteds)
		if err == nil {
			redis.GetClient().SetEX(c, postDetailsRedisKey, string(postFormattedsByte), 2*time.Minute).Result()
			if intlOpenID != "" {
				searchKeysKey := cache.GetPostsListCacheKeyWithOpenid(intlOpenID)
				redis.GetClient().SAdd(c, searchKeysKey, postDetailsRedisKey)
			}
		}
	}

	getPostListRsp.List = postFormatteds

	if len(hits) > 0 {
		lastHit := hits[len(hits)-1]
		jsonData, err := json.Marshal(lastHit.Sort)
		if err != nil {
			log.Fatalf("Error marshaling to JSON: %s", err)
		}
		if int64(len(hits)) < req.Limit {
			getPostListRsp.PageInfo.IsFinish = true
		} else {
			nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "postsFrom | Failed to postsFrom EncryptPageCursorS")
			}
			getPostListRsp.PageInfo.NextPageCursor = nextPageCursor
		}

	} else {
		getPostListRsp.PageInfo.IsFinish = true
	}
	getPostListRsp.PageInfo.PreviousPageCursor = req.NextPageCursor
	// 获取统计数据
	postStatsList, err := dao.BatchGetPostStatsByPostUuids(postUUIDs)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("postsFrom err by get post stats list, postUUIDs:(%v), err=(%v)", postUUIDs, err)
	} else {
		for _, postItem := range getPostListRsp.List {
			if len(postStatsList) > 0 {
				for _, postStatsItem := range postStatsList {
					if postStatsItem.PostUUID == postItem.PostUuid {
						upvoteMap := make(map[int64]int64)
						if postStatsItem.UpvoteMap != "" {
							err := json.Unmarshal([]byte(postStatsItem.UpvoteMap), &upvoteMap)
							if err != nil {
								log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetIndexPosts UpvoteMap json.Unmarshal err: %v, postUUID is %s", err, postItem.PostUuid)
							}
						}
						postItem.PowerNum = postStatsItem.PowerNumFloat
						postItem.CommentCount = postStatsItem.CommentCount
						postItem.CollectionCount = postStatsItem.CollectionCount
						postItem.UpvoteCount = postStatsItem.UpvoteCount
						postItem.UpvoteMap = upvoteMap
						postItem.BrowseCount = postStatsItem.BrowseCount
						postItem.ForwardCount = int32(postStatsItem.ForwardCount)
					}
				}
			}
		}
	}
	return getPostListRsp, nil
}

// addLimit 加量查询的值，在返回用户数据的时候需要减去
func CmsPostsFrom(c context.Context, intlOpenID string, hits []*es7.SearchHit, req *pb.GetPostListReq, language string, addLimit int) (*pb.CMSGetPostListRsp, error) {
	getPostListRsp := &pb.CMSGetPostListRsp{
		List:     make([]*pb.CmsPostItem, 0),
		PageInfo: &pb.PageInfo{},
	}
	if len(hits) == 0 {
		getPostListRsp.PageInfo.IsFinish = true
		return getPostListRsp, nil
	}
	currentTime := time.Now().Unix()

	// 裁剪返回结果的数据，需要减去加量查询的值
	if len(hits) > int(req.Limit) {
		hits = hits[0:(len(hits) - addLimit)]
	}

	var postUUIDs []string
	for _, hit := range hits {
		espost := &model.ESPost{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, espost); err != nil {
			return nil, err
		}

		postUUIDs = append(postUUIDs, espost.PostUuid)
	}

	// 先获取缓存数据
	postFormatteds := make([]*pb.CmsPostItem, 0, len(hits))

	var wg sync.WaitGroup
	postFormattedChannel := make(chan *CmsPostFormattedChannel, 1)
	for i, hit := range hits {
		espost := &model.ESPost{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, espost); err != nil {
			return nil, err
		}

		if espost.EssenceOn < currentTime {
			espost.IsEssence = 0
		}
		if espost.TopOn < currentTime {
			espost.IsTop = 0
		}
		upvoteMap := make(map[int64]int64)
		if espost.UpvoteMap != "" {
			err := json.Unmarshal([]byte(espost.UpvoteMap), &upvoteMap)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("ES postsFrom UpvoteMap json.Unmarshal err: %v, postUUID is %s", err, espost.PostUuid)
			}
		}

		item := &pb.CmsPostItem{
			PostUuid:        espost.PostUuid,
			IntlOpenid:      espost.IntlOpenid,
			Title:           espost.Title,
			Content:         espost.Content,
			ContentSummary:  espost.ContentSummary,
			PicUrls:         strings.Split(espost.PicUrls, ","),
			CommentCount:    espost.CommentCount,
			CollectionCount: espost.CollectionCount,
			UpvoteCount:     espost.UpvoteCount,
			BrowseCount:     espost.BrowseCount,
			ForwardCount:    espost.ForwardCount,
			IsTop:           espost.IsTop,
			TopSort:         espost.TopSort,
			TopOn:           espost.TopOn,
			IsEssence:       espost.IsEssence,
			EssenceOn:       espost.EssenceOn,
			Type:            espost.Type,
			HotNum:          espost.HotNum,
			CreatedOn:       espost.CreatedOn,
			ModifiedOn:      espost.ModifiedOn,
			Platform:        espost.Platform,
			PlateId:         espost.PlateID,
			Language:        espost.Language,
			IsAudit:         espost.IsAudit,
			IsDel:           espost.IsDel,
			PowerNum:        espost.PowerNum,
			PublishOn:       int32(espost.PublishOn),
			IsOfficial:      espost.IsOfficial,
			DelReason:       espost.DelReason,
			DelType:         espost.DelType,
			IsHide:          espost.IsHide,
		}

		if espost.Type != constants.POST_TYPE_RICH_TEXT {
			item.ContentSummary = item.Content
		}

		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(postItem *pb.CmsPostItem, lang string, index int) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			userInfo, err := dao.GetUserByIntlOpenid(postItem.IntlOpenid)
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("postsFrom err, intl_openid:(%s), err=(%v)", postItem.IntlOpenid, err)
			} else {
				// 重置认证用户昵称个签多语言
				authUserName, authUserRemark, _ := user.GetUserCerificationUserLanguage(c, userInfo.IntlOpenid, language)
				if authUserName != "" {
					userInfo.Username = authUserName
				}
				if authUserRemark != "" {
					userInfo.Remark = authUserRemark
				}
				// 头像挂件信息， cms应该用不上
				//avatarPendant, _ := user.GetUserCurWearedAvatarPendantIcon(c, postItem.IntlOpenid)
				item.User = &pbUser.UserInfo{
					Username:   userInfo.Username,
					IntlOpenid: userInfo.IntlOpenid,
					Avatar:     userInfo.Avatar,
					Mood:       userInfo.Mood,
					GameTag:    userInfo.GameTag,
					//AvatarPendant: avatarPendant,
				}
			}
			tagInfos, err := getPostTagInfo(c, espost.PostUuid, lang)
			if err == nil && len(tagInfos) > 0 {
				postItem.Tags = tagInfos
			}

			// 当前语言不符合需要的语言，查询多语言, 同时判读语言不为空，如果为空可能是cms超管查询
			if postItem.Language != language && language != "" {
				postContent, err := dao.PostContentGet(postItem.PostUuid, language)
				if err == nil {
					postItem.Platform = postContent.Platform
					postItem.Title = postContent.Title
					postItem.Content = postContent.Content
					postItem.ContentSummary = postContent.ContentSummary
					postItem.PicUrls = strings.Split(postContent.PicUrls, ",")
				}
			} else {
				postContents, err := dao.GetPostContentList(postItem.PostUuid, true)
				if err == nil && len(postContents) > 0 {
					item.RiskRemindType = int32(postContents[0].RiskRemindType)
					item.AiContentType = int32(postContents[0].AiContentType)
					item.CreatorStatementType = int32(postContents[0].CreatorStatementType)
				}
			}
			handlerCmsPostImages(postItem)

			postFormattedChannel <- &CmsPostFormattedChannel{
				PostInfo: postItem,
				Index:    index,
			}

		}(item, language, i) // 通过闭包传递参数

		//getPostListRsp.List = append(getPostListRsp.List, item)
	}

	// 等待所有 goroutine 完成
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		wg.Wait()
		close(postFormattedChannel)
	}()
	// 重新赋值,可能某些数据会被剔除
	var list = make([]*CmsPostFormattedChannel, 0)
	for postFormatted := range postFormattedChannel {
		if postFormatted != nil {
			list = append(list, postFormatted)
		}
	}
	sort.Slice(list, func(i, j int) bool {
		return list[i].Index < list[j].Index
	})
	for _, item := range list {
		postFormatteds = append(postFormatteds, item.PostInfo)
	}

	getPostListRsp.List = postFormatteds

	if len(hits) > 0 {
		lastHit := hits[len(hits)-1]
		jsonData, err := json.Marshal(lastHit.Sort)
		if err != nil {
			log.Fatalf("Error marshaling to JSON: %s", err)
		}
		if int64(len(hits)) < req.Limit {
			getPostListRsp.PageInfo.IsFinish = true
		} else {
			nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "postsFrom | Failed to postsFrom EncryptPageCursorS")
			}
			getPostListRsp.PageInfo.NextPageCursor = nextPageCursor
		}

	} else {
		getPostListRsp.PageInfo.IsFinish = true
	}
	getPostListRsp.PageInfo.PreviousPageCursor = req.NextPageCursor
	// 获取统计数据
	postStatsList, err := dao.BatchGetPostStatsByPostUuids(postUUIDs)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("postsFrom err by get post stats list, postUUIDs:(%v), err=(%v)", postUUIDs, err)
	} else {
		for _, postItem := range getPostListRsp.List {
			if len(postStatsList) > 0 {
				for _, postStatsItem := range postStatsList {
					if postStatsItem.PostUUID == postItem.PostUuid {
						upvoteMap := make(map[int64]int64)
						if postStatsItem.UpvoteMap != "" {
							err := json.Unmarshal([]byte(postStatsItem.UpvoteMap), &upvoteMap)
							if err != nil {
								log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetIndexPosts UpvoteMap json.Unmarshal err: %v, postUUID is %s", err, postItem.PostUuid)
							}
						}
						postItem.PowerNum = postStatsItem.PowerNumFloat
						postItem.CommentCount = postStatsItem.CommentCount
						postItem.CollectionCount = postStatsItem.CollectionCount
						postItem.UpvoteCount = postStatsItem.UpvoteCount
						postItem.BrowseCount = postStatsItem.BrowseCount
						postItem.ForwardCount = int32(postStatsItem.ForwardCount)
					}
				}
			}
		}
	}
	// 获取待审核中的数据
	postAuditList, err := dao.GetPostEditNotAuditListByPostUUIDs(postUUIDs)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CmsPostsFrom GetPostEditNotAuditListByPostUUIDs err , postUUIDs:(%v), err=(%v)", postUUIDs, err)
	} else {
		for _, postItem := range getPostListRsp.List {
			if len(postAuditList) > 0 {
				for _, postAuditItem := range postAuditList {
					if postAuditItem.PostUUID == postItem.PostUuid {
						postItem.Type = postAuditItem.Type
						postItem.Platform = postAuditItem.Platform
						postItem.Title = postAuditItem.Title
						postItem.Content = postAuditItem.Content
						postItem.ContentSummary = postAuditItem.ContentSummary
						if len(postAuditItem.PicUrls) > 0 {
							postItem.PicUrls = strings.Split(postAuditItem.PicUrls, ",")
						}
					}
				}
			}
		}
	}
	return getPostListRsp, nil
}

// addLimit 加量查询的值，在返回用户数据的时候需要减去
func postsAuditFrom(c context.Context, intlOpenID string, resp *es7.SearchResult, req *pb.CMSGetPostAuditListReq, language string, addLimit int) (*pb.CMSGetPostAuditListRsp, error) {
	getPostListRsp := &pb.CMSGetPostAuditListRsp{
		List:     make([]*pb.GetPostAuditForCMSRsp, 0),
		PageInfo: &pb.PageInfo{},
	}
	if resp == nil || resp.Hits == nil || len(resp.Hits.Hits) == 0 {
		getPostListRsp.PageInfo.IsFinish = true
		return getPostListRsp, nil
	}

	// 裁剪返回结果的数据，需要减去加量查询的值
	hits := resp.Hits.Hits
	if len(resp.Hits.Hits) > int(req.Limit) {
		hits = hits[0:(len(hits) - addLimit)]
	}

	var wg sync.WaitGroup
	var postUuids []string
	for _, hit := range hits {
		espost := &model.ESPostAudit{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, espost); err != nil {
			return nil, err
		}
		postUuids = append(postUuids, espost.PostUuid)
		item := &pb.GetPostRsp{
			PostUuid:       espost.PostUuid,
			IntlOpenid:     espost.IntlOpenid,
			PlateId:        int32(espost.PlateID),
			Title:          espost.Title,
			Content:        espost.Content,
			ContentSummary: espost.ContentSummary,
			PicUrls:        strings.Split(espost.PicUrls, ","),
			Type:           espost.Type,
			CreatedOn:      espost.CreatedOn,
			ModifiedOn:     espost.ModifiedOn,
			GameId:         espost.GameId,
			AreaId:         espost.AreaId,
			Platform:       espost.Platform,
			ExtInfo:        espost.ExtInfo,
			Language:       espost.Language,
			IsDel:          espost.IsDel,
		}
		auditInfo := &pb.PostAuditInfo{
			Id:               espost.ID,
			TextRiskLevel:    espost.TextRiskLevel,
			TextRiskType:     espost.TextRiskType,
			PicRiskLevel:     espost.PicRiskLevel,
			PicRiskType:      espost.PicRiskType,
			Status:           espost.Status,
			AuditUser:        espost.AuditUser,
			AuditIntroduce:   espost.AuditIntroduce,
			AuditOn:          espost.AuditOn,
			MachineStatus:    espost.MachineStatus,
			ArtificialStatus: espost.ArtificialStatus,
		}

		getPostAuditForCMSRsp := &pb.GetPostAuditForCMSRsp{
			PostInfo:  item,
			AuditInfo: auditInfo,
		}

		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(postItem *pb.GetPostRsp, lang string) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			userInfo, err := dao.GetUserByIntlOpenid(postItem.IntlOpenid)
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("postsAuditFrom err, intl_openid:(%s), err=(%v)", postItem.IntlOpenid, err)
			} else {
				// 重置认证用户昵称个签多语言
				authUserName, authUserRemark, _ := user.GetUserCerificationUserLanguage(c, userInfo.IntlOpenid, language)
				if authUserName != "" {
					userInfo.Username = authUserName
				}
				if authUserRemark != "" {
					userInfo.Remark = authUserRemark
				}
				// 头像挂件信息
				avatarPendant, _ := user.GetUserCurWearedAvatarPendantIcon(c, postItem.IntlOpenid)
				// 获取用户是否禁言
				myIsMute := formatted.GetUserMute(item.IntlOpenid)
				item.User = &pbUser.UserInfo{
					Username:      userInfo.Username,
					IntlOpenid:    userInfo.IntlOpenid,
					Avatar:        userInfo.Avatar,
					Mood:          userInfo.Mood,
					IsMute:        myIsMute,
					AvatarPendant: avatarPendant,
				}
				// 获取用户管理员、认证、禁言标识
				authType := formatted.GetUserAuth(postItem.IntlOpenid)
				isMute := formatted.GetUserMute(postItem.IntlOpenid)
				// 获取登录用户是否是管理员
				isAdmin := formatted.GetUserAdmin(intlOpenID)
				item.User.IsAdmin = isAdmin
				item.User.AuthType = authType
				item.User.IsMute = isMute
				item.CanDelete = intlOpenID == userInfo.IntlOpenid || isAdmin
				item.CanReport = intlOpenID != userInfo.IntlOpenid
				item.IsMine = intlOpenID == userInfo.IntlOpenid
			}
			tagInfos, err := getPostTagInfo(c, espost.PostUuid, lang)
			if err == nil && len(tagInfos) > 0 {
				postItem.Tags = tagInfos
			}
			// post content 字段不作为搜索，不用放入ES
			postContents, err := dao.GetPostContentList(espost.PostUuid, true)
			if err == nil && len(postContents) > 0 {
				item.RiskRemindType = int32(postContents[0].RiskRemindType)
				item.AiContentType = int32(postContents[0].AiContentType)
				item.CreatorStatementType = int32(postContents[0].CreatorStatementType)
			}
		}(item, language) // 通过闭包传递参数

		getPostListRsp.List = append(getPostListRsp.List, getPostAuditForCMSRsp)
	}
	// 等待所有 goroutine 完成
	wg.Wait()

	// 获取帖子信息
	postList, err := dao.GetPostList(&dao.PostConditions{PostUuids: postUuids}, 0, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("postsAuditFrom | get post failed, post_uuids: %v, err: %s", postUuids, err)
		return nil, errs.NewCustomError(c, code.GetPostsFailed, "postsAuditFrom | Failed to get posts")
	}

	for _, getPostAuditForCMSItem := range getPostListRsp.List {
		for _, postItem := range postList {
			if postItem.PostUUID == getPostAuditForCMSItem.PostInfo.PostUuid {
				getPostAuditForCMSItem.PostInfo.PlateId = postItem.PlateID
				getPostAuditForCMSItem.PostInfo.IsAudit = postItem.IsAudit
			}
		}
	}
	if len(resp.Hits.Hits) > 0 {
		jsonData, err := json.Marshal(resp.Hits.Hits[len(resp.Hits.Hits)-1].Sort)
		if err != nil {
			log.Fatalf("Error marshaling to JSON: %s", err)
		}
		if len(resp.Hits.Hits) == 0 || int64(len(resp.Hits.Hits)) < req.Limit {
			getPostListRsp.PageInfo.IsFinish = true
		} else {
			nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to postsFrom EncryptPageCursorS")
			}
			getPostListRsp.PageInfo.NextPageCursor = nextPageCursor
		}
	} else {
		getPostListRsp.PageInfo.IsFinish = true
	}
	getPostListRsp.PageInfo.PreviousPageCursor = req.NextPageCursor
	return getPostListRsp, nil
}

// 参考：https://docs.elasticsearch.dev/api-es-compatible/search/types/#prefix
func CMSGetReportInfoList(c context.Context, req *pb.CMSGetReportListReq, language, gameId, areaId string) (*pb.CMSGetReportListRsp, error) {
	boolQuery := es7.NewBoolQuery()
	//gameidQuery := es7.NewTermQuery("game_id", gameId)
	//areaidQuery := es7.NewTermQuery("area_id", areaId)
	//boolQuery.Must(gameidQuery, areaidQuery)
	if req.ReportType > 0 {
		itemQuery := es7.NewTermQuery("report_type", req.ReportType)
		boolQuery.Must(itemQuery)
	}
	if req.ContentType > 0 {
		itemQuery := es7.NewTermQuery("content_type", req.ContentType)
		boolQuery.Must(itemQuery)
	}
	if req.ContentUuid != "" {
		itemQuery := es7.NewTermQuery("content_uuid", req.ContentUuid)
		boolQuery.Must(itemQuery)
	}
	if req.Status == 1 {
		// 【待审核tab】 不显示已删除的记录数据
		boolQuery.Must(es7.NewTermQuery("is_del", 0))
	}
	if req.Status > 0 {
		itemQuery := es7.NewTermQuery("status", req.Status)
		boolQuery.Must(itemQuery)
	}
	if req.TitleKeyword != "" {
		// itemQuery := es7.NewMatchPhraseQuery("title", req.TitleKeyword)
		itemQuery := es7.NewWildcardQuery("title", "*"+req.TitleKeyword+"*")
		boolQuery.Must(itemQuery)
	}
	if req.ContentKeyword != "" {
		// itemQuery := es7.NewMatchPhraseQuery("content", req.ContentKeyword)
		itemQuery := es7.NewWildcardQuery("content", "*"+req.ContentKeyword+"*")
		boolQuery.Must(itemQuery)
	}
	if req.Reason != "" {
		// itemQuery := es7.NewMatchPhraseQuery("reason", req.Reason)
		itemQuery := es7.NewWildcardQuery("reason", "*"+req.Reason+"*")
		boolQuery.Must(itemQuery)
	}
	if req.ReportIntlOpenid != "" {
		itemQuery := es7.NewTermQuery("report_intl_openid", req.ReportIntlOpenid)
		boolQuery.Must(itemQuery)
	}
	if req.ReportedIntlOpenid != "" {
		itemQuery := es7.NewTermQuery("reported_intl_openid", req.ReportedIntlOpenid)
		boolQuery.Must(itemQuery)
	}
	if req.ReportIntlUserOpenid != "" {
		if strings.Contains(req.ReportIntlUserOpenid, "-") {
			// 如果传递进来的用户id包含了区服信息的，需要使用另外一个字段
			itemQuery := es7.NewTermQuery("report_intl_openid", req.ReportIntlUserOpenid)
			boolQuery.Must(itemQuery)
		} else {
			itemQuery := es7.NewTermQuery("report_intl_user_openid", req.ReportIntlUserOpenid)
			boolQuery.Must(itemQuery)
		}
	}
	if req.ReportedIntlUserOpenid != "" {
		if strings.Contains(req.ReportIntlUserOpenid, "-") {
			itemQuery := es7.NewTermQuery("reported_intl_openid", req.ReportedIntlUserOpenid)
			boolQuery.Must(itemQuery)
		} else {
			itemQuery := es7.NewTermQuery("reported_intl_user_openid", req.ReportedIntlUserOpenid)
			boolQuery.Must(itemQuery)
		}
	}
	if language != "" {
		itemQuery := es7.NewTermQuery("language", language)
		boolQuery.Must(itemQuery)
	}
	var sortBys []es7.Sorter
	sortQuery := es7.NewFieldSort("created_on").Desc()
	sortByIdQuery := es7.NewFieldSort("id").Desc()
	sortBys = append(sortBys, sortQuery, sortByIdQuery)
	var lastSortValue []interface{}
	if req.NextPageCursor != "" {
		cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
		if err != nil {
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
		}
		err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "queryByContent2 | Failed to get idCursor")
		}
	}
	// 查询数量加1，避免最后一页和分页数相同
	addLimit := 1
	limit := req.Limit + int64(addLimit)
	resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.ReportIndex, boolQuery, sortBys, lastSortValue, limit)
	if err != nil {
		return nil, err
	}
	getPostReportListRsp, err := reportInfoFormate(c, resp, req, language, addLimit)
	if err != nil {
		return nil, err
	}
	return getPostReportListRsp, nil
}

// addLimit 加量查询的值，在返回用户数据的时候需要减去
func reportInfoFormate(c context.Context, resp *es7.SearchResult, req *pb.CMSGetReportListReq, language string, addLimit int) (*pb.CMSGetReportListRsp, error) {
	getPostListRsp := &pb.CMSGetReportListRsp{
		List:     make([]*pb.CMSGetReportRsp, 0),
		PageInfo: &pb.PageInfo{},
	}
	if resp == nil || resp.Hits == nil || len(resp.Hits.Hits) == 0 {
		getPostListRsp.PageInfo.IsFinish = true
		return getPostListRsp, nil
	}
	hits := resp.Hits.Hits
	// 裁剪返回结果的数据，需要减去加量查询的值
	if len(resp.Hits.Hits) > int(req.Limit) {
		hits = hits[0:(len(hits) - addLimit)]
	}

	var wg sync.WaitGroup
	for _, hit := range hits {
		espost := &model.ESReportInfo{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, espost); err != nil {
			return nil, err
		}

		item := &pb.CMSGetReportRsp{
			ReportType:         espost.ReportType,
			ContentUuid:        espost.ContentUuid,
			ContentType:        espost.ContentType,
			SubContentType:     espost.SubContentType,
			IntlOpenid:         espost.ReportedIntlOpenid,
			Title:              espost.Title,
			Content:            espost.Content,
			PicUrls:            espost.PicUrls,
			CreatedOn:          espost.CreatedOn,
			ModifiedOn:         espost.ModifiedOn,
			GameId:             espost.GameId,
			AreaId:             espost.AreaId,
			Platform:           espost.Platform,
			ExtInfo:            espost.ExtInfo,
			Reason:             espost.Reason,
			ReportIntlOpenid:   espost.ReportIntlOpenid,
			ReportedIntlOpenid: espost.ReportedIntlOpenid,
			Status:             espost.Status,
			UpdateUser:         espost.UpdateUser,
			Language:           espost.Language,
		}

		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(postItem *pb.CMSGetReportRsp, esitem *model.ESReportInfo, lang string) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			userInfo, err := dao.GetUserByIntlOpenid(postItem.IntlOpenid)
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("postsFrom err, intl_openid:(%s), err=(%v)", postItem.IntlOpenid, err)
			} else {
				// 重置认证用户昵称个签多语言
				authUserName, authUserRemark, _ := user.GetUserCerificationUserLanguage(c, userInfo.IntlOpenid, language)
				if authUserName != "" {
					userInfo.Username = authUserName
				}
				if authUserRemark != "" {
					userInfo.Remark = authUserRemark
				}
				item.User = &pbUser.UserInfo{
					Username:   userInfo.Username,
					IntlOpenid: userInfo.IntlOpenid,
					Avatar:     userInfo.Avatar,
					Mood:       userInfo.Mood,
				}
				// 获取用户管理员、认证、禁言标识
				//isAdmin, authType, isMute := userService.GetUserAuth(postItem.IntlOpenid)
				//item.User.IsAdmin = isAdmin
				//item.User.AuthType = authType
				//item.User.IsMute = isMute
			}
			if postItem.ContentType == 1 {
				tagInfos, err := getPostTagInfo(c, postItem.ContentUuid, lang)
				if err == nil && len(tagInfos) > 0 {
					postItem.Tags = tagInfos
				}
			}
		}(item, espost, language) // 通过闭包传递参数

		getPostListRsp.List = append(getPostListRsp.List, item)
	}

	// 等待所有 goroutine 完成
	wg.Wait()
	if len(resp.Hits.Hits) > 0 {
		jsonData, err := json.Marshal(resp.Hits.Hits[len(resp.Hits.Hits)-1].Sort)
		if err != nil {
			log.Fatalf("Error marshaling to JSON: %s", err)
		}
		if len(resp.Hits.Hits) == 0 || int64(len(resp.Hits.Hits)) < req.Limit {
			getPostListRsp.PageInfo.IsFinish = true
		} else {
			nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to postsFrom EncryptPageCursorS")
			}
			getPostListRsp.PageInfo.NextPageCursor = nextPageCursor
		}
	} else {
		getPostListRsp.PageInfo.IsFinish = true
	}
	getPostListRsp.PageInfo.PreviousPageCursor = req.NextPageCursor
	return getPostListRsp, nil
}

// 参考：https://docs.elasticsearch.dev/api-es-compatible/search/types/#prefix
func CMSGetPostCommentAuditList(c context.Context, req *pb.CMSGetPostCommentAuditListReq, language, gameId, areaId string) (*pb.CMSGetPostCommentAuditListRsp, error) {
	boolQuery := es7.NewBoolQuery()
	//gameidQuery := es7.NewTermQuery("game_id", gameId)
	//areaidQuery := es7.NewTermQuery("area_id", areaId)
	//boolQuery.Must(gameidQuery, areaidQuery)
	if req.IsDel > 0 {
		itemQuery := es7.NewTermQuery("is_del", req.IsDel)
		boolQuery.Must(itemQuery)
	} else {
		itemQuery := es7.NewTermQuery("is_del", 0)
		boolQuery.Must(itemQuery)
	}
	if req.TextRiskLevel > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		textRiskLevel := req.TextRiskLevel - 1
		if textRiskLevel < 0 {
			textRiskLevel = 0
		}
		itemQuery := es7.NewTermQuery("text_risk_level", textRiskLevel)
		boolQuery.Must(itemQuery)
	}
	if req.TextRiskType > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		textRiskType := req.TextRiskType - 1
		if textRiskType < 0 {
			textRiskType = 0
		}
		itemQuery := es7.NewTermQuery("text_risk_type", textRiskType)
		boolQuery.Must(itemQuery)
	}
	if req.PicRiskLevel > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		picRiskLevel := req.PicRiskLevel - 1
		if picRiskLevel < 0 {
			picRiskLevel = 0
		}
		itemQuery := es7.NewTermQuery("pic_risk_level", picRiskLevel)
		boolQuery.Must(itemQuery)
	}
	if req.PicRiskType > 0 {
		// 这个文本分享因为需要筛选0值，所以需要B端+1， 这里减1
		picRiskType := req.PicRiskType - 1
		if picRiskType < 0 {
			picRiskType = 0
		}
		itemQuery := es7.NewTermQuery("pic_risk_type", picRiskType)
		boolQuery.Must(itemQuery)
	}
	if req.Status > 0 {
		itemQuery := es7.NewTermQuery("status", req.Status)
		boolQuery.Must(itemQuery)
	}
	if req.Keyword != "" {
		// itemQuery := es7.NewMatchPhraseQuery("content", req.Keyword)
		itemQuery := es7.NewWildcardQuery("content", "*"+req.Keyword+"*")
		boolQuery.Must(itemQuery)
	}
	if req.IntlOpenid != "" {
		itemQuery := es7.NewTermQuery("intl_openid", req.IntlOpenid)
		boolQuery.Must(itemQuery)
	}
	if req.IntlUserOpenid != "" {
		if strings.Contains(req.IntlUserOpenid, "-") {
			itemQuery := es7.NewTermQuery("intl_openid", req.IntlUserOpenid)
			boolQuery.Must(itemQuery)
		} else {
			itemQuery := es7.NewTermQuery("intl_user_openid", req.IntlUserOpenid)
			boolQuery.Must(itemQuery)
		}
	}
	if req.StartTime > 0 {
		itemQuery := es7.NewRangeQuery("created_on").Gte(req.StartTime)
		boolQuery.Filter(itemQuery)
	}
	if req.EndTime > 0 {
		itemQuery := es7.NewRangeQuery("created_on").Lte(req.EndTime)
		boolQuery.Filter(itemQuery)
	}
	// 筛选语言
	if req.Language != "" {
		itemQuery := es7.NewTermQuery("language", req.Language)
		boolQuery.Must(itemQuery)
	}
	// 机审状态查询
	if req.MachineStatus > 0 {
		// b端加1
		machineStatus := req.MachineStatus - 1
		if machineStatus <= 0 {
			machineStatus = 0
		}
		//switch machineStatus {
		//case 0:
		//	// 待机审
		//	itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		//	auditStatusQuery := es7.NewTermQuery("status", 1)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//	break
		//case 1:
		//	// 机身通过
		//	itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		//	auditStatusQuery := es7.NewTermQuery("status", 2)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//case 2:
		//	// 机审异常
		//	itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		//	auditStatusQuery := es7.NewTermQuery("status", 1)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//}
		// 产品需要单个字段可以单独筛选
		itemQuery := es7.NewTermQuery("machine_status", machineStatus)
		boolQuery.Must(itemQuery)
	}
	// 人审状态查询
	if req.ArtificialStatus > 0 {
		// b端加1
		artificialStatus := req.ArtificialStatus - 1
		if artificialStatus <= 0 {
			artificialStatus = 0
		}
		//switch artificialStatus {
		//case 0:
		//	// 待人审
		//	machineQuery := es7.NewTermQuery("machine_status", 2)
		//	auditStatusQuery := es7.NewTermQuery("status", 1)
		//	boolQuery.Must(machineQuery, auditStatusQuery)
		//	break
		//case 1:
		//	// 人审通过
		//	itemQuery := es7.NewTermQuery("artificial_status", artificialStatus)
		//	auditStatusQuery := es7.NewTermQuery("status", 2)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//case 2:
		//	// 人审拒绝
		//	itemQuery := es7.NewTermQuery("artificial_status", artificialStatus)
		//	auditStatusQuery := es7.NewTermQuery("status", 3)
		//	boolQuery.Must(itemQuery, auditStatusQuery)
		//}
		// 产品需要单个字段可以单独筛选
		itemQuery := es7.NewTermQuery("artificial_status", artificialStatus)
		boolQuery.Must(itemQuery)
	}
	// 内容状态查询
	if req.ContentStatus > 0 {
		var itemQuery *es7.TermQuery
		switch constants.PostContentStatus(req.ContentStatus) {
		case constants.NormalPost:
			itemQuery = es7.NewTermQuery("status", 2)
			break
		case constants.SandboxPost:
			itemQuery = es7.NewTermQuery("status", 1)
			break
		case constants.UserDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.UserDeleted)
			break
		case constants.CAdminDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.CAdminDeleted)
			break
		case constants.BReviewDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.BReviewDeleted)
			break
		case constants.BAdminDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.BAdminDeleted)
		case constants.BReportDeletedPost:
			itemQuery = es7.NewTermQuery("del_type", constants.ReportBAdminDeleted)
			break
		default:
			return nil, errs.NewCustomError(c, code.InvalidParamsErr, "content status enum is required")
		}
		boolQuery.Must(itemQuery)
	}
	var sortBys []es7.Sorter
	if req.Status == 0 {
		// 【全部tab】 按照操作时间倒序
		sortBys = append(sortBys, es7.NewFieldSort("audit_on").Desc())
	}
	sortBys = append(sortBys, es7.NewFieldSort("status").Asc(),
		es7.NewFieldSort("id").Desc())
	var lastSortValue []interface{}
	if req.NextPageCursor != "" {
		cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
		if err != nil {
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
		}
		err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "queryByContent2 | Failed to get idCursor")
		}
	}
	// 查询数量加1，避免最后一页和分页数相同
	addLimit := 1
	limit := req.Limit + int64(addLimit)

	resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, boolQuery, sortBys, lastSortValue, limit)
	if err != nil {
		return nil, err
	}
	getPostAuditListRsp, err := commentAuditFrom(c, resp, req, addLimit)
	if err != nil {
		return nil, err
	}
	return getPostAuditListRsp, nil
}

// addLimit 加量查询的值，在返回用户数据的时候需要减去
func commentAuditFrom(c context.Context, resp *es7.SearchResult, req *pb.CMSGetPostCommentAuditListReq, addLimit int) (*pb.CMSGetPostCommentAuditListRsp, error) {
	getCommentAuditListRsp := &pb.CMSGetPostCommentAuditListRsp{
		List:     make([]*pb.GetPostCommentAuditForCMSRsp, 0),
		PageInfo: &pb.PageInfo{},
	}
	if resp == nil || resp.Hits == nil || len(resp.Hits.Hits) == 0 {
		getCommentAuditListRsp.PageInfo.IsFinish = true
		return getCommentAuditListRsp, nil
	}
	// 裁剪返回结果的数据，需要减去加量查询的值
	hits := resp.Hits.Hits
	// 裁剪返回结果的数据，需要减去加量查询的值
	if len(resp.Hits.Hits) > int(req.Limit) {
		hits = hits[0:(len(hits) - addLimit)]
	}

	var wg sync.WaitGroup
	for _, hit := range hits {
		esComment := &model.ESComment{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, esComment); err != nil {
			return nil, err
		}

		item := &pb.UserCommentItem{
			CommentUuid: esComment.CommentUuid,
			PostUuid:    esComment.PostUuid,
			IntlOpenid:  esComment.IntlOpenid,
			Type:        esComment.Type,
			Title:       esComment.Title,
			Content:     esComment.Content,
			PicUrls:     strings.Split(esComment.PicUrls, ","),
			CreatedOn:   esComment.CreatedOn,
			ModifiedOn:  esComment.ModifiedOn,
			GameId:      esComment.GameId,
			AreaId:      esComment.AreaId,
			IsDel:       esComment.IsDel,
			Language:    esComment.Language,
			DelReason:   esComment.DelReason,
			DelType:     esComment.DelType,
		}
		auditInfo := &pb.PostAuditInfo{
			TextRiskLevel:    esComment.TextRiskLevel,
			TextRiskType:     esComment.TextRiskType,
			PicRiskLevel:     esComment.PicRiskLevel,
			PicRiskType:      esComment.PicRiskType,
			Status:           esComment.Status,
			AuditUser:        esComment.AuditUser,
			AuditIntroduce:   esComment.AuditIntroduce,
			AuditOn:          esComment.AuditOn,
			MachineStatus:    esComment.MachineStatus,
			ArtificialStatus: esComment.ArtificialStatus,
		}
		getPostCommentAuditForCMSRsp := &pb.GetPostCommentAuditForCMSRsp{
			CommentItem: item,
			AuditInfo:   auditInfo,
		}

		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(commentItem *pb.UserCommentItem) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			userInfo, err := dao.GetUserByIntlOpenid(commentItem.IntlOpenid)
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("postsFrom err, intl_openid:(%s), err=(%v)", commentItem.IntlOpenid, err)
			} else {
				// 获取用户是否禁言
				isMute := formatted.GetUserMute(item.IntlOpenid)
				item.User = &pbUser.UserInfo{
					Username:   userInfo.Username,
					IntlOpenid: userInfo.IntlOpenid,
					Avatar:     userInfo.Avatar,
					Mood:       userInfo.Mood,
					IsMute:     isMute,
				}
			}

			// 查询动态
			postInfo, err := dao.GetPostNoIgnoreDel(commentItem.PostUuid)
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostNoIgnoreDel err, postuuid:(%s), err=(%v)", commentItem.PostUuid, err)
			} else {
				item.PostDel = int32(postInfo.IsDel)
			}
		}(item) // 通过闭包传递参数

		getCommentAuditListRsp.List = append(getCommentAuditListRsp.List, getPostCommentAuditForCMSRsp)
	}

	// 等待所有 goroutine 完成
	wg.Wait()
	if len(resp.Hits.Hits) > 0 {
		jsonData, err := json.Marshal(resp.Hits.Hits[len(resp.Hits.Hits)-1].Sort)
		if err != nil {
			log.Fatalf("Error marshaling to JSON: %s", err)
		}
		if len(resp.Hits.Hits) == 0 || int64(len(resp.Hits.Hits)) < req.Limit {
			getCommentAuditListRsp.PageInfo.IsFinish = true
		} else {
			nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to postsFrom EncryptPageCursorS")
			}
			getCommentAuditListRsp.PageInfo.NextPageCursor = nextPageCursor
		}
	} else {
		getCommentAuditListRsp.PageInfo.IsFinish = true
	}
	getCommentAuditListRsp.PageInfo.PreviousPageCursor = req.NextPageCursor
	return getCommentAuditListRsp, nil
}

// addLimit 加量查询的值，在返回用户数据的时候需要减去
// 从es中查询数据使用到同步接口中
func postsFromBySync(c context.Context, hits []*es7.SearchHit, limit int) (*pb.GetPostListRsp, error) {
	getPostListRsp := &pb.GetPostListRsp{
		List:     make([]*pb.GetPostRsp, 0),
		PageInfo: &pb.PageInfo{},
	}
	if len(hits) == 0 {
		getPostListRsp.PageInfo.IsFinish = true
		return getPostListRsp, nil
	}
	currentTime := time.Now().Unix()

	var postUUIDsStr string
	var postUUIDs []string
	for _, hit := range hits {
		espost := &model.ESPost{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, espost); err != nil {
			return nil, err
		}
		postUUIDsStr = fmt.Sprintf("%s_%s", postUUIDsStr, espost.PostUuid)
		postUUIDs = append(postUUIDs, espost.PostUuid)
	}

	// 先获取缓存数据
	postFormatteds := make([]*pb.GetPostRsp, 0, len(hits))

	for _, hit := range hits {
		espost := &model.ESPost{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, espost); err != nil {
			return nil, err
		}

		if espost.EssenceOn < currentTime {
			espost.IsEssence = 0
		}
		if espost.TopOn < currentTime {
			espost.IsTop = 0
		}
		upvoteMap := make(map[int64]int64)
		if espost.UpvoteMap != "" {
			err := json.Unmarshal([]byte(espost.UpvoteMap), &upvoteMap)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("ES postsFrom UpvoteMap json.Unmarshal err: %v, postUUID is %s", err, espost.PostUuid)
			}
		}

		item := &pb.GetPostRsp{
			PostUuid:        espost.PostUuid,
			IntlOpenid:      espost.IntlOpenid,
			Title:           espost.Title,
			Content:         espost.Content,
			ContentSummary:  espost.ContentSummary,
			PicUrls:         strings.Split(espost.PicUrls, ","),
			CommentCount:    espost.CommentCount,
			CollectionCount: espost.CollectionCount,
			UpvoteCount:     espost.UpvoteCount,
			UpvoteMap:       upvoteMap,
			BrowseCount:     espost.BrowseCount,
			ForwardCount:    espost.ForwardCount,
			IsTop:           espost.IsTop,
			TopSort:         espost.TopSort,
			TopOn:           espost.TopOn,
			IsEssence:       espost.IsEssence,
			EssenceOn:       espost.EssenceOn,
			Type:            espost.Type,
			IsOriginal:      espost.IsOriginal,
			OriginalUrl:     espost.OriginalUrl,
			OriginalReprint: int32(espost.OriginalReprint),
			HotNum:          espost.HotNum,
			LatestRepliedOn: espost.LatestRepliedOn,
			CreatedOn:       espost.CreatedOn,
			ModifiedOn:      espost.ModifiedOn,
			GameId:          espost.GameId,
			AreaId:          espost.AreaId,
			Platform:        espost.Platform,
			ExtInfo:         espost.ExtInfo,
			PlateId:         espost.PlateID,
			Language:        espost.Language,
			IsAudit:         espost.IsAudit,
			IsDel:           espost.IsDel,
			PowerNum:        espost.PowerNum,
			RankInfo: &pb.CreatorHubActivityRank{
				Id:       espost.CreatorHubRankID,
				RankName: "",
			},
			TaskInfo: &pb.CreatorHubActivityInfo{
				Id:       0,
				TaskId:   espost.CreatorHubTaskID,
				TaskName: "",
			},
			PublishOn:  int32(espost.PublishOn),
			IsOfficial: espost.IsOfficial,
			DelReason:  espost.DelReason,
			DelType:    espost.DelType,
		}

		if espost.Type != constants.POST_TYPE_RICH_TEXT {
			item.ContentSummary = item.Content
		}
		postFormatteds = append(postFormatteds, item)
	}

	getPostListRsp.List = postFormatteds

	if len(hits) > 0 {
		lastHit := hits[len(hits)-1]
		jsonData, err := json.Marshal(lastHit.Sort)
		if err != nil {
			log.Fatalf("Error marshaling to JSON: %s", err)
		}
		if len(hits) < limit {
			getPostListRsp.PageInfo.IsFinish = true
		} else {
			nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "postsFrom | Failed to postsFrom EncryptPageCursorS")
			}
			getPostListRsp.PageInfo.NextPageCursor = nextPageCursor
		}

	} else {
		getPostListRsp.PageInfo.IsFinish = true
	}
	return getPostListRsp, nil
}

// QueryGuildHotPostInfo 根据公会id查询热度最高的帖子
func QueryGuildHotPostInfo(c context.Context, intlOpenID, language, guildId string) (*pb.GetGuildHotPostRsp, error) {
	rsp := &pb.GetGuildHotPostRsp{}
	// 先获取缓存数据
	var hits []*es7.SearchHit
	guildHotPostKey := cache.GuildHotPostCacheKey(guildId)
	guildHotPostCacheInfo, err := redis.GetClient().Get(c, guildHotPostKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(guildHotPostCacheInfo), &hits)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("QueryGuildHotPostInfo cache json.Unmarshal error.guildHotPostKey: %s, err: %v", guildHotPostKey, err)
			return nil, errs.NewCustomError(c, code.GetGuildHotPostsJsonUnmarshalError, "Failed to obtain guild posts info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("QueryGuildHotPostInfo postBases redis err: %v", err)
		}

		var err error
		hotPostQuery := es7.NewBoolQuery()
		isAuditQuery := es7.NewTermQuery("is_audit", 1)
		isDelQuery := es7.NewTermQuery("is_del", 0)
		platIdQuery := es7.NewTermQuery("guild_id", guildId)
		hotPostQuery.Must(isAuditQuery, isDelQuery, platIdQuery)

		var sortBys []es7.Sorter
		sortByHotNumQuery := es7.NewFieldSort("hot_num").Desc()
		sortByCreateOnQuery := es7.NewFieldSort("created_on_ms").Desc()
		sortByIdQuery := es7.NewFieldSort("id").Desc()
		sortBys = append(sortBys, sortByHotNumQuery, sortByCreateOnQuery, sortByIdQuery)
		var lastSortValue []interface{}

		guildHotPostResp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, hotPostQuery, sortBys, lastSortValue, 1)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("QueryGuildHotPostInfo EsQuery error, err: %v", err)
			return nil, errs.NewCustomError(c, code.GetGuildHotPostESError, "Failed to obtain guild posts info, data parsing exception")
		}
		if guildHotPostResp != nil && guildHotPostResp.Hits != nil && len(guildHotPostResp.Hits.Hits) > 0 {
			hits = append(hits, guildHotPostResp.Hits.Hits...)
		}
		postListByte, err := json.Marshal(hits)
		if err == nil {
			redis.GetClient().SetEX(c, guildHotPostKey, string(postListByte), 2*time.Minute).Result()
		}
	}
	req := &pb.GetPostListReq{
		Limit: 1,
	}
	queryResp, err := postsFrom(c, intlOpenID, hits, req, language, 0)
	if err != nil {
		return nil, err
	}
	if queryResp != nil && len(queryResp.List) > 0 {
		rsp.PostInfo = queryResp.List[0]
		if intlOpenID != "" {
			// 查看是否点赞
			if rsp.PostInfo.UpvoteCount != 0 {
				if postStarInfo, err := GetPostStar(rsp.PostInfo.PostUuid, intlOpenID, true); err == nil && postStarInfo.Model != nil && postStarInfo.ID > 0 {
					rsp.PostInfo.MyUpvote = &pb.MyUpvote{
						IsStar:     true,
						UpvoteType: int32(postStarInfo.StarType),
					}
				}
			}
			// 查看是否收藏
			// if rsp.PostInfo.CollectionCount != 0 {
			// 	if _, err = GetPostCollection(rsp.PostInfo.PostUuid, intlOpenID, false); err == nil {
			// 		rsp.PostInfo.IsCollection = true
			// 	}
			// }
			// 查看是否关注
			// if collectionInfo, err := user.GetUserCollectionUser(intlOpenID, rsp.PostInfo.IntlOpenid, false); err == nil {
			// 	rsp.PostInfo.IsFollow = true
			// 	rsp.PostInfo.IsMutualFollow = collectionInfo.IsMutual == 1
			// }
		}
	}

	return rsp, nil
}
