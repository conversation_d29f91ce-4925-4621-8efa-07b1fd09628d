package comment

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"html"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	redisClient "github.com/go-redis/redis/v8"
	"trpc.publishing_application.standalonesite/app/common"

	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/tweet"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"

	"trpc.publishing_application.standalonesite/app/logic/formatted"

	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	hotService "trpc.publishing_application.standalonesite/app/logic/hot"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/pkg/security"
	"trpc.publishing_application.standalonesite/app/util"
)

// PushCommentReplyToSecurityDetection 动态的评论回复内容走内容安全审核
func PushCommentReplyToSecurityDetection(c context.Context, param CommentCreationReq, commentAudit *model.CommentContentAudit, intlOpenID string) {
	defer recovery.CatchGoroutinePanic(context.Background())
	userInfo, err := dao.GetUserByIntlOpenid(intlOpenID)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PushCommentReplyToSecurityDetection err: %v", err)
		return
	}
	// logrus.Debugln("PushCommentReplyToSecurityDetection start")
	accountInfo := security.AccountInfo{
		Account:  userInfo.IntlOpenid,
		RoleName: userInfo.Username,
		PlatId:   3,
	}
	// 1.先检查文本内容
	commentAudit.TextRiskLevel, commentAudit.TextRiskType = security.PushTextToSecurityDetection(param.Content, "", "", commentAudit.ID, 3003, accountInfo)
	// 2.如果文本没问题，并且动态有图片，则走图片安全机审
	if len(param.PicUrls) == 0 {
		commentAudit.PicRiskLevel = 1
		commentAudit.PicRiskType = 100
	} else {
		// if commentAudit.TextRiskLevel != security.RISK_LEVLE_NORMAL && commentAudit.TextRiskLevel != security.RISK_LEVLE_DETECT_FAILED {
		commentAudit.PicRiskLevel, commentAudit.PicRiskType = security.PushPicToSecurityDetection(param.PicUrls, commentAudit.ID, 3004, accountInfo)
		// }
	}
	if err := dao.CommentContentAuditUpdate(commentAudit); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PushCommentReplyToSecurityDetection ds.UpdateCommentAudit err: %v", err)
	}
	auditDoc := map[string]interface{}{
		"text_risk_level": commentAudit.TextRiskLevel,
		"text_risk_type":  commentAudit.TextRiskType,
		"pic_risk_level":  commentAudit.PicRiskLevel,
		"pic_risk_type":   commentAudit.PicRiskType,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, commentAudit.CommentUUID, auditDoc)
}

func GetPostCommentReply(id int64) (*model.CommentReply, error) {
	return dao.CommentReplyGet(id)
}

func GetPostCommentReplyNoIgnoreDel(id int64) (*model.CommentReply, error) {
	return dao.CommentReplyGetNoIgnoreDel(id)
}

//func DeletePostCommentReply(c context.Context, reply *model.CommentReply) error {
//	err := dao.CommentReplyDelete(reply.ID)
//	if err != nil {
//		return err
//	}
//
//	// 删除审核记录
//	commentAudit, _ := dao.CommentContentAuditGet(0, reply.ID)
//	if commentAudit != nil && commentAudit.Type == 2 {
//		if err = dao.CommentContentAuditDelete(commentAudit.ID); err != nil {
//			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteComment err: %v", err)
//		}
//	}
//
//	// 删除回复评论记录，p_comment表
//	commentReply, err := dao.CommentGet(0, 0, "", reply.ID, 0)
//	if err != nil {
//		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteComment err: %v", err)
//	}
//	if commentReply != nil && commentReply.Type == 2 {
//		if err = dao.CommentDelete(commentReply.ID); err != nil {
//			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteComment err: %v", err)
//		}
//	}
//
//	// 加载Comment
//	comment, err := dao.CommentGet(reply.CommentID, 0, "", 0, 0)
//	if err != nil {
//		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteComment err: %v", err)
//		return nil
//	}
//
//	// 加载comment的post，主要是为了更新动态的评论数
//	post, err := dao.GetPost(comment.PostUUID)
//	if err != nil {
//		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostByID err: %v", err)
//		return nil
//	}
//
//	// 获取当前评论下所有回复的回复信息
//	commentReplyForDeletes, err := dao.CommentReplyListForDelete(reply.CommentID)
//	if err != nil {
//		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetCommentReplyIdForDelete err: %v", err)
//		return nil
//	}
//
//	// 获取该回复下的其他所有已审核的回复id
//	var isAuditReplyIds []int64
//	// 获取该回复下的其他所有已审核的回复id
//	var isUnAuditReplyIds []int64
//	var allReplyIds []int64
//	var parentReplyIds []int64
//	var childReplyIds []int64
//	parentReplyIds = append(parentReplyIds, reply.ID)
//	for {
//		for _, commentReply := range commentReplyForDeletes {
//			for _, parentReplyId := range parentReplyIds {
//				if commentReply.Reply2replyID == parentReplyId {
//					childReplyIds = append(childReplyIds, commentReply.ID)
//					allReplyIds = append(allReplyIds, commentReply.ID)
//					if commentReply.IsAudit == 2 {
//						isUnAuditReplyIds = append(isUnAuditReplyIds, commentReply.ID)
//					} else {
//						isAuditReplyIds = append(isAuditReplyIds, commentReply.ID)
//					}
//				}
//			}
//		}
//		if len(childReplyIds) == 0 {
//			// 没有更多关联的回复的回复了
//			break
//		}
//		parentReplyIds = parentReplyIds[0:0]
//		parentReplyIds = append(parentReplyIds, childReplyIds...)
//		childReplyIds = childReplyIds[0:0]
//	}
//
//	// 将未审核的数据置为已忽略
//	err = dao.CommentContentAuditIgnoreReplyByCommentIds(isUnAuditReplyIds)
//	if err != nil {
//		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("IgnoreCommentReplyAuditByIds err: %v", err)
//	}
//
//	// 将所有该回复下的id设置is_parent_del=1
//	err = dao.CommentReplySetComentReplyParentDel(allReplyIds)
//	if err != nil {
//		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetComentReplyParentIsDel err: %v", err)
//	}
//	post.CommentCount -= int64(len(isAuditReplyIds))
//
//	// 删除回复的回复
//	// if reply.Reply2replyID != 0 {
//	// 	reply2, err := GetPostCommentReply(reply.Reply2replyID)
//	// 	if err != nil {
//	// 		conf.Errorf(c, "service.GetPostCommentReply err: %v\n", err)
//	// 	}
//	// 	if err = ds.DeleteCommentReply(reply2); err != nil {
//	// 		conf.Errorf(c, "service.DeleteCommentReply 2 err: %v\n", err)
//	// 	}
//	// 	// 删除成功 - 1
//	// 	if reply2 != nil && err == nil && reply2.IsAudit == 1 {
//	// 		post.CommentCount--
//	// 	}
//	// }
//
//	if reply.IsAudit == 1 {
//		// 更新Post回复数
//		post.CommentCount--
//		if post.CommentCount < 0 {
//			post.CommentCount = 0
//		}
//		post.LatestRepliedOn = time.Now().Unix()
//		dao.UpdatePost(post)
//	}
//
//	// 更新索引
//	// PushPostToSearch(post)
//	doc := map[string]interface{}{
//		"id":                post.PostUUID,
//		"comment_count":     post.CommentCount,
//		"latest_replied_on": post.LatestRepliedOn,
//	}
//	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, fmt.Sprintf("%d", post.PostUUID), doc)
//
//	return nil
//}

// CreateCommentReplyAudit 创建评论恢复审核记录
func CreateCommentReplyAudit(c context.Context, intlOpenID string, commentUUID string, gameId, areaId string, isAudit int8, content, picUrls string) (commentReplyAudit *model.CommentContentAudit, err error) {
	var auditOn int64
	auditOn = 0
	status := 1
	// 白名单用户直接生成审批通过的记录
	if isAudit == 1 {
		auditOn = time.Now().Unix()
		status = 2
	}
	commentReplyAudit = &model.CommentContentAudit{
		CommentUUID:   commentUUID,
		Type:          2,
		IntlOpenid:    intlOpenID,
		TextRiskLevel: 0,
		TextRiskType:  100,
		PicRiskLevel:  0,
		PicRiskType:   100,
		Status:        status,
		AuditOn:       auditOn,
		AreaId:        areaId,
		GameId:        gameId,
	}
	if err = dao.CommentContentAuditCreate(commentReplyAudit); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreateCommentReplyAudit err: %v", err)
		return nil, err
	}

	return commentReplyAudit, nil
}

// GetPostCommentReplys 根据评论id获取评论的回复列表，分页查询
func GetPostCommentReplys(c context.Context, myIntlOpenID, language string, req *pb.GetPostCommentRepliesReq) (*pb.GetPostCommentRepliesRsp, error) {
	rsp := &pb.GetPostCommentRepliesRsp{
		PageInfo: &pb.PageInfo{},
		List:     make([]*pb.RepliesItem, 0),
	}
	var err error
	var replys []*model.Comment
	var nextPageCursor, previousPageCursor string

	if req.CommentUuid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "comment id is required")
	}

	// 先获取缓存数据
	commentsBaseRedisKey := cache.GetCommentReplyBaseListGuestKey(req.CommentUuid, req.NextPageCursor, req.Limit)
	if myIntlOpenID != "" {
		// 主态
		commentsBaseRedisKey = cache.GetCommentReplyBaseListHostKey(req.CommentUuid, myIntlOpenID, req.NextPageCursor, req.Limit)
	}
	commentsBaseCacheInfo, err := redis.GetClient().Get(c, commentsBaseRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(commentsBaseCacheInfo), &replys)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPostCommentReplys cache json.Unmarshal error.commentsBaseRedisKey: %s, err: %v", commentsBaseRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetRecentCommentReplysBaseJsonUnmarshalError, "Failed to obtain plate name info, data parsing exception")
		}
	} else {
		// 没有缓存则直接查db数据
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostCommentReplys redis err: %v", err)
		}
		conditions := &dao.CommentListConditions{
			ReplyUuid: req.CommentUuid,
			Type:      2,
			Order: []*dao.OrderConditions{
				&dao.OrderConditions{
					Column: "id",
					IsDesc: true,
				},
			},
		}
		if myIntlOpenID != "" {
			//审核通过的 或者 审核未通过 但是我发布的 我发布的暂且忽略 需要更改方法
			conditions.CompositeCondition = &dao.CompositeCondition{
				Operator: "OR",
				Conditions: []dao.ConditionInterface{
					dao.Conditions{
						Column: "is_audit",
						Symbol: "=",
						Value:  1,
					},
					dao.CompositeCondition{
						Operator: "AND",
						Conditions: []dao.ConditionInterface{
							dao.Conditions{
								Column: "is_audit",
								Symbol: "=",
								Value:  2,
							},
							dao.Conditions{
								Column: "intl_openid",
								Symbol: "=",
								Value:  myIntlOpenID,
							},
						},
					},
				},
			}
		} else {
			conditions.IsAudit = 1
		}

		// 查询类型：下一页数据
		if req.PageType == pb.PageType_NEXTPAGE {
			var idCursor int64
			// 如果是首页
			if req.NextPageCursor == "" {
				idCursor = 0
			} else {
				previousPageCursor = req.NextPageCursor
				idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
				if err != nil {
					return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
				}
				conditions.LtId = idCursor
			}
			replys, err = dao.CommentList(conditions, 10)
			if err != nil {
				return nil, err
			}
		}
		replyListByte, err := json.Marshal(replys)
		if err == nil {
			redis.GetClient().SetEX(c, commentsBaseRedisKey, string(replyListByte), 2*time.Minute).Result()
			if myIntlOpenID != "" {
				keysKey := cache.GetUserCommentCacheKeysKey(myIntlOpenID)
				redis.GetClient().SAdd(c, keysKey, commentsBaseRedisKey).Result()
			}
		}
	}

	// 生成下一页的游标
	rsp.PageInfo.PreviousPageCursor = previousPageCursor
	if len(replys) > 0 {
		nextPageCursor, err = util.EncryptPageCursorI(replys[len(replys)-1].ID)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetPostCommentReplys | Failed to create comments nextPageCursor")
		}
	} else {
		rsp.PageInfo.IsFinish = true
		return rsp, nil
	}

	postUuid := replys[0].PostUUID
	postInfo, err := tweet.GetPostInfoByUUID(c, postUuid)
	if err != nil {
		return nil, errs.NewCustomError(c, code.GetPostFailed, "GetPostCommentReplys | Failed to get post base info")
	}

	// 接下来获取评论列表的分表的详细数据
	replyFormattedItems := make([]*pb.RepliesItem, 0, len(replys))
	var replyUUIDsStr string
	var replyUUIDs []string
	for _, reply := range replys {
		replyUUIDsStr = fmt.Sprintf("%s_%s", replyUUIDsStr, reply.CommentUUID)
		replyUUIDs = append(replyUUIDs, reply.CommentUUID)
	}

	hash := sha256.New()
	hash.Write([]byte(replyUUIDsStr))
	hashValue := hash.Sum(nil)
	replyUUIDsMd5Str := hex.EncodeToString(hashValue)

	// 先获取缓存数据
	replyDetailsRedisKey := cache.GetCommentDetailListKey(replyUUIDsMd5Str, language)
	replyDetailsCacheInfo, err := redis.GetClient().Get(c, replyDetailsRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(replyDetailsCacheInfo), &replyFormattedItems)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPostCommentReplys replyDetails cache json.Unmarshal error.replyDetailsRedisKey: %s, err: %v", replyDetailsRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetRecentReplysDetailJsonUnmarshalError, "Failed to obtain post comment reply info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostCommentReplys commentDetails redis err: %v", err)
		}
		var wg sync.WaitGroup
		var mu sync.Mutex
		userGamePlayerInfoMap := make(map[string]*pbUser.UserInfo)
		for _, reply := range replys {
			replyFormatted := &pb.RepliesItem{
				CommentUuid: reply.CommentUUID,
				PostUuid:    reply.PostUUID,
				IntlOpenid:  reply.IntlOpenid,
				GameId:      reply.GameId,
				AreaId:      reply.AreaId,
				CreatedOn:   reply.CreatedOn,
				IsMine:      reply.IntlOpenid == myIntlOpenID,
				CanDelete:   reply.IntlOpenid == myIntlOpenID,
				CanReport:   reply.IntlOpenid != myIntlOpenID,
				IsAuthor:    reply.IntlOpenid == postInfo.IntlOpenid,
			}
			wg.Add(1) // 增加 WaitGroup 的计数器
			go func(replyFormattedItem *pb.RepliesItem, myHostIntlOpenID string) {
				defer recovery.CatchGoroutinePanic(context.Background())
				defer wg.Done() // 函数结束时减少计数器

				replyContent, err := dao.GetCommentInfoByUUID(replyFormattedItem.CommentUuid)
				if err != nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostCommentReplys GetCommentInfoByUUID err, reply_uuid:(%s), err=(%v)", replyFormattedItem.CommentUuid, err)
				} else {
					replyFormattedItem.Title = replyContent.Title
					replyFormattedItem.Content = replyContent.Content
					replyFormattedItem.PicUrls = strings.Split(replyContent.PicUrls, ",")
				}

				replyState, err := dao.GetCommentStateByCommentUuid(replyFormattedItem.CommentUuid)
				if err != nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostCommentReplys GetCommentStateByCommentUuid err, comment_uuid:(%s), err=(%v)", replyFormattedItem.CommentUuid, err)
				} else {
					replyFormattedItem.UpvoteCount = int64(replyState.UpvoteCount)
				}

				mu.Lock() // 锁定
				userInfo, exists := userGamePlayerInfoMap[replyContent.IntlOpenid]
				mu.Unlock() // 解锁

				if !exists {
					// 如果值不存在，查询用户标签值
					userInfo, err = user.GetUserDetailInfoByOpenid(c, myHostIntlOpenID, replyContent.IntlOpenid, language)
					if err != nil {
						log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostCommentReplys GetUserDetailInfoByOpenid err, intl_openid:(%s), err=(%v)", replyFormattedItem.IntlOpenid, err)
					} else {
						mu.Lock() // 再次锁定以更新 map
						userGamePlayerInfoMap[replyContent.IntlOpenid] = userInfo
						mu.Unlock() // 解锁
					}
				}
				if userInfo != nil {
					replyFormattedItem.User = userInfo
				}

				if replyContent.AtIntlOpenid != "" {
					mu.Lock() // 锁定
					replyUserInfo, exists := userGamePlayerInfoMap[replyContent.AtIntlOpenid]
					mu.Unlock() // 解锁

					if !exists {
						// 如果值不存在，查询用户标签值
						replyUserInfo, err = user.GetUserDetailInfoByOpenid(c, myHostIntlOpenID, replyContent.AtIntlOpenid, language)
						if err != nil {
							log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostCommentReplys GetUserDetailInfoByOpenid err, at_intl_openid:(%s), err=(%v)", replyFormattedItem.IntlOpenid, err)
						} else {
							mu.Lock() // 再次锁定以更新 map
							userGamePlayerInfoMap[replyContent.AtIntlOpenid] = replyUserInfo
							mu.Unlock() // 解锁
						}
					}
					if replyUserInfo != nil {
						replyFormattedItem.AtUser = replyUserInfo
					}
				}
			}(replyFormatted, myIntlOpenID) // 通过闭包传递参数

			replyFormattedItems = append(replyFormattedItems, replyFormatted)
		}
		wg.Wait()

		replyFormattedsByte, err := json.Marshal(replyFormattedItems)
		if err == nil {
			redis.GetClient().SetEX(c, replyDetailsRedisKey, string(replyFormattedsByte), 2*time.Minute).Result()
		}
	}

	// 批量获取主态用户是否有对评论点赞
	if myIntlOpenID != "" {
		// 批量获取当前用户对评论和回复是否有点赞
		starReplyUUIDs, err := dao.BatchCheckUserCommentsStar(replyUUIDs, myIntlOpenID)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostCommentReplys BatchCheckUserCommentsStar err, intl_openid:(%s), err=(%v)", myIntlOpenID, err)
		}
		for _, starReplyUUID := range starReplyUUIDs {
			for _, replyItem := range replyFormattedItems {
				if starReplyUUID == replyItem.CommentUuid {
					replyItem.IsStar = true
				}
			}
		}
	}

	rsp.List = replyFormattedItems
	if len(rsp.List) == 0 || len(rsp.List) < int(req.Limit) {
		rsp.PageInfo.IsFinish = true
	} else {
		rsp.PageInfo.NextPageCursor = nextPageCursor
	}
	return rsp, nil
}

// 回复评论
func CreatePostCommentReply(c context.Context, intlOpenID string, param CommentCreationReq, language string) (*pb.CommentItem, error) {
	var (
		post       *model.Post
		postStats  *model.PostStats
		comment    *model.Comment
		err        error
		atUserInfo *model.UserContent
		rsp        *pb.CommentItem
	)
	userInfo, err := dao.GetUserByIntlOpenid(intlOpenID)
	if err != nil {
		return rsp, errs.NewCustomError(c, code.GetUserInfoError, "CreatePostCommentReply | failed to get user info")
	}

	if post, postStats, comment, atUserInfo, err = createPostPreHandler(c, param.CommentUUID, param.AtIntlOpenid, param.PostUUID, intlOpenID); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("createPostPreHandler err: %v", err)
		return nil, err
	}

	// 回复存在两种情况，如果当前传过来的评论id（comment_uuid）是回复类型的就要生成回复的回复，reply_uuid是评论的id，reply2reply_uuid是评论的回复id，comment_uuid是回复的回复id

	// 创建评论
	// ip := ctx.ClientIP()
	gameidStr := param.GameId
	areaidStr := param.AreaId
	currentTime := time.Now()
	commentReplyData := &model.Comment{
		CommentUUID: util.CreateSnowflakeID(),
		PostUUID:    post.PostUUID,
		IntlOpenid:  intlOpenID,
		Type:        param.Type,
		IsAudit:     param.IsAudit,
		GameId:      gameidStr,
		AreaId:      areaidStr,
		CreatedOnMs: currentTime.UnixMicro(),
		Language:    language,
	}
	// 回复的回复
	if comment.Type == int32(constants.DYNAMIC_REPLY) {
		commentReplyData.ReplyUUID = comment.ReplyUUID         // 评论的id
		commentReplyData.Reply2ReplyUUID = comment.CommentUUID // 这个是回复的id
	} else {
		// 评论的回复
		commentReplyData.ReplyUUID = param.CommentUUID
	}

	err = dao.CommentCreate(commentReplyData, currentTime)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreateComment err: %v\n", err)
		go func(ctx context.Context) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportPostCommentLog(c, comment, param.Content, userInfo, err)
		}(c)
		return rsp, errs.NewCustomError(c, code.CreateCommentFailed, "Failed to publish comment")
	}

	go func(ctx context.Context) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportPostCommentLog(c, comment, param.Content, userInfo, nil)
	}(c)

	// 写入内容表
	replyContentData := &model.CommentContent{
		CommentUUID: commentReplyData.CommentUUID,
		IntlOpenid:  intlOpenID,
		// AtIntlOpenid: param.AtIntlOpenid,
		Content: param.Content,
		PicUrls: strings.Join(param.PicUrls, ","),
	}
	if comment.Type == int32(constants.DYNAMIC_REPLY) {
		replyContentData.AtIntlOpenid = comment.IntlOpenid
	}
	err = dao.CommentContentCreate(replyContentData)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CommentContentCreate err: %v\n", err)
		return rsp, errs.NewCustomError(c, code.CreateCommentFailed, "Failed to publish comment content")
	}

	// 非白名单 写入审核表
	_, err = CreateCommentReplyAudit(c, intlOpenID, commentReplyData.CommentUUID, gameidStr, areaidStr, param.IsAudit, replyContentData.Content, replyContentData.PicUrls)
	// 记录添加错误 需要回滚
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreateCommentReplyAudit err: %v", err)
		return nil, errs.NewCustomError(c, code.CreateReplyFailed, "CreatePostCommentReply | Comment reply failed")
	}

	// 写入es
	go PushCommentToES(commentReplyData, replyContentData)

	//if param.IsAudit == 2 {
	//	go PushCommentReplyToSecurityDetection(c, param, commentReplyAudit, intlOpenID)
	//}

	var commentMaster *model.UserContent
	var commentMasterErr error
	// 前置这个查询评论用户的动作，后面回复的需要用上
	commentMaster, commentMasterErr = dao.GetUserByIntlOpenid(comment.IntlOpenid)
	if param.IsAudit == 1 {
		if comment.Type == int32(constants.DYNAMIC_REPLY) {
			TrigerUpdateCommentHotNum(c, comment.ReplyUUID, true)
		} else {
			TrigerUpdateCommentHotNum(c, param.CommentUUID, true)
		}

		// 更新Post回复数
		postStats.CommentCount++
		post.LatestRepliedOn = time.Now().Unix()
		dao.UpdatePost(post)
		dao.UpdatePostStatsComment(post.PostUUID, int32(postStats.CommentCount))
		// todo 更新索引
		// PushPostToSearch(post)
		hotNum := hotService.CalculatingPostHotNum(postStats)
		doc := map[string]interface{}{
			"comment_count":     postStats.CommentCount,
			"latest_replied_on": post.LatestRepliedOn,
			"hot_num":           hotNum,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)

		// 给评论发布者发消息，有人回复了他
		//commentMaster, err = dao.GetUserByIntlOpenid(comment.IntlOpenid)
		if commentMasterErr == nil && commentMaster.IntlOpenid != intlOpenID {
			go writemessage.SetUserMessage(&model.Message{
				Type:                   constants.MsgTypeReply,
				PostUUID:               post.PostUUID,
				CommentUUID:            comment.CommentUUID,
				ReplyUUID:              comment.ReplyUUID,
				Reply2ReplyUUID:        comment.Reply2ReplyUUID,
				GameID:                 gameidStr,
				AreaID:                 areaidStr,
				SenderUserIntlOpenid:   intlOpenID,
				ReceiverUserIntlOpenid: commentMaster.IntlOpenid,
			}, commentMaster.IntlOpenid, constants.CommentMessageCount)
		}
		// 给动态发布者发消息，有人回复了他
		postMaster, err := dao.GetUserByIntlOpenid(post.IntlOpenid)
		if err == nil && postMaster.IntlOpenid != intlOpenID && commentMaster.ID != postMaster.ID {
			go writemessage.SetUserMessage(&model.Message{
				Type:                   constants.MsgTypeReply,
				PostUUID:               post.PostUUID,
				CommentUUID:            comment.CommentUUID,
				ReplyUUID:              comment.ReplyUUID,
				Reply2ReplyUUID:        comment.Reply2ReplyUUID,
				GameID:                 gameidStr,
				AreaID:                 areaidStr,
				SenderUserIntlOpenid:   intlOpenID,
				ReceiverUserIntlOpenid: postMaster.IntlOpenid,
			}, postMaster.IntlOpenid, constants.CommentMessageCount)
		}
		//给评论回复的发布者发消息，有人在他的评论回复下发了回复
		if param.AtIntlOpenid != "" && atUserInfo != nil {
			if err == nil && atUserInfo.IntlOpenid != intlOpenID && commentMaster.IntlOpenid != atUserInfo.IntlOpenid && postMaster.IntlOpenid != atUserInfo.IntlOpenid {
				// 创建消息提醒
				go writemessage.SetUserMessage(&model.Message{
					Type:                   constants.MsgTypeReply,
					PostUUID:               param.PostUUID,
					CommentUUID:            comment.CommentUUID,
					ReplyUUID:              comment.ReplyUUID,
					Reply2ReplyUUID:        comment.Reply2ReplyUUID,
					GameID:                 gameidStr,
					AreaID:                 areaidStr,
					SenderUserIntlOpenid:   intlOpenID,
					ReceiverUserIntlOpenid: atUserInfo.IntlOpenid,
				}, atUserInfo.IntlOpenid, constants.CommentMessageCount)
			}
		}
	}
	var atUserObj *pbUser.UserInfo
	if commentMaster != nil && replyContentData.AtIntlOpenid != "" {
		atUserObj = &pbUser.UserInfo{
			Username:   html.UnescapeString(commentMaster.Username),
			Remark:     html.UnescapeString(commentMaster.Remark),
			Avatar:     commentMaster.Avatar,
			IntlOpenid: commentMaster.IntlOpenid,
		}
	}
	rsp = &pb.CommentItem{
		CommentUuid:  commentReplyData.CommentUUID,
		PostUuid:     commentReplyData.PostUUID,
		IntlOpenid:   commentReplyData.IntlOpenid,
		Content:      param.Content,
		PicUrls:      param.PicUrls,
		CanDelete:    true,
		CanReport:    false, // 自己发布的不能举报自己
		CreatedOn:    commentReplyData.CreatedOn,
		User:         formatted.ReturnDynamicProtoUserInfoFormatted(userInfo.Format()),
		AtUser:       atUserObj,
		AtIntlOpenid: replyContentData.AtIntlOpenid,
		IsAuthor:     commentReplyData.IntlOpenid == post.IntlOpenid,
		IsMine:       true,
	}
	go cache.RemoveUserCommentCacheKeys(intlOpenID)
	return rsp, nil
}

// 获取评论下最新的回复
func GetNewsReplyByCommentUUID(c context.Context, commentUUID string, count int, intlOpenid string) ([]*pb.RepliesItem, error) {
	conditions := &dao.CommentListConditions{
		ReplyUuid: commentUUID,
		IsAudit:   1,
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}
	comments, err := dao.CommentList(conditions, count)
	if err != nil {
		//return nil, errs.NewCustomError(c, code.GetNewReplyTheComment, "GetNewsReplyByCommentUUID | Failed to get user new reply list")
		return nil, err
	}
	var wg sync.WaitGroup
	var commentContentChan = make(chan *pb.RepliesItem)
	for _, comment := range comments {
		wg.Add(1)
		go func(commentItem *model.Comment) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done()
			var repliesItem = &pb.RepliesItem{}
			commentContent, err := dao.GetCommentInfoByUUID(commentItem.CommentUUID)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetNewsReplyByCommentUUID, comment_uuid: %s err: %v", commentItem.CommentUUID, err)
				return
			}
			repliesItem = &pb.RepliesItem{
				CommentUuid:  commentContent.CommentUUID,
				PostUuid:     commentItem.PostUUID,
				ReplyUuid:    commentItem.ReplyUUID,
				IntlOpenid:   commentContent.IntlOpenid,
				IsAudit:      int32(commentItem.IsAudit),
				AtIntlOpenid: commentContent.AtIntlOpenid,
				GameId:       commentItem.GameId,
				AreaId:       commentItem.AreaId,
				Title:        commentContent.Title,
				Content:      commentContent.Content,
				PicUrls:      strings.Split(commentContent.PicUrls, ","),
				//UpvotecCount: commentContent.UpvoteCount,
				CreatedOn:  commentItem.CreatedOn,
				ModifiedOn: commentItem.ModifiedOn,
			}
			var userIntlOpenids = make([]string, 0)
			if commentContent.AtIntlOpenid != "" {
				userIntlOpenids = append(userIntlOpenids, commentContent.AtIntlOpenid)
			}
			userIntlOpenids = append(userIntlOpenids, commentContent.IntlOpenid)
			userInfos, err := dao.GetUserListByOpenid(userIntlOpenids)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserListByOpenid, comment_uuid: %s, uuids: %+v err: %v", commentItem.CommentUUID, userIntlOpenids, err)
			}
			for _, info := range userInfos {
				if info.IntlOpenid == repliesItem.AtIntlOpenid {
					repliesItem.AtUser = formatted.ReturnDynamicProtoUserInfoFormatted(info.Format())
					continue
				}
				if info.IntlOpenid == repliesItem.IntlOpenid {
					repliesItem.User = formatted.ReturnDynamicProtoUserInfoFormatted(info.Format())
					continue
				}
			}
			// 查看是否点赞
			star, err := dao.CommentStarGetV2(0, repliesItem.CommentUuid, intlOpenid)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CommentStarGetV2, comment_uuid: %s, uuids: %+v err: %v", commentItem.CommentUUID, userIntlOpenids, err)
			} else {
				if star.Model != nil && star.ID > 0 {
					repliesItem.IsStar = true
				}
			}

			commentContentChan <- repliesItem
		}(comment)
	}
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		wg.Wait()
		close(commentContentChan)
	}()
	var data []*pb.RepliesItem
	for content := range commentContentChan {
		data = append(data, content)
	}
	return data, nil
}
