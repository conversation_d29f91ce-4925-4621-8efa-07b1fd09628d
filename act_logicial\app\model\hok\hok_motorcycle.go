package hok

import (
	"gorm.io/plugin/soft_delete"
	"trpc.act.logicial/app/model"
)

// LuckyNumberConfigModel ...
type LuckyNumberConfigModel struct {
	model.AppModel
}

// TableName .
func (LuckyNumberConfigModel) TableName() string {
	return "lucky_number_config"
}

type LuckyNumberConfig struct {
	LuckyNumberConfigModel
	ID              uint   `gorm:"primaryKey"`
	SequentialValue int    `json:"sequential_value" gorm:"not null"`
	NumberSegment   string `json:"number_segment" gorm:"not null"`
}

// LuckyNumberLogModel ...
type LuckyNumberLogModel struct {
	model.AppModel
}

// TableName .
func (LuckyNumberLogModel) TableName() string {
	return "lucky_number_log"
}

type LuckyNumberLog struct {
	LuckyNumberLogModel
	ID              uint   `json:"id" column:"id"`
	LuckyNumber     string `json:"lucky_number" column:"lucky_number"`
	SendStatus      int    `json:"send_status" column:"send_status"`
	UID             string `json:"uid" column:"uid"`
	AccountType     int    `json:"account_type" column:"account_type"`
	SequentialValue int    `json:"sequential_value" column:"sequential_value"`
}

type UserLuckyNumRecordModel struct {
	model.AppModel
}

// TableName .
func (UserLuckyNumRecordModel) TableName() string {
	return "user_lucky_num_records"
}

// UserLuckyNumRecord 用户幸运号码记录表
type UserLuckyNumRecord struct {
	UserLuckyNumRecordModel
	ID          uint32 `json:"id" gorm:"column:id;primaryKey;autoIncrement" comment:"primary key"` // primary key
	UID         string `json:"uid" gorm:"column:uid" comment:"用户ID"`                               // 用户ID
	AccountType int    `json:"account_type" gorm:"column:account_type" comment:"用户类型"`             // 用户类型
	LuckyNum    string `json:"lucky_num" gorm:"column:lucky_num" comment:"幸运号码"`                   // 幸运号码
	SendType    int    `json:"send_type" gorm:"column:send_type" comment:"发放任务类型：1任务，2cdk，3邀请"`    // 发放任务类型：1任务，2cdk，3邀请
	WinStatus   int    `json:"win_status" gorm:"column:win_status" comment:"中奖状态 0 未中奖，1已中奖"`      // 中奖状态 0 未中奖，1已中奖
	WinningAt   uint32 `json:"winning_at" gorm:"column:winning_at" comment:"中奖时间"`                 // 中奖时间
	Remark      string `json:"remark" gorm:"column:remark" comment:"兑换的cdk"`                       // 兑换的cdk
}

type WinningRecordModel struct {
	model.AppModel
}

// TableName .
func (WinningRecordModel) TableName() string {
	return "winning_records"
}

// WinningRecord 中奖记录表
type WinningRecord struct {
	WinningRecordModel
	ID                uint32 `json:"id" column:"id" comment:"primary key"`                // primary key
	UID               string `json:"uid" column:"uid" comment:"用户ID"`                     // 用户ID
	AccountType       int16  `json:"account_type" column:"account_type" comment:"用户类型"`   // 用户类型
	PresentID         string `json:"present_id" column:"present_id" comment:"摩托车礼包ID"`    // 摩托车礼包ID
	LuckyNumber       string `json:"lucky_number" column:"lucky_number" comment:"开奖幸运号码"` // 开奖幸运号码
	PrizeMultilingual string `json:"prize_multilingual" column:"prize_multilingual"`      // 奖品多语言
}

type ActivityPrizeRedemptionRecordsModel struct {
	model.AppModel
}

func (ActivityPrizeRedemptionRecordsModel) TableName() string {
	return "hok_motorcycle_activity_prize_redemption_records"
}

type ActivityPrizeRedemptionRecords struct {
	ActivityPrizeRedemptionRecordsModel
	ID          uint32                `json:"id" column:"id" comment:"primary key"`              // primary key
	UID         string                `json:"uid" column:"uid" comment:"用户ID"`                   // 用户ID
	AccountType int16                 `json:"account_type" column:"account_type" comment:"用户类型"` // 用户类型
	CommodityID uint32                `json:"commodity_id" column:"commodity_id" comment:"商品ID"` // 商品ID
	ExpendNum   uint32                `json:"expend_num" column:"expend_num" comment:"消耗数量"`     // 消耗数量
	DeletedAt   soft_delete.DeletedAt `gorm:"softDelete:milli;column:deleted_at"`
}
