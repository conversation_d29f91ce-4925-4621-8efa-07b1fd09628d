package comment

import (
	"context"
	"fmt"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	monitorPb "git.woa.com/trpcprotocol/publishing_application/standalonesite_monitor"
	"strings"
	"time"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

func CommentReviewPass(c context.Context, req *pb.CMSReviewPostCommentReq, isMachineReview bool, commentInfo *model.Comment, commentContentInfo *model.CommentContent, post *model.Post) error {
	currentTime := time.Now().Unix()
	// 如果是审批通过
	auditUpdateData := map[string]interface{}{
		"audit_user":      req.UpdateUser,
		"audit_on":        currentTime,
		"modified_on":     currentTime,
		"status":          2,
		"audit_introduce": req.AuditIntroduce,
	}
	if !isMachineReview {
		auditUpdateData["artificial_status"] = 1
	}
	// 更新审批表
	err := dao.UpdateCommenAudit(commentInfo.CommentUUID, auditUpdateData)
	if err != nil {
		return err
	}
	// 更新p_comment表，并更新评论的回复数
	commentUpdateData := map[string]interface{}{
		"is_audit":    1,
		"modified_on": currentTime,
	}
	err = dao.CommentUpdate(commentInfo.CommentUUID, commentUpdateData)
	if err != nil {
		return err
	}
	// 更新评论ES
	doc := map[string]interface{}{
		"status":          2,
		"audit_user":      req.UpdateUser,
		"audit_on":        currentTime,
		"modified_on":     currentTime,
		"audit_introduce": req.AuditIntroduce,
	}
	if !isMachineReview {
		doc["artificial_status"] = 1
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, commentInfo.CommentUUID, doc)
	if post.IsDel == 0 {
		// 创建消息通知
		if commentInfo.Type == int32(constants.DYNAMIC_COMMENT) {
			// 评论的消息
			if commentInfo.IntlOpenid != post.IntlOpenid {
				writemessage.SetUserMessage(&model.Message{
					Type:                   constants.MsgTypeComment,
					PostUUID:               commentInfo.PostUUID,
					CommentUUID:            commentInfo.CommentUUID,
					GameID:                 commentInfo.GameId,
					AreaID:                 commentInfo.AreaId,
					SenderUserIntlOpenid:   commentInfo.IntlOpenid,
					ReceiverUserIntlOpenid: post.IntlOpenid,
				}, post.IntlOpenid, constants.CommentMessageCount)
			}
			// 是否有艾特用户信息
			if commentContentInfo.AtIntlOpenid != "" {
				// 创建消息提醒
				writemessage.SetUserMessage(&model.Message{
					Type:                   constants.MsgTypeComment,
					PostUUID:               commentInfo.PostUUID,
					CommentUUID:            commentInfo.CommentUUID,
					ReplyUUID:              commentInfo.ReplyUUID,
					Reply2ReplyUUID:        commentInfo.Reply2ReplyUUID,
					GameID:                 commentInfo.GameId,
					AreaID:                 commentInfo.AreaId,
					SenderUserIntlOpenid:   commentInfo.IntlOpenid,
					ReceiverUserIntlOpenid: commentContentInfo.AtIntlOpenid,
				}, commentContentInfo.AtIntlOpenid, constants.CommentMessageCount)
			}
		} else {
			// 回复的消息
			// 获取被回复的评论
			var received *model.Comment
			if commentInfo.Reply2ReplyUUID != "" {
				// 回复的回复
				received, err = dao.GetCommentByUUID(commentInfo.Reply2ReplyUUID)
				if err != nil {
					return err
				}
			} else {
				// 回复
				received, err = dao.GetCommentByUUID(commentInfo.ReplyUUID)
				if err != nil {
					return err
				}
			}
			// 创建用户消息提醒,如果不是回复自己的话需要发送站内信
			if received.IntlOpenid != commentInfo.IntlOpenid {
				writemessage.SetUserMessage(&model.Message{
					Type:                   constants.MsgTypeReply,
					PostUUID:               commentInfo.PostUUID,
					CommentUUID:            commentInfo.CommentUUID,
					ReplyUUID:              commentInfo.ReplyUUID,
					Reply2ReplyUUID:        commentInfo.Reply2ReplyUUID,
					GameID:                 commentInfo.GameId,
					AreaID:                 commentInfo.AreaId,
					SenderUserIntlOpenid:   commentInfo.IntlOpenid,
					ReceiverUserIntlOpenid: received.IntlOpenid,
				}, received.IntlOpenid, constants.CommentMessageCount)
			}
			//postMaster, err := dao.GetUserByIntlOpenid(postContentInfo.IntlOpenid)
			// 回复评论的人如果是发布动态的人或者是回复的评论是发布动态的人
			if err == nil && post.IntlOpenid != commentInfo.IntlOpenid && post.IntlOpenid != received.IntlOpenid {
				writemessage.SetUserMessage(&model.Message{
					Type:                   constants.MsgTypeReply,
					PostUUID:               post.PostUUID,
					CommentUUID:            commentInfo.CommentUUID,
					ReplyUUID:              commentInfo.ReplyUUID,
					Reply2ReplyUUID:        commentInfo.Reply2ReplyUUID,
					GameID:                 commentInfo.GameId,
					AreaID:                 commentInfo.AreaId,
					SenderUserIntlOpenid:   commentInfo.IntlOpenid,
					ReceiverUserIntlOpenid: post.IntlOpenid,
				}, post.IntlOpenid, constants.CommentMessageCount)
			}
		}
		go DeleteCommentListCache(context.Background(), post.PostUUID, "", "", 10)
	}
	// 推送到舆情bot AI分类
	if commentContentInfo.Content != "" {
		var content string
		// 用于存储从文本字段抽离出来的图片链接、文本的数组、换行次数
		_, contentTexts, _, err := util.TrimHtmlLabel(commentContentInfo.Content)
		if err == nil {
			content = util.RemoveSymbol(strings.Join(contentTexts, " "))
		} else {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushCommentToSecurityDetection | trim html label failed, err:%v, content: %s", err, commentContentInfo.Content)
			content = commentContentInfo.Content
		}
		classifyParam := &monitorPb.ContentClassifyReq{
			ContentType: 2,
			PostUuid:    commentInfo.PostUUID,
			CommentUuid: commentInfo.CommentUUID,
			Language:    commentInfo.Language,
			Title:       commentContentInfo.Title,
			Content:     content,
		}
		monitorProxy.ContentClassify(c, classifyParam)
	}
	return nil
}

func CommentReviewReject(c context.Context, req *pb.CMSReviewPostCommentReq, commentInfo *model.Comment) error {
	currentTime := time.Now().Unix()
	// 如果是审批不通过
	auditUpdateData := map[string]interface{}{
		"audit_user":        req.UpdateUser,
		"audit_introduce":   req.AuditIntroduce,
		"audit_on":          currentTime,
		"modified_on":       currentTime,
		"status":            3,
		"del_type":          int(constants.POST_COMMENT_CONTENT_DELETE_TYPE_BREVIEW),
		"del_reason":        req.DelReason,
		"artificial_status": 2,
	}
	// 更新审批表
	err := dao.UpdateCommenAudit(commentInfo.CommentUUID, auditUpdateData)
	if err != nil {
		return err
	}

	// 删除评论表中的数据
	// 删除评论p_comment表中评论的记录
	if err = dao.CommentDelete(commentInfo.CommentUUID); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteComment err: %v", err)
		return err
	}
	// 删除评论p_comment_content_xx表中评论的记录
	if err = dao.CommentContentDelete(commentInfo.CommentUUID, commentInfo.IntlOpenid); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteCommentContent err: %v", err)
		return err
	}

	// 删除可能在审核当中的数据
	if commentInfo.IsAudit == 2 {
		go func(commentUuid string) {
			redis.GetClient().ZRem(context.Background(), cache.WaitingForReviewCommentToKafkaKeys(), commentUuid)
		}(commentInfo.CommentUUID)
	}

	// 创建官方消息通知
	// 评论的消息
	writemessage.SetUserMessage(&model.Message{
		Type:                   constants.MsgTypeOfficialDeleteComment,
		PostUUID:               commentInfo.PostUUID,
		CommentUUID:            commentInfo.CommentUUID,
		ReplyUUID:              commentInfo.ReplyUUID,
		Reply2ReplyUUID:        commentInfo.Reply2ReplyUUID,
		GameID:                 commentInfo.GameId,
		AreaID:                 commentInfo.AreaId,
		SenderUserIntlOpenid:   commentInfo.GameId,
		ReceiverUserIntlOpenid: commentInfo.IntlOpenid,
		ExtInfo:                fmt.Sprintf("{\"del_type\": %d, \"del_reason\": %d}", int(constants.POST_COMMENT_CONTENT_DELETE_TYPE_BREVIEW), req.DelReason),
	}, commentInfo.IntlOpenid, constants.SiteMessageCount)

	// 更新ES
	doc := map[string]interface{}{
		"is_del":            1,
		"deleted_on":        time.Now().Unix(),
		"status":            3,
		"audit_user":        req.UpdateUser,
		"audit_on":          currentTime,
		"modified_on":       currentTime,
		"audit_introduce":   req.AuditIntroduce,
		"artificial_status": 2,
		"del_type":          int(constants.POST_COMMENT_CONTENT_DELETE_TYPE_BREVIEW),
		"del_reason":        req.DelReason,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, commentInfo.CommentUUID, doc)
	return err
}
