package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

type UserPermissionConditions struct {
	IntlOpenid  string
	ActionType  int
	IntlOpenids []string
	ActionValue int
	ValidOn     int64
}

func UserPermissionGet(id int64, intlOpenID string, needDelRecord bool) (*model.UserPermission, error) {
	var userPermission model.UserPermission
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserPermission{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if intlOpenID != "" {
		db = db.Where("intl_openid = ?", intlOpenID)
	}

	if needDelRecord {
		// 查询已删除is_del=1的记录
		err := db.Unscoped().First(&userPermission).Error
		if err != nil {
			return nil, err
		}
	} else {
		// 查询is_del=0的记录
		err := db.First(&userPermission).Error
		if err != nil {
			return nil, err
		}
	}
	return &userPermission, nil
}

func UserPermissionCreate(userPermission *model.UserPermission) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.UserPermission{}).TableName()).Create(&userPermission).Error

	return err
}

func BatchAddUserPermission(list []*model.UserPermission) error {
	bErr := DB.SelectConnect("db_standalonesite").Table((&model.UserPermission{}).TableName()).CreateInBatches(list, len(list)).Error
	if bErr != nil {
		return bErr
	}

	return nil
}

func UserPermissionDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserPermission{}).TableName()).Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func BatchUserPermissionDelete(intlOpenids []string, actionType, actionValue int) error {
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserPermission{}).TableName())
	if actionValue > 0 {
		db.Where("action_value = ?", actionValue)
	}
	return db.Where("intl_openid in ? AND action_type = ?", intlOpenids, actionType).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func UserPermissionUpdate(id int64, isMutual int) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserPermission{}).TableName()).Where("id = ?", id).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"is_del":      0,
	}).Error
}

func UserPermissionList(conditions *UserPermissionConditions, limit int) ([]*model.UserPermission, error) {
	var userPermissions []*model.UserPermission
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserPermission{}).TableName())

	if limit > 0 {
		db = db.Limit(limit)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenids)
	}
	if len(conditions.IntlOpenids) > 0 {
		db = db.Where("intl_openid in ?", conditions.IntlOpenids)
	}
	if conditions.ActionType > 0 {
		db = db.Where("action_type = ?", conditions.ActionType)
	}
	if conditions.ActionValue > 0 {
		db = db.Where("action_value = ?", conditions.ActionValue)
	}
	if conditions.ValidOn > 0 {
		db = db.Where("valid_on > ?", conditions.ValidOn)
	}

	err = db.Find(&userPermissions).Error
	if err != nil {
		return nil, err
	}
	return userPermissions, nil
}

func BatchDeleteAllUserPermission(intlOpenids []string, actionType int) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserPermission{}).TableName()).Where("intl_openid in ? AND action_type = ? AND action_value != 0", intlOpenids, actionType).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func BatchDeleteOtherPermission(intlOpenids []string, actionType int, actionValue int) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserPermission{}).TableName()).Where("intl_openid in ? AND action_type != ? AND action_value != ?", intlOpenids, actionType, actionValue).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}
