package code

import (
	"git.code.oa.com/trpc-go/trpc-go/errs"
)

// 503001 - 503500 预留不可以使用
const (
	// CommonNotMathchStr 没有匹配到字符串
	CommonNotMathchStr = 503501
	// CommonParamJsonError json字段错误
	CommonParamJsonError = 503502
	// CommonRateRandomNotPass 没有通过概率
	CommonRateRandomNotPass = 503503
	// CommonConfigureTwoValueFail 计算失败
	CommonConfigureTwoValueFail = 503504
	// CommonMapKeyValueDontMatch 对象未匹配到key值
	CommonMapKeyValueDontMatch = 503505
	// GenerateUUIDErr 生成uuid报错
	GenerateUUIDErr = 503506
	// CommonValueIsFalse object中key值为false
	CommonValueIsFalse = 503507

	// IsSensitive 敏感词
	IsSensitive = 503601
	// CommonParamTypeErr 参数类型错误
	CommonParamTypeErr = 503602

	// RequestFrequencyExceededLimit 请求频率超过限制
	RequestFrequencyExceededLimit = 503701
	// CommonDataError 数据异常
	CommonDataError = 503702
	// CommonMatchStr 匹配到字符串
	CommonMatchStr = 503703
	// CommonHttpPostError 接口请求错误
	CommonHttpPostError = 503704

	// CommonJSONMarshalErr JSONMarshalErr
	CommonJSONMarshalErr = 503705
	// PeriodTypeError 日期时间类型错误
	PeriodTypeError        = 503706
	CommonJSONUnmarshalErr = 503707

	// 系统错误
	CommonSystemError = 503708
	// 用户未登录
	CommonUserNotLogin = 503709

	// RedisAllowReturnError 限频异常
	RedisAllowReturnError = 503710
	// RequestsTooFrequent 请求过于频繁
	RequestsTooFrequent = 503711
	// NotInCheckTime 不在检查时间内
	NotInCheckTime = 503712
	// NotOutCheckTime 不在检查时间外
	NotOutCheckTime         = 503713
	CaptchaParameterMissing = 503714
	CheckCaptchaErr         = 503715
	VerificationCodeFails   = 503716
	GetIPFail               = 503717
	NoMatch                 = 503718
	TagIdIsErr              = 503719
	TimeRangeErr            = 503720 // 时间范围异常
	InvalidTimeType         = 503721 // 无效的时间类型
	InvalidUID              = 503722 // 无效的uid
	GameIDNotObtained       = 503723 // 未获取游戏ID
	TaskPointsParamErr      = 503724 // 积分任务接口参数异常
	GetConfErr              = 503725 // 获取配置数据异常
)

var (
	ErrSystemError                        = errs.New(CommonSystemError, "system error")
	ErrParamError                         = errs.New(CommonParamTypeErr, "param error")
	ErrUserNotLoginError                  = errs.New(CommonUserNotLogin, "user not login")
	ErrRequestFrequencyExceededLimitError = errs.New(RequestFrequencyExceededLimit, "request frequency exceeded limit")
)
