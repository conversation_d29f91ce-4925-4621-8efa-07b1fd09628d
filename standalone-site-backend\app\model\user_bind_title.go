package model

// UserBindTitle 用户称号
type UserBindTitle struct {
	*Model
	//User          *User          `json:"user"`
	Title         *Title         `json:"title"`
	TitleLanguage *TitleLanguage `json:"language" gorm:"foreignKey:TitleId;references:TitleId;"`
	IntlOpenid    string         `json:"intl_openid"`
	TitleId       int64          `json:"title_id"`
	Status        int64          `json:"status"`
	GameId        string         `json:"game_id"`
	AreaId        string         `json:"area_id"`
}

// UserBindTitle 用户称号
type UserBindTitleRow struct {
	*Model
	IntlOpenid string `json:"intl_openid"`
	TitleId    int64  `json:"title_id"`
	Status     int64  `json:"status"`
	GameId     string `json:"game_id"`
	AreaId     string `json:"area_id"`
}

type UserBindTitleFormated struct {
	ID         int64                 `json:"id"`
	Title      *TitleNewListFormated `json:"title"`
	IntlOpenid string                `json:"intl_openid"`
	TitleId    int64                 `json:"title_id"`
	Status     int64                 `json:"status"`
	GameId     string                `json:"game_id"`
	AreaId     string                `json:"area_id"`
}

func (t *UserBindTitle) TableName() string {
	return "p_user_bind_title"
}

func (t *UserBindTitle) Format() *UserBindTitleFormated {
	if t.Model != nil {
		return &UserBindTitleFormated{
			ID:         t.ID,
			Title:      &TitleNewListFormated{},
			IntlOpenid: t.IntlOpenid,
			TitleId:    t.TitleId,
			Status:     t.Status,
			GameId:     t.GameId,
			AreaId:     t.AreaId,
		}
	}

	return nil
}
