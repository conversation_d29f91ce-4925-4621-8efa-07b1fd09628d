package user

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	user_pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	redisClient "github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

type UserAvatarPendantsItem struct {
	ID            int64  `json:"id"`
	AvaterPendant string `json:"avater_pendant"`
	Title         string `json:"title"`
	Condition     string `json:"condition"`
	JumpURL       string `json:"jump_url"`
	ValidBeginAt  int64  `json:"valid_begin_at"`
	ValidEndAt    int64  `json:"valid_end_at"`
	IsWeared      int    `json:"is_weared"`
	IsPermanent   int    `json:"is_permanent"`
	IsOwned       int    `json:"is_owned"`
}

// 添加头像挂件
func AddAvatarPendantToUser(intlOpenids []string, avatarPendantId int64) (err error) {
	ctx := context.Background()
	if avatarPendantId == 0 || len(intlOpenids) == 0 {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Warnf("AddAvatarPendantToUser failed, avatarPendantId or intlOpenid is empty")
		return
	}
	// 写入数据库
	// 当前挂件的信息
	avatarPendantInfo, err := dao.GetAvatarPendantInfo(avatarPendantId)
	secondDuration := avatarPendantInfo.SecondDuration
	// 没必要搞这么大，当前时间戳加上1500000000都已经到2072-07-05后了，确保db存储没问题
	if avatarPendantInfo.IsPermanent == 1 {
		secondDuration = 1500000000
	}
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("AddAvatarPendantToUser failed, get avatarPendantInfo failed, err: %v", err)
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, code.AddAvatarPendantToUserFailed, "set user avatar pendant failed,intlOpenids: %v, avatarPendantId: %v", intlOpenids, avatarPendantId)
	}
	// 获取这批用户的已有挂件
	hasOwnedAvatarPendantList, err := dao.GetUserOwnedAvatarPendantsByUserId(intlOpenids, avatarPendantId)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("AddAvatarPendantToUser failed, getUserAvatarPendantsByUserId failed, err: %v", err)
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, code.AddAvatarPendantToUserFailed, "get user avatar pendant failed,intlOpenids: %v, avatarPendantId: %v", intlOpenids, avatarPendantId)
	}
	userAvatarPendants := make([]*model.UserAvatarPendant, 0)
	for _, intlOpenid := range intlOpenids {
		isFound := false
		for _, hasOwnedAvatarPendant := range hasOwnedAvatarPendantList {
			if hasOwnedAvatarPendant.IntlOpenid == intlOpenid {
				hasOwnedAvatarPendant.ValidEndAt += int64(secondDuration)
				// 过期时间设置为2090-01-01  00:00:00
				if hasOwnedAvatarPendant.ValidEndAt > 3786883200 {
					hasOwnedAvatarPendant.ValidEndAt = 3786883200
				}

				hasOwnedAvatarPendant.IsDel = 0
				// 如果用户已经拥有该挂件，累加生效时间
				userAvatarPendants = append(userAvatarPendants, hasOwnedAvatarPendant)
				isFound = true
				break
			}
		}
		if !isFound {
			validEndAt := time.Now().Unix() + int64(secondDuration)
			if validEndAt > 3786883200 {
				validEndAt = 3786883200
			}
			userAvatarPendant := &model.UserAvatarPendant{
				IntlOpenid:      intlOpenid,
				AvatarPendantID: avatarPendantId,
				ValidEndAt:      validEndAt,
				ValidBeginAt:    time.Now().Unix(),
				GameID:          avatarPendantInfo.GameID,
				AreaID:          avatarPendantInfo.AreaID,
				Model: &model.Model{
					IsDel: 0,
				},
				IsWeared: 0, // 为找到的，直接设置为未佩戴
			}
			userAvatarPendants = append(userAvatarPendants, userAvatarPendant)
		}
	}
	err = dao.BatchUpdateOrCreateUserAvatarPendants(userAvatarPendants)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("AddAvatarPendantToUser failed, BatchUpdateOrCreateUserAvatarPendants failed, err: %v", err)
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, code.AddAvatarPendantToUserFailed, "set user avatar pendant failed,intlOpenids: %v, avatarPendantId: %v", intlOpenids, avatarPendantId)
	}
	go BatchRemoveUserAvatarPendantCache(context.Background(), intlOpenids)
	return nil
}

// func UpdateUserAvatarPendantCache(userIntlOpenids []string, avatarPendantId int64) (err error) {
// 	if len(userIntlOpenids) == 0 {
// 		return nil
// 	}
// 	curTimestamp := time.Now().Unix()
// 	// 获取当前生效的挂件
// 	validAvatarPendentList, err := dao.BatchGetValidUserAvatarPendants(userIntlOpenids, avatarPendantId, curTimestamp)
// 	if err != nil {
// 		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UpdateUserAvatarPendantCache failed, BatchGetValidUserAvatarPendants failed, err: %v", err)
// 		return errs.NewSystemError(context.Background(), errs.ErrorTypeMysql, code.AddAvatarPendantToUserFailed, "get user avatar pendant failed,intlOpenids: %v, avatarPendantId: %v", userIntlOpenids, avatarPendantId)
// 	}
// 	for _, validAvatarPendant := range validAvatarPendentList {
// 		userAvatarPendantsKey := cache.GetUserAvatarPendantsListKey(validAvatarPendant.IntlOpenid)

// 	}
// }

func GetUserAvatarPendants(c context.Context, intlOpenid string, language string, nextPageCursor string, limit int64, previousPageCursor string) (rsp *user_pb.GetUserAvatarPendantListRsp, err error) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetUserAvatarPendants, intlOpenid: %s, langauge: %s, nextPageCursor:%s, limit:%d", intlOpenid, language, nextPageCursor, limit)
	rsp = &user_pb.GetUserAvatarPendantListRsp{
		UserAvatarPendants: make([]*user_pb.UserAvatarPendantItem, 0),
		PageInfo: &user_pb.UserPageInfo{
			NextPageCursor: "",
			IsFinish:       false,
		},
	}
	userAvatarPendants := make([]*user_pb.UserAvatarPendantItem, 0)
	rspUserAvatarPendants := make([]*user_pb.UserAvatarPendantItem, 0)
	if intlOpenid == "" {
		return rsp, nil
	}
	if language == "" {
		language = "en"
	}
	userAvatarPendantsKey := cache.GetUserAvatarPendantsListKey(intlOpenid, language, nextPageCursor, limit)
	if userAvatarPendantsKey == "" {
		return rsp, errs.NewCustomError(c, code.AvatarPendantGetListFailed, "userAvatarPendantsKey is empty")
	}
	userAvaterPendantsListStr, rErr := redis.GetClient().Get(c, userAvatarPendantsKey).Result()
	// log.Debugf("userAvaterPendantsListStr: %s", userAvaterPendantsListStr)
	if rErr == nil && userAvaterPendantsListStr != "" {
		umErr := json.Unmarshal([]byte(userAvaterPendantsListStr), &rsp)
		if umErr == nil {
			// 请求参数是否在缓存列表中
			isParamInCache, _ := IsRequestParamsInAvatarPendantCacheList(c, userAvaterPendantsListStr)
			// 需要判断用户挂件缓存列表中有没有这个用户
			isInCahce, _ := IsUserInAvatarPendantCacheList(c, intlOpenid)
			if isInCahce && isParamInCache {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetUserAvatarPendants from cache: %s", userAvaterPendantsListStr)
				return rsp, nil
			}
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarPendants failed, json.Unmarshal failed, err: %v", umErr)
		}
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetUserAvatarPendants failed, json.Unmarshal failed, err: %v", rErr)
	}
	// 获取当前用户未过期的挂件
	curTimestamp := time.Now().Unix()
	userValidAvatarPendants, err := dao.GetValidUserAvatarPendants(intlOpenid, curTimestamp)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarPendants failed, dao.GetValidUserAvatarPendants failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.AvatarPendantGetListFailed, "get user avatar pendant failed")
	}
	// 获取所有挂件数据
	allAvatarPendants, err := GetAllAvatarPendants(c)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarPendants failed, dao.GetValidUserAvatarPendants failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.AvatarPendantGetListFailed, "get user avatar pendant failed")
	}

	var isWearedAvatarPendant *user_pb.UserAvatarPendantItem
	var ownedAvatarPendants []*user_pb.UserAvatarPendantItem = make([]*user_pb.UserAvatarPendantItem, 0)
	var notOwnedAvatarPendants []*user_pb.UserAvatarPendantItem = make([]*user_pb.UserAvatarPendantItem, 0)
	intCacheTime := 24 * time.Hour
	// 已经排好序的挂件
	for _, allAvatarPendantItem := range allAvatarPendants {
		isUserAvaterFound := false
		curTitleLanguage := ""
		curConditionLanguage := ""
		for _, languageItem := range allAvatarPendantItem.Languages {
			if languageItem.Language == language {
				curTitleLanguage = languageItem.Title
				curConditionLanguage = languageItem.Condition
				break
			} else if languageItem.Language == "en" {
				// 默认英语
				if curConditionLanguage == "" && curTitleLanguage == "" {
					curTitleLanguage = languageItem.Title
					curConditionLanguage = languageItem.Condition
				}

			}
		}
		for _, userValidAvatarPendantItem := range userValidAvatarPendants {
			// 状态是已穿戴，且生效时间大于当前时间
			var isWeared int32
			if userValidAvatarPendantItem.IsWeared == 1 && userValidAvatarPendantItem.ValidEndAt > curTimestamp {
				isWeared = 1
			}
			if userValidAvatarPendantItem.AvatarPendantID == allAvatarPendantItem.ID {
				userValidDuration := userValidAvatarPendantItem.ValidEndAt - curTimestamp
				d := time.Duration(userValidDuration) * time.Second
				if d < intCacheTime {
					intCacheTime = d
				}
				// 找到当前用户对应的挂件
				isUserAvaterFound = true
				curLanguageAvaterItem := &user_pb.UserAvatarPendantItem{
					Id:            allAvatarPendantItem.ID,
					Title:         curTitleLanguage,
					AvatarPendant: allAvatarPendantItem.Icon,
					Condition:     curConditionLanguage,
					JumpUrl:       allAvatarPendantItem.JumpURL,
					IsWeared:      isWeared,
					ValidEndAt:    userValidAvatarPendantItem.ValidEndAt,
					ValidBeginAt:  userValidAvatarPendantItem.ValidBeginAt,
					IsOwned:       1,
					IsPermanent:   allAvatarPendantItem.IsPermanent,
				}
				// 已穿戴单独存储
				if isWeared == 1 {
					// 已穿戴的挂件只有一条
					if isWearedAvatarPendant != nil {
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetUserAvatarPendants failed, isWearedAvatarPendant is not nil, intlOpenid: %d", intlOpenid)
						curLanguageAvaterItem.IsWeared = 0
						// return nil, errs.NewCustomError(c, code.AvatarPendantGetListFailed, "isWearedAvatarPendant is not nil")
					} else {
						isWearedAvatarPendant = curLanguageAvaterItem
					}

				} else if allAvatarPendantItem.IsDel == 0 {
					// 未穿戴且未被删除，添加
					ownedAvatarPendants = append(ownedAvatarPendants, curLanguageAvaterItem)
				}
				break
			}
		}
		// log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetUserAvatarPendants , intlOpenid: %s, allAvatarPendantItem: %v", intlOpenid, allAvatarPendantItem)
		// 未找到，且未被删除，添加
		if !isUserAvaterFound && allAvatarPendantItem.IsDel == 0 {
			notOwnedAvatarPendants = append(notOwnedAvatarPendants, &user_pb.UserAvatarPendantItem{
				Id:            allAvatarPendantItem.ID,
				Title:         curTitleLanguage,
				AvatarPendant: allAvatarPendantItem.Icon,
				Condition:     curConditionLanguage,
				JumpUrl:       allAvatarPendantItem.JumpURL,
				IsWeared:      0,
				ValidEndAt:    0,
				ValidBeginAt:  0,
				IsOwned:       0,
				IsPermanent:   allAvatarPendantItem.IsPermanent,
			})
		}
	}
	// 组合数据
	if isWearedAvatarPendant == nil {
		userAvatarPendants = append(ownedAvatarPendants, notOwnedAvatarPendants...)
	} else {
		userAvatarPendants = append([]*user_pb.UserAvatarPendantItem{isWearedAvatarPendant}, append(ownedAvatarPendants, notOwnedAvatarPendants...)...)
	}
	// 返回分页数据
	// 解析出分页开始位置
	var startIndex int64
	if nextPageCursor != "" {
		index, err := util.DecryptPageCursorI(nextPageCursor)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarPendants failed, util.DecryptPageCursorI failed, err: %v", err)
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
		}
		startIndex = index
	}

	if limit == 0 {
		rsp.PageInfo.NextPageCursor = ""
		rsp.PageInfo.IsFinish = true
		rsp.UserAvatarPendants = userAvatarPendants
	} else {
		isFinished := false
		for i, userAvatarPendantItem := range userAvatarPendants {
			i64 := int64(i)
			isFinished = i64 >= int64(len(userAvatarPendants))-1
			if i64 >= startIndex && i64 < startIndex+limit {
				rspUserAvatarPendants = append(rspUserAvatarPendants, userAvatarPendantItem)
			} else if i64 >= startIndex+limit {
				rsp.UserAvatarPendants = rspUserAvatarPendants
				break
			}
		}
		if isFinished {
			rsp.PageInfo.PreviousPageCursor = previousPageCursor
			rsp.PageInfo.NextPageCursor = ""
			rsp.PageInfo.IsFinish = true
			rsp.UserAvatarPendants = rspUserAvatarPendants
		} else {
			next, err := util.EncryptPageCursorI(startIndex + limit)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarPendants failed, util.EncryptPageCursorI failed, err: %v", err)
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetUserAvatarPendants | Failed to create comments nextPageCursor")
			}
			rsp.PageInfo.PreviousPageCursor = previousPageCursor
			rsp.PageInfo.NextPageCursor = next
			rsp.PageInfo.IsFinish = false
		}
	}
	// 写入缓存
	go func(intCacheTime time.Duration) {
		defer recovery.CatchGoroutinePanic(context.Background())
		if data, jErr := json.Marshal(rsp); jErr == nil {
			redis.GetClient().SetEX(context.Background(), userAvatarPendantsKey, data, intCacheTime+1*time.Minute)
			AddToUserInAvatarPendantCacheList(context.Background(), intlOpenid)
			AddAvatarPendantRequestParamsCacheList(context.Background(), userAvatarPendantsKey)
		}
	}(intCacheTime)
	go RemoveCurWearAvatarPendant(c, []string{intlOpenid})
	return rsp, nil
}

// 获取挂件列表V2版本
func GetUserAvatarPendantsV2(c context.Context, intlOpenid string, language string, nextPageCursor string, limit int64, previousPageCursor string) (rsp *user_pb.GetUserAvatarPendantListRsp, err error) {
	rsp = &user_pb.GetUserAvatarPendantListRsp{
		UserAvatarPendants: make([]*user_pb.UserAvatarPendantItem, 0),
		PageInfo: &user_pb.UserPageInfo{
			NextPageCursor: "",
			IsFinish:       false,
		},
	}
	userAvatarPendants := make([]*user_pb.UserAvatarPendantItem, 0)
	rspUserAvatarPendants := make([]*user_pb.UserAvatarPendantItem, 0)
	if intlOpenid == "" {
		return rsp, nil
	}
	// 获取当前用户未过期的挂件
	curTimestamp := time.Now().Unix()
	// 获取当前用户拥有的挂件
	userValidAvatarPendants, err := getValidAvatarPendantOfUser(c, intlOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarPendantsV2 failed, getValidAvatarPendantOfUser, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.AvatarPendantGetListFailed, "get user avatar pendant failed")
	}
	// 获取所有挂件数据
	allAvatarPendants, err := GetAllAvatarPendantList(c, true, false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarPendantsV2 failed, GetAllAvatarPendantList failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.AvatarPendantGetListFailed, "get user avatar pendant failed")
	}
	// 获取当前穿戴的气泡
	curWearedAvatarPendant, err := GetUserCurWearedAvatarPendant(c, intlOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarPendantsV2 failed, GetUserCurWearedAvatarPendant failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.AvatarPendantGetListFailed, "get user avatar pendant  failed")
	} else {
		if curWearedAvatarPendant != nil {
			// 若当前穿戴已删除，加入列表
			if curWearedAvatarPendant.IsDel == 1 {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Info("GetUserAvatarPendantsV2 found weared avatar pendant deleted, id: %d, intlOpenid: %d", curWearedAvatarPendant.ID, intlOpenid)
				allAvatarPendants = append([]*model.AvatarPendant{curWearedAvatarPendant}, allAvatarPendants...)
			}
		}
	}
	// 组装数据
	var isWearedAvatarPendant *user_pb.UserAvatarPendantItem
	var ownedAvatarPendants []*user_pb.UserAvatarPendantItem = make([]*user_pb.UserAvatarPendantItem, 0)
	var notOwnedAvatarPendants []*user_pb.UserAvatarPendantItem = make([]*user_pb.UserAvatarPendantItem, 0)
	intCacheTime := 24 * time.Hour
	// 已经排好序的挂件
	for _, allAvatarPendantItem := range allAvatarPendants {
		isUserAvaterFound := false
		curTitleLanguage := ""
		curConditionLanguage := ""
		for _, languageItem := range allAvatarPendantItem.Languages {
			if languageItem.Language == language {
				curTitleLanguage = languageItem.Title
				curConditionLanguage = languageItem.Condition
				break
			} else if languageItem.Language == "en" {
				// 默认英语
				if curConditionLanguage == "" && curTitleLanguage == "" {
					curTitleLanguage = languageItem.Title
					curConditionLanguage = languageItem.Condition
				}

			}
		}
		for _, userValidAvatarPendantItem := range userValidAvatarPendants {
			// 状态是已穿戴，且生效时间大于当前时间
			var isWeared int32
			if userValidAvatarPendantItem.IsWeared == 1 && userValidAvatarPendantItem.ValidEndAt > curTimestamp {
				isWeared = 1
			}
			if userValidAvatarPendantItem.AvatarPendantID == allAvatarPendantItem.ID {
				userValidDuration := userValidAvatarPendantItem.ValidEndAt - curTimestamp
				d := time.Duration(userValidDuration) * time.Second
				if d < intCacheTime {
					intCacheTime = d
				}
				// 找到当前用户对应的挂件
				isUserAvaterFound = true
				curLanguageAvaterItem := &user_pb.UserAvatarPendantItem{
					Id:            allAvatarPendantItem.ID,
					Title:         curTitleLanguage,
					AvatarPendant: allAvatarPendantItem.Icon,
					Condition:     curConditionLanguage,
					JumpUrl:       allAvatarPendantItem.JumpURL,
					IsWeared:      isWeared,
					ValidEndAt:    userValidAvatarPendantItem.ValidEndAt,
					ValidBeginAt:  userValidAvatarPendantItem.ValidBeginAt,
					IsOwned:       1,
					IsPermanent:   allAvatarPendantItem.IsPermanent,
				}
				// 已穿戴单独存储
				if isWeared == 1 {
					// 已穿戴的挂件只有一条
					if isWearedAvatarPendant != nil {
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetUserAvatarPendants failed, isWearedAvatarPendant is not nil, intlOpenid: %d", intlOpenid)
						curLanguageAvaterItem.IsWeared = 0
						// return nil, errs.NewCustomError(c, code.AvatarPendantGetListFailed, "isWearedAvatarPendant is not nil")
					} else {
						isWearedAvatarPendant = curLanguageAvaterItem
					}

				} else if allAvatarPendantItem.IsDel == 0 {
					// 未穿戴且未被删除，添加
					ownedAvatarPendants = append(ownedAvatarPendants, curLanguageAvaterItem)
				}
				break
			}
		}
		// log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetUserAvatarPendants , intlOpenid: %s, allAvatarPendantItem: %v", intlOpenid, allAvatarPendantItem)
		// 未找到，且未被删除，添加
		if !isUserAvaterFound && allAvatarPendantItem.IsDel == 0 {
			notOwnedAvatarPendants = append(notOwnedAvatarPendants, &user_pb.UserAvatarPendantItem{
				Id:            allAvatarPendantItem.ID,
				Title:         curTitleLanguage,
				AvatarPendant: allAvatarPendantItem.Icon,
				Condition:     curConditionLanguage,
				JumpUrl:       allAvatarPendantItem.JumpURL,
				IsWeared:      0,
				ValidEndAt:    0,
				ValidBeginAt:  0,
				IsOwned:       0,
				IsPermanent:   allAvatarPendantItem.IsPermanent,
			})
		}
	}
	// 组合数据
	if isWearedAvatarPendant == nil {
		userAvatarPendants = append(ownedAvatarPendants, notOwnedAvatarPendants...)
	} else {
		userAvatarPendants = append([]*user_pb.UserAvatarPendantItem{isWearedAvatarPendant}, append(ownedAvatarPendants, notOwnedAvatarPendants...)...)
	}
	// 返回分页数据
	// 解析出分页开始位置
	var startIndex int64
	if nextPageCursor != "" {
		index, err := util.DecryptPageCursorI(nextPageCursor)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarPendants failed, util.DecryptPageCursorI failed, err: %v", err)
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
		}
		startIndex = index
	}

	if limit == 0 {
		rsp.PageInfo.NextPageCursor = ""
		rsp.PageInfo.IsFinish = true
		rsp.UserAvatarPendants = userAvatarPendants
	} else {
		isFinished := false
		for i, userAvatarPendantItem := range userAvatarPendants {
			i64 := int64(i)
			isFinished = i64 >= int64(len(userAvatarPendants))-1
			if i64 >= startIndex && i64 < startIndex+limit {
				rspUserAvatarPendants = append(rspUserAvatarPendants, userAvatarPendantItem)
			} else if i64 >= startIndex+limit {
				rsp.UserAvatarPendants = rspUserAvatarPendants
				break
			}
		}
		if isFinished {
			rsp.PageInfo.PreviousPageCursor = previousPageCursor
			rsp.PageInfo.NextPageCursor = ""
			rsp.PageInfo.IsFinish = true
			rsp.UserAvatarPendants = rspUserAvatarPendants
		} else {
			next, err := util.EncryptPageCursorI(startIndex + limit)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarPendants failed, util.EncryptPageCursorI failed, err: %v", err)
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetUserAvatarPendants | Failed to create comments nextPageCursor")
			}
			rsp.PageInfo.PreviousPageCursor = previousPageCursor
			rsp.PageInfo.NextPageCursor = next
			rsp.PageInfo.IsFinish = false
		}
	}
	return rsp, nil
}

// 获取有效的用户挂件
func getValidAvatarPendantOfUser(c context.Context, intlOpenid string) (validAvatarPendants []*model.UserAvatarPendant, err error) {
	curTimestamp := time.Now().Unix()
	validAvatarPendants = make([]*model.UserAvatarPendant, 0)
	// 1.先从换从中获取
	userValidAvatarPendantCachekey := cache.GetUserValidAvatarPendantsCacheKey(intlOpenid)
	userValidAvatarPendantsCacheStr, err := redis.GetClient().Get(c, userValidAvatarPendantCachekey).Result()
	if err == nil && userValidAvatarPendantsCacheStr != "" {
		umErr := json.Unmarshal([]byte(userValidAvatarPendantsCacheStr), &validAvatarPendants)
		if umErr == nil {
			return validAvatarPendants, nil
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getValidAvatarPendantOfUser failed, json.Unmarshal failed, intlOpenid: %s err: %v", intlOpenid, umErr)
		}
	} else {
		if err != nil && !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getValidAvatarPendantOfUser failed, redis.GetClient().Get failed,key: %s, err: %v", userValidAvatarPendantCachekey, err)
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("getValidAvatarPendantOfUser from cache failed,key: %s, err: %v", userValidAvatarPendantCachekey, err)
	}
	// 2.从db中获取
	validAvatarPendants, err = dao.GetValidUserAvatarPendants(intlOpenid, curTimestamp)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getValidAvatarPendantOfUser failed, dao.GetValidUserAvatarPendants failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.AvatarPendantGetListFailed, "get user avatar pendant failed")
	}
	// 3.设置缓存
	var minValidateEndTime = time.Now().Unix()
	minValidateEndTime = 3786883200

	var cacheTime = time.Duration(24 * time.Hour)
	if len(validAvatarPendants) > 0 {
		for _, validAvatarPendant := range validAvatarPendants {
			if validAvatarPendant.ValidEndAt < minValidateEndTime {
				minValidateEndTime = validAvatarPendant.ValidEndAt
			}
		}
	}
	// 获取最小的有效截止时间: 最多 24 小时 最少 1分钟
	diffTime := minValidateEndTime - curTimestamp
	if diffTime > 60 && diffTime <= 24*60*60 {
		cacheTime = time.Duration(diffTime) * time.Second
	} else if diffTime > 0 && diffTime > 24*60*60 {
		cacheTime = 24 * time.Hour
	} else {
		cacheTime = 1 * time.Minute
	}
	cacheStr, mErr := json.Marshal(validAvatarPendants)
	if mErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getValidAvatarPendantOfUser failed, json.Marshal failed, intlOpenid: %s err: %v", intlOpenid, mErr)
	} else {
		err = redis.GetClient().Set(c, userValidAvatarPendantCachekey, string(cacheStr), cacheTime).Err()
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getValidAvatarPendantOfUser failed, redis.GetClient().Set failed,key: %s, val: %s, err: %v", userValidAvatarPendantCachekey, string(cacheStr), err)
		}
	}
	return validAvatarPendants, err
}

// 设置穿戴头像挂件
func SetUserAvatarPendants(c context.Context, intlOpenid string, avatarPendantId int64, setWearStatus int32) (err error) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SetUserAvatarPendants, intlOpenid: %s, avatarPendantId: %d,setWearStatus: %d", intlOpenid, avatarPendantId, setWearStatus)
	// 取消穿戴
	if setWearStatus == 0 {
		err = dao.CancleWearUserAvatarPendant(intlOpenid, avatarPendantId)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetUserAvatarPendants failed, dao.CancleWearUserAvatarPendant failed, err: %v", err)
			return errs.NewSystemError(c, errs.ErrorTypeMysql, code.SetAvatarPendantToUserFailed, "set avatar pendant failed")
		}
	} else {
		// 获取用户头像
		usrAvatarPendant, err := dao.GetUserAvatarPendant(intlOpenid, avatarPendantId)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetUserAvatarPendants failed, dao.GetUserAvatarPendant failed, err: %v", err)
			return errs.NewSystemError(c, errs.ErrorTypeMysql, code.SetAvatarPendantToUserFailed, "get user avatar pendant failed")
		}
		if usrAvatarPendant == nil {
			return errs.NewCustomError(c, code.SetAvatarPendantToUserFailed, "avatar pendant not found")
		}
		// 未获得挂件
		if usrAvatarPendant.ValidEndAt < time.Now().Unix() {
			return errs.NewCustomError(c, code.AvatarPendantNotAvalible, "avatar pendant is expired")
		}
		// 先更新db
		_, err = dao.SetWearUserAvatarPendant(intlOpenid, avatarPendantId)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetUserAvatarPendants failed, dao.SetWearUserAvatarPendant failed, err: %v", err)
			return errs.NewSystemError(c, errs.ErrorTypeMysql, code.SetAvatarPendantToUserFailed, "set avatar pendant failed")
		}
	}

	// 清理缓存
	cacheKey := cache.GetUserWearedAvatarPendantKey(intlOpenid)
	redis.GetClient().Del(c, cacheKey)
	go BatchRemoveUserAvatarPendantCache(context.Background(), []string{intlOpenid})
	DeleteUserInfoCache(c, intlOpenid)
	// 清空用户帖子相关的缓存
	go cache.RemoveUserPostCacheKeys(intlOpenid)
	go cache.RemoveUserCommentCacheKeys(intlOpenid)
	// 设置成功，清空缓存数据
	return nil
}

func GetUserCurWearedAvatarPendant(c context.Context, intlOpenid string) (userAvatatPendant *model.AvatarPendant, err error) {
	if intlOpenid == "" {
		return nil, nil
	}
	intlOpenidsKey := cache.GetUserWearAvatarPendantIntlOpenidsKey()
	// 先从缓存中获取
	userWearedAvatarPendantKey := cache.GetUserWearedAvatarPendantKey(intlOpenid)
	avatatPendantStr, err := redis.GetClient().Get(c, userWearedAvatarPendantKey).Result()
	if err == nil {
		if avatatPendantStr == "" {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant not wear, intlOpenid: %s, avatatPendantStr: %s", intlOpenid, avatatPendantStr)
			return nil, nil
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant from cache success, intlOpenid: %s, avatatPendantStr: %s", intlOpenid, avatatPendantStr)
		umErr := json.Unmarshal([]byte(avatatPendantStr), &userAvatatPendant)
		if umErr == nil {
			redis.GetClient().SAdd(c, intlOpenidsKey, intlOpenid).Result()
			return userAvatatPendant, nil
		} else {
			// unmarshal 失败,可能是格式发生变化，重新生成
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, json.Unmarshal failed, intlOpenid: %s err: %v", intlOpenid, umErr)
			// return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetCurWearedCommentBubbleError, "get comment bubble failed")
		}
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetUserCurWearedAvatarPendant failed, redis.GetClient().Get failed, err: %v", err)
	}
	// db获取
	userWearedAvatarPendant, err := dao.GetWearedUserAvatarPendant(intlOpenid)
	if err != nil {
		// 没有有效穿戴
		if errors.Is(err, gorm.ErrRecordNotFound) {
			redis.GetClient().SetNX(c, userWearedAvatarPendantKey, "", 2*time.Minute)
			return nil, nil
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, dao.GetWearedUserAvatarPendant failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetAvatarPendantFailed, "get avatar pendant failed")
	}
	avatarPendant, err := dao.GetAvatarPendantInfo(userWearedAvatarPendant.AvatarPendantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			redis.GetClient().SetNX(c, userWearedAvatarPendantKey, "", 2*time.Minute)
			return nil, nil
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, dao.GetAvatarPendantInfo failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetAvatarPendantFailed, "get avatar pendant  failed")
	}

	avatarPendantByte, err := json.Marshal(avatarPendant)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, json.Marshal failed, intlOpenid: %s err: %v", intlOpenid, err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetAvatarPendantFailed, "get avatar pendant  failed")
	}
	cacheTime := int64(userWearedAvatarPendant.ValidEndAt - time.Now().Unix())
	if cacheTime <= 0 {
		cacheTime = 2 * 60
	} else if cacheTime > 24*60*60 {
		cacheTime = 24 * 60 * 60
	}

	_, err = redis.GetClient().SetEX(c, userWearedAvatarPendantKey, string(avatarPendantByte), time.Duration(cacheTime)*time.Second).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, redis.GetClient().SetEX failed, err: %v", err)
	} else {
		_, err := redis.GetClient().SAdd(c, intlOpenidsKey, intlOpenid).Result()
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, redis.GetClient().SAdd failed,intlOpenidsKey: %s, intlOpenid%s, err: %v", intlOpenidsKey, intlOpenid, err)
		}
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant success, intlOpenid: %s, avatarPendant: %s, id: %d", intlOpenid, string(avatarPendantByte), avatarPendant.ID)
	return avatarPendant, nil
}

// 获取当前穿戴的头像挂件
func GetUserCurWearedAvatarPendantIcon(c context.Context, intlOpenid string) (avatatPendant string, err error) {
	if intlOpenid == "" {
		return "", nil
	}
	userAvatarPendant, err := GetUserCurWearedAvatarPendant(c, intlOpenid)
	if err != nil || userAvatarPendant == nil {
		return "", nil
	}
	return userAvatarPendant.Icon, nil
	// // 先从缓存中获取
	// userWearedAvatarPendantKey := cache.GetUserWearedAvatarPendantKey(intlOpenid)
	// avatatPendant, err = redis.GetClient().Get(c, userWearedAvatarPendantKey).Result()
	// if err == nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant from cache success, intlOpenid: %s, avatarPendant: %s", intlOpenid, avatatPendant)
	// 	return avatatPendant, nil
	// } else {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetUserCurWearedAvatarPendant failed, redis.GetClient().Get failed, err: %v", err)
	// }
	// // db获取
	// userAvatarPendant, err := dao.GetWearedUserAvatarPendant(intlOpenid)
	// if err != nil {
	// 	if errors.Is(err, gorm.ErrRecordNotFound) {
	// 		// 删除这个key,过期时间有点延迟
	// 		redis.GetClient().Del(c, userWearedAvatarPendantKey)
	// 		return "", nil
	// 	}
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, dao.GetUserCurWearedAvatarPendant failed, err: %v", err)
	// 	return "", errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetAvatarPendantFailed, "get avatar pendant failed")
	// }
	// avatarPendant, err := dao.GetAvatarPendantInfo(userAvatarPendant.AvatarPendantID)
	// if err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant failed, dao.GetAvatarPendantInfo failed, err: %v", err)
	// 	return "", errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetAvatarPendantFailed, "get avatar pendant failed")
	// }
	// cacheTime := int64(userAvatarPendant.ValidEndAt - time.Now().Unix())
	// redis.GetClient().SetEX(c, userWearedAvatarPendantKey, avatarPendant.Icon, time.Duration(cacheTime)*time.Second)
	// log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCurWearedAvatarPendant success, intlOpenid: %s, avatarPendant: %s, id: %d", intlOpenid, avatarPendant.Icon, avatarPendant.ID)
	// return avatarPendant.Icon, nil
}

func RemoveCurWearAvatarPendant(c context.Context, intlOpenids []string) {
	defer recovery.CatchGoroutinePanic(c)
	for _, intlOpenid := range intlOpenids {
		userWearedAvatarPendantKey := cache.GetUserWearedAvatarPendantKey(intlOpenid)
		redis.GetClient().Del(c, userWearedAvatarPendantKey)
	}
}

// 用户是否在挂件缓存列表中
func IsUserInAvatarPendantCacheList(c context.Context, intlOpenid string) (isInCache bool, err error) {
	// 先从缓存中获取
	userOpenidsAvatarPendantKey := cache.GetUserIntlOpenidsInAvatarPendantCacheKey()
	isInCache, err = redis.GetClient().SIsMember(c, userOpenidsAvatarPendantKey, intlOpenid).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("IsUserInAvatarPendantCacheList failed, redis.GetClient().SIsMember failed, err: %v", err)
		return false, err
	}
	return isInCache, nil
}

// 挂件请求列表参数是否在缓存列表中
func IsRequestParamsInAvatarPendantCacheList(c context.Context, cacheKey string) (isInCache bool, err error) {
	userOpenidsAvatarPendantKey := cache.GetUserAvatarPendantListCacheParamsCacheKey()
	isInCache, err = redis.GetClient().SIsMember(c, userOpenidsAvatarPendantKey, cacheKey).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("IsRequestParamsInAvatarPendantCacheList failed, redis.GetClient().SIsMember failed, err: %v", err)
		return false, err
	}
	return isInCache, nil
}

// 将用户添加到缓存列表
func AddToUserInAvatarPendantCacheList(c context.Context, userIntlOpenid string) {
	userOpenidsAvatarPendantKey := cache.GetUserIntlOpenidsInAvatarPendantCacheKey()
	redis.GetClient().SAdd(c, userOpenidsAvatarPendantKey, userIntlOpenid)
}

// 将请求参数添加到缓存列表
func AddAvatarPendantRequestParamsCacheList(c context.Context, cacheKey string) {
	userOpenidsAvatarPendabtKey := cache.GetUserAvatarPendantListCacheParamsCacheKey()
	redis.GetClient().SAdd(c, userOpenidsAvatarPendabtKey, cacheKey)
}

// 清除用户挂件缓存
func BatchRemoveUserAvatarPendantCache(c context.Context, userIntlOpenids []string) {
	c = context.Background()
	defer recovery.CatchGoroutinePanic(c)
	for _, userIntlOpenid := range userIntlOpenids {
		userOpenidsAvatarPendantKey := cache.GetUserIntlOpenidsInAvatarPendantCacheKey()
		redis.GetClient().SRem(c, userOpenidsAvatarPendantKey, userIntlOpenid)
		// 用户挂件关系
		userValidAvatarPendantsCacheKey := cache.GetUserValidAvatarPendantsCacheKey(userIntlOpenid)
		redis.GetClient().Del(c, userValidAvatarPendantsCacheKey)
	}
}

// 清除所有用户的缓存
func RemoveAllUserAvatarPendantCache(c context.Context) {
	allDataKey := cache.GetUserIntlOpenidsInAvatarPendantCacheKey()
	redis.GetClient().Del(c, allDataKey)
	requestParamsKey := cache.GetUserAvatarPendantListCacheParamsCacheKey()
	redis.GetClient().Del(c, requestParamsKey)
	//
	allAvatarPendantListCacheKeyFF := cache.GetAllAvatarPendantListCacheKey(false, false)
	allAvatarPendantListCacheKeyFT := cache.GetAllAvatarPendantListCacheKey(false, true)
	allAvatarPendantListCacheKeyTF := cache.GetAllAvatarPendantListCacheKey(true, false)
	allAvatarPendantListCacheKeyTT := cache.GetAllAvatarPendantListCacheKey(true, true)
	redis.GetClient().Del(c, allAvatarPendantListCacheKeyFF, allAvatarPendantListCacheKeyFT, allAvatarPendantListCacheKeyTF, allAvatarPendantListCacheKeyTT)
	// 用户穿戴数拒
	intlOpenidsKey := cache.GetUserWearAvatarPendantIntlOpenidsKey()
	members, err := redis.GetClient().SMembers(c, intlOpenidsKey).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveAllUserAvatarPendantCache failed, redis.GetClient().SMembers failed, err: %v", err)
		return
	}
	RemoveCurWearAvatarPendant(c, members)
	redis.GetClient().Del(c, intlOpenidsKey)
}

// 获取所有挂件数据
func GetAllAvatarPendants(c context.Context) (avatarPendants []*model.AvatarPendant, err error) {
	// avatarPendants = make([]model.AvatarPendant, 0)
	// allDataKey := cache.GetAvatarPendantsAllDataKey()
	// if result, cErr := redis.GetClient().Get(context.Background(), allDataKey).Result(); cErr == nil {
	// 	cErr = json.Unmarshal([]byte(result), &avatarPendants)
	// 	if cErr != nil {
	// 		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetAllAvatarPendants failed, json.Unmarshal failed, err: %v", cErr)
	// 		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.AvatarPendansGetListFailedToJsonDecode, "get all avatar pendant failed")
	// 	}
	// 	return avatarPendants, nil
	// }
	// // 缓存中没有数据，从数据库中获取
	avatarPendants, err = dao.GetAllAvatarPendanDataWithLanguage(true)
	// for _, avatarPendant := range avatarPendants {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("GetAllAvatarPendants, avatarPendant: %d", avatarPendant.IsDel)
	// }
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetAllAvatarPendants failed, dao.GetAllAvatarPendanData failed, err: %v", err)
		return nil, errs.NewSystemError(c, errs.ErrorTypeMysql, code.AvatarPendantGetListFailed, "get all avatar pendant failed")
	}
	// // 写入缓存
	// go func() {
	// 	defer recovery.CatchGoroutinePanic(context.Background())
	// 	if data, jErr := json.Marshal(avatarPendants); jErr == nil {
	// 		redis.GetClient().SetEX(context.Background(), allDataKey, data, time.Hour*24)
	// 	}
	// }()
	return
}

func GetAllAvatarPendantList(ctx context.Context, withLanguage bool, withDeleted bool) (avatarpendants []*model.AvatarPendant, err error) {
	avatarpendants = []*model.AvatarPendant{}
	// 1. 先从cache中获取
	cacheKey := cache.GetAllAvatarPendantListCacheKey(withDeleted, withLanguage)
	if cacheKey == "" {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllAvatarPendantList cacheKey is empty")
		// return
	} else {
		result, err := redis.GetClient().Get(ctx, cacheKey).Result()
		if err != nil && !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllAvatarPendantList error: %s", err.Error())
		} else {
			err := json.Unmarshal([]byte(result), avatarpendants)
			if err != nil {
				log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllAvatarPendantList unmarshal error: %s", err.Error())
			} else {
				return avatarpendants, nil
			}
		}
	}
	var avatarPendantsDB = []*model.AvatarPendant{}
	// 2. DB中获取
	if withLanguage {
		avatarPendantsDB, err = dao.GetAllAvatarPendanDataWithLanguage(withDeleted)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllAvatarPendantList error: %s", err.Error())
			return avatarPendantsDB, err
		}
	} else {
		avatarPendantsDB, err = dao.GetAllAvatarPendanDataWithLanguage(withDeleted)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllAvatarPendantList error: %s", err.Error())
			return avatarpendants, err
		}
	}
	// 3. 写入缓存
	if cacheKey != "" {
		avatarPendantsStr, err := json.Marshal(avatarPendantsDB)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllAvatarPendantList unmarshal error: %s", err.Error())
		} else {
			redis.GetClient().Set(ctx, cacheKey, string(avatarPendantsStr), 10*time.Minute)
		}
	}
	return avatarPendantsDB, nil
}
