// Package taristmp 泰瑞斯临时活动
package taristmp

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	ES "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/elasticsearch"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	addressPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_address"
	LotteryPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_lottery"
	MissionPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_mission"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	"git.woa.com/gpts/baselib/crypto"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_taristmp"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/model/address"
	"trpc.act.logicial/app/model/base"
	generalModel "trpc.act.logicial/app/model/general"
	"trpc.act.logicial/app/model/taris"
)

// ClearInstanceDataInfo TODO
type ClearInstanceDataInfo struct {
	Data []TarisServiceDupTimeRank `json:"data"`
}

// TarisServiceDupTimeRank TODO
type TarisServiceDupTimeRank struct {
	DtEventTime    string `json:"dteventtime"`
	IZoneAreaID    string `json:"iZoneareaiD"`
	RoleProfession string `json:"roleprofession"`
	UsedTime       string `json:"usedtime"`
	VGuildID       string `json:"vguildid"`
	VGuildName     string `json:"vguildname"`
	VOpenID        string `json:"vopenid"`
	VRoleID        string `json:"vroleid"`
	VRoleName      string `json:"vrolename"`
}

// CheckTaskReadyInfo TODO
type CheckTaskReadyInfo struct {
	Data ServiceTaskReadyInfo `json:"data"`
}

// Dragon1000Info TODO
type Dragon1000Info struct {
	Data ServiceDragon1000Info `json:"data"`
}

// ServiceTaskReadyInfo TODO
type ServiceTaskReadyInfo struct {
	Resultxinxi string `json:"resultxinxi"`
}

// ServiceDragon1000Info TODO
type ServiceDragon1000Info struct {
	GuildXinXi string `json:"guildxinxi"`
}

// GuildXinXi TODO
type GuildXinXi []GuildXinXiItem

// GuildXinXiItem TODO
type GuildXinXiItem struct {
	A1 string `json:"a1"` // vguildid
	A2 string `json:"a2"` // vguildname
	A3 string `json:"a3"` // guildchairmanid
	A4 string `json:"a4"` // izoneareaid
	A5 string `json:"a5"` // vrolename
	A6 string `json:"a6"` // sumpoint
	A7 string `json:"a7"` // lasttime
}

// ResultXinxi TODO
type ResultXinxi []ResultXinxiItem

// ResultXinxiItem TODO
type ResultXinxiItem struct {
	Key30310 int `json:"30310"`
	Key30330 int `json:"30330"`
	Key30350 int `json:"30350"`
	Key30360 int `json:"30360"`
	Key30370 int `json:"30370"`
	Key30380 int `json:"30380"`
}

// DragonDataInfo TODO
type DragonDataInfo struct {
	Data *KillxinxiType `json:"data"`
}

// KillxinxiType TODO
type KillxinxiType struct {
	Killxinxi string `json:"killxinxi"`
}

// KillxinxiItem TODO
type KillxinxiItem struct {
	DupID       int64  `json:"dupid"`
	IZoneAreaID string `json:"izoneareaid"`
	KillCount   int64  `json:"killcount"`
	VGuildID    string `json:"vguildid"`
	VGuildName  string `json:"vguildname"`
	DtEventTime string `json:"dteventtime"`
}

// TimeInterval TODO
type TimeInterval struct {
	StartDate time.Time
	EndDate   time.Time
}

// 屠龙分金中映射
var mapIndexKey = map[int32]string{
	0: "time_points_1",
	1: "time_points_2",
	2: "time_points_3",
	3: "time_points_4",
	4: "time_points_5",
}

var loc = time.FixedZone("UTC+0", 0)
var utc7 = time.FixedZone("UTC+7", 7*60*60)

// 定义时间段
var timeIntervals = []TimeInterval{
	{time.Date(2024, 6, 24, 0, 0, 0, 0, utc7), time.Date(2024, 6, 28, 23, 59, 59, 59, utc7)},
	{time.Date(2024, 6, 29, 0, 0, 0, 0, utc7), time.Date(2024, 7, 5, 23, 59, 59, 59, utc7)},
	{time.Date(2024, 7, 6, 0, 0, 0, 0, utc7), time.Date(2024, 7, 12, 23, 59, 59, 59, utc7)},
	{time.Date(2024, 7, 13, 0, 0, 0, 0, utc7), time.Date(2024, 7, 19, 23, 59, 59, 59, utc7)},
	{time.Date(2024, 7, 20, 0, 0, 0, 0, utc7), time.Date(2024, 7, 26, 23, 59, 59, 59, utc7)},
	{time.Date(2024, 7, 27, 0, 0, 0, 0, utc7), time.Date(2024, 8, 2, 23, 59, 59, 59, utc7)},
}
var racingTimeIntervals = []TimeInterval{
	{time.Date(2024, 6, 21, 0, 0, 0, 0, loc), time.Date(2024, 7, 10, 23, 59, 59, 59, loc)},
	{time.Date(2024, 6, 21, 0, 0, 0, 0, loc), time.Date(2024, 7, 12, 23, 59, 59, 59, loc)},
	{time.Date(2024, 6, 21, 0, 0, 0, 0, loc), time.Date(2024, 7, 15, 23, 59, 59, 59, loc)},
	{time.Date(2024, 6, 21, 0, 0, 0, 0, loc), time.Date(2024, 7, 18, 23, 59, 59, 59, loc)},
	{time.Date(2024, 6, 21, 0, 0, 0, 0, loc), time.Date(2024, 7, 21, 23, 59, 59, 59, loc)},
	{time.Date(2024, 6, 21, 0, 0, 0, 0, loc), time.Date(2024, 7, 31, 23, 59, 59, 59, loc)},
}

// 还有副本id
var instanceIDs = []string{"30310", "30330", "30360", "30350", "30370", "30380"}

// SetCommHeader 河图数据头部
func SetCommHeader() map[string]string {
	nowStr := fmt.Sprintf("%d", time.Now().Unix())
	headers := map[string]string{}
	headers["X-NTM-Destination-Service"] = "dmfeature-12762"
	headers["X-NTM-Source-Service"] = "pa-api"
	headers["X-NTM-Timestamp"] = nowStr
	nonce := fmt.Sprintf("%x", md5.Sum([]byte(nowStr)))[16:22]
	headers["X-NTM-Nonce"] = nonce
	headers["Content-Type"] = "application/x-www-form-urlencoded"
	sign := strings.ToLower(fmt.Sprintf("%x", md5.Sum([]byte(
		fmt.Sprintf("%s,%s,%s,%s,%s",
			"dmfeature-12762", "pa-api", nowStr, nonce, "cd8fc24e-d27f-40a4-aaf0-085f74b31922")))))
	headers["X-NTM-Signature"] = sign
	return headers
}

// GetSelf 获取用户的数据
func GetSelf(ctx context.Context, instanceID int32, roleInfo *gamePb.RoleInfo) (self *pb.SelfMsg, err error) {
	// 获取对应服务器 查询角色 在查询对应的数据
	self = &pb.SelfMsg{}
	condition := &taris.TarisRankTemp{
		RoleID:     roleInfo.RoleId,
		AreaID:     roleInfo.AreaId,
		ZoneID:     roleInfo.ZoneId,
		InstanceID: instanceIDs[instanceID-3],
	}

	var data taris.TarisRankTemp
	result := DB.DefaultConnect().Debug().WithContext(ctx).Table(taris.TarisRankTemp{}.TableName()).
		Where(condition).Order("clear_level_at desc").First(&data)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 没有找到记录
			return self, err
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return nil, err
	}

	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("[taris] get self: [%+v]",
		data))

	self.ClearLevelTimestamp = data.ClearLevelAt
	self.RoleName = data.RoleName
	self.UnionName = data.UnionName
	self.Service = fmt.Sprintf("%v", data.ZoneID)
	self.Rank = data.Rank
	return
}

// GetTotal 获取排行榜总数量
func GetTotal(ctx context.Context, index int32, instanceID int32) (total int32, err error) {
	areaIDMap := config.GetConfig().TarisAreaIDMap
	var count int64
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("taris get rank area: %v", areaIDMap[index]))
	result := DB.DefaultConnect().Debug().WithContext(ctx).Table(taris.TarisRankTemp{}.TableName()).
		Where("area_id in ? and instance_id = ?", areaIDMap[index], instanceIDs[instanceID-3]).
		// Where("area_id in ? and instance_id = ?", []int32{59}, instanceIDs[instanceID-3]).
		Count(&count)
	if result.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	total = int32(count)
	return
}

// GetRankList 获取指定副本的排行榜列表
func GetRankList(ctx context.Context, instanceID int32, pageSize int32, pageNum int32,
	areaIDs []int32) (rankList []*pb.RankList,
	err error) {
	rankList = make([]*pb.RankList, 0)
	// 在库表里查询
	condition := &taris.TarisRankTemp{
		InstanceID: instanceIDs[instanceID-3],
	}
	var data []taris.TarisRankTemp
	result := DB.DefaultConnect().Debug().WithContext(ctx).Table(taris.TarisRankTemp{}.TableName()).
		Where(condition).Where("area_id in ?", areaIDs).Order("`rank` asc").
		Limit(int(pageSize)).Offset((int(pageNum) - 1) * int(pageSize)).
		Find(&data)

	if result.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	for _, item := range data {
		rankList = append(rankList, &pb.RankList{
			Rank:                int64(item.Rank),
			RoleName:            item.RoleName,
			UnionName:           item.UnionName,
			ClearLevelTimestamp: item.ClearLevelAt,
			Service:             fmt.Sprintf("%v", item.ZoneID),
		})
	}

	return
}

// GetClearInstanceData 通过接口获取塔瑞斯开服竞速
func GetClearInstanceData(ctx context.Context, instanceId int32) (err error) {
	scheduleCtx := context.Background()
	var roleIDList = []*taris.TarisSignUpRoleTemp{}
	resultSQL := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisSignUpRoleTemp{}.TableName()).
		Find(&roleIDList)

	if resultSQL.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", resultSQL.Error)
		return
	}
	rankUrl := config.GetConfig().TarisGetClearInstanceUrl
	// // 判断时间段拉数据 判断当前时间段

	var groups [][]string
	groupSize := 30
	for i := 0; i < len(roleIDList); i += groupSize {
		end := i + groupSize
		if end > len(roleIDList) {
			end = len(roleIDList)
		}
		// 将子切片转换为 []string 类型
		var group []string
		for _, role := range roleIDList[i:end] {
			group = append(group, role.RoleID)
		}
		groups = append(groups, group)
	}

	// 判断时间
	timestamp := time.Now().UTC().Unix()
	areaIDMap := config.GetConfig().TarisAreaIDMap
	presentList := [][]string{
		{"Wand-20240528123946-P9c0933488230", "Wand-20240527123552-P393b434b56d6"}, // 鱼人国王
		{"Wand-20240528124032-Paf24246555b3", "Wand-20240527123637-Pc966a67249be"}, // 战争古树
		{"Wand-20240528124050-P5a17dde7e96b", "Wand-20240527123703-P06ee4a9f549d"}, // 合成博士
		{"Wand-20240528124115-Pf5b6b6dc4dd9", "Wand-20240527123724-P7330d9c9d97f"}, // 森林双子
		{"Wand-20240528124126-Pb347dcf8ed92", "Wand-20240527123737-P8fb9f3ef9c48"}, // 大德鲁伊
	}
	specialAMSList := [][]string{{
		"Wand-20240612102712-P9424c1daf3c6",
		"Wand-20240612102712-P9424c1daf3c6",
		"Wand-20240612102712-P9424c1daf3c6",
		"Wand-20240612102712-P9424c1daf3c6",
	}, {
		"Wand-20240527123755-P70ec92916641",
		"Wand-20240527123755-P70ec92916641",
		"Wand-20240527123755-P70ec92916641",
		"Wand-20240527123755-P70ec92916641",
	}}
	specialPhysicalList := [][]string{{
		"Wand-20240528124309-P0950fc09c937",
		"Wand-20240528124339-Pbb200bc3c4c7",
		"Wand-20240528124359-P60ce4c259e05",
		"Wand-20240528124429-P3f3acdd04fe1",
	}, {
		"Wand-20240528124259-P56ee1d5470ae",
		"Wand-20240612103013-P1072bee14fd1",
		"Wand-20240528124348-P99110e8223a5",
		"Wand-20240528124442-P01fee4f5c600",
	}}

	maxGoroutines := 90
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	for instanceIndex, id := range instanceIDs {
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] instanceID: %v", id))
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] instanceIndex: %v", instanceIndex))
		if (timestamp >= racingTimeIntervals[instanceIndex].StartDate.Unix() && timestamp <=
			racingTimeIntervals[instanceIndex].EndDate.Unix()) ||
			(timestamp > racingTimeIntervals[instanceIndex].EndDate.Unix() &&
				timestamp < racingTimeIntervals[instanceIndex].EndDate.Add(24*time.Hour).Unix()) ||
			int32(instanceIndex) == instanceId-1 {
			for _, group := range groups {
				wg.Add(1)
				go func(list []string) {
					semaphore <- struct{}{}
					var keys []string
					for _, roleID := range list {
						key := fmt.Sprintf("%s|%s", id, roleID)
						keys = append(keys, key)
					}

					resultKey := strings.Join(keys, ",")

					header := SetCommHeader()

					data := httpclient.ClientOption{
						URL: rankUrl,
						// Timeout: 2 * time.Second,
						Header:     header,
						Type:       "POST",
						PostString: fmt.Sprintf("keys=%v", url.QueryEscape(resultKey)),
					}
					result := httpclient.RequestOne(scheduleCtx, data)

					if result.RequestError != nil {
						// 请求失败
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeHttp, code.PubgHttpError,
							"http error, \t [Error]:{%v} {%v} ", rankUrl, result)
						log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] GetClearInstanceData err:%v",
							err.Error()))
					}
					response := result.Result
					var respData ClearInstanceDataInfo

					err = json.Unmarshal([]byte(response), &respData)
					if err != nil {
						log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] GetClearInstanceData err:%v",
							err.Error()))
					}
					// respData.Data 格式需要转化
					for _, item := range respData.Data {
						if item.UsedTime != "" {
							ClearLevelAt, err := strconv.ParseInt(item.UsedTime, 10, 64)
							if err != nil {
								log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] set ClearLevelAt error: %v",
									item.UsedTime))
								ClearLevelAt = 0
							}
							ZoneID, err := strconv.ParseInt(item.IZoneAreaID, 10, 64)
							if err != nil {
								log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] set ZoneID error: %v",
									item.IZoneAreaID))
								ZoneID = 0
							}
							AreaID := 0
							switch {
							case item.IZoneAreaID[0:2] == "35":
								AreaID = 12
							case item.IZoneAreaID[0:2] == "40":
								AreaID = 13
							case item.IZoneAreaID[0:2] == "45":
								AreaID = 14
							case item.IZoneAreaID[0:2] == "50":
								AreaID = 15
							default:
								log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] set AreaID error: %v",
									item.IZoneAreaID))
							}
							layout := "2006-01-02 15:04:05"
							t, _ := time.Parse(layout, item.DtEventTime)

							decodedBytes, err := base64.StdEncoding.DecodeString(item.VRoleName)
							if err != nil {
								log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] base64 error: %v",
									item.VRoleName))
							}

							insertDataItem := &taris.TarisRankTemp{
								UnionName:    item.VGuildName,
								RoleID:       item.VRoleID,
								RoleName:     string(decodedBytes),
								DtEventTime:  t.Unix(),
								ClearLevelAt: ClearLevelAt,
								AreaID:       int64(AreaID),
								ZoneID:       ZoneID,
								InstanceID:   id,
								Now:          timestamp,
							}
							// 以人为维度判断是否有重复有重复就干掉
							var first *taris.TarisRankTemp

							resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisRankTemp{}.TableName()).
								Where("area_id = ? and zone_id = ? and role_id = ? and instance_id = ?", insertDataItem.AreaID,
									insertDataItem.ZoneID,
									insertDataItem.RoleID, id).
								First(&first)

							if resultSQL.Error != nil {
								if errors.Is(resultSQL.Error, gorm.ErrRecordNotFound) {
									// 没有找到记录
									// 需要插入
									resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisRankTemp{}.TableName()).
										Save(&insertDataItem)
									if resultSQL.Error != nil {
										err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
											"mysql error, \t [Error]:{%v} ", resultSQL.Error)
										log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
											"[taris] GetClearInstanceData err:%v",
											err.Error()))
									}
								} else {
									err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
										"mysql error, \t [Error]:{%v} ", resultSQL.Error)
									log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
										"[taris] GetClearInstanceData err:%v",
										err.Error()))
								}
							}
							if first.RoleID != "" {
								// 需要更新
								if first.ClearLevelAt > insertDataItem.ClearLevelAt {
									first.ClearLevelAt = insertDataItem.ClearLevelAt
									first.Now = insertDataItem.Now
									first.DtEventTime = insertDataItem.DtEventTime
									first.RoleName = insertDataItem.RoleName
									first.UnionName = insertDataItem.UnionName
								}
								resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisRankTemp{}.TableName()).
									Where("id = ?", first.ID).
									Save(&first)
								if resultSQL.Error != nil {
									err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
										"mysql error, \t [Error]:{%v} ", resultSQL.Error)
									log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
										"[taris] GetClearInstanceData err:%v",
										err.Error()))
								}
							}

						}
					}
					// 等1s防止超qps
					time.Sleep(time.Second * 1)
					wg.Done()
					<-semaphore
				}(group)
			}
			wg.Wait()
			InsertClearInstanceData(scheduleCtx, id)
		}
		if timestamp > racingTimeIntervals[instanceIndex].EndDate.Unix() {
			// 在 taris_send_temp 表里添加记录即可
			// 查询前100名
			log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] start send"))
			for areaIndex, item := range areaIDMap {
				var topList []*taris.TarisRankTemp
				resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisRankTemp{}.TableName()).
					Where("area_id in ? and instance_id = ?", item, id).Limit(100).Order("`rank` asc").Find(&topList)
				if resultSQL.Error != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"mysql error, \t [Error]:{%v} ", resultSQL.Error)
					return err
				}
				for _, topItem := range topList {
					wg.Add(1)
					go func(index int, item *taris.TarisRankTemp) {
						semaphore <- struct{}{}
						FsourceId := "page-26622"
						if item.RoleID[0:2] == "50" {
							FsourceId = "page-26627"
						}
						// 查询对应uid
						var signUpItem taris.TarisSignUpRoleTemp
						resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisSignUpRoleTemp{}.TableName()).
							Where("role_id = ? and Fsource_id = ?", item.RoleID, FsourceId).First(&signUpItem)
						if resultSQL.Error != nil {
							if errors.Is(resultSQL.Error, gorm.ErrRecordNotFound) {
							} else {
								log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] GetClearInstanceData err:%v",
									err.Error()))
								err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
									"mysql error, \t [Error]:{%v} ", resultSQL.Error)
							}
						}

						log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] signUpItem: %v", signUpItem))
						if signUpItem.UID != "" {
							if instanceIndex != 5 {
								resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisSendTemp{}.TableName()).
									Where("uid = ? and tag = ? and Fsource_id = ?", signUpItem.UID, presentList[instanceIndex][areaIndex],
										FsourceId).
									FirstOrCreate(&taris.TarisSendTemp{
										UID:       signUpItem.UID,
										Status:    0,
										Tag:       presentList[instanceIndex][index],
										FsourceID: FsourceId,
										LangType:  signUpItem.LangType,
									})
								if resultSQL.Error != nil {
									log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
										"[taris] GetClearInstanceData err:%v",
										err.Error()))
									err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
										"mysql error, \t [Error]:{%v} ", resultSQL.Error)
								}
							} else {
								sendData := &taris.TarisSendTemp{
									UID:       signUpItem.UID,
									Status:    0,
									FsourceID: FsourceId,
									LangType:  signUpItem.LangType,
								}
								switch {
								case item.Rank >= 1 && item.Rank <= 10:
									sendData.Tag = specialAMSList[index][0]
								case item.Rank >= 11 && item.Rank <= 20:
									sendData.Tag = specialAMSList[index][1]
								case item.Rank >= 21 && item.Rank <= 30:
									sendData.Tag = specialAMSList[index][2]
								default:
									sendData.Tag = specialAMSList[index][3]
								}
								resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisSendTemp{}.TableName()).
									Where("uid = ? and tag = ? and Fsource_id = ?", sendData.UID, sendData.Tag, FsourceId).
									FirstOrCreate(&sendData)
								if resultSQL.Error != nil {
									log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
										"[taris] GetClearInstanceData err:%v",
										err.Error()))
									err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
										"mysql error, \t [Error]:{%v} ", resultSQL.Error)
								}
								otherSendData := &taris.TarisSendTemp{
									UID:       signUpItem.UID,
									Status:    0,
									FsourceID: FsourceId,
									LangType:  signUpItem.LangType,
								}
								switch {
								case item.Rank >= 1 && item.Rank <= 10:
									otherSendData.Tag = specialPhysicalList[index][0]
								case item.Rank >= 11 && item.Rank <= 20:
									otherSendData.Tag = specialPhysicalList[index][1]
								case item.Rank >= 21 && item.Rank <= 30:
									otherSendData.Tag = specialPhysicalList[index][2]
								default:
									otherSendData.Tag = specialPhysicalList[index][3]
								}
								resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisSendTemp{}.TableName()).
									Where("uid = ? and tag = ? and Fsource_id = ?", otherSendData.UID, otherSendData.Tag, FsourceId).
									FirstOrCreate(&otherSendData)
								if resultSQL.Error != nil {
									log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
										"[taris] GetClearInstanceData err:%v",
										err.Error()))
									err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
										"mysql error, \t [Error]:{%v} ", resultSQL.Error)
								}
							}
						}
						wg.Done()
						<-semaphore
					}(areaIndex, topItem)
				}
				wg.Wait()
			}
		}
	}

	if err != nil {
		return err
	}
	return
}

// InsertClearInstanceData 储存开服竞速数据
func InsertClearInstanceData(ctx context.Context, instanceID string) (err error) {
	scheduleCtx := context.Background()

	// 要区分不同区
	areaIDMap := config.GetConfig().TarisAreaIDMap
	var wg sync.WaitGroup
	maxGoroutines := 90
	semaphore := make(chan struct{}, maxGoroutines)

	for _, areaID := range areaIDMap {
		var list []*taris.TarisRankTemp
		// 添加rank字段
		result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisRankTemp{}.TableName()).
			Where("instance_id = ? and area_id IN ?", instanceID, areaID).
			Order("clear_level_at asc, dteventtime asc, role_name asc").Find(&list)

		if result.Error != nil {

			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql error, \t [Error]:{%v} ", result.Error)
			return err
		}

		for index, item := range list {
			wg.Add(1)
			go func(detail *taris.TarisRankTemp, num int32) {
				semaphore <- struct{}{}
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] update data: [%v - %v - %v]",
					instanceID, detail.ID, num+1))
				// 更新数据
				result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
					taris.TarisRankTemp{}.TableName()).
					Where("id = ? and instance_id = ?", detail.ID, instanceID).
					Updates(&taris.TarisRankTemp{
						Rank: num + 1,
					})
				if result.Error != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"mysql error, \t [Error]:{%v} ", result.Error)
					return
				}
				wg.Done()
				<-semaphore
			}(item, int32(index))
		}
	}
	wg.Wait()
	return
}

// CheckReadyTask 查询哪些任务完成了
func CheckReadyTask(ctx context.Context, FsourceID string) (err error) {
	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	var signUpInfo = taris.TarisSignUpRoleTemp{}
	resultSQL := DB.DefaultConnect().Debug().WithContext(ctx).Table(taris.TarisSignUpRoleTemp{}.TableName()).
		Where("uid = ? and Fsource_id = ?", userAccount.Uid, FsourceID).
		First(&signUpInfo)

	if resultSQL.Error != nil {
		if errors.Is(resultSQL.Error, gorm.ErrRecordNotFound) {
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", resultSQL.Error)
		return
	}
	// 获取table name
	accountTableName, err := model.GetTableNameWithAccount(ctx, &userAccount, base.BaseTotalLogModel{}.TableName())
	if err != nil {
		return
	}

	condition := base.BaseTotalLogModel{
		Uid:        userAccount.Uid,
		FsourceId:  FsourceID,
		StorageKey: "mission",
	}
	var list []base.BaseTotalLogModel
	db := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition).Find(&list)

	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	if signUpInfo.UID != "" && signUpInfo.RoleID != "" {
		mapTagNum := map[string]int64{
			"30310": 1,
			"30330": 1,
			"30350": 1,
			"30360": 1,
			"30370": 1,
			"30380": 3,
		}
		url := config.GetConfig().TarisTaskUrl
		header := SetCommHeader()
		data := httpclient.ClientOption{
			URL: fmt.Sprintf("%v?openid=%v&roleid=%v", url, strings.Split(signUpInfo.UID, "-")[1], signUpInfo.RoleID),
			// Timeout: 2 * time.Second,
			Header: header,
			Type:   "GET",
		}
		result := httpclient.RequestOne(ctx, data)
		if result.RequestError != nil {
			// 请求失败
			err = errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
				"http error, \t [Error]:{%v} ", url)
			return
		}
		response := result.Result
		var respData CheckTaskReadyInfo
		err = json.Unmarshal([]byte(response), &respData)
		if err != nil {
			return
		}

		var resultData ResultXinxi
		err = json.Unmarshal([]byte(respData.Data.Resultxinxi), &resultData)
		if err != nil {
			return
		}

		var wg sync.WaitGroup
		// 判断一下对应id是否完成
		for index, id := range instanceIDs {
			if !CheckHaveId(list, id) && CheckValue(resultData[0], id) {
				wg.Add(1)
				go func(tag string, index int32) {
					// 上报
					ReportTlog(ctx, "speedrun_backup_task_complete", "cm_click", 29157, userAccount.Uid, tag)
					// i就是对应的副本id
					lottery := LotteryPb.NewLotteryClientProxy()

					realAddNum, err := lottery.AddNumsLottery(ctx, &LotteryPb.AddNumsLotteryReq{
						FsourceId:  FsourceID,
						LotteryId:  int64(index + 3),
						DayLimit:   mapTagNum[tag],
						TotalLimit: mapTagNum[tag],
						AddNum:     mapTagNum[tag],
					})
					if err != nil {
						log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
							"[taris] CheckReadyTask err:%v",
							err.Error()))
					}

					if realAddNum.RealAddNum != 0 {
						mission := MissionPb.NewMissionClientProxy()
						MissionHasRecord, err := mission.AddFinishMission(ctx, &MissionPb.AddFinishMissionReq{
							FsourceId:  FsourceID,
							TagId:      fmt.Sprintf("%v", index+3),
							DayLimit:   1,
							TotalLimit: 1,
							TimeZone:   1,
						})
						if err != nil {
							log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
								"[taris] CheckReadyTask err:%v",
								err.Error()))
						}
						log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
							"[taris] update instance realNum: [%v], [%v]",
							realAddNum.RealAddNum, MissionHasRecord.MissionHasRecord))
					}

					wg.Done()
				}(id, int32(index))
			}
		}
		wg.Wait()
	}
	return
}

// CheckHaveId TODO
func CheckHaveId(list []base.BaseTotalLogModel, id string) (has bool) {
	has = false
	for _, item := range list {
		if item.TagId == id {
			has = true
			break
		}
	}
	return
}

// CheckValue TODO
func CheckValue(obj ResultXinxiItem, id string) (has bool) {
	has = false
	switch id {
	case "30310":
		if obj.Key30310 == 1 {
			has = true
		}
	case "30330":
		if obj.Key30330 == 1 {
			has = true
		}
	case "30350":
		if obj.Key30350 == 1 {
			has = true
		}
	case "30360":
		if obj.Key30360 == 1 {
			has = true
		}
	case "30370":
		if obj.Key30370 == 1 {
			has = true
		}
	case "30380":
		if obj.Key30380 == 1 {
			has = true
		}
	}
	return has
}

// ReportTlog 上报
func ReportTlog(ctx context.Context, action string, subAction string, gameId int32, openId string,
	taskID string) (err error) {
	langType := metadata.GetLangType(ctx)
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: langType,
			XGameId:   cast.ToInt(gameId),
			XSource:   "pc_web",
		},
		Action:         action,
		SubAction:      subAction,
		OriginalGameId: cast.ToString(gameId),
		ExtentContent: map[string]interface{}{
			"game_id": gameId,
			"open_id": openId,
			"task_id": taskID,
		},
	}
	report.ReportTlog(ctx, tlogData)
	log.WithFieldsContext(ctx, "log_type", action).Infof(
		"vote ReportTlog action:[%v],,gameId:[%v],openId:[%v], taskId: [%v] ",
		action, gameId, openId, taskID)
	return
}

// GetPresentList 获取开服竞速排行榜礼物列表
func GetPresentList(ctx context.Context, FsourceID string) (list []*pb.TarisPresentList, err error) {
	list = make([]*pb.TarisPresentList, 0)
	// 获取角色信息
	gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: FsourceID}
	gameProxy := gamePb.NewGameClientProxy()
	gameRoleInfo, err := gameProxy.GetSavedRoleInfo(ctx, gameReq)
	if err != nil {
		return nil, err
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("[taris] GetPresentList gameRoleInfo: [%v]",
		gameRoleInfo))

	var wg sync.WaitGroup
	scheduleCtx := context.Background()
	// 判断时间
	timestamp := time.Now().UTC().Unix()

	instanceList := instanceIDs
	if gameRoleInfo.AreaId == 11 {
		instanceList = asiaInstanceIDs
	}
	for index, value := range instanceList {
		// if true {
		if timestamp > speedAsiaTimeIntervals[index].EndDate.Unix() {
			wg.Add(1)
			go func(instanceID string, mapIndex int32) {
				// 查询数据库拿到排名
				var data taris.TarisRankTemp
				condition := &taris.TarisRankTemp{
					AreaID:     gameRoleInfo.AreaId,
					ZoneID:     gameRoleInfo.ZoneId,
					RoleID:     gameRoleInfo.RoleId,
					InstanceID: instanceID,
				}
				tableName := taris.TarisRankTemp{}.TableName()
				if gameRoleInfo.AreaId == 11 {
					tableName = "taris_rank_temp_asia"
				}
				result := DB.DefaultConnect().WithContext(scheduleCtx).Table(tableName).
					Where(condition).Find(&data)
				if result.Error != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"mysql error, \t [Error]:{%v} ", result.Error)
					log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] GetPresentList err: [%v]",
						err.Error()))
				}
				if data.Rank <= 100 && data.Rank > 0 {
					instanceId := mapIndex + 3
					if gameRoleInfo.AreaId == 11 {
						instanceId = mapIndex + 1
					}
					list = append(list, &pb.TarisPresentList{
						Rank:       data.Rank,
						InstanceId: instanceId,
					})
				}
				wg.Done()
			}(value, int32(index))
		}
	}
	wg.Wait()
	sort.SliceStable(list, func(i, j int) bool {
		return list[i].InstanceId < list[j].InstanceId
	})

	return
}

func mergeDataTemp(data1 []*taris.TarisDragonRaidBountyDetail,
	data2 []*taris.TarisDragonRaidBountyDetail) []*taris.TarisDragonRaidBountyDetail {
	a1Map := make(map[string]*taris.TarisDragonRaidBountyDetail)

	for _, obj := range data1 {
		a1Map[obj.VGuildId] = obj
	}

	for _, obj := range data2 {
		if oldObj, ok := a1Map[obj.VGuildId]; ok {
			oldObjSumPoint, _ := strconv.ParseInt(oldObj.SumPoint, 10, 64)
			objSumPoint, _ := strconv.ParseInt(obj.SumPoint, 10, 64)
			oldObj.SumPoint = fmt.Sprintf("%v", oldObjSumPoint+objSumPoint)
			a1Map[obj.VGuildId] = oldObj
		} else {
			a1Map[obj.VGuildId] = obj
		}
	}

	mergedData := make([]*taris.TarisDragonRaidBountyDetail, 0, len(a1Map))
	for _, obj := range a1Map {
		mergedData = append(mergedData, obj)
	}

	return mergedData
}

// GetDragonDataByHTTP 通过接口拉取屠龙分金数据
func GetDragonDataByHTTP(ctx context.Context, date string) (err error) {

	// 判断当前时间所在的区间
	realTimeIndex, err := GetCurrentIndex(date)
	currentIntervalIndex := realTimeIndex
	if currentIntervalIndex > 0 {
		currentIntervalIndex = currentIntervalIndex - 1
	}
	if currentIntervalIndex >= 5 {
		return
	}

	scheduleCtx := context.Background()

	// 拿到当天的时间
	// 获取当前时间
	currentTime := time.Now()
	currentTime = currentTime.AddDate(0, 0, -1)

	if date != "" {
		t, errP := time.Parse("20060102", date)
		if errP != nil {
			log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof("TimeDtEventTime1 error", errP)
		}
		currentTime = t
	}

	// 将当前时间格式化为"2006-01-02"格式的字符串
	formattedTime := currentTime.Format("20060102")
	// 分批次处理每次处理100个
	maxGoroutines := 50
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup

	var total int64
	result := DB.SelectConnect("trssj").Debug().WithContext(scheduleCtx).Table(
		taris.TarisDragonRaidBountyDetail{}.TableName()).
		Where("dtstatdate = ? and dupcd = ?", formattedTime, realTimeIndex).
		Count(&total)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	groupSize := 50
	for i := 0; int64(i) <= total; i += groupSize {
		var todayData []*taris.TarisDragonRaidBountyDetail
		// 查询数据
		result = DB.SelectConnect("trssj").Debug().WithContext(scheduleCtx).Table(
			taris.TarisDragonRaidBountyDetail{}.TableName()).
			Where("dtstatdate = ? and dupcd = ?", formattedTime, realTimeIndex).
			Find(&todayData).Limit(groupSize).Offset(i)
		if result.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql error, \t [Error]:{%v} ", result.Error)
			return
		}
		if currentIntervalIndex == 0 && realTimeIndex == 1 {
			var todayData0 []*taris.TarisDragonRaidBountyDetail
			// 需要合并前两个cd
			result = DB.SelectConnect("trssj").Debug().WithContext(scheduleCtx).Table(
				taris.TarisDragonRaidBountyDetail{}.TableName()).
				Where("dtstatdate = ? and dupcd = ?", formattedTime, realTimeIndex).
				Find(&todayData0).Limit(groupSize).Offset(i)
			if result.Error != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql error, \t [Error]:{%v} ", result.Error)
				return
			}
			todayData = mergeDataTemp(todayData, todayData0)
		}

		for _, dataItem := range todayData {
			wg.Add(1)
			go func(item *taris.TarisDragonRaidBountyDetail) {
				semaphore <- struct{}{}
				var findItem *taris.TarisDragonRaidBountyTotalDetail
				// 查询总表里是否有如果有则加 如果没有则插入
				result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
					taris.TarisDragonRaidBountyTotalDetail{}.TableName()).
					Where("guild_id = ?", item.VGuildId).
					First(&findItem)
				if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
					log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] SyncDragonTotalTable err:%v",
						err.Error()))
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"mysql error, \t [Error]:{%v} ", result.Error)
				} else {
					findItem.GuildID = item.VGuildId
					findItem.AreaId = 11
					zoneId, _ := strconv.ParseInt(item.IZoneAreaId, 10, 64)
					findItem.ZoneId = zoneId
					t, errP := time.Parse("2006-01-02 15:04:05", item.LastTime)
					if errP != nil {
						log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof("TimeDtEventTime1 error", errP)
					}
					SumPoint, err := strconv.ParseInt(item.SumPoint, 10, 64)
					switch currentIntervalIndex {
					case 0:
						findItem.TimeGuildName1 = item.VGuildName
						findItem.TimeMasterID1 = item.GuildChairManId
						findItem.TimeMasterName1 = item.VRoleName
						findItem.TimePoints1 = SumPoint
						findItem.TimeDtEventTime1 = t.Unix()
					case 1:
						findItem.TimeGuildName2 = item.VGuildName
						findItem.TimeMasterID2 = item.GuildChairManId
						findItem.TimeMasterName2 = item.VRoleName
						findItem.TimePoints2 = SumPoint
						findItem.TimeDtEventTime2 = t.Unix()
					case 2:
						findItem.TimeGuildName3 = item.VGuildName
						findItem.TimeMasterID3 = item.GuildChairManId
						findItem.TimeMasterName3 = item.VRoleName
						findItem.TimePoints3 = SumPoint
						findItem.TimeDtEventTime3 = t.Unix()
					case 3:
						findItem.TimeGuildName4 = item.VGuildName
						findItem.TimeMasterID4 = item.GuildChairManId
						findItem.TimeMasterName4 = item.VRoleName
						findItem.TimePoints4 = SumPoint
						findItem.TimeDtEventTime4 = t.Unix()
					case 4:
						findItem.TimeGuildName5 = item.VGuildName
						findItem.TimeMasterID5 = item.GuildChairManId
						findItem.TimeMasterName5 = item.VRoleName
						findItem.TimePoints5 = SumPoint
						findItem.TimeDtEventTime5 = t.Unix()
					}
					findItem.Points = findItem.TimePoints1 + findItem.TimePoints2 + findItem.TimePoints3 + findItem.TimePoints4 +
						findItem.TimePoints5

					result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
						taris.TarisDragonRaidBountyTotalDetail{}.TableName()).
						Save(&findItem)
					if result.Error != nil {
						log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] SyncDragonTotalTable err:%v",
							err.Error()))
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"mysql error, \t [Error]:{%v} ", result.Error)
					}
				}
				wg.Done()
				<-semaphore
			}(dataItem)
		}
		wg.Wait()
	}
	return
}

// GetCurrentIndex 拿到当前时间是哪个范围内
func GetCurrentIndex(date string) (index int32, err error) {

	// 获取当前时间
	now := time.Now().Unix()

	if date != "" {
		scheduleCtx := context.Background()

		t, errP := time.Parse("20060102", date)
		if errP != nil {
			log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof("TimeDtEventTime1 error", errP)
		}

		now = t.Unix()
	}
	// 创建一个UTC+8的时区
	location := time.FixedZone("UTC+7", 7*60*60)

	// 将当前时间转换为UTC+8时区的时间
	utc7Time := time.Unix(now, 0).In(location)

	// 判断当前时间所在的区间
	currentIntervalIndex := 0
	for i, interval := range timeIntervals {
		if utc7Time.Unix() >= interval.StartDate.Unix() && utc7Time.Unix() <= interval.EndDate.Unix() {
			currentIntervalIndex = i
			break
		}
	}
	if utc7Time.Unix() > timeIntervals[5].EndDate.Unix() {
		currentIntervalIndex = 6
	}
	return int32(currentIntervalIndex), nil
}

// SyncDragonTotalTable 从明细表计算总积分并且记录
func SyncDragonTotalTable(ctx context.Context, date string) (err error) {
	scheduleCtx := context.Background()
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof("----------SyncDragonTotalTable-----------")

	// 判断当前时间所在的区间
	currentIntervalIndex, err := GetCurrentIndex(date)
	if currentIntervalIndex > 0 {
		currentIntervalIndex = currentIntervalIndex - 1
	}

	if currentIntervalIndex >= 5 {
		return
	}

	maxGoroutines := 100
	semaphore := make(chan struct{}, maxGoroutines)
	// 	// 计算出对应积分消除instance_id产生的分裂
	var wg sync.WaitGroup

	// 在通过排名排一下在赋值到rank中
	var totalList []*taris.TarisDragonRaidBountyTotalDetail

	resultErr := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
		taris.TarisDragonRaidBountyTotalDetail{}.TableName()).
		Order(fmt.Sprintf("time_points_%v desc, time_dteventtime_%v asc ", currentIntervalIndex+1, currentIntervalIndex+1)).
		Find(&totalList)
	if resultErr.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", resultErr.Error)
		return
	}
	for index, item := range totalList {
		switch currentIntervalIndex {
		case 0:
			item.TimeRank1 = int64(index) + 1
		case 1:
			item.TimeRank2 = int64(index) + 1
		case 2:
			item.TimeRank3 = int64(index) + 1
		case 3:
			item.TimeRank4 = int64(index) + 1
		case 4:
			item.TimeRank5 = int64(index) + 1
		default:
			log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] currentIntervalIndex: [%v]",
				currentIntervalIndex))
			return
		}

		wg.Add(1)
		go func(detail *taris.TarisDragonRaidBountyTotalDetail) {
			semaphore <- struct{}{}
			// 更新数据
			resultErr = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
				taris.TarisDragonRaidBountyTotalDetail{}.TableName()).
				Save(&detail)
			if resultErr.Error != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] SyncDragonTotalTable err:%v",
					err.Error()))
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql error, \t [Error]:{%v} ", resultErr.Error)
			}
			wg.Done()
			<-semaphore
		}(item)
	}
	wg.Wait()
	// }
	return
}

// InsertDragonEs 屠龙分金数据存储到es中
func InsertDragonEs(ctx context.Context, date string) (err error) {
	scheduleCtx := context.Background()
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof("----------InsertDragonEs-----------")
	currentIntervalIndex, err := GetCurrentIndex(date)
	if err != nil {
		return
	}
	if currentIntervalIndex >= 1 {
		currentIntervalIndex = currentIntervalIndex - 1
	}

	if currentIntervalIndex >= 5 {
		return
	}

	var data []*taris.TarisDragonRaidBountyTotalDetail

	// 根据字段进行order
	result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
		taris.TarisDragonRaidBountyTotalDetail{}.TableName()).
		Where(fmt.Sprintf("%v > 0", mapIndexKey[currentIntervalIndex])).
		Order(fmt.Sprintf("%v asc", mapIndexKey[currentIntervalIndex])).
		Find(&data)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}
	// 按照当前时区来区分
	client := ES.DefaultConnect()

	maxGoroutines := 100
	semaphore := make(chan struct{}, maxGoroutines)
	// 检查文档是否已存在
	var wg sync.WaitGroup
	for _, item := range data {
		wg.Add(1)
		go func(item *taris.TarisDragonRaidBountyTotalDetail) {
			semaphore <- struct{}{}
			// 定义查询条件
			query := elastic.NewBoolQuery().Must(
				elastic.NewTermQuery("zone_id", item.ZoneId),
				elastic.NewTermQuery("guild_id", item.GuildID),
				elastic.NewTermQuery("period_time", fmt.Sprintf("%v", currentIntervalIndex)),
			)

			// 执行查询并检查文档是否存在
			searchResult, err := client.Search().
				Index(taris.TarisDragonRaidBountyESModel{}.TableName()). // 设置索引名称
				Query(query).                                            // 设置查询条件
				Size(1).
				Do(scheduleCtx) // 执行查询

			if err != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeElasticsearch, ES.ElasticsearchErr,
					"es error, \t [Error]:{%v} ", err)
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] InsertDragonEs err:%v",
					err.Error()))
			}

			var data taris.TarisDragonRaidBountyESDetail
			if searchResult.TotalHits() > 0 {
				errM := json.Unmarshal(searchResult.Hits.Hits[0].Source, &data)
				if errM != nil {
					err = errM
					log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] InsertDragonEs err:%v",
						err.Error()))
				}
			}
			switch currentIntervalIndex {
			case 0:
				data.Points = item.TimePoints1
				data.Rank = item.TimeRank1
				data.GuildMaster = item.TimeMasterName1
				data.GuildMasterID = item.TimeMasterID1
				data.GuildName = item.TimeGuildName1
			case 1:
				data.Points = item.TimePoints2
				data.Rank = item.TimeRank2
				data.GuildMaster = item.TimeMasterName2
				data.GuildMasterID = item.TimeMasterID2
				data.GuildName = item.TimeGuildName2
			case 2:
				data.Points = item.TimePoints3
				data.Rank = item.TimeRank3
				data.GuildMaster = item.TimeMasterName3
				data.GuildMasterID = item.TimeMasterID3
				data.GuildName = item.TimeGuildName3
			case 3:
				data.Points = item.TimePoints4
				data.Rank = item.TimeRank4
				data.GuildMaster = item.TimeMasterName4
				data.GuildMasterID = item.TimeMasterID4
				data.GuildName = item.TimeGuildName4
			case 4:
				data.Points = item.TimePoints5
				data.Rank = item.TimeRank5
				data.GuildMaster = item.TimeMasterName5
				data.GuildMasterID = item.TimeMasterID5
				data.GuildName = item.TimeGuildName5
			default:
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] currentIntervalIndex: [%v]",
					currentIntervalIndex))
			}
			// 检查文档是否存在
			if searchResult.TotalHits() > 0 {
				// 更新

				_, err := client.Update().
					Index(taris.TarisDragonRaidBountyESModel{}.TableName()).
					Id(searchResult.Hits.Hits[0].Id).
					Doc(data).
					Do(scheduleCtx)
				if err != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeElasticsearch, ES.ElasticsearchErr,
						"es error, \t [Error]:{%v} ", err)
					log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] InsertDragonEs err:%v",
						err.Error()))
				}
			} else {
				// 插入
				data.AreaId = item.AreaId
				data.ZoneId = item.ZoneId
				data.GuildID = item.GuildID
				data.PeriodTime = fmt.Sprintf("%v", currentIntervalIndex)
				_, err := client.Index().
					Index(taris.TarisDragonRaidBountyESModel{}.TableName()).
					BodyJson(data).
					Do(scheduleCtx)
				if err != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeElasticsearch, ES.ElasticsearchErr,
						"es error, \t [Error]:{%v} ", err)
					log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] InsertDragonEs err:%v",
						err.Error()))
				}
			}
			wg.Done()
			<-semaphore
		}(item)
	}
	wg.Wait()

	// 清除es缓存
	_, err = client.ClearCache().Index(taris.TarisDragonRaidBountyESDetail{}.TableName()).Do(scheduleCtx)
	if err != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeElasticsearch, ES.ElasticsearchErr,
			"es error, \t [Error]:{%v} ", err)
		return
	}
	return
}

// GetDataByRoleID 获取奖励列表
func GetDataByRoleID(ctx context.Context, Fsource_id string, gameRoleInfo *gamePb.RoleInfo) (
	data []*taris.TarisDragonRaidBountyTotalDetail, err error) {

	data = make([]*taris.TarisDragonRaidBountyTotalDetail, 0)

	// 到总积分表查询数据
	condition := taris.TarisDragonRaidBountyTotalDetail{
		AreaId: gameRoleInfo.AreaId,
		ZoneId: gameRoleInfo.ZoneId,
	}

	result := DB.DefaultConnect().Debug().WithContext(ctx).Table(taris.TarisDragonRaidBountyTotalDetail{}.TableName()).
		Where(condition).
		Where(
			"(time_master_id_1 = ? and time_rank_1 <= 1000) or (time_master_id_2 = ? and time_rank_2 <= 1000) or (time_master_id_3 = ? and time_rank_3 <= 1000) or (time_master_id_4 = ? and time_rank_4 <= 1000) or (time_master_id_5 = ? and time_rank_5 <= 1000)",
			gameRoleInfo.RoleId, gameRoleInfo.RoleId, gameRoleInfo.RoleId, gameRoleInfo.RoleId, gameRoleInfo.RoleId).
		Order("points desc").Find(&data)

	if result.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	return
}

// TotalDataChangeList 将屠龙积分总表每一期数据变成list返回给前端
func TotalDataChangeList(data []*taris.TarisDragonRaidBountyTotalDetail, gameRoleInfo *gamePb.RoleInfo) (
	list []*pb.DragonPresentItem, err error) {

	// 获取当前时间
	now := time.Now().Unix()
	// 创建一个UTC+8的时区
	location := time.FixedZone("UTC+7", 7*60*60)

	// 将当前时间转换为UTC+8时区的时间
	utc7Time := time.Unix(now, 0).In(location)
	timeDDL := []time.Time{
		time.Date(2024, 7, 5, 23, 59, 59, 59, utc7),
		time.Date(2024, 7, 12, 23, 59, 59, 59, utc7),
		time.Date(2024, 7, 19, 23, 59, 59, 59, utc7),
		time.Date(2024, 7, 26, 23, 59, 59, 59, utc7),
		time.Date(2024, 8, 2, 23, 59, 59, 59, utc7),
	}
	list = make([]*pb.DragonPresentItem, 0)
	for index, item := range data {
		if item.TimeMasterID1 == gameRoleInfo.RoleId && item.TimeRank1 <= 1000 && utc7Time.Unix() > timeDDL[index].Unix() {
			// if item.TimeMasterID1 == gameRoleInfo.RoleId && item.TimeRank1 <= 1000 {
			list = append(list, &pb.DragonPresentItem{
				GuildName:  item.TimeMasterName1,
				PeriodTime: "0",
				GuildRank:  item.TimeRank1,
				GuildPoint: item.TimePoints1,
			})
		}
		if item.TimeMasterID2 == gameRoleInfo.RoleId && item.TimeRank2 <= 1000 && utc7Time.Unix() > timeDDL[index].Unix() {
			// if item.TimeMasterID2 == gameRoleInfo.RoleId && item.TimeRank2 <= 1000 {
			list = append(list, &pb.DragonPresentItem{
				GuildName:  item.TimeMasterName2,
				PeriodTime: "1",
				GuildRank:  item.TimeRank2,
				GuildPoint: item.TimePoints2,
			})
		}
		if item.TimeMasterID3 == gameRoleInfo.RoleId && item.TimeRank3 <= 1000 && utc7Time.Unix() > timeDDL[index].Unix() {
			// if item.TimeMasterID3 == gameRoleInfo.RoleId && item.TimeRank3 <= 1000 {
			list = append(list, &pb.DragonPresentItem{
				GuildName:  item.TimeMasterName3,
				PeriodTime: "2",
				GuildRank:  item.TimeRank3,
				GuildPoint: item.TimePoints3,
			})
		}
		if item.TimeMasterID4 == gameRoleInfo.RoleId && item.TimeRank4 <= 1000 && utc7Time.Unix() > timeDDL[index].Unix() {
			// if item.TimeMasterID4 == gameRoleInfo.RoleId && item.TimeRank4 <= 1000 {
			list = append(list, &pb.DragonPresentItem{
				GuildName:  item.TimeMasterName4,
				PeriodTime: "3",
				GuildRank:  item.TimeRank4,
				GuildPoint: item.TimePoints4,
			})
		}
		if item.TimeMasterID5 == gameRoleInfo.RoleId && item.TimeRank5 <= 1000 && utc7Time.Unix() > timeDDL[index].Unix() {
			// if item.TimeMasterID5 == gameRoleInfo.RoleId && item.TimeRank5 <= 1000 {
			list = append(list, &pb.DragonPresentItem{
				GuildName:  item.TimeMasterName5,
				PeriodTime: "4",
				GuildRank:  item.TimeRank5,
				GuildPoint: item.TimePoints5,
			})
		}

	}
	return
}

// GetDragonTotalPointTop 屠龙积分总积分排行榜
func GetDragonTotalPointTop(ctx context.Context, pageSize int32, pageNum int32) (list []*pb.DragonTotalPointItem,
	err error) {

	var data []taris.TarisDragonRaidBountyTotalDetail

	currentIntervalIndex, err := GetCurrentIndex("")
	if err != nil {
		return
	}
	if currentIntervalIndex >= 1 {
		currentIntervalIndex = currentIntervalIndex - 1
	}

	if currentIntervalIndex >= 5 {
		currentIntervalIndex = 4
	}

	result := DB.DefaultConnect().Debug().WithContext(ctx).Table(taris.TarisDragonRaidBountyTotalDetail{}.TableName()).
		Order(fmt.Sprintf("points desc, time_dteventtime_%v asc", currentIntervalIndex+1)).Limit(int(pageSize)).
		Offset((int(pageNum) - 1) * int(pageSize)).
		Find(&data)

	if result.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	currentIndex, err := GetCurrentIndex("")
	if err != nil {
		return
	}
	if currentIndex > 0 {
		currentIndex = currentIndex - 1
	}

	if currentIndex >= 5 {
		currentIndex = 4
	}

	for index, item := range data {
		appendItem := &pb.DragonTotalPointItem{
			ZoneId:     fmt.Sprintf("%v", item.ZoneId),
			GuildPoint: item.Points,
		}
		switch currentIndex {
		case 0:
			appendItem.GuildMaster = item.TimeMasterName1
			appendItem.GuildName = item.TimeGuildName1
		case 1:
			appendItem.GuildMaster = item.TimeMasterName2
			appendItem.GuildName = item.TimeGuildName2
		case 2:
			appendItem.GuildMaster = item.TimeMasterName3
			appendItem.GuildName = item.TimeGuildName3
		case 3:
			appendItem.GuildMaster = item.TimeMasterName4
			appendItem.GuildName = item.TimeGuildName4
		case 4:
			appendItem.GuildMaster = item.TimeMasterName5
			appendItem.GuildName = item.TimeGuildName5
		default:
			appendItem.GuildMaster = item.TimeMasterName5
			appendItem.GuildName = item.TimeGuildName5
		}
		appendItem.GuildRank = int64((int(pageNum)-1)*int(pageSize) + index + 1)
		list = append(list, appendItem)
	}

	return
}

// GetDragonRankByEs 屠龙分金获取积分列表
func GetDragonRankByEs(ctx context.Context, req *pb.GetDragonPointListReq) (list []*pb.DragonPointItem, count int32,
	err error) {
	list = make([]*pb.DragonPointItem, 0)
	count = 0
	client := ES.DefaultConnect()

	// 创建一个 BoolQuery
	query := elastic.NewBoolQuery()

	if req.ZoneId != 0 {
		query = query.Filter(elastic.NewTermQuery("zone_id", req.ZoneId))
	}

	if req.PeriodTime == "" {
		currentIntervalIndex, errs := GetCurrentIndex("")
		if errs != nil {
			return
		}
		if currentIntervalIndex > 0 {
			currentIntervalIndex = currentIntervalIndex - 1
		}

		if currentIntervalIndex >= 5 {
			currentIntervalIndex = 4
		}

		req.PeriodTime = fmt.Sprintf("%v", currentIntervalIndex)
	}

	// 添加其他查询条件
	query = query.Filter(
		elastic.NewTermQuery("period_time", req.PeriodTime),
	)
	if req.GuildName != "" {
		guildNameMatchQuery := elastic.NewQueryStringQuery(fmt.Sprintf("*%v*", req.GuildName)).Field("guild_name")
		query = query.Filter(guildNameMatchQuery)
	} else {
		query = query.Filter(elastic.NewRangeQuery("rank").Lte(1000))
	}

	// 执行查询并检查文档是否存在
	searchResult, err := client.Search().
		Index(taris.TarisDragonRaidBountyESModel{}.TableName()). // 设置索引名称
		From((int(req.PageNum)-1)*int(req.PageSize)).
		Size(int(req.PageSize)).
		Query(query). // 设置查询条件
		Sort("rank", true).
		Do(ctx)

	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeElasticsearch, ES.ElasticsearchErr,
			"es error, \t [Error]:{%v} ", err)
		return
	}
	// 输出搜索结果
	for _, hit := range searchResult.Hits.Hits {

		var item *taris.TarisDragonRaidBountyESDetail
		err = json.Unmarshal(hit.Source, &item)
		if err != nil {
			return
		}
		list = append(list, &pb.DragonPointItem{
			ZoneId:      fmt.Sprintf("%v", item.ZoneId),
			GuildName:   item.GuildName,
			GuildMaster: item.GuildMaster,
			GuildPoint:  item.Points,
			PeriodTime:  item.PeriodTime,
			GuildRank:   item.Rank,
		})

	}

	if req.PageNum == 1 {
		// 执行查询并检查文档是否存在
		searchResult, err = client.Search().
			Index(taris.TarisDragonRaidBountyESModel{}.TableName()). // 设置索引名称
			Query(query).                                            // 设置查询条件
			Do(ctx)

		if err != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeElasticsearch, ES.ElasticsearchErr,
				"es error, \t [Error]:{%v} ", err)
			return
		}

		count = int32(searchResult.TotalHits())

	}

	return
}

// InsertRoleID 记录报名的roleId
func InsertRoleID(ctx context.Context, roleID string, langType string, FsourceID string) (err error) {
	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	totalItem := taris.TarisSignUpRoleTemp{
		RoleID:    roleID,
		UID:       userAccount.Uid,
		LangType:  langType,
		FsourceID: FsourceID,
	}
	tableName := taris.TarisSignUpRoleTemp{}.TableName()
	if FsourceID == "page-26635" {
		tableName = "taris_sign_up_role_asia"
	}

	result := DB.DefaultConnect().Debug().WithContext(ctx).Table(
		tableName).
		Where("role_id = ? and Fsource_id = ?", roleID, FsourceID).
		FirstOrCreate(&totalItem)
	if result.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	return
}

// GetDragonTotalCount 获取总积分条数
func GetDragonTotalCount(ctx context.Context) (count int32, err error) {
	var total int64
	result := DB.DefaultConnect().Debug().WithContext(ctx).Table(
		taris.TarisDragonRaidBountyTotalDetail{}.TableName()).
		Count(&total)
	if result.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	count = int32(total)
	return
}

// ScheduledSend 发送奖品
func ScheduledSend(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	tableName := taris.TarisSendTemp{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页50条
	pageSize := 50
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	gameProxy := gamePb.NewGameClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		offset := (pageNumber - 1) * pageSize
		var logData = []taris.TarisSendTemp{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Where(condition).Offset(offset).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", logData))
		for _, v := range logData {
			wg.Add(1)
			go func(data taris.TarisSendTemp) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(1),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(string(accountData))

				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
				}
				// 获取角色信息
				gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: data.FsourceID}

				gameRoleInfo, err := gameProxy.GetSavedRoleInfo(newCtx, gameReq, callopts...)
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("gameRoleInfo：%v", gameRoleInfo))

				if err == nil {
					log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("v: %#v", data))
					// 发送礼包
					sendReq := &presentPb.SendPresentReq{
						FsourceId: data.FsourceID,
						PresentId: data.Tag,
						RoleInfo:  gameRoleInfo,
					}
					accountData, _ = proto.Marshal(&accountPb.UserAccount{
						Uid:         data.UID,
						AccountType: accountPb.AccountType(1),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							GameId:    gameRoleInfo.GameId,
							ChannelId: 3,
						},
					})
					callopts = []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
						client.WithMetaData(metadata.LangType, []byte(data.LangType)),
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
					}
					if sendErr != nil {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						fmt.Println("---------------sendErr---------------")
						fmt.Printf("%#v\n", sendErr.Error())
						log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err111:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						if strings.Contains(sendErr.Error(), "package limit left not enough") {
							DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						}
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err:%v", sendErr))
					// 修改发货状态
				} else {
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[GetRoleInfo] service err:%v", err))
				}
				wg.Done()
			}(v)
		}
		wg.Wait()
	}
	return
}

// DeleteBindRole 删除绑定角色
func DeleteBindRole(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	// 获取需要删除的总和然后一批100去删除
	var count int64
	result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
		taris.TarisUnBindRole{}.TableName()).
		Where("status = ?", 0).
		Count(&count)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}
	var wg sync.WaitGroup
	for i := 0; int64(i) < count; i += 100 {
		list := make([]*taris.TarisUnBindRole, 0)
		// 查询数据
		result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
			taris.TarisUnBindRole{}.TableName()).
			Where("status = ?", 0).
			Limit(100).Offset(i).Find(&list)
		if result.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql error, \t [Error]:{%v} ", result.Error)
			return
		}
		for _, item := range list {
			wg.Add(1)
			go func(item *taris.TarisUnBindRole) {
				tx := DB.DefaultConnect().WithContext(scheduleCtx).Begin()
				defer func() {
					if r := recover(); r != nil {
						tx.Rollback()
					}
				}()
				defer wg.Done()
				var signItem *taris.TarisSignUpRoleTemp
				result = tx.Table(
					taris.TarisSignUpRoleTemp{}.TableName()).
					Debug().
					Where("role_id = ?", item.RoleID).
					Find(&signItem)
				if result.Error != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"mysql error, \t [Error]:{%v} ", result.Error)
					return
				}
				if signItem.UID != "" {
					// 查询绑定角色的两个表删除数据
					userAccount := accountPb.UserAccount{
						Uid:         signItem.UID,
						AccountType: accountPb.AccountType(2),
					}
					// 获取表名
					accountRecordTableName, errM := model.GetTableNameWithAccount(scheduleCtx, &userAccount,
						"region_account_record")
					if errM != nil {
						err = errM
						return
					}
					zoneListCondition := map[string]interface{}{
						"uid": userAccount.Uid,
					}
					// 删除保存角色
					result = tx.Table(accountRecordTableName).
						Debug().
						Where("uid = ?", userAccount.Uid).
						Delete(&zoneListCondition)
					if result.Error != nil {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"mysql error, \t [Error]:{%v} ", result.Error)
						return
					}

					// 删除保存角色
					accountListTableName, errM := model.GetTableNameWithAccount(scheduleCtx, &userAccount,
						"region_account_list")
					if errM != nil {
						err = errM
						return
					}
					result = tx.Table(accountListTableName).
						Debug().
						Where("uid = ?", userAccount.Uid).
						Delete(&zoneListCondition)
					if result.Error != nil {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"mysql error, \t [Error]:{%v} ", result.Error)
						return
					}

					// 删除报名记录roleid
					result = tx.Table(
						"taris_sign_up_role_asia").
						Debug().
						Where("uid", signItem.UID).
						Delete(&zoneListCondition)
					if result.Error != nil {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"mysql error, \t [Error]:{%v} ", result.Error)
						return
					}
					// 删除报名
					tableName, errM := model.GetTabNameWithGeneral(scheduleCtx, (&generalModel.ConfigModel{}).TableName(),
						signItem.FsourceID,
						"signup",
						(&generalModel.LogModel{}).TableName(), 100)
					if errM != nil {
						err = errM
						return
					}
					result = tx.Table(tableName).
						Debug().
						Where("uid = ? and Fsource_id = ?", signItem.UID, signItem.FsourceID).
						Delete(&zoneListCondition)
					if result.Error != nil {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"mysql error, \t [Error]:{%v} ", result.Error)
						return
					}
					// 删除排行榜
					result = tx.Table("taris_rank_temp_asia").
						Debug().
						Where("role_id = ?", signItem.RoleID).
						Delete(&taris.TarisRankTemp{})
					if result.Error != nil {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"mysql error, \t [Error]:{%v} ", result.Error)
						return
					}
					result = tx.Table(taris.TarisUnBindRole{}.TableName()).
						Debug().
						Where("role_id = ?", signItem.RoleID).
						Updates(&taris.TarisUnBindRole{
							Status: 1,
						})
					if result.Error != nil {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"mysql error, \t [Error]:{%v} ", result.Error)
						return
					}
				}
				tx.Commit()
			}(item)
			wg.Wait()
		}
	}
	// 查询出来openid

	return
}

// GetDragonRank 屠龙分金数据导出
func GetDragonRank(ctx context.Context, PeriodTime string) (err error) {
	scheduleCtx := context.Background()
	// 创建一个新的 Excel 文件，并设定默认 sheet 名称为 "Sheet1"
	f := excelize.NewFile()

	data := make([][]interface{}, 1001)
	data[0] = []interface{}{
		"zoneid", "role_id", "guildid", "guildname", "country", "role_name", "periodtime", "rank", "points", "email",
		"address",
	}
	maxGoroutines := 100
	semaphore := make(chan struct{}, maxGoroutines)
	// 检查文档是否已存在
	var wg sync.WaitGroup

	var conf = config.GetConfig()
	aesSecret := conf.Crypto
	aesUtil := crypto.NewAesCrypto()
	// 查询填写邮箱的数据 在判断事否在对应的数据中
	for i := 1; i <= 99; i++ {
		wg.Add(1)
		go func(index int32) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			iStr := fmt.Sprintf("%02d", index)
			tableName := fmt.Sprintf("%v_openid_%v", address.AddressAccountLog{}.TableName(), iStr)

			list := make([]*address.AddressAccountLog, 0)
			result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
				Where("Fsource_id = ? and is_del = 0", "page-26625").
				Find(&list)
			if result.Error != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql error, \t [Error]:{%v} ", result.Error)
				return
			}

			for _, item := range list {
				wg.Add(1)
				go func(item *address.AddressAccountLog) {
					semaphore <- struct{}{}
					defer func() {
						<-semaphore
						wg.Done()
					}()
					openID := strings.Split(item.UID, "-")[1]
					accountData, _ := proto.Marshal(&accountPb.UserAccount{
						Uid:         item.UID,
						AccountType: accountPb.AccountType(1),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							ChannelId: 3,
						},
					})
					callopts := []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
					}
					// 通过uid查找绑定role_id
					gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: "page-26625"}
					gameProxy := gamePb.NewGameClientProxy()
					gameRoleInfo, roleErr := gameProxy.GetSavedRoleInfo(scheduleCtx, gameReq, callopts...)
					if roleErr != nil {
						log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("get role info error by uid: %v",
							roleErr))
						return
					}

					// 通过role info 获取到对应的排名
					var dragonItem *taris.TarisDragonRaidBountyTotalDetail
					result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
						taris.TarisDragonRaidBountyTotalDetail{}.TableName()).
						Where(fmt.Sprintf("time_master_id_%v = ?", PeriodTime), gameRoleInfo.RoleId).
						Where(fmt.Sprintf("time_rank_%v <= ?", PeriodTime), 1000).
						First(&dragonItem)
					if result.Error != nil {
						if errors.Is(result.Error, gorm.ErrRecordNotFound) {
							return
						}
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"mysql error, \t [Error]:{%v} ", result.Error)
						return
					}

					if item.Email != "" {
						emailByte, _ := aesUtil.DecryptBase64([]byte(item.Email), []byte(aesSecret))
						item.Email = string(emailByte)
					}
					if item.Address != "" {
						addressByte, _ := aesUtil.DecryptBase64([]byte(item.Address), []byte(aesSecret))
						item.Address = string(addressByte)
					}
					if item.Name != "" {
						countryByte, _ := aesUtil.DecryptBase64([]byte(item.Name), []byte(aesSecret))
						item.Name = string(countryByte)
					}
					switch {
					case PeriodTime == "1":
						data[dragonItem.TimeRank1] = []interface{}{
							dragonItem.ZoneId, gameRoleInfo.RoleId, dragonItem.GuildID, dragonItem.TimeGuildName1,
							item.Name, gameRoleInfo.RoleName, PeriodTime, dragonItem.TimeRank1,
							dragonItem.TimePoints1, item.Email,
							item.Address,
						}
					case PeriodTime == "2":
						data[dragonItem.TimeRank2] = []interface{}{
							dragonItem.ZoneId, gameRoleInfo.RoleId, dragonItem.GuildID, dragonItem.TimeGuildName2,
							item.Name, gameRoleInfo.RoleName, PeriodTime, dragonItem.TimeRank2,
							dragonItem.TimePoints2, item.Email,
							item.Address,
						}
					case PeriodTime == "3":
						data[dragonItem.TimeRank3] = []interface{}{
							dragonItem.ZoneId, gameRoleInfo.RoleId, dragonItem.GuildID, dragonItem.TimeGuildName3,
							item.Name, gameRoleInfo.RoleName, PeriodTime, dragonItem.TimeRank3,
							dragonItem.TimePoints3, item.Email,
							item.Address,
						}
					case PeriodTime == "4":
						data[dragonItem.TimeRank4] = []interface{}{
							dragonItem.ZoneId, gameRoleInfo.RoleId, dragonItem.GuildID, dragonItem.TimeGuildName4,
							item.Name, gameRoleInfo.RoleName, PeriodTime, dragonItem.TimeRank4,
							dragonItem.TimePoints4, item.Email,
							item.Address,
						}
					case PeriodTime == "5":
						data[dragonItem.TimeRank5] = []interface{}{
							dragonItem.ZoneId, gameRoleInfo.RoleId, dragonItem.GuildID, dragonItem.TimeGuildName5,
							item.Name, gameRoleInfo.RoleName, PeriodTime, dragonItem.TimeRank5,
							dragonItem.TimePoints5, item.Email,
							item.Address,
						}
					}
				}(item)
			}
		}(int32(i))
		wg.Wait()
	}

	// 将data数据写到excel中
	// 使用循环按行插入数据
	for i, row := range data {
		for j, col := range row {
			cellName := fmt.Sprintf("%c%d", 'A'+j, i+1)
			f.SetCellValue("Sheet1", cellName, col)
		}
	}
	// 保存 Excel 文件
	err = f.SaveAs(fmt.Sprintf("dragon-%v.xlsx", PeriodTime))
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("create elxcel err: %v", err))
	}
	return
}

// UserPresentData TODO
type UserPresentData struct {
	ID            int64  `gorm:"type:int(11);column:id;primary_key"`
	Uid           string `gorm:"type:varchar(32);column:uid;''"`
	AccountType   int    `gorm:"type:tinyint(1);column:account_type;0"`
	ResourceId    int64  `gorm:"type:int(11);column:resource_id;primary_key"`
	ResourceCid   int64  `gorm:"type:int(11);column:resource_cid;"`
	Present       string `gorm:"type:varchar(256);column:present;not null;0"`
	Level         int64  `gorm:"type:int(11);column:level;"`
	FsourceId     string `gorm:"type:varchar(255);column:Fsource_id;''"`
	SendStatus    int    `gorm:"type:tinyint(1);column:send_status;not null;''"`
	SendTimestamp int64  `gorm:"type:date();column:send_timestamp"`
	SendRemark    string `gorm:"type:varchar(255);column:send_remark"`
}

// PresentListData TODO
type PresentListData struct {
	EncryptId   string  `gorm:"type:varchar(32);column:encrypt_id;null"`
	ResourceId  int64   `gorm:"type:int(11);column:resource_id;not null"`
	ResourceCid int64   `gorm:"type:int(11);column:resource_cid;not null"`
	Name        string  `gorm:"type:varchar(100);column:name;not null"`
	Type        string  `gorm:"type:varchar(255);column:type;null"`
	Level       int64   `gorm:"type:int(5);column:level;0"`
	IsLast      int64   `gorm:"type:tinyint(3);column:is_last;0"`
	Chance      float64 `gorm:"type:decimal(8,5);column:chance;0"`
	Cfg         string  `gorm:"type:text;column:cfg;not null"`
	CfgValue    string  `gorm:"type:text;column:cfg_value;not null"`
	IsDelete    int32   `gorm:"type:tinyint(3);column:is_delete;not null"`
}

// GetSpeedRank 开服竞速数据导出
func GetSpeedRank(ctx context.Context, instanceID int32) (err error) {
	scheduleCtx := context.Background()
	// 创建一个新的 Excel 文件，并设定默认 sheet 名称为 "Sheet1"
	f := excelize.NewFile()

	data := make([][]interface{}, 1)
	data[0] = []interface{}{
		"zone_id", "role_id", "gift", "country", "email", "openid",
	}

	instanceIDRank := make([]*taris.TarisRankTemp, 0)

	result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table("taris_rank_temp_asia").
		Where("instance_id = ? and `rank` < 101", asiaInstanceIDs[instanceID-1]).
		Order("`rank`").
		Find(&instanceIDRank)
	if result.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	gitName := "10"

	for index, item := range instanceIDRank {

		if instanceID >= 6 {
			if index > 0 && index < 11 {
				gitName = "500"
			}
			if index > 10 && index < 21 {
				gitName = "100"
			}
			if index > 20 && index < 31 {
				gitName = "50"
			}
			if index > 30 && index < 101 {
				gitName = "10"
			}
		}
		// 通过taris_sign_up_role_asia拿uid
		var signupItem *taris.TarisSignUpRoleTemp
		result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table("taris_sign_up_role_asia").
			Where("role_id = ?", item.RoleID).
			Find(&signupItem)
		if result.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql error, \t [Error]:{%v} ", result.Error)
			return
		}
		// 通过uid查找对应的zone_id和role_id
		openID := strings.Split(signupItem.UID, "-")[1]
		accountData, _ := proto.Marshal(&accountPb.UserAccount{
			Uid:         signupItem.UID,
			AccountType: accountPb.AccountType(1),
			IntlAccount: &accountPb.IntlAccount{
				OpenId:    openID,
				ChannelId: 3,
			},
		})
		callopts := []client.Option{
			client.WithMetaData(metadata.UserAccount, accountData),
		}

		addressReq := &addressPb.GetAccountAddressInfoReq{FsourceId: signupItem.FsourceID}
		addressProxy := addressPb.NewAddressClientProxy()
		addressAccountLogInfo, addressErr := addressProxy.GetAccountAddressInfo(scheduleCtx, addressReq, callopts...)
		if addressErr != nil {
			log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf(
				"get address info error by tmpFsourceId: %v",
				addressErr))
			continue
		}

		// data = append(data, []interface{}{
		// 	"zone_id", "role_id", "gift", "country", "email",
		// })
		data = append(data, []interface{}{
			item.ZoneID, item.RoleID, gitName,
			addressAccountLogInfo.Name, addressAccountLogInfo.Email, openID,
		})
	}

	// 将data数据写到excel中
	// 使用循环按行插入数据
	for i, row := range data {
		for j, col := range row {
			cellName := fmt.Sprintf("%c%d", 'A'+j, i+1)
			f.SetCellValue("Sheet1", cellName, col)
		}
	}
	// 保存 Excel 文件
	err = f.SaveAs("speed-run.xlsx")
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf("create elxcel err: %v", err))
	}
	return
}

func getNameByResourceCid(nameList []*PresentListData, resourceCid int64) string {
	for _, item := range nameList {
		if item.ResourceCid == resourceCid {
			if resourceCid == 9500 || resourceCid == 9506 {
				return "300 card"
			}
			if resourceCid == 9503 {
				return "20 card"
			}
			if resourceCid == 9504 {
				return "15 card"
			}
			if resourceCid == 9502 || resourceCid == 9501 {
				return "100 card"
			}
			if resourceCid == 9498 || resourceCid == 9499 {
				return "500 card"
			}
			return item.Name
		}
	}
	return ""
}
