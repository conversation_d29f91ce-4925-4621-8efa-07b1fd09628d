package comment

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"trpc.publishing_application.standalonesite/app/common"

	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/logic/privacy"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	monitorPb "git.woa.com/trpcprotocol/publishing_application/standalonesite_monitor"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	redisClient "github.com/go-redis/redis/v8"
	es7 "github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	hotService "trpc.publishing_application.standalonesite/app/logic/hot"
	plateService "trpc.publishing_application.standalonesite/app/logic/plate"
	"trpc.publishing_application.standalonesite/app/logic/tweet"
	userService "trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/pkg/security"
	"trpc.publishing_application.standalonesite/app/util"
)

var (
	monitorProxy = monitorPb.NewMonitorClientProxy()
)

type CommentCreationReq struct {
	PostUUID        string   `json:"post_uuid"`
	CommentUUID     string   `json:"comment_uuid"`
	Reply2ReplyUUID string   `json:"reply_to_reply_uuid"`
	Type            int32    `json:"type"` //1-评论2-回复
	Content         string   `json:"content"`
	PicUrls         []string `json:"picUrls"`
	AtIntlOpenid    string   `json:"users"`
	GameId          string   `json:"game_id"`
	AreaId          string   `json:"area_id"`
	IsAudit         int8
	CommentBubbleId int64 `json:"comment_bubble_id"`
}

type CommentCreationNewReq struct {
	PostUUID    string                 `json:"post_uuid" binding:"required"`
	PostUrl     string                 `json:"post_url" binding:"required"` // 动态或资讯的访问链接
	CommentText string                 `json:"comment_text" binding:"required,min=1"`
	BizID       constants.PostCommentT `json:"biz_id"` // 业务id，3=资讯评论，4=动态评论
	IsAudit     int8
}

type CommentReplyCreationReq struct {
	CommentID    int64  `json:"comment_id" binding:"required"`
	Content      string `json:"content" binding:"required"`
	AtIntlOpenid string `json:"at_intl_openid"`
	ReplyID      int64  `json:"reply_id"`
	IsAudit      int8
}

type CommentDelReq struct {
	ID int64 `json:"id" binding:"required"`
}
type CommentDelNewReq struct {
	CommentID  string                 `json:"comment_id" binding:"required"`
	BizID      constants.PostCommentT `json:"biz_id" binding:"required"` // 业务id，3=资讯评论，4=动态评论
	UpdateUser string                 `json:"update_user"`
}

type ReplyDelReq struct {
	ID int64 `json:"id" binding:"required"`
}

type CommentStarReq struct {
	ID int64 `json:"comment_id" binding:"required"`
}

type CommentStarReqNew struct {
	CommentID string `json:"comment_id" binding:"required"`
}

type CommentReplyStarReq struct {
	ID int64 `json:"comment_reply_id" binding:"required"`
}

type CommentReviewItem struct {
	BizID     constants.PostCommentT `json:"biz_id"`     // 业务id，3=资讯评论，4=动态评论
	CommentID string                 `json:"comment_id"` // 评论id
	ParentID  string                 `json:"parent_id"`  // 父内容id
}

type CommentReviewReq struct {
	UpdateUser        string              `json:"update_user"`
	ReviewStatus      int                 `json:"review_status"` // '审核状态，0待审核，1已审核通过, 2：1审通过，3：1审驳回 4：2审驳回, 5:白名单特殊用户不需要审核'
	CommentReviewList []CommentReviewItem `json:"review_comments"`
	GameId            string              `json:"game_id"`
	AreaId            string              `json:"area_id"`
}

type CMSCommentDelReq struct {
	CommentDeleteList []CommentDelReq `json:"delete_comments"`
	UpdateUser        string          `json:"update_user"`
	GameId            string          `json:"game_id"`
	AreaId            string          `json:"area_id"`
}

func GetPostCommentInfo(c context.Context, myIntlOpenID, commentUuid, language string) (*pb.GetPostCommentRsp, error) {
	rsp := &pb.GetPostCommentRsp{}

	isAdmin := false
	// 先获取缓存数据
	commentRedisKey := cache.GetOneCommentGuestKey(commentUuid, language)
	if myIntlOpenID != "" {
		// 主态
		commentRedisKey = cache.GetOneCommentHostKey(commentUuid, myIntlOpenID, language)
		isAdmin = formatted.GetUserAdmin(myIntlOpenID)
	}
	commentCacheInfo, err := redis.GetClient().Get(c, commentRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(commentCacheInfo), &rsp)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPostCommentInfo cache json.Unmarshal error.commentRedisKey: %s, err: %v", commentRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetCommentJsonUnmarshalError, "Failed to obtain comment info, data parsing exception")
		}
	} else {
		// 没有缓存则直接查db数据
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostCommentInfo redis err: %v", err)
		}
		comment, err := dao.GetCommentByUUID(commentUuid)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetCommentFailed, "GetPostCommentInfo | Failed to get comment base info")
		}

		postInfo, err := tweet.GetPostInfoByUUID(c, comment.PostUUID)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetPostFailed, "GetPostCommentInfo | Failed to get post base info")
		}
		rsp.Comment = &pb.CommentItem{
			CommentUuid:     comment.CommentUUID,
			PostUuid:        comment.PostUUID,
			IntlOpenid:      comment.IntlOpenid,
			GameId:          comment.GameId,
			AreaId:          comment.AreaId,
			CreatedOn:       comment.CreatedOn,
			IsAuthor:        comment.IntlOpenid == postInfo.IntlOpenid,
			CanDelete:       (myIntlOpenID == comment.IntlOpenid) || isAdmin,
			CanReport:       myIntlOpenID != comment.IntlOpenid,
			Language:        comment.Language,
			CommentBubbleId: comment.CommentBubbleId,
		}
		commentContent, err := dao.GetCommentInfoByUUID(comment.CommentUUID)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostCommentInfo GetCommentInfoByUUID err, comment_uuid:(%s), err=(%v)", comment.CommentUUID, err)
		} else {
			rsp.Comment.Title = commentContent.Title
			rsp.Comment.Content = commentContent.Content
			rsp.Comment.PicUrls = strings.Split(commentContent.PicUrls, ",")
		}
		userInfo, err := user.GetUserDetailInfoByOpenid(c, myIntlOpenID, comment.IntlOpenid, language)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostCommentInfo GetUserDetailInfoByOpenid err, intl_openid:(%s), err=(%v)", comment.IntlOpenid, err)
		} else {
			rsp.Comment.User = userInfo
		}
		commentState, err := dao.GetCommentStateByCommentUuid(comment.CommentUUID)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostCommentstate GetCommentStateByCommentUuid err, comment_uuid:(%s), err=(%v)", comment.CommentUUID, err)
		} else {
			rsp.Comment.UpvoteCount = int64(commentState.UpvoteCount)
		}

		commentByte, err := json.Marshal(rsp)
		if err == nil {
			redis.GetClient().SetEX(c, commentRedisKey, string(commentByte), 2*time.Minute).Result()
		}
	}

	// 获取当前用户对评论和回复是否有点赞
	starCommentIds, err := dao.CommentStarCheckUserCommentsStar2(commentUuid, myIntlOpenID)
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostCommentInfo CommentStarCheckUserCommentsStar2 err, intl_openid:(%s), err=(%v)", myIntlOpenID, err)
	}
	if len(starCommentIds) > 0 {
		rsp.Comment.IsStar = true
	}

	return rsp, nil
}

// 获取评论列表
func GetPostComments(c context.Context, myIntlOpenID string, language string, req *pb.GetPostCommentsReq) (*pb.GetPostCommentsRsp, error) {
	rsp := &pb.GetPostCommentsRsp{
		PageInfo: &pb.PageInfo{},
		List:     make([]*pb.CommentItem, 0),
	}
	if req.PostUuid == "" {
		rsp.PageInfo.IsFinish = true
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostComments req.PostUuid is empty, myIntlOpenid: %s, language: %s req: %v", myIntlOpenID, language, req)
		return rsp, nil
	}
	addLimit := 1
	limit := int(req.Limit) + addLimit //写死一个总数值，少于这个就是没有下一页了
	total := 0                         // 捞出出来的值总共有多少条
	var err error
	// 当前主态是否是管理员
	var isAdmin bool
	var comments []*model.Comment
	var nextPageCursor, previousPageCursor string

	// 先获取缓存数据
	commentsBaseRedisKey := cache.GetCommentBaseListGuestKey(req.PostUuid, req.NextPageCursor, req.Limit, req.OrderBy)
	if myIntlOpenID != "" {
		// 主态
		commentsBaseRedisKey = cache.GetCommentBaseListHostKey(req.PostUuid, myIntlOpenID, req.NextPageCursor, req.Limit, req.OrderBy)
		isAdmin = formatted.GetUserAdmin(myIntlOpenID)
	}
	commentsBaseCacheInfo, err := redis.GetClient().Get(c, commentsBaseRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(commentsBaseCacheInfo), &comments)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPostComments cache json.Unmarshal error.commentsBaseRedisKey: %s, err: %v", commentsBaseRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetRecentCommentsBaseJsonUnmarshalError, "Failed to obtain comments info, data parsing exception")
		}
	} else {
		// 没有缓存则直接查db数据
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostComments redis err: %v", err)
		}
		var conditions *dao.CommentListConditions
		conditions = &dao.CommentListConditions{
			PostUuid: req.PostUuid,
			Type:     1,
			Order:    make([]*dao.OrderConditions, 0),
		}
		if req.OrderBy == constants.OrderByTypeHot {
			conditions.Order = append(conditions.Order, &dao.OrderConditions{
				Column: "hot_num",
				IsDesc: true,
			})
		}
		conditions.Order = append(conditions.Order, &dao.OrderConditions{
			Column: "id",
			IsDesc: true,
		})

		if myIntlOpenID != "" {
			//审核通过的 或者 审核未通过 但是我发布的 我发布的暂且忽略 需要更改方法
			conditions.CompositeCondition = &dao.CompositeCondition{
				Operator: "OR",
				Conditions: []dao.ConditionInterface{
					dao.Conditions{
						Column: "is_audit",
						Symbol: "=",
						Value:  1,
					},
					dao.CompositeCondition{
						Operator: "AND",
						Conditions: []dao.ConditionInterface{
							dao.Conditions{
								Column: "is_audit",
								Symbol: "=",
								Value:  2,
							},
							dao.Conditions{
								Column: "intl_openid",
								Symbol: "=",
								Value:  myIntlOpenID,
							},
						},
					},
				},
			}
		} else {
			conditions.IsAudit = 1
		}

		// 查询类型：下一页数据
		if req.PageType == pb.PageType_NEXTPAGE {
			var idCursor int64
			// 如果是首页
			if req.NextPageCursor == "" {
				idCursor = 0
			} else {
				previousPageCursor = req.NextPageCursor
				if req.OrderBy == constants.OrderByTypeHot {
					combination, err := util.DecryptPageCursorCombination(req.NextPageCursor)
					if err != nil || (len(combination) == 0 || len(combination) != 2) {
						return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
					}
					// todo 可能在极限的时候会报错
					conditions.HotPageLimitCondition = &dao.CompositeCondition{
						Operator: "OR",
						Conditions: []dao.ConditionInterface{
							dao.Conditions{
								Column: "hot_num",
								Symbol: "<",
								Value:  combination[0],
							},
							dao.CompositeCondition{
								Operator: "AND",
								Conditions: []dao.ConditionInterface{
									dao.Conditions{
										Column: "hot_num",
										Symbol: "=",
										Value:  combination[0],
									},
									dao.Conditions{
										Column: "id",
										Symbol: "<",
										Value:  combination[1],
									},
								},
							},
						},
					}
					//conditions.LteHotNumStr = combination[0]
					//conditions.LteIdStr = combination[1]
				} else {
					idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
					if err != nil {
						return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
					}
					conditions.LtId = idCursor
				}
			}
			// 只有筛选是最热的情况下才需要区分置顶置底
			if req.OrderBy == constants.OrderByTypeHot {
				conditions.PosStatus = fmt.Sprintf("%d", constants.CommentPosStatusDefault)
			}
			// 查询非置顶且非置顶的评论列表
			comments, err = dao.CommentListNotTopBottom(conditions, limit)
			if err != nil {
				return nil, err
			}
		}
		commentListByte, err := json.Marshal(comments)
		if err == nil {
			redis.GetClient().SetEX(c, commentsBaseRedisKey, string(commentListByte), 2*time.Minute).Result()
			if myIntlOpenID != "" {
				keyKeys := cache.GetUserCommentCacheKeysKey(myIntlOpenID)
				redis.GetClient().SAdd(c, keyKeys, commentsBaseRedisKey)
				redis.GetClient().Expire(c, keyKeys, 2*time.Minute)
			}
		}
	}
	// 生成下一页的游标
	rsp.PageInfo.PreviousPageCursor = previousPageCursor
	if len(comments) > 0 {
		total = len(comments)
		if total > (limit - addLimit) { //判断是否有9条数据，没有的话直接切割会导致panic
			comments = comments[:(limit - addLimit)] //截取前10条， 第10条的下标是9
		}
		if req.OrderBy == constants.OrderByTypeHot {
			// 热度排序由于是按照热度来作为主排序的，这里生成游标的时候就不能只依赖id，不然会造成数据重复；正确的做法是热度值 + id
			cursorData := comments[len(comments)-1]
			cursorKeys := fmt.Sprintf("%d-%d", cursorData.HotNum, cursorData.ID)
			nextPageCursor, err = util.EncryptPageCursorCombination(cursorKeys)
		} else {
			nextPageCursor, err = util.EncryptPageCursorI(comments[len(comments)-1].ID)
		}
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetPostComments | Failed to create comments nextPageCursor")
		}
	}
	// newComments := comments
	if req.OrderBy == constants.OrderByTypeHot {
		newComments, err := FillTopBottomComments(c, req, limit-addLimit, comments)
		if err != nil {
			// 报错了记录错误日志即可，还是要返回原有的数据
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostComments | get post comment list at add top and bottom data failed, err:%v", err)
		} else {
			comments = newComments
		}
	}
	if len(comments) == 0 {
		rsp.PageInfo.IsFinish = true
		return rsp, nil
	}

	var postIntlOpenid string

	postInfo, err := tweet.GetPostInfoByUUID(c, req.PostUuid)
	if err != nil {
		return nil, errs.NewCustomError(c, code.GetPostFailed, "GetPostComments | Failed to get post base info")
	}
	if postInfo != nil {
		postIntlOpenid = postInfo.IntlOpenid
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostComments postIntlOpenid postInfo nil: %s", req.PostUuid)
	}

	// 接下来获取评论列表的分表的详细数据
	commentFormattedItems := make([]*pb.CommentItem, 0, len(comments))
	var commentUUIDsStr string
	var commentUUIDs []string
	var mu sync.Mutex
	for _, comment := range comments {
		commentUUIDsStr = fmt.Sprintf("%s_%s", commentUUIDsStr, comment.CommentUUID)
		commentUUIDs = append(commentUUIDs, comment.CommentUUID)
	}

	hash := sha256.New()
	hash.Write([]byte(commentUUIDsStr))
	hashValue := hash.Sum(nil)
	commentUUIDsMd5Str := hex.EncodeToString(hashValue)

	// 先获取缓存数据
	commentDetailsRedisKey := cache.GetCommentDetailListKey(commentUUIDsMd5Str, language)
	commentDetailsCacheInfo, err := redis.GetClient().Get(c, commentDetailsRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(commentDetailsCacheInfo), &commentFormattedItems)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPostComments commentDetails cache json.Unmarshal error.commentDetailsRedisKey: %s, err: %v", commentDetailsRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetRecentCommentsDetailJsonUnmarshalError, "Failed to obtain post comment info, data parsing exception")
		}
		// 修复前面获取到的评论id不包含回复的，会导致回复的评论id缺失，导致获取回复的点赞状态失败
		newCommentUuids := make([]string, 0, len(commentFormattedItems))
		for _, commentItem := range commentFormattedItems {
			newCommentUuids = append(newCommentUuids, commentItem.CommentUuid)
			if commentItem.Replies == nil || commentItem.Replies.DataList == nil {
				continue
			}
			for _, item := range commentItem.Replies.DataList {
				newCommentUuids = append(newCommentUuids, item.CommentUuid)
			}
		}
		if len(newCommentUuids) > 0 {
			// 重新赋值
			commentUUIDs = newCommentUuids
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostComments commentDetails redis err: %v", err)
		}
		// 获取所有的replay
		repliesUUids, err := dao.GetReplaysOfComments(commentUUIDs, myIntlOpenID, 3)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostComments GetReplaysOfComments err: %v", err)
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetPostComments | Failed to create GetReplaysOfComments")
		}
		//// 批量获取评论Content
		//repliesCounts, err := dao.GetReplaysOfCommentsCount(commentUUIDs, myIntlOpenID)
		//if err != nil {
		//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostComments GetReplaysOfCommentsCount err: %v", err)
		//	return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetPostComments | Failed to create GetReplaysOfCommentsCount")
		//}
		// 批量获取content state
		commentStatesUUids := make([]string, 0)
		for _, commentItem := range comments {
			commentStatesUUids = append(commentStatesUUids, commentItem.CommentUUID)
		}
		// 回复
		for _, replies := range repliesUUids {
			for _, reply := range replies {
				commentStatesUUids = append(commentStatesUUids, reply.CommentUUID)
			}
		}
		commentStates, err := dao.GetCommentStatesByCommentUUids(commentStatesUUids)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostComments GetCommentStatesByCommentUUids err: %v", err)
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetPostComments | Failed to create GetCommentStatesByCommentUUids")
		}
		// 帖子数据
		// postInfo, err := dao.GetPostNoIgnoreDel(req.PostUuid)
		// 批量获取前三条回复
		// 批量获取回复总数量
		// 批量获取前三条回复对应的Content 和 数量
		// 批量获取用户对应评论、前三条回复、at用户详情
		commentContents := dao.GetCommentContentList(commentStatesUUids)
		// log.WithFieldsContext(context.Background(), "logs_types", constants.LogType_Standalonesite).Infof("getPostComments time consuming to query GetCommentContentList, time consuming: %d", time.Since(nowTime).Milliseconds())
		var wg sync.WaitGroup
		for _, comment := range comments {
			commentFormatted := &pb.CommentItem{
				CommentUuid:     comment.CommentUUID,
				PostUuid:        comment.PostUUID,
				IntlOpenid:      comment.IntlOpenid,
				GameId:          comment.GameId,
				AreaId:          comment.AreaId,
				CreatedOn:       comment.CreatedOn,
				IsMine:          myIntlOpenID == comment.IntlOpenid,
				CanDelete:       (myIntlOpenID == comment.IntlOpenid) || isAdmin,
				CanReport:       myIntlOpenID != comment.IntlOpenid,
				IsAuthor:        comment.IntlOpenid == postIntlOpenid,
				CommentBubbleId: comment.CommentBubbleId,
				Language:        comment.Language,
			}
			wg.Add(1) // 增加 WaitGroup 的计数器
			go func(commentFormattedItem *pb.CommentItem, myHostIntlOpenID string) {
				defer recovery.CatchGoroutinePanic(context.Background())
				defer wg.Done() // 函数结束时减少计数器

				// commentContent, err := dao.GetCommentInfoByUUID(commentFormattedItem.CommentUuid)
				var commentContent *model.CommentContent
				for _, commentContentItem := range commentContents {
					if commentContentItem.CommentUUID == comment.CommentUUID {
						commentContent = commentContentItem
						break
					}
				}
				if commentContent == nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments GetCommentInfoByUUID err, comment_uuid:(%s), err=(%v)", commentFormattedItem.CommentUuid, err)
				} else {
					commentFormattedItem.Title = commentContent.Title
					commentFormattedItem.Content = commentContent.Content
					commentFormattedItem.PicUrls = strings.Split(commentContent.PicUrls, ",")
				}

				userInfo, err := user.GetUserDetailInfoByOpenid(c, myHostIntlOpenID, commentFormattedItem.IntlOpenid, language)
				if err != nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments GetUserDetailInfoByOpenid err, intl_openid:(%s), err=(%v)", commentFormattedItem.IntlOpenid, err)
				} else {
					commentFormattedItem.User = userInfo
				}

				var commentState *model.CommentState
				for _, commentStateItem := range commentStates {
					if commentStateItem.CommentUUID == commentFormattedItem.CommentUuid {
						commentState = commentStateItem
						break
					}
				}
				// commentState, err := dao.GetCommentStateByCommentUuid(commentFormattedItem.CommentUuid)
				if commentState == nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments GetCommentStateByCommentUuid err, comment_uuid:(%s), err=(%v)", commentFormattedItem.CommentUuid, err)
				} else {
					commentFormattedItem.UpvoteCount = int64(commentState.UpvoteCount)
				}

				// 获取评论的前三条回复
				reply := &pb.Replies{
					DataList: make([]*pb.RepliesItem, 0),
					PageInfo: &pb.PageInfo{},
				}
				// replyList, err := dao.CommentList(replyConditions, 3)
				replyList := repliesUUids[commentFormattedItem.CommentUuid]
				if err != nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments get reply err, comment_uuid:(%s), err=(%v)", commentFormattedItem.CommentUuid, err)
				}
				// count, err := dao.CommentCount(replyConditions)
				count := commentState.ReplyCount
				// if err != nil {
				// 	log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments get reply count err, comment_uuid:(%s), err=(%v)", commentFormattedItem.CommentUuid, err)
				// }

				if replyList != nil && len(replyList) > 0 {
					for _, replyItem := range replyList {
						// 统一记录评论和回复的uuid，一起查主态用户是否有点赞
						mu.Lock()
						commentUUIDs = append(commentUUIDs, replyItem.CommentUUID)
						mu.Unlock()
						replyFormatted := &pb.RepliesItem{
							CommentUuid: replyItem.CommentUUID,
							PostUuid:    replyItem.PostUUID,
							IntlOpenid:  replyItem.IntlOpenid,
							GameId:      replyItem.GameId,
							AreaId:      replyItem.AreaId,
							CreatedOn:   replyItem.CreatedOn,
							IsMine:      myHostIntlOpenID == replyItem.IntlOpenid,
							CanDelete:   (myHostIntlOpenID == replyItem.IntlOpenid) || isAdmin,
							CanReport:   myHostIntlOpenID != replyItem.IntlOpenid,
							IsAuthor:    postIntlOpenid == replyItem.IntlOpenid,
						}
						// replyContent, err := dao.GetCommentInfoByUUID(replyItem.CommentUUID)
						var replyContent *model.CommentContent
						for _, commentContentItem := range commentContents {
							if commentContentItem.CommentUUID == replyItem.CommentUUID {
								replyContent = commentContentItem
								break
							}
						}
						if replyContent == nil {
							log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments GetCommentInfoByUUID err, comment_uuid:(%s), err=(%v)", commentFormattedItem.CommentUuid, err)
						} else {
							replyFormatted.Title = replyContent.Title
							replyFormatted.Content = replyContent.Content
							replyFormatted.PicUrls = strings.Split(replyContent.PicUrls, ",")
						}

						replyUserInfo, err := user.GetUserDetailInfoByOpenid(c, myHostIntlOpenID, replyContent.IntlOpenid, language)
						if err != nil {
							log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments reply GetUserDetailInfoByOpenid err, intl_openid:(%s), err=(%v)", replyContent.IntlOpenid, err)
						} else {
							replyFormatted.User = replyUserInfo
						}

						if replyContent.AtIntlOpenid != "" {
							replyAtUserInfo, err := user.GetUserDetailInfoByOpenid(c, myHostIntlOpenID, replyContent.AtIntlOpenid, language)
							if err != nil {
								log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments replyat GetUserDetailInfoByOpenid err, intl_openid:(%s), err=(%v)", replyContent.AtIntlOpenid, err)
							} else {
								replyFormatted.AtUser = replyAtUserInfo
							}
						}

						// replyState, err := dao.GetCommentStateByCommentUuid(replyItem.CommentUUID)
						var replyState *model.CommentState
						for _, commentStateItem := range commentStates {
							if commentStateItem.CommentUUID == replyItem.CommentUUID {
								replyState = commentStateItem
								break
							}
						}
						if replyState == nil {
							log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments reply GetCommentStateByCommentUuid err, comment_uuid:(%s), err=(%v)", replyContent.CommentUUID, err)
						} else {
							replyFormatted.UpvoteCount = int64(replyState.UpvoteCount)
						}

						reply.DataList = append(reply.DataList, replyFormatted)
						reply.PageInfo = &pb.PageInfo{Total: int32(count)}
					}
				}
				commentFormattedItem.Replies = reply
			}(commentFormatted, myIntlOpenID) // 通过闭包传递参数

			commentFormattedItems = append(commentFormattedItems, commentFormatted)
		}

		wg.Wait()
		commentFormattedsByte, err := json.Marshal(commentFormattedItems)
		if err == nil {
			redis.GetClient().SetEX(c, commentDetailsRedisKey, string(commentFormattedsByte), 2*time.Minute).Result()
			if myIntlOpenID != "" {
				keyKeys := cache.GetUserCommentCacheKeysKey(myIntlOpenID)
				redis.GetClient().SAdd(c, keyKeys, commentDetailsRedisKey)
			}
		}
	}

	// 设置评论的状态 (不可提前，与用户相关)
	isPoster := false
	if postInfo != nil {
		isPoster = postInfo.IntlOpenid == myIntlOpenID
	}
	for _, commentItem := range commentFormattedItems {
		var curComment *model.Comment
		for _, comment := range comments {
			if commentItem.CommentUuid == comment.CommentUUID {
				curComment = comment
				break
			}
		}
		if curComment == nil {
			continue
		}
		canTop, canBottom, topBottomStatus := getPosInfo(c, curComment, isPoster, isAdmin)
		commentItem.CanTop = canTop
		commentItem.CanBottom = canBottom
		commentItem.TopBottomStatus = topBottomStatus
		commentItem.CanDelete = (myIntlOpenID == commentItem.IntlOpenid) || isAdmin
	}

	// log.WithFieldsContext(context.Background(), "logs_types", constants.LogType_Standalonesite).Infof("getPostComments time consuming to getPosInfo, time consuming: %d", time.Since(nowTime).Milliseconds())

	// 批量获取主态用户是否有对评论点赞
	if myIntlOpenID != "" {
		// 批量获取当前用户对评论和回复是否有点赞
		starCommentUUIDs, err := dao.BatchCheckUserCommentsStar(commentUUIDs, myIntlOpenID)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments BatchCheckUserCommentsStar err, intl_openid:(%s), err=(%v)", myIntlOpenID, err)
		}
		for _, starCommentUUID := range starCommentUUIDs {
			for _, commentItem := range commentFormattedItems {
				if starCommentUUID == commentItem.CommentUuid {
					commentItem.IsStar = true
				}
				if commentItem.Replies == nil || commentItem.Replies.DataList == nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments BatchCheckUserCommentsStar reply IsStar err, commentItem: %s intl_openid:(%s), err=(%v)", commentItem.CommentUuid, myIntlOpenID, err)
					continue
				}
				for _, replyItem := range commentItem.Replies.DataList {
					if starCommentUUID == replyItem.CommentUuid {
						replyItem.IsStar = true
					}
				}
			}
		}
	}

	rsp.List = commentFormattedItems
	if len(rsp.List) == 0 || total < limit {
		rsp.PageInfo.IsFinish = true
	} else {
		rsp.PageInfo.NextPageCursor = nextPageCursor
	}
	return rsp, nil
}

/**
 * 获取置顶置底评论: comment: 评论数据，curIntlOpenid: 当前用户intl_openid, isPoster: 是否是post作者
 */
func getPosInfo(c context.Context, comment *model.Comment, isPoster bool, isAdmin bool) (canTop, canBottom bool, topButtomStatus string) {
	log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Debugf("getPosInfo comment: %s, isPoster: %t, isAdmin: %t", comment.CommentUUID, isPoster, isAdmin)
	topButtomStatus = string(constants.ECommentPosStatusString_Unset)
	commentPosStatus := comment.PosStatus
	if commentPosStatus == constants.CommentPosStatusTop || commentPosStatus == constants.CommentPostStatusModerationTop || commentPosStatus == constants.CommentPostStatusPosterTop {
		// 当前置顶
		topButtomStatus = string(constants.ECommentPosStatusString_Top)
	} else if commentPosStatus == constants.CommentPosStatusBottom || commentPosStatus == constants.CommentPostStatusModerationBottom || commentPosStatus == constants.CommentPostStatusPosterBottom {
		// 当前置底
		topButtomStatus = string(constants.ECommentPosStatusString_Bottom)
	}
	// cms设置置顶、置底，无法操作
	if commentPosStatus == constants.CommentPosStatusTop || commentPosStatus == constants.CommentPosStatusBottom {
		canTop = false
		canBottom = false
	} else {
		canTop = isPoster || isAdmin
		canBottom = isPoster || isAdmin
	}

	// if commentPosStatus == constants.CommentPosStatusDefault || commentPosStatus == constants.CommentPosStatusGeneral {
	// 	// 未设置 版主或者作者可设置
	// 	canTop = isPoster || isAdmin
	// 	canBottom = isPoster || isAdmin
	// } else if commentPosStatus == constants.CommentPosStatusTop || commentPosStatus == constants.CommentPosStatusBottom {
	// 	// CMS设置置顶、置底，无法
	// 	canTop = false
	// 	canBottom = false
	// } else if commentPosStatus == constants.CommentPostStatusModerationTop || commentPosStatus == constants.CommentPostStatusPosterTop {
	// 	// 当前评论CMS置顶,无法再操作指定，可置底
	// 	canTop = false
	// 	canBottom = isAdmin || isPoster
	// } else if commentPosStatus == constants.CommentPostStatusModerationBottom || commentPosStatus == constants.CommentPostStatusPosterBottom {
	// 	// 当前评论作者置顶、置底，无法操作
	// 	canTop = isPoster || isAdmin
	// 	canBottom = false
	// }
	return
}

// FillTopBottomComments 填充置顶置底评论列表
func FillTopBottomComments(ctx context.Context, req *pb.GetPostCommentsReq, limit int,
	comments []*model.Comment) ([]*model.Comment, error) {
	var posStatusList []int
	if len(comments) < limit {
		posStatusList = append(posStatusList, constants.CommentPosStatusBottom)
		// 其他状态置底
		posStatusList = append(posStatusList, constants.CommentPostStatusModerationBottom)
		posStatusList = append(posStatusList, constants.CommentPostStatusPosterBottom)

	}
	if req.NextPageCursor == "" {
		posStatusList = append(posStatusList, constants.CommentPosStatusTop)
		posStatusList = append(posStatusList, constants.CommentPostStatusModerationTop)
		posStatusList = append(posStatusList, constants.CommentPostStatusPosterTop)
	}
	if len(posStatusList) == 0 {
		// 不是一头一尾的数据，直接返回
		return comments, nil
	}
	log.InfoContextf(ctx, "FillTopBottomComments postUuid:%v, posStatusList:%v, limit:%v, len(comments):%v",
		req.PostUuid, posStatusList, limit, len(comments))

	gotComments, err := dao.GetTopBottomComments(ctx, req.PostUuid, posStatusList)
	if err != nil {
		log.ErrorContextf(ctx, "dao.GetTopBottomComments error:%v, postUuid:%v, posStatusList:%v",
			err, req.GetPostUuid(), posStatusList)
		return nil, fmt.Errorf("fillTopBottomComments error:%w", err)
	}
	if len(gotComments) == 0 {
		// 无置顶置底数据，直接返回即可
		return comments, nil
	}
	var topComments, bottomComments []*model.Comment
	for _, comment := range gotComments {
		status := comment.PosStatus
		switch status {
		case constants.CommentPosStatusTop:
			// 置顶评论
			topComments = append(topComments, comment)
		case constants.CommentPostStatusModerationTop:
			// 管理员置顶
			topComments = append(topComments, comment)
		case constants.CommentPostStatusPosterTop:
			// 作者置顶
			topComments = append(topComments, comment)
		case constants.CommentPosStatusBottom:
			// 置底评论
			bottomComments = append(bottomComments, comment)
		case constants.CommentPostStatusModerationBottom:
			// 管理员置底
			bottomComments = append(bottomComments, comment)
		case constants.CommentPostStatusPosterBottom:
			// 作者置底
			bottomComments = append(bottomComments, comment)
		}
	}
	sort.SliceStable(topComments, func(i, j int) bool {
		// 降序
		return topComments[i].PosSetTime > topComments[j].PosSetTime
	})
	sort.SliceStable(bottomComments, func(i, j int) bool {
		// 升序
		return bottomComments[i].PosSetTime < bottomComments[j].PosSetTime
	})
	var newComments []*model.Comment
	// 添加置顶列表
	newComments = append(newComments, topComments...)
	// 添加原评论列表
	comments = RemoveTopBottomComments(ctx, comments, gotComments)
	newComments = append(newComments, comments...)
	// 添加置底列表
	newComments = append(newComments, bottomComments...)

	return newComments, nil
}

// RemoveTopBottomComments 从comments中移除置顶置底评论
func RemoveTopBottomComments(ctx context.Context, comments, topBottomComments []*model.Comment) []*model.Comment {
	if len(topBottomComments) == 0 {
		return comments
	}
	needDelCommentUUIDs := make(map[string]int8)
	for _, item := range topBottomComments {
		cuuid := item.CommentUUID
		if cuuid != "" {
			needDelCommentUUIDs[cuuid] = 1
		}
	}
	var results []*model.Comment
	for _, item := range comments {
		cuuid := item.CommentUUID
		_, exist := needDelCommentUUIDs[cuuid]
		if exist {
			continue
		}
		results = append(results, item)
	}
	return results
}

func CreateCommentFrequencyLimitCheck(c context.Context, userIntlOpenid string) error {
	minuteLimitRedisKey := cache.GetUserCreateCommentLimitMinuteKey(userIntlOpenid)
	if res, _ := redis.GetClient().Get(c, minuteLimitRedisKey).Result(); cast.ToInt64(res) >= config.GetConfig().UserLimit.Comment.Minute.Count {
		return errs.NewCustomError(c, code.UserCreateCommentLimitMinuteError, "CreateCommentFrequencyLimitCheck | Comments sent too frequently, please try again later.")
	}
	hourLimitRedisKey := cache.GetUserCreateCommentLimitHourKey(userIntlOpenid)
	if res, _ := redis.GetClient().Get(c, hourLimitRedisKey).Result(); cast.ToInt64(res) >= config.GetConfig().UserLimit.Comment.Hour.Count {
		return errs.NewCustomError(c, code.UserCreateCommentLimitHourError, "CreateCommentFrequencyLimitCheck | Comments sent too frequently, please try again later.")
	}
	dayLimitRedisKey := cache.GetUserCreateCommentLimitDayKey(userIntlOpenid)
	if res, _ := redis.GetClient().Get(c, dayLimitRedisKey).Result(); cast.ToInt64(res) >= config.GetConfig().UserLimit.Comment.Day.Count {
		return errs.NewCustomError(c, code.UserCreateCommentLimitDayError, "CreateCommentFrequencyLimitCheck | Comments sent too frequently, please try again later.")
	}

	return nil
}

func CreateCommentSetUserLimit(c context.Context, intlOpenID string) {
	minuteLimitRedisKey := cache.GetUserCreateCommentLimitMinuteKey(intlOpenID)
	if ok, err := redis.GetClient().SetNX(c, minuteLimitRedisKey, 1, time.Duration(config.GetConfig().UserLimit.Comment.Minute.Duration)*time.Minute).Result(); !ok && err == nil {
		redis.GetClient().Incr(c, minuteLimitRedisKey)
	}
	hourLimitRedisKey := cache.GetUserCreateCommentLimitHourKey(intlOpenID)
	if ok, err := redis.GetClient().SetNX(c, hourLimitRedisKey, 1, time.Duration(config.GetConfig().UserLimit.Comment.Hour.Duration)*time.Hour).Result(); !ok && err == nil {
		redis.GetClient().Incr(c, hourLimitRedisKey)
	}
	dayLimitRedisKey := cache.GetUserCreateCommentLimitDayKey(intlOpenID)
	if ok, err := redis.GetClient().SetNX(c, dayLimitRedisKey, 1, time.Duration(config.GetConfig().UserLimit.Comment.Day.Duration)*24*time.Hour).Result(); !ok && err == nil {
		redis.GetClient().Incr(c, dayLimitRedisKey)
	}
}

// CreatePostComment 创建动态的评论
func CreatePostComment(ctx context.Context, intlOpenID string, param CommentCreationReq, language string) (*pb.CommentItem, error) {
	var rsp *pb.CommentItem
	// TODO 优化查询用户信息逻辑
	userInfo, err := dao.GetUserByIntlOpenid(intlOpenID)
	if err != nil {
		return rsp, errs.NewCustomError(ctx, code.GetUserInfoError, "CreatePostComment | failed to get user info")
	}
	// 加载Post
	//comment := &model.Comment{}
	post, err := dao.GetPost(param.PostUUID)

	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePostComment err: %v\n", err)
		return rsp, errs.NewCustomError(ctx, code.CreateCommentFailed, "CreatePostComment | Failed to publish comment")
	}
	// 如果是未审核的数据，并且不是发帖者自己就报错，需要审核才能继续下一步操作
	if post.IsAudit == 2 && post.IntlOpenid != intlOpenID {
		return rsp, errs.NewCustomError(ctx, code.CreatePostCommentFailedAuditIng, "Failed to publish comments, dynamic review")
	}

	postStats, err := dao.GetPostStatsByPostUuid(param.PostUUID)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePostComment err: %v\n", err)
		return rsp, errs.NewCustomError(ctx, code.CreateCommentFailed, "CreatePostComment | Failed to get post content")
	}

	// 判断当前用户是否拥有评论气泡
	if param.CommentBubbleId != 0 {
		_, err := dao.GetUserCommentBubble(intlOpenID, param.CommentBubbleId)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePostComment GetUserCommentBubble err: %v\n", err)
			return rsp, errs.NewCustomError(ctx, code.CommentBubbleNotAvalible, "reatePostComment | Failed to get user comment bubble")
		}
	}

	// if postContent.CommentCount >= config.GetConfig().App.MaxCommentCount {
	// 	return rsp, errs.NewCustomError(ctx, code.MaxCommentCount, "The number of comments has reached the maximum limit")
	// }
	// ip := ctx.ClientIP()
	gameidStr := param.GameId
	areaidStr := param.AreaId

	currentTime := time.Now()
	commentUuid := util.CreateSnowflakeID()
	// 评论内容前置组装，好计算热度
	commentContentData := &model.CommentContent{
		CommentUUID:  commentUuid,
		IntlOpenid:   intlOpenID,
		AtIntlOpenid: param.AtIntlOpenid,
		Content:      param.Content,
		PicUrls:      strings.Join(param.PicUrls, ","),
	}
	// 由于是创建评论，这里只需要一个初始热度值就够了
	commentHotNum := hotService.CalculatingInitCommentHotNum(commentContentData)

	comment := &model.Comment{
		CommentUUID:     commentUuid,
		PostUUID:        post.PostUUID,
		IntlOpenid:      intlOpenID,
		IsAudit:         param.IsAudit,
		Type:            1,
		GameId:          gameidStr,
		AreaId:          areaidStr,
		CreatedOnMs:     currentTime.UnixMicro(),
		Language:        language,
		CommentBubbleId: param.CommentBubbleId,
		HotNum:          commentHotNum,
	}
	err = dao.CommentCreate(comment, currentTime)
	if err != nil {
		go func(ctx context.Context) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportPostCommentLog(ctx, comment, param.Content, userInfo, err)
		}(ctx)

		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.CreateComment err: %v\n", err)
		return rsp, errs.NewCustomError(ctx, code.CreateCommentFailed, "Failed to publish comment")
	}
	go func(ctx context.Context) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportPostCommentLog(ctx, comment, param.Content, userInfo, nil)
	}(ctx)

	err = dao.CommentContentCreate(commentContentData)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.CommentContentCreate err: %v\n", err)
		return rsp, errs.NewCustomError(ctx, code.CreateCommentFailed, "Failed to publish comment content")
	}
	// 写入审核表
	_, err = CreateCommentAudit(ctx, intlOpenID, comment.CommentUUID, gameidStr, areaidStr, param.IsAudit, "", commentContentData.Content, commentContentData.PicUrls)
	// 记录添加错误 需要回滚
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("CreateCommentAudit err: %v", err)
		return rsp, errs.NewCustomError(ctx, code.CreateCommentFailed, "Failed to publish comment")
	}
	// 写入es
	go PushCommentToES(comment, commentContentData)

	//if param.IsAudit == 2 {
	//	go PushCommentToSecurityDetection(ctx, intlOpenID, param, commentAudit)
	//}

	if param.IsAudit == 1 {
		// 更新Post评论数
		postStats.CommentCount++
		post.LatestRepliedOn = time.Now().Unix()
		dao.UpdatePost(post)
		dao.UpdatePostStatsComment(post.PostUUID, int32(postStats.CommentCount))

		// todo 更新索引
		// PushPostToSearch(post)
		hotNum := hotService.CalculatingPostHotNum(postStats)
		doc := map[string]interface{}{
			"comment_count":     postStats.CommentCount,
			"latest_replied_on": post.LatestRepliedOn,
			"hot_num":           hotNum,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)

		// 创建用户消息提醒
		postMaster, err := dao.GetUserByIntlOpenid(post.IntlOpenid)
		if err == nil && postMaster.IntlOpenid != intlOpenID {
			go writemessage.SetUserMessage(&model.Message{
				Type:                   constants.MsgTypeComment,
				PostUUID:               post.PostUUID,
				CommentUUID:            comment.CommentUUID,
				GameID:                 gameidStr,
				AreaID:                 areaidStr,
				SenderUserIntlOpenid:   intlOpenID,
				ReceiverUserIntlOpenid: postMaster.IntlOpenid,
			}, postMaster.IntlOpenid, constants.CommentMessageCount)
		}
		if len(param.AtIntlOpenid) > 0 {
			user, err := dao.GetUserByUsername(param.AtIntlOpenid)
			if err != nil || user.IntlOpenid == intlOpenID || user.IntlOpenid == postMaster.IntlOpenid {
				return rsp, nil
			}
			// 创建消息提醒
			go writemessage.SetUserMessage(&model.Message{
				Type:                   constants.MsgTypeComment,
				PostUUID:               post.PostUUID,
				CommentUUID:            comment.CommentUUID,
				GameID:                 gameidStr,
				AreaID:                 areaidStr,
				SenderUserIntlOpenid:   intlOpenID,
				ReceiverUserIntlOpenid: user.IntlOpenid,
			}, user.IntlOpenid, constants.CommentMessageCount)
		}

	}
	//获取当前用户信息
	userObj := formatted.ReturnDynamicProtoUserInfoFormatted(userInfo.Format())
	//查询用户标签值
	getUserGamePlayerInfoRsp, err := userService.GetUserNikkeBasicInfo(ctx, userInfo.IntlOpenid, "")
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CreatePostComment GetUserNikkeBasicInfo err: %v, myOpenid:%s, postItem.IntlOpenid:%s", err, userInfo.IntlOpenid, userInfo.IntlOpenid)
		// return nil, errs.NewCustomError(c, code.GetUserPlayerBasicInfoError, "GetUserAllInfoByOpenid | Failed to obtain user nikke game information, please check")
	} else {
		if userObj.GameTag == 1 {
			userObj.GameTagNum = getUserGamePlayerInfoRsp.TowerFloor
		} else if userObj.GameTag == 2 {
			userObj.GameTagNum = getUserGamePlayerInfoRsp.NormalProgress
		} else if userObj.GameTag == 3 {
			userObj.GameTagNum = getUserGamePlayerInfoRsp.HardProgress
		} else if userObj.GameTag == 4 {
			userObj.GameTagNum = getUserGamePlayerInfoRsp.OwnNikkeCnt
		} else if userObj.GameTag == 5 {
			userObj.GameTagNum = getUserGamePlayerInfoRsp.AvatarFrame
		} else if userObj.GameTag == 6 {
			userObj.GameTagNum = getUserGamePlayerInfoRsp.Costume
		}
	}

	rsp = &pb.CommentItem{
		CommentUuid:     comment.CommentUUID,
		PostUuid:        comment.PostUUID,
		IntlOpenid:      comment.IntlOpenid,
		Content:         param.Content,
		PicUrls:         param.PicUrls,
		CanDelete:       true,
		CanReport:       false, //自己发布的不能举报自己
		CreatedOn:       comment.CreatedOn,
		User:            userObj,
		IsAuthor:        comment.IntlOpenid == post.IntlOpenid,
		IsMine:          true,
		CommentBubbleId: param.CommentBubbleId,
	}
	go DeleteCommentListCache(ctx, param.PostUUID, intlOpenID, "", 10)
	return rsp, nil
}

func PushCommentToES(comment *model.Comment, commentContent *model.CommentContent) {
	status := 1
	// 先定义这个机审和人审的状态，因为官方帖子不需要审核的话这两个机审状态也不需要赋值
	var machineStatus, artificialStatus int
	if comment.IsAudit == 1 {
		// 发布评论都需要审核，这个几乎走不进去
		status = 2
	}
	// 处理用户intl_user_openid, 区别于intl_openid; ps: 这个intl_openid是一定会有的。
	var intlUserOpenid string
	if strings.Contains(comment.IntlOpenid, "-") && len(strings.Split(comment.IntlOpenid, "-")) > 1 {
		intlUserOpenid = strings.Split(comment.IntlOpenid, "-")[1]
	}
	data := []map[string]interface{}{{
		"id":                comment.ID,
		"post_uuid":         comment.PostUUID,
		"comment_uuid":      comment.CommentUUID,
		"intl_openid":       comment.IntlOpenid,
		"intl_user_openid":  intlUserOpenid,
		"type":              comment.Type,
		"text_risk_level":   0,
		"text_risk_type":    0,
		"pic_risk_level":    0,
		"pic_risk_type":     0,
		"status":            status,
		"audit_user":        "",
		"title":             "",
		"content":           commentContent.Content,
		"pic_urls":          commentContent.PicUrls,
		"audit_on":          0,
		"audit_introduce":   "",
		"created_on":        comment.CreatedOn,
		"modified_on":       comment.ModifiedOn,
		"game_id":           comment.GameId,
		"area_id":           comment.AreaId,
		"is_del":            0,
		"created_on_ms":     comment.CreatedOnMs,
		"language":          comment.Language,
		"machine_status":    machineStatus,    //机审状态默认是0
		"artificial_status": artificialStatus, // 人审的状态默认是0
	}}

	dao.EsBulkPushDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, "comment_uuid", data)
}

// CreateCommentAudit 创建评论审核记录
func CreateCommentAudit(c context.Context, intlOpenID string, commentUUID string, gameId, areaId string, isAudit int8, title, content, picUrls string) (commentAudit *model.CommentContentAudit, err error) {
	var auditOn int64
	auditOn = 0
	status := 1
	// 白名单用户直接生成审批通过的记录
	if isAudit == 1 {
		auditOn = time.Now().Unix()
		status = 2
	}
	commentAudit = &model.CommentContentAudit{
		CommentUUID:   commentUUID,
		IntlOpenid:    intlOpenID,
		Type:          1,
		TextRiskLevel: 0,
		TextRiskType:  100,
		PicRiskLevel:  0,
		PicRiskType:   100,
		Status:        status,
		AuditOn:       auditOn,
		AreaId:        areaId,
		GameId:        gameId,
	}
	if err = dao.CommentContentAuditCreate(commentAudit); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreateCommentAudit err: %v", err)
		return nil, err
	}

	return commentAudit, nil
}

// PushCommentToSecurityDetection 动态的评论内容走内容安全审核
func PushCommentToSecurityDetection(c context.Context, intlOpenID string, param CommentCreationReq, commentUuid string) {
	defer recovery.CatchGoroutinePanic(context.Background())

	userInfo, err := dao.GetUserByIntlOpenid(intlOpenID)
	if err != nil {
		return
	}

	accountInfo := security.AccountInfo{
		Account:  userInfo.IntlOpenid,
		RoleName: userInfo.Username,
		PlatId:   3,
	}

	var content string

	// 用于存储从文本字段抽离出来的图片链接、文本的数组、换行次数
	_, contentTexts, _, err := util.TrimHtmlLabel(param.Content)
	if err == nil {
		content = util.RemoveSymbol(strings.Join(contentTexts, " "))
	} else {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushCommentToSecurityDetection | trim html label failed, err:%v, content: %s", err, param.Content)
		content = param.Content
	}

	var commentAudit = &model.CommentContentAudit{CommentUUID: commentUuid}
	// 1.先检查文本内容
	commentAudit.TextRiskLevel, commentAudit.TextRiskType = security.PushTextToSecurityDetection(content, "", "", userInfo.ID, 3003, accountInfo)

	// 2.如果文本没问题，并且动态有图片，则走图片安全机审
	if len(param.PicUrls) == 0 {
		commentAudit.PicRiskLevel = 1
		commentAudit.PicRiskType = 100
	} else {
		// if commentAudit.TextRiskLevel != security.RISK_LEVLE_NORMAL && commentAudit.TextRiskLevel != security.RISK_LEVLE_DETECT_FAILED {
		commentAudit.PicRiskLevel, commentAudit.PicRiskType = security.PushPicToSecurityDetection(param.PicUrls, userInfo.ID, 3004, accountInfo)
		// }
	}
	// 审核通过
	if commentAudit.TextRiskType == 100 && commentAudit.TextRiskLevel == 1 && commentAudit.PicRiskType == 100 && commentAudit.PicRiskLevel == 1 {
		commentAudit.MachineStatus = 1 // 机审通过
	} else {
		// 机审不通过，转入人工审核
		commentAudit.MachineStatus = 2
	}
	if err := dao.CommentContentAuditUpdateV2(commentAudit); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PushCommentToSecurityDetection ds.UpdateCommentAudit err: %v", err)
	}
	auditDoc := map[string]interface{}{
		"text_risk_level": commentAudit.TextRiskLevel,
		"text_risk_type":  commentAudit.TextRiskType,
		"pic_risk_level":  commentAudit.PicRiskLevel,
		"pic_risk_type":   commentAudit.PicRiskType,
		"machine_status":  commentAudit.MachineStatus,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, commentAudit.CommentUUID, auditDoc)
	if commentAudit.MachineStatus == 1 {
		// 更新es的缓存
		dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetCommentIndex)
		// 审核通过的话
		err = CMSReviewCommentInfo(c, &pb.CMSReviewPostCommentReq{
			CommentUuids: []string{commentAudit.CommentUUID},
			Type:         1,
			UpdateUser:   "admin",
		}, true)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PushCommentToSecurityDetection review comment content pass, comment_uuid: %s, err: %v", commentAudit.CommentUUID, err)
		}
	}
}

func GetPostComment(commentUUID string) (*model.Comment, error) {
	return dao.GetCommentByUUID(commentUUID)
}

// 根据目标数据获取已审核的评论数据
func getAuditedPostComment(targetData *model.Comment) ([]string, error) {
	var commentList []string
	var err error
	if targetData.Type == int32(constants.DYNAMIC_COMMENT) {
		commentList, err = dao.GetAuditedCommentListByCommentUUID(targetData.CommentUUID)
	} else {
		commentList, err = dao.GetAuditedReplyListByCommentUUID(targetData.CommentUUID)
	}
	if err != nil {
		return nil, err
	}
	return commentList, nil
}

// 根据目标数据获取未审核的评论数据
func getUnAuditedPostComment(targetData *model.Comment) ([]string, error) {
	var commentList []string
	var err error
	if targetData.Type == int32(constants.DYNAMIC_COMMENT) {
		commentList, err = dao.GetUnAuditCommentListByCommentUUID(targetData.CommentUUID)
	} else {
		commentList, err = dao.GetUnAuditReplyListByCommentUUID(targetData.CommentUUID)
	}
	if err != nil {
		return nil, err
	}
	return commentList, nil
}

// intlopenid可能为空的，删除评论不删除回复
func DeletePostComment(c context.Context, comment *model.Comment, auditIntroduce, intlOpenid string, delType int, delReason int32) error {
	log.InfoContextf(c, "start DeletePostComment comment uuid:%v, deltype:%v, comment.IsAudit:%v",
		comment.CommentUUID, delType, comment.IsAudit)
	// 加载当前评论对应的post动态详情
	//post, err := dao.GetPost(comment.PostUUID)
	//if err != nil {
	//	return errs.NewCustomError(c, code.GetPostFailed, "DeletePostComment | Failed to get dynamic details")
	//}

	if comment.Type == int32(constants.DYNAMIC_REPLY) {
		TrigerUpdateCommentHotNum(c, comment.ReplyUUID, false)
	}

	postStats, err := dao.GetPostStatsByPostUuidIgnoreDel(comment.PostUUID)
	if err != nil {
		errs.NewCustomError(c, code.GetPostFailed, "DeletePostComment | Failed to get dynamic content details")
	}

	// 获取审批信息
	commentAudit, _ := dao.CommentContentAuditGet(0, comment.CommentUUID)
	if commentAudit != nil {
		// 删除审批信息
		data := map[string]interface{}{
			"audit_introduce": auditIntroduce,
			"deleted_on":      time.Now().Unix(),
			"is_del":          1,
			"del_type":        delType,
			"del_reason":      delReason,
		}
		if err = dao.CommentContentAuditDelete(commentAudit.ID, data); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteCommentAudit err: %v", err)
		}
	}

	// 更新举报管理记录状态
	reportIndex := config.GetConfig().ElasticSearchSetting.ReportIndex
	// 举报的内容类型：1是动态，2是评论，3是评论回复
	const ReportContentTypeComment int = 2 // 评论
	err = tweet.DeletePostCommentReport(c, reportIndex, ReportContentTypeComment, comment.CommentUUID, intlOpenid)
	if err != nil {
		log.ErrorContextf(c, "DeletePostCommentReport(comment) error:%v, CommentUUID:%v", err, comment.CommentUUID)
		return errs.NewCustomError(c, code.DeletePostFailed, "DeletePostCommentReport(comment) failed, err: %v", err)
	}

	//// 获取当前评论、回复下所有数量, 应该返回comment_uuid,根据comment_uuid删除
	postCommentIds, err := getAuditedPostComment(comment)
	if err != nil {
		errs.NewCustomError(c, code.GetCommentFailed, "DeletePostComment | Failed to get post comment")
	}
	// 删除评论p_comment表中评论的记录
	if err = dao.CommentDelete(comment.CommentUUID); err != nil {
		go func(ctx context.Context) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportCommentDelete(c, intlOpenid, comment, delType, err)
		}(c)

		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteComment err: %v", err)
	}
	// 上报
	go func(ctx context.Context) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportCommentDelete(c, intlOpenid, comment, delType, nil)
	}(c)
	// 删除评论p_comment_content_xx表中评论的记录
	if err = dao.CommentContentDelete(comment.CommentUUID, comment.IntlOpenid); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteCommentContent err: %v", err)
	}

	//// 批量删除评论和内容
	//err = dao.BatchCommentDeleteAndContent(postCommentIds)
	//if err != nil {
	//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetComentReplyParentIsDel err: %v", err)
	//}

	// 获取评论下的未审核的回复
	//unAuditCommentReplyUuids, err := getUnAuditedPostComment(comment)
	//if err != nil {
	//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUnAuditCommentReplyByCommentID err: %v", err)
	//}

	//// 将未审核的数据置为已忽略
	//err = dao.CommentContentAuditIgnoreReplyByCommentIds(unAuditCommentReplyUuids)
	//if err != nil {
	//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("IgnoreCommentReplyAuditByIds err: %v", err)
	//}

	if err == nil && comment.IsAudit == 1 && postStats != nil {
		// 删除已审批的评论回复数
		postStats.CommentCount -= int64(len(postCommentIds))
		if postStats.CommentCount < 0 {
			postStats.CommentCount = 0
		}
		dao.UpdatePostStatsComment(postStats.PostUUID, int32(postStats.CommentCount))
		hotNum := hotService.CalculatingPostHotNum(postStats)
		doc := map[string]interface{}{
			"comment_count": postStats.CommentCount,
			"hot_num":       hotNum,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postStats.PostUUID, doc)
	}

	// 更新es数据
	doc := map[string]interface{}{
		"status":          3,
		"modified_on":     time.Now().Unix(),
		"audit_introduce": auditIntroduce,
		"is_del":          1,
		"del_type":        delType,
		"del_reason":      delReason,
	}
	log.InfoContextf(c, "dao.EsUpdateDoc commentUUID:%v, doc:%v", util.ToJson(doc))
	_, esErr := dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, comment.CommentUUID, doc)
	if esErr != nil {
		log.ErrorContextf(c, "dao.EsUpdateDoc error:%v, commentUUID:%v, doc:%v", esErr, comment.CommentUUID, util.ToJson(doc))
	}

	// 评论不是当前用户的发送消息通知
	if comment.IntlOpenid != intlOpenid {
		// 发送消息通知
		writemessage.SetUserMessage(&model.Message{
			Type:                   constants.MsgTypeOfficialDeleteComment,
			PostUUID:               comment.PostUUID,
			CommentUUID:            comment.CommentUUID,
			ReplyUUID:              comment.ReplyUUID,
			Reply2ReplyUUID:        comment.Reply2ReplyUUID,
			GameID:                 comment.GameId,
			AreaID:                 comment.AreaId,
			ReceiverUserIntlOpenid: comment.IntlOpenid,
			SenderUserIntlOpenid:   fmt.Sprintf("%s", comment.GameId),
			ExtInfo:                fmt.Sprintf("{\"del_type\": %d, \"del_reason\": %d}", delType, delReason),
		}, comment.IntlOpenid, constants.SiteMessageCount)
	}

	go DeleteCommentListCache(c, comment.PostUUID, intlOpenid, "", 10)
	// 删除可能在审核当中的数据
	if comment.IsAudit == 2 {
		go func(commentUuid string) {
			redis.GetClient().ZRem(context.Background(), cache.WaitingForReviewCommentToKafkaKeys(), commentUuid)
		}(comment.CommentUUID)
	}

	return nil
}

func createPostPreHandler(c context.Context, commentUUID string, atUserOpenIDs string, postUUID string, intlOpenid string) (*model.Post, *model.PostStats, *model.Comment, *model.UserContent, error) {
	// 加载Comment
	comment, err := dao.GetCommentByUUID(commentUUID)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetCommentByID err: %v", err)
		return nil, nil, nil, nil, errs.NewCustomError(c, code.CreateReplyFailed, "createPostPreHandler | Comment reply failed")
	}
	if postUUID == "" {
		// 如果postUuid不存在就用评论里面的postUuid
		postUUID = comment.PostUUID
	}
	// 加载comment的post-#2
	post, err := dao.GetPost(postUUID)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostByID err: %v", err)
		return nil, nil, nil, nil, errs.NewCustomError(c, code.CreateReplyFailed, "createPostPreHandler | Comment reply failed at get post")
	}

	postStats, err := dao.GetPostStatsByPostUuid(postUUID)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostContentByID err: %v", err)
		return nil, nil, nil, nil, errs.NewCustomError(c, code.CreateReplyFailed, "createPostPreHandler | Comment reply failed at get post content")
	}

	// if postContent.CommentCount >= config.GetConfig().App.MaxCommentCount {
	// 	return nil, nil, nil, nil, errs.NewCustomError(c, code.MaxCommentCount, "The number of comments has reached the maximum limit")
	// }

	// 如果帖子还没有审核，但是回复的是自己的动态那就直接
	if post.IsAudit == 2 && post.IntlOpenid != intlOpenid {
		return nil, nil, nil, nil, errs.NewCustomError(c, code.CreatePostCommentFailedAuditIng, "Failed to publish comments, dynamic review")
	}

	// 必须是回复自己的评论
	if comment.IsAudit == 2 && comment.IntlOpenid != intlOpenid {
		return nil, nil, nil, nil, errs.NewCustomError(c, code.CreatePostCommentReplyFailedAuditIng, "CreatePostCommentReplyFailedAuditIng")
	}

	/*
		回复自己
		if userID == atUserID {
				atUserID = 0
			}
	*/
	var atUserInfos *model.UserContent
	if len(atUserOpenIDs) > 0 {
		// 检测目前用户是否存在
		users, _ := dao.GetUserByIntlOpenid(atUserOpenIDs)
		if users != nil {
			atUserInfos = users
		}
	}

	return post, postStats, comment, atUserInfos, nil
}

// GetUserComments 获取用户评论列表·
func GetUserComments(c context.Context, req *pb.GetUserCommentListReq, myIntlOpenID, gameId, areaId, language string) (*pb.GetUserCommentListRsp, error) {
	rsp := &pb.GetUserCommentListRsp{
		List:     make([]*pb.UserCommentItem, 0),
		PageInfo: &pb.PageInfo{},
	}
	var err error
	var comments []*model.Comment
	var nextPageCursor, previousPageCursor string
	var addLimit = 1 //加量分页
	var limit = addLimit + int(req.Limit)
	var pageTotal int //当前页面的数量

	// 先获取缓存数据
	queryIntlOpenID := req.IntlOpenid

	commentsBaseRedisKey := cache.GetUserCommentBaseListGuestKey(req.IntlOpenid, req.NextPageCursor, req.Limit)
	if myIntlOpenID == queryIntlOpenID {
		// 主态
		commentsBaseRedisKey = cache.GetUserCommentBaseListHostKey(myIntlOpenID, req.NextPageCursor, req.Limit)
	} else {
		privacySwitch, err := privacy.GetUserPrivacySwitch(c, queryIntlOpenID)
		if err != nil {
			return rsp, err
		}
		if privacySwitch.ShowMyComment == 0 {
			rsp.PageInfo.IsFinish = true
			return rsp, nil
		}
	}
	commentsBaseCacheInfo, err := redis.GetClient().Get(c, commentsBaseRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(commentsBaseCacheInfo), &comments)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserComments cache json.Unmarshal error.commentsBaseRedisKey: %s, err: %v", commentsBaseRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetUserCommentsBaseJsonUnmarshalError, "Failed to obtain user comment info, data parsing exception")
		}
	} else {
		// 没有缓存则直接查db数据
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserComments redis err: %v", err)
		}

		// 查询类型：下一页数据
		if req.PageType == pb.PageType_NEXTPAGE {
			previousPageCursor = req.NextPageCursor
			var idCursor int64
			if req.NextPageCursor != "" {
				previousPageCursor = req.NextPageCursor
				idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
				if err != nil {
					return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
				}
			}

			conditions := &dao.CommentListConditions{
				IntlOpenid: req.IntlOpenid,
				Order:      make([]*dao.OrderConditions, 0),
			}
			conditions.Order = append(conditions.Order, &dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			})
			if myIntlOpenID == queryIntlOpenID {
				conditions.CompositeCondition = &dao.CompositeCondition{
					Operator: "OR",
					Conditions: []dao.ConditionInterface{
						dao.Conditions{
							Column: "is_audit",
							Symbol: "=",
							Value:  1,
						},
						dao.Conditions{
							Column: "is_audit",
							Symbol: "=",
							Value:  2,
						},
					},
				}
			} else {
				conditions.IsAudit = 1
			}
			if idCursor > 0 {
				conditions.LtId = idCursor
			}
			comments, err = dao.CommentList(conditions, limit)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetCommentFailed, "GetUserComments | Failed to get post comment info")
			}

			commentListByte, err := json.Marshal(comments)
			if err == nil {
				redis.GetClient().SetEX(c, commentsBaseRedisKey, string(commentListByte), 2*time.Minute).Result()
			}
		}
	}
	// 生成下一页的游标
	if len(comments) > 0 {
		pageTotal = len(comments)
		if pageTotal == limit {
			// 切割当前评论数据
			comments = comments[0:(limit - addLimit)]
		}
		nextPageCursor, err = util.EncryptPageCursorI(comments[len(comments)-1].ID)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetCommentFailed, "GetUserComments | Failed to create comments nextPageCursor")
		}
	} else {
		rsp.PageInfo.IsFinish = true
		return rsp, nil
	}
	// 获取板块多语言信息
	allPlateLangMap, err := plateService.GetAllPlateNameMap(c)
	if err != nil {
		return rsp, err
	}

	// 接下来获取评论列表的分表的详细数据
	commentFormattedItems := make([]*pb.UserCommentItem, 0, len(comments))
	var commentUUIDsStr string
	var commentUUIDs []string
	var postUUIDs []string
	for _, comment := range comments {
		commentUUIDsStr = fmt.Sprintf("%s_%s", commentUUIDsStr, comment.CommentUUID)
		commentUUIDs = append(commentUUIDs, comment.CommentUUID)
		postUUIDs = append(postUUIDs, comment.PostUUID)
	}

	hash := sha256.New()
	hash.Write([]byte(commentUUIDsStr))
	hashValue := hash.Sum(nil)
	commentUUIDsMd5Str := hex.EncodeToString(hashValue)

	// 先获取缓存数据
	commentDetailsRedisKey := cache.GetCommentDetailListKey(commentUUIDsMd5Str, language)
	commentDetailsCacheInfo, err := redis.GetClient().Get(c, commentDetailsRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(commentDetailsCacheInfo), &commentFormattedItems)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserComments commentDetails cache json.Unmarshal error.commentDetailsRedisKey: %s, err: %v", commentDetailsRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetUserCommentsDetailJsonUnmarshalError, "Failed to obtain post comment info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserComments commentDetails redis err: %v", err)
		}

		var wg sync.WaitGroup
		// 判断这些帖子是否都是主态用户发布的，如果是则只需要查询一次用户信息
		var myUserInfo *pbUser.UserInfo
		isAllMyComments := true
		for _, comment := range comments {
			if comment.IntlOpenid != myIntlOpenID {
				isAllMyComments = false
			}
		}
		if isAllMyComments {
			myUserInfo, err = user.GetUserDetailInfoByOpenid(c, myIntlOpenID, myIntlOpenID, language)
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetUserComments GetUserDetailInfoByOpenid isAllMyComments err, intl_openid:(%s), err=(%v)", myIntlOpenID, err)
			}
		}
		// 批量获取评论内容的状态数据
		commentStates, err := dao.GetCommentStatesByCommentUUids(commentUUIDs)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetUserComments BatchGetCommentStateByCommentUuid err, comment_uuid:(%v), err=(%v)", commentUUIDs, err)
		}
		// 批量获取帖子数据
		postList := make([]*model.Post, 0)
		if len(postUUIDs) > 0 {
			postList, err = dao.GetPostList(&dao.PostConditions{PostUuids: postUUIDs, IsIgnore: true}, 0, 0)
			if err != nil {
				// 没有查询到这条数据，直接报错
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserComments | get post list failed, post_uuids: %v, err:%v", postUUIDs, err)
			}
		}
		for _, comment := range comments {
			commentFormatted := &pb.UserCommentItem{
				CommentUuid:      comment.CommentUUID,
				PostUuid:         comment.PostUUID,
				Type:             comment.Type,
				IntlOpenid:       comment.IntlOpenid,
				GameId:           comment.GameId,
				AreaId:           comment.AreaId,
				CreatedOn:        comment.CreatedOn,
				ReplyUuid:        comment.ReplyUUID,
				ReplyToReplyUuid: comment.Reply2ReplyUUID,
				// IsDel:            int32(commentItem.IsDel),
			}
			wg.Add(1) // 增加 WaitGroup 的计数器
			go func(commentFormattedItem *pb.UserCommentItem, myHostIntlOpenID string, isAllMyCommentsBool bool, myUserInfoItem *pbUser.UserInfo, commentStateItems []*model.CommentState, postInfoList []*model.Post) {
				defer recovery.CatchGoroutinePanic(context.Background())
				defer wg.Done() // 函数结束时减少计数器

				commentContent, err := dao.GetCommentInfoByUUID(commentFormattedItem.CommentUuid)
				if err != nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetUserComments GetCommentInfoByUUID err, comment_uuid:(%s), err=(%v)", commentFormattedItem.CommentUuid, err)
				} else {
					commentFormatted.Title = commentContent.Title
					commentFormatted.Content = commentContent.Content
					commentFormatted.PicUrls = strings.Split(commentContent.PicUrls, ",")
				}
				if len(commentStates) > 0 {
					for _, commentStateItem := range commentStateItems {
						if commentStateItem.CommentUUID == commentFormattedItem.CommentUuid {
							commentFormatted.UpvoteCount = int64(commentStateItem.UpvoteCount)
						}
					}
				}
				// commentState, err := dao.GetCommentStateByCommentUuid(commentFormattedItem.CommentUuid)
				// if err != nil {
				// 	log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetUserComments GetCommentStateByCommentUuid err, comment_uuid:(%s), err=(%v)", commentFormattedItem.CommentUuid, err)
				// } else {
				// 	commentFormatted.UpvoteCount = int64(commentState.UpvoteCount)
				// }

				if isAllMyCommentsBool && myUserInfoItem != nil {
					commentFormattedItem.User = myUserInfoItem
				} else {
					userInfo, err := user.GetUserDetailInfoByOpenid(c, myHostIntlOpenID, commentFormattedItem.IntlOpenid, language)
					if err != nil {
						log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetUserComments GetUserDetailInfoByOpenid err, intl_openid:(%s), err=(%v)", commentFormattedItem.IntlOpenid, err)
					} else {
						commentFormattedItem.User = userInfo
					}
				}

				// 评论对应的动态
				// TODO 这里要优化一下，加上帖子缓存
				postItemInfo := &model.Post{}
				if len(postInfoList) > 0 {
					for _, postItem := range postInfoList {
						if postItem.PostUUID == commentFormattedItem.PostUuid {
							postItemInfo = postItem
							if plateLangMap, ok := allPlateLangMap[postItem.PlateID]; ok {
								commentFormattedItem.PlateId = postItem.PlateID
								if plateLang, ok2 := plateLangMap[language]; ok2 {
									commentFormattedItem.PlateName = plateLang
								} else if plateEnName, ok3 := plateLangMap["en"]; ok3 {
									commentFormattedItem.PlateName = plateEnName
								}
							}
							commentFormattedItem.PostDel = int32(postItem.IsDel)
						}
					}
				}
				// postItem, err := dao.GetPostNoIgnoreDel(commentFormattedItem.PostUuid)
				// if err != nil {
				// 	log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetUserComments GetPostNoIgnoreDel err, post_uuid:(%s), err=(%v)", commentFormattedItem.PostUuid, err)
				// } else {
				// 	if plateLangMap, ok := allPlateLangMap[postItem.PlateID]; ok {
				// 		commentFormattedItem.PlateId = postItem.PlateID
				// 		if plateLang, ok2 := plateLangMap[language]; ok2 {
				// 			commentFormattedItem.PlateName = plateLang
				// 		} else if plateEnName, ok3 := plateLangMap["en"]; ok3 {
				// 			commentFormattedItem.PlateName = plateEnName
				// 		}
				// 	}
				// 	commentFormattedItem.PostDel = int32(postItem.IsDel)
				// }
				postContent, err := dao.GetPostContentOrOfficialData(commentFormattedItem.PostUuid, postItemInfo.IsOfficial, language, true)
				if err != nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetUserComments PostContentGet err, post_uuid:(%s), err=(%v)", commentFormattedItem.PostUuid, err)
				} else {
					commentFormattedItem.PostContext = postContent.Content
					commentFormattedItem.PostTitle = postContent.Title
				}

				if commentFormattedItem.Type == 2 {
					commentReplyUuid := commentFormattedItem.ReplyUuid
					if commentFormatted.ReplyToReplyUuid != "" {
						// 三级回复捞取2级回复内容
						commentReplyUuid = commentFormatted.ReplyToReplyUuid
					}
					replyCommentContent, err := dao.GetCommentInfoByUUID(commentReplyUuid)
					if err != nil {
						commentFormatted.CommentDel = 1
						log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetUserComments GetCommentInfoByUUID err, comment_uuid:(%s), err=(%v)", commentFormattedItem.ReplyUuid, err)
					} else {
						commentFormatted.CommentContext = replyCommentContent.Content
					}
				}

				commentFormattedItems = append(commentFormattedItems, commentFormatted)
			}(commentFormatted, myIntlOpenID, isAllMyComments, myUserInfo, commentStates, postList) // 通过闭包传递参数
		}
		wg.Wait()

		sort.Slice(commentFormattedItems, func(i, j int) bool {
			return commentFormattedItems[i].CreatedOn > commentFormattedItems[j].CreatedOn
		})
		commentFormattedsByte, err := json.Marshal(commentFormattedItems)
		if err == nil {
			redis.GetClient().SetEX(c, commentDetailsRedisKey, string(commentFormattedsByte), 2*time.Minute).Result()
		}
	}
	rsp.List = commentFormattedItems

	if len(rsp.List) == 0 || pageTotal < limit {
		rsp.PageInfo.IsFinish = true
	} else {
		rsp.PageInfo.NextPageCursor = nextPageCursor
	}
	rsp.PageInfo.PreviousPageCursor = previousPageCursor
	return rsp, nil
}

// isMachineStatus 是否是机器审核
func CMSReviewCommentInfo(ctx context.Context, req *pb.CMSReviewPostCommentReq, isMachineReview bool) error {
	req.CommentUuids = util.RemoveDuplicateString(req.CommentUuids)
	if len(req.CommentUuids) == 0 {
		return nil
	}
	// 未找到的评论id
	var notFoundCommentUuids []string = make([]string, 0)
	var wg sync.WaitGroup
	var reviewErr error
	commentList, reviewErr := dao.CommentList(&dao.CommentListConditions{
		IsDel:        0,
		CommentUuids: req.CommentUuids,
	}, 0)
	if reviewErr != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Get comment failed， err: %v", reviewErr)
		return errs.NewCustomError(ctx, code.GetCommentFailed, "Get comment failed, please check comment uuid")
	}
	if len(commentList) == 0 {
		// 如果没有找到数据也是提示状态异常这个报错
		// todo 这个可能会存在一种情况，cms传递了一份空数据过来也会报这个状态异常
		return errs.NewCustomError(ctx, code.StatusIsChanged, "review post comment failed, post comment is empty!")
	}
	// 只要有已经审核通过的，直接退出
	for _, comment := range commentList {
		if comment.IsAudit == 1 {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("There are moderated comments")
			return errs.NewCustomError(ctx, code.StatusIsChanged, "There are moderated comments")
		}
	}
	var postStatsHandlerChannel = make(chan string, 1)
	for _, item := range req.CommentUuids {
		var commentInfo *model.Comment
		for _, commentItem := range commentList {
			if commentItem.CommentUUID == item {
				commentInfo = commentItem
				break
			}
		}
		if commentInfo == nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Get comment failed， comment uuid not find, uuid: %s", item)
			notFoundCommentUuids = append(notFoundCommentUuids, item)
			continue
		}
		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(commentInfo *model.Comment, req *pb.CMSReviewPostCommentReq, isMachineReview bool) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器
			if commentInfo.IsAudit == 1 {
				// 已审核
				reviewErr = errs.NewCustomError(context.Background(), code.CommentAlreadyReviewed, "CMSReviewCommentInfo | comment already reviewed")
				return
			}

			if constants.PostCommentReviewT(req.Type) == constants.PostCommentReviewPass {
				if commentInfo.Type == int32(constants.DYNAMIC_REPLY) {
					TrigerUpdateCommentHotNum(ctx, commentInfo.ReplyUUID, true)
				}
				// 查询评论内容，是否有艾特用户
				commentContentInfo, err := dao.GetCommentInfoByUUID(commentInfo.CommentUUID)
				if err != nil {
					reviewErr = err
					return
				}
				post, err := dao.GetPostNoIgnoreDel(commentInfo.PostUUID)
				if err != nil {
					reviewErr = errs.NewCustomError(context.Background(), code.GetPostFailed, "tweet not find! ")
					return
				}
				err = CommentReviewPass(context.Background(), req, isMachineReview, commentInfo, commentContentInfo, post)
				if err != nil {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewCommentInfo | CommentReviewPass failed, comment_uuid: %s, err: %v", commentInfo.CommentUUID, err)
					reviewErr = err
					return
				}
				// 写入通道中，通道里面执行更新逻辑
				if post.IsDel == 0 {
					postStatsHandlerChannel <- post.PostUUID
				}
			} else {
				err := CommentReviewReject(context.Background(), req, commentInfo)
				if err != nil {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewCommentInfo | CommentReviewPass failed, comment_uuid: %s, err: %v", commentInfo.CommentUUID, err)
					reviewErr = err
					return
				}
			}
		}(commentInfo, req, isMachineReview) // 通过闭包传递参数

	}
	go func() {
		// 读取通道里面的值，执行更新操作
		for postUuid := range postStatsHandlerChannel {
			// 获取p_post_stats
			postStats, err := dao.GetPostStatsByPostUuid(postUuid)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewCommentInfo | CommentReviewPass get post stats data failed, post_uuid: %s, err: %v", postUuid, err)
				return
			}
			// 更新p_post表评论数
			postStats.CommentCount++
			err = dao.UpdatePostStatsComment(postStats.PostUUID, int32(postStats.CommentCount))
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewCommentInfo | CommentReviewPass update post stats failed, post_uuid: %s, err: %v", postUuid, err)
				return
			}
			// 更新动态es
			hotNum := hotService.CalculatingPostHotNum(postStats)
			postDoc := map[string]interface{}{
				"comment_count": postStats.CommentCount,
				"hot_num":       hotNum,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postStats.PostUUID, postDoc)
		}
	}()
	wg.Wait()
	close(postStatsHandlerChannel)
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetCommentIndex)

	return reviewErr
}

// CMSDeletePostComment CMS管理端删除评论
func CMSDeletePostComment(ctx context.Context, req *pb.CMSReviewPostCommentReq) error {
	log.InfoContextf(ctx, "start CMSDeletePostComment req:%v", req.String())
	req.CommentUuids = util.RemoveDuplicateString(req.CommentUuids)
	if len(req.CommentUuids) == 0 {
		return nil
	}
	conditions := &dao.CommentListConditions{
		CommentUuids: req.CommentUuids,
		IsDel:        0,
	}
	comments, err := dao.CommentList(conditions, 0)
	if err != nil {
		return errs.NewCustomError(ctx, code.GetCommentFailed, "CMSDeletePostComment | Failed to get comment details")
	}
	// 判断获取到的评论是否等于需要删除评论的长度，不是的话直接退出
	if len(comments) != len(req.CommentUuids) {
		return errs.NewCustomError(ctx, code.StatusIsChanged, "There are deleted comments")
	}
	var delType int
	if req.Type == int32(constants.PostCommentReportDelete) {
		delType = int(constants.POST_COMMENT_CONTENT_DELETE_TYPE_BREPORT)
	} else {
		delType = int(constants.POST_COMMENT_CONTENT_DELETE_TYPE_BADMIN)
	}
	log.InfoContextf(ctx, "comments len:%v", len(comments))
	for _, comment := range comments {

		err := DeletePostComment(ctx, comment, req.AuditIntroduce, "", delType, req.DelReason)
		if err != nil {
			return err
		}
		// TODO 创建官方消息通知
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetCommentIndex)
	err = CMSUpdatePostCommentReport(ctx, req, false)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("CMSDeletePost CMSUpdatePostReport err: %v, req is %s", err, req)
		return errs.NewCustomError(ctx, code.UpdateContentReportError, "CMSDeletePost | ignore post report failed")
	}
	// 写入消息
	go CmsDeletePostCommentReportSendSiteMsg(req.CommentUuids, delType, req.DelReason)
	return nil
}

func CMSUpdatePostCommentReport(ctx context.Context, req *pb.CMSReviewPostCommentReq, checkReportStatus bool) error {
	req.CommentUuids = util.RemoveDuplicateString(req.CommentUuids)
	if len(req.CommentUuids) == 0 {
		return nil
	}
	status := 2
	if constants.PostCommentReviewT(req.Type) == constants.PostCommentReviewDelete || constants.PostCommentReviewT(req.Type) == constants.PostCommentReportDelete {
		status = 3
	}
	if checkReportStatus {
		// 只查询未处理的状态
		reportList, err := dao.GetCommentReportStatus(req.CommentUuids, 1)
		if err != nil {
			return err
		}
		// 这里一条帖子会有多个举报记录
		reportUuidMap := make(map[string]int)
		for _, reportItem := range reportList {
			reportUuidMap[reportItem.ContentUuid] += 1
		}
		for _, commentUuid := range req.CommentUuids {
			if _, ok := reportUuidMap[commentUuid]; !ok {
				// 数据对不上，可能是已经被审核了，或者是被删除了
				log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("CMSUpdatePostCommentReport Data status does not match, content_uuid: %v ", req.CommentUuids)
				return errs.NewCustomError(ctx, code.StatusIsChanged, "Data status does not match")
			}
		}
	}
	for _, commentUuid := range req.CommentUuids {
		err := dao.UpdateCommentReportStatus(commentUuid, status)
		if err != nil {
			return err
		}

		// 更新es
		doc := map[string]interface{}{
			"status":      status,
			"update_user": req.UpdateUser,
			"modified_on": time.Now().Unix(),
		}
		boolQuery := es7.NewBoolQuery()
		contentUuidQuery := es7.NewTermQuery("content_uuid", commentUuid)
		contentTypeQuery := es7.NewTermQuery("content_type", 2)
		boolQuery.Must(contentUuidQuery, contentTypeQuery)
		scritpStr := `
            ctx._source.status = params.status;
			ctx._source.update_user = params.update_user;
            ctx._source.modified_on = params.modified_on;
		`
		dao.EsUpdateDocByCondition(config.GetConfig().ElasticSearchSetting.ReportIndex, boolQuery, scritpStr, doc)
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.ReportIndex)

	return nil
}

func CmsDeletePostCommentReportSendSiteMsg(commentUuids []string, delType int, delReason int32) {
	reports, err := dao.PostReportListWithIgnoreDelete(&dao.PostReportConditions{
		ContentUuids: commentUuids,
		ContentTypes: []int32{2, 3},
	}, 0, len(commentUuids))
	if err != nil {
		// 查询报错，直接返回
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CmsDeletePostCommentReportSendSiteMsg | get report data failed, err: %v", err)
		return
	}
	for _, item := range reports {
		commentInfo, err := dao.GetCommentByUUIDWithIgnoreDelete(item.ContentUuid)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CmsDeletePostCommentReportSendSiteMsg | GetCommentByUUIDWithIgnoreDelete get comment err: %v", err)
			continue
		}
		commentContentInfo, err := dao.GetCommentInfoByUUIDWithIgnoreDelete(item.ContentUuid)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CmsDeletePostCommentReportSendSiteMsg | GetCommentByUUIDWithIgnoreDelete get comment content err: %v", err)
			continue
		}
		// 删除评论的时候已经发送过消息给评论创建者了，这里就没有必要继续发了
		//writemessage.SetUserMessage(&model.Message{
		//	Type:                   constants.MsgTypeOfficialDeleteComment,
		//	PostUUID:               commentInfo.PostUUID,
		//	CommentUUID:            commentInfo.CommentUUID,
		//	ReplyUUID:              commentInfo.ReplyUUID,
		//	Reply2ReplyUUID:        commentInfo.Reply2ReplyUUID,
		//	GameID:                 commentInfo.GameId,
		//	AreaID:                 commentInfo.AreaId,
		//	Content:                commentContentInfo.Content, // 因为这条评论被删除了，用户也看不到了，更不可能再去编辑了，直接写死当前评论的内容
		//	ReceiverUserIntlOpenid: item.ReportedIntlOpenid,
		//	SenderUserIntlOpenid:   fmt.Sprintf("%s", commentInfo.GameId),
		//	ExtInfo:                fmt.Sprintf("{\"del_type\": %d, \"del_reason\": %d}", delType, delReason),
		//}, item.ReportedIntlOpenid, constants.SiteMessageCount)

		writemessage.SetUserMessage(&model.Message{
			Type:                   constants.MsgTypeReportMessage,
			PostUUID:               commentInfo.PostUUID,
			CommentUUID:            commentInfo.CommentUUID,
			ReplyUUID:              commentInfo.ReplyUUID,
			Reply2ReplyUUID:        commentInfo.Reply2ReplyUUID,
			GameID:                 commentInfo.GameId,
			AreaID:                 commentInfo.AreaId,
			Content:                commentContentInfo.Content, // 因为这条评论被删除了，用户也看不到了，更不可能再去编辑了，直接写死当前评论的内容
			ReceiverUserIntlOpenid: item.ReportIntlOpenid,
			SenderUserIntlOpenid:   fmt.Sprintf("%s", commentInfo.GameId),
		}, item.ReportIntlOpenid, constants.SiteMessageCount)
	}
}

func DeleteCommentListCache(ctx context.Context, postUUID, myIntlOpenID, nextPageCursor string, limit int64) {
	defer recovery.CatchGoroutinePanic(ctx)

	if limit <= 0 || limit > 100 {
		limit = 10
	}
	commentsBaseRedisKey := cache.GetCommentBaseListGuestKey(postUUID, nextPageCursor, limit, 1)
	redis.GetClient().Del(context.Background(), commentsBaseRedisKey)
	commentsBaseRedisKey = cache.GetCommentBaseListGuestKey(postUUID, nextPageCursor, limit, 2)
	redis.GetClient().Del(context.Background(), commentsBaseRedisKey)
	if myIntlOpenID != "" {
		commentsBaseRedisKey = cache.GetCommentBaseListHostKey(postUUID, myIntlOpenID, nextPageCursor, limit, 1)
		redis.GetClient().Del(context.Background(), commentsBaseRedisKey)
		commentsBaseRedisKey = cache.GetCommentBaseListHostKey(postUUID, myIntlOpenID, nextPageCursor, limit, 2)
		redis.GetClient().Del(context.Background(), commentsBaseRedisKey)
		commentsBaseRedisKey = cache.GetUserCommentBaseListGuestKey(myIntlOpenID, "", limit)
		redis.GetClient().Del(context.Background(), commentsBaseRedisKey)
		commentsBaseRedisKey = cache.GetUserCommentBaseListHostKey(myIntlOpenID, "", limit)
		redis.GetClient().Del(context.Background(), commentsBaseRedisKey)
		cache.RemoveUserCommentCacheKeys(myIntlOpenID)
	}
}

func TrigerUpdateCommentHotNum(c context.Context, commentUuid string, isAddReplyCount bool) {
	if commentUuid == "" {
		return
	}
	// 获取评论统计数据内容
	commentStateInfo, err := dao.GetCommentStateByCommentUuid(commentUuid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("TrigerUpdateCommentHotNum GetCommentStateByCommentUuid err: %v", err)
		// return errs.NewCustomError(c, code.GetCommentStateByCommentUuidFailed, "Failed to get comment info")
	} else {
		if isAddReplyCount {
			commentStateInfo.ReplyCount++
		} else {
			commentStateInfo.ReplyCount--
		}
		if commentStateInfo.ReplyCount < 0 {
			commentStateInfo.ReplyCount = 0
		}
		UpdateCommentReplyCount(c, commentStateInfo)
		// 记录被点赞的评论commentuuid，用于计算热度的定时任务
		commentHotCalculationUUIDsKey := cache.GetCommentHotCalculationUUIDKey()
		redis.GetClient().SAdd(c, commentHotCalculationUUIDsKey, commentUuid)
	}
}
