package dao

import (
	"errors"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

type PostCollectionConditions struct {
	IntlOpenid string
	Order      []*OrderConditions
	LtId       int64
	Language   string
	PostUuids  []string
}

func PostCollectionGet(postUUID, intlOpenID string, needDelRecord bool) (*model.PostCollection, error) {
	var postCollection model.PostCollection

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostCollection{}).TableName())
	if postUUID != "" {
		db = db.Where("post_uuid = ?", postUUID)
	}
	if intlOpenID != "" {
		db = db.Where("intl_openid = ?", intlOpenID)
	}
	var err error
	if needDelRecord {
		err = db.Unscoped().First(&postCollection).Error
	} else {
		err = db.Where("is_del = 0").First(&postCollection).Error
	}
	if err != nil {
		return nil, err
	}
	post, err := GetPost(postCollection.PostUUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &postCollection, nil
		}
		return nil, err
	}

	postCollection.Post = post
	return &postCollection, nil
}

func PostCollectionCreate(collection *model.PostCollection) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostCollection{}).TableName()).Omit("Post").Create(&collection).Error
}

func PostCollectionDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostCollection{}).TableName()).Omit("Post").Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func PostCollectionDeleteByPostUuid(postUuid string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostCollection{}).TableName()).Omit("Post").Where("post_uuid = ?", postUuid).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func PostReCollection(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostCollection{}).TableName()).Omit("Post").Where("id = ?", id).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"is_del":      0,
	}).Error
}

func PostCollectionListNotPost(conditions *PostCollectionConditions, limit int) ([]*model.PostCollection, error) {
	var collections []*model.PostCollection
	var err error

	db := DB.SelectConnect("db_standalonesite").Table((&model.PostCollection{}).TableName())
	if limit > 0 {
		db.Limit(limit)
	}

	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if conditions.Language != "" {
		db = db.Where("language = ?", conditions.Language)
	}
	if len(conditions.PostUuids) > 0 {
		db = db.Where("post_uuid in ?", conditions.PostUuids)
	}

	db = db.Order("id DESC")
	if err = db.Find(&collections).Error; err != nil {
		return nil, err
	}
	return collections, nil
}
