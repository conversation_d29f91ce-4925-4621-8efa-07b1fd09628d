// Package share TODO
package share

import (
	"context"
	"errors"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/logic/common"
	"trpc.act.logicial/app/model/invitation"
	"trpc.act.logicial/app/util"
)

const (
	logStatusSuccess = 1
)

// GetUserTeamSizeUntilYesterday TODO
func GetUserTeamSizeUntilYesterday(ctx context.Context, timeZone int32, fSourceId string) (int64, error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return 0, err
	}

	// 获取昨天时间戳
	yesterdayEndTimestamp, err := common.GetYesterdayEndTimestamp(timeZone)
	if err != nil {
		return 0, errs.NewCustomError(ctx, code.CommonDataError,
			"invalid timezone: %d. Timezone must be between -11 and 12", timeZone)
	}
	// 获取当前用户分享短码
	shortTableName, err := util.GetShortShareTableName(ctx, fSourceId)
	if err != nil {
		return 0, err
	}
	var shortCode invitation.ShortCode
	err = DB.DefaultConnect().WithContext(ctx).Table(shortTableName).Where(
		"uid = ? and account_type = ? and Fsource_id = ?",
		userAccount.Uid, int32(userAccount.AccountType), fSourceId).First(&shortCode).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"sel shortCode db error, \t [Error]:{%v} ", err)
	}

	tableName, err := util.GetShareTableName(ctx, fSourceId)
	if err != nil {
		return 0, err
	}
	var squadSize int64
	// 用户短码不为空,获取小队人数
	if shortCode.ShareCode != "" {
		squadSize, err = GetSquadSize(ctx, GetSquadSizeParam{
			UID:                   userAccount.Uid,
			AccountType:           int32(userAccount.AccountType),
			FSourceId:             fSourceId,
			YesterdayEndTimestamp: yesterdayEndTimestamp,
			TableName:             tableName,
		})
		if err != nil {
			return 0, err
		}
		if squadSize > 0 {
			return squadSize, nil
		}
		// 短码不为空, 已创建小队, 返回自己
		return 1, nil
	}
	// 小队人数为0或当前用户短码不存在,获取当前用户加入的小队
	var invitationItem invitation.Invitation
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where("Fsource_id = ? and invitee_uid = ? and invitee_account_type = ? and status = 1 and is_delete = 0",
			fSourceId, userAccount.Uid, int32(userAccount.AccountType)).
		// Where("created_at <= ?", yesterdayEndTimestamp).
		Limit(1).
		Find(&invitationItem)
	if err = db.Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"GetUserTeamSizeUntilYesterday db error, \t [Error]:{%v} ", err)
	}
	if invitationItem.UID == "" {
		return 0, nil
	}
	// 获取小队人数
	squadSize, err = GetSquadSize(ctx, GetSquadSizeParam{
		UID:                   invitationItem.UID,
		AccountType:           invitationItem.AccountType,
		FSourceId:             fSourceId,
		YesterdayEndTimestamp: yesterdayEndTimestamp,
		TableName:             tableName,
	})
	if err != nil {
		return 0, err
	}
	return squadSize, nil
}

// GetUserTeamSize TODO
func GetUserTeamSize(ctx context.Context, fSourceId string) (int64, error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return 0, err
	}
	// 获取当前用户分享短码
	shortTableName, err := util.GetShortShareTableName(ctx, fSourceId)
	if err != nil {
		return 0, err
	}
	var shortCode invitation.ShortCode
	err = DB.DefaultConnect().WithContext(ctx).Table(shortTableName).Where(
		"uid = ? and account_type = ? and Fsource_id = ?",
		userAccount.Uid, int32(userAccount.AccountType), fSourceId).First(&shortCode).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"sel shortCode db error, \t [Error]:{%v} ", err)
	}

	tableName, err := util.GetShareTableName(ctx, fSourceId)
	if err != nil {
		return 0, err
	}
	var squadSize int64
	now := time.Now()
	nowEnd := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location()).Unix()
	// 用户短码不为空,获取小队人数
	if shortCode.ShareCode != "" {
		squadSize, err = GetSquadSize(ctx, GetSquadSizeParam{
			UID:                   userAccount.Uid,
			AccountType:           int32(userAccount.AccountType),
			FSourceId:             fSourceId,
			YesterdayEndTimestamp: nowEnd,
			TableName:             tableName,
		})
		if err != nil {
			return 0, err
		}
		if squadSize > 0 {
			return squadSize, nil
		}
		// 短码不为空, 已创建小队, 返回自己
		return 1, nil
	}
	// 小队人数为0或当前用户短码不存在,获取当前用户加入的小队
	var invitationItem invitation.Invitation
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where("Fsource_id = ? and invitee_uid = ? and invitee_account_type = ? and is_delete = 0",
			fSourceId, userAccount.Uid, int32(userAccount.AccountType)).
		Where("created_at <= ?", nowEnd).Limit(1).
		Find(&invitationItem)
	if err = db.Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"GetUserTeamSizeUntilYesterday db error, \t [Error]:{%v} ", err)
	}
	if invitationItem.UID == "" {
		return 0, nil
	}
	// 获取小队人数
	squadSize, err = GetSquadSize(ctx, GetSquadSizeParam{
		UID:                   invitationItem.UID,
		AccountType:           invitationItem.AccountType,
		FSourceId:             fSourceId,
		YesterdayEndTimestamp: nowEnd,
		TableName:             tableName,
	})
	if err != nil {
		return 0, err
	}
	return squadSize, nil
}

// GetSquadSizeParam TODO
type GetSquadSizeParam struct {
	UID                   string
	AccountType           int32
	FSourceId             string
	YesterdayEndTimestamp int64
	TableName             string
}

// GetSquadSize TODO
func GetSquadSize(ctx context.Context, param GetSquadSizeParam) (int64, error) {

	var hasShareNum int64
	query := invitation.Invitation{
		UID:         param.UID,
		AccountType: param.AccountType,
		FsourceId:   param.FSourceId,
		Status:      logStatusSuccess,
		IsDelete:    0,
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(param.TableName).
		Where(&query).Where("created_at <= ?", param.YesterdayEndTimestamp).
		Count(&hasShareNum)
	if err := db.Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"GetSquadSize db error, \t [Error]:{%v} ", err)
	}
	if hasShareNum > 0 {
		// 返回小队成员数 + 自己
		return hasShareNum + 1, nil
	}
	return 0, nil
}

// GetTeamShareCode 获取自己队伍的shareCode
func GetTeamShareCode(ctx context.Context, uid string, accountType int32, FsourceId string) (shareCode string,
	err error) {
	// 先查表有没有shareCode 没有就是被邀请的
	shareCode = ""
	var shareCodeData *invitation.ShortCode
	shareTableName, err := util.GetShortShareTableName(ctx, FsourceId)
	if err != nil {
		return
	}
	findDb := DB.DefaultConnect().Debug().WithContext(ctx).Table(shareTableName).Where("uid = ? and Fsource_id = ?", uid,
		FsourceId).
		First(&shareCodeData)

	if findDb.Error != nil && findDb.Error != gorm.ErrRecordNotFound {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}

	if findDb.Error != gorm.ErrRecordNotFound {
		shareCode = shareCodeData.ShareCode
	} else {
		// 被邀请的
		where := invitation.Invitation{
			InviteeUid:         uid,
			InviteeAccountType: accountType,
			FsourceId:          FsourceId,
			Status:             1,
			IsDelete:           0,
		}
		tableName, err := util.GetShareTableName(ctx, FsourceId)
		if err != nil {
			return "", err
		}
		var invitedData *invitation.Invitation
		findDb := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).Where(&where).First(&invitedData)

		if findDb.Error != nil {
			if findDb.Error == gorm.ErrRecordNotFound {
				return "", nil
			}
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql db error, \t [Error]:{%v} ", findDb.Error)
			return "", err
		}
		var shareCodeData *invitation.ShortCode
		shareTableName, err := util.GetShortShareTableName(ctx, FsourceId)
		if err != nil {
			return "", err
		}
		findDb = DB.DefaultConnect().Debug().WithContext(ctx).Table(shareTableName).Where("uid = ? and Fsource_id = ?",
			invitedData.UID, FsourceId).
			First(&shareCodeData)

		if findDb.Error != nil {
			if findDb.Error == gorm.ErrRecordNotFound {
				return "", nil
			}
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql db error, \t [Error]:{%v} ", findDb.Error)
			return "", err
		}
		shareCode = shareCodeData.ShareCode

	}
	return
}

// DelShortUserShare 删除用户短码分享记录
func DelShortUserShare(ctx context.Context, uid string, accountType int32, shareCode string,
	FsourceId string) (err error) {
	where := invitation.Invitation{
		InviteeUid:         uid,
		InviteeAccountType: accountType,
		FsourceId:          FsourceId,
		Status:             1,
		IsDelete:           0,
	}
	tableName, err := util.GetShareTableName(ctx, FsourceId)
	if err != nil {
		return
	}
	var invitedData *invitation.Invitation
	findDb := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).Where(&where).First(&invitedData)

	if findDb.Error != nil {
		if findDb.Error == gorm.ErrRecordNotFound {
			return nil
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}

	invitedData.IsDelete = 1
	findDb = DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).Save(&invitedData)
	if findDb.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}

	return
}
