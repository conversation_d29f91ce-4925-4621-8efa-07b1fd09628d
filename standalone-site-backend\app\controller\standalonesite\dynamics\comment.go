package dynamics

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	redis2 "github.com/go-redis/redis/v8"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/logic/tweet"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/comment"
	userService "trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/pkg/metadatadecode"
)

func (s *DynamicsImpl) GetPostComment(c context.Context, req *pb.GetPostCommentReq) (*pb.GetPostCommentRsp, error) {

	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	openid := userAccount.Uid
	// openid := userAccount.IntlAccount.OpenId
	// openid := "2670566212109452541"

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	if req.CommentUuid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "comment id is required")
	}

	postCommentRsp, err := comment.GetPostCommentInfo(c, openid, req.CommentUuid, language)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetPostComments err: %v\n", err)
		return nil, err
	}
	return postCommentRsp, nil

}

func (s *DynamicsImpl) GetPostComments(c context.Context, req *pb.GetPostCommentsReq) (*pb.GetPostCommentsRsp, error) {
	// 默认写死分页类型和分页数
	req.PageType = 0
	if req.Limit < 0 || req.Limit > 100 {
		req.Limit = 10
	}
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	openid := userAccount.Uid
	// openid := userAccount.IntlAccount.OpenId
	// openid := "2670566212109452541"

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	comment_reply_limit := req.CommentReplyLimit
	// comment_reply_limit 代表每条评论的第一页回复的条数
	if comment_reply_limit == 0 || comment_reply_limit > 100 {
		comment_reply_limit = 20
	}

	if req.OrderBy != constants.OrderByTypeHot {
		req.OrderBy = 1
	}

	postCommentRsp, err := comment.GetPostComments(c, openid, language, req)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetPostComments err: %v\n", err)
		return nil, err
	}
	return postCommentRsp, nil

}

func (s *DynamicsImpl) GetPostCommentReplies(c context.Context, req *pb.GetPostCommentRepliesReq) (*pb.GetPostCommentRepliesRsp, error) {

	// 默认写死分页类型和分页数
	req.PageType = 0
	if req.Limit < 0 || req.Limit > 100 {
		req.Limit = 10
	}
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	openid := userAccount.Uid
	// openid := userAccount.IntlAccount.OpenId

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	postCommentReplyRsp, err := comment.GetPostCommentReplys(c, openid, language, req)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetPostCommentReplies err: %v\n", err)
		return nil, err
	}
	return postCommentReplyRsp, nil

}

func (s *DynamicsImpl) GetUserCommentList(c context.Context, req *pb.GetUserCommentListReq) (rsp *pb.GetUserCommentListRsp, err error) {
	// 默认写死分页类型和分页数
	req.PageType = 0
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	var myOpenid string
	if userAccount.Uid != "" {
		myOpenid = userAccount.Uid
	}
	//openid := userAccount.IntlAccount.OpenId
	// openid := "2670566212109452541"
	// myOpenid := "29080-12945745392039390084"
	// gameId := "16"
	// areaId := "global"

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	gameId, areaId := "", ""

	//gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	//if gameId == "" || areaId == "" {
	//	return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "GetUserComment x-common-param gameid or areaid Parameter error")
	//}

	queryOpenid := req.IntlOpenid
	if queryOpenid == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.NoExistUsername, "User does not exist")
	}

	// user, err := userService.GetUserInfoByOpenid(c, myOpenid, false)
	// if err != nil || user == nil || user.Model == nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserInfoByOpenid err: %v\n", err)
	// 	return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.NoExistUsername, "User does not exist")
	// }
	userCommentRsp, err := comment.GetUserComments(c, req, myOpenid, gameId, areaId, language)

	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserComments err: %v\n", err)
		return
	}
	return userCommentRsp, nil
}

func (s *DynamicsImpl) CommentStar(c context.Context, req *pb.CommentStarReq) (rsp *pb.CommentStarRsp, err error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	// openid := "2670566212109452541"
	// 获取metadata中的header

	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}
	if req.CommentUuid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "comment id is required")
	}
	starComment, err := comment.UserStarComment(c, openid, req.CommentUuid, gameId, areaId)
	if err != nil {
		return nil, err
	}
	rsp = &pb.CommentStarRsp{
		Status: pb.Status_FAILURE,
	}
	if starComment {
		rsp.Status = pb.Status_SUCCESS
	}
	return
}

func (s *DynamicsImpl) PostComment(c context.Context, req *pb.PostCommentReq) (rsp *pb.PostCommentRsp, err error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	// 验证是否被禁言了
	mute := formatted.GetUserMute(openid)
	if mute {
		return nil, errs.NewCustomError(c, code.UserHasBeenBanned, "You have been banned, please try again later!")
	}
	// // 验证用户是否成年
	// err = user.CheckUserAdultStatus(c, openid)
	// if err != nil {
	// 	return nil, err
	// }
	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	// openid := "29080-12945745392039390084"
	// gameId := "16"
	// areaId := "global"

	if req.PostUuid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "post uuid is required")
	}

	param := comment.CommentCreationReq{
		PostUUID:        req.PostUuid,
		CommentUUID:     req.CommentUuid,
		Type:            req.Type,
		Content:         req.Content,
		PicUrls:         req.PicUrls,
		AtIntlOpenid:    req.AtIntlOpenid,
		CommentBubbleId: req.CommentBubbleId,
		IsAudit:         0,
		GameId:          gameId,
		AreaId:          areaId,
	}
	if param.Type <= 0 && param.Type > 2 {
		return nil, errs.NewCustomError(c, code.InvalidParamsErr, "PostComment | Parameter error ")
	}
	param.IsAudit = 2

	exists, errCode := userService.IsInUserWhiteList(c, gameId, areaId, openid)
	if errCode != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePostComment err: %v\n", errCode)
		return
	}
	if exists {
		param.IsAudit = 1
	}
	// 用于压测创建数据
	if trpc.GlobalConfig().Global.EnvName == "test" && strings.Contains(openid, "-100000") {
		param.IsAudit = 1
	}
	var commentInfo *pb.CommentItem
	if param.Type == 1 {
		commentInfo, err = comment.CreatePostComment(c, openid, param, language)
	} else {
		commentInfo, err = comment.CreatePostCommentReply(c, openid, param, language)
	}
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreatePostComment err: %v\n", err)
		return
	}
	// 创建一个评论点赞数据
	go func(commentUuid string) {
		comment.CreateEmptyCommentState(context.Background(), commentUuid)
	}(commentInfo.CommentUuid)
	// 完成任务进度
	tweet.CompletePostRelatedTask(c, 3, language, req.PostUuid)
	// 写入redis，推送到kafka
	go func(commentUuid string) {
		redis.GetClient().ZAdd(context.Background(), cache.WaitingForReviewCommentToKafkaKeys(), &redis2.Z{
			Score:  float64(time.Now().Unix()),
			Member: commentUuid,
		})
	}(commentInfo.CommentUuid)

	return &pb.PostCommentRsp{
		Comment: commentInfo,
	}, nil
}

func (s *DynamicsImpl) DeletePostComment(c context.Context, req *pb.DeletePostCommentReq) (rsp *pb.DeletePostCommentRsp, err error) {
	rsp = &pb.DeletePostCommentRsp{}
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return
	}
	openid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	// openid := "2670566212109452541"
	// 获取metadata中的header
	header := metadata.GetHTTPHeader(c)
	// 注入到当前的context中
	c = metadatadecode.ParseHeaderCommonParam(header, c)

	if req.CommentUuid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "comment id is required")
	}

	commentInfo, err := comment.GetPostComment(req.CommentUuid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetPostComment err: %v\n", err)
		return nil, errs.NewCustomError(c, code.DeletePostCommentFailed, "DeletePostComment | failed to delete comment info")
	}

	isAdmin := formatted.GetUserAdmin(openid)

	// 如果不是自己发布的评论和非管理员就报错
	if openid != commentInfo.IntlOpenid && !isAdmin {
		err = errs.NewCustomError(c, code.NoPermission, "DeletePostComment | Not permission")
		return
	}
	delType := constants.POST_COMMENT_CONTENT_DELETE_TYPE_SELF
	if isAdmin {
		delType = constants.POST_COMMENT_CONTENT_DELETE_TYPE_CADMIN
	}

	// 执行删除
	err = comment.DeletePostComment(c, commentInfo, "user delete comment", openid, int(delType), req.DelReason)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.DeletePostComment err: %v\n", err)
		return nil, errs.NewCustomError(c, code.DeletePostCommentFailed, "DeletePostComment | failed to delete comment info")
	}
	return
}

// 获取所有评论气泡
func (s *DynamicsImpl) GetAllCommentBubbleList(c context.Context, req *pb.GetAllCommentBubbleListReq) (rsp *pb.GetAllCommentBubbleListRsp, err error) {
	rsp = &pb.GetAllCommentBubbleListRsp{}
	commentBubbleWithoutLanguage, err := userService.GetAllCommentBubbleList(c, false, true)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetAllCommentBubbleList err: %v\n", err)
		return
	}
	rsp.CommentBubbles = formatted.ReturnCommentBubbleItems(commentBubbleWithoutLanguage)
	return rsp, err
}

// 设置置顶置底
func (s *DynamicsImpl) SetCommentTopOrBottom(c context.Context, req *pb.SetCommentTopOrBottomReq) (rsp *pb.SetCommentTopOrBottomRsp, err error) {
	rsp = &pb.SetCommentTopOrBottomRsp{}
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	var myOpenid string
	if userAccount.Uid != "" {
		myOpenid = userAccount.Uid
	}
	err = comment.SetCommentTopOrBottomV2(c, req.CommentUuid, constants.ECommentPosStatusString(req.TopBottomStatus), myOpenid, false)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}
