package storage

import (
	"context"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/tencentyun/cos-go-sdk-v5"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/util"
)

var (
	_ ObjectStorageService = (*cosServant)(nil)
	_ OssCreateService     = (*cosCreateServant)(nil)
	_ OssCreateService     = (*cosCreateTempDirServant)(nil)
)

type cosCreateServant struct {
	client *cos.Client
	domain string
}

type cosCreateTempDirServant struct {
	client    *cos.Client
	domain    string
	bucketUrl string
	tempDir   string
}

type cosServant struct {
	OssCreateService

	client *cos.Client
	domain string
}

func (s *cosCreateServant) PutObject(objectKey string, reader io.Reader, objectSize int64, contentType string, _persistance bool) (string, error) {
	_, err := s.client.Object.Put(context.Background(), objectKey, reader, &cos.ObjectPutOptions{
		ObjectPutHeaderOptions: &cos.ObjectPutHeaderOptions{
			ContentType:   contentType,
			ContentLength: objectSize,
		},
	})
	if err != nil {
		return "", err
	}
	return s.domain + objectKey, nil
}

func (s *cosCreateServant) PersistObject(_objectKey string) error {
	// empty
	return nil
}

func (s *cosCreateTempDirServant) PutObject(objectKey string, reader io.Reader, objectSize int64, contentType string, persistance bool) (string, error) {
	objectName := objectKey
	if !persistance {
		objectName = s.tempDir + objectKey
	}
	_, err := s.client.Object.Put(context.Background(), objectName, reader, &cos.ObjectPutOptions{
		ObjectPutHeaderOptions: &cos.ObjectPutHeaderOptions{
			ContentType:   contentType,
			ContentLength: objectSize,
		},
	})
	if err != nil {
		return "", err
	}
	return s.domain + objectKey, nil
}

func (s *cosCreateTempDirServant) PersistObject(objectKey string) error {
	exsit, err := s.client.Object.IsExist(context.Background(), objectKey)
	if err != nil {
		return err
	}
	if exsit {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Debugf("object exist so do nothing objectKey: %s", objectKey)
		return nil
	}
	_, _, err = s.client.Object.Copy(context.Background(), objectKey, s.bucketUrl+s.tempDir+objectKey, nil)
	if err != nil {
		return err
	}
	_, err = s.client.Object.Delete(context.Background(), s.tempDir+objectKey, nil)
	return err
}

func (s *cosServant) DeleteObject(objectKey string) error {
	_, err := s.client.Object.Delete(context.Background(), objectKey)
	return err
}

func (s *cosServant) DeleteObjects(objectKeys []string) error {
	defer recovery.CatchGoroutinePanic(context.Background())
	obs := []cos.Object{}
	for _, v := range objectKeys {
		obs = append(obs, cos.Object{Key: v})
	}
	_, _, err := s.client.Object.DeleteMulti(context.Background(), &cos.ObjectDeleteMultiOptions{
		Objects: obs,
	})
	return err
}

func (s *cosServant) IsObjectExist(objectKey string) (bool, error) {
	return s.client.Object.IsExist(context.Background(), objectKey)
}

func (s *cosServant) SignURL(objectKey string, expiredInSec int64) (string, error) {
	//TODO 临时逻辑，先写死是COS桶，这个逻辑暂时用不上
	cosConf := ((config.GetConfig()).COS)[0]
	signedURL, err := s.client.Object.GetPresignedURL(context.Background(),
		http.MethodGet, objectKey, cosConf.SecretID, cosConf.SecretKey, time.Second*time.Duration(expiredInSec), nil)

	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("client.SignURL err: %v", err)
		return "", err
	}

	epath, err := url.PathUnescape(signedURL.Path)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("url.PathUnescape err: %v", err)
		return "", err
	}

	signedURL.Path, signedURL.RawPath = epath, epath
	return signedURL.String(), nil
}

func (s *cosServant) ObjectURL(objetKey string) string {
	return s.domain + objetKey
}

func (s *cosServant) ObjectKey(objectUrl string) string {
	return strings.Replace(objectUrl, s.domain, "", -1)
}

func (s *cosServant) Name() string {
	return "COS"
}

func (s *cosServant) DownloadFile(objectKey string) (error, *os.File) {
	resp, err := s.client.Object.Get(context.Background(), objectKey, nil)
	if err != nil {
		return err, nil
	}
	defer resp.Body.Close()
	return util.WriteFile(resp.Body, "siteMessagePackage")
}
