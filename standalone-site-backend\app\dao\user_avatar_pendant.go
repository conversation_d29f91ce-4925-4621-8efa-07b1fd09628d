package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"trpc.publishing_application.standalonesite/app/model"
)

func BatchAddUserAvatarPendant(userAvatarPendant []*model.UserAvatarPendant) error {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserAvatarPendant{}).TableName())
	err := tx.CreateInBatches(userAvatarPendant, len(userAvatarPendant)).Error
	return err
}

// 获取用户拥有的挂件
func GetUserOwnedAvatarPendantsByUserId(intlOpenids []string, avatarPendantId int64) ([]*model.UserAvatarPendant, error) {
	var userAvatarPendants []*model.UserAvatarPendant
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserAvatarPendant{}).TableName())
	err := tx.Where("intl_openid in ? and avatar_pendant_id = ? and is_del = 0 and valid_end_at > ?", intlOpenids, avatarPendantId, time.Now().Unix()).Find(&userAvatarPendants).Error
	return userAvatarPendants, err
}

func BatchUpdateOrCreateUserAvatarPendants(userAvatarPendants []*model.UserAvatarPendant) error {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserAvatarPendant{}).TableName())
	err := tx.Clauses((clause.OnConflict{
		Columns:   []clause.Column{{Name: "game_id"}, {Name: "area_id"}, {Name: "area_id"}, {Name: "avatar_pendant_id"}, {Name: "intl_openid"}},
		DoUpdates: clause.AssignmentColumns([]string{"valid_begin_at", "valid_end_at", "creator", "updater", "modified_on", "deleted_on", "is_del", "deleted_on", "is_weared"}),
	})).Create(&userAvatarPendants).Error
	return err
}

// // 获取有效的挂件
// func GetValidItemsByIntlOpenidAndAvatarPendentId(intlOpenids []string, avatarPendantId int64, timestamp int64) ([]*model.UserAvatarPendant, error) {
// 	// 获取用户未过期挂件
// 	userAvatarPendants := make([]*model.UserAvatarPendant, 0)
// 	if len(intlOpenids) == 0 || avatarPendantId == 0 {
// 		return userAvatarPendants, nil
// 	}
// 	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserAvatarPendant{}).TableName())
// 	err := tx.Where("intl_openid in ? and avatar_pendant_id = ? and is_del = 0 and valid_end_at > ?", intlOpenids, avatarPendantId, timestamp).Find(&userAvatarPendants).Error
// 	return userAvatarPendants, err
// }

// 获取某个用户所有有效的挂件
func GetValidUserAvatarPendants(intlOpenid string, timestamp int64) ([]*model.UserAvatarPendant, error) {
	// 获取用户未过期挂件
	userAvatarPendants := make([]*model.UserAvatarPendant, 0)
	if intlOpenid == "" {
		return userAvatarPendants, nil
	}
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserAvatarPendant{}).TableName())
	err := tx.Where("intl_openid = ? and is_del = 0 and valid_end_at > ? ", intlOpenid, timestamp).Find(&userAvatarPendants).Error
	return userAvatarPendants, err
}

// 获取当前穿戴的挂件
func GetWearedUserAvatarPendant(intlOpenid string) (isWearedAvatarPendant *model.UserAvatarPendant, err error) {
	// 获取用户未过期挂件
	// isWearedAvatarPendant = &model.UserAvatarPendant{}
	if intlOpenid == "" {
		return isWearedAvatarPendant, nil
	}
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserAvatarPendant{}).TableName())
	err = tx.Where("intl_openid = ? and is_weared = 1 and valid_end_at > ?", intlOpenid, time.Now().Unix()).First(&isWearedAvatarPendant).Error
	return isWearedAvatarPendant, err
}

// 设置穿戴挂件
func SetWearUserAvatarPendant(intlOpenid string, avatarPendantId int64) (effectRow int64, err error) {
	curTimestamp := time.Now().Unix()
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserAvatarPendant{}).TableName())
	// 获取当前已经穿戴的挂件
	isWearedAvatarPendant, err := GetWearedUserAvatarPendant(intlOpenid)
	// 如果当前穿戴的挂件和传入的挂件一致，则取消穿戴
	if avatarPendantId == isWearedAvatarPendant.ID {
		return 1, nil
	}
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return 0, err
		}
	} else {
		// 取消穿戴
		tx.Where("id = ? ", isWearedAvatarPendant.ID).Update("is_weared", 0)
	}
	// 穿戴
	tx = DB.SelectConnect("db_standalonesite").Table((&model.UserAvatarPendant{}).TableName())
	updateRes := tx.Where("intl_openid = ? and avatar_pendant_id = ? and is_del = 0 and valid_end_at > ?", intlOpenid, avatarPendantId, curTimestamp).Update("is_weared", 1)
	return updateRes.RowsAffected, updateRes.Error
}

func GetUserAvatarPendant(intlOpenid string, avatarPendantId int64) (userAvatarPendant *model.UserAvatarPendant, err error) {
	// 获取用户未过期挂件
	if intlOpenid == "" {
		return userAvatarPendant, nil
	}
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserAvatarPendant{}).TableName())
	err = tx.Where("intl_openid = ? and avatar_pendant_id = ? and is_del = 0", intlOpenid, avatarPendantId).First(&userAvatarPendant).Error
	return userAvatarPendant, err
}

func CancleWearUserAvatarPendant(intlOpenid string, avatarPendantId int64) (err error) {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.UserAvatarPendant{}).TableName())
	err = tx.Where("intl_openid = ? and avatar_pendant_id = ? and is_del = 0", intlOpenid, avatarPendantId).Update("is_weared", 0).Error
	return err
}
