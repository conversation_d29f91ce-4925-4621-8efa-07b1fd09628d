package creatorhub

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/common"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/model"
)

// 绑定CreatorHub账号
func BindCreatorHubAccount(c context.Context, intlOpenid string, creatorHubAccessToken string, uid string, language string, gameId string, areaId string) (err error) {
	defer func(ctx context.Context) {
		go func() {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportBindCreatorhubLog(newC, intlOpenid, uid, err)
			log.WithFieldsContext(newC, "log_type", constants.LogType_Standalonesite).Infof("ReportBindCreatorhubLog intlOpenid:%s,uid: %s, err: %v", intlOpenid, uid, err)
		}()
	}(c)
	if intlOpenid == "" {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for empty intlOpenid")
		err = errs.NewCustomError(c, code.ErrCodeCreatorHubBindError, "intl openid is empty")
		return
	}
	if creatorHubAccessToken == "" {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for empty creatorHubAccessToken")
		err = errs.NewCustomError(c, code.ErrCodeCreatorHubBindError, "creator hub access token is empty")
		return
	}
	if uid == "" {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for empty uid")
		err = errs.NewCustomError(c, code.ErrCodeCreatorHubBindError, "uid is empty")
		return
	}
	// TODO 调用接口获取CH用户信息
	// uid := ""
	// 判断该CreatorHub账号是否已经绑定
	chUserHasBound, err := isChUserHasBound(c, uid, intlOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for isChUserHasBound err: %v\n", err)
		return err
	}
	if chUserHasBound {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for intl openid has bound")
		err = errs.NewCustomError(c, code.ErrCodeCreatorHubAccountHasBound, "uid has bound")
		return
	}
	// // 独立站站账号已绑定
	// standloneSiteHasBound, err := isStandaloneSiteUserHasBound(c, uid, intlOpenid)
	// if err != nil {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for isStandaloneSiteUserHasBound err: %v\n", err)
	// 	return errs.NewCustomError(c, code.ErrCodeCreatorHubBindError, "check bound failed")
	// }
	// if standloneSiteHasBound {
	// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for intl openid has bound")
	// 	return errs.NewCustomError(c, code.ErrCodeStandloneSiteHasBound, "intl openid has bound")
	// }
	// 查看用户状态
	_, status, err := IsCreatorHubUserNormal(c, uid, "16", "asia", false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for get creator hub user info err: %v\n", err)
		err = errs.NewCustomError(c, code.ErrCodeCreatorHubBindError, "get creator hub user info err")
		return
	}
	// 独立站账号异常
	err = IsBoundAccountNormal(c, status)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for creator hub user is not normal %d", status)
		errStr := err.Error()
		if strings.Contains(errStr, fmt.Sprintf("%d", code.ErrCodeCreatorHubAccountHasFreezed)) {
			// 绑定是冻结错误码，不一致
			err = errs.NewCustomError(c, code.ErrCodeCreatorHubAccountHasFreezedWhenBind, "creator hub user is frozen")
		}
		return err
	}
	// 校验token
	isValid, _ := IsUserTokenValid(c, uid, creatorHubAccessToken, "16", "asia")
	if !isValid {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for creator hub user token is invalid")
		err = errs.NewCustomError(c, code.ErrCodeInvalidToken, "creator hub user token is invalid")
		return
	}
	// 获取country
	chUser, err := GetCreatorHubUserInfo(c, uid, "16", "asia", true)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for get creator hub user info err: %v\n", err)
		err = errs.NewCustomError(c, code.ErrCodeCreatorHubBindError, "get creator hub user info err")
		return
	}
	// 开始绑定
	err = dao.CreateOrUpdateAccountBindInfo(intlOpenid, uid, language, gameId, areaId, chUser.Country)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for create account bind info err: %v\n", err)
		err = errs.NewCustomError(c, code.ErrCodeCreatorHubBindError, "create account bind info err")
		return
	}
	// 清除绑定缓存
	redisKey := cache.GetCreatorHubUserBoundInfoKey(intlOpenid)
	redis.GetClient().Del(c, redisKey)
	// 添加认证
	err = addCreatorHubCertification(c, intlOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for add creator hub certification err: %v\n", err)
	}
	return nil
}

// 获取绑定的用户信息
func GetBoundCreatorHubUserInfoByOpenID(c context.Context, curUserIntlOpenid string) (rsp *pbUser.GetCreatorHubUserInfoRsp, err error) {
	boundInfo, err := GetBoundInfoOfCreatorHub(c, curUserIntlOpenid)
	if err != nil {
		return nil, err
	}
	if boundInfo == nil {
		return nil, errs.NewCustomError(c, code.ErrCodeNotBindCreatorHub, "intl openid is not bind creator hub")
	}
	creatorHubUserDetail, err := GetCreatorHubUserInfo(c, boundInfo.ChUid, "16", "asia", true)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetBoundCreatorHubUserInfoByOpenID failed for get creator hub user info err: %v\n", err)
		return nil, errs.NewCustomError(c, code.ErrCodeCreatorHubBindError, "get creator hub user info err")
	}
	thirdChannels := []*pbUser.ThirdChannel{}
	for _, channel := range creatorHubUserDetail.ThirdChannels {
		thirdChannels = append(thirdChannels, &pbUser.ThirdChannel{
			ChannelName: channel.ChannelName,
			ChannelType: int32(channel.ChannelType),
		})
	}
	rsp = &pbUser.GetCreatorHubUserInfoRsp{
		Email:          creatorHubUserDetail.Email,
		Status:         int32(creatorHubUserDetail.Status),
		ThirdChannels:  thirdChannels,
		UserId:         boundInfo.ChUid,
		UserName:       creatorHubUserDetail.UserName,
		IsAutoSync:     int32(boundInfo.IsAutoSync),
		AbnormalStatus: int32(boundInfo.AbnormalStatus),
	}
	// 判断账户是否已冻结，如果已冻结，需要更新表字段的冻结状态，这里的冻结状态不以CH返回状态为准
	if creatorHubUserDetail.Status != constants.CH_USER_STATUS_NORMAL {
		FreezedAccount(c, curUserIntlOpenid, boundInfo.ChUid, int(creatorHubUserDetail.Status))
		// 关闭自动同步
		ChangeSyncStatus(c, curUserIntlOpenid, 0, boundInfo.SyncLanguage)
		// dao.ChangeSyncStatus(curUserIntlOpenid, 0, boundInfo.SyncLanguage)
	}
	return rsp, nil
}

// 获取网红绑定信息
func GetBoundInfoOfCreatorHub(c context.Context, curUserIntlOpenid string) (*model.ChUserBind, error) {
	rsp := &model.ChUserBind{}
	if curUserIntlOpenid == "" {
		return rsp, errs.NewCustomError(c, code.ErrCodeNotBindCreatorHub, "intl openid is empty")
	}
	// var rsp *model.ChUserBind
	//
	redisKey := cache.GetCreatorHubUserBoundInfoKey(curUserIntlOpenid)
	// 从Redis获取失败，从DB中获取
	getBoundCreatorHubUserInfoByOpenIdFromDb := func(c context.Context) (interface{}, error) {
		boundChUserInfo, err := dao.GetBoundChUserInfo(curUserIntlOpenid)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetBoundCreatorHubUserInfoByOpenID failed for get bound ch user info err: %v\n", err)
			return nil, err
		} else {
			// 写入redis
			cache.SetCacheWithMarshal(c, redisKey, boundChUserInfo, 2*time.Minute)
		}
		return boundChUserInfo, err
	}
	// 从Redis获取成功，直接返回
	getBoundCreatorHubUserInfoByOpenIdFromRedisKey := func(c context.Context, res interface{}) (interface{}, error) {
		return res, nil
	}
	boundChUserInfoIf, err := cache.GetCacheWithUnmarshal(c, redisKey, rsp, &getBoundCreatorHubUserInfoByOpenIdFromRedisKey, &getBoundCreatorHubUserInfoByOpenIdFromDb, true)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetBoundCreatorHubUserInfoByOpenID err: %v\n", err)
			return rsp, errs.NewCustomError(c, code.ErrCodeNotBindCreatorHub, "intl openid is not bind creator hub")
		}
	}
	if boundChUserInfoIf == nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Warnf("GetBoundCreatorHubUserInfoByOpenID err: %v\n", err)
		return rsp, errs.NewCustomError(c, code.ErrCodeNotBindCreatorHub, "intl openid is not bind creator hub")
	}
	// 类型转换
	boundChUserInfo := boundChUserInfoIf.(*model.ChUserBind)
	if boundChUserInfo == nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetBoundCreatorHubUserInfoByOpenID type change failed for err: %v\n", err)
		return rsp, errs.NewCustomError(c, code.ErrCodeNotBindCreatorHub, "intl openid is not bind creator hub")
	}
	// 返回
	return boundChUserInfo, nil
}

// func GetCreatorHubUserDetail(c context.Context, creatorHubUid string) (*pbUser.GetCreatorHubUserInfoReq, error) {
// 	// TODO 调用接口获取绑定CreatorHub用户信息
// 	// 接口缓存
// 	redisKey := cache.GetCreatorHubUserInfoKey(creatorHubUid)
// 	getCretorHubUserDetailByApi := func(c context.Context) (interface{}, error) {
// 		// TODO 调用接口获取绑定CreatorHub用户信息
// 		// 获取账户冻结状态
// 		freezeStatus := 0
// 		// 已冻结移除认证称号
// 		if freezeStatus == 1 {
// 			removeCreatorHubCertification(c, creatorHubUid)
// 		}
// 		return nil, nil
// 	}
// 	rsp, err := cache.GetCacheWithUnmarshal(c, redisKey, nil, &getCretorHubUserDetailByApi)
// 	if err != nil {
// 		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubUserDetail err: %v\n", err)
// 		return nil, errs.NewCustomError(c, code.ErrCodeCreatorHubApiError, "get creator hub user detail failed")
// 	}
// 	if rsp == nil {
// 		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubUserDetail rsp is nil")
// 		return nil, errs.NewCustomError(c, code.ErrCodeCreatorHubApiError, "get creator hub user detail failed")
// 	}
// 	return rsp, err
// }

// 修改同步状态
func ChangeSyncStatus(c context.Context, intlOpenid string, syncStatus int, language string) (err error) {
	var uid string
	defer func(ctx context.Context) {
		go func() {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportAutoSyncLog(newC, intlOpenid, uid, syncStatus, err)
			log.WithFieldsContext(newC, "log_type", constants.LogType_Standalonesite).Infof("ReportAutoSyncLog end, intlOpenid: %s, uid: %s, syncStatus: %d, err: %v\n", intlOpenid, uid, syncStatus, err)
		}()
	}(c)
	if intlOpenid == "" {
		err = errs.NewCustomError(c, code.ErrCodeChangeCreatorHubSubmissionError, "intl openid is empty")
		return
	}
	// 查看是否绑定
	boundChUserInfo, err := dao.GetBoundChUserInfo(intlOpenid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errs.NewCustomError(c, code.ErrCodeNotBindCreatorHub, "intl openid is not bind creator hub")
			return
		}
	}
	if boundChUserInfo == nil {
		err = errs.NewCustomError(c, code.ErrCodeNotBindCreatorHub, "intl openid is not bind creator hub")
		return
	}
	uid = boundChUserInfo.ChUid
	// 账号异常，无法开启
	if syncStatus == 1 {
		err = IsBoundAccountNormal(c, boundChUserInfo.AbnormalStatus)
		if err != nil {
			return err
		}
		// 禁言无法开启
		isMute := formatted.GetUserMute(intlOpenid)
		if isMute {
			err = errs.NewCustomError(c, code.UserHasBeenBanned, "You have been banned, please try again later!")
			return
		}
	}
	if language == "" {
		language = boundChUserInfo.SyncLanguage
	}
	// // 判断账号是否已冻结
	// if boundChUserInfo.AbnormalStatus == constants.CH_USER_STATUS_UNREGISTERED {
	// 	return errs.NewCustomError(c, code.ErrCodeCreatorHubAccountNotRegistered, "creator hub account has freezed")
	// }
	err = dao.ChangeSyncStatus(intlOpenid, syncStatus, language)
	if err != nil {
		err = errs.NewCustomError(c, code.ErrCodeChangeCreatorHubSubmissionError, "change sync status failed")
		return
	}
	// 清缓存
	redisKey := cache.GetCreatorHubUserBoundInfoKey(intlOpenid)
	redis.GetClient().Del(c, redisKey)
	return nil
}

// CreatorHub用户是否已绑定, 已绑定报错
func isChUserHasBound(c context.Context, uid string, intlOpenid string) (bool, error) {
	boundInfos, err := dao.GetAllBoundInfoByChUid(uid)
	// 非未绑定报错
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for get bound info err: %v\n", err)
		return false, errs.NewCustomError(c, code.ErrCodeCreatorHubBindError, "get bound info err")
	}
	// 校验是否已绑定
	for _, boundInfo := range boundInfos {
		if boundInfo.IntlOpenid != intlOpenid && boundInfo.ChUid != "" {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for current creator Account has bound %s", boundInfo.IntlOpenid)
			return true, errs.NewCustomError(c, code.ErrCodeCreatorHubAccountHasBound, "creator hub account has bound")
		}
	}
	return false, nil
}

// 独立站用户是否已绑定
func isStandaloneSiteUserHasBound(c context.Context, uid string, intlOpenid string) (bool, error) {
	boundInfos, err := dao.GetAllBoundInfoByIntlOpenid(intlOpenid)
	// 非未绑定报错
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for get bound info err: %v\n", err)
		return false, errs.NewCustomError(c, code.ErrCodeStandloneSiteHasBound, "get bound info err")
	}
	// 校验是否已绑定
	for _, boundInfo := range boundInfos {
		if boundInfo.IntlOpenid != intlOpenid && boundInfo.ChUid != "" {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BindCreatorHubAccount failed for current creator Account has bound %s", boundInfo.IntlOpenid)
			return true, errs.NewCustomError(c, code.ErrCodeStandloneSiteHasBound, "creator hub account has bound")
		}
	}
	return false, nil
}

// 添加网红认证
func addCreatorHubCertification(c context.Context, intlOpenid string) error {
	// 判断是否已经存在认证，若已存在，忽略
	authType := formatted.GetUserAuth(intlOpenid)
	if authType > 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("addCreatorHubCertification skip for user has authType %d", authType)
		return nil
	}
	err := user.CMSSetAuthUserInfoV2(c, &pbUser.CMSSetAuthUserReq{
		IntlOpenids: []string{intlOpenid},
		Type:        int32(constants.SetWriteAuth),
		Languages: []*pbUser.LanguageItem{
			{
				Language: "zh",
				Desc:     "CreatorHub创作者",
			},
			{
				Language: "en",
				Desc:     "CreatorHub Creator",
			},
			{
				Language: "ja",
				Desc:     "CreatorHub クリエイター",
			},
			{
				Language: "ko",
				Desc:     "CreatorHub 크리에이터",
			},
			{
				Language: "zh-TW",
				Desc:     "CreatorHub 創作者",
			},
		},
	})
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("addCreatorHubCertification failed, err: %v", err)
		return err
	}
	// cache.RemoveUserHostInfoKey(c, intlOpenid)
	return nil
}

func removeCreatorHubCertification(c context.Context, intlOpenid string) error {
	authType := formatted.GetUserAuth(intlOpenid)
	if authType != 2 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("addCreatorHubCertification skip for user has no write authtype %d", authType)
		return nil
	}
	err := user.CMSSetAuthUserInfoV2(c, &pbUser.CMSSetAuthUserReq{
		IntlOpenids: []string{intlOpenid},
		Type:        0,
		Languages: []*pbUser.LanguageItem{
			{
				Language: "zh",
				Desc:     "创作者",
			},
			{
				Language: "en",
				Desc:     "Creator",
			},
			{
				Language: "ja",
				Desc:     "クリエイター",
			},
			{
				Language: "ko",
				Desc:     "크리에이터",
			},
			{
				Language: "zh-TW",
				Desc:     "創作者",
			},
		},
	})
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("removeCreatorHubCertification failed, err: %v", err)
		return err
	}
	// user.DeleteUserInfoCache(c, intlOpenid)
	return nil
}

// 拉取的用户信息被冻结后，需要将绑定关系中的用户信息置为冻结
func FreezedAccount(c context.Context, intlOpenid string, uid string, abnormalStatus int) error {
	err := dao.FreezedChUser(intlOpenid, uid, abnormalStatus)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("FreezedAccount failed, err: %v", err)
		return err
	}
	removeCreatorHubCertification(c, intlOpenid)
	return nil
}

// 绑定账户是否正常？
func IsBoundAccountNormal(c context.Context, status constants.ECreatorHubUserStatus) error {
	if status == constants.CH_USER_STATUS_UNREGISTERED {
		return errs.NewCustomError(c, code.ErrCodeCreatorHubAccountNotRegistered, "creator hub account not registered")
	}
	if status == constants.CH_USER_STATUS_AUDITING {
		return errs.NewCustomError(c, code.ErrCodeCreatorHubAccountAuditing, "creator hub account auditing")
	}
	if status == constants.CH_USER_STATUS_AUDIT_REJECT {
		return errs.NewCustomError(c, code.ErrCodeCreatorHubAccountAuthenticationRejected, "creator hub account authentication rejected")
	}
	if status == constants.CH_USER_STATUS_FROZEN || status == constants.CH_USER_STATUS_PERMANENTLY_FROZEN {
		return errs.NewCustomError(c, code.ErrCodeCreatorHubAccountHasFreezed, "creator hub account has frozen")
	}
	if status > constants.CH_USER_STATUS_NORMAL {
		return errs.NewCustomError(c, code.ErrCodeCreatorHubAccountAbnormal, "creator hub account abnormal")
	}
	return nil
}

func SyncCreatorUserCountry(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncCreatorUserCountry start")
	chBoundUsers, err := dao.GetAllChBoundInfos()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncCreatorUserCountry GetAllChBoundInfos err: %v", err)
		return
	}
	for _, chBoundUser := range chBoundUsers {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncCreatorUserCountry chBoundUser: %v", chBoundUser)
		// 拉取网红作者信息
		chUser, err := GetCreatorHubUserInfo(c, chBoundUser.ChUid, "16", "asia", false)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncCreatorUserCountry GetCreatorHubUserInfo err: %v", err)
			continue
		}
		err = dao.UpdateCountry(chBoundUser.ID, chUser.Country)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncCreatorUserCountry UpdateCountry err: %v", err)
			continue
		}
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncCreatorUserCountry end")
}
