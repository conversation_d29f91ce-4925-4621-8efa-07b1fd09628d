package creatorhub

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/http"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	dynamic_pd "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"github.com/spf13/cast"
	"gorm.io/plugin/soft_delete"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/attachment"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	hotService "trpc.publishing_application.standalonesite/app/logic/hot"
	"trpc.publishing_application.standalonesite/app/logic/plate"
	tweet "trpc.publishing_application.standalonesite/app/logic/tweet"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

type creatorHubWork struct {
	AdminTaskId         int64              `json:"admin_task_id"`
	AverageViewerCount  int64              `json:"average_viewer_count"`   // 平均观看人数
	ChannelType         int32              `json:"channel_type"`           // 渠道类型
	CommentNum          int32              `json:"comment_num"`            // 评论数量
	CreateTime          int32              `json:"create_time"`            // 视频提交时间
	UpdateTime          int32              `json:"update_time"`            // 更新时间
	IncrCommentNum      int32              `json:"incr_comment_num"`       //评论数-增量
	IncrLikeNum         int32              `json:"incr_like_num"`          //点赞数-增量
	IncrShareNum        int32              `json:"incr_share_num"`         //分享转发数-增量
	IncrWatchNum        int32              `json:"incr_watch_num"`         //观看数-增量
	LikeNum             int32              `json:"like_num"`               //点赞数-全量
	PeakViewerCount     int32              `json:"peak_viewer_count"`      //观看人数峰值
	ShareNum            int32              `json:"share_num"`              //分享转发数-全量
	Status              int32              `json:"status"`                 //审批状态,0=未审核,1=审核通过,2=审核不通过
	TaskLangPackageList []*TaskLangPackage `json:"task_lang_package_list"` //多语言列表
	RankRewardRuleList  *RankRewardRule    `json:"rank_reward_info"`       // 赛道列表
	Uid                 string             `json:"uid"`
	WatchNum            int32              `json:"watch_num"` //观看数-全量
	WorkId              int32              `json:"work_id"`
	WorkName            string             `json:"work_name"`
	WorkUrl             string             `json:"work_url"`
	WorkMediaInfo       []*MediaInfo       `json:"work_media_infos,omitempty"` // 预览的信息，包含图片、视频等
	WorkChannel         int32              `json:"work_channel"`
	UserLanguage        string             `json:"user_language"`
	IsValid             bool               `json:"is_valid"` //是否是无效
}
type WorkList struct {
	WorkRows []creatorHubWork `json:"work_list"`
}

type MediaInfo struct {
	MediaType       string `json:"media_type"`        // 资源类型，photo video
	OriginalUrl     string `json:"original_url"`      // 资源原始url
	PreviewImageUrl string `json:"preview_image_url"` // 预览图url
	MediaID         string `json:"media_id"`          // 从作品url中提取的资源id
}

type WorkRes struct {
	Ret  int32    `json:"ret"`
	Msg  string   `json:"msg"`
	Data WorkList `json:"data"`
}

type PostData struct {
	Post       *model.PostExternalFormat
	RankId     int64
	ActivityId int64
}

type WorkItemRes struct {
	Ret  int32                 `json:"ret"`
	Msg  string                `json:"msg"`
	Data model.AuthorWorksInfo `json:"data"`
}

type AuthorWorksListRes struct {
	Ret  int32                         `json:"ret"`
	Msg  string                        `json:"msg"`
	Data model.CreatorHubWorksListData `json:"data"`
}

func SyncCreatorHubWork(gameId, areaId string) {
	c := context.Background()
	// 防止panic
	defer recovery.CatchGoroutinePanic(c)
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncCreatorHubWork ticker at %v", time.Now())
	syncCreatorHubWorkCacheKey := cache.GetSyncCreatorHubWorkTaskKey()
	if ok, _ := redis.GetClient().SetNX(c, syncCreatorHubWorkCacheKey, "1", config.GetConfig().CreatorHubSetting.SyncCreatorHubActivityTimeoutDuration*time.Second).Result(); ok {
		// 完成之后删除锁占用
		defer redis.GetClient().Del(context.Background(), syncCreatorHubWorkCacheKey)
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncCreatorHubWork start at %v", time.Now())
		var lastTime int64
		// 获取上一次同步的时间
		lastTimeKey := cache.GetSyncCreatorHubWorkKey()
		lastTimeRes, err := redis.GetClient().Get(c, lastTimeKey).Result()
		if err != nil {
			// 获取上一次同步时间错误，需要全量获取数据同步
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubWork | get last time failed, cache key: %s,err: %v", lastTimeKey, err)
			lastTime = 0
		}
		lastTime = cast.ToInt64(lastTimeRes)
		workUrl := config.GetConfig().CreatorHubSetting.GetTaskWorkPath
		limit := 10
		offset := 0

		// 获取所有的活动, 没有活动的等待活动同步完成，或者先等活动同步完成，手动触发这个同步
		// 获取非取消状态的活动数据
		condition := &model.CreatorHubActivityWhereByStatus{
			Status: []int{constants.CreatorhubActivityStatus_Published, constants.CreatorhubActivityStatus_TimeRelease, constants.CreatorhubActivityStatus_Over, constants.CreatorhubActivityStatus_ReviewPending, constants.CreatorhubActivityStatus_WaitingSendReward, constants.CreatorhubActivityStatus_SentReward},
			Order: map[string]string{
				"publish_time": "desc",
			},
			Limit: 0,
		}
		activities, err := dao.GetAllActivityListByStatus(condition)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SyncCreatorHubWork | failed to get activity list,err: %v", err)
			return
		}

		var normalActivityMap = make(map[int64]int)
		for _, activity := range activities {
			normalActivityMap[int64(activity.TaskID)] = 1
		}

		// 获取板块信息
		plateInfo, err := dao.PlateGet(&dao.PlateConditions{
			UniqueIdentifier: constants.PLATE_CREATORHUB,
		})
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncCreatorHubWork GetPlateList failed, err: %v", err)
			return
		}

		for {
			postData := map[string]interface{}{
				"gameid":          gameId,
				"offset":          offset * limit,
				"limit":           limit,
				"min_update_time": lastTime,
			}
			postJson, _ := json.Marshal(postData)

			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("GetCreatorHubWork | request creator hub params: [%s]", string(postJson))

			resultOption, err := RequestCreateHub(c, gameId, areaId, workUrl, postJson)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubWork | request failed, request data: %s, err: %v", string(postJson), err)
				return
			}

			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("GetCreatorHubWork | request creator hub result data: [%s]", resultOption)

			var res WorkRes
			err = json.Unmarshal([]byte(resultOption), &res)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubWork | json decode failed, result data: %s,err: %v", resultOption, err)
				return
			}
			if res.Ret != 0 {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubWork | api return failed, result data: %s,err: %v", resultOption, err)
				return
			}

			if len(res.Data.WorkRows) == 0 {
				break
			}

			postList, postContentList, postStatsList, socialmediaPostIds, delWorkIds, err := WorkDataToPost(res.Data, gameId, plateInfo.ID, normalActivityMap)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubWork | WorkDataToPost hanlder failed, result data: %s,err: %v", resultOption, err)
				offset++
				continue
			}
			err = handlerWorkData(postList, postContentList, postStatsList, socialmediaPostIds, delWorkIds, plateInfo.ID)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubWork | handlerWorkData failed, result data: %s,err: %v", resultOption, err)
				offset++
				continue
			}

			if len(res.Data.WorkRows) < limit {
				// 已经拉取完毕了
				break
			}
			offset++
		}
		// 设置当前时间为上次更新时间
		err = redis.GetClient().Set(c, lastTimeKey, fmt.Sprintf("%d", time.Now().Unix()), 0).Err()
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubWork | set last time cache failed, last time: %d ,err: %v", time.Now().Unix(), err)
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncCreatorHubWork end at %v", time.Now())

		// 删除列表的缓存
		cacheKey := cache.GetCreatorHubActivityListKey()
		redis.GetClient().Del(c, cacheKey)
	}
}

// 自动同步内容到动态站点
// 1. youtube、tiktok 视频，将内容发布至OutPut
// 2. 非识别内容，将内容发送至NikkeArt(带图)， OutPost(无兜底图)
func PublishContent2DynamicSitePost(c context.Context, workInfo *model.AuthorWorksInfo, intlOpenid string, language string, gameId string, areaId string) (postUuid string, err error) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("PublishContent2DynamicSitePost workInfo: %+v, intlOpenid:%s, language:%s, gameId: %s, areaId:%s", workInfo, intlOpenid, language, gameId, areaId)
	if workInfo == nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishContent2DynamicSitePost workInfo is nil")
		return "", errors.New("workInfo is nil")
	}
	// 只支持 Youtbu YoutubeShorts Twitter TikTok渠道
	if workInfo.WorkChannel != int64(constants.YOUTUBE) && workInfo.WorkChannel != int64(constants.TIKTOK) && workInfo.WorkChannel != int64(constants.YOUTUBESHORT) && workInfo.WorkChannel != int64(constants.TWITTER) {
		// 非视频内容，直接返回
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishContent2DynamicSitePost workInfo.WorkChannel is not support, channel: %d", workInfo.WorkChannel)
		return "", errs.NewCustomError(c, code.ErrCodeChannelNotSupport, "channel not support")
	}
	// plates, err := dao.GetPlateList(&dao.PlateConditions{
	// 	UniqueIdentifierList: []string{constants.PLATE_OUTPOST, constants.PLATE_NIKKEART},
	// }, 0)
	// 获取plat列表
	plagesRsp, err := plate.GetPlateList(c, &dynamic_pd.GetPlateListReq{Limit: 10, PageType: 0}, language)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishContent2DynamicSitePost GetPlateList failed, err: %v", err)
		return "", err
	}
	var outpostPlateId int32
	var nikkeartPlateId int32
	for _, plate := range plagesRsp.List {
		if plate.UniqueIdentifier == constants.PLATE_OUTPOST {
			outpostPlateId = plate.Id
		}
		if plate.UniqueIdentifier == constants.PLATE_NIKKEART {
			nikkeartPlateId = plate.Id
		}
	}
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishContent2DynamicSitePost GetPlateList failed, err: %v", err)
		return "", err
	}
	// TODO 获取createhub的tag
	tagId := GetCreatorHubTag()
	// 创建Post
	createPostContent, contentType, err := turnCreatorHubWork2Post(c, workInfo, true, gameId, "asia", language)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishContent2DynamicSitePost getContentAndTypeFromWork failed, err: %v", err)
		return "", errors.New("getContentAndTypeFromWork failed")
	}
	if createPostContent == nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishContent2DynamicSitePost createPostContent is nil")
		return "", errors.New("createPostContent is nil")
	}
	if contentType == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishContent2DynamicSitePost contentType is 0")
		return "", errors.New("contentType is 0")
	}
	var curPlateId int32 = 0
	// 发送到OutPut
	if workInfo.WorkChannel == int64(constants.YOUTUBE) || workInfo.WorkChannel == int64(constants.TIKTOK) || workInfo.WorkChannel == int64(constants.YOUTUBESHORT) || len(createPostContent.PicUrls) == 0 {
		curPlateId = outpostPlateId
	} else {
		curPlateId = nikkeartPlateId
	}
	if curPlateId == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishContent2DynamicSitePost curPlate is nil")
		return "", errors.New("curPlate is nil")
	}
	// 自动同步，过滤掉无图、无视频、无正文内容
	if contentType == 1 && len(createPostContent.Content) == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("PublishContent2DynamicSitePost skip for none content, none pic, none video: workId :%s", workInfo.WorkId)
		return "", nil
	}
	if len(createPostContent.Title) > 197 {
		createPostContent.Title = createPostContent.Title[:197] + "..."
	}
	postCreateReq := &dynamic_pd.CreatePostNewReq{
		Contents: []*dynamic_pd.CreatePostContent{
			createPostContent,
		},
		PlateId:              curPlateId,
		Type:                 contentType,
		Tags:                 []int64{tagId},
		ChWorkId:             workInfo.WorkId,
		CreatorStatementType: int32(constants.ECreatorStatementType_FromSelf_Repostable),
	}
	// 调用内部接口，创建Post
	createRsp, err := tweet.CreatePostInternal(c, postCreateReq, intlOpenid, gameId, areaId, language, false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PublishContent2DynamicSitePost CreatePostInternal failed intlOpenid: %s, ch_uid: %s workId %d, err: %v", intlOpenid, workInfo.Uid, workInfo.WorkId, err)
		return "", err
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("PublishContent2DynamicSitePost CreatePostInternal success intlOpenid: %s, ch_uid: %s workId %d, post_uuid: %s", intlOpenid, workInfo.Uid, workInfo.WorkId, createRsp.PostData.PostUuid)
	}
	return createRsp.PostData.PostUuid, err
}

// 把接口数据转换成post动态数据，方便存储
func WorkDataToPost(workList WorkList, gameId string, plateId int64, normalActivityMap map[int64]int) ([]*PostData, []*model.PostContent, []*model.PostStats, []string, []string, error) {
	var postContentList []*model.PostContent
	var postStatsList []*model.PostStats
	var postList []*PostData
	var socialmediaPostIds []string
	var postDelIds []string
	var socialMediaIntIds []int64

	for _, row := range workList.WorkRows {
		if row.AdminTaskId > 0 {
			socialMediaIntIds = append(socialMediaIntIds, row.AdminTaskId)
		}
	}

	// 获取当前所有的活动关联的赛道
	rankList, err := dao.GetActivityWithRankList(socialMediaIntIds)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | get rank with social media ids, social media ids: %v,err: %v", socialMediaIntIds, err)
	}

	var rankIds []int64
	for _, ranks := range rankList {
		for _, rank := range ranks {
			rankIds = append(rankIds, rank.RankID)
		}
	}
	var activityRankInfos []*model.CreatorHubRankLanguageList
	if len(rankIds) > 0 {
		activityRankInfos, err = dao.GetRankListById(rankIds)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | get rank data failed, rank ids: %v,err: %v", rankIds, err)
		}
	}

	// 组装活动和赛道之间的关系
	var rankInfos = make(map[int64][]*model.CreatorHubRankLanguageList)
	for taskId, ranks := range rankList {
		for _, info := range activityRankInfos {
			for _, rank := range ranks {
				if info.ID == rank.RankID {
					rankInfos[taskId] = append(rankInfos[taskId], info)
				}
			}
		}
	}

	for _, workItem := range workList.WorkRows {
		if !workItem.IsValid {
			postDelIds = append(postDelIds, cast.ToString(workItem.WorkId))
			continue
		}

		postUuid := util.CreateSnowflakeID()
		var auditStatus int
		if workItem.Status == 1 {
			// 审核通过
			auditStatus = 1
		} else {
			// 待审核
			// 审核不通过
			auditStatus = 2
			//log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("GetCreatorHubWork | unreview id： %d", workItem.WorkId)
		}

		workIdStr := cast.ToString(workItem.WorkId)

		// 标记是否删除，1-删除0-不删除
		var isDel int

		// 获取平台类型
		platformStr := "unknown"
		if workItem.ChannelType == 1 {
			platformStr = "youtube"
		} else if workItem.ChannelType == 2 {
			platformStr = "facebook"
		} else if workItem.ChannelType == 3 {
			platformStr = "twitter"
		} else if workItem.ChannelType == 4 {
			platformStr = "twitch" // 屏蔽, 删除不展示
			isDel = 1
		} else if workItem.ChannelType == 5 {
			platformStr = "youtubeshort"
		} else if workItem.ChannelType == 6 {
			platformStr = "tiktok"
		} else if workItem.ChannelType == 7 {
			platformStr = "facebooklive"
		} else if workItem.ChannelType == 8 {
			platformStr = "youtubelive"
		} else if workItem.ChannelType == 9 {
			platformStr = "tiktoklive"
		} else if workItem.ChannelType == 10 {
			platformStr = "trovo"
		} else if workItem.ChannelType == 11 {
			platformStr = "pixiv" // 屏蔽 删除不展示
			isDel = 1
		} else if workItem.ChannelType == 12 {
			platformStr = "instagram" // 屏蔽 删除不展示
			isDel = 1
		}

		// 判断当前活动是否是下线的
		if _, ok := normalActivityMap[workItem.AdminTaskId]; !ok {
			isDel = 1
		}

		var postType int32
		postType = 2
		var videoContentStr string
		var photos []string
		// 处理媒体信息
		for _, mediaItem := range workItem.WorkMediaInfo {
			if mediaItem.MediaType == "video" {
				postType = 3
				newImageUrl := mediaItem.PreviewImageUrl
				// 兜底，如果获取不到视频的预览图则过滤掉这条动态
				res1, err := http.Get(mediaItem.PreviewImageUrl)
				if err != nil || res1.StatusCode != 200 {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | createPostByCreatorHubData get video preview image url failed, image url: %s, workid: %d, error: %v", mediaItem.PreviewImageUrl, workItem.WorkId, err)
				} else {
					// 转存图片
					uploadImageUrl := uploadCreatorHubImage("lip/ugc/public/image/", mediaItem.PreviewImageUrl, gameId, "global", "")
					if uploadImageUrl == "" {
						log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | createPostByCreatorHubData uploadCreatorHubImage failed: %s", mediaItem.PreviewImageUrl)
					} else {
						newImageUrl = uploadImageUrl
					}
				}
				// 把视频信息写入扩展字段中
				videoContent := map[string]string{
					"video_url":         mediaItem.OriginalUrl,
					"video_id":          mediaItem.MediaID,
					"platform":          platformStr,
					"video_preview_url": newImageUrl,
				}
				videoContentByte, err := json.Marshal(videoContent)
				if err != nil {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost |createPostByCreatorHubData marshal video content failed: %v", err)
					continue
				}
				videoContentStr = string(videoContentByte)
			} else if mediaItem.MediaType == "photo" {
				newImageUrl := uploadCreatorHubImage("lip/ugc/public/image/", mediaItem.OriginalUrl, gameId, "global", "")
				if newImageUrl == "" {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | createPostByCreatorHubData uploadCreatorHubImage failed: %s", mediaItem.OriginalUrl)
					newImageUrl = mediaItem.OriginalUrl
				}
				photos = append(photos, newImageUrl)
			}
		}
		// 获取作品和赛道的关系
		var rankId int64
		if workItem.RankRewardRuleList != nil {
			// 判断作品中的赛道是否存在
			if activityRankList, ok := rankInfos[workItem.AdminTaskId]; ok {
				for _, row := range activityRankList {
					if workItem.RankRewardRuleList.RankKey == row.RankKey {
						for _, language := range row.Languages {
							if workItem.RankRewardRuleList.RankName == language.Name {
								rankId = language.RankID
								break
							}
						}

					}
				}
			}
		}
		// 多语言数据
		var titleLanguage = make(map[string]string)
		if workItem.TaskLangPackageList != nil && len(workItem.TaskLangPackageList) > 0 {
			for _, langPackage := range workItem.TaskLangPackageList {
				for _, val := range langPackage.KeyValList {
					if val != nil && val.Key == "work_name" {
						language := langPackage.Lang
						//转换多语言的枚举
						if changeLang, ok := CreatorhubLanguageMap[language]; ok {
							language = changeLang
						}
						titleLanguage[language] = val.Val
						break
					}
				}
			}
		}
		if len(titleLanguage) == 0 {
			language := workItem.UserLanguage
			//转换多语言的枚举
			if changeLang, ok := CreatorhubLanguageMap[language]; ok {
				language = changeLang
			}
			titleLanguage[language] = workItem.WorkName
		}
		for language, title := range titleLanguage {
			// 没有多语言数据直接写入en兜底
			var createdOn int64
			var createdOnMs int64
			createdOnT := time.Unix(int64(workItem.CreateTime), 0)
			// 转换为时间戳（秒）
			createdOn = createdOnT.Unix()
			randomThreeDigits := rand.Intn(10000)
			createdOnMs = createdOnT.UnixMicro() + int64(randomThreeDigits)

			postList = append(postList, &PostData{
				Post: &model.PostExternalFormat{
					IsDel:             soft_delete.DeletedAt(isDel),
					PostUUID:          postUuid,
					PlateID:           int32(plateId),
					Language:          language,
					Type:              postType,
					IsAudit:           int32(auditStatus),
					SocialmediaPostId: workIdStr,
					GameId:            "16",
					AreaId:            "global",
					CreatedOn:         createdOn,
					ModifiedOn:        int64(workItem.UpdateTime),
					CreatedOnMs:       createdOnMs,
					TaskId:            cast.ToString(workItem.AdminTaskId),
					RankId:            int32(rankId),
					Platform:          platformStr,
				},
				RankId:     rankId,
				ActivityId: workItem.AdminTaskId,
			})
			picUrlsStr := strings.Join(photos, ",")
			postContentList = append(postContentList, &model.PostContent{
				Model: &model.Model{
					CreatedOn: createdOn,
				},
				PostUUID:        postUuid,
				IsOriginal:      2,
				OriginalURL:     workItem.WorkUrl,
				Platform:        platformStr,
				OriginalReprint: 1,
				Title:           title,
				PicUrls:         picUrlsStr,
				ExtInfo:         videoContentStr,
				Language:        language,
			})
			postStatsList = append(postStatsList, &model.PostStats{
				Model: &model.Model{
					CreatedOn: createdOn,
				},
				PostUUID:     postUuid,
				CommentCount: int64(workItem.CommentNum),
				UpvoteCount:  int64(workItem.LikeNum),
				UpvoteMap:    "{}",
				BrowseCount:  int64(workItem.WatchNum),
				ForwardCount: int64(workItem.ShareNum),
			})
		}
		socialmediaPostIds = append(socialmediaPostIds, workIdStr)
	}
	return postList, postContentList, postStatsList, socialmediaPostIds, postDelIds, nil
}

// func getContentAndTypeFromWork(ctx context.Context, workInfo *model.AuthorWorksInfo, gameId string, language string) (*dynamic_pd.CreatePostContent, int32, error) {
// 	if workInfo == nil {
// 		return nil, 0, errors.New("work info is nil")
// 	}
// 	var photos []string
// 	var platformStr = ""
// 	var videoContentStr = ""
// 	if workInfo.WorkChannel == int64(constants.YOUTUBE) {
// 		platformStr = "youtube"
// 	} else if workInfo.WorkChannel == int64(constants.TWITTER) {
// 		platformStr = "twitter"
// 	} else if workInfo.WorkChannel == int64(constants.YOUTUBESHORT) {
// 		platformStr = "youtubeshort"
// 	} else if workInfo.WorkChannel == int64(constants.TIKTOK) {
// 		platformStr = "tiktok"
// 	} else {
// 		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | invalid work channel: %d", workInfo.WorkChannel)
// 		return nil, 0, errors.New("invalid work channel")
// 	}
// 	// 富文本贴
// 	var postType int32 = 1
// 	for _, mediaItem := range workInfo.WorkMediaInfo {
// 		if mediaItem.MediaType == "video" {
// 			postType = 3
// 			newImageUrl := mediaItem.PreviewImageUrl
// 			// 兜底，如果获取不到视频的预览图则过滤掉这条动态
// 			res1, err := http.Get(mediaItem.PreviewImageUrl)
// 			if err != nil || res1.StatusCode != 200 {
// 				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | createPostByCreatorHubData get video preview image url failed, image url: %s, workid: %d, error: %v", mediaItem.PreviewImageUrl, workInfo.WorkId, err)
// 			} else {
// 				// 转存图片
// 				uploadImageUrl := uploadCreatorHubImage("lip/ugc/public/image/", mediaItem.PreviewImageUrl, gameId, "global", "")
// 				if uploadImageUrl == "" {
// 					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | createPostByCreatorHubData uploadCreatorHubImage failed: %s", mediaItem.PreviewImageUrl)
// 				} else {
// 					newImageUrl = uploadImageUrl
// 				}
// 			}
// 			// 把视频信息写入扩展字段中
// 			videoContent := map[string]string{
// 				"video_url":         mediaItem.OriginalUrl,
// 				"video_id":          mediaItem.MediaID,
// 				"platform":          platformStr,
// 				"video_preview_url": newImageUrl,
// 			}
// 			videoContentByte, err := json.Marshal(videoContent)
// 			if err != nil {
// 				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost |createPostByCreatorHubData marshal video content failed: %v", err)
// 				continue
// 			}
// 			videoContentStr = string(videoContentByte)
// 		} else if mediaItem.MediaType == "photo" {
// 			// 图片贴
// 			if postType == 1 {
// 				postType = 2
// 			}
// 			newImageUrl := uploadCreatorHubImage("lip/ugc/public/image/", mediaItem.OriginalUrl, gameId, "global", "")
// 			if newImageUrl == "" {
// 				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | createPostByCreatorHubData uploadCreatorHubImage failed: %s", mediaItem.OriginalUrl)
// 				newImageUrl = mediaItem.OriginalUrl
// 			}
// 			photos = append(photos, newImageUrl)
// 		}
// 	}
// 	createPostContent := &dynamic_pd.CreatePostContent{
// 		Content:        "",
// 		ContentSummary: "",
// 		ExtInfo:        videoContentStr,
// 		Platform:       platformStr,
// 		Title:          workInfo.WorkTitle,
// 		Language:       language,
// 		// IsOriginal:      2,
// 		// OriginalReprint: 1,
// 		PicUrls: photos,
// 	}
// 	return createPostContent, postType, nil
// }

// 获取网红Tag
func GetCreatorHubTag() int64 {
	creatorhubConf := (config.GetConfig()).CreatorHubSetting
	if creatorhubConf == nil {
		return 0
	}
	return creatorhubConf.CreatorHubTagId
}

// 调用CreateHub后台接口拉取作品详情
func GetWorkDetailInfoByWorkId(ctx context.Context, workId int64, gameId string, areaId string) (*model.AuthorWorksInfo, error) {
	var workRes WorkItemRes
	var workInfo model.AuthorWorksInfo
	workUrl := config.GetConfig().CreatorHubSetting.CreatorHubWorkDetailPath
	postData := map[string]interface{}{
		"gameid":  gameId,
		"areaid":  areaId,
		"work_id": workId,
	}
	postDataByte, err := json.Marshal(postData)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetWorkDetailInfoByWorkId | json marshal failed: %v", err)
		return nil, err
	}
	// println(string(postDataByte), workUrl)
	resultOption, err := RequestCreateHub(ctx, gameId, areaId, workUrl, postDataByte)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetWorkDetailInfoByWorkId | RequestCreateHub failed: %v", err)
		return nil, err
	} else {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("GetWorkDetailInfoByWorkId | workId: %d resultOption: %s", workId, resultOption)
	}
	// resultOption := "{\"ret\":0,\"msg\":\"\",\"data\":{\"areaid\":\"asia\",\"gameid\":\"16\",\"uid\":\"16367066663333277103\",\"open_id\":\"UCG7QkiPq_WXthaMRe14ZBVw\",\"author_name\":\"space@#￥12\",\"author_image\":\"https://yt3.ggpht.com/ytc/AIdro_kZLgsiw3lD2chVBHShBsZLa6TW9ajtuxoCp7EP6VfcCi6Fc2TYh4nTzTEWJ4SmYrfSOg=s88-c-k-c0x00ffffff-no-rj\",\"author_link\":\"https://www.youtube.com/channel/UCG7QkiPq_WXthaMRe14ZBVw\",\"work_id\":54531,\"work_channel\":1,\"work_publish_time\":1736157606,\"work_original_url\":\"https://www.youtube.com/watch?v=yXUxzyThUvY\",\"work_title\":\"【Multi | FULL】EP13 Liu Bai was kidnapped by Lei Changtian | Let Wind Goes By 风中的火焰 | iQIYI\",\"work_desc\":\"\",\"work_media_infos\":[{\"media_type\":\"video\",\"original_url\":\"https://www.youtube.com/watch?v=yXUxzyThUvY\",\"preview_image_url\":\"https://i.ytimg.com/vi/yXUxzyThUvY/hqdefault.jpg\",\"media_id\":\"yXUxzyThUvY\",\"sub_image_url\":null,\"channel_type\":0}]}}"
	err = json.Unmarshal([]byte(resultOption), &workRes)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetWorkDetailInfoByWorkId | json unmarshal failed: %v", err)
		return nil, err
	}
	if workRes.Ret != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetWorkDetailInfoByWorkId | get work detail failed, ret: %d, msg: %s", workRes.Ret, workRes.Msg)
		return nil, errors.New(workRes.Msg)
	}
	workInfo = workRes.Data
	return &workInfo, nil
}

func handlerWorkData(postList []*PostData, postContentList []*model.PostContent, postStats []*model.PostStats, socialMediaPostIds []string, deleteSocialMediaIds []string, plateId int64) error {
	var posts []*model.PostExternal
	var err error
	// 根据内容流id获取动态内容，对比状态
	allSocialMediaPostIds := make([]string, 0, len(socialMediaPostIds)+len(deleteSocialMediaIds))
	allSocialMediaPostIds = append(socialMediaPostIds, deleteSocialMediaIds...)
	if len(allSocialMediaPostIds) > 0 {
		posts, err = dao.GetPostExternalListBySocialMediaId(allSocialMediaPostIds, plateId)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("handlerWorkData | get post data by social media ids, social media ids: %v,err: %v", socialMediaPostIds, err)
			return err
		}
	}
	var updatePost []*model.PostExternal
	var insertPost []*PostData
	var insertPostContent []*model.PostContent
	var insertPostStats []*model.PostStats
	if len(posts) == 0 {
		//全量新增
		return createCreateHubPost(postList, postContentList, postStats)
	}
	for _, item := range postList {
		isExist := false
		for _, post := range posts {
			// 如果是相等的社媒内容流id,并且在库中的是没有删除的数据
			if post.SocialmediaPostId == item.Post.SocialmediaPostId {
				isExist = true
				// 如果之前是审核不通过，并且是删除的状态，但是现在社媒那边同步过来的数据是审核通过的，说明就是再审是通过的，所以需要把删除状态去除,假如判断是否是需要屏蔽删除的
				if post.IsAudit == 2 && post.IsDel == 1 && item.Post.IsAudit == 1 && item.Post.IsDel == 0 {
					post.IsDel = 0
					post.DeletedOn = 0
				}
				// 已经是删除的状态了，不需要二次删除了
				if post.IsDel == 1 {
					break
				}
				// 同步是否删除状态,如果同步的状态是需要删除，那么就直接删除
				if item.Post.IsDel == 1 {
					deleteSocialMediaIds = append(deleteSocialMediaIds, item.Post.SocialmediaPostId)
					break
				}
				post.IsAudit = item.Post.IsAudit // 同步审核状态
				updatePost = append(updatePost, post)

				// 更改内容中的postUUid
				for j, content := range postContentList {
					if content.PostUUID == item.Post.PostUUID {
						postContentList[j].PostUUID = post.PostUUID
						break
					}
				}
				// 更改数据统计表中的postUuid
				for k, stat := range postStats {
					if stat.PostUUID == item.Post.PostUUID {
						postStats[k].PostUUID = post.PostUUID
						break
					}
				}
				break
			}
		}
		if !isExist {
			// 如果没有找到对应的动态，那就是新增的内容
			for _, content := range postContentList {
				if item.Post.PostUUID == content.PostUUID {
					insertPostContent = append(insertPostContent, content)
					break
				}
			}
			for _, stat := range postStats {
				if item.Post.PostUUID == stat.PostUUID {
					insertPostStats = append(insertPostStats, stat)
					break
				}
			}
			insertPost = append(insertPost, item)
		}
	}

	if len(updatePost) > 0 {
		// 如果有更新的数据需要更新内容
		updateCreateHubPost(updatePost, postContentList, postStats)
	}
	if len(insertPost) > 0 {
		// 新增动态
		createCreateHubPost(insertPost, insertPostContent, insertPostStats)
	}
	if len(deleteSocialMediaIds) > 0 {
		// 先判断是否需要删除的
		var postUuids []string
		for _, deleteSocialMediaId := range deleteSocialMediaIds {
			var postUuid string
			for _, post := range posts {
				if post.SocialmediaPostId == deleteSocialMediaId && post.IsDel == 0 {
					// 是需要删除的
					postUuid = post.PostUUID
					postUuids = append(postUuids, postUuid)
					break
				}
			}
			//if postUuid != "" {
			//	// 更新es
			//	postDoc := map[string]interface{}{
			//		"is_del":     1,
			//		"deleted_on": time.Now().Unix(),
			//	}
			//	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUuid, postDoc)
			//}
		}
		if len(postUuids) > 0 {
			deletePostAndWithActivity(postUuids)
		}
	}
	return nil
}

// 新增动态
func createCreateHubPost(postData []*PostData, postContent []*model.PostContent, postStats []*model.PostStats) error {
	for _, postItem := range postData {
		var postContentItem *model.PostContent
		var postStatsItem *model.PostStats
		for _, content := range postContent {
			if content.PostUUID == postItem.Post.PostUUID {
				postContentItem = content
				break
			}
		}
		if postContentItem == nil {
			continue
		}
		for _, stat := range postStats {
			if stat.PostUUID == postItem.Post.PostUUID {
				postStatsItem = stat
				break
			}
		}
		if postStatsItem == nil {
			continue
		}
		// 初始化热度
		hotNum := hotService.CalculatingPostHotNum(postStatsItem)
		// 创建帖子
		postItem.Post.HotNum = hotNum
		err := dao.CreatePostExternal(postItem.Post)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("handlerWorkData | create post failed, post data: %+v,err: %v", postItem, err)
			continue
		}
		err = dao.CreatePostContent(postContentItem)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("handlerWorkData | create post content failed, post content data: %+v,err: %v", postContentItem, err)
			continue
		}

		//// 写入es
		//postModel := &model.Post{
		//	Model: &model.Model{
		//		ID:        postItem.Post.ID,
		//		CreatedOn: postItem.Post.CreatedOn,
		//		IsDel:     postItem.Post.IsDel,
		//		DeletedOn: postItem.Post.DeletedOn,
		//	},
		//	PostUUID:          postItem.Post.PostUUID,
		//	PlateID:           postItem.Post.PlateID,
		//	Language:          postItem.Post.Language,
		//	Type:              postItem.Post.Type,
		//	IsAudit:           postItem.Post.IsAudit,
		//	SocialmediaPostId: postItem.Post.SocialmediaPostId,
		//	GameId:            "16",
		//	AreaId:            "global",
		//}
		//tweet.PushCreatothubPostToSearch(postModel, postContentItem, hotNum, postItem.RankId, postItem.ActivityId)
	}
	return nil
}

// 更新动态
func updateCreateHubPost(postData []*model.PostExternal, postContent []*model.PostContent, postStats []*model.PostStats) error {
	for _, post := range postData {
		postContentList, err := dao.GetPostContentList(post.PostUUID, false)
		// 组装更新es的信息
		//postDoc := map[string]interface{}{
		//	"is_audit":   post.IsAudit,
		//	"is_del":     post.IsDel,
		//	"deleted_on": post.DeletedOn,
		//}
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("updateCreateHubPost | get post content data failed, post uuid: %s,err: %v", post.PostUUID, err)
			continue
		}
		for _, content := range postContent {
			for _, contentItem := range postContentList {
				if content.PostUUID == post.PostUUID && content.Language == contentItem.Language {
					contentItem.Title = content.Title
					contentItem.PicUrls = content.PicUrls
					contentItem.ExtInfo = content.ExtInfo
					contentItem.OriginalURL = content.OriginalURL
					contentItem.Platform = content.Platform
					err = dao.UpdatePostContent(contentItem)
					if err != nil {
						log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("updateCreateHubPost | update post content failed, post content data: %+v,err: %v", postContent, err)
						continue
					}
					break
				}
			}

		}

		var postStatsItem *model.PostStats
		for _, stat := range postStats {
			if stat.PostUUID == post.PostUUID {
				postStatsItem = stat
				break
			}
		}

		err = dao.UpdatePostStats(postStatsItem)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("updateCreateHubPost | update post stats failed, post stats data: %+v,err: %v", postStatsItem, err)
			continue
		}
		// 重新计算一遍热度
		hotNum := hotService.CalculatingPostHotNum(postStatsItem)
		post.HotNum = hotNum
		err = dao.UpdatePostExternal(post)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("updateCreateHubPost | update post failed, post content data: %+v,err: %v", post, err)
			continue
		}

		//postDoc["hot_num"] = hotNum
		//dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, postDoc)
	}
	return nil
}
func uploadCreatorHubImage(path, imgUrl, gameId, areaId, imgType string) (newImgUrl string) {
	return attachment.UploadImage(path, imgUrl, gameId, areaId, imgType)
}

func GetAuthorWorksInfosOfUser(ctx context.Context, uid string, gameId string, areaId string, startIdx int32, pageNum int32) (*model.CreatorHubWorksListData, error) {
	var CreatorHubAuthorWorksRes AuthorWorksListRes
	var rsp model.CreatorHubWorksListData
	workUrl := config.GetConfig().CreatorHubSetting.CreatorHubGetAuthorWorksList
	postData := map[string]interface{}{
		"gameid":                gameId,
		"areaid":                areaId,
		"page_num":              pageNum,
		"start_idx":             startIdx,
		"user_id":               uid,
		"exclude_twitter_video": true,
	}
	postDataByte, err := json.Marshal(postData)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetAuthorWorksInfosOfUser | json marshal failed: %v", err)
		return nil, err
	}

	resultOption, err := RequestCreateHub(ctx, gameId, areaId, workUrl, postDataByte)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetAuthorWorksInfosOfUser | RequestCreateHub failed: %v", err)
		return nil, err
	} else {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("GetAuthorWorksInfosOfUser | uid: %s, page_num: %s, start_idx: %d, resultOption: %s", uid, pageNum, startIdx, resultOption)
	}
	err = json.Unmarshal([]byte(resultOption), &CreatorHubAuthorWorksRes)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetAuthorWorksInfosOfUser | json unmarshal failed: %v", err)
		return nil, err
	}
	if CreatorHubAuthorWorksRes.Ret != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetAuthorWorksInfosOfUser | CreatorHubAuthorWorksRes.Ret is not 0, ret: %d, msg: %s", CreatorHubAuthorWorksRes.Ret, CreatorHubAuthorWorksRes.Msg)
		return nil, errors.New(CreatorHubAuthorWorksRes.Msg)
	}
	rsp = CreatorHubAuthorWorksRes.Data
	if rsp.NextIdx == -1 {
		rsp = model.CreatorHubWorksListData{
			NextIdx:   0,
			WorkInfos: []*model.AuthorWorksInfo{},
		}
	}
	return &rsp, nil
}

func GetSubmissionOfUser(ctx context.Context, intlOpenid string, limit int32, nextIdx int32) (*dynamic_pd.GetMySubmissionRsp, error) {
	if intlOpenid == "" {
		return nil, errs.NewCustomError(ctx, code.InvalidParamsErr, "intlOpenid is empty")
	}
	rsp := &dynamic_pd.GetMySubmissionRsp{}
	// 获取绑定用户信息
	bindUserInfo, err := GetBoundCreatorHubUserInfoByOpenID(ctx, intlOpenid)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetSubmissionOfUser | GetBoundCreatorHubUserInfoByOpenID failed, intlOpenid: %s, err: %v", intlOpenid, err)
		return nil, err
	}
	uid := bindUserInfo.UserId
	if bindUserInfo == nil || uid == "" {
		return nil, errs.NewCustomError(ctx, code.ErrCodeNotBindCreatorHub, "uid is empty")
	}
	err = IsBoundAccountNormal(ctx, constants.ECreatorHubUserStatus(bindUserInfo.AbnormalStatus))
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetSubmissionOfUser | IsBoundAccountNormal failed, intlOpenid: %s, err: %v", intlOpenid, err)
		return nil, err
	}
	// _, status, _ := IsCreatorHubUserNormal(ctx, uid, "16", "asia", true)
	// if status == constants.CH_USER_STATUS_FROZEN || status == constants.CH_USER_STATUS_PERMANENTLY_FROZEN {
	// 	return nil, errs.NewCustomError(ctx, code.ErrCodeCreatorHubAccountHasFreezed, "user is frozen")
	// }
	submissionKey := cache.GetCreatorHubSubmissionOfUserKey(uid, "16", "asia", limit, nextIdx)
	getSubmissionFunc := func(c context.Context) (interface{}, error) {
		// index := offset / limit
		worksRsp, err := GetAuthorWorksInfosOfUser(c, uid, "16", "asia", nextIdx, limit)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetSubmissionOfUser | GetAuthorWorksInfosOfUser failed, uid: %s, err: %v", uid, err)
			return nil, err
		}
		// 类型转换
		mySubmissionRsp := &dynamic_pd.GetMySubmissionRsp{}
		mySubmissionRsp.List = make([]*dynamic_pd.CreatorHubWorkItem, 0)
		workIds := make([]int64, 0)
		for _, work := range worksRsp.WorkInfos {
			workIds = append(workIds, work.WorkId)
		}
		workPosts, _ := dao.GetWorkPostsByWorkIds(workIds)
		// 获取creator tagid
		tagId := GetCreatorHubTag()
		for _, work := range worksRsp.WorkInfos {
			postContent, _, err := turnCreatorHubWork2Post(ctx, work, true, "16", "", "")
			workContent := &dynamic_pd.WorkContent{
				Uid:             work.Uid,
				WorkId:          work.WorkId,
				WorkPublishTime: work.WorkPublishTime,
			}
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetSubmissionOfUser | turnCreatorHubWork2Post failed, workId: %d, err: %v", work.WorkId, err)
				continue
			}
			// mediaInfos := make([]*dynamic_pd.CreatorHubWorkMediaInfo, 0)
			// for _, mediaInfo := range work.WorkMediaInfo {
			// 	curMediaInfo := dynamic_pd.CreatorHubWorkMediaInfo{
			// 		ChannelType:     int32(mediaInfo.ChannelType),
			// 		MediaId:         mediaInfo.MediaID,
			// 		MediaType:       mediaInfo.MediaType,
			// 		OriginalUrl:     mediaInfo.OriginalUrl,
			// 		PreviewImageUrl: mediaInfo.PreviewImageUrl,
			// 	}
			// 	mediaInfos = append(mediaInfos, &curMediaInfo)
			// }
			// 是否发布到站内
			var isPublishedPost int32
			for _, workPost := range workPosts {
				if workPost.WorkId == work.WorkId {
					isPublishedPost = 1
					break
				}
			}
			mySubmissionRsp.List = append(mySubmissionRsp.List, &dynamic_pd.CreatorHubWorkItem{
				PostContent:     postContent,
				WorkContent:     workContent,
				IsPublishedPost: isPublishedPost,
				TagId:           tagId,
			})
		}
		// 下一页索引
		mySubmissionRsp.NextIdx = worksRsp.NextIdx
		// 设置缓存
		cache.SetCacheWithMarshal(c, submissionKey, mySubmissionRsp, 2*time.Minute)
		return mySubmissionRsp, nil
	}
	r, err := cache.GetCacheWithUnmarshal(ctx, submissionKey, rsp, nil, &getSubmissionFunc, true)
	if err != nil || r == nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetSubmissionOfUser | GetCacheWithUnmarshal failed, uid: %s, err: %v", uid, err)
		return nil, err
	}
	rsp = r.(*dynamic_pd.GetMySubmissionRsp)
	return rsp, nil

}

func turnCreatorHubWork2Post(ctx context.Context, workInfo *model.AuthorWorksInfo, withRestore bool, gameId string, areaId string, language string) (*dynamic_pd.CreatePostContent, int32, error) {
	if workInfo == nil {
		return nil, 0, errors.New("work info is nil")
	}
	var photos []string
	var platformStr = ""
	var videoContentStr = ""
	if workInfo.WorkChannel == int64(constants.YOUTUBE) {
		platformStr = "youtube"
	} else if workInfo.WorkChannel == int64(constants.TWITTER) {
		platformStr = "twitter"
	} else if workInfo.WorkChannel == int64(constants.YOUTUBESHORT) {
		platformStr = "youtubeshort"
	} else if workInfo.WorkChannel == int64(constants.TIKTOK) {
		platformStr = "tiktok"
	} else {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | invalid work channel: %d", workInfo.WorkChannel)
		return nil, 0, errors.New("invalid work channel")
	}
	var postType int32 = 1
	for _, mediaItem := range workInfo.WorkMediaInfo {
		// 视频类型，不支持twitter
		if mediaItem.MediaType == "video" && workInfo.WorkChannel != int64(constants.TWITTER) {
			postType = 3
			newImageUrl := mediaItem.PreviewImageUrl
			// 兜底，如果获取不到视频的预览图则过滤掉这条动态
			res1, err := http.Get(mediaItem.PreviewImageUrl)
			if err != nil || res1.StatusCode != 200 {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | createPostByCreatorHubData get video preview image url failed, image url: %s, workid: %d, error: %v", mediaItem.PreviewImageUrl, workInfo.WorkId, err)
			} else {
				if withRestore {
					// 转存图片
					uploadImageUrl := uploadCreatorHubImage("lip/ugc/public/image/", mediaItem.PreviewImageUrl, gameId, "global", "")
					if uploadImageUrl == "" {
						log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | createPostByCreatorHubData uploadCreatorHubImage failed: %s", mediaItem.PreviewImageUrl)
					} else {
						newImageUrl = uploadImageUrl
					}
				}
			}
			// 把视频信息写入扩展字段中
			videoContent := map[string]string{
				"video_url":         mediaItem.OriginalUrl,
				"video_id":          mediaItem.MediaID,
				"platform":          platformStr,
				"video_preview_url": newImageUrl,
			}
			videoContentByte, err := json.Marshal(videoContent)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost |createPostByCreatorHubData marshal video content failed: %v", err)
				continue
			}
			videoContentStr = string(videoContentByte)
		} else if mediaItem.MediaType == "photo" {
			if postType == 1 {
				postType = 2
			}
			var newImageUrl = mediaItem.PreviewImageUrl
			if withRestore {
				newImageUrl = uploadCreatorHubImage("lip/ugc/public/image/", mediaItem.OriginalUrl, gameId, "global", "")
				if newImageUrl == "" {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("WorkDataToPost | createPostByCreatorHubData uploadCreatorHubImage failed: %s", mediaItem.OriginalUrl)
					newImageUrl = mediaItem.OriginalUrl
				}
			}
			photos = append(photos, newImageUrl)
		}
	}
	createPostContent := &dynamic_pd.CreatePostContent{
		Content:        workInfo.WorkDesc,
		ContentSummary: "",
		ExtInfo:        videoContentStr,
		Platform:       platformStr,
		Title:          workInfo.WorkTitle,
		Language:       language,
		// IsOriginal:      2,
		// OriginalReprint: 1,
		PicUrls: photos,
	}
	return createPostContent, postType, nil
}
