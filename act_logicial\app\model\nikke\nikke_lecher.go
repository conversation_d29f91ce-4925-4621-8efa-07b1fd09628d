package nikke

import (
	"trpc.act.logicial/app/model"
)

type NikkeInfiniteLecherKillDataModel struct {
	model.AppModel
}

func (NikkeInfiniteLecherKillDataModel) TableName() string {
	return "nikke_infinite_lecher_kill_data"
}

type NikkeInfiniteLecherKillData struct {
	NikkeInfiniteLecherKillDataModel
	ID            uint32 `gorm:"column:id" json:"id"`                         // primary key
	RawKillNum    int64  `gorm:"column:raw_kill_num" json:"raw_kill_num"`     // 原始击杀数
	KillNum       int64  `gorm:"column:kill_num" json:"kill_num"`             // 击杀数
	LeftNum       int64  `gorm:"column:left_num" json:"left_num"`             // 剩余数
	CurrentMinute int64  `gorm:"column:current_minute" json:"current_minute"` // 当前分钟数
}

type NikkeInfiniteLecherShareGiftRecordModel struct {
	model.AppModel
}

func (NikkeInfiniteLecherShareGiftRecordModel) TableName() string {
	return "nikke_infinite_lecher_share_gift_record"
}

// NikkeInfiniteLecherShareGiftRecord 无限莱彻分享礼包发放记录表
type NikkeInfiniteLecherShareGiftRecord struct {
	NikkeInfiniteLecherShareGiftRecordModel
	ID          uint32 `gorm:"column:id" json:"id"`                     // primary key
	UID         string `gorm:"column:uid" json:"uid"`                   // 用户ID
	AccountType int16  `gorm:"column:account_type" json:"account_type"` // 账户类型
	RoleInfo    string `gorm:"column:role_info" json:"role_info"`       // 角色信息
	PresentID   string `gorm:"column:present_id" json:"present_id"`     // 礼包ID
	LangType    string `gorm:"column:lang_type" json:"lang_type"`       // 语言类型
	Status      int8   `gorm:"column:status" json:"status"`             // 当前状态0:未发货;1:已发货
}
