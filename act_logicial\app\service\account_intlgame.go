package service

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountIntlGame "git.code.oa.com/trpcprotocol/publishing_marketing/account_intlgame"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/constant"
)

// GetGameOpenId 获取游戏openid
func GetGameOpenId(ctx context.Context, gameId string) (gameOpenId string, err error) {
	accountIntlProxy := accountIntlGame.NewIntlgameClientProxy()
	rsp, err := accountIntlProxy.IsUserHasGameOpenid(ctx, &accountIntlGame.IsUserHasGameOpenidReq{
		GameId: gameId,
	})
	if err != nil {
		errParse := errs.ParseError(ctx, err)
		if errParse.Code == constant.GAME_HAS_NO_OPEN_ID {
			log.DebugContextf(ctx, "accountIntlProxy.IsUserHasGameOpenid has no openid err:%v", err)
			return "", nil
		}
		if errParse.Code == constant.SRC_OPEN_ID_NOT_EXIST {
			log.DebugContextf(ctx, "accountIntlProxy.GetGameOpenIdByLipOpenId lip openid err:%v",
				err)
			return "", code.ErrParamError
		}
		log.ErrorContextf(ctx, "accountIntlProxy.IsUserHasGameOpenid err:%v, gameId: %v", err, gameId)
		return "", code.ErrSystemError
	}

	gameOpenId = rsp.GameOpenid
	return
}

// GetGameOpenIdByLipOpenId 获取游戏openid
func GetGameOpenIdByLipOpenId(ctx context.Context, gameId string, openId string) (gameOpenId string, err error) {
	accountIntlProxy := accountIntlGame.NewIntlgameClientProxy()
	rsp, err := accountIntlProxy.Openid2OpenidWithNoToken(ctx, &accountIntlGame.Openid2OpenidWithNoTokenReq{
		ToGameid:  cast.ToInt64(gameId),
		LipOpenid: openId,
	})
	if err != nil {
		errParse := errs.ParseError(ctx, err)
		if errParse.Code == constant.GAME_OPEN_ID_EMPTY || errParse.Code == constant.NO_GAME_OPEN_ID {
			log.DebugContextf(ctx, "accountIntlProxy.GetGameOpenIdByLipOpenId has no openid err:%v, openId: %v",
				err, openId)
			return "", nil
		}
		if errParse.Code == constant.SRC_LIP_OPEN_ID_NOT_EXIST {
			log.DebugContextf(ctx, "accountIntlProxy.GetGameOpenIdByLipOpenId lip openid err:%v, openId: %v",
				err, openId)
			return "", code.ErrParamError
		}
		log.ErrorContextf(ctx, "accountIntlProxy.GetGameOpenIdByLipOpenId err:%v, gameId: %v", err, gameId)
		return "", code.ErrSystemError
	}
	log.DebugContextf(ctx, "accountIntlProxy.GetGameOpenIdByLipOpenId rsp: %+v, lipOpenId: %v", rsp, openId)
	gameOpenId = rsp.Openid
	return
}
