package df_tmp

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"trpc.act.logicial/app/task/cron"
)

// GunActUpdateTask 加载拼枪任务配置文件
func GunActUpdateTask(ctx context.Context, req *pb.GunActUpdateTaskReq) (*pb.GunActUpdateTaskRsp, error) {
	log.DebugContextf(ctx, "GunActUpdateTask enter, req: %v", req)
	go func() { // 启动一个协程来处理
		cron.UpdateGunActTasks(context.Background())
	}()
	rsp := &pb.GunActUpdateTaskRsp{}
	return rsp, nil
}
