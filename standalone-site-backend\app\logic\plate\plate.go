package plate

import (
	"context"
	"encoding/json"
	"errors"
	"sync"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

var nextPageCirsor = ""
var limit = int64(500)

func GetPlateList(c context.Context, req *pb.GetPlateListReq, language string) (*pb.GetPlateListRsp, error) {
	rsp := &pb.GetPlateListRsp{
		List:     make([]*pb.PlateItem, 0),
		PageInfo: &pb.PageInfo{},
	}
	var err error
	// 经讨论板块不会很多，写死全量
	req.NextPageCursor = nextPageCirsor
	req.Limit = limit
	// 先获取缓存数据
	plateListRedisKey := cache.GetPlateListKey(req.NextPageCursor, req.Limit, language)
	plateListCacheInfo, err := redis.GetClient().Get(c, plateListRedisKey).Result()
	if err == nil {
		if plateListCacheInfo == "" {
			rsp.PageInfo.IsFinish = true
			return rsp, nil
		}
		err = json.Unmarshal([]byte(plateListCacheInfo), rsp)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPlateList cache json.Unmarshal error.plateListCacheInfo: %s, err: %v", plateListCacheInfo, err)
			return nil, errs.NewCustomError(c, code.GetPlatesJsonUnmarshalError, "Failed to obtain plate list, data parsing exception")
		} else {
			return rsp, nil
		}
	}

	var plates []*model.Plate
	var nextPageCursor, previousPageCursor string
	conditions := &dao.PlateConditions{
		Status: 2,
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
		GameId: "30054",
		AreaId: "global",
	}
	// 查询类型：下一页数据
	if req.PageType == pb.PageType_NEXTPAGE {
		var idCursor int64
		// 如果是首页
		if req.NextPageCursor == "" {
			idCursor = 0
		} else {
			previousPageCursor = req.NextPageCursor
			idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
			}
			conditions.GtId = idCursor
		}
		plates, err = dao.GetPlateList(conditions, 10)
		// 生成下一页的游标
		if len(plates) > 0 {
			nextPageCursor, err = util.EncryptPageCursorI(plates[len(plates)-1].ID)
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetIndexPosts | Failed to create comments nextPageCursor")
			}
		}
		if len(plates) == 0 {
			rsp.PageInfo.IsFinish = true
			return rsp, nil
		}
	}
	var wg sync.WaitGroup
	var plateFormatted = make(chan *pb.PlateItem)
	for _, plate := range plates {
		wg.Add(1)
		go func(plateItem *model.Plate, lang string) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done()
			langData, err := dao.GetPlateListLanguage(int(plateItem.ID))
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostComments GetCommentInfoByUUID err, comment_uuid:(%d), err=(%v)", plateItem.ID, err)
				return
			}
			for _, datum := range langData {
				if datum.Language == lang {
					plateItem.Language = datum
					break
				}
			}
			if plateItem.Language == nil {
				for _, datum := range langData {
					if datum.Language == "en" {
						plateItem.Language = datum
						break
					}
				}
			}
			plateFormatted <- &pb.PlateItem{
				Id:               int32(plateItem.ID),
				Type:             plateItem.Type,
				LanguageData:     plateItem.JSONData,
				Status:           int32(plateItem.Status),
				Order:            int32(plateItem.Order),
				GameId:           plateItem.GameID,
				AreaId:           plateItem.AreaID,
				PlateName:        plateItem.Language.PlateName,
				UniqueIdentifier: plateItem.UniqueIdentifier,
			}
		}(plate, language)
	}
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		wg.Wait()
		close(plateFormatted)
	}()
	for item := range plateFormatted {
		rsp.List = append(rsp.List, item)
	}
	if len(rsp.List) == 0 || len(rsp.List) < int(req.Limit) {
		rsp.PageInfo.IsFinish = true
	} else {
		rsp.PageInfo.NextPageCursor = nextPageCursor
	}
	rsp.PageInfo.PreviousPageCursor = previousPageCursor

	plateListRspByte, err := json.Marshal(rsp)
	if err == nil {
		redis.GetClient().SetEX(c, plateListRedisKey, string(plateListRspByte), 2*time.Minute).Result()
	}

	return rsp, nil
}

func GetAllPlateNameMap(c context.Context) (map[int32]map[string]string, error) {
	plateLanguageMap := make(map[int32]map[string]string, 0)
	// 先获取缓存数据
	plateNamesRedisKey := cache.GetAllPlateNameKey()
	plateNamesCacheInfo, err := redis.GetClient().Get(c, plateNamesRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(plateNamesCacheInfo), &plateLanguageMap)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetAllPlateNameMap cache json.Unmarshal error.plateNamesRedisKey: %s, err: %v", plateNamesRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetPlateNamesJsonUnmarshalError, "Failed to obtain plate name info, data parsing exception")
		} else {
			return plateLanguageMap, nil
		}
	}
	plateLanguageList, err := dao.GetAllPlateListLanguage()
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetAllPlateNameMap GetAllPlateListLanguage err=(%v)", err)
		return nil, errs.NewCustomError(c, code.GetAllPlateLanguageMapFailed, "Failed to get plate language info")
	}

	for _, plateLanguageItem := range plateLanguageList {
		if _, ok := plateLanguageMap[int32(plateLanguageItem.PlateID)]; !ok {
			plateLanguageMap[int32(plateLanguageItem.PlateID)] = make(map[string]string, 0)
		}
		plateLanguageMap[int32(plateLanguageItem.PlateID)][plateLanguageItem.Language] = plateLanguageItem.PlateName
	}

	plateNamesByte, err := json.Marshal(plateLanguageMap)
	if err == nil {
		redis.GetClient().SetEX(c, plateNamesRedisKey, string(plateNamesByte), 2*time.Minute).Result()
	}
	return plateLanguageMap, nil
}

func GetPlateInfoById(c context.Context, plateId int64) (*model.Plate, error) {
	// 先获取缓存数据
	var plateInfo *model.Plate
	plateRedisKey := cache.GetPlateKey(plateId)
	plateCacheInfo, err := redis.GetClient().Get(c, plateRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(plateCacheInfo), &plateInfo)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetPlateInfoById cache json.Unmarshal error.plateRedisKey: %s, err: %v", plateRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetPlateJsonUnmarshalError, "Failed to obtain plate info, data parsing exception")
		} else {
			return plateInfo, nil
		}
	}
	plateInfo, err = dao.PlateGet(&dao.PlateConditions{
		Id: plateId,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPlateInfoByPlateId PlateGet failed, err: %v", err)
		return nil, errs.NewCustomError(c, code.InvalidParamsErr, "Failed to obtain plate information")
	}

	plateInfoByte, err := json.Marshal(plateInfo)
	if err == nil {
		redis.GetClient().SetEX(c, plateRedisKey, string(plateInfoByte), 2*time.Minute).Result()
	}

	return plateInfo, nil
}

func RemovePlatListCache(c context.Context) {
	var languageCacheKey = make([]string, 0)
	for _, language := range constants.AllPostLanguages {
		plateListRedisKey := cache.GetPlateListKey(nextPageCirsor, limit, language)
		languageCacheKey = append(languageCacheKey, plateListRedisKey)
	}
	if len(languageCacheKey) > 0 {
		redis.GetClient().Del(c, languageCacheKey...)
	}

}
