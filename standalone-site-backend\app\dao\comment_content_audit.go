package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func CommentContentAuditGet(id int64, commentUUID string) (*model.CommentContentAudit, error) {
	var commentContentAudit model.CommentContentAudit
	db := DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName())
	if id > 0 {
		db = db.Where("id= ? AND  is_del = ?", id, 0)
	}
	if commentUUID != "" {
		db = db.Where("comment_uuid = ? AND  is_del = ?", commentUUID, 0)
	}
	err := db.First(&commentContentAudit).Error
	if err != nil {
		return nil, err
	}
	return &commentContentAudit, nil
}

func CommentContentAuditList(status int, limit int, ltId int64) ([]*model.CommentContentAudit, error) {
	var commentContentAudit []*model.CommentContentAudit
	db := DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName())
	if status > 0 {
		db = db.Where("status = ?", status)
	}
	if limit > 0 {
		db = db.Limit(limit)
	}
	if ltId > 0 {
		db = db.Where("id < ?", ltId)
	}
	err := db.Order("id desc").Find(&commentContentAudit).Error
	if err != nil {
		return nil, err
	}
	return commentContentAudit, nil
}

// CommentContentAuditListV2 返回指定的字段
func CommentContentAuditListV2(status int) ([]*model.CommentContentAudit, error) {
	var commentContentAudit []*model.CommentContentAudit
	db := DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName())
	if status > 0 {
		db = db.Where("status = ?", status)
	}
	err := db.Order("id desc").Find(&commentContentAudit).Error
	if err != nil {
		return nil, err
	}
	return commentContentAudit, nil
}

func CommentContentAuditCreate(commentContentAudit *model.CommentContentAudit) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Omit("Comment").Create(&commentContentAudit).Error
	return err
}

func CommentContentAuditDelete(id int64, data map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("id = ? AND is_del = ?", id, 0).Updates(&data).Error
}

func CommentContentAuditDeleteByCommentIds(commentIds []int64) error {
	if len(commentIds) == 0 {
		return nil
	}
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("comment_id IN ? AND type = 1 AND is_del = ?", commentIds, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func CommentContentAuditIgnoreByCommentIds(commentIds []int64) error {
	if len(commentIds) == 0 {
		return nil
	}
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("comment_id IN ? AND type = ? AND is_del = ? AND status = ?", commentIds, 1, 0, 1).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"status":      3,
	}).Error
}

func CommentContentAuditIgnoreReplyByCommentIds(commentUuids []string) error {
	if len(commentUuids) == 0 {
		return nil
	}
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("comment_uuid IN ? AND type = ? AND is_del = ? AND status = ?", commentUuids, 2, 0, 1).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"status":      3,
	}).Error
}

func CommentContentAuditDeleteByCommentReplyIds(commentIds []int64) error {
	if len(commentIds) == 0 {
		return nil
	}
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("comment_id IN ? AND type = 2 AND is_del = ?", commentIds, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func CommentContentAuditIgnoreByCommentReplyIds(replyIds []int64) error {
	if len(replyIds) == 0 {
		return nil
	}
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("comment_id IN ? AND type = ? AND is_del = ? AND status = ?", replyIds, 2, 0, 1).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"status":      3,
	}).Error
}

func CommentContentAuditUpdate(audit *model.CommentContentAudit) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("id = ? AND is_del = ?", audit.ID, 0).Updates(audit).Error
}

func CommentContentAuditUpdateV2(audit *model.CommentContentAudit) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("comment_uuid = ? AND is_del = ?", audit.CommentUUID, 0).Updates(audit).Error
}

func UpdateCommenAudit(commentUuid string, data map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("comment_uuid = ?", commentUuid).Updates(data).Error
}

func UpdateCommentAuditV2(commentUuids []string, data map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("comment_uuid in ?", commentUuids).Updates(data).Error
}

// 获取机审状态变化，但状态为变化数据
func GetReviewedByNotPublishList(limit int) ([]*model.CommentContentAudit, error) {
	var commentContentAuditList []*model.CommentContentAudit

	err := DB.SelectConnect("db_standalonesite").Table((&model.CommentContentAudit{}).TableName()).Where("status = 1 and (machine_status = 1 and artificial_status = 0) and is_del = 0").Limit(limit).Find(&commentContentAuditList).Error
	if err != nil {
		return nil, err
	}
	return commentContentAuditList, nil
}
