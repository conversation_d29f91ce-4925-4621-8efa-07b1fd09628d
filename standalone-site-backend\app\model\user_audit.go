package model

type UserAudit struct {
	*Model
	User             *UserContent `json:"-" gorm:"-"`
	TextRiskLevel    int          `json:"text_risk_level"` // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	TextRiskType     int          `json:"text_risk_type"`  // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	PicRiskLevel     int          `json:"pic_risk_level"`  // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	PicRiskType      int          `json:"pic_risk_type"`   // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	AuditStatus      int          `json:"audit_status"`    //状态  1:未处理 2:已发布 3:已忽略
	AuditUser        string       `json:"audit_user"`      //审批用户
	Type             int8         `json:"type"`            //类型：1:昵称;2:个签；3:头像
	Context          string       `json:"context"`         //审核内容
	AuditOn          int64        `json:"audit_on"`        //审核时间
	AuditIntroduce   string       `json:"audit_introduce"` //审核介绍
	IntlOpenid       string       `json:"intl_openid"`
	Language         string       `json:"language"`
	MachineStatus    int          `json:"machine_status"`    //机审状态：0-未处理1-审核通过2-审核异常
	ArtificialStatus int          `json:"artificial_status"` //人审状态：0-未处理1-审核通过2-审核拒绝
	DelType          int32        `json:"del_type"`          // 删除类型，isdel > 0 这个字段才生效
	DelReason        int32        `json:"del_reason"`        // 删除原因
}

type UserAuditFormatted struct {
	Id             int64  `json:"id"`
	TextRiskLevel  int    `json:"text_risk_level"` // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	TextRiskType   int    `json:"text_risk_type"`  // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	PicRiskLevel   int    `json:"pic_risk_level"`  // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	PicRiskType    int    `json:"pic_risk_type"`   // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	AuditStatus    int    `json:"audit_status"`    //状态  1:未处理 2:已发布 3:已忽略
	AuditUser      string `json:"audit_user"`      //审批用户
	Type           int8   `json:"type"`            //类型：1:昵称;2:个签；3:头像
	Context        string `json:"context"`         //审核内容
	AuditOn        int64  `json:"audit_on"`        //审核时间
	AuditIntroduce string `json:"audit_introduce"` //审核介绍
	IntlOpenid     string `json:"intl_openid"`     //用户id
	CreatedOn      int64  `json:"created_on"`      // 创建时间
	ModifiedOn     int64  `json:"modified_on"`     // 修改时间
	DeletedOn      int64  `json:"deleted_on"`      // 删除时间
	IsDel          int8   `json:"is_del"`          // 是否删除 0:否 1:是
}

type UserAuditAndUserFormatted struct {
	Id             int64        `json:"id"`
	User           *UserContent `json:"-"`
	TextRiskLevel  int          `json:"text_risk_level"` // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	TextRiskType   int          `json:"text_risk_type"`  // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	PicRiskLevel   int          `json:"pic_risk_level"`  // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
	PicRiskType    int          `json:"pic_risk_type"`   // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	AuditStatus    int          `json:"audit_status"`    //状态  1:未处理 2:已发布 3:已忽略
	AuditUser      string       `json:"audit_user"`      //审批用户
	Type           int8         `json:"type"`            //类型：1:昵称;2:个签；3:头像
	Context        string       `json:"context"`         //审核内容
	AuditOn        int64        `json:"audit_on"`        //审核时间
	AuditIntroduce string       `json:"audit_introduce"` //审核介绍
	IntlOpenid     string       `json:"intl_openid"`     //用户id
	CreatedOn      int64        `json:"created_on"`      // 创建时间
	ModifiedOn     int64        `json:"modified_on"`     // 修改时间
	IsDel          int8         `json:"is_del"`          // 是否删除 0:否 1:是
	DeletedOn      int64        `json:"deleted_on"`      // 删除时间
}

func (u *UserAudit) TableName() string {
	return "p_user_audit"
}

type ESUserAuditInfo struct {
	Id               int64  `json:"id"`
	IntlOpenid       string `json:"intl_openid"`
	IntlUserOpenid   string `json:"intl_user_openid"`
	TextRiskLevel    int64  `json:"text_risk_level"`
	TextRiskType     int64  `json:"text_risk_type"`
	PicRiskLevel     int64  `json:"pic_risk_level"` // 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败
	PicRiskType      int64  `json:"pic_risk_type"`  // 风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
	AuditStatus      int32  `json:"audit_status"`   // 状态  1:未处理 2:已发布 3:已忽略
	Type             int32  `json:"type"`           // 类型：1:昵称;2:个签；3:头像
	AuditUser        string `json:"audit_user"`
	Context          string `json:"context"`
	AuditOn          int32  `json:"audit_on"`
	AuditIntroduce   string `json:"audit_introduce"`
	CreatedOn        int32  `json:"created_on"`
	ModifiedOn       int64  `json:"modified_on"`
	DeletedOn        int32  `json:"deleted_on"`
	IsDel            int    `json:"is_del"`
	Language         string `json:"language"`
	MachineStatus    int32  `json:"machine_status"`    //机审状态：0-未处理1-审核通过2-审核异常
	ArtificialStatus int32  `json:"artificial_status"` //人审状态：0-未处理1-审核通过2-审核拒绝
	DelType          int32  `json:"del_type"`          // 删除类型，isdel > 0 这个字段才生效
	DelReason        int32  `json:"del_reason"`        // 删除原因
}
