package user

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/kafka"
	"trpc.publishing_application.standalonesite/app/model"
)

// 新用户注册后的活动
func UserRegisterActivity(ctx context.Context, intlOpenid string) error {
	log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("UserRegisterActivity start, intlOpenid: %s", intlOpenid)
	conf := config.GetConfig()
	if conf == nil ||
		conf.TasksConf == nil ||
		conf.TasksConf.TempTask == nil ||
		conf.TasksConf.TempTask.NewUserRegisterTask == nil ||
		conf.TasksConf.TempTask.NewUserRegisterTask.StartTime == 0 ||
		conf.TasksConf.TempTask.NewUserRegisterTask.EndTime == 0 ||
		conf.TasksConf.TempTask.NewUserRegisterTask.MsgId == 0 {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("UserRegisterActivity config is nil")
		return nil
	}
	// 判断是否在时间范围内
	curTime := time.Now().Unix()
	if curTime < conf.TasksConf.TempTask.NewUserRegisterTask.StartTime ||
		curTime > conf.TasksConf.TempTask.NewUserRegisterTask.EndTime {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("UserRegisterActivity time is not in range")
		return nil
	}
	// 将数据写入kafka
	siteMsgItem := &model.SiteMessageKafkaData{
		MsgID:               conf.TasksConf.TempTask.NewUserRegisterTask.MsgId,
		IntlOpenidList:      make([]string, 0),
		PushType:            int(constants.NUMBERPACKAGEPUSH),
		IntlOpenidJoinCdkey: make([]*model.UserJoinCdkey, 0),
	}
	siteMsgItem.IntlOpenidList = append(siteMsgItem.IntlOpenidList, intlOpenid)
	// push到kafka中
	marshal, err := json.Marshal(siteMsgItem)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("batch write queue json code failed: %v, message: %v", err, siteMsgItem)
		return errs.NewCustomError(context.Background(), code.SiteMsgJsonCodeFailed, "batchToKafka | Site message json code failed")
	}
	key := fmt.Sprintf("site-msg-%d", time.Microsecond)
	err = kafka.Produce(context.Background(), constants.SiteMessageProducer, "", string(marshal))
	if err != nil {
		// 写入kafka失败
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("batch write queue failed: %v, key: %s,message: %v", err, key, siteMsgItem)
		return errs.NewCustomError(context.Background(), code.SiteMsgWriteToQueueFailed, "batchToKafka | Site message write to queue failed")
	} else {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("UserRegisterActivity success, intlOpenid: %s", intlOpenid)
	}
	return nil
}
