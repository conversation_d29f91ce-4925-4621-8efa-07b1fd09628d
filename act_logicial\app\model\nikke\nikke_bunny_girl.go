package nikke

import "trpc.act.logicial/app/model"

type NikkeBunnyGirlGiftDistributionRecordModel struct {
	model.AppModel
}

func (NikkeBunnyGirlGiftDistributionRecordModel) TableName() string {
	return "nikke_bunny_girl_gift_distribution_record"
}

// 礼包发放记录表
type NikkeBunnyGirlGiftDistributionRecord struct {
	NikkeBunnyGirlGiftDistributionRecordModel
	ID          uint   `gorm:"type:int;column:id;primary_key" comment:"primary key"`
	UID         string `gorm:"type:varchar(64);column:uid" comment:"用户ID"`
	AccountType int    `gorm:"type:smallint;column:account_type" comment:"用户类型"`
	RoleInfo    string `gorm:"type:varchar(512);column:role_info" comment:"角色信息"`
	PresentID   string `gorm:"type:varchar(64);column:present_id" comment:"礼包id"`
	LangType    string `gorm:"type:varchar(64);column:lang_type" comment:"语言类型"`
	Status      int8   `gorm:"type:tinyint;column:status" comment:"当前状态0:未发货;1:已发货"`
}
