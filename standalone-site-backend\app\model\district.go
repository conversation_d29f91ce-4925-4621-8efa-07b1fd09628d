package model

type District struct {
	*Model
	GameID   string `gorm:"column:game_id" json:"game_id"`     //游戏id
	AreaID   string `gorm:"column:area_id" json:"area_id"`     //大区id
	ToolName string `gorm:"column:tool_name" json:"tool_name"` //工具名称
	Icon     string `gorm:"column:icon" json:"icon"`           //icon，前端显示的图片链接
	JumpURL  string `gorm:"column:jump_url" json:"jump_url"`   //前端点击跳转的URL
	Order    int    `gorm:"column:order" json:"order"`         //顺序值
	Creator  string `gorm:"column:creator" json:"creator"`     //创建人
	Updater  string `gorm:"column:updater" json:"updater"`     //操作人
	ExtInfo  string `gorm:"column:ext_info" json:"ext_info"`   //扩展信息
}

type DistrictLanguage struct {
	*Model
	DistrictID int64  `gorm:"column:district_id" json:"district_id"` //话题 id
	Language   string `gorm:"column:language" json:"language"`       //语言
	ToolName   string `gorm:"column:tool_name" json:"tool_name"`     //工具名称
	Creator    string `gorm:"column:creator" json:"creator"`         //创建人
	Updater    string `gorm:"column:updater" json:"updater"`         //更新人
}

func (d *District) TableName() string {
	return "p_district"
}

func (d *DistrictLanguage) TableName() string {
	return "p_district_language"
}
