// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.6.1
// source: query_batch_request.proto

package hok_request

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TargetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetID   string `protobuf:"bytes,1,opt,name=TargetID,proto3" json:"TargetID,omitempty"` // 指标ID
	OpenID     string `protobuf:"bytes,2,opt,name=OpenID,proto3" json:"OpenID,omitempty"`
	ZoneID     uint32 `protobuf:"varint,3,opt,name=ZoneID,proto3" json:"ZoneID,omitempty"`
	HeroID     uint32 `protobuf:"varint,4,opt,name=HeroID,proto3" json:"HeroID,omitempty"`        // 英雄id
	StartDay   string `protobuf:"bytes,5,opt,name=StartDay,proto3" json:"StartDay,omitempty"`     // 闭区间
	EndDay     string `protobuf:"bytes,6,opt,name=EndDay,proto3" json:"EndDay,omitempty"`         // 闭区间
	HeroIDList string `protobuf:"bytes,7,opt,name=HeroIDList,proto3" json:"HeroIDList,omitempty"` // 英雄id列表,逗号分隔
	SkinIDList string `protobuf:"bytes,8,opt,name=SkinIDList,proto3" json:"SkinIDList,omitempty"` // 皮肤id列表,逗号分隔
	Wave       uint32 `protobuf:"varint,9,opt,name=Wave,proto3" json:"Wave,omitempty"`
	CountryID  uint32 `protobuf:"varint,10,opt,name=CountryID,proto3" json:"CountryID,omitempty"` // 国家id
	SkinID     uint32 `protobuf:"varint,11,opt,name=SkinID,proto3" json:"SkinID,omitempty"`       // 皮肤id
}

func (x *TargetInfo) Reset() {
	*x = TargetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_query_batch_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetInfo) ProtoMessage() {}

func (x *TargetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_query_batch_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetInfo.ProtoReflect.Descriptor instead.
func (*TargetInfo) Descriptor() ([]byte, []int) {
	return file_query_batch_request_proto_rawDescGZIP(), []int{0}
}

func (x *TargetInfo) GetTargetID() string {
	if x != nil {
		return x.TargetID
	}
	return ""
}

func (x *TargetInfo) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *TargetInfo) GetZoneID() uint32 {
	if x != nil {
		return x.ZoneID
	}
	return 0
}

func (x *TargetInfo) GetHeroID() uint32 {
	if x != nil {
		return x.HeroID
	}
	return 0
}

func (x *TargetInfo) GetStartDay() string {
	if x != nil {
		return x.StartDay
	}
	return ""
}

func (x *TargetInfo) GetEndDay() string {
	if x != nil {
		return x.EndDay
	}
	return ""
}

func (x *TargetInfo) GetHeroIDList() string {
	if x != nil {
		return x.HeroIDList
	}
	return ""
}

func (x *TargetInfo) GetSkinIDList() string {
	if x != nil {
		return x.SkinIDList
	}
	return ""
}

func (x *TargetInfo) GetWave() uint32 {
	if x != nil {
		return x.Wave
	}
	return 0
}

func (x *TargetInfo) GetCountryID() uint32 {
	if x != nil {
		return x.CountryID
	}
	return 0
}

func (x *TargetInfo) GetSkinID() uint32 {
	if x != nil {
		return x.SkinID
	}
	return 0
}

type DataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        int32       `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"` // 指标是否计算成功，0成功1失败
	Msg         string      `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg,omitempty"`
	Target      *TargetInfo `protobuf:"bytes,3,opt,name=Target,proto3" json:"Target,omitempty"`           // 指标ID
	TargetValue string      `protobuf:"bytes,4,opt,name=TargetValue,proto3" json:"TargetValue,omitempty"` // 指标值
}

func (x *DataInfo) Reset() {
	*x = DataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_query_batch_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataInfo) ProtoMessage() {}

func (x *DataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_query_batch_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataInfo.ProtoReflect.Descriptor instead.
func (*DataInfo) Descriptor() ([]byte, []int) {
	return file_query_batch_request_proto_rawDescGZIP(), []int{1}
}

func (x *DataInfo) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DataInfo) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DataInfo) GetTarget() *TargetInfo {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *DataInfo) GetTargetValue() string {
	if x != nil {
		return x.TargetValue
	}
	return ""
}

type QueryBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityID string        `protobuf:"bytes,1,opt,name=ActivityID,proto3" json:"ActivityID,omitempty"` // 活动ID
	Targets    []*TargetInfo `protobuf:"bytes,2,rep,name=Targets,proto3" json:"Targets,omitempty"`       // 指标ID
}

func (x *QueryBatchRequest) Reset() {
	*x = QueryBatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_query_batch_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryBatchRequest) ProtoMessage() {}

func (x *QueryBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_query_batch_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryBatchRequest.ProtoReflect.Descriptor instead.
func (*QueryBatchRequest) Descriptor() ([]byte, []int) {
	return file_query_batch_request_proto_rawDescGZIP(), []int{2}
}

func (x *QueryBatchRequest) GetActivityID() string {
	if x != nil {
		return x.ActivityID
	}
	return ""
}

func (x *QueryBatchRequest) GetTargets() []*TargetInfo {
	if x != nil {
		return x.Targets
	}
	return nil
}

type QueryBatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32       `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"` // 0成功1失败
	Msg   string      `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg,omitempty"`
	Datas []*DataInfo `protobuf:"bytes,3,rep,name=Datas,proto3" json:"Datas,omitempty"`
}

func (x *QueryBatchResponse) Reset() {
	*x = QueryBatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_query_batch_request_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryBatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryBatchResponse) ProtoMessage() {}

func (x *QueryBatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_query_batch_request_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryBatchResponse.ProtoReflect.Descriptor instead.
func (*QueryBatchResponse) Descriptor() ([]byte, []int) {
	return file_query_batch_request_proto_rawDescGZIP(), []int{3}
}

func (x *QueryBatchResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *QueryBatchResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *QueryBatchResponse) GetDatas() []*DataInfo {
	if x != nil {
		return x.Datas
	}
	return nil
}

var File_query_batch_request_proto protoreflect.FileDescriptor

var file_query_batch_request_proto_rawDesc = []byte{
	0x0a, 0x19, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x68, 0x6f, 0x6b,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xae, 0x02, 0x0a, 0x0a, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x5a,
	0x6f, 0x6e, 0x65, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x5a, 0x6f, 0x6e,
	0x65, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x48, 0x65, 0x72, 0x6f, 0x49, 0x44, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x48, 0x65, 0x72, 0x6f, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x45, 0x6e, 0x64, 0x44, 0x61,
	0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x79, 0x12,
	0x1e, 0x0a, 0x0a, 0x48, 0x65, 0x72, 0x6f, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x48, 0x65, 0x72, 0x6f, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x53, 0x6b, 0x69, 0x6e, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x6b, 0x69, 0x6e, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x57, 0x61, 0x76, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x57,
	0x61, 0x76, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x44,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49,
	0x44, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x6b, 0x69, 0x6e, 0x49, 0x44, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x53, 0x6b, 0x69, 0x6e, 0x49, 0x44, 0x22, 0x83, 0x01, 0x0a, 0x08, 0x44, 0x61,
	0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4d, 0x73, 0x67, 0x12, 0x2f, 0x0a, 0x06,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68,
	0x6f, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x20, 0x0a,
	0x0b, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x66, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x49, 0x44, 0x12, 0x31, 0x0a, 0x07, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68, 0x6f, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0x67, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x4d, 0x73, 0x67, 0x12, 0x2b, 0x0a, 0x05, 0x44, 0x61, 0x74, 0x61, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x6f, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x44, 0x61, 0x74, 0x61, 0x73,
	0x32, 0x5e, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x50,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x1e, 0x2e,
	0x68, 0x6f, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e,
	0x68, 0x6f, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x45, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x65, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x73, 0x2e, 0x68, 0x6f, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5a, 0x24, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x2f, 0x68, 0x6f, 0x6b, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_query_batch_request_proto_rawDescOnce sync.Once
	file_query_batch_request_proto_rawDescData = file_query_batch_request_proto_rawDesc
)

func file_query_batch_request_proto_rawDescGZIP() []byte {
	file_query_batch_request_proto_rawDescOnce.Do(func() {
		file_query_batch_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_query_batch_request_proto_rawDescData)
	})
	return file_query_batch_request_proto_rawDescData
}

var file_query_batch_request_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_query_batch_request_proto_goTypes = []interface{}{
	(*TargetInfo)(nil),         // 0: hok_request.TargetInfo
	(*DataInfo)(nil),           // 1: hok_request.DataInfo
	(*QueryBatchRequest)(nil),  // 2: hok_request.QueryBatchRequest
	(*QueryBatchResponse)(nil), // 3: hok_request.QueryBatchResponse
}
var file_query_batch_request_proto_depIdxs = []int32{
	0, // 0: hok_request.DataInfo.Target:type_name -> hok_request.TargetInfo
	0, // 1: hok_request.QueryBatchRequest.Targets:type_name -> hok_request.TargetInfo
	1, // 2: hok_request.QueryBatchResponse.Datas:type_name -> hok_request.DataInfo
	2, // 3: hok_request.QueryBatch.GetFeatures:input_type -> hok_request.QueryBatchRequest
	3, // 4: hok_request.QueryBatch.GetFeatures:output_type -> hok_request.QueryBatchResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_query_batch_request_proto_init() }
func file_query_batch_request_proto_init() {
	if File_query_batch_request_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_query_batch_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_query_batch_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_query_batch_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryBatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_query_batch_request_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryBatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_query_batch_request_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_query_batch_request_proto_goTypes,
		DependencyIndexes: file_query_batch_request_proto_depIdxs,
		MessageInfos:      file_query_batch_request_proto_msgTypes,
	}.Build()
	File_query_batch_request_proto = out.File
	file_query_batch_request_proto_rawDesc = nil
	file_query_batch_request_proto_goTypes = nil
	file_query_batch_request_proto_depIdxs = nil
}
