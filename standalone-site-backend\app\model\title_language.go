package model

// 用户称号语言
type TitleLanguage struct {
	*Model
	Language  string `json:"language"`
	TitleId   int64  `json:"title_id"`
	Title     string `json:"title"`
	Introduce string `json:"introduce"`
}

type TitleLanguageFormated struct {
	ID        int64  `json:"id"`
	Language  string `json:"language"`
	TitleId   int64  `json:"title_id"`
	Title     string `json:"title"`
	Introduce string `json:"introduce"`
}

func (t *TitleLanguage) TableName() string {
	return "p_title_language"
}

func (t *TitleLanguage) Format() *TitleLanguageFormated {
	if t.Model != nil {
		return &TitleLanguageFormated{
			ID:        t.ID,
			Language:  t.Language,
			TitleId:   t.TitleId,
			Title:     t.Title,
			Introduce: t.Introduce,
		}
	}

	return nil
}
