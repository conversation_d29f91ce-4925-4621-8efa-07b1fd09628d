package user

import (
	"context"
	"errors"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"gorm.io/gorm"
	"time"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

// 设置用户降权, actionType: 1-设置0-取消
func CMSSetUserDemotion(c context.Context, intlOpenids []string, endTime int64, actionType int, updateUser string) error {
	if len(intlOpenids) == 0 {
		return nil
	}
	if actionType == constants.SetUserDemotion {
		// 获取哪些是已经有数据了的，就不重复操作
		conditionsT := &dao.UserPermissionConditions{
			IntlOpenids: intlOpenids,
			ActionType:  constants.UserActionType_Demotion,
			ValidOn:     time.Now().Unix(), //查询当前时间之后的数据
		}
		userPermissionList, err := dao.UserPermissionList(conditionsT, 0)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetUserDemotion | get user permission failed, err: %v", err)
			return errs.NewCustomError(c, code.GetUserPermissionAuditInfoError, "set user demotion failed!")
		}
		var userPermissions []*model.UserPermission
		for _, intlOpenid := range intlOpenids {
			needBreak := false
			for _, userPermission := range userPermissionList {
				if intlOpenid == userPermission.IntlOpenid {
					needBreak = true
				}
			}
			// 已存在的数据就直接过滤
			if needBreak {
				continue
			}
			userPermission := &model.UserPermission{
				IntlOpenid: intlOpenid,
				ActionType: constants.UserActionType_Demotion,
				ValidOn:    endTime,
				Creator:    updateUser,
				GameId:     "30054",
				AreaId:     "global",
			}
			userPermissions = append(userPermissions, userPermission)

			// 更新es
			doc := map[string]interface{}{
				"intl_openid":  intlOpenid,
				"is_demotion":  1,
				"demotion_off": endTime,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)
		}
		if len(userPermissions) == 0 {
			return nil
		}
		// 更新db
		if err := dao.BatchAddUserPermission(userPermissions); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetUserDemotion BatchAddUserPermission err: %v", err)
			return errs.NewCustomError(c, code.CMSSetUserDemotionErr, "CMSSetUserDemotion | Failed to demotion user.")
		}
		for _, permission := range userPermissions {
			userRedisKey := cache.GetDemotionUserKey(constants.SetUserDemotion, permission.IntlOpenid)

			// 更新redis
			cErr := redis.GetClient().SetEX(context.Background(), userRedisKey, "1", time.Duration(endTime-time.Now().Unix())*time.Second).Err()
			if cErr != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetUserDemotion err: %v", permission.IntlOpenid, cErr)
				return errs.NewCustomError(c, code.CMSSetMuteUserInfoError, "CMSSetUserDemotion | Failed to demotion user cache.")
			}
			// 删除用户缓存
			DeleteUserInfoCache(context.Background(), permission.IntlOpenid)
		}
	} else {
		// 取消用户禁言
		if err := dao.BatchUserPermissionDelete(intlOpenids, constants.UserActionType_Demotion, 0); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSSetUserDemotion BatchUserPermission err: %v", err)
			return errs.NewCustomError(c, code.CMSCancelUserDemotionErr, "CMSSetUserDemotion | Failed to demotion user.")
		}
		//删除redis
		for _, intlOpenid := range intlOpenids {
			// 更新es
			doc := map[string]interface{}{
				"intl_openid":  intlOpenid,
				"is_demotion":  0,
				"demotion_off": 0,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, intlOpenid, doc)

			userRedisKey := cache.GetDemotionUserKey(constants.SetUserDemotion, intlOpenid)

			// 更新redis
			cErr := redis.GetClient().Del(c, userRedisKey).Err()

			if cErr != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("%s ,CMSSetUserDemotion cancel demotion err: %v", intlOpenid, cErr)
			}
			// 删除用户缓存
			DeleteUserInfoCache(context.Background(), intlOpenid)
		}
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	return nil
}
