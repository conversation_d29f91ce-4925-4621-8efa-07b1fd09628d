package model

type ChSubmissionPost struct {
	*Model
	IntlOpenid   string `gorm:"column:intl_openid" json:"intl_openid"`
	ChUid        string `gorm:"column:ch_uid" json:"ch_uid"`
	SubmissionId string `gorm:"column:submission_id" json:"submission_id"`
	PostUid      string `gorm:"column:post_uid" json:"post_uid"`
}

func (a *ChSubmissionPost) TableName() string {
	return "ch_submission_post"
}

type CreatorHubWorkKafkaItem struct {
	WorkId      int64  `json:"WorkId"`
	Uid         string `json:"Uid"`
	AdminTaskId int64  `json:"AdminTaskId"`
}

type CreatorHubWorkKafkaData struct {
	WorkList    []*CreatorHubWorkKafkaItem `json:"WorkList"`
	AuditStatus int                        `json:"AuditStatus"`
	GameId      string                     `json:"GameId"`
	AreaId      string                     `json:"AreaId"`
}
