package security

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/pkg/httpclient"
)

// 风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
const (
	RISK_LEVLE_NOT_DETECTED = iota
	RISK_LEVLE_NORMAL
	RISK_LEVLE_HIGH_RISK
	RISK_LEVLE_NORMAL_RISK
	RISK_LEVLE_DETECT_FAILED
)

// 安全审核图片类型，1：头像图片；2：非头像图片
const (
	SECURITY_PIC_TYPE_AVATAR = iota + 1
	SECURITY_PIC_TYPE_NOT_AVATAR
)

type CheckPostContent struct {
	Content      string `json:"content"`
	Title        string `json:"title"`
	Introduction string `json:"introduction"`
	DataId       int64  `json:"data_id"`
	PicType      int    `json:"pic_type"` // 1：头像图片	2: 非头像图片
}

type AccountInfo struct {
	Account  string `json:"account"`   // 这里用的是intlsdk的openid
	PlatId   int    `json:"plat_id"`   // 玩家平台id。0 : IOS 1: andriod 2: 无效的值，不使用	3: PC
	RoleName string `json:"role_name"` // 这里用的是intlsdk的昵称
}

type CheckTextParam struct {
	Tasks []CheckTextDataItem `json:"tasks"`
}

type CheckTextDataItem struct {
	SceneId      int64       `json:"scene_id"`
	Text         string      `json:"text"`
	Title        string      `json:"title"`
	Introduction string      `json:"introduction"`
	DataId       string      `json:"data_id"`
	Account      AccountInfo `json:"account"`
}

type CheckTextResp struct {
	ErrCode int64                 `json:"err_code"`
	ErrMsg  string                `json:"err_msg"`
	Data    []CheckTextResultData `json:"data"`
}

type CheckTextResultData struct {
	ErrCode         int64           `json:"err_code"`
	ErrMsg          string          `json:"err_msg"`
	DataId          string          `json:"data_id"`
	RequestId       string          `json:"request_id"`
	TextCheckResult TextCheckResult `json:"text_check_result"`
}

type TextCheckResult struct {
	CheckResult  int    `json:"check_result"`
	FilteredText string `json:"filtered_text"`
	Label        int    `json:"label"`
	CheckDesc    string `json:"check_desc"`
}

type CheckPicParam struct {
	Tasks []CheckPicDataItem `json:"tasks"`
}

type CheckPicDataItem struct {
	SceneId int64       `json:"scene_id"`
	Url     string      `json:"url"`
	PicType int         `json:"pic_type"`
	DataId  string      `json:"data_id"`
	Account AccountInfo `json:"account"`
}

type CheckPicResp struct {
	ErrCode int64                `json:"err_code"`
	ErrMsg  string               `json:"err_msg"`
	Data    []CheckPicResultData `json:"data"`
}

type CheckPicResultData struct {
	ErrCode   int64  `json:"err_code"`
	ErrMsg    string `json:"err_msg"`
	DataId    string `json:"data_id"`
	RequestId string `json:"request_id"`
	Label     int    `json:"label"`
	CheckDesc string `json:"check_desc"`
}

// BatchCheckText 内容安全检测，文本检测接口
// 参考：https://ace.woa.com/#/doc-center/bb276d7a22244a3f445ecf52847aaf336a0442ed
func BatchCheckText(checkParams []CheckPostContent, accountInfo AccountInfo, sceneId int64) (checkTextResp CheckTextResp, err error) {
	postDataStr := getTextReqBodyStr(checkParams, accountInfo, sceneId)
	configModel := config.GetConfig()
	secretId := configModel.Security.SecretId
	secretKey := configModel.Security.SecretKey
	queryParam := ""
	// 生成签名，调用intlsdk
	gmtDate := getFormatNow()
	uri := "/check/batch_text"
	requestURL := fmt.Sprintf("%s%s", configModel.Security.Host, uri)
	signatureStr := getSignatureString(secretId, secretKey, uri, gmtDate, queryParam, postDataStr)

	optionOne := httpclient.ClientOption{
		URL:     requestURL,
		Type:    "POST",
		Timeout: 300 * time.Second,
		Header: map[string]string{
			"Content-Type":     "application/json",
			"X-HMAC-SIGNATURE": signatureStr,
			"X-HMAC-SECRET-ID": secretId,
			"X-HMAC-ALGORITHM": "hmac-sha256",
			"Date":             gmtDate,
		},
		PostString: postDataStr,
	}

	// 发请求
	resultOption := httpclient.RequestOne(optionOne)
	// 结果判断
	if resultOption.RequestError != nil {
		err = resultOption.RequestError
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("BatchText httpclient.RequestOne error:%v", err)
		return
	}
	apiResultString := resultOption.Result
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("PushPostToSecurityDetection security.BatchCheckText return result: %v", apiResultString)
	err = json.Unmarshal([]byte(apiResultString), &checkTextResp)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("BatchText json.Unmarshal error:%v", err)
		return
	}
	return
}

// BatchCheckPic 内容安全检测，图片检测接口
func BatchCheckPic(checkParams []CheckPostContent, accountInfo AccountInfo, sceneId int64) (checkPicResp CheckPicResp, err error) {
	postDataStr := getPicReqBodyStr(checkParams, accountInfo, sceneId)
	configModel := config.GetConfig()
	secretId := configModel.Security.SecretId
	secretKey := configModel.Security.SecretKey
	queryParam := ""
	// 生成签名，调用intlsdk
	gmtDate := getFormatNow()
	uri := "/check/syn/batch_picture"
	requestURL := fmt.Sprintf("%s%s", configModel.Security.Host, uri)
	signatureStr := getSignatureString(secretId, secretKey, uri, gmtDate, queryParam, postDataStr)

	optionOne := httpclient.ClientOption{
		URL:     requestURL,
		Type:    "POST",
		Timeout: 300 * time.Second,
		Header: map[string]string{
			"Content-Type":     "application/json",
			"X-HMAC-SIGNATURE": signatureStr,
			"X-HMAC-SECRET-ID": secretId,
			"X-HMAC-ALGORITHM": "hmac-sha256",
			"Date":             gmtDate,
		},
		PostString: postDataStr,
	}

	// 发请求
	resultOption := httpclient.RequestOne(optionOne)
	// 结果判断
	if resultOption.RequestError != nil {
		err = resultOption.RequestError
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("BatchCheckPic httpclient.RequestOne error:%v", err)
		return
	}
	apiResultString := resultOption.Result
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("PushPostToSecurityDetection security.BatchCheckPic return result: %v", apiResultString)
	err = json.Unmarshal([]byte(apiResultString), &checkPicResp)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("BatchCheckPic json.Unmarshal error:%v", err)
		return
	}
	return
}

func getTextReqBodyStr(checkParams []CheckPostContent, accountInfo AccountInfo, sceneId int64) string {
	req_body := &CheckTextParam{}
	req_body.Tasks = make([]CheckTextDataItem, 0)
	for _, v := range checkParams {
		dataIdS := fmt.Sprintf("%s-%d", "Text", (v.DataId))
		checkTextDataItem := &CheckTextDataItem{
			SceneId:      sceneId,
			Text:         v.Content,
			Title:        v.Title,
			Introduction: v.Introduction,
			DataId:       dataIdS,
			Account: AccountInfo{
				Account:  accountInfo.Account,
				PlatId:   accountInfo.PlatId,
				RoleName: accountInfo.RoleName,
			},
		}
		req_body.Tasks = append(req_body.Tasks, *checkTextDataItem)
	}

	req_body_data, _ := json.Marshal(&req_body)
	return string(req_body_data)
}

func getPicReqBodyStr(checkParams []CheckPostContent, accountInfo AccountInfo, sceneId int64) string {
	req_body := &CheckPicParam{}
	req_body.Tasks = make([]CheckPicDataItem, 0)
	for _, v := range checkParams {
		dataIdS := fmt.Sprintf("%s-%d", "Pic", (v.DataId))
		checkPicDataItem := &CheckPicDataItem{
			SceneId: sceneId,
			Url:     v.Content,
			PicType: v.PicType,
			DataId:  dataIdS,
			Account: AccountInfo{
				Account:  accountInfo.Account,
				PlatId:   accountInfo.PlatId,
				RoleName: accountInfo.RoleName,
			},
		}
		req_body.Tasks = append(req_body.Tasks, *checkPicDataItem)
	}

	req_body_data, _ := json.Marshal(&req_body)
	return string(req_body_data)
}

// 没有query_string,使用空字符串
func get_canonical_query_string(query_param string) string {
	return ""
}

func getBodySign(body string) string {
	h := sha256.New()
	h.Write([]byte(body))
	res := hex.EncodeToString(h.Sum(nil))
	return res
}

func getSignatureString(secret_id string,
	secret_key string,
	uri string,
	gmt_date string,
	query_param string,
	body string) string {

	canonical_query_string := get_canonical_query_string(query_param)
	body_sign := getBodySign(body)
	// fmt.Println("body: " + body)
	// fmt.Println("body_sign: " + body_sign)
	res := "POST\n"
	res = res + uri + "\n"
	res = res + canonical_query_string + "\n"
	res = res + secret_id + "\n"
	res = res + gmt_date + "\n"
	res = res + body_sign + "\n"

	// fmt.Println("res: " + res)
	hmac := hmac.New(sha256.New, []byte(secret_key))
	hmac.Write([]byte(res))

	hash_result := base64.StdEncoding.EncodeToString(hmac.Sum(nil))
	// fmt.Println("res: ", res)
	// fmt.Println("hash_result: " + hash_result)
	return hash_result

}

func getFormatNow() string {
	now := time.Now().UTC()
	strNow := now.Format("Mon, 02 Jan 2006 15:04:05 GMT")
	return strNow
}

// PushTextToSecurityDetection 对发布的文字安全审核
func PushTextToSecurityDetection(content, title, introduction string, dataId, sceneId int64, accountInfo AccountInfo) (riskLevel, riskType int) {
	defer func() {
		if err := recover(); err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushTextToSecurityDetection panic err: %v", err)
		}
	}()
	riskLevel = 1
	riskType = 100
	if content == "" {
		return
	}
	contentStrLen := utf8.RuneCountInString(content)
	var contentStrLenSplitCount int
	if contentStrLen%3000 == 0 {
		contentStrLenSplitCount = contentStrLen / 3000
	} else {
		contentStrLenSplitCount = (contentStrLen / 3000) + 1
	}

	// 1.先检查内容文字安全，一次检查3000字，分段检测
	var checkTextParams []CheckPostContent
	start := 0
	end := 3000
	for i := 1; i <= contentStrLenSplitCount; i++ {
		if i == contentStrLenSplitCount {
			end = contentStrLen
		}
		contentSplitStr := string([]rune(content)[start:end])
		// 先获取动态的标题、内容、话题，合成一个文本机审请求
		checkTextParam := CheckPostContent{
			Content:      contentSplitStr,
			Title:        title,
			Introduction: introduction,
			DataId:       dataId,
		}
		checkTextParams = append(checkTextParams, checkTextParam)
		start += 3000
		end += 3000
	}
	// 不能超过10个
	// if len(checkTextParams) > 10 {
	// 	checkTextParams = checkTextParams[:10]
	// }
	checkTextResp, err := BatchCheckText(checkTextParams, accountInfo, sceneId)

	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushTextToSecurityDetection security.BatchCheckText err: %v", err)
		riskLevel = 4
		return
	}
	if checkTextResp.ErrCode != 0 || len(checkTextResp.Data) == 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushTextToSecurityDetection security.BatchCheckText return err: %v", checkTextResp)
		riskLevel = 4
		return
	}
	for _, checkTextResultData := range checkTextResp.Data {
		riskLevel = checkTextResultData.TextCheckResult.CheckResult + 1
		riskType = checkTextResultData.TextCheckResult.Label
		if riskLevel != 1 {
			break
		}
	}

	return
}

// PushPicToSecurityDetection 对发布的图片安全审核
func PushPicToSecurityDetection(picUrls []string, dataId, sceneId int64, accountInfo AccountInfo) (riskLevel, riskType int) {
	defer func() {
		if err := recover(); err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushPicToSecurityDetection panic err: %v", err)
		}
	}()

	riskLevel = 1
	riskType = 100
	if len(picUrls) > 0 {
		var checkPicParams []CheckPostContent
		for _, picUrl := range picUrls {
			checkPicParam := CheckPostContent{
				Content: picUrl,
				PicType: 2,
				DataId:  dataId,
			}
			checkPicParams = append(checkPicParams, checkPicParam)
		}
		// 图片检测接口，一次只能检测10张
		batchSize := 10
		for i := 0; i < len(checkPicParams); i += batchSize {
			picEnd := i + batchSize
			if picEnd > len(checkPicParams) {
				picEnd = len(checkPicParams)
			}
			checkPicSubParams := checkPicParams[i:picEnd]
			checkPicResp, err := BatchCheckPic(checkPicSubParams, accountInfo, sceneId)

			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushPicToSecurityDetection security.BatchCheckPic err: %v", err)
				riskLevel = 4
				return
			}
			if checkPicResp.ErrCode != 0 || len(checkPicResp.Data) == 0 {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushPicToSecurityDetection security.BatchCheckPic return err: %v", checkPicResp)
				riskLevel = 4
				return
			}
			for _, checkTextResultData := range checkPicResp.Data {
				if checkTextResultData.Label != 100 {
					riskType = checkTextResultData.Label
					riskLevel = 2
					return
				}
			}
		}
	}
	return
}
