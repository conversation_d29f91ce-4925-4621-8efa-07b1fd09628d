package team

import "trpc.act.logicial/app/model"

type TeamCodeConfModel struct {
	model.AppModel
}

// TableName .
func (TeamCodeConfModel) TableName() string {
	return "team_code_conf"
}

// TeamCodeConf 临时结构
type TeamCodeConf struct {
	TeamCodeConfModel
	ID        int64  `gorm:"type:int(11);column:id;primary_key"`
	FsourceId string `gorm:"type:varchar(255);column:Fsource_id;"`
}
type TeamAccountCodeModel struct {
	model.AppModel
}

// TableName .
func (TeamAccountCodeModel) TableName() string {
	return "team_account_code"
}

// TeamAccountCode 临时结构
type TeamAccountCode struct {
	TeamAccountCodeModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	FsourceId   string `gorm:"type:varchar(255);column:Fsource_id;"`
	Uid         string `gorm:"type:varchar(255);column:uid;"`
	TeamCode    string `gorm:"type:varchar(255);column:team_code;"`
	IsDelete    int    `gorm:"type:int(1);column:is_delete;default:0"`
	AccountType int    `gorm:"type:int(4);column:account_type;"`
}
type TeamCodeLogModel struct {
	model.AppModel
}

// TableName .
func (TeamCodeLogModel) TableName() string {
	return "team_code_log"
}

// TeamCodeLog 临时结构
type TeamCodeLog struct {
	TeamCodeLogModel
	ID                 int64  `gorm:"type:int(11);column:id;primary_key"`
	FsourceId          string `gorm:"type:varchar(255);column:Fsource_id;"`
	InviteeUid         string `gorm:"type:varchar(255);column:invitee_uid;"`
	TeamCode           string `gorm:"type:varchar(255);column:team_code;"`
	IsDelete           int    `gorm:"type:int(1);column:is_delete;default:0"`
	InviteeAccountType int    `gorm:"type:int(4);column:invitee_account_type;"`
}
