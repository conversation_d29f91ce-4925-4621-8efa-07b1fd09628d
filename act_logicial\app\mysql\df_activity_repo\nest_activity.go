package df_activity_repo

import (
	"context"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/log"
	model "trpc.act.logicial/app/model/df_activity"
)

var NestActivityRepoClient = NestActivityRepo{}

type NestActivityRepo struct {
}

// AddUserBirdNestReward 新增用户掏鸟窝活动抽奖信息
func (p NestActivityRepo) AddUserBirdNestReward(ctx context.Context,
	record *model.UserBirdNestReward) error {
	err := DB.DefaultConnect().WithContext(ctx).Create(record).Error
	if err != nil {
		log.ErrorContextf(ctx, "AddUserBirdNestReward error: %v, record:%+v", err, record)
		return err
	}
	return nil
}

// GetUserBirdNestReward 获取用户掏鸟窝活动抽奖信息
func (p NestActivityRepo) GetUserBirdNestReward(ctx context.Context,
	userID string) (*model.UserBirdNestReward, error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&model.UserBirdNestReward{}).
		Where("user_id = ?", userID)

	var record model.UserBirdNestReward
	err := query.First(&record).Error
	if err != nil {
		return nil, err
	}

	return &record, nil
}
