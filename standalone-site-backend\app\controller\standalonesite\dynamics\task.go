package dynamics

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/logic/task"
	"trpc.publishing_application.standalonesite/app/pkg/metadatadecode"
)

// 获取关注任务官方账号列表
func (s *DynamicsImpl) GetFollowTaskOfficialAccounts(ctx context.Context, req *pb.GetFollowTaskOfficialAccountsReq) (*pb.GetFollowTaskOfficialAccountsRsp, error) {
	rsp := &pb.GetFollowTaskOfficialAccountsRsp{}
	userAccount, _ := metadata.GetUserAccount(ctx)
	myOpenid := userAccount.Uid
	language := metadata.GetLangType(ctx)
	if language == "" {
		language = "en"
	}
	users, err := task.GetFollowTaskOfficialAccounts(ctx, myOpenid, language)
	if err != nil {
		return rsp, err
	}
	rsp.Users = users
	return rsp, nil
}

// 一键关注
func (s *DynamicsImpl) QuicklyFollowAllOfficialAccounts(ctx context.Context, req *pb.QuicklyFollowAllOfficialAccountsReq) (*pb.QuicklyFollowAllOfficialAccountsRsp, error) {
	rsp := &pb.QuicklyFollowAllOfficialAccountsRsp{}
	userAccount, _ := metadata.GetUserAccount(ctx)
	myOpenid := userAccount.Uid
	gameId, areaId := metadatadecode.GetGameIdAndAreaId(ctx)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}
	err := task.QuicklyFollowAllOfficialAccounts(ctx, myOpenid, gameId, areaId)
	return rsp, err
}

// 是否关注官方账号
func (s *DynamicsImpl) HasFollowedOfficialAccounts(ctx context.Context, req *pb.HasFollowedOfficialAccountsReq) (*pb.HasFollowedOfficialAccountsRsp, error) {
	rsp := &pb.HasFollowedOfficialAccountsRsp{}
	userAccount, _ := metadata.GetUserAccount(ctx)
	myOpenid := userAccount.Uid
	err := task.HasFollowedOfficialAccounts(ctx, myOpenid)
	return rsp, err
}
