package httpclient

// 提供并发的http请求

import (
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"reflect"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/util"

	"encoding/json"
)

// ClientOption 请求的接口
type ClientOption struct {
	// URL 地址
	URL string
	// Type 请求方式，默认get
	Type string
	// Timeout 超时时间, 默认2秒
	Timeout time.Duration
	// PostData post的内容，map格式
	PostData map[string]interface{}
	// PostString post的内容，string格式
	PostString string
	// Header 请求的header配置
	Header map[string]string
	// Cookie 直观的cookie
	Cookie map[string]string
	// Result 结果
	Result string
	// RequestError 请求是否有错误
	RequestError error
	// Host 设置host 不能卸载header里面
	Host string
	// 请求耗时
	TimeCost string
	// RespHeader 请求返回的header
	RespHeader http.Header
}

// 默认的请求参数
var defaultOption = ClientOption{
	Type:    "GET",
	Timeout: 3 * time.Second,
	Header: map[string]string{
		"User-Agent": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.130 Safari/537.36 Go-http-client/1.1 Opdev-Service",
	},
}

// var devPostBody = map[string]interface{}{}

// doRequest 发起一个请求
func doRequest(option ClientOption) ([]byte, http.Header, error) {
	// defer wg.Done()

	//默认参数
	requestOption := defaultOption
	value := reflect.ValueOf(option)
	v1Elem := reflect.ValueOf(&requestOption).Elem()
	// 遍历结构体
	for i := 0; i < value.NumField(); i++ {
		v := value.Field(i)
		if !reflect.DeepEqual(v.Interface(), reflect.Zero(v.Type()).Interface()) {
			//不为空的字段，直接复制
			v1Elem.Field(i).Set(v)
			//todo map合并，而不是直接替换
		}
	}
	//转成大写
	requestOption.Type = strings.ToUpper(requestOption.Type)

	//兼容误将HOST写入header中
	if requestOption.Host == "" {
		host, ok := requestOption.Header["Host"]
		if ok && host != "" {
			requestOption.Host = host
		}
	}

	//设定超时时间
	client := &http.Client{
		Timeout: requestOption.Timeout,
	}

	var reqest *http.Request
	var err error
	if requestOption.Type == "GET" {
		reqest, err = http.NewRequest("GET", requestOption.URL, nil)
	} else if requestOption.Type == "POST" {
		var postBodyStr string
		if requestOption.PostString != "" {
			postBodyStr = requestOption.PostString
		} else if requestOption.PostData != nil {
			// 是否是json类型
			isTypeJSON := false
			for key, item := range requestOption.Header {
				if strings.ToLower(key) == "content-type" && strings.ToLower(item) == "application/json" {
					isTypeJSON = true
					break
				}
			}
			//判断header application/json
			if isTypeJSON {
				value, _ := json.Marshal(requestOption.PostData)
				postBodyStr = string(value)
			} else {
				//否则按 application/x-www-form-urlencoded 处理
				postBodyStr = util.ParamMapToStr(requestOption.PostData)
			}
			// 待测试 json
		}
		reqest, err = http.NewRequest("POST", requestOption.URL, strings.NewReader(postBodyStr))
	} else {
		return nil, nil, errors.New("requestOption.Type no allow")
	}
	if err != nil {
		return nil, nil, err
	}
	//添加header
	for key, value := range requestOption.Header {
		// panic("i aim painc  doRequest ")
		reqest.Header.Add(key, value)
	}

	if requestOption.Host != "" {

		reqest.Host = requestOption.Host

	}

	//添加cookie
	var cookieString []string
	var cookie string
	for key, value := range requestOption.Cookie {
		cookieString = append(cookieString, key+"="+value)
	}
	cookie = strings.Join(cookieString, ";")
	if cookie != "" {
		reqest.Header.Add("Cookie", cookie)
	}

	//处理返回结果
	startT := time.Now() //计算当前时间
	response, err := client.Do(reqest)
	if err == nil {
		defer response.Body.Close()
	}
	tc := time.Since(startT) //计算耗时
	requestOption.TimeCost = fmt.Sprintf("%v", tc)
	if response == nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("error_code %v, http_url: %s", code.HTTPResponseEmptyError, requestOption.URL)
		return nil, nil, errors.New("Http response is empty")
	}
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("error_code %v, http_url: %s, http_code = %v, http request error = %v", code.HTTPResponseError, requestOption.URL, response.StatusCode, err)
		return nil, nil, err
	}
	if response.StatusCode != http.StatusOK {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("error_code %v, http_url: %s, http_code = %v, http request error = %v", code.HTTPResponseCodeError, requestOption.URL, response.StatusCode, err)
	}
	body, err := ioutil.ReadAll(response.Body)

	// 添加http日志
	requestOption.Result = string(body)
	requestOption.RequestError = err

	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("http response body error = %v", err)
		return nil, nil, err
	}

	//todo 如果启动L5，要上报结果
	return body, response.Header, nil
}

// Request 并发的请求, 请求的结果，会追加到 Result 字段
func Request(options []ClientOption) []ClientOption {

	var wg sync.WaitGroup
	//添加header

	wg.Add(len(options))

	for index := 0; index < len(options); index++ {
		// wg.Add(1)
		go func(option *ClientOption, wg *sync.WaitGroup) {
			defer wg.Done()
			defer recovery.CatchGoroutinePanic(context.Background())
			// logrus.Debugf("http request start at %v", time.Now())
			body, respHeader, err := doRequest(*option)

			if err != nil {
				// 添加log	//todo
				fmt.Println(err)
			} else {
				option.Result = string(body)
			}
			option.RequestError = err
			option.RespHeader = respHeader
		}(&options[index], &wg)
	}

	wg.Wait()
	return options
}

// RequestOne 发起一个请求， 请求的结果，会追加到 Result 字段
func RequestOne(option ClientOption) (resultOption ClientOption) {
	resultOption = option
	resultOption.RequestError = errors.New("Do not request")
	result := Request([]ClientOption{option})

	//结果
	for _, option := range result {
		resultOption = option
		return
	}
	return
}
