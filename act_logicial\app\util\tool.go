package util

import (
	"context"
	"github.com/go-redis/redis/v8"
	"time"
)

// RedisRateLimiter 是一个基于 Redis 的限频器
type RedisRateLimiter struct {
	client   *redis.Client
	rate     int           // 允许的最大请求数
	interval time.Duration // 时间窗口
}

// NewRedisRateLimiter 创建一个新的 RedisRateLimiter
func NewRedisRateLimiter(client *redis.Client, rate int, interval time.Duration) *RedisRateLimiter {
	return &RedisRateLimiter{
		client:   client,
		rate:     rate,
		interval: interval,
	}
}

// Allow 检查是否允许请求
func (r *RedisRateLimiter) Allow(ctx context.Context, key string) (bool, error) {
	// 使用 Lua 脚本实现原子操作
	script := redis.NewScript(`
        local current
        current = redis.call("INCR", KEYS[1])
        if tonumber(current) == 1 then
            redis.call("EXPIRE", KEYS[1], ARGV[1])
        end
        if tonumber(current) > tonumber(ARGV[2]) then
            return 0
        end
        return 1
    `)

	// 执行 Lua 脚本
	result, err := script.Run(ctx, r.client, []string{key}, int(r.interval.Seconds()), r.rate).Result()
	if err != nil {
		return false, err
	}

	return result.(int64) == 1, nil
}
