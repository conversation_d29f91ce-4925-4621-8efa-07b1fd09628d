package redis

import "fmt"

const (
	// 服务数据key前缀
	dataPrefix = "act_logical_svr_data"
)

const (
	// gunActJoinLockKey 拼枪活动用户参与活动锁key
	gunActJoinLockKey = "gun_act_join_lock_key"
	// gunActTaskHelpLockKey 拼枪活动用户助力锁key
	gunActTaskHelpLockKey = "gun_act_task_help_lock_key"
	// gunActClaimPrizeLockKey 拼枪活动用户领取奖励锁key
	gunActClaimPrizeLockKey = "gun_act_claim_prize_lock_key"
	// gunActSendPrizeLockKey 拼枪活动用户发放奖励锁key
	gunActSendPrizeLockKey = "gun_act_send_prize_lock_key"
	// gunActCheckTaskLockKey 拼枪活动任务定时检查锁key
	gunActCheckTaskLockKey = "check_task_lock"

	// cag活动相关
	// CagGetCreateRolePrizeLock  cag获取创建角色奖励锁key
	CagGetCreateRolePrizeLock = "cag_get_create_role_prize_lock"
	// CagGetRoleLevelPrizeLock  cag获取角色等级奖励锁key
	CagGetRoleLevelPrizeLock = "cag_get_role_level_prize_lock"
	// CagSendRoleLevelPrizeLock  cag发放角色等级奖励锁key
	CagSendRoleLevelPrizeLock = "cag_send_role_level_prize_lock"
	// CagRoleLevelPrizeSendCheckLock cag角色等级奖励发送检查锁key
	CagRoleLevelPrizeSendCheckLock = "cag_role_level_prize_send_check_lock"
)

// GetUserJoinGunActLockKey 获取拼枪活动用户参与活动锁key
func GetUserJoinGunActLockKey(gameID, userID string) string {
	return fmt.Sprintf("%s/%s_%s_%s", dataPrefix, gunActJoinLockKey, gameID, userID)
}

// GetGunActTaskHelpLockKey 获取拼枪活动用户助力锁key
func GetGunActTaskHelpLockKey(gameID, userID string) string {
	return fmt.Sprintf("%s/%s_%s_%s", dataPrefix, gunActTaskHelpLockKey, gameID, userID)
}

// GetGunActClaimPrizeLockKey 获取拼枪活动用户领取奖励锁key
func GetGunActClaimPrizeLockKey(userID string) string {
	return fmt.Sprintf("%s/%s_%s", dataPrefix, gunActClaimPrizeLockKey, userID)
}

// GetGunActSendPrizeLockKey 获取拼枪活动用户发送奖励锁key
func GetGunActSendPrizeLockKey(taskId string) string {
	return fmt.Sprintf("%s/%s_%s", dataPrefix, gunActSendPrizeLockKey, taskId)
}

// GetGunActCheckTaskLockKey 获取拼枪活动任务定时检查锁key
func GetGunActCheckTaskLockKey() string {
	return fmt.Sprintf("%s/%s", dataPrefix, gunActCheckTaskLockKey)
}

// GetCagCreateRolePrizeLockKey 获取cag创建角色奖励锁key
func GetCagCreateRolePrizeLockKey(userId string) string {
	return fmt.Sprintf("%s/%s_%s", dataPrefix, CagGetCreateRolePrizeLock, userId)
}

// GetCagGetRoleLevelPrizeLockKey 获取cag角色等级奖励锁key
func GetCagGetRoleLevelPrizeLockKey(userId string) string {
	return fmt.Sprintf("%s/%s_%s", dataPrefix, CagGetRoleLevelPrizeLock, userId)
}

// GetCagSendRoleLevelPrizeLockKey 获取发放cag角色等级奖励锁key
func GetCagSendRoleLevelPrizeLockKey(userId, amsSerial string) string {
	return fmt.Sprintf("%s/%s_%s_%s", dataPrefix, CagSendRoleLevelPrizeLock, userId, amsSerial)
}

// GetCagRoleLevelPrizeSendCheckLockKey 获取cag角色等级奖励发送检查锁key
func GetCagRoleLevelPrizeSendCheckLockKey() string {
	return fmt.Sprintf("%s/%s", dataPrefix, CagRoleLevelPrizeSendCheckLock)
}
