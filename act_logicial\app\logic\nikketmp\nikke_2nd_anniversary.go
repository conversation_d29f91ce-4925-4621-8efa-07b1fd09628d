package nikketmp

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_2nd_anniversary"
	"google.golang.org/protobuf/proto"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/controller/lottery"
	AnniversaryModel "trpc.act.logicial/app/model/nikke_2nd_anniversary"
)

var actid = "h5-02"
var dimid = "10001"
var gameIDMap = map[string]string{
	"29080": "nikke",     // 全球
	"29157": "nikke_hmt", // 港澳台
}
var channelIDMap = map[string]string{
	"29080": "82",  // 全球
	"29157": "129", // 港澳台
}

// UserInfo TODO
type UserInfo struct {
	Izoneareaid string `json:"izoneareaid"`
	Vopenid     string `json:"vopenid"`
	Vroleid     string `json:"vroleid"`
}

// DimensionInfo dimensino请求参数
type DimensionInfo struct {
	Interval    string   `json:"interval"`
	EndInterval int      `json:"endInterval"`
	IntervalAgo int      `json:"intervalAgo"`
	Metrics     []string `json:"metrics"`
	Cmds        []string `json:"cmds"`
}

// GetDimensionsURL 获取dimensions的请求链接
func GetDimensionsURL(ctx context.Context, gameID string, areaID int64, postData map[string]interface{}) (
	url string, err error,
) {
	channelID := channelIDMap[gameID]
	ts := time.Now().Unix()

	seq := actid + dimid + gameIDMap[gameID] + strconv.FormatInt(ts, 10)
	postDataJSONString, err := json.Marshal(postData)
	sigStr := fmt.Sprintf(
		"%v%v%v%v%v%v%v%v%v",
		"/data/v1/general/dimensions?",
		"channelid="+channelID,
		"&gameid="+gameID,
		"&os=0",
		"&seq="+seq,
		"&source=0",
		"&timezone=0",
		"&ts="+strconv.FormatInt(ts, 10),
		"&version=1.0",
	)
	sinKey := map[string]string{
		"29080": "3d0ef5272bf6bc22fd484c18d96be5c0",
		"29157": "c8960de288716bf11e450e8b1319656d",
	}
	sig := lottery.GetMD5Encode(
		sigStr +
			string(postDataJSONString) +
			sinKey[gameID],
	)
	url = fmt.Sprintf(
		"%v%v%v",
		config.GetConfig().NikkeDataHost,
		sigStr,
		"&sig="+sig,
	)
	return
}

// GetDimensionsPostData 获取Dimensions post请求参数
func GetDimensionsPostData(ctx context.Context, gameID string, areaID int64) (
	data map[string]interface{}, err error,
) {

	userAccount, err := metadata.GetUserAccount(ctx)

	if err != nil {
		return
	}

	metricsReqArr := make([]string, 24)
	cmdsArr := make([]string, 24)

	for i := 1; i <= 24; i++ {
		metricsReqArr[i-1] = "c" + strconv.Itoa(i)
		cmdsArr[i-1] = ""
	}

	data = map[string]interface{}{
		"actid":    actid,
		"gameName": gameIDMap[gameID],
		"users": []UserInfo{
			{
				Izoneareaid: strconv.FormatInt(areaID, 10),
				Vroleid:     "0",
				// Vopenid:     "268130863772397056",
				Vopenid: userAccount.IntlAccount.OpenId,
			},
		},
		"dimensions": map[string]DimensionInfo{
			dimid: {
				Interval:    "now", // 1. "now"（代表最新数据，没有时间跨度）, 2. "day"（读取特定天数的数据）
				EndInterval: 0,
				IntervalAgo: 0,
				Metrics:     metricsReqArr,
				Cmds:        cmdsArr,
			},
		},
	}
	return
}

// GetHavePresentTag 获取已经拿到的奖励
func GetHavePresentTag(ctx context.Context, fsourceId string) (tags []string, err error) {
	tags = make([]string, 0)
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	var nikkeMbtiAddData []AnniversaryModel.NikkeTwoAnniversary

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
		"Fsource_id":   fsourceId,
	}

	db := DB.DefaultConnect().WithContext(ctx).Where(condition).Find(&nikkeMbtiAddData)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return
	}

	for _, item := range nikkeMbtiAddData {
		tags = append(tags, item.Tag)
	}
	return
}

// TwoThAddSendLog 添加发奖记录
func TwoThAddSendLog(ctx context.Context, req *pb.TwoThAddSendLogReq) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var nikkeMbtiAddData AnniversaryModel.NikkeTwoAnniversary

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
		"tag":          req.Tag,
		"Fsource_id":   req.FsourceId,
	}

	db := DB.DefaultConnect().WithContext(ctx).Where(condition).Attrs(&AnniversaryModel.NikkeTwoAnniversary{
		LangType: req.LangType,
	}).FirstOrCreate(&nikkeMbtiAddData)
	if db.Error != nil {
		// 检查 err 是否为重复插入错误
		if isNotDuplicateInsertError(db.Error) {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
	}
	log.WithFieldsContext(ctx, "log_type", "TwoThAddSendLog", "str_field_1", req.Tag).
		Infof("TwoThAddSendLogAddSendLog")
	return
}

// TwoThScheduledSend nikke2周年发奖
func TwoThScheduledSend(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	tableName := AnniversaryModel.NikkeTwoAnniversary{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(AnniversaryModel.NikkeTwoAnniversary{}.TableName()).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页50条
	pageSize := 50
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	gameProxy := gamePb.NewGameClientProxy()
	var lastId int64
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		// offset := (pageNumber - 1) * pageSize
		var logData = []AnniversaryModel.NikkeTwoAnniversary{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Where(condition).Where("id > ?", lastId).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", logData))
		if len(logData) == 0 {
			break
		}
		lastId = logData[len(logData)-1].ID
		for _, v := range logData {
			wg.Add(1)
			go func(data AnniversaryModel.NikkeTwoAnniversary) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(string(accountData))

				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				// 获取角色信息
				gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: data.FsourceID}

				gameRoleInfo, err := gameProxy.GetSavedRoleInfo(newCtx, gameReq, callopts...)
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("gameRoleInfo：%v", gameRoleInfo))

				if err == nil {
					log.WithFieldsContext(scheduleCtx, "ScheduledSend log", "debug").Infof(fmt.Sprintf("v: %#v", data))
					// 发送礼包
					sendReq := &presentPb.SendPresentReq{
						FsourceId: data.FsourceID,
						PresentId: data.Tag,
						RoleInfo:  gameRoleInfo,
					}
					accountData, _ = proto.Marshal(&accountPb.UserAccount{
						Uid:         data.UID,
						AccountType: accountPb.AccountType(data.AccountType),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							GameId:    gameRoleInfo.GameId,
							ChannelId: 3,
						},
					})
					callopts = []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
						client.WithMetaData(metadata.LangType, []byte(data.LangType)),
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
					}
					delErr := errs.ParseError(ctx, sendErr)

					if sendErr != nil || delErr.Code == 400018 || delErr.Code == 400021 {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err111:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						// if strings.Contains(sendErr.Error(), "package limit left not enough") {
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// }
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err:%v", sendErr))
					// 修改发货状态
				} else {
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("GetRoleInfo] service err:%v", err))

				}
				wg.Done()
			}(v)

		}
		wg.Wait()

	}
	return
}
