package hoktmp

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	deltaversePb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/deltaverse"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/object"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/publishing_marketing/game"
	gameHokPb "git.woa.com/trpcprotocol/publishing_marketing/game_hok"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_hok_tmp"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	baseLogic "trpc.act.logicial/app/logic/base"
	"trpc.act.logicial/app/model/hok"
)

// RecordCycleTaskRewardClaimed 记录周期任务已领取礼包
func RecordCycleTaskRewardClaimed(ctx context.Context, presentId string) error {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	// log.WithFieldsContext(ctx, "log_type", "debug").Infof(
	//	"RecordCycleTaskRewardClaimed show uid:[%v],presentId:[%v]", userAccount.Uid, presentId)
	periodicGiftClaim := hok.HokPeriodicGiftClaim{
		UID:         userAccount.Uid,
		AccountType: int(userAccount.AccountType),
		PresentID:   presentId,
	}
	if err = DB.DefaultConnect().WithContext(ctx).Table(hok.HokPeriodicGiftClaimModel{}.TableName()).
		Create(&periodicGiftClaim).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RecordCycleTaskRewardClaimed FirstOrCreate db error, \t [Error]:{%v} ", err)
	}
	return nil
}

// GetClaimedCycleTaskRewards 查询周期任务已领取礼包列表
func GetClaimedCycleTaskRewards(ctx context.Context, timeList []int64) ([]string, error) {

	if len(timeList) < 2 {
		return nil, errs.NewCustomError(ctx, code.CommonDataError, "Periodic Time List len err")
	}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	// 格式化时间
	starTime, endTime := baseLogic.GetStarAndEndTime(timeList)

	var presentIds []string
	sel := "present_id"
	db := DB.DefaultConnect().WithContext(ctx).Table(hok.HokPeriodicGiftClaimModel{}.TableName()).Select(sel).
		Where(hok.HokPeriodicGiftClaim{
			UID:         userAccount.Uid,
			AccountType: int(userAccount.AccountType),
		})
	if starTime != 0 && endTime != 0 {
		db = db.Where("created_at > ? and created_at <= ?", starTime, endTime)
	}
	if starTime == 0 {
		db = db.Where("created_at <= ?", endTime)
	}
	if endTime == 0 {
		db = db.Where("created_at > ?", starTime)
	}
	if err = db.Find(&presentIds).Error; err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"GetClaimedCycleTaskRewards FirstOrCreate db error, \t [Error]:{%v} ", err)
	}
	return presentIds, nil
}

// IsCurrentCycleRewardClaimed 判断当前周期礼包是否已领取
func IsCurrentCycleRewardClaimed(ctx context.Context, timeList []int64, presentId string) error {

	taskRewards, err := GetClaimedCycleTaskRewards(ctx, timeList)
	if err != nil {
		return err
	}

	if ok, _ := object.InArray(presentId, taskRewards); ok {
		return errs.NewCustomError(ctx, code.PresentHasBeenCollected, "The present package has been collected")
	}
	return nil
}

// HOKRecordUserActivityV2 记录参加活动的用户
func HOKRecordUserActivityV2(ctx context.Context) error {

	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	langType := metadata.GetLangType(ctx)

	// 记录参与活动的用户
	var userParticipation hok.HokClubUserParticipationV2
	if err = DB.DefaultConnect().WithContext(ctx).Where(hok.HokClubUserParticipationV2{
		UID:         userAccount.Uid,
		AccountType: int(userAccount.AccountType),
	}).Assign(hok.HokClubUserParticipationV2{
		LangType: langType,
	}).FirstOrCreate(&userParticipation).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"HOKRecordUserActivityV2 FirstOrCreate db error, \t [Error]:{%v} ", err)
	}
	return nil
}

// GetUserHokHotRankList 获取用户排行榜数据
func GetUserHokHotRankList(ctx context.Context) (*pb.GetUserHokHotRankListRsp, error) {

	rsp := &pb.GetUserHokHotRankListRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	proxy := gameHokPb.NewHokClientProxy()
	// 获取缓存数据
	cacheKey := getHokRankCacheKey()
	result, err := redis.GetClient().HGetAll(ctx, cacheKey).Result()
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
			"GetUserHokHotRankList get cache err: cacheKey:[%v],err:[%v]", cacheKey, err))
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}

	userRankInfo := &pb.RankInfo{}
	rankList := make([]*pb.RankInfo, 0, len(result))
	if userRankStr, ok := result[userAccount.IntlAccount.OpenId]; !ok {
		// 不在排行榜中获取用户数据
		hokEventsClub := config.GetConfig().HOKEventsClub
		envName := hokEventsClub.Env
		areaId, zoneId, platId := getAreaItemByEnv(envName)
		req := &game.GetRoleInfoReq{
			GameId:    HokGameId,
			AreaId:    areaId,
			ZoneId:    zoneId,
			PlatId:    platId,
			Partition: 0,
		}
		var moreRoleInfo *gameHokPb.HOKMoreRoleInfo
		var heatValueStr string
		var heatValue float32
		moreRoleInfo, err = proxy.GetHOKMoreRoleInfo(ctx, req)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", "debug").Infof(
				"GetUserHokHotRankList GetHOKMoreRoleInfo req:[%v], err:[%v]", req, err)
			return nil, err
		}
		// 获取用户热度值
		heatValueStr, err = GetSelfHeatValue(ctx, userAccount.IntlAccount.OpenId)
		if err != nil {
			return nil, err
		}
		if heatValueStr != "" {
			heatValue, err = cast.ToFloat32E(heatValueStr)
			if err != nil {
				return nil, err
			}
		}
		headUrl := getHokHeadUrl(moreRoleInfo.HeadUrl)
		userRankInfo.HeadUrl = headUrl
		userRankInfo.Name = decodeURIComponent(moreRoleInfo.RoleInfo.RoleName)
		userRankInfo.HeatValue = heatValue
		if len(result) == 0 {
			log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
				"Hok Hot Rank List Not Data: cacheKey:[%v],UID:[%v]", cacheKey, userAccount.Uid))
			errs.NewCustomError(ctx, code.HokHotRankListNotData, "Hok Hot Rank List Not Data")
			return &pb.GetUserHokHotRankListRsp{
				UserRank: userRankInfo,
			}, nil
		}
	} else {
		var userRankItem HokRankItem
		if err = json.Unmarshal([]byte(userRankStr), &userRankItem); err != nil {
			// 解析失败
			err = errs.NewCustomError(ctx, code.CommonParamJsonError,
				"GetUserHokHotRankList userRankItem json.Unmarshal error, userRankStr = %v, error:%v", userRankStr, err)
			return nil, err
		}
		userRankInfo.Rank = userRankItem.Rank
		userRankInfo.HeadUrl = userRankItem.HeadUrl
		userRankInfo.Name = userRankItem.Name
		userRankInfo.HeatValue = float32(userRankItem.HeatValue)
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
		"GetUserHokHotRankList show result: result:[%v]", result))
	for _, v := range result {
		var userRankItem HokRankItem
		if err = json.Unmarshal([]byte(v), &userRankItem); err != nil {
			// 解析失败
			log.WithFieldsContext(ctx, "log_type", "debug").Infof(
				"GetUserHokHotRankList range result json.Unmarshal error, result:[%v], error:%v", v, err)
			// err = errs.NewCustomError(ctx, code.CommonParamJsonError,
			//	"range result json.Unmarshal error,error:%v", err)
			continue
		}
		rankList = append(rankList, &pb.RankInfo{
			Rank:      userRankItem.Rank,
			Name:      userRankItem.Name,
			HeadUrl:   userRankItem.HeadUrl,
			HeatValue: float32(userRankItem.HeatValue),
		})
	}
	rsp.UserRank = userRankInfo
	rsp.RankList = rankList
	return rsp, nil
}

// GetSelfHeatValue TODO
func GetSelfHeatValue(ctx context.Context, openId string) (string, error) {

	hokEventsClub := config.GetConfig().HOKEventsClub
	envName := hokEventsClub.Env
	var env int
	if envName == "dev" {
		env = 1
	}
	paramMap := make(map[string]interface{})
	paramMap["env"] = env
	paramMap["openid"] = openId

	response, err := deltaversePb.SendRequest(ctx, deltaversePb.SendRequestParam{
		ServiceType:        "hok",
		DestinationService: "dmfeature-13484",
		Path:               "/dmfeature/13484/clubHot",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "debug").Infof(
			"GetSelfHeatValue SendHokComm err; openId:[%v],err:[%v]", openId, err)
		return "", err
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(
		"GetSelfHeatValue show response; openId:[%v],response:[%v]", openId, response)
	var userRankResponse HokRankResponse
	jsonErr := json.Unmarshal([]byte(response), &userRankResponse)
	if jsonErr != nil {
		err = errs.NewCustomError(ctx, code.JsonParseError,
			"GetSelfHeatValue Unmarshal err,result=%v, \t [Error]:{%v} ", response, jsonErr)
		return "", err
	}
	var heatValue string
	for _, v := range userRankResponse.Data {
		heatValue = v
		break
	}
	return heatValue, nil
}
