package dftransfer

import (
	"encoding/json"

	gameDfPb "git.woa.com/trpcprotocol/publishing_marketing/game_df"
	"gorm.io/gorm"
	"gorm.io/plugin/soft_delete"
	"trpc.act.logicial/app/model"
)

// DfSteamGATransferModel 账号转移
type DfSteamGATransferModel struct {
	model.AppModel
}

// TableName TODO
func (DfSteamGATransferModel) TableName() string {
	return "df_steam_ga_transfer"
}

// DfSteamGATransfer 账号转移
type DfSteamGATransfer struct {
	DfSteamGATransferModel
	ID                 int64        `gorm:"type:int(11);column:id;primary_key"`
	CalmDate           string       `gorm:"column:calm_date;type:varchar(10);NOT NULL;comment:冷静期" json:"calm_date"`
	Uid                string       `gorm:"column:uid;type:varchar(64);NOT NULL" json:"uid"`
	AccountType        int32        `gorm:"column:account_type;type:smallint(6);default:0;NOT NULL" json:"account_type"`
	GaOpeinid          string       `gorm:"column:ga_openid;type:varchar(64);NOT NULL" json:"ga_uid"`
	GaEmail            string       `gorm:"column:ga_email;type:varchar(64);NOT NULL" json:"ga_email"`
	Status             int32        `gorm:"column:status;type:tinyint(4);default:0;comment:当前状态0:未绑定;NOT NULL" json:"status"`
	TransferData       string       `gorm:"column:transfer_data;type:text;" json:"transfer_data"`
	TransferDataStruct UserSendData `gorm:"-" json:"transfer_data_struct"`
	// EmailData       string                `gorm:"column:transfer_data;type:text;" json:"transfer_data"`
	// EmailDataStruct UserSendData          `gorm:"-" json:"transfer_data_struct"`
	EmailBasicData                      string                `gorm:"column:email_basic_data"` // 基本数据
	EmailBasicDataStruct                UserEmailData         `gorm:"-" json:"email_basic_data_struct"`
	EmailMpData                         string                `gorm:"column:email_mp_data"` // Mp数据
	EmailMpDataStruct                   UserEmailData         `gorm:"-" json:"email_mp_data_struct"`
	EmailSolData                        string                `gorm:"column:email_sol_data"` // SOL数据
	EmailSolDataStruct                  UserEmailData         `gorm:"-" json:"email_sol_data_struct"`
	EmailTrianglecoinData               string                `gorm:"column:email_trianglecoin_data"` // 三角币数据
	EmailTrianglecoinDataStruct         UserEmailData         `gorm:"-" json:"email_trianglecoin_data_struct"`
	EmailMandelData                     string                `gorm:"column:email_mandel_data"` // Mandel数据
	EmailMandelDataStruct               UserEmailData         `gorm:"-" json:"email_mandel_data_struct"`
	EmailCollectionHouseData            string                `gorm:"column:email_collection_house_data"` // 仓库数据
	EmailCollectionHouseDataStruct      UserEmailData         `gorm:"-" json:"email_collection_house_data_struct"`
	EmailCommercialSkinData             string                `gorm:"column:email_commercial_skin_data"` // 典藏皮肤数据
	EmailCommercialSkinDataStruct       UserEmailData         `gorm:"-" json:"email_commercial_skin_data_struct"`
	EmailSecurityBoxData                string                `gorm:"column:email_security_box_data"` // 顶级安全宝箱数据
	EmailSecurityBoxDataStruct          UserEmailData         `gorm:"-" json:"email_security_box_data_struct"`
	EmailOperatorData                   string                `gorm:"column:email_operator_data"` // 干员数据
	EmailOperatorDataStruct             UserEmailData         `gorm:"-" json:"email_operator_data_struct"`
	EmailOperatorCollectiblesData       string                `gorm:"column:email_operator_collectibles_data"` // 干员挂饰数据
	EmailOperatorCollectiblesDataStruct UserEmailData         `gorm:"-" json:"email_operator_collectibles_data_struct"`
	EmailMoreEmailData                  string                `gorm:"column:email_more_email_data"` // 超过180封邮件数据
	EmailMoreEmailDataStruct            UserEmailData         `gorm:"-" json:"email_more_email_data_struct"`
	OrangeGunEmailData                  string                `gorm:"column:email_orange_gun_collectors_data"` // 典藏皮肤
	OrangeGunEmailDataEmailDataStruct   UserEmailData         `gorm:"-" json:"email_orange_gun_collectors_data_struct"`
	PendantEmailData                    string                `gorm:"column:email_pendant_data"` // 典藏挂饰
	PendantEmailDataStruct              UserEmailData         `gorm:"-" json:"email_pendant_data_struct"`
	SendStatus                          int32                 `gorm:"column:send_status;type:tinyint(4);default:0;comment:发送状态0:未发送,1:发送成功,2:部分失败;NOT NULL" json:"send_status"`
	LangType                            string                `gorm:"column:lang_type;type:varchar(32);default:'';comment:语言;NOT NULL" json:"lang_type"`
	DeletedAt                           soft_delete.DeletedAt `gorm:"softDelete:milli;column:deleted_at"`
}

// AfterFind 查找后钩子
func (a *DfSteamGATransfer) AfterFind(tx *gorm.DB) (err error) {
	err = json.Unmarshal([]byte(a.TransferData), &a.TransferDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailBasicData), &a.EmailBasicDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailMpData), &a.EmailMpDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailSolData), &a.EmailSolDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailTrianglecoinData), &a.EmailTrianglecoinDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailMandelData), &a.EmailMandelDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailCollectionHouseData), &a.EmailCollectionHouseDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailCommercialSkinData), &a.EmailCommercialSkinDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailSecurityBoxData), &a.EmailSecurityBoxDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailOperatorData), &a.EmailOperatorDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailOperatorCollectiblesData), &a.EmailOperatorCollectiblesDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.EmailMoreEmailData), &a.EmailMoreEmailDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.OrangeGunEmailData), &a.OrangeGunEmailDataEmailDataStruct)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(a.PendantEmailData), &a.PendantEmailDataStruct)
	if err != nil {
		return
	}

	return
}

// // BeforeSave 保存前钩子
// func (a *DfSteamGATransfer) BeforeSave(tx *gorm.DB) (err error) {
// 	jsonBytes, err := json.Marshal(a.EmailDataStruct)
// 	fmt.Println("---------------BeforeSave err1---------------")
// 	fmt.Printf("%#v\n", err)
// 	fmt.Printf("%#v\n", string(jsonBytes))

// 	if err != nil {
// 		return err
// 	}
// 	a.EmailData = string(jsonBytes)
// 	jsonBytes, err = json.Marshal(a.EmailDataStruct)
// 	fmt.Println("---------------BeforeSave err2---------------")
// 	fmt.Printf("%#v\n", err)
// 	fmt.Printf("%#v\n", string(jsonBytes))
// 	if err != nil {
// 		return err
// 	}
// 	a.EmailData = string(jsonBytes)
// 	return nil
// }

// BeforeCreate 创建前钩子
func (a *DfSteamGATransfer) BeforeCreate(tx *gorm.DB) (err error) {
	jsonBytes, err := json.Marshal(a.TransferDataStruct)
	if err != nil {
		return err
	}
	a.TransferData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailBasicDataStruct)
	if err != nil {
		return err
	}
	a.EmailBasicData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailMpDataStruct)
	if err != nil {
		return err
	}
	a.EmailMpData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailSolDataStruct)
	if err != nil {
		return err
	}
	a.EmailSolData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailTrianglecoinDataStruct)
	if err != nil {
		return err
	}
	a.EmailTrianglecoinData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailMandelDataStruct)
	if err != nil {
		return err
	}
	a.EmailMandelData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailCollectionHouseDataStruct)
	if err != nil {
		return err
	}
	a.EmailCollectionHouseData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailCommercialSkinDataStruct)
	if err != nil {
		return err
	}
	a.EmailCommercialSkinData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailSecurityBoxDataStruct)
	if err != nil {
		return err
	}
	a.EmailSecurityBoxData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailOperatorDataStruct)
	if err != nil {
		return err
	}
	a.EmailOperatorData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailOperatorCollectiblesDataStruct)
	if err != nil {
		return err
	}
	a.EmailOperatorCollectiblesData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.EmailMoreEmailDataStruct)
	if err != nil {
		return err
	}
	a.EmailMoreEmailData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.OrangeGunEmailDataEmailDataStruct)
	if err != nil {
		return err
	}
	a.OrangeGunEmailData = string(jsonBytes)
	jsonBytes, err = json.Marshal(a.PendantEmailDataStruct)
	if err != nil {
		return err
	}
	a.PendantEmailData = string(jsonBytes)
	return nil

}

type UserSendData struct {
	List []OneSendData `json:"list"`
}
type OneSendData struct {
	Type         string            `json:"type"`
	AmsId        string            `json:"ams_id"`
	GroupId      string            `json:"group_id"`
	Seq          string            `json:"seq"`
	Num          int32             `json:"num"`
	ScoreType    int32             `json:"score_type"`
	ScoreSubType int32             `json:"score_sub_type"`
	SendStatus   int32             `json:"send_status"` // 0:未发送,1:发送成功,2:失败,3:不需要发送
	Priority     int32             `json:"priority"`    // 优先级
	ItemName     string            `json:"item_name"`
	ItemID       string            `json:"item_id"`
	MoreInfo     map[string]string `json:"more_info"`
}

type UserEmailData struct {
	List []OneEmailData `json:"list"`
}

type OneEmailData struct {
	Key      string                             `json:"key"`
	FileName string                             `json:"file_name"`
	Openid   string                             `json:"openid"`
	GaOpenid string                             `json:"ga_openid"`
	InfoList []*gameDfPb.IDIPReturnValueDetails `json:"info_list"`
}

// type InfoItem struct {
// 	Parameter   string `json:"parameter"`   // 参数名称
// 	Type        string `json:"type"`        // 参数类型
// 	Description string `json:"description"` // 描述
// 	Value       string `json:"value"`       // 值
// }
