// Package record 记录
package record

import (
	"context"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	configModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_record"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/logic/base"
	recordLogicial "trpc.act.logicial/app/logic/record"
	"trpc.act.logicial/app/model/record"
)

// RecordImpl 结构体
type RecordImpl struct{}

// HasRecord 是否有记录
func (s *RecordImpl) HasRecord(ctx context.Context, req *pb.HasRecordReq) (rsp *pb.HasRecordRsp, err error) {
	// implement business logic here ...
	// ...
	rsp = &pb.HasRecordRsp{}
	return
}

// InitRecord 初始化记录
func (s *RecordImpl) InitRecord(ctx context.Context, req *pb.InitRecordReq) (rsp *pb.InitRecordRsp, err error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var tableName string

	tableName, err = configModel.GetTabNameWithSourceId(ctx, (&record.RecordConfig{}).TableName(), req.FsourceId,
		(&record.Record{}).TableName(), 100)
	if err != nil {
		return
	}

	condition := record.Record{
		UID:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		FsourceId:   req.FsourceId,
	}

	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition).FirstOrCreate(&condition)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	if db.RowsAffected > 0 {
		log.WithFieldsContext(ctx, "log_type", "logicial_record", "logicial_record_type", "create").Infof("create record")
	}
	rsp = &pb.InitRecordRsp{
		RecordId: condition.ID,
	}
	return
}

// GetRecordDataById 获取记录（通过id）
func (s *RecordImpl) GetRecordDataById(ctx context.Context, req *pb.GetRecordDataByIdReq) (rsp *pb.GetRecordDataByIdRsp,
	err error) {
	rsp = &pb.GetRecordDataByIdRsp{}
	var tableName string
	tableName, err = configModel.GetTabNameWithSourceId(ctx, (&record.RecordConfig{}).TableName(), req.FsourceId,
		(&record.Record{}).TableName(), 100)
	if err != nil {
		return
	}
	condition := record.Record{
		ID:        int64(req.RecordId),
		FsourceId: req.FsourceId,
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition).First(&condition)
	if db.Error != nil {

	}
	rsp = &pb.GetRecordDataByIdRsp{
		Uid:         condition.UID,
		AccountType: condition.AccountType,
	}
	return
}

// DoRecordTag 记录标签
func (s *RecordImpl) DoRecordTag(ctx context.Context, req *pb.DoRecordTagReq) (rsp *pb.DoRecordTagRsp,
	err error) {
	rsp = &pb.DoRecordTagRsp{}

	var status int32 = 0
	if req.Status {
		status = 1
	}
	err = recordLogicial.DoRecordTag(ctx, req.FsourceId, req.TagId, status)
	if err != nil {
		return
	}
	rsp.Result = true
	log.WithFieldsContext(ctx, "log_type", "logicial_record_tag", "source_id", req.FsourceId, "tag_id", req.TagId).
		Infof("add record tag")
	return
}

// RecordUniqueTag 记录唯一标签,如已记录则覆盖原有标签
func (s *RecordImpl) RecordUniqueTag(ctx context.Context, req *pb.DoRecordTagReq) (rsp *pb.DoRecordTagRsp,
	err error) {
	rsp = &pb.DoRecordTagRsp{}

	var status int32 = 0
	if req.Status {
		status = 1
	}
	err = recordLogicial.RecordUniqueTag(ctx, req.FsourceId, req.TagId, status)
	if err != nil {
		return
	}
	rsp.Result = true
	log.WithFieldsContext(ctx, "log_type", "logicial_record_tag", "source_id", req.FsourceId, "tag_id", req.TagId).
		Infof("add RecordUnique tag")
	return
}

// BatchGetRecordTagListByUidList 根据openid批量获取已记录标签列表(内网调用)
func (s *RecordImpl) BatchGetRecordTagListByUidList(ctx context.Context, req *pb.BatchGetRecordTagListByUidListReq) (
	*pb.BatchGetRecordTagListByUidListRsp, error) {
	// recordTagKey := recordLogicial.RecordTagKey
	list, err := base.BatchGetTagByUIDList(ctx, req.UidList, req.FsourceId, recordLogicial.RecordTagKey)
	if err != nil {
		return nil, err
	}
	return &pb.BatchGetRecordTagListByUidListRsp{
		UserTagList: list,
	}, nil
}

// HasRecordTagList 已记录标签列表
func (s *RecordImpl) HasRecordTagList(ctx context.Context, req *pb.HasRecordTagListReq) (rsp *pb.HasRecordTagListRsp,
	err error) {
	rsp = &pb.HasRecordTagListRsp{}
	list, err := recordLogicial.HasRecordTagList(ctx, req.FsourceId, req.OnlyFinish)
	if err != nil {
		return
	}
	rsp.TagIds = list
	return
}

// HasRecordTag 是否已记录标签
func (s *RecordImpl) HasRecordTag(ctx context.Context, req *pb.HasRecordTagReq) (rsp *pb.HasRecordTagRsp,
	err error) {
	rsp = &pb.HasRecordTagRsp{}
	var hasRecord bool
	hasRecord, err = recordLogicial.CheckHasRecordTag(ctx, req.FsourceId, req.TagId, req.OnlyFinish)
	if err != nil {
		return
	}
	if hasRecord {
		rsp.Result = true
	} else {
		err = errs.NewCustomError(ctx, code.HasNotRecordTag, "has not record_tag ,tag_id=[%v]", req.TagId)
	}
	return
}
