package user

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"trpc.publishing_application.standalonesite/app/common"
	"trpc.publishing_application.standalonesite/app/config"

	"trpc.publishing_application.standalonesite/app/logic/privacy"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"

	"trpc.publishing_application.standalonesite/app/logic/formatted"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

//type UserFansCacheData struct {
//	UserFans []*model.UserCollectionFormat `json:"user_fans"`
//	TotalNum int64                         `json:"total_num"`
//}

type UserFollowsCacheData struct {
	UserFollows []*model.UserCollectionFormat `json:"user_follows"`
	TotalNum    int64                         `json:"total_num"`
}

// isFollowOfficial 是否是关注官号，一键关注官号的能力需要避免关注之后取消关注
func UserFollowOtherUser(c context.Context, myOpenid, toUserOpenID, gameId, areaId string, isFollowOfficial bool) (*pbUser.UserCollectionRsp, error) {
	rsp := &pbUser.UserCollectionRsp{}
	limitRedisKey := cache.GetUserFollowOtherUserLimitKey(myOpenid, toUserOpenID)
	if ok, err := redis.GetClient().SetNX(c, limitRedisKey, 1, 2*time.Minute).Result(); ok {
		defer redis.GetClient().Del(c, limitRedisKey)
		user, err := GetUserInfoByOpenid(c, toUserOpenID, false)
		if err != nil || user == nil || user.Model == nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserFollowOtherUser GetUserInfoByOpenid err: %v\n", err)
			return rsp, errs.NewCustomError(c, code.NoExistUsername, "UserFollowOtherUser | User does not exist")
		}

		// 用于判断是否是互相关注
		mutualCollection, err := GetUserCollectionUser(toUserOpenID, myOpenid, false)
		if err != nil && err.Error() != "record not found" {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserFollowOtherUser GetUserCollectionUser mutual err: %v, userOpenID is %s, toUserOpenID is %s", err, myOpenid, toUserOpenID)
			return rsp, errs.NewCustomError(c, code.GetUserFollowFailed, "UserFollowOtherUser | Failed to obtain information about other users followed by the user.")
		}
		collection, err := GetUserCollectionUser(myOpenid, toUserOpenID, true)
		if err != nil {
			if err.Error() != "record not found" {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserFollowOtherUser GetUserCollectionUser err: %v, userOpenID is %s, toUserOpenID is %s", err, myOpenid, toUserOpenID)
				return rsp, errs.NewCustomError(c, code.GetUserFollowFailed, "UserFollowOtherUser | Failed to obtain information about other users followed by the user.")
			}
			// 从无到有，创建collection
			rsp.IsFollow = true
			collectionInfo, err := CreateUserCollectionUser(c, myOpenid, toUserOpenID, gameId, areaId, mutualCollection)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserFollowOtherUser CreateUserCollectionUser err: %v, userOpenID is %s, toUserOpenID is %s", err, myOpenid, toUserOpenID)
				return rsp, errs.NewCustomError(c, code.CreateUserCollectionFailed, "UserFollowOtherUser | Follow user failed")
			}
			rsp.IsMutualFollow = collectionInfo.IsMutual == 1
		} else {
			// 已有记录
			if collection.IsDel == 0 {
				if !isFollowOfficial {
					// 从关注变成不关注
					err = DeleteUserCollectionUser(c, collection, mutualCollection)
					if err != nil {
						return rsp, errs.NewCustomError(c, code.DeleteUserCollectionFailed, "UserFollowOtherUser | Unfollow user failed")
					}
				}
			} else {
				// 从不关注又重新关注
				rsp.IsFollow = true
				isMutual, err := UserReCollectionUser(c, collection, mutualCollection, gameId, areaId)
				if err != nil {
					return rsp, errs.NewCustomError(c, code.UserReCollectionFailed, "UserReCollectionUser | Refollow user failed")
				}
				rsp.IsMutualFollow = isMutual == 1
			}

		}
		// DeleteUserFansListCache(c, toUserOpenID)
		// DeleteUserFollowsListCache(c, myOpenid)
		cache.RemoveUserFollowsCache(c, myOpenid)
		cache.RemoveUserFansCache(c, myOpenid)
		cache.RemoveUserFollowsCache(c, toUserOpenID)
		cache.RemoveUserFansCache(c, toUserOpenID)
		return rsp, nil
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserFollowOtherUser redis return error: %v", err)
		return rsp, errs.NewCustomError(c, code.UserFollowOtherFrequentLimit, "UserFollowOtherUser | Favorite other users who frequently operate frequency limit.")
	}
}

// 用户批量关注其他用户
func UserBatchFollowOthersUsers(c context.Context, myOpenid string, toUserOpenIDs []string, gameId, areaId string, isFollowOfficial bool) error {
	// 相互关注
	mutualCollections, err := dao.UserCollectionsGet(0, toUserOpenIDs, []string{myOpenid}, false, false)
	if err != nil && err.Error() != "record not found" {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserFollowOtherUser GetUserCollectionUser mutual err: %v, userOpenID is %s, toUserOpenIDs is %v", err, myOpenid, toUserOpenIDs)
		return errs.NewCustomError(c, code.GetUserFollowFailed, "UserFollowOtherUser | Failed to obtain information about other users followed by the user.")
	}
	collections, err := dao.UserCollectionsGet(0, []string{myOpenid}, toUserOpenIDs, true, false)
	if err != nil {
		if err.Error() != "record not found" {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserFollowOtherUser GetUserCollectionUser err: %v, userOpenID is %s, toUserOpenIDs is %v", err, myOpenid, toUserOpenIDs)
			return errs.NewCustomError(c, code.GetUserFollowFailed, "UserFollowOtherUser | Failed to obtain information about other users followed by the user.")
		}
	}
	// 数据过滤
	toUserOpenIDs = filterFromUsersAndToUsers(myOpenid, toUserOpenIDs)
	// 需要创建的关注数据
	waitingCreatedCollections := make([]*model.UserCollection, 0)
	// 等待重新关注数据
	waitingReFollowsWithMutual := make([]int64, 0)
	waitingReFollowsWithoutMutual := make([]int64, 0)
	messages := make([]*model.Message, 0)
	allUserCollections := make([]*model.UserCollection, 0)
	for _, toUserOpenId := range toUserOpenIDs {
		isMutual := 0
		for _, mutualCollection := range mutualCollections {
			if mutualCollection.IntlOpenid == toUserOpenId {
				// 已经是互相关注
				isMutual = 1
				continue
			}
		}
		var curCollection *model.UserCollection = nil
		for _, collection := range collections {
			if collection.ToIntlOpenid == toUserOpenId {
				curCollection = collection
				break
			}
		}
		// 创建记录
		if curCollection == nil {
			curCollection = &model.UserCollection{
				IntlOpenid:   myOpenid,
				ToIntlOpenid: toUserOpenId,
				IsMutual:     isMutual,
			}
			waitingCreatedCollections = append(waitingCreatedCollections, curCollection)
			messages = append(messages, &model.Message{
				ReceiverUserIntlOpenid: toUserOpenId,
				SenderUserIntlOpenid:   myOpenid,
				Type:                   constants.MsgTypeFollow,
				GameID:                 gameId,
				AreaID:                 areaId,
			})
			allUserCollections = append(allUserCollections, curCollection)
		} else {
			allUserCollections = append(allUserCollections, curCollection)
			// 已关注，忽略
			if curCollection.IsDel == 0 {
				continue
			} else {
				if isMutual == 1 {
					waitingReFollowsWithMutual = append(waitingReFollowsWithMutual, curCollection.ID)
				} else {
					waitingReFollowsWithoutMutual = append(waitingReFollowsWithoutMutual, curCollection.ID)
				}
				messages = append(messages, &model.Message{
					ReceiverUserIntlOpenid: toUserOpenId,
					SenderUserIntlOpenid:   myOpenid,
					Type:                   constants.MsgTypeFollow,
					GameID:                 gameId,
					AreaID:                 areaId,
				})
			}
		}
	}
	err = dao.BatchFollowed(waitingCreatedCollections, waitingReFollowsWithoutMutual, waitingReFollowsWithMutual, allUserCollections)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("BatchFollowed error, userOpenID is %s, toUserOpenIDs is %v, waitingCreatedCollections: %v, waitingReFollowsWithoutMutual: %v, waitingReFollowsWithMutual: %v, error: %v", err, myOpenid, toUserOpenIDs, waitingCreatedCollections, waitingReFollowsWithoutMutual, waitingReFollowsWithMutual, err)
		return errs.NewCustomError(c, code.GetUserFollowFailed, "UserBatchFollowOthersUsers | Failed to follow other users.")
	}
	// 发送message
	for _, message := range messages {
		go writemessage.SetUserMessage(message, message.ReceiverUserIntlOpenid, constants.FollowMessageCount)
	}
	cache.RemoveUserFollowsCache(c, myOpenid)
	cache.RemoveUserFansCache(c, myOpenid)
	for _, toUserOpenID := range toUserOpenIDs {
		cache.RemoveUserFansCache(c, toUserOpenID)
		cache.RemoveUserFollowsCache(c, toUserOpenID)
	}
	return nil
}

func filterFromUsersAndToUsers(fromIntlOpenids string, toIntlOpenids []string) []string {
	res := make([]string, 0)
	unique := make(map[string]int, 0)
	for _, toIntlOpenid := range toIntlOpenids {
		if _, ok := unique[toIntlOpenid]; ok {
			continue
		}
		if toIntlOpenid != fromIntlOpenids {
			res = append(res, toIntlOpenid)
		}
		unique[toIntlOpenid] = 1
	}
	return res
}

func GetUserCollectionUser(userIntlOpenid, toUserIntlOpenid string, needDelRecord bool) (*model.UserCollection, error) {
	return dao.UserCollectionGet(0, userIntlOpenid, toUserIntlOpenid, needDelRecord, true)
}

func CreateUserCollectionUser(c context.Context, intlOpenID, toUserOpenid, gameId, areaId string, mutualCollection *model.UserCollection) (*model.UserCollection, error) {
	user, toUser, err := GetUserCollectionState(c, intlOpenID, toUserOpenid)
	if err != nil {
		return nil, err
	}
	isMutual := 0
	if mutualCollection != nil && mutualCollection.Model != nil && mutualCollection.ID > 0 {
		isMutual = 1
		err = dao.UseCollectionUpdate(mutualCollection.ID, isMutual)
		if err != nil {
			return nil, err
		}
	}
	var userCollection = &model.UserCollection{
		IntlOpenid:   intlOpenID,
		ToIntlOpenid: toUserOpenid,
		IsMutual:     isMutual,
	}
	err = dao.UserCollectionCreate(userCollection)
	if err != nil {
		go func(ctx context.Context, err2 error) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportUserFollow(c, intlOpenID, userCollection, false, err2)
		}(c, err)
		return userCollection, err
	}

	go func(ctx context.Context) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportUserFollow(c, intlOpenID, userCollection, false, nil)
	}(c)

	// 更新用户关注数
	user.FollowNum++
	dao.UpdateUserFollowNum(user.IntlOpenid, user.FollowNum)
	toDoc := map[string]interface{}{
		"follow_num": user.FollowNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, user.IntlOpenid, toDoc)

	// 更新用户粉丝数
	toUser.FansNum++
	dao.UpdateUserFansNum(toUser.IntlOpenid, toUser.FansNum)
	doc := map[string]interface{}{
		"fans_num": toUser.FansNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, toUser.IntlOpenid, doc)
	// 刷新es索引，使其立即生效
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	// 创建用户消息提醒
	if toUser.IntlOpenid != intlOpenID {
		go writemessage.SetUserMessage(&model.Message{
			Type:                   constants.MsgTypeFollow,
			GameID:                 gameId,
			AreaID:                 areaId,
			ReceiverUserIntlOpenid: toUserOpenid,
			SenderUserIntlOpenid:   intlOpenID,
		}, toUser.IntlOpenid, constants.FollowMessageCount)
	}
	return userCollection, nil
}

func DeleteUserCollectionUser(c context.Context, collection *model.UserCollection, mutualCollection *model.UserCollection) error {
	err := dao.UserCollectionDelete(collection.ID)
	if err != nil {
		go func(ctx context.Context, err2 error) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportUserFollow(c, collection.IntlOpenid, collection, true, err2)
		}(c, err)
		return err
	}
	go func(ctx context.Context) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportUserFollow(c, collection.IntlOpenid, collection, true, nil)
	}(c)
	if mutualCollection != nil && mutualCollection.Model != nil && mutualCollection.ID > 0 {
		err = dao.UseCollectionUpdate(mutualCollection.ID, 0)
		if err != nil {
			return err
		}
	}

	user, toUser, err := GetUserCollectionState(c, collection.IntlOpenid, collection.ToIntlOpenid)
	if err != nil {
		return err
	}
	// 更新用户关注数
	user.FollowNum--
	if user.FollowNum < 0 {
		user.FollowNum = 0
	}
	dao.UpdateUserFollowNum(user.IntlOpenid, user.FollowNum)
	toDoc := map[string]interface{}{
		"follow_num": user.FollowNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, user.IntlOpenid, toDoc)

	// 更新用户粉丝数
	toUser.FansNum--
	if toUser.FansNum < 0 {
		toUser.FansNum = 0
	}
	dao.UpdateUserFansNum(toUser.IntlOpenid, toUser.FansNum)
	doc := map[string]interface{}{
		"fans_num": toUser.FansNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, toUser.IntlOpenid, doc)
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)

	return nil
}

func UserReCollectionUser(c context.Context, collection *model.UserCollection, mutualCollection *model.UserCollection, gameId, areaId string) (int, error) {
	isMutual := 0
	if mutualCollection != nil && mutualCollection.Model != nil && mutualCollection.ID > 0 {
		isMutual = 1
		err := dao.UseCollectionUpdate(mutualCollection.ID, isMutual)
		if err != nil {
			go func(ctx context.Context, err2 error) {
				newC := trpc.CloneContext(ctx)
				defer recovery.CatchGoroutinePanic(newC)
				common.ReportUserFollow(c, mutualCollection.IntlOpenid, mutualCollection, true, err2)
			}(c, err)
			return isMutual, err
		}
	}
	err := dao.UseCollectionUpdate(collection.ID, isMutual)
	if err != nil {
		go func(ctx context.Context, err2 error) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportUserFollow(c, collection.IntlOpenid, collection, true, err2)
		}(c, err)
		return isMutual, err
	}

	go func(ctx context.Context) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportUserFollow(c, collection.IntlOpenid, collection, true, nil)
	}(c)

	user, toUser, err := GetUserCollectionState(c, collection.IntlOpenid, collection.ToIntlOpenid)
	if err != nil {
		return isMutual, err
	}

	// 更新用户关注数
	user.FollowNum++
	if user.FollowNum < 0 {
		user.FollowNum = 0
	}
	dao.UpdateUserFollowNum(user.IntlOpenid, user.FollowNum)
	toDoc := map[string]interface{}{
		"follow_num": user.FollowNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, user.IntlOpenid, toDoc)

	// 更新用户粉丝数
	toUser.FansNum++
	if toUser.FansNum < 0 {
		toUser.FansNum = 0
	}
	dao.UpdateUserFansNum(toUser.IntlOpenid, toUser.FansNum)
	doc := map[string]interface{}{
		"fans_num": toUser.FansNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, toUser.IntlOpenid, doc)
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	// 创建用户消息提醒
	if toUser.IntlOpenid != user.IntlOpenid {
		go writemessage.SetUserMessage(&model.Message{
			Type:                   constants.MsgTypeFollow,
			GameID:                 gameId,
			AreaID:                 areaId,
			ReceiverUserIntlOpenid: toUser.IntlOpenid,
			SenderUserIntlOpenid:   user.IntlOpenid,
		}, toUser.IntlOpenid, constants.FollowMessageCount)
	}
	return isMutual, nil
}

// GetPostListMyIsFollow 是否关注发帖人
func GetPostListMyIsFollow(c context.Context, intlOpenid string, postUserFollowIds []string, posts []*pb.GetPostRsp) error {
	// 我关注的人列表
	if len(postUserFollowIds) > 0 {
		userFollows, err := dao.UserCollectionList(&dao.UserCollectionConditions{
			IntlOpenid:    intlOpenid,
			ToIntlOpenids: postUserFollowIds,
		}, 0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserPostsComments err: %v\n", err)
			return errs.NewCustomError(c, code.GetUserCollectionListError, "GetPostListMyIsFollow | Failed to get follow list.")
		}
		for _, post := range posts {
			for _, follow := range userFollows {
				if post.IntlOpenid == follow.ToIntlOpenid {
					post.IsFollow = true
					post.IsMutualFollow = follow.IsMutual == 1
				}
			}
		}
	}
	return nil
}

func DeleteUserFansListCache(c context.Context, intlOpenID string) {
	if intlOpenID == "" {
		return
	}
	conditionsT := &dao.UserCollectionConditions{
		ToIntlOpenid: intlOpenID,
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}
	totalRows, _ := dao.UserCollectionCount(conditionsT)
	totalPages := (totalRows / 10) + 1
	var offset int32
	var userFansRedisKeys []string
	for offset = 0; offset < totalPages; offset++ {
		userFansRedisKey := cache.GetUserFansKey(intlOpenID, "", 10)
		userFansRedisKeys = append(userFansRedisKeys, userFansRedisKey)
	}
	if len(userFansRedisKeys) > 0 {
		redis.GetClient().Del(context.Background(), userFansRedisKeys...)
	}
}

func DeleteUserFollowsListCache(c context.Context, intlOpenID string) {
	if intlOpenID == "" {
		return
	}
	conditionsT := &dao.UserCollectionConditions{
		IntlOpenid: intlOpenID,
		Order: []*dao.OrderConditions{
			&dao.OrderConditions{
				Column: "id",
				IsDesc: true,
			},
		},
	}
	totalRows, _ := dao.UserCollectionFollowCount(conditionsT)
	totalPages := (totalRows / 10) + 1
	var offset int64
	var userFollowsRedisKeys []string
	for offset = 0; offset < totalPages; offset++ {
		userFollowsRedisKey := cache.GetUserFollowsKey(intlOpenID, "", 10)
		userFollowsRedisKeys = append(userFollowsRedisKeys, userFollowsRedisKey)
	}
	if len(userFollowsRedisKeys) > 0 {
		redis.GetClient().Del(context.Background(), userFollowsRedisKeys...)
	}
}

// GetUserFollowUser 获取用户关注列表
func GetUserFollowUser(intlOpenID, myUserOpenid string) (int64, error) {
	if myUserOpenid == "" || intlOpenID == "" {
		return 0, nil
	}
	conditionsT := &dao.UserCollectionConditions{
		IntlOpenid:   myUserOpenid,
		ToIntlOpenid: intlOpenID,
	}
	totalRows, err := dao.UserCollectionFollowCount(conditionsT)
	if err != nil {
		return 0, err
	}
	return totalRows, nil
}

// GetUserFans 获取用户粉丝列表
func GetUserFans(c context.Context, myUserOpenid, intlOpenID string, limit int, language string, conditions *dao.UserCollectionConditions, nextPage string) ([]*model.UserCollectionFormat, error) {
	var userFansCacheData UserFollowsCacheData
	var userFanFormats []*model.UserCollectionFormat
	var err error
	var userFansRedisKey = ""
	if myUserOpenid != intlOpenID {
		privacySwitch, err := privacy.GetUserPrivacySwitch(c, intlOpenID)
		if err != nil {
			return userFanFormats, err
		}
		if privacySwitch.ShowMyFans == 0 {
			return userFanFormats, nil
		}
	}
	// 前置这个搜索条件，避免缓存这里查询的都是同一个人
	conditions.ToIntlOpenid = intlOpenID
	// 先获取缓存数据
	// conditions转存string 作为key
	conditionsStr, err := json.Marshal(conditions)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserFans cache Marshal conditions: %v err: %v", conditions, err)
	} else {
		userFansRedisKey = cache.GetUserFansKeyWithConditionLang(myUserOpenid, nextPage, limit, string(conditionsStr), language)
		userFansCacheInfo, err := redis.GetClient().Get(c, userFansRedisKey).Result()
		if err == nil {
			err = json.Unmarshal([]byte(userFansCacheInfo), &userFansCacheData)
			if err == nil {
				userFanFormats = userFansCacheData.UserFollows
				return userFanFormats, nil
			}
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserFans cache err: %v", err)
		}
	}
	// userFansRedisKey := cache.GetUserFansKey(intlOpenID, nextPage, limit)
	// TODO 临时屏蔽缓存，待技术优化
	// userFansCacheInfo, err := redis.GetClient().Get(c, userFansRedisKey).Result()
	// if err == nil {
	// 	err = json.Unmarshal([]byte(userFansCacheInfo), &userFansCacheData)
	// 	if err == nil {
	// 		userFanFormats = userFansCacheData.UserFollows
	// 	}
	// }

	// 获取缓存数据失败则直接查db
	// if err != nil {

	//conditions := &model.ConditionsT{
	//	"to_intl_openid": intlOpenID,
	//	"is_del":         0,
	//	"ORDER":          "id desc",
	//}
	collections, err := dao.UserCollectionList(conditions, limit)
	if err != nil {
		return nil, err
	}
	for _, collection := range collections {
		collectionFormat := collection.FormatFollow()
		if collection.User != nil {
			collectionFormat.User = collection.User.Format()
			if err = SetPostUserTitle(c, formatted.ReturnDynamicProtoUserInfoFormatted(collectionFormat.User), language); err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetPostUserTitle err:%v", err)
			}
		}
		// 重置认证用户昵称个签多语言
		authUserName, authUserRemark, _ := GetUserCerificationUserLanguage(c, collectionFormat.User.IntlOpenid, language)
		if authUserName != "" {
			collectionFormat.User.Username = authUserName
		}
		if authUserRemark != "" {
			collectionFormat.User.Remark = authUserRemark
		}
		userFanFormats = append(userFanFormats, collectionFormat)
	}

	// }

	// 查询他的粉丝中是否有我关注的用户
	// 获取我关注的人员id列表
	if myUserOpenid != "" {
		checkUserFollow(c, userFanFormats, myUserOpenid, true)
	}
	// 设置缓存
	userFansCacheData.UserFollows = userFanFormats
	userFansCacheByte, err := json.Marshal(userFansCacheData)
	if err == nil {
		redis.GetClient().SetEX(c, userFansRedisKey, string(userFansCacheByte), 2*time.Minute).Result()
		if myUserOpenid != "" {
			cacheKeys := cache.GetUserFansKeysKey(myUserOpenid)
			redis.GetClient().SAdd(c, cacheKeys, userFansRedisKey)
		}
	}
	return userFanFormats, nil
}

// GetUserFollow 获取用户关注列表
func GetUserFollow(c context.Context, myUserOpenid, intlOpenID string, limit int, language string, conditions *dao.UserCollectionConditions, nextPage string) ([]*model.UserCollectionFormat, error) {
	var userFollowsCacheData UserFollowsCacheData
	var userFollowFormats []*model.UserCollectionFormat
	var err error
	var userFollowsRedisKey = ""
	if myUserOpenid != intlOpenID {
		privacySwitch, err := privacy.GetUserPrivacySwitch(c, intlOpenID)
		if err != nil {
			return userFollowFormats, err
		}
		if privacySwitch.ShowMyFollow == 0 {
			return userFollowFormats, nil
		}
	}

	// 前置这个搜索条件，避免缓存这里查询的都是同一个人
	conditions.IntlOpenid = intlOpenID
	// 先获取缓存数据
	// conditions转存string 作为key
	conditionsStr, err := json.Marshal(conditions)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserFans cache Marshal conditions: %v err: %v", conditions, err)
	} else {
		userFollowsRedisKey = cache.GetUserFollowsKeyWithConditionLang(myUserOpenid, nextPage, limit, string(conditionsStr), language)
		userFollowsCacheDataStr, err := redis.GetClient().Get(c, userFollowsRedisKey).Result()
		if err == nil {
			err = json.Unmarshal([]byte(userFollowsCacheDataStr), &userFollowsCacheData)
			if err == nil {
				userFollowFormats = userFollowsCacheData.UserFollows
				return userFollowFormats, nil
			}
		}
	}
	// TODO 临时屏蔽缓存
	// userFollowsCacheInfo, err := redis.GetClient().Get(c, userFollowsRedisKey).Result()
	// if err == nil {
	// 	err = json.Unmarshal([]byte(userFollowsCacheInfo), &userFollowsCacheData)
	// 	if err == nil {
	// 		userFollowFormats = userFollowsCacheData.UserFollows
	// 	}
	// }

	// 获取缓存数据失败则直接查db
	// if err != nil {

	collections, err := dao.UserCollectionListFollow(conditions, limit)
	if err != nil {
		return nil, err
	}
	for _, collection := range collections {
		collectionFormat := collection.FormatFollow()
		if myUserOpenid == intlOpenID {
			// 如果是自己查看自己的，必然是关注的
			collectionFormat.IsFollow = true
			collectionFormat.IsMutualFollow = collection.IsMutual == 1
		}
		if collection.User != nil {
			collectionFormat.User = collection.User.Format()
			if err = SetPostUserTitle(c, formatted.ReturnDynamicProtoUserInfoFormatted(collectionFormat.User), language); err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetPostUserTitle err:%v", err)
			}
		}
		// 重置认证用户昵称个签多语言
		authUserName, authUserRemark, _ := GetUserCerificationUserLanguage(c, collectionFormat.User.IntlOpenid, language)
		if authUserName != "" {
			collectionFormat.User.Username = authUserName
		}
		if authUserRemark != "" {
			collectionFormat.User.Remark = authUserRemark
		}
		userFollowFormats = append(userFollowFormats, collectionFormat)
	}

	// }

	// TODO 不能查全量，按需查询
	// 获取我关注的人员id列表, 如果是查询自己就没有必要
	if myUserOpenid != "" && myUserOpenid != intlOpenID {
		checkUserFollow(c, userFollowFormats, myUserOpenid, false)
	}
	// 设置缓存
	userFollowsCacheData.UserFollows = userFollowFormats
	userFollowsCacheByte, err := json.Marshal(userFollowsCacheData)
	if err == nil {
		redis.GetClient().SetEX(c, userFollowsRedisKey, string(userFollowsCacheByte), 2*time.Minute).Result()
		if myUserOpenid != "" {
			cacheKey := cache.UserFollowsCacheKeyKeys(myUserOpenid)
			redis.GetClient().SAdd(c, cacheKey, userFollowsRedisKey)
		}
	}
	return userFollowFormats, nil
}

// 组装关系数据， userFollowFormats: 他的关注列表用户、他得粉丝列表
func checkUserFollow(c context.Context, userFollowFormats []*model.UserCollectionFormat, myIntlPOpenid string, isFans bool) {
	// 查询列表中的用户是否关注自己
	var userIntlOpenids = make([]string, 0, len(userFollowFormats))
	for _, follow := range userFollowFormats {
		if !isFans {
			userIntlOpenids = append(userIntlOpenids, follow.ToIntlOpenid)
			continue
		}
		userIntlOpenids = append(userIntlOpenids, follow.IntlOpenid)

	}
	// 我关注的
	var myFollowed = &dao.UserCollectionConditions{
		IntlOpenid:    myIntlPOpenid,
		ToIntlOpenids: userIntlOpenids,
	}
	// 获取我关注的用户信息
	myFollowList, err := dao.UserCollectionListFollow(myFollowed, len(userIntlOpenids))
	for i, format := range userFollowFormats {
		for _, collection := range myFollowList {
			if collection.ToIntlOpenid == format.ToIntlOpenid && !isFans {
				userFollowFormats[i].IsFollow = true
				break
			}
			if collection.ToIntlOpenid == format.IntlOpenid && isFans {
				userFollowFormats[i].IsFollow = true
				break
			}
		}
	}
	// 我的粉丝
	followdMy := &dao.UserCollectionConditions{
		ToIntlOpenid: myIntlPOpenid,
		IntlOpenids:  userIntlOpenids,
	}
	myFans, err := dao.UserCollectionListFollow(followdMy, len(userIntlOpenids))
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("ds.GetMyFollowUserIDs to following me err:%v", err)
	}
	for _, fans := range myFans {
		for i, format := range userFollowFormats {
			if fans.IntlOpenid == format.ToIntlOpenid && format.IsFollow == true && !isFans {
				userFollowFormats[i].IsMutualFollow = true
				break
			}
			if fans.IntlOpenid == format.IntlOpenid && format.IsFollow == true && isFans {
				userFollowFormats[i].IsMutualFollow = true
				break
			}
		}
	}
}

func GetUserCollectionState(c context.Context, userIntlOpenid, toUserIntlOpenid string) (*model.UserState, *model.UserState, error) {
	userState, err := dao.GetUserStateByUserOpenid(userIntlOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCollectionState get user stat failed, userIntlOpenid is %s, err:[%v]", userIntlOpenid, err)
		return nil, nil, errs.NewCustomError(c, code.GetUserCollectionStatInfoNotExist, "GetUserCollectionState | Failed to get user stat information.")
	}
	toUserState, err := dao.GetUserStateByUserOpenid(toUserIntlOpenid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCollectionState get user stat failed, toUserIntlOpenid is %s, err:[%v]", toUserIntlOpenid, err)
		return nil, nil, errs.NewCustomError(c, code.GetUserCollectionStatInfoNotExist, "GetUserCollectionState | Failed to get user stat information.")
	}

	return userState, toUserState, nil
}

func IsFollowOtherUsers(c context.Context, intlOpenid string, intlOpenIds []string) (isFolloweds []bool, err error) {
	collections, err := dao.UserCollectionsGet(0, []string{intlOpenid}, intlOpenIds, false, false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("IsFollowOtherUsers get user collection failed, intlOpenid is %s, err:[%v]", intlOpenid, err)
		return isFolloweds, errs.NewCustomError(c, code.GetUserCollectionStatInfoNotExist, "IsFollowOtherUsers | Failed to get user collection information.")
	}
	for _, toIntOpenid := range intlOpenIds {
		isFound := false
		for _, collection := range collections {
			if toIntOpenid == collection.ToIntlOpenid {
				isFound = true
				isFolloweds = append(isFolloweds, true)
				break
			}
		}
		if !isFound {
			isFolloweds = append(isFolloweds, false)
		}
	}
	return isFolloweds, nil
}
