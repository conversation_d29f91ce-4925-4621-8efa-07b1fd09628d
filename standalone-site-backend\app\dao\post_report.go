package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

type PostReportConditions struct {
	ContentUuids []string
	ContentTypes []int32
	ContentUuid  string
	ContentType  int32
}

func PostReportCreate(report *model.PostReport) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostReport{}).TableName()).Create(report).Error

	return err
}

func PostReportListWithIgnoreDelete(conditions *PostReportConditions, offset, limit int) ([]*model.PostReport, error) {
	var contents []*model.PostReport
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.PostReport{}).TableName())
	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}

	if len(conditions.ContentUuids) > 0 {
		db = db.Where("content_uuid in ?", conditions.ContentUuids)
	}
	if len(conditions.ContentTypes) > 0 {
		db = db.Where("content_type in ?", conditions.ContentTypes)
	}

	if err = db.Unscoped().Find(&contents).Error; err != nil {
		return nil, err
	}

	return contents, nil
}

func PostReportGet(id int64, reportIntlOpenid string, contentUuid string, contentType int32) (*model.PostReport, error) {
	var content model.PostReport
	db := DB.SelectConnect("db_standalonesite").Table((&model.PostReport{}).TableName())
	if id > 0 {
		db = db.Where("id = ? ", id)
	} else if contentUuid != "" && contentType > 0 && reportIntlOpenid != "" {
		db = db.Where("content_uuid = ? AND content_type = ? AND report_intl_openid = ? ", contentUuid, contentType, reportIntlOpenid)
	} else {
		return nil, gorm.ErrRecordNotFound
	}

	err := db.First(&content).Error
	if err != nil {
		return &content, err
	}

	return &content, nil
}

func PostReportGetRowWithIgnoreDelete(conditions *PostReportConditions) (*model.PostReport, error) {
	var content model.PostReport
	db := DB.SelectConnect("db_standalonesite").Table((&model.PostReport{}).TableName())

	if conditions.ContentUuid != "" {
		db = db.Where("content_uuid = ?", conditions.ContentUuid)
	}
	if conditions.ContentType > 0 {
		db = db.Where("content_type = ?", conditions.ContentType)
	}

	err := db.Unscoped().First(&content).Error
	if err != nil {
		return &content, err
	}

	return &content, nil
}

func PostReportUpdateCommentMoveInfo(commentID int64, communityContentID string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostReport{}).TableName()).Where("content_id = ?", commentID).Where("content_type = ?", 2).Updates(map[string]interface{}{
		"community_content_id": communityContentID,
	}).Error
}

func UpdateCommentReportStatus(commentUuid string, status int) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostReport{}).TableName()).Where("content_uuid = ?", commentUuid).Where("content_type in ?", []int{2, 3}).Updates(map[string]interface{}{
		"status":      status,
		"modified_on": time.Now().Unix(),
	}).Error
}

func GetCommentReportStatus(commentUuid []string, status int) ([]*model.PostReport, error) {
	var reportList []*model.PostReport
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostReport{}).TableName()).Where("content_uuid in ? and status = ?", commentUuid, status).Find(&reportList).Error
	return reportList, err
}

func UpdatePostReport(postUuid string, status int) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostReport{}).TableName()).Where("content_uuid = ?", postUuid).Where("content_type = ?", 1).Updates(map[string]interface{}{
		"status":      status,
		"modified_on": time.Now().Unix(),
	}).Error
}

func GetPostReportList(limit int) ([]*model.PostReport, error) {
	var list []*model.PostReport
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostReport{}).TableName()).Unscoped().Limit(limit).Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func BatchUpdateReportLanguage(list map[string][]int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostReport{}).TableName()).Transaction(func(tx *gorm.DB) error {
		for language, ids := range list {
			err := tx.Unscoped().Where("id in ?", ids).Update("language", language).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}
