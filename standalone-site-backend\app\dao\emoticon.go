package dao

import (
	"fmt"
	"sort"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetAllEmoticonInfo() ([]*model.EmoticonGroupInfo, error) {
	gameId := "30054"
	areaId := "global"
	var emoticonGroupJoinIconInfos []model.EmoticonGroupJoinIconInfo
	var field = "eg.id as group_id, eg.name as group_name, eg.pic_url as group_pic_url, eg.order as group_order, ei.id as icon_id, ei.name as icon_name, ei.icon as icon, ei.order as icon_order"
	err := DB.SelectConnect("db_standalonesite").
		Table((&model.EmoticonGroup{}).TableName()+" eg").
		Joins(fmt.Sprintf("left join %s eig on eg.id = eig.group_id", (&model.EmoticonIconGroup{}).TableName())).
		Joins(fmt.Sprintf("left join %s ei on ei.id = eig.icon_id ", (&model.EmoticonIcon{}).TableName())).
		Where("eg.`status` = ? and eg.`is_del` = ? and eg.`game_id` = ? and eg.`area_id` = ?", 2, 0, gameId, areaId).
		Where("ei.`is_del` = ? and eg.`game_id` = ? and eg.`area_id` = ?", 0, gameId, areaId).
		Select(field).
		Find(&emoticonGroupJoinIconInfos).Error
	if err != nil {
		return nil, err
	}

	resultMap := make(map[int64]*model.EmoticonGroupInfo)

	for _, emoticonGroupJoinIconInfo := range emoticonGroupJoinIconInfos {
		if _, exists := resultMap[emoticonGroupJoinIconInfo.GroupID]; !exists {
			resultMap[emoticonGroupJoinIconInfo.GroupID] = &model.EmoticonGroupInfo{
				ID:                emoticonGroupJoinIconInfo.GroupID,
				Name:              emoticonGroupJoinIconInfo.GroupName,
				Order:             emoticonGroupJoinIconInfo.GroupOrder,
				PicURL:            emoticonGroupJoinIconInfo.GroupPicURL,
				EmoticonIconInfos: make([]*model.EmoticonIconInfo, 0),
			}
		}
		if emoticonGroupJoinIconInfo.IconID == 0 {
			continue
		}
		resultMap[emoticonGroupJoinIconInfo.GroupID].EmoticonIconInfos = append(resultMap[emoticonGroupJoinIconInfo.GroupID].EmoticonIconInfos, &model.EmoticonIconInfo{
			ID:    emoticonGroupJoinIconInfo.IconID,
			Name:  emoticonGroupJoinIconInfo.IconName,
			Icon:  emoticonGroupJoinIconInfo.Icon,
			Order: emoticonGroupJoinIconInfo.IconOrder,
		})
	}

	// 将结果转换为切片
	var resultList []*model.EmoticonGroupInfo
	for _, r := range resultMap {
		sort.Slice(r.EmoticonIconInfos, func(i, j int) bool {
			return r.EmoticonIconInfos[i].Order < r.EmoticonIconInfos[j].Order
		})
		resultList = append(resultList, r)
	}
	sort.Slice(resultList, func(i, j int) bool {
		return resultList[i].Order < resultList[j].Order
	})
	return resultList, nil

}
