package hoktmp

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	languageUtil "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/language"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/object"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	userAccountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	"git.woa.com/trpcprotocol/publishing_application/lipass_common"
	gameHokPb "git.woa.com/trpcprotocol/publishing_marketing/game_hok"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_hok_tmp"
	"git.woa.com/trpcprotocol/publishing_marketing/present_present_v2"
	redisOrigin "github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/global"
	baseLogic "trpc.act.logicial/app/logic/base"
	"trpc.act.logicial/app/logic/common"
	"trpc.act.logicial/app/logic/hoktmp/feature_platform_pb"
	baseTotalModel "trpc.act.logicial/app/model/base"
	"trpc.act.logicial/app/model/hok"
	"trpc.act.logicial/app/model/invitation"
	"trpc.act.logicial/app/util"
)

const (
	// SendTypeTask TODO
	SendTypeTask = 1
	// SendTypeCdk TODO
	SendTypeCdk = 2
	// SendTypeShareCode TODO
	SendTypeShareCode = 3

	// MotorcycleSendLuckyNumCacheKey TODO
	MotorcycleSendLuckyNumCacheKey = "hok_motorcycle_send_lucky_num_cache_key"
	// MotorcycleScheduledDrawInfoCacheKey TODO
	MotorcycleScheduledDrawInfoCacheKey = "hok_motorcycle_scheduled_draw_info_cache_key" // 定时开奖缓存
	// MotorcycleAllWinningRecordListCacheKey TODO
	MotorcycleAllWinningRecordListCacheKey = "hok_motorcycle_all_winning_record_list_cache_key" // 跑马灯信息缓存
	// MotorcyclePresentLanguageCacheKey TODO
	MotorcyclePresentLanguageCacheKey = "hok_motorcycle_present_language_cache_key" // 跑马灯信息缓存

	// TableNumTotalCount TODO
	TableNumTotalCount = int64(18000000)
	// CreateOnceNumber TODO
	// TableNumTotalCount = int64(180)
	CreateOnceNumber = int64(1800000)
	// TestLuckyNumbers TODO
	TestLuckyNumbers = int64(180)

	// MotorcyclePresentId 摩托车礼包
	MotorcyclePresentId = "Wand-20241208081259-Pc936a1e8af00"
	// MotorcycleGroupId TODO
	MotorcycleGroupId = "24e65108-0150-4bd3-9fbf-f761dfede6e8"

	// HOKGameId TODO
	HOKGameId = "29134"

	// FSourceID TODO
	FSourceID = "pageV3-1842"
)

var (
	gameProxy      = gamePb.NewGameClientProxy()
	presentV2Proxy = present_present_v2.NewPresentV2ClientProxy()
)

const (
	// NewUser TODO
	NewUser = iota + 1 // 表示新用户
	// UserReturned TODO
	UserReturned // 表示回流用户
	// UserAtRisk TODO
	UserAtRisk // 表示预流失用户
	// LowEngagementUser TODO
	LowEngagementUser // 表示低活用户
	// RegularUser TODO
	RegularUser // 表示普通用户
)

// WinningRecord TODO
type WinningRecord struct {
	NickName          string
	HeadUrl           string
	PresentId         string
	Timestamp         string
	PrizeMultilingual string // 奖品多语言
}

// ScheduledDrawItem TODO
type ScheduledDrawItem struct {
	UID         string `json:"uid"`          // 用户ID
	LuckyNumber string `json:"lucky_number"` // 开奖幸运号码
}

// SendLuckyNumbersParam TODO
type SendLuckyNumbersParam struct {
	SendCount int    // 发送数量
	SendType  int    // 发送类型;1任务，2cdk，3邀请码
	Remark    string // 记录信息
	UID       string // UID
}

// MotorcycleTaskResponse TODO
type MotorcycleTaskResponse struct {
	Code int          `json:"code"`
	Data ResponseData `json:"data"`
}

// ResponseData TODO
type ResponseData struct {
	TaskGroup TaskGroup `json:"taskgroup"`
}

// TaskGroup TODO
type TaskGroup struct {
	GroupTasks []GroupTask `json:"grouptasks"`
}

// GroupTask TODO
type GroupTask struct {
	TaskID   int      `json:"taskid"`
	TaskData TaskData `json:"taskdata"`
}

// TaskData TODO
type TaskData struct {
	ID         int    `json:"id"`
	Start      string `json:"start"`
	End        string `json:"end"`
	Target     int    `json:"target"`
	Progress   int    `json:"progress"`
	IsFinished bool   `json:"isfinished"`
	IsAwarded  bool   `json:"isawarded"`
	Ext        Ext    `json:"ext"`
	Status     int    `json:"status"`
}

// Ext TODO
type Ext struct {
	DataMoreID string `json:"datamoreid"`
	RuleID     string `json:"ruleid"`
}

var mux sync.Mutex

// ReportParam TODO
type ReportParam struct {
	OpenId        string
	RoleId        string
	AreaId        string
	LangType      string
	SubAction     string
	Action        string
	TaskId        string
	LotteryIndex  int32
	InviteeOpenId string // 被邀请人id
	CDK           string
}

// HOKMotorcycleCdkAlreadyRedeemedByUser 检查CDK用户是否已使用
func HOKMotorcycleCdkAlreadyRedeemedByUser(ctx context.Context, cdkey string) error {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	// 更新用户表
	tableName, err := model.GetTableNameWithAccount(ctx, &userAccount, hok.UserLuckyNumRecordModel{}.TableName())
	if err != nil {
		return err
	}
	var count int64
	err = DB.DefaultConnect().Table(tableName).Where("uid = ? and send_type = ? and remark = ?",
		userAccount.Uid, SendTypeCdk, cdkey).Count(&count).Error
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	if count > 0 {
		return errs.NewCustomError(ctx, code.HokTmpCurrentCDKHasUsed, "The current CDK has been used")
	}
	return nil
}

// motorcyclePresentReport 摩托车上报
func motorcyclePresentReport(ctx context.Context, reportParam ReportParam) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	langType := metadata.GetLangType(ctx)
	reportParam.LangType = langType
	if reportParam.OpenId == "" {
		reportParam.OpenId = userAccount.IntlAccount.OpenId
	}
	var tlogData report.ReportTlogData
	extentContent := buildExtentContent(reportParam)
	tlogData = buildTLogData(reportParam, extentContent)
	report.ReportTlog(ctx, tlogData)
}

func buildExtentContent(reportParam ReportParam) map[string]interface{} {
	extentContent := make(map[string]interface{})
	extentContent["openid"] = reportParam.OpenId
	extentContent["role_id"] = reportParam.RoleId
	extentContent["gift_id"] = reportParam.LotteryIndex
	extentContent["task_id"] = reportParam.TaskId
	extentContent["dst_openid"] = reportParam.TaskId
	extentContent["cdk"] = reportParam.CDK
	return extentContent
}

func buildTLogData(param ReportParam, extentContent map[string]interface{}) report.ReportTlogData {
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: param.LangType,
			XSource:   "pc_web",
			XAreaid:   param.AreaId,
		},
		Action:         param.Action,
		SubAction:      param.SubAction,
		OriginalGameId: "29134",
		ExtentContent:  extentContent,
	}
	return tlogData
}

// SendLuckyNumByTask 任务发放幸运号码
func SendLuckyNumByTask(ctx context.Context, sendCount int, taskId string) ([]string, error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	luckyNumbers, err := SendLuckyNumbers(ctx, SendLuckyNumbersParam{
		SendCount: sendCount,
		SendType:  SendTypeTask,
		Remark:    taskId,
		UID:       userAccount.Uid,
	})
	if err != nil {
		return nil, err
	}
	// 任务完成上报
	motorcyclePresentReport(ctx, ReportParam{
		OpenId: userAccount.IntlAccount.OpenId,
		Action: "motorcycle_backup_complete_task",
		TaskId: cast.ToString(taskId),
	})
	return luckyNumbers, nil
}

// SendLuckyNumByCDK CDK发放幸运号码
func SendLuckyNumByCDK(ctx context.Context, sendCount int, cdk string) ([]string, error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	luckyNumbers, err := SendLuckyNumbers(ctx, SendLuckyNumbersParam{
		SendCount: sendCount,
		SendType:  SendTypeCdk,
		Remark:    cdk,
		UID:       userAccount.Uid,
	})
	if err != nil {
		return nil, err
	}
	motorcyclePresentReport(ctx, ReportParam{
		OpenId: userAccount.IntlAccount.OpenId,
		Action: "motorcycle_backup_redeem_ret",
		CDK:    cdk,
	})
	return luckyNumbers, nil
}

// HOKMotorcycleLucyNumList 摩托车我的幸运号码列表
func HOKMotorcycleLucyNumList(ctx context.Context, pageNum, pageSize int) (*pb.HOKMotorcycleLucyNumListRsp, error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	langType := metadata.GetLangType(ctx)

	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}

	offset := (pageNum - 1) * pageSize
	tableName, err := model.GetTableNameWithAccount(ctx, &userAccount, hok.UserLuckyNumRecordModel{}.TableName())
	if err != nil {
		return nil, err
	}
	var userLuckyNumRecordList []hok.UserLuckyNumRecord
	if err = DB.DefaultConnect().Table(tableName).Where(
		"uid = ?", userAccount.Uid).Offset(offset).Limit(pageSize).
		Order("id desc").Find(&userLuckyNumRecordList).Error; err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	var count int64
	if err = DB.DefaultConnect().Table(tableName).Where(
		"uid = ?", userAccount.Uid).Count(&count).Error; err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	items := make([]*pb.GetLuckyNumItem, 0, len(userLuckyNumRecordList))
	for _, v := range userLuckyNumRecordList {
		winStatus := cast.ToBool(v.WinStatus)
		var prizeName, result string
		if winStatus {
			// 获取摩托车多语言
			languageCacheKey := getMotorcyclePresentLanguageCacheKey()
			result, err = redis.GetClient().Get(ctx, languageCacheKey).Result()
			if err == nil && result != "" {
				var languageType lipass_common.LanguageType
				// _ = proto.Unmarshal([]byte(result), &languageType)
				_ = json.Unmarshal([]byte(result), &languageType)
				prizeName = languageUtil.GetLangString(&languageType, langType, "en")
			}
		}
		items = append(items, &pb.GetLuckyNumItem{
			LuckyNum:  v.LuckyNum,
			WinStatus: winStatus,
			PrizeName: prizeName,
		})
	}
	return &pb.HOKMotorcycleLucyNumListRsp{
		GetLuckyNumList: items,
		Total:           count,
	}, nil
}

// HOKMotorcycleTaskOneClickCollection 摩托车任务一键领取
func HOKMotorcycleTaskOneClickCollection(ctx context.Context, fSourceId string, timeZone int32) ([]string, error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}

	// 游戏任务完成
	gameTagIdDoneMap := make(map[int]bool, 4)
	// 游戏任务领取
	gameTagIdClaimedMap := make(map[int]bool, 4)
	// 页面任务完成
	pageTagIdDoneMap := make(map[int]bool, 2)
	// 页面任务领取
	pageTagIdClaimedMap := make(map[int]bool, 2)

	taskIdTagIdMap := config.GetConfig().HOKMotorcycleEvents.TaskIdTagIdMap
	tagIdClaimedMap := config.GetConfig().HOKMotorcycleEvents.TagIdClaimedMap
	taskIdLuckNumMap := config.GetConfig().HOKMotorcycleEvents.TaskIdLuckNumMap
	// 页面任务列表
	pageTagIds := config.GetConfig().HOKMotorcycleEvents.PageTagIds
	// 游戏任务完成状态
	taskCompleteStatus, err := motorcycleTaskCompleteStatus(ctx, userAccount.IntlAccount.OpenId)
	if err != nil {
		return nil, err
	}

	// 当前用户任务完成列表
	currentTime := time.Now()
	duration, _ := time.ParseDuration(fmt.Sprintf("%vh", timeZone))
	currentTime = currentTime.Add(duration)
	todayDateStr := currentTime.Format("2006-01-02")
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   fSourceId,
		"storage_key":  storageKey,
		"status":       0,
	}

	dataList, err := baseLogic.GetAllData(ctx, condition)
	if err != nil {
		return nil, err
	}

	for tagId, claimedTagId := range tagIdClaimedMap {
		tagInt, err := cast.ToIntE(tagId)
		if err != nil {
			continue
		}
		exists, _ := object.InArray(tagInt, pageTagIds)
		for _, item := range dataList {
			// 已领取任务
			if cast.ToString(claimedTagId) == item.TagId {
				if todayDateStr == item.Fday.Format("2006-01-02") && item.TodayNum >= 1 {
					if exists {
						// 页面任务已领取
						pageTagIdClaimedMap[claimedTagId] = true
						continue
					} else {
						// 游戏任务已领取
						gameTagIdClaimedMap[claimedTagId] = true
						continue
					}
				}
			}
			// 已完成任务
			if cast.ToString(tagId) == item.TagId {
				if todayDateStr == item.Fday.Format("2006-01-02") && item.TodayNum >= 1 {
					if exists {
						// 页面任务已完成
						pageTagIdDoneMap[tagId] = true
						continue
					} else {
						// 游戏任务已完成
						gameTagIdDoneMap[tagId] = true
						continue
					}
				}
			}
		}
	}

	// 根据游戏任务完成状态记录任务完成
	var hasSendLuckNumCount int
	var taskStrList []string
	for taskId, taskDone := range taskCompleteStatus {
		if !taskDone {
			continue
		}
		// 游戏id tagid映射
		tagId, ok2 := taskIdTagIdMap[taskId]
		if !ok2 {
			continue
		}
		if done, ok := gameTagIdDoneMap[tagId]; ok && !done {
			// 任务未完成，完成任务
			addData := baseTotalModel.AddParamStruct{
				FsourceID:  fSourceId,
				Type:       2,
				Tag:        cast.ToString(tagId),
				StorageKey: storageKey,
				DayLimit:   1,
			}
			_, err = baseLogic.AddData(ctx, addData, timeZone)
			if err != nil {
				return nil, err
			}
		}
		collectTagId := tagIdClaimedMap[tagId]
		if done, ok := gameTagIdClaimedMap[collectTagId]; ok && done {
			// 已领取
			continue
		}
		// 完成领取任务
		addData2 := baseTotalModel.AddParamStruct{
			FsourceID:  fSourceId,
			Type:       2,
			Tag:        cast.ToString(collectTagId),
			StorageKey: storageKey,
			DayLimit:   1,
		}
		_, err = baseLogic.AddData(ctx, addData2, timeZone)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", "err", "m", "HOKMotorcycleTaskOneClickCollection", "uid", userAccount.Uid).
				Infof(
					"HOKMotorcycleTaskOneClickCollection Complete the claim task err; collectTagId：[%v]", collectTagId)
			return nil, err
		}
		// 获取幸运号码数量
		sendCount := taskIdLuckNumMap[taskId]
		hasSendLuckNumCount += sendCount
		taskStrList = append(taskStrList, cast.ToString(taskId))
	}

	// 页面任务完成状态
	for tagId, done := range pageTagIdDoneMap {
		if !done {
			continue
		}
		claimedTagId, ok := tagIdClaimedMap[tagId]
		if !ok {
			continue
		}
		if done2 := pageTagIdClaimedMap[claimedTagId]; done2 {
			// 已领取
			continue
		}
		// 已完成未领取, 完成领取任务
		addData2 := baseTotalModel.AddParamStruct{
			FsourceID:  fSourceId,
			Type:       2,
			Tag:        cast.ToString(claimedTagId),
			StorageKey: storageKey,
			DayLimit:   1,
		}
		_, err = baseLogic.AddData(ctx, addData2, timeZone)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", "err", "m", "HOKMotorcycleTaskOneClickCollection", "uid", userAccount.Uid).
				Infof(
					"HOKMotorcycleTaskOneClickCollection Complete the claim task err; claimedTagId：[%v]", claimedTagId)
			return nil, err
		}
		sendCount := taskIdLuckNumMap[claimedTagId]
		hasSendLuckNumCount += sendCount
	}

	// 发送幸运号码
	sendLuckyNumbers, err := SendLuckyNumbers(ctx, SendLuckyNumbersParam{
		SendCount: hasSendLuckNumCount,
		SendType:  SendTypeTask,
		Remark:    strings.Join(taskStrList, "_"),
		UID:       userAccount.Uid,
	})
	if err != nil {
		return nil, err
	}
	// 一键领取任务完成上报
	for _, v := range taskStrList {
		taskReportMap := map[string]string{
			"1002520": "1",
			"1002521": "2",
			"1002522": "3",
			"1002523": "4",
			"14":      "5",
			"15":      "6",
		}
		taskId, ok := taskReportMap[v]
		if !ok {
			continue
		}
		motorcyclePresentReport(ctx, ReportParam{
			OpenId: userAccount.IntlAccount.OpenId,
			Action: "motorcycle_backup_complete_task",
			TaskId: taskId,
		})
	}
	return sendLuckyNumbers, nil
}

// HOKMotorcycleTaskHasFinish 摩托车任务是否完成
func HOKMotorcycleTaskHasFinish(ctx context.Context, taskId int32) error {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	openId := userAccount.IntlAccount.OpenId
	taskCompleteStatus, err := motorcycleTaskCompleteStatus(ctx, openId)
	if err != nil {
		return err
	}
	hasData := taskCompleteStatus[int(taskId)]
	if !hasData {
		err = errs.NewCustomError(ctx, code.HokTmpMissionHasNotFinish, fmt.Sprintf(
			"has not  finish mission,taskId=%v", taskId))
		return err
	}
	return nil
}

// HOKMotorcycleDoneTaskList TODO
func HOKMotorcycleDoneTaskList(ctx context.Context) ([]string, error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}

	completeStatus, err := motorcycleTaskCompleteStatus(ctx, userAccount.IntlAccount.OpenId)
	if err != nil {
		return nil, err
	}
	var doneTaskList []string
	for taskId, done := range completeStatus {
		if done {
			doneTaskList = append(doneTaskList, cast.ToString(taskId))
		}
	}
	return doneTaskList, nil
}

// motorcycleTaskCompleteStatus 摩托车任务完成状态
func motorcycleTaskCompleteStatus(ctx context.Context, openId string) (map[int]bool, error) {

	taskStatusMap := make(map[int]bool)
	taskResponse, err := getUserTaskCompleteStatus(ctx, openId)
	if err != nil {
		return nil, err
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(
		"motorcycleTaskCompleteStatus getUserTaskCompleteStatus taskResponse：[%v]", taskResponse)
	log.WithFieldsContext(ctx, "log_type", "debug").Infof("motorcycleTaskCompleteStatus send success")
	for _, taskItem := range taskResponse.Data.TaskGroup.GroupTasks {
		taskStatusMap[taskItem.TaskID] = taskItem.TaskData.IsFinished
	}
	return taskStatusMap, nil
}

// ScheduledDrawInfo 查看定时开奖信息接口
func ScheduledDrawInfo(ctx context.Context) (bool, string, error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return false, "", err
	}
	lastWinningDetails, err := getLastWinningDetails(ctx)
	if err != nil {
		return false, "", err
	}
	var winPrize bool
	winPrize = lastWinningDetails.UID == userAccount.Uid
	return winPrize, lastWinningDetails.LuckyNumber, nil
}

// GetPreviousWinningRecords 分页查询往期中奖记录
func GetPreviousWinningRecords(ctx context.Context, pageNum, pageSize int) (*pb.GetPreviousWinningRecordsRsp, error) {
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	langType := metadata.GetLangType(ctx)
	winningItems := make([]*pb.PreviousWinningItem, 0)
	var pagedData []hok.WinningRecord
	winningRecords, err := getWinningRecords(ctx)
	if err != nil {
		return nil, err
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(
		"GetPreviousWinningRecords winningRecords：[%v]", winningRecords)
	data := PageSlice(winningRecords, pageSize, pageNum)
	if data != nil {
		pagedData = data.([]hok.WinningRecord)
		log.WithFieldsContext(ctx, "log_type", "debug").Infof(
			"GetPreviousWinningRecords PageSlice data：[%v]，data：[%v]", data, pagedData)
	}
	for _, v := range pagedData {
		headUrl, nickName, err1 := getNikeNameHeadUrlByUid(ctx, v.UID)
		if err1 != nil {
			log.WithFieldsContext(ctx, "log_type", "err1", "m", "GetPreviousWinningRecords").Infof(
				"GetPreviousWinningRecords getNikeNameHeadUrlByUid UID:[%v], err:[%v]", v.UID, err1)
			// continue
		}
		var prizeName string
		if v.PrizeMultilingual != "" {
			var languageType lipass_common.LanguageType
			_ = json.Unmarshal([]byte(v.PrizeMultilingual), &languageType)
			prizeName = languageUtil.GetLangString(&languageType, langType, "en")
		}
		winningItems = append(winningItems, &pb.PreviousWinningItem{
			Nickname:  nickName,
			HeadUrl:   headUrl,
			LuckyNum:  v.LuckyNumber,
			PrizeName: prizeName,
		})
	}
	return &pb.GetPreviousWinningRecordsRsp{
		PreviousWinningRecords: winningItems,
		Count:                  int64(len(winningRecords)),
	}, nil
}

// QueryCDKRedemptionListPage 分页查询CDK兑换列表
func QueryCDKRedemptionListPage(ctx context.Context, pageNum, pageSize int) ([]hok.UserLuckyNumRecord, int64, error) {
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, 0, err
	}
	offset := (pageNum - 1) * pageSize
	tableName, err := model.GetTableNameWithAccount(ctx, &userAccount, hok.UserLuckyNumRecordModel{}.TableName())
	if err != nil {
		return nil, 0, err
	}
	var userLuckyNumRecordList []hok.UserLuckyNumRecord
	if err = DB.DefaultConnect().Table(tableName).Where(
		"uid = ? and send_type = ?", userAccount.Uid, SendTypeCdk).Offset(offset).Limit(pageSize).
		Order("id desc").Find(&userLuckyNumRecordList).Error; err != nil {
		return nil, 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	var count int64
	if err = DB.DefaultConnect().Table(tableName).Where(
		"uid = ? and send_type = ?", userAccount.Uid, SendTypeCdk).Count(&count).Error; err != nil {
		return nil, 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return userLuckyNumRecordList, count, nil
}

// GrantLuckyNumberByInvitationCode 根据邀请码使用人 账号状态 给双方发放幸运号码
func GrantLuckyNumberByInvitationCode(ctx context.Context, shareCode, fSourceId string) ([]string, error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}

	tableName, err := util.GetShareTableName(ctx, fSourceId)
	if err != nil {
		return nil, err
	}
	where := invitation.Invitation{
		InviteeUid:         userAccount.Uid,
		InviteeAccountType: int32(userAccount.AccountType),
		FsourceId:          fSourceId,
		IsDelete:           0,
	}
	var invitationItem invitation.Invitation
	firstDb := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(&where).First(&invitationItem)
	if firstDb.Error != nil && !errors.Is(firstDb.Error, gorm.ErrRecordNotFound) {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"isHas db error, \t [Error]:{%v} ", firstDb.Error)
	}
	// 未被邀请
	if invitationItem.ID == 0 {
		return nil, errs.NewCustomError(ctx, code.HokTmpInvitationStatusException, "The current user is not invited")
	}

	// 获取用户账号状态
	dimension, err := checkAccountStatusByDimension(ctx, userAccount.IntlAccount.OpenId)
	if err != nil {
		return nil, err
	}

	// 发放幸运码
	exchangeUserTypeMap := config.GetConfig().HOKMotorcycleEvents.ExchangeUserTypeMap
	var luckyNumbers []string
	currentUser := SendLuckyNumbersParam{
		SendType: SendTypeShareCode,
		Remark:   shareCode,
		UID:      userAccount.Uid,
	}
	inviterUser := SendLuckyNumbersParam{
		SendType: SendTypeShareCode,
		Remark:   shareCode,
		UID:      invitationItem.UID,
	}
	switch dimension {
	case NewUser:
		sendCount := exchangeUserTypeMap[NewUser]
		currentUser.SendCount = sendCount
		inviterUser.SendCount = sendCount
		luckyNumbers, err = SendLuckyNumbers(ctx, currentUser)
		if err != nil {
			return nil, err
		}
		_, err = SendLuckyNumbers(ctx, inviterUser)
		if err != nil {
			return nil, err
		}
	case UserReturned:
		sendCount := exchangeUserTypeMap[UserReturned]
		currentUser.SendCount = sendCount
		inviterUser.SendCount = sendCount
		luckyNumbers, err = SendLuckyNumbers(ctx, currentUser)
		if err != nil {
			return nil, err
		}
		_, err = SendLuckyNumbers(ctx, inviterUser)
		if err != nil {
			return nil, err
		}
	case UserAtRisk:
		sendCount := exchangeUserTypeMap[UserAtRisk]
		currentUser.SendCount = sendCount
		inviterUser.SendCount = sendCount
		luckyNumbers, err = SendLuckyNumbers(ctx, currentUser)
		if err != nil {
			return nil, err
		}
		_, err = SendLuckyNumbers(ctx, inviterUser)
		if err != nil {
			return nil, err
		}
	case LowEngagementUser:
		sendCount := exchangeUserTypeMap[LowEngagementUser]
		currentUser.SendCount = sendCount
		inviterUser.SendCount = sendCount
		luckyNumbers, err = SendLuckyNumbers(ctx, currentUser)
		if err != nil {
			return nil, err
		}
		_, err = SendLuckyNumbers(ctx, inviterUser)
		if err != nil {
			return nil, err
		}
	case RegularUser:
		sendCount := exchangeUserTypeMap[RegularUser]
		currentUser.SendCount = sendCount
		inviterUser.SendCount = sendCount
		luckyNumbers, err = SendLuckyNumbers(ctx, currentUser)
		if err != nil {
			return nil, err
		}
		_, err = SendLuckyNumbers(ctx, inviterUser)
		if err != nil {
			return nil, err
		}
	default:
		sendCount := 1
		currentUser.SendCount = sendCount
		inviterUser.SendCount = sendCount
		luckyNumbers, err = SendLuckyNumbers(ctx, currentUser)
		if err != nil {
			return nil, err
		}
		_, err = SendLuckyNumbers(ctx, inviterUser)
		if err != nil {
			return nil, err
		}
	}
	// 邀请上报
	var openId string
	split := strings.Split(invitationItem.UID, "-")
	if len(split) == 2 {
		openId = split[1]
	}
	motorcyclePresentReport(ctx, ReportParam{
		Action:        "motorcycle_backup_invite_per",
		OpenId:        openId,
		InviteeOpenId: userAccount.IntlAccount.OpenId,
	})
	return luckyNumbers, nil
}

// checkAccountStatusByDimension 根据维度信息判断当前用户账号状态
func checkAccountStatusByDimension(ctx context.Context, openId string) (int, error) {

	// |新用户|在活动时间段内注册的新用户（不需要到活动页）|
	// |回流用户|14天内未登录游戏|
	// |预流失用户|活动开始前14天内玩家活跃1天|
	// |低活用户|活动开始前14天内玩家活跃2或3天|
	// |普通用户|非新进之外的其他用户（排除上述规则）|

	// 活动开始时间戳
	activityStartTimeStamp := config.GetConfig().HOKMotorcycleEvents.ActivityStartTimeStamp
	daysDifference := common.DaysDifference(activityStartTimeStamp)
	dimensionInformation, err := getPlayerDimensionInformation(ctx, openId)
	if err != nil {
		// 报错返回 0
		return 0, nil
	}
	if len(dimensionInformation) <= daysDifference {
		// 新用户
		return NewUser, nil
	}
	// 14天内未登录游戏
	if len(dimensionInformation) >= 14 {
		list := dimensionInformation[:14]
		exists, _ := object.InArray(int8(1), list)
		if !exists {
			return UserReturned, nil
		}
	}
	// 活动开始前14天内玩家活跃1天
	if len(dimensionInformation) >= 14+daysDifference {
		list := dimensionInformation[daysDifference : daysDifference+14]
		_, oneCount := countZerosAndOnes(list)
		if oneCount == 1 {
			return UserAtRisk, nil
		}
		if oneCount == 2 || oneCount == 3 {
			return LowEngagementUser, nil
		}
	}

	// 普通用户
	return RegularUser, nil
}

// CheckIfDrawWonGrandPrize 开奖是否中大奖
func CheckIfDrawWonGrandPrize(ctx context.Context) (bool, error) {
	var isWinner bool
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return isWinner, err
	}
	// 获取中奖记录
	now := time.Now().Unix()
	var winningRecordList []hok.WinningRecord
	if err = DB.DefaultConnect().Table(hok.WinningRecordModel{}.TableName()).Where(
		"present_id = ? and created_at < ?", MotorcyclePresentId, now).Order("id desc").
		Find(&winningRecordList).Error; err != nil {
		return isWinner, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	for _, winningRecord := range winningRecordList {
		if userAccount.Uid == winningRecord.UID {
			isWinner = true
			break
		}
	}
	return isWinner, nil
}

// CheckIfLastDrawWonGrandPrize 上一次开奖是否中大奖/未中奖报错 + 抽奖机会
func CheckIfLastDrawWonGrandPrize(ctx context.Context) error {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	// 获取中奖记录
	winningDetails, err := getLastWinningDetails(ctx)
	if err != nil {
		return err
	}
	if userAccount.Uid != winningDetails.UID {
		return errs.NewCustomError(ctx, code.HokTmpUserNotWinLastTime, "User did not win the last draw")
	}
	return nil
}

// getLastWinningDetails 获取上次中奖的UID和幸运号码
func getLastWinningDetails(ctx context.Context) (ScheduledDrawItem, error) {
	// 获取中奖记录
	drawingTime := config.GetConfig().HOKMotorcycleEvents.DrawingTime
	drawingTimeZone := config.GetConfig().HOKMotorcycleEvents.DrawingTimeZone
	now := time.Now().Unix()
	timestamp, _ := common.GenerateTimestamp(drawingTime, drawingTimeZone)
	if now+1 < timestamp {
		timestamp, _ = common.GenerateYesterdayTimestamp(drawingTime, drawingTimeZone)
	}
	cacheKey := getScheduledDrawInfoRedisKey(cast.ToString(timestamp))
	result, err := redis.GetClient().Get(ctx, cacheKey).Result()
	scheduledDrawItem := ScheduledDrawItem{}
	if err == nil {
		_ = json.Unmarshal([]byte(result), &scheduledDrawItem)
	}
	if scheduledDrawItem.UID == "" {
		// 查表
		var winningRecord hok.WinningRecord
		if err = DB.DefaultConnect().Table(hok.WinningRecordModel{}.TableName()).Select("uid, lucky_number").Where(
			"present_id = ? and created_at <= ?", MotorcyclePresentId, now).Order("id desc").Limit(1).
			Find(&winningRecord).Error; err != nil {
			return scheduledDrawItem, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
		}
		scheduledDrawItem.UID = winningRecord.UID
		// 写入缓存
		if scheduledDrawItem.UID != "" && scheduledDrawItem.LuckyNumber != "" {
			var scheduledDrawItemStr []byte
			scheduledDrawItemStr, err = json.Marshal(scheduledDrawItem)
			if err == nil {
				redis.GetClient().SetEX(ctx, cacheKey, string(scheduledDrawItemStr), 48*time.Hour)
			}
		}
	}
	return scheduledDrawItem, nil
}

// getPlayerDimensionInformation 获取玩家维度数据
func getPlayerDimensionInformation(ctx context.Context, openId string) ([]int8, error) {

	regionConfRsp, err := gameProxy.GetGameRegionConf(ctx, &gamePb.GetGameRegionConfReq{
		GameId: HOKGameId,
	})
	if err != nil {
		return nil, err
	}
	zoneIdNum := regionConfRsp.ZoneId
	zoneId := cast.ToString(zoneIdNum)
	if zoneId == "" || zoneId == "0" {
		return nil, errs.NewCustomError(ctx, code.HokTmpGetGameRegionConfDataError, "Game Region Conf Data Error")
	}

	dimensionHost := config.GetConfig().HOKMotorcycleEvents.CapturePlayerDimensionHost
	dimensionPath := config.GetConfig().HOKMotorcycleEvents.CapturePlayerDimensionPath
	// url := "http://*************:2002/trpc.sgamesvr.sgamerec_gateway.SGameRecGateway/RecProxy"
	url := strings.Join([]string{dimensionHost, dimensionPath}, "")
	uuid, _ := common.GenerateUUID()
	header := map[string]string{
		"Content-Type": "application/x-rec-bytes",
		"X-Bid":        "motorcycle_activity",
		"trpc-caller":  "PAActivity",
		"X-Ext-Uid":    openId,
		"X-Request-Id": uuid,
	}
	getFeaturesRequestReq := &feature_platform_pb.GetFeaturesRequest{
		Requests: []*feature_platform_pb.Request{
			{
				Type: feature_platform_pb.EntityType_RequestEntityType_Openid_Zoneid,
				EntityInfo: &feature_platform_pb.EntityInfo{
					OpenId: openId,
					ZoneId: zoneId,
				},
			},
		},
	}

	// 序列化
	marshal, err := proto.Marshal(getFeaturesRequestReq)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("proto.Marshal err: %v", err))
		return nil, err
	}
	// marshal, _ := json.Marshal(req)
	// log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("header: %v", header))
	data := httpclient.ClientOption{
		URL: url,
		// Timeout: 2 * time.Second,
		Header:     header,
		Type:       "POST",
		PostString: string(marshal),
	}
	result := httpclient.RequestOne(ctx, data)
	if result.RequestError != nil {
		// 请求失败
		log.WithFieldsContext(ctx, "log_type", "err", "uuid", uuid).Infof("getPlayerDimensionInformation err data: %v", data)
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.HokTmpHttpError, "http error, \t [Error]:{%v} ",
			result.RequestError)
	}
	response := result.Result
	var getFeaturesResponse feature_platform_pb.GetFeaturesResponse
	if err = proto.Unmarshal([]byte(response), &getFeaturesResponse); err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "uuid", uuid).Infof(
			"getPlayerDimensionInformation proto Unmarshal err: %v", err)
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.HokTmpHttpError, "http error, \t [Error]:{%v} ", err)
	}
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "getPlayerDimensionInformation").Infof(
		"getPlayerDimensionInformation success response: %v,getFeaturesResponse:[%v]", response,
		getFeaturesResponse.Responses)
	var stringVal string
	if len(getFeaturesResponse.Responses) != 0 {
		features := getFeaturesResponse.Responses[0].Features
		if value, ok := features["player.motorcycle_activity_bitmap"]; ok {
			// feature_platform_pb.Value_StringVal
			stringVal = value.GetStringVal()
		}
		bytes, err := json.Marshal(getFeaturesResponse.Responses)
		if err != nil {
			return nil, err
		}
		log.WithFieldsContext(ctx, "log_type", "end", "m", "getPlayerDimensionInformation").Infof(
			"getPlayerDimensionInformation show Responses: %v,err:[%v],", string(bytes), err)
	} else {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.HokTmpHttpError,
			"getPlayerDimensionInformation err, \t [Error]:{%v} ", err)
	}
	dynamicDayList := make([]int8, 0, len(stringVal))
	for _, char := range stringVal {
		// dynamicDayList = append(dynamicDayList, int8(char))
		// 将字符 '0' 或 '1' 转换为 int8
		if char == '0' {
			dynamicDayList = append(dynamicDayList, 0)
		} else if char == '1' {
			dynamicDayList = append(dynamicDayList, 1)
		} else {
			dynamicDayList = append(dynamicDayList, 0)
		}
	}
	log.WithFieldsContext(ctx, "log_type", "end", "m", "getPlayerDimensionInformation").Infof(
		"getPlayerDimensionInformation end success stringVal: %v,openId:[%v],dynamicDayList:[%v]", stringVal, openId,
		dynamicDayList)

	return dynamicDayList, nil
}

// getUserTaskCompleteStatus 获取用户任务完成状态
func getUserTaskCompleteStatus(ctx context.Context, openid string) (MotorcycleTaskResponse, error) {

	var motorcycleTaskResponse MotorcycleTaskResponse
	regionConfRsp, err := gameProxy.GetGameRegionConf(ctx, &gamePb.GetGameRegionConfReq{
		GameId: HOKGameId,
	})
	if err != nil {
		return motorcycleTaskResponse, err
	}
	zoneIdNum := regionConfRsp.ZoneId
	zoneId := cast.ToString(zoneIdNum)
	if zoneId == "" || zoneId == "0" {
		return motorcycleTaskResponse, errs.NewCustomError(ctx, code.HokTmpGetGameRegionConfDataError,
			"Game Region Conf Data Error")
	}

	motorcycleEvents := config.GetConfig().HOKMotorcycleEvents
	completeStatusService := motorcycleEvents.GetTaskCompleteStatusService
	completeStatusNamespace := motorcycleEvents.GetTaskCompleteStatusNamespace
	path := motorcycleEvents.GetTaskCompleteStatusPath
	groupId := motorcycleEvents.TaskGroupId
	accType := motorcycleEvents.GetTaskCompleteStatusAccType
	// url := "http://10.9.0.79:8000/task/getgrouptask"
	port, err := util.GetServer(completeStatusService, completeStatusNamespace)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "HOKMotorcycleTaskCompleteStatus").Infof(
			"HOKMotorcycleTaskCompleteStatus err:[%v]", err)
	}
	url := strings.Join([]string{"http://", port, path}, "")
	postData := map[string]interface{}{
		"app": "hoktasks",
		"user": map[string]interface{}{
			"uin":          openid,
			"area_id":      zoneId,
			"role_id":      "",
			"platform":     "1",
			"acc_type":     accType,
			"partition_id": zoneId,
			"ext": map[string]interface{}{
				"newroleid":     strings.Join([]string{zoneId, openid}, "_"),
				"origin_areaid": zoneId,
			},
		},
		"groupid": groupId,
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	data := httpclient.ClientOption{
		URL: url,
		// Timeout: 2 * time.Second,
		Header:   header,
		Type:     "POST",
		PostData: postData,
	}
	result := httpclient.RequestOne(ctx, data)
	if result.RequestError != nil {
		// 请求失败
		log.WithFieldsContext(ctx, "log_type", "error").Infof("getUserTaskCompleteStatus err data: %v", data)
		return motorcycleTaskResponse, errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"http error, \t [Error]:{%v} ", result.RequestError)
	}
	response := result.Result
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "getUserTaskCompleteStatus").Infof(
		"getUserTaskCompleteStatus success response: %v", response)

	if err = json.Unmarshal([]byte(response), &motorcycleTaskResponse); err != nil {
		return motorcycleTaskResponse, errs.NewCustomError(ctx, code.HokTmpDataUnmarshalError, "Data Unmarshal Error err",
			err)
	}

	return motorcycleTaskResponse, nil
}

// ScheduledTaskLottery 定时任务开奖接口
func ScheduledTaskLottery(ctx context.Context) error {
	unix := time.Now().Unix()
	// 2025-01-11 22:00:05
	if unix >= 1736604005 {
		return nil
	}
	// 添加redis限频
	redisClient := redis.GetClient()
	rateLimiter := util.NewRedisRateLimiter(redisClient, 1, 2*time.Minute)
	redisKey := global.GetRedisKey("ScheduledTaskLottery_RedisRateLimiter")
	if allow, err := rateLimiter.Allow(ctx, redisKey); err != nil {
		return errs.NewCustomError(ctx, code.RedisAllowReturnError, "Redis AllowReturn Error;err:[%v]", err)
	} else if !allow {
		return errs.NewCustomError(ctx, code.RequestsTooFrequent, "Requests Too Frequent")
	}

	// 获取总表数据
	luckyNumberConfig, err := getLuckyNumberConfig(ctx)
	if err != nil {
		return err
	}
	// 获取当前已发放的幸运号码数量
	currentSendCount, err := getIssuedLuckyNumberCount(ctx, luckyNumberConfig)
	if err != nil {
		return err
	}
	if currentSendCount == 0 {
		return errs.NewCustomError(ctx, code.GetIssuedLuckyNumberCountError, "get Issued Lucky Number Count err")
	}
	// 开奖
	_, subTableIndex, subTableName, err := DrawLottery(ctx, currentSendCount)
	if err != nil {
		return err
	}
	// 更新幸运码发送状态用户中奖状态和开奖记录和redis跑马灯
	if err = UpdateLuckyNumberStatus(ctx, subTableName, subTableIndex); err != nil {
		return err
	}
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "ScheduledTaskLottery").
		Infof("ScheduledTaskLottery end success")

	return nil
}

// GetHomepageMarqueeData 获取跑马灯数据
func GetHomepageMarqueeData(ctx context.Context) (*pb.AllWinningRecordListRsp, error) {
	// 时间小于活动第一天开奖时间则不返回跑马灯数据
	activityStartTimeStamp := config.GetConfig().HOKMotorcycleEvents.ActivityStartTimeStamp
	now := time.Now().Unix()
	if now < activityStartTimeStamp {
		return &pb.AllWinningRecordListRsp{}, nil
	}
	langType := metadata.GetLangType(ctx)
	winningRecordListCacheKey := getAllWinningRecordListRedisKey()

	result, err := redis.GetClient().LRange(ctx, winningRecordListCacheKey, 0, -1).Result()
	if err != nil && !errors.Is(err, redisOrigin.Nil) {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
			"GetHomepageMarqueeData redis get error, \t [Error]:{%v}", err)
	}
	winningRecordItem := make([]*pb.WinningRecordItem, 0, len(result))
	for _, v := range result {
		var winningRecord WinningRecord
		_ = json.Unmarshal([]byte(v), &winningRecord)
		// 开奖时间
		drawingTime := config.GetConfig().HOKMotorcycleEvents.DrawingTime
		drawingTimeZone := config.GetConfig().HOKMotorcycleEvents.DrawingTimeZone
		timestamp, _ := common.GenerateTimestamp(drawingTime, drawingTimeZone)
		if winningRecord.PresentId == MotorcyclePresentId && timestamp != 0 && cast.ToInt64(
			winningRecord.Timestamp) > timestamp {
			// 如果奖品为摩托车且当前时间小于开奖时间则不展示
			continue
		}
		var prizeName string
		var languageType lipass_common.LanguageType
		if err = json.Unmarshal([]byte(winningRecord.PrizeMultilingual), &languageType); err != nil {
			str, _ := json.Marshal(&languageType)
			log.WithFieldsContext(ctx, "log_type", "proto_err", "m", "GetHomepageMarqueeData").
				Infof("GetHomepageMarqueeData Unmarshal err;data:[%v];prizeName:[%v];languageType:[%v]",
					v, prizeName, string(str))
		}
		prizeName = languageUtil.GetLangString(&languageType, langType, "en")
		log.WithFieldsContext(ctx, "log_type", "proto_err", "m", "GetHomepageMarqueeData").
			Infof(
				"GetHomepageMarqueeData winningRecordItem list data;data:[%v];prizeName:[%v],languageType.Zh_TW:[%v],langType:[%v]",
				v, prizeName, languageType.Zh_TW, langType)
		winningRecordItem = append(winningRecordItem, &pb.WinningRecordItem{
			Nickname:  winningRecord.NickName,
			HeadUrl:   winningRecord.HeadUrl,
			PrizeName: prizeName,
		})
	}
	// log result
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "GetHomepageMarqueeData").
		Infof("GetHomepageMarqueeData return success show result: [%v],winningRecordItem：[%v], len:winningRecordItem[%v]",
			result, winningRecordItem, len(winningRecordItem))
	return &pb.AllWinningRecordListRsp{
		AllWinningRecordList: winningRecordItem,
	}, nil
}

// UpdateLuckyNumberStatus 更新幸运码发送状态
func UpdateLuckyNumberStatus(ctx context.Context, subTableName string, subTableIndex uint64) error {

	luckyNumberLogInfo, err := getCurrentLuckyNumberInfo(ctx, subTableName, subTableIndex)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "error", "m", "UpdateLuckyNumberStatus").
			Infof("UpdateLuckyNumberStatus getCurrentLuckyNumberInfo err: subTableName：[%v],subTableIndex：[%v],err:[%v]",
				subTableName, subTableIndex, err)
		return err
	}
	// 更新幸运码子表
	if err = DB.DefaultConnect().Table(subTableName).Where("id = ?", subTableIndex).
		Update("send_status", 1).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	// 更新用户信息表
	userAccount := userAccountPb.UserAccount{
		Uid:         luckyNumberLogInfo.UID,
		AccountType: userAccountPb.AccountType_INTL,
	}
	tableName, err := model.GetTableNameWithAccount(ctx, &userAccount, hok.UserLuckyNumRecordModel{}.TableName())
	if err != nil {
		return err
	}
	// 开奖时间
	drawingTime := config.GetConfig().HOKMotorcycleEvents.DrawingTime
	drawingTimeZone := config.GetConfig().HOKMotorcycleEvents.DrawingTimeZone
	timestamp, err := common.GenerateTimestamp(drawingTime, drawingTimeZone)
	if err != nil {
		return err
	}
	userLuckyNumRecord := hok.UserLuckyNumRecord{
		WinStatus: 1,
		WinningAt: uint32(timestamp),
	}

	if err = DB.DefaultConnect().Table(tableName).Where(
		"uid = ? and lucky_num = ?", luckyNumberLogInfo.UID, luckyNumberLogInfo.LuckyNumber).
		Updates(userLuckyNumRecord).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"UpdateLuckyNumberStatus db error, \t [Error]:{%v} ", err)
	}

	// 发送摩托车礼包
	motorcyclePresentLanguage, err := SendMotorcyclePresent(ctx, luckyNumberLogInfo.UID)
	if err != nil {
		return err
	}
	// encodedData := base64.StdEncoding.EncodeToString([]byte(motorcyclePresentLanguage))
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "UpdateLuckyNumberStatus").Infof(
		"UpdateLuckyNumberStatus SendMotorcyclePresent show rsp; encodedData：[%v]", motorcyclePresentLanguage)
	// 写入开奖记录表
	winningRecord := hok.WinningRecord{
		UID:               luckyNumberLogInfo.UID,
		AccountType:       int16(userAccountPb.AccountType_INTL),
		PresentID:         MotorcyclePresentId,
		LuckyNumber:       luckyNumberLogInfo.LuckyNumber,
		PrizeMultilingual: motorcyclePresentLanguage, // 奖品多语言
	}
	winningRecord.CreatedAt = timestamp
	winningRecord.UpdatedAt = time.Now().Unix()

	if err = DB.DefaultConnect().Table(hok.WinningRecordModel{}.TableName()).
		Create(&winningRecord).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"UpdateLuckyNumberStatus db error, \t [Error]:{%v} ", err)
	}
	// 今日开奖写入缓存
	scheduledDrawInfoRedisKey := getScheduledDrawInfoRedisKey(cast.ToString(timestamp))
	scheduledDrawItem := ScheduledDrawItem{
		UID:         luckyNumberLogInfo.UID,
		LuckyNumber: luckyNumberLogInfo.LuckyNumber,
	}
	scheduledDrawItemStr, err := json.Marshal(scheduledDrawItem)
	if err != nil {
		return errs.NewCustomError(ctx, code.JsonParseError, "Json Marshal Error;scheduledDrawItem:[%v]", scheduledDrawItem)
	}

	if err = redis.GetClient().SetEX(ctx, scheduledDrawInfoRedisKey, scheduledDrawItemStr, 48*time.Hour).
		Err(); err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
			"UpdateLuckyNumberStatus redis SetEX error, \t [Error]:{%v} ", err)
	}
	err = prizeWrittenRunningLantern(ctx, luckyNumberLogInfo.UID, MotorcyclePresentId, motorcyclePresentLanguage,
		cast.ToString(timestamp))
	if err != nil {
		return err
	}
	return nil
}

// getNikeNameHeadUrlByUid 获取用户昵称,头像
func getNikeNameHeadUrlByUid(ctx context.Context, uid string) (string, string, error) {

	openID := strings.Split(uid, "-")[1]
	accountData, _ := proto.Marshal(&userAccountPb.UserAccount{
		Uid:         uid,
		AccountType: userAccountPb.AccountType_INTL,
		IntlAccount: &userAccountPb.IntlAccount{
			OpenId:    openID,
			ChannelId: 3,
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	regionConfRsp, err := gameProxy.GetGameRegionConf(ctx, &gamePb.GetGameRegionConfReq{
		GameId: HOKGameId,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "getNikeNameHeadUrlByUid").Infof(
			"getNikeNameHeadUrlByUid GetGameRegionConf, err:[%v]", err)
		// return "", "", err
	}
	proxy := gameHokPb.NewHokClientProxy()
	moreRoleInfo, err := proxy.GetHOKMoreRoleInfo(ctx, &gamePb.GetRoleInfoReq{
		GameId: HOKGameId,
		AreaId: int64(regionConfRsp.GetAreaId()),
		ZoneId: int64(regionConfRsp.GetZoneId()),
		// PlatId:    int64(regionConfRsp.GetPlatId()),
		// Partition: 0,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "error", "m", "getNikeNameHeadUrlByUid").Infof(
			"getNikeNameHeadUrlByUid GetHOKMoreRoleInfo err;uid:[%v]; regionConfRsp:[%v], err:[%v]", uid, regionConfRsp, err)
		// return "", "", err
	}
	headUrl := getHokHeadUrl(moreRoleInfo.GetHeadUrl())
	nickName := decodeURIComponent(moreRoleInfo.GetRoleInfo().GetRoleName())
	return headUrl, nickName, nil
}

// DrawLottery 开奖
func DrawLottery(ctx context.Context, currentSendCount int64) (string, uint64, string, error) {

	// 获取总表数据
	luckyNumberConfig, err := getLuckyNumberConfig(ctx)
	if err != nil {
		return "", 0, "", err
	}
	// 获取中奖记录表
	winningRecords, err := getWinningRecords(ctx)
	if err != nil {
		return "", 0, "", err
	}
	winningUIDMap := make(map[string]struct{}, len(winningRecords))
	luckyNumberList := make([]string, 0, len(winningRecords))
	for _, v := range winningRecords {
		winningUIDMap[v.UID] = struct{}{}
		luckyNumberList = append(luckyNumberList, v.LuckyNumber)
	}

	maxAttempts := config.GetConfig().HOKMotorcycleEvents.MaxAttempts
	specifyWinningUID := config.GetConfig().HOKMotorcycleEvents.SpecifyWinningUID // todo 测试指定开奖uid
	for i := 0; i < maxAttempts; i++ {
		// 开奖 获取随机数并判断该用户是否已获奖/已中过奖需要重新开奖
		var randomIntInRange int64
		randomIntInRange, err = common.RandomIntInRange(currentSendCount)
		if err != nil {
			return "", 0, "", errs.NewCustomError(ctx, code.RandomIntInRangeError, "max must be greater than or equal to 1")
		}
		// 根据表名称和索引获取当前开奖的幸运号码信息
		subTableName, subTableIndex := getTableNameAndIndexByCount(randomIntInRange-1, luckyNumberConfig)
		if subTableIndex == 0 || subTableName == "" {
			continue
		}
		var luckyNumber string
		var sendStatus int
		var luckyNumberLogInfo hok.LuckyNumberLog
		luckyNumberLogInfo, err = getCurrentLuckyNumberInfo(ctx, subTableName, subTableIndex)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", "error", "m", "DrawLottery").
				Infof("DrawLottery getCurrentLuckyNumberInfo err: subTableName：[%v],subTableIndex：[%v],err:[%v]",
					subTableName, subTableIndex, err)
			continue
		}
		luckyNumber, sendStatus = luckyNumberLogInfo.LuckyNumber, luckyNumberLogInfo.SendStatus
		if luckyNumber == "" || sendStatus == 0 {
			log.WithFieldsContext(ctx, "log_type", "info", "m", "DrawLottery").
				Infof("DrawLottery getCurrentLuckyNumberInfo has send: subTableName：[%v],subTableIndex：[%v]",
					subTableName, subTableIndex)
			continue
		}

		if exists, _ := object.InArray(luckyNumber, luckyNumberList); exists {
			log.WithFieldsContext(ctx, "log_type", "debug", "m", "DrawLottery").
				Infof("DrawLottery getCurrentLuckyNumberInfo The current number has already won prizes")
			continue
		}
		if specifyWinningUID != "" {
			// todo 用于指定中奖uid
			if luckyNumberLogInfo.UID != specifyWinningUID {
				continue
			}
		} else {
			if _, ok := winningUIDMap[luckyNumberLogInfo.UID]; ok {
				// 当前用户已中过将
				continue
			}
		}
		log.WithFieldsContext(ctx, "log_type", "debug", "status", "success", "m", "DrawLottery").
			Infof(
				"DrawLottery getCurrentLuckyNumberInfo sucess: subTableName：[%v],subTableIndex：[%v],randomIntInRange：[%v],luckyNumber：[%v]",
				subTableName, subTableIndex, randomIntInRange-1, luckyNumber)
		// 返回本次开奖的幸运码
		return luckyNumber, subTableIndex, subTableName, nil
	}
	return "", 0, "", errs.NewCustomError(ctx, code.DrawLotteryError, "No lucky number was obtained in this drawing")
}

// getTableNameAndIndexByCount 根据count获取对应表名称和索引
func getTableNameAndIndexByCount(count int64, luckyNumberConfig []hok.LuckyNumberConfig) (string, uint64) {

	// 计算出在哪张子表中, count从0开始
	subTableSequentialValue := count / TableNumTotalCount
	subTableName := getSubTableName(luckyNumberConfig[subTableSequentialValue].NumberSegment)
	// 计算在子表中的索引
	subTableIndex := uint64(count%TableNumTotalCount) + 1

	return subTableName, subTableIndex
}

// getCurrentLuckyNumberInfo 获取当前开奖的幸运号码信息
func getCurrentLuckyNumberInfo(ctx context.Context, subTableName string, subTableIndex uint64) (hok.LuckyNumberLog,
	error) {

	var luckyNumberInfo hok.LuckyNumberLog
	if err := DB.DefaultConnect().Table(subTableName).Select("lucky_number, send_status, uid, account_type").
		Where("id = ?", subTableIndex).First(&luckyNumberInfo).Error; err != nil {
		return luckyNumberInfo, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return luckyNumberInfo, nil
}

// getWinningRecords 获取中奖记录表数据 倒序
func getWinningRecords(ctx context.Context) ([]hok.WinningRecord, error) {

	var winningRecordList []hok.WinningRecord
	if err := DB.DefaultConnect().Table(hok.WinningRecordModel{}.TableName()).
		Where("present_id = ?", MotorcyclePresentId).Order("id desc").
		Find(&winningRecordList).Error; err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return winningRecordList, nil
}

// SendLuckyNumbers 发放幸运号码
func SendLuckyNumbers(ctx context.Context, param SendLuckyNumbersParam) ([]string, error) {

	userAccount := userAccountPb.UserAccount{
		Uid:         param.UID,
		AccountType: userAccountPb.AccountType_INTL,
	}

	sendCount := param.SendCount
	sendType := param.SendType
	remark := param.Remark
	if sendCount <= 0 {
		return nil, errs.NewCustomError(ctx, code.LuckyNumbersSentNumError, "The number of lucky numbers sent is abnormal")
	}

	numbersIndexAndTableNames, err := getLuckyNumbersIndexAndTableNames(ctx, sendCount)
	if err != nil {
		return nil, err
	}
	// 更新幸运号码的发送状态和领取的用户 UID
	var luckyNumList []string
	now := time.Now().Unix()
	for tableName, tableIdxList := range numbersIndexAndTableNames {
		updateData := map[string]interface{}{
			"send_status":  1,
			"uid":          userAccount.Uid,
			"account_type": userAccount.AccountType,
			"updated_at":   now,
		}
		if err = DB.DefaultConnect().Table(tableName).Where("id in (?)", tableIdxList).
			Updates(updateData).Error; err != nil {
			log.WithFieldsContext(ctx, "log_type", "db_err", "m", "SendLuckyNumbers").
				Infof("SendLuckyNumbers Updates err tableIdxList: %v, err:[%v]", tableIdxList, err)
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
		}
		// 获取幸运号码
		var selLuckyNum []string
		if err = DB.DefaultConnect().Table(tableName).Select("lucky_number").Where("id in (?)", tableIdxList).
			Find(&selLuckyNum).Error; err != nil {
			log.WithFieldsContext(ctx, "log_type", "db_err", "m", "SendLuckyNumbers").
				Infof("SendLuckyNumbers find err tableIdxList: %v, err:[%v]", tableIdxList, err)
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
		}
		luckyNumList = append(luckyNumList, selLuckyNum...)
	}

	// 更新用户表
	tableName, err := model.GetTableNameWithAccount(ctx, &userAccount, hok.UserLuckyNumRecordModel{}.TableName())
	if err != nil {
		return nil, err
	}
	var userLuckyNumRecord []hok.UserLuckyNumRecord
	for _, luckyNum := range luckyNumList {
		userLuckyNumRecord = append(userLuckyNumRecord, hok.UserLuckyNumRecord{
			UID:         userAccount.Uid,
			AccountType: int(userAccount.AccountType),
			LuckyNum:    luckyNum,
			SendType:    sendType,
			Remark:      remark,
		})
	}

	if err = DB.DefaultConnect().Table(tableName).Create(userLuckyNumRecord).Error; err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return luckyNumList, nil
}

// getIssuedLuckyNumberCount 获取已发放幸运号码数量
func getIssuedLuckyNumberCount(ctx context.Context, luckyNumberConfig []hok.LuckyNumberConfig) (int64, error) {

	// 获取缓存
	var currentCount int64
	cacheKey := getSendLuckyNumCountRedisKey()
	currentCount, redisError := redis.GetClient().Get(ctx, cacheKey).Int64()
	if redisError != nil && !errors.Is(redisError, redisOrigin.Nil) {
		err := errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			redisError.Error())
		return 0, err
	}

	if errors.Is(redisError, redisOrigin.Nil) || currentCount == 0 {
		// 获取表中已发送幸运号码数量数据
		var marker bool
		for _, v := range luckyNumberConfig {
			tableName := getSubTableName(v.NumberSegment)
			var count int64
			if err := DB.DefaultConnect().Table(tableName).Where("send_status = ?", 1).Count(&count).Error; err != nil {
				return 0, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", err)
			}
			// 已发送数量
			if count < TableNumTotalCount {
				currentCount = int64(v.SequentialValue)*TableNumTotalCount + count
				// 写入缓存
				redis.GetClient().IncrBy(ctx, cacheKey, currentCount)
				marker = true
				break
			}
		}
		if !marker {
			return 0, errs.NewCustomError(ctx, code.DataExceptionError, "No suitable table data")
		}
	}
	// if currentCount == 0 {
	//	return 0, errs.NewCustomError(ctx, code.GetIssuedLuckyNumberCountError, "get Issued Lucky Number Count err")
	// }
	return currentCount, nil
}

// HOKMotorcycleInsertLuckyConfigData 生成主表数据
func HOKMotorcycleInsertLuckyConfigData(ctx context.Context) error {
	var count int64
	// 避免重复创建
	if err := DB.DefaultConnect().Table(hok.LuckyNumberConfigModel{}.TableName()).Count(&count).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		return errs.NewCustomError(ctx, code.HokDataAlreadyExistsErr, "LuckyNumberConfig Data Already Exists Err")
	}
	var luckyNumberConfigList []hok.LuckyNumberConfig
	// 生成组合并将其每两个拼接成一个字符串
	combinations := generateCombinationsNum()
	pairedCombinations := pairCombinations(combinations)

	for i, pair := range pairedCombinations {
		luckyNumberConfigList = append(luckyNumberConfigList, hok.LuckyNumberConfig{
			SequentialValue: i,
			NumberSegment:   pair,
		})
	}
	if err := DB.DefaultConnect().Table(hok.LuckyNumberConfigModel{}.TableName()).Create(&luckyNumberConfigList).
		Error; err != nil {
		return fmt.Errorf("failed to insert data: %v", err)
	}
	return nil
}

// GenerateLuckyNumbers 生成幸运码
func GenerateLuckyNumbers(segment string) []string {
	parts := strings.Split(segment, "_")
	if len(parts) != 2 {
		return nil
	}

	num1, num2 := parts[0], parts[1]
	if len(num1) != 7 || len(num2) != 7 {
		return nil
	}

	luckyNumbers := make([]string, 0, TableNumTotalCount)
	for i := 1; i <= 9; i++ {
		for j := 0; j <= 9; j++ {
			for k := 0; k <= 9; k++ {
				for l := 0; l <= 9; l++ {
					for m := 0; m <= 9; m++ {
						for n := 0; n <= 9; n++ {
							for o := 0; o <= 9; o++ {
								for _, start := range parts {
									luckyNumber := fmt.Sprintf("%c%d %c%d %c%d %c%d %c%d %c%d %c%d",
										start[0], i, start[1], j, start[2], k, start[3], l, start[4], m, start[5], n, start[6], o)
									luckyNumbers = append(luckyNumbers, luckyNumber)
									if int64(len(luckyNumbers)) >= TableNumTotalCount {
										// todo
										return luckyNumbers
									}
								}
							}
						}
					}
				}
			}
		}
	}
	return luckyNumbers
}

// HOKMotorcycleInsertLuckyLogData 插入子表数据
func HOKMotorcycleInsertLuckyLogData(ctx context.Context, segment string, number int32) error {
	if number < 1 || number > 10 {
		return errs.NewCustomError(ctx, code.HokDataAlreadyExistsErr, "number Data Err")
	}
	tableName := getSubTableName(segment)
	// var count int64
	// if err := DB.DefaultConnect().Table(tableName).Count(&count).Error; err != nil {
	//	return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
	//		"db error, \t [Error]:{%v} ", err)
	// }
	// if count != 0 {
	//	return errs.NewCustomError(ctx, code.HokDataAlreadyExistsErr, "LuckyNumberLog Data Already Exists Err")
	// }

	luckyNumbersAll := GenerateLuckyNumbers(segment)
	if int64(len(luckyNumbersAll)) != TableNumTotalCount {
		return errs.NewCustomError(ctx, code.HOKGeneratedDataException, "luckyNumbers LEN err")
	}
	// 分段
	startIndex := int64(number-1) * CreateOnceNumber
	endIndex := startIndex + CreateOnceNumber
	luckyNumbers := luckyNumbersAll[startIndex:endIndex]
	batchSize := 1000

	for i := 0; i < len(luckyNumbers); i += batchSize {
		end := i + batchSize
		if end > len(luckyNumbers) {
			end = len(luckyNumbers)
		}

		luckyNumberLogs := make([]*hok.LuckyNumberLog, 0, 2000)
		for j := i; j < end; j++ {
			logEntry := &hok.LuckyNumberLog{
				ID:              uint(j + 1),
				LuckyNumber:     luckyNumbers[j],
				SequentialValue: j,
			}
			luckyNumberLogs = append(luckyNumberLogs, logEntry)
		}
		if err := DB.DefaultConnect().Table(tableName).Create(&luckyNumberLogs).Error; err != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"HOKMotorcycleInsertLuckyLogData db error, \t [Error]:{%v} ", err)
			return err
		}
		log.WithFieldsContext(ctx, "log_type", "hok_yace", "m", "HOKMotorcycleInsertLuckyLogData").
			Infof("HOKMotorcycleInsertLuckyLogData insert success end：[%v]", end)

	}
	return nil
}

// getLuckyNumbersIndexAndTableNames 获取需要发放的幸运号码索引及对应的表名称
func getLuckyNumbersIndexAndTableNames(ctx context.Context, sendCount int) (map[string][]uint64, error) {

	// 获取总表数据
	luckyNumberConfig, err := getLuckyNumberConfig(ctx)
	if err != nil {
		return nil, err
	}

	sendNumTableNameIdMap := make(map[string][]uint64)

	cacheKey := getSendLuckyNumCountRedisKey()
	// 加锁
	mux.Lock()
	// 解锁
	defer mux.Unlock()
	var starCount int64
	currentCount, err := CountSendLuckyNumByRedis(ctx, cacheKey, uint(sendCount))
	if err != nil {
		starCount, err = getIssuedLuckyNumberCount(ctx, luckyNumberConfig)
		if err != nil {
			return nil, err
		}
		currentCount = starCount + int64(sendCount)
		// 写入缓存
		redis.GetClient().IncrBy(ctx, cacheKey, currentCount)
	} else {
		starCount = currentCount - int64(sendCount)
	}

	if currentCount == 0 {
		return nil, errs.NewCustomError(ctx, code.DataExceptionError, "Send count is 0")
	}

	// 计算需要发放的表名称及对应索引
	for idx := starCount; idx < currentCount; idx++ {
		// 计算出在哪张子表中
		// subTableSequentialValue := idx / 18000000
		// subTableName := getSubTableName(luckyNumberConfig[subTableSequentialValue].NumberSegment)
		// // 计算在子表中的索引
		// subTableIndex := uint64(idx%18000000) + 1
		subTableName, subTableIndex := getTableNameAndIndexByCount(idx, luckyNumberConfig)
		if _, ok := sendNumTableNameIdMap[subTableName]; !ok {
			sendNumTableNameIdMap[subTableName] = []uint64{subTableIndex}
		} else {
			sendNumTableNameIdMap[subTableName] = append(sendNumTableNameIdMap[subTableName], subTableIndex)
		}
	}
	log.WithFieldsContext(ctx, "log_type", "numIdx", "m", "getLuckyNumbersIndexAndTableNames").
		Infof("getLuckyNumbersIndexAndTableNames sendNumTableNameIdMap: %v", sendNumTableNameIdMap)

	log.WithFieldsContext(ctx, "log_type", "send_num_count", "int_field_1", fmt.Sprintf("%d", currentCount)).
		Infof("Number of lucky numbers issued: [%v]", currentCount)
	return sendNumTableNameIdMap, nil
}

// CountSendLuckyNumByRedis 使用Redis累计数据
func CountSendLuckyNumByRedis(ctx context.Context, key string, count uint) (int64, error) {
	// 创建 Redis 客户端
	getClient := redis.GetClient()

	// Lua 脚本
	luaScript := `
		local key = KEYS[1]
		local increment = tonumber(ARGV[1])

		if redis.call('EXISTS', key) == 1 then
			return redis.call('INCRBY', key, increment)
		else
			return redis.error_reply("Key does not exist")
		end
	`

	// 要操作的键和增量
	increment := count

	// 执行 Lua 脚本
	result, err := getClient.Eval(ctx, luaScript, []string{key}, increment).Result()
	if err != nil {
		return 0, errs.NewCustomError(ctx, code.ExecutingLuaScriptError, "Error executing Lua script: %v", err)
	}
	// 将结果转换为 int64 类型
	newValue, ok := result.(int64)
	if !ok {
		return 0, errs.NewCustomError(ctx, code.ExecutingLuaScriptError, "unexpected result type: %T", result)
	}
	log.WithFieldsContext(ctx, "log_type", "redis_count", "m", "CountSendLuckyNumByRedis").
		Infof("CountSendLuckyNumByRedis count: %v", newValue)
	return newValue, nil
}

// getLuckyNumberConfig 获取总表数据
func getLuckyNumberConfig(ctx context.Context) ([]hok.LuckyNumberConfig, error) {
	// 获取总表数据
	var LuckyNumberConfig []hok.LuckyNumberConfig
	if err := DB.DefaultConnect().Table(hok.LuckyNumberConfigModel{}.TableName()).
		Select("sequential_value,number_segment").Order("sequential_value asc").
		Find(&LuckyNumberConfig).Error; err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	if len(LuckyNumberConfig) != 60 {
		return nil, errs.NewCustomError(ctx, code.DataExceptionError, "LuckyNumberConfig table data count err")
	}
	return LuckyNumberConfig, nil
}

// getSendLuckyNumCountRedisKey 发送幸运号码数量缓存Key
func getSendLuckyNumCountRedisKey() string {
	return global.GetRedisKey(MotorcycleSendLuckyNumCacheKey)
}

// getScheduledDrawInfoRedisKey 定时开奖缓存key
func getScheduledDrawInfoRedisKey(timestamp string) string {
	return global.GetRedisKey(strings.Join([]string{MotorcycleScheduledDrawInfoCacheKey, timestamp}, "_"))
}

// getMotorcyclePresentLanguageCacheKey 摩托车多语言缓存MotorcyclePresentLanguageCacheKey
func getMotorcyclePresentLanguageCacheKey() string {
	return global.GetRedisKey(MotorcyclePresentLanguageCacheKey)
}

// getAllWinningRecordListRedisKey 跑马灯信息缓存key
func getAllWinningRecordListRedisKey() string {
	return global.GetRedisKey(MotorcycleAllWinningRecordListCacheKey)
}

// getSubTableName 获取子表名称
func getSubTableName(segment string) string {
	return strings.Join([]string{hok.LuckyNumberLogModel{}.TableName(), segment}, "_")
}

// generateCombinationsNum 生成所有从 0 到 9 中选择 7 个数字组成的严格递增组合
func generateCombinationsNum() []string {
	var result []string
	var combination []int
	generate(0, 7, combination, &result)
	return result
}

// generate 递归生成组合
func generate(start, k int, combination []int, result *[]string) {
	if k == 0 {
		// 将组合转换为字符串并添加到结果中
		combStr := ""
		for _, num := range combination {
			combStr += fmt.Sprintf("%d", num)
		}
		*result = append(*result, combStr)
		return
	}

	for i := start; i <= 9; i++ {
		// 添加当前数字到组合中
		combination = append(combination, i)
		// 递归生成剩余的组合
		generate(i+1, k-1, combination, result)
		// 回溯，移除最后一个数字
		combination = combination[:len(combination)-1]
	}
}

// pairCombinations 将组合切片里的字符串每两个拼接成一个字符串，格式为"0123456_0123457"
func pairCombinations(combinations []string) []string {
	var pairedCombinations []string
	for i := 0; i < len(combinations)-1; i += 2 {
		pair := fmt.Sprintf("%s_%s", combinations[i], combinations[i+1])
		pairedCombinations = append(pairedCombinations, pair)
	}
	// 如果组合数量是奇数，最后一个组合单独处理
	if len(combinations)%2 != 0 {
		pairedCombinations = append(pairedCombinations, combinations[len(combinations)-1])
	}
	return pairedCombinations
}

func countZerosAndOnes(s []int8) (int, int) {
	zeroCount := 0
	oneCount := 0
	for _, char := range s {
		if char == 0 {
			zeroCount++
		} else if char == 1 {
			oneCount++
		}
	}
	return zeroCount, oneCount
}

// PageSlice TODO
func PageSlice(slice interface{}, limit, page int) interface{} {
	// 将传入的切片进行类型断言，确保是切片类型
	sliceValue := reflect.ValueOf(slice)
	if sliceValue.Kind() != reflect.Slice {
		return nil // 返回空，表示传入的不是切片类型
	}
	// 获取切片的长度
	sliceLen := sliceValue.Len()
	if limit == 0 {
		limit = 10
	}
	if page == 0 {
		page = 1
	}

	// 计算起始索引和结束索引
	startIndex := (page - 1) * limit
	endIndex := startIndex + limit

	// 检查索引是否超出切片范围
	if startIndex >= sliceLen {
		return nil // 返回空，表示没有数据
	}
	if endIndex > sliceLen {
		endIndex = sliceLen
	}

	// 切片操作，获取分页数据
	pagedSlice := sliceValue.Slice(startIndex, endIndex).Interface()

	return pagedSlice
}

// SendMotorcyclePresent 发送摩托车礼包
func SendMotorcyclePresent(ctx context.Context, uid string) (string, error) {

	openID := strings.Split(uid, "-")[1]
	accountData, _ := proto.Marshal(&userAccountPb.UserAccount{
		Uid:         uid,
		AccountType: userAccountPb.AccountType(1),
		IntlAccount: &userAccountPb.IntlAccount{
			OpenId:    openID,
			ChannelId: 3,
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}

	gameRoleInfo, err := gameProxy.GetSavedRoleInfo(ctx, &gamePb.GetSavedRoleInfoReq{
		FsourceId: FSourceID,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "error", "m", "SendMotorcyclePresent").Infof(
			"SendMotorcyclePresent GetSavedRoleInfo err;uid:[%v]; err:[%v]", uid, err)
		return "", err
	}
	// gameRegionConf, err := gameProxy.GetGameRegionConf(ctx, &gamePb.GetGameRegionConfReq{
	//	GameId: HOKGameId,
	// })
	// if err != nil {
	//	return "", err
	// }
	// serialId := ams_pb.CreateAmsSerial(gameRegionConf.GameCode, gameRegionConf.Appid)
	sendPresentRsp, err := presentV2Proxy.SendPresent(ctx, &present_present_v2.SendPresentDataReq{
		FsourceId:      FSourceID,
		PresentId:      MotorcyclePresentId,
		PresentGroupId: MotorcycleGroupId,
		RoleInfo:       gameRoleInfo,
		// SerialNo:       serialId,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "SendMotorcyclePresent").Infof(
			"SendMotorcyclePresent SendPresent err; err：[%v]", err)
		return "", err
	}

	// 获取礼包多语言, 写入缓存
	language, err := getMotorcycleGiftMultilingualInfoFromLottery(ctx, sendPresentRsp.PresentId,
		sendPresentRsp.PresentGroupId)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "SendMotorcyclePresent").Infof(
			"SendMotorcyclePresent getMotorcycleGiftMultilingualInfoFromLottery err; err：[%v]", err)
		return "", err
	}
	if language == nil {
		return "", errs.NewCustomError(ctx, code.HokTmpNoPresentLanguage, "SendMotorcyclePresent No package language")
	}
	languageByte, err := json.Marshal(language)
	if err == nil {
		// 写入缓存
		languageCacheKey := getMotorcyclePresentLanguageCacheKey()
		redis.GetClient().SetEX(ctx, languageCacheKey, string(languageByte), 31*24*time.Hour)
	}
	return string(languageByte), nil
}

// getMotorcycleGiftMultilingualInfoFromLottery 根据获取摩托车礼包多语言
func getMotorcycleGiftMultilingualInfoFromLottery(ctx context.Context, presentId,
	presentGroupId string) (*lipass_common.LanguageType, error) {
	langTypeConfig, err := presentV2Proxy.GetPresentGroupLangTypeConfig(ctx, &present_present_v2.GetPresentGroupLangTypeConfigReq{
		PresentId:      presentId,
		PresentGroupId: presentGroupId,
	})
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "getMotorcycleGiftMultilingualInfoFromLottery").Infof(
			"getMotorcycleGiftMultilingualInfoFromLottery GetPresentGroupLangTypeConfig err; err：[%v]", err)
		return nil, err
	}
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "getMotorcycleGiftMultilingualInfoFromLottery").Infof(
		"getMotorcycleGiftMultilingualInfoFromLottery show rsp; langTypeConfig：[%v]", langTypeConfig)
	return langTypeConfig.GetPresentNameLanguage(), nil
}

// getGiftMultilingualInfoFromLottery 抽奖信息礼包多语言
func getGiftMultilingualInfoFromLottery(ctx context.Context, presentId, presentGroupId string) (
	[]*present_present_v2.PresentItemLangTypeConfig, error) {
	langTypeConfig, err := presentV2Proxy.GetPresentGroupLangTypeConfig(ctx, &present_present_v2.GetPresentGroupLangTypeConfigReq{
		PresentId:      presentId,
		PresentGroupId: presentGroupId,
	})
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "getGiftMultilingualInfoFromLottery").Infof(
			"getGiftMultilingualInfoFromLottery GetPresentGroupLangTypeConfig err; err：[%v]", err)
		return nil, err
	}
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "getGiftMultilingualInfoFromLottery").Infof(
		"getGiftMultilingualInfoFromLottery show rsp; langTypeConfig：[%v]", langTypeConfig)
	return langTypeConfig.GetPresentItemList(), nil
}

// prizeWrittenRunningLantern 奖品写入跑马灯
func prizeWrittenRunningLantern(ctx context.Context, uid, presentId, motorcyclePresentLanguage,
	timestamp string) error {

	runningHorseLightsRecordNumber := config.GetConfig().HOKMotorcycleEvents.RunningHorseLightsRecordNumber
	headUrl, nickName, err := getNikeNameHeadUrlByUid(ctx, uid)
	if err != nil {
		return err
	}
	if presentId == MotorcyclePresentId {
		log.WithFieldsContext(ctx, "log_type", "motorcycle",
			"str_field_1", fmt.Sprintf("%s", headUrl),
			"str_field_2", fmt.Sprintf("%s", nickName),
			"str_field_3", fmt.Sprintf("%s", uid)).
			Infof("MotorcyclePresentId uid: [%v]", uid)
	}
	winningRecordItem := WinningRecord{
		NickName:          nickName,
		HeadUrl:           headUrl,
		Timestamp:         timestamp,
		PresentId:         presentId,
		PrizeMultilingual: motorcyclePresentLanguage, // 奖品多语言
	}
	// 序列化记录
	recordListStr, err := json.Marshal(winningRecordItem)
	if err != nil {
		return errs.NewCustomError(ctx, code.JsonParseError, "Json Marshal Error;recordList:[%v]", winningRecordItem)
	}
	// 更新跑马灯
	winningRecordListCacheKey := getAllWinningRecordListRedisKey()
	// 开始一个事务
	pipe := redis.GetClient().Pipeline()
	// 将新记录添加到队列的左侧
	pipe.LPush(ctx, winningRecordListCacheKey, string(recordListStr))
	// 修剪队列以确保长度不超过限制
	pipe.LTrim(ctx, winningRecordListCacheKey, 0, int64(runningHorseLightsRecordNumber-1))
	// 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
			"UpdateLuckyNumberStatus redis pipe push error, \t [Error]:{%v}", err)
	}
	return nil
}

// FetchLotteryInfoAndCheckMarquee 获取抽奖信息并判断是否写入跑马灯
func FetchLotteryInfoAndCheckMarquee(ctx context.Context, param *present_present_v2.SendPresentData) error {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil
	}
	if param == nil {
		return errs.NewCustomError(ctx, code.HokTmpNoPresentInformationObtained, "No prize information was obtained")
	}
	// 抽奖结果上报
	motorcyclePresentReport(ctx, ReportParam{
		OpenId:       userAccount.IntlAccount.OpenId,
		Action:       "motorcycle_backup_reward_exchange",
		LotteryIndex: param.GetLotteryIndex(),
	})
	// 判断当前礼包是否要写入跑马灯，不写入直接返回
	writeGiftList := config.GetConfig().HOKMotorcycleEvents.WriteRunningLanternGiftList
	var needWrite bool
	for _, v := range writeGiftList {
		if param.GetPresentId() == v.PresentId && cast.ToString(param.GetLotteryIndex()) == v.LotteryIndex {
			needWrite = true
			break
		}
	}
	if !needWrite {
		return nil
	}
	// 获取礼包多语言信息
	infoFromLottery, err := getGiftMultilingualInfoFromLottery(ctx, param.PresentId, param.PresentGroupId)
	if err != nil {
		return err
	}
	var language *lipass_common.LanguageType
	for _, v := range infoFromLottery {
		if v.GetLotteryIndex() == param.GetLotteryIndex() {
			language = v.GetItemNameLanguage()
			break
		}
	}
	var motorcyclePresentLanguage string
	if language != nil {
		bytes, err2 := json.Marshal(language)
		if err2 != nil {
			log.WithFieldsContext(ctx, "log_type", "merr", "m", "FetchLotteryInfoAndCheckMarquee").Infof(
				"FetchLotteryInfoAndCheckMarquee json Marshal err; language：[%v]，err：[%v]", language, err)
		}
		motorcyclePresentLanguage = string(bytes)
	}
	err = prizeWrittenRunningLantern(ctx, userAccount.Uid, "", motorcyclePresentLanguage, cast.ToString(time.Now().Unix()))
	if err != nil {
		return err
	}
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "FetchLotteryInfoAndCheckMarquee").Infof(
		"FetchLotteryInfoAndCheckMarquee show success; param：[%v]，motorcyclePresentLanguage：[%v]", param,
		motorcyclePresentLanguage)
	return nil
}
