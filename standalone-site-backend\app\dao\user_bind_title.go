package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

type UserBindTitleConditions struct {
	IntlOpenids []string
	Status      int
	Language    string
	Order       []*OrderConditions
	IntlOpenid  string
}

func UserBindTitleList(conditions *UserBindTitleConditions, offset, limit int) ([]*model.UserBindTitle, error) {
	var titles []*model.UserBindTitle
	var userBindTitle []*model.UserBindTitleRow
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserBindTitle{}).TableName())
	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	var language string
	if len(conditions.IntlOpenids) > 0 {
		db = db.Where("intl_openid in ?", conditions.IntlOpenids)
	}
	if conditions.Status > 0 {
		db = db.Where("status = ?", conditions.Status)
	}
	if conditions.Language != "" {
		language = conditions.Language
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	err = db.Find(&userBindTitle).Error
	if err != nil {
		return nil, err
	}
	// 没有绑定称号直接返回数据
	if len(userBindTitle) == 0 {
		return titles, nil
	}
	var ids = make([]int64, 0)
	for _, row := range userBindTitle {
		ids = append(ids, row.TitleId)
	}
	newTime := time.Now().Unix()
	var titleList []*model.Title
	var titleLangList []*model.TitleLanguage
	err = db.Session(&gorm.Session{NewDB: true}).Table((&model.TitleLanguage{}).TableName()).Where("title_id in ? AND language = ?", ids, language).Find(&titleLangList).Error
	if err != nil {
		return nil, err
	}
	// todo 多语言不影响返回称号，先注释
	//var langTitleIds = make([]int64, 0)
	//for _, lang := range titleLangList {
	//	langTitleIds = append(langTitleIds, lang.TitleId)
	//}
	//if len(langTitleIds) == 0 {
	//	return titles, nil
	//}
	err = db.Session(&gorm.Session{NewDB: true}).Table((&model.Title{}).TableName()).Where("id in ? AND status = ? AND up_time < ? AND down_time > ?", ids, 2, newTime, newTime).Find(&titleList).Error
	if err != nil {
		return nil, err
	}
	// 整理组合
	for i, title := range titleList {
		for _, lang := range titleLangList {
			if title.ID == lang.TitleId {
				titleList[i].TitleLanguage = lang
			}
		}
	}
	for _, title := range titleList {
		for _, row := range userBindTitle {
			if title.ID == row.TitleId {
				titles = append(titles, &model.UserBindTitle{
					Model: &model.Model{
						ID:         row.ID,
						CreatedOn:  row.CreatedOn,
						ModifiedOn: row.ModifiedOn,
						DeletedOn:  row.DeletedOn,
						IsDel:      row.IsDel,
					},
					Title:         title,
					TitleLanguage: title.TitleLanguage,
					IntlOpenid:    row.IntlOpenid,
					TitleId:       row.TitleId,
					Status:        row.Status,
					GameId:        row.GameId,
					AreaId:        row.AreaId,
				})
			}
		}
	}
	return titles, nil
}
