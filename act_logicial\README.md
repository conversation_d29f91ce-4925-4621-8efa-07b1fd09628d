# 活动C端后台逻辑相关服务（抽奖、邀请、组队、任务等）

## 一、项目说明

### 1. 项目介绍

简短的项目描述。[项目文档](https://iwiki.woa.com/p/4010411560)

### 2. 快速体验

| 环境 | B 端                                      | C 端                                   | ... |
| ---- | ----------------------------------------- | -------------------------------------- | --- |
| 测试 | <https://test-sg-wand.levelinfiniteapps.com/> | <https://test-sg-act.playerinfinite.com/> |     |
| 预发 | 无  | <https://pre-sg-act.playerinfinite.com/>  |     |
| 正式 | <https://wand.levelinfiniteapps.com/>      | <https://sg-act.playerinfinite.com/>      |     |

### 3. 技术选型

- 开发语言：[Golang](https://go.dev/)
- 开发框架：[Trpc-GO](https://iwiki.woa.com/p/118272478)
- 数据上报：[CLS](https://console.intl.cloud.tencent.com/cls/search?region=ap-singapore&topic_id=8adb84a2-eaf6-4b1e-9c6f-1b8b043bd45a&queryBase64=)
- 代码扫描：[Codedog](https://codedog.woa.com/code-analysis/repos/624440/projects?offset=0&limit=10)

## 二、 开始

这些说明将指导你如何在本地机器上获取和运行该项目的副本。请阅读进一步了解如何部署该项目到现场系统。

### 1.环境

在开始之前，你需要安装哪些软件以及如何安装它们。例如：

- Golang 1.17
- fresh 


```
go get github.com/pilu/fresh
```

### 2.安装

按照以下步骤安装和设置项目：

1. 克隆此仓库

```sh
git clone https://git.woa.com/iegg_distribution/Marketing_group/act_logicial.git
```

2. 安装依赖

先参考 [tRPC-Go 环境搭建](https://iwiki.woa.com/p/99485252) 配置代理
然后执行
```sh
go mod tidy
```

3. 拷贝配置文件

配置文件仓库： https://git.woa.com/iegg_distribution/Marketing_group/wand_server_conf/tree/master/configmap/dev

- 先在根目录下创建 `conf`目录（创建后和`app`同级）
- 拷贝 `act-logicial-conf/*`  到 `conf/config/`
- 拷贝 `database/*` 到 `conf/database/`

拷贝后目录结构
* app
    * 其他
* conf
    * config
        * custom.yaml
        * trpc_go.yaml
    * database
        * database.yaml

### 3. 运行

1. 执行命令

```sh
fresh
```

2. 本地访问：<http://127.0.0.1:8004>

## 三、开发

### 1. 分支说明

常驻分支：

- master： 生产分支
- demo：开发分支

分支操作指引：

- 测试：切 `demo` 分支
- 新需求：在demo分支开发
- 热修复：从 `master` 创建 `fix/xxx` 分支

### 2. 开发规范

参见：<https://iwiki.woa.com/p/4006766683>

### 3. 开发指引

提供开发方面的操作指引，包括特殊的环境，必须说明的部署等等

## 四、 发布流程

### 测试环境

1. 推送 demo 分支：`git push origin demo`
2. 自动触发流水线：[【TEST】活动act_logicial服务](https://devops.woa.com/console/pipeline/wgwebglobal/p-ca1de1e96a7b400ebf4d07072321e8ee)

### 预发布环境

1. 合并 demo 到 master 分支：[提交 MR](https://git.woa.com/iegg_distribution/Marketing_group/act_logicial/-/tags)
2. 给 master 打 Tag：`v{version}`
3. 手动执行流水线：[【PRE】活动act_logicial服务](https://devops.woa.com/console/pipeline/wgwebglobal/p-6bfe19f8aad447989b202b9bbc401eb9)

### 正式环境

1. 合并 demo 到 master：[提交 MR](https://git.woa.com/iegg_distribution/Marketing_group/act_logicial/-/tags) 
2. 给 master 打 Tag：`v{version}`
3. 手动执行流水线：[【PROD】活动act_logicial服务](https://devops.woa.com/console/pipeline/wgwebglobal/p-6a77d97daf2243b4aa2912f06a53e091)
