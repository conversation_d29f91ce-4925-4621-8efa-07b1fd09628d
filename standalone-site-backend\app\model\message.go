package model

import (
	"errors"
	"fmt"

	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/util"
)

type Message struct {
	*Model
	Type                   constants.MessageT `json:"type"`
	SenderUserIntlOpenid   string             `gorm:"column:sender_user_intl_openid" json:"sender_user_intl_openid"`     //发送方用户intlopenid
	ReceiverUserIntlOpenid string             `gorm:"column:receiver_user_intl_openid" json:"receiver_user_intl_openid"` //接收方用户intlopenid
	Brief                  string             `gorm:"column:brief" json:"brief"`                                         //摘要说明
	Content                string             `gorm:"column:content" json:"content"`                                     //详细内容
	ExtInfo                string             `gorm:"column:ext_info" json:"ext_info"`                                   //扩展字段，json字符串
	PostUUID               string             `gorm:"column:post_uuid" json:"post_uuid"`                                 //动态唯一ID(如果是根据动态审核来创建的相关联)
	CommentUUID            string             `gorm:"column:comment_uuid" json:"comment_uuid"`                           //评论唯一ID
	ReplyUUID              string             `gorm:"column:reply_uuid" json:"reply_uuid"`                               //回复ID
	Reply2ReplyUUID        string             `json:"reply2reply_uuid" gorm:"column:reply2reply_uuid"`                   //回复的回复ID
	IsRead                 int                `gorm:"column:is_read" json:"is_read"`                                     //是否已读
	CreatedOn              int64              `gorm:"column:created_on" json:"created_on"`                               //创建时间
	ModifiedOn             int64              `gorm:"column:modified_on" json:"modified_on"`                             //修改时间
	DeletedOn              int64              `gorm:"column:deleted_on" json:"deleted_on"`                               //删除时间
	IsDel                  int                `gorm:"column:is_del" json:"is_del"`                                       //是否删除 0 为未删除、1 为已删除
	GameID                 string             `gorm:"column:game_id" json:"game_id"`                                     //游戏id
	AreaID                 string             `gorm:"column:area_id" json:"area_id"`                                     //大区id
	CmsMsgID               int64              `gorm:"column:cms_msg_id" json:"cms_msg_id"`                               //cms管理端配置的官方推送消息id
}

type MessageFormattedNew struct {
	ID                     int64              `json:"id"`
	Type                   constants.MessageT `json:"type"`
	SenderUser             *UserFormated      `json:"sender_user"`
	Brief                  string             `json:"brief"`
	Content                string             `json:"content"`
	CommunityPostID        string             `json:"community_post_id"`
	Post                   *PostFormatted     `json:"post"`
	CommunityPostURL       string             `json:"community_post_url"`
	CommunityCommentID     string             `json:"community_comment_id"`
	ReplyUUID              string             `json:"reply_uuid"`
	Reply                  *CommentReply      `json:"reply"`
	IsRead                 int8               `json:"is_read"`
	CreatedOn              int64              `json:"created_on"`
	ModifiedOn             int64              `json:"modified_on"`
	AreaId                 string             `json:"area_id"`
	GameId                 string             `json:"game_id"`
	ExtInfo                string             `json:"ext_info"`
	CmsMsgID               int64              `json:"cms_msg_id"`
	SenderUserIntlOpenid   string             `json:"sender_user_intl_openid"`
	ReceiverUserIntlOpenid string             `json:"receiver_user_intl_openid"`
	PostUUID               string             `json:"post_uuid"`    //动态唯一ID(如果是根据动态审核来创建的相关联)
	CommentUUID            string             `json:"comment_uuid"` //评论ID
}

// 去掉动态、评论等，只保留必要的信息
type MessageFormatedReduce struct {
	ID                     int64              `json:"id"`
	SenderUser             *UserFormated      `json:"sender_user"`
	Type                   constants.MessageT `json:"type"`
	Brief                  string             `json:"brief"`
	Content                string             `json:"content"`
	IsRead                 int8               `json:"is_read"`
	CreatedOn              int64              `json:"created_on"`
	ModifiedOn             int64              `json:"modified_on"`
	AreaId                 string             `json:"area_id"`
	GameId                 string             `json:"game_id"`
	ExtInfo                string             `json:"ext_info"`
	CmsMsgID               int64              `json:"cms_msg_id"`
	SenderUserIntlOpenid   string             `json:"sender_user_intl_openid"`
	ReceiverUserIntlOpenid string             `json:"receiver_user_intl_openid"`
	PostUUID               string             `json:"post_uuid"`  //动态唯一ID(如果是根据动态审核来创建的相关联)
	CommentUUID            string             `json:"comment_id"` //评论ID
	ReplyUUID              string             `json:"reply_uuid"`
}

type MessageUnreadInfo struct {
	ReceiverUserIntlOpenId string `json:"receiver_user_intl_openid"`
	UnreadCount            int64  `json:"unread_count"`
}

type MsgExtInfo struct {
	Days                int64                        `json:"days"`
	OriginalContentText string                       `json:"original_content_text"` // 原始内容文本，主要用于CMS官方删除用户的一些内容后，通知消息展示原始内容，目前后台数据是真删除逻辑，所以需要自己存一下
	TotalPoints         int64                        `json:"total_points"`          // 用户积分总数
	Href                string                       `json:"href"`                  //站内信跳转链接
	SiteMsgSubType      constants.SiteMessageSubType `json:"site_msg_sub_type"`     // 站内信子类型 1 普通站内信消息，2头像挂件站内信消息
	AvatarPendantId     int64                        `json:"avatar_pendant_id"`     // 若站内挂件则返回id
	AvatarPendantUrl    string                       `json:"avatar_pendant"`
	CdKey               string                       `json:"cdkey"`             // 附件码
	CommentBubbleId     int64                        `json:"comment_bubble_id"` // 评论气泡id
	DelType             int32                        `json:"del_type"`          // 删除类型
	DelReason           int32                        `json:"del_reason"`        // 删除原因
}

// 定义4中类型的结构体
type NikkeMessageTabCount struct {
	Comment     int64 `json:"comment"`      // 回复 + 评论的总数， 动态删除不删除消息
	Follow      int64 `json:"follow"`       // 关注总数
	Like        int64 `json:"like"`         // 评论点赞 + 回复点赞 + 动态点赞
	SiteMessage int64 `json:"site_message"` // 站内信的未读数
}

func (m *Message) FormatNew() *MessageFormattedNew {
	if m.Model == nil || m.Model.ID == 0 {
		return nil
	}
	mf := &MessageFormattedNew{
		ID:                     m.ID,
		SenderUser:             &UserFormated{},
		Type:                   m.Type,
		Brief:                  m.Brief,
		Content:                m.Content,
		PostUUID:               m.PostUUID,
		Post:                   &PostFormatted{},
		ReplyUUID:              m.ReplyUUID,
		Reply:                  &CommentReply{},
		IsRead:                 int8(m.IsRead),
		CreatedOn:              m.CreatedOn,
		ModifiedOn:             m.ModifiedOn,
		AreaId:                 m.AreaID,
		GameId:                 m.GameID,
		ExtInfo:                m.ExtInfo,
		CmsMsgID:               m.CmsMsgID,
		SenderUserIntlOpenid:   m.SenderUserIntlOpenid,
		ReceiverUserIntlOpenid: m.ReceiverUserIntlOpenid,
		CommentUUID:            m.CommentUUID,
	}

	return mf
}

func (m *Message) FormatReduce() *MessageFormatedReduce {
	if m.Model == nil || m.Model.ID == 0 {
		return nil
	}
	mf := &MessageFormatedReduce{
		ID:                     m.ID,
		SenderUser:             &UserFormated{},
		Type:                   m.Type,
		Brief:                  m.Brief,
		Content:                m.Content,
		PostUUID:               m.PostUUID,
		IsRead:                 int8(m.IsRead),
		CreatedOn:              m.CreatedOn,
		ModifiedOn:             m.ModifiedOn,
		AreaId:                 m.AreaID,
		GameId:                 m.GameID,
		ExtInfo:                m.ExtInfo,
		CmsMsgID:               m.CmsMsgID,
		SenderUserIntlOpenid:   m.SenderUserIntlOpenid,
		ReceiverUserIntlOpenid: m.ReceiverUserIntlOpenid,
		ReplyUUID:              m.ReplyUUID,
		CommentUUID:            m.CommentUUID,
	}

	return mf
}

func (m *Message) FormatReduceNew(user *UserContent) *MessageFormatedReduce {
	if m.Model == nil || m.Model.ID == 0 {
		return nil
	}
	mf := &MessageFormatedReduce{
		ID:                     m.ID,
		SenderUser:             &UserFormated{},
		Type:                   m.Type,
		Brief:                  m.Brief,
		Content:                m.Content,
		PostUUID:               m.PostUUID,
		IsRead:                 int8(m.IsRead),
		CreatedOn:              m.CreatedOn,
		ModifiedOn:             m.ModifiedOn,
		AreaId:                 m.AreaID,
		GameId:                 m.GameID,
		ExtInfo:                m.ExtInfo,
		CmsMsgID:               m.CmsMsgID,
		SenderUserIntlOpenid:   m.SenderUserIntlOpenid,
		ReceiverUserIntlOpenid: m.ReceiverUserIntlOpenid,
		ReplyUUID:              m.ReplyUUID,
		CommentUUID:            m.CommentUUID,
	}
	if user != nil && user.ID > 0 {
		mf.SenderUser = user.Format()
	}
	return mf
}

func (m *Message) TableName() string {
	return "p_message"
}

func (m *Message) GetTableName(openid string) (string, error) {
	suffixId := util.ExtractSurplusByOpenid(openid, 2)
	if suffixId < 0 {
		return "", errors.New("get table name failed")
	}
	tableName := fmt.Sprintf("%s_%d", m.TableName(), suffixId)
	return tableName, nil
}
