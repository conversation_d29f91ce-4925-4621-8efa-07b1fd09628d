package exobrntmp

import (
	"context"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	"google.golang.org/protobuf/proto"
	model "trpc.act.logicial/app/model/exobrn_tmp"
)

const (
	ExobrnGameID = 30006
)

func SendSevenDayPresent(ctx context.Context) (err error) {
	fsourceId := "sevenday_tmp"
	presentId := "Wand-**************-Pf90041f96044"
	scheduleCtx := context.Background()
	tableName := model.SendTempSevenDay{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.SendTempSevenDay{}.TableName()).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "exobrn_sevenday").Infof(fmt.Sprintf("%v", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页50条
	pageSize := 50
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	gameProxy := gamePb.NewGameClientProxy()
	var lastId int64
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		// offset := (pageNumber - 1) * pageSize
		var logData = []model.SendTempSevenDay{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Where(condition).Where("id > ? ", lastId).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "exobrn_sevenday").Infof(fmt.Sprintf("%v", logData))
		if len(logData) == 0 {
			break
		}
		lastId = logData[len(logData)-1].ID
		for _, v := range logData {
			wg.Add(1)
			go func(data model.SendTempSevenDay) {
				newCtx := context.Background()
				openID := data.OpenId
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         fmt.Sprintf("%d-%s", ExobrnGameID, openID),
					AccountType: accountPb.AccountType_INTL,
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				log.WithFieldsContext(newCtx, "log_type", "exobrn_sevenday").Infof(string(accountData))

				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
				}
				// 获取角色信息
				gameReq := &gamePb.GetRoleInfoReq{
					GameId: fmt.Sprintf("%d", ExobrnGameID),
				}

				gameRoleList, err := gameProxy.GetRoleList(newCtx, gameReq, callopts...)
				if err != nil || gameRoleList.RoleList == nil || len(gameRoleList.RoleList) == 0 {
					trpcErr := errs.ParseError(newCtx, err)
					log.WithFieldsContext(newCtx, "log_type", "exobrn_role_err", "str_field_1", fmt.Sprintf("%d", trpcErr.Code)).Infof(err.Error())

				} else {
					log.WithFieldsContext(ctx, "ExobrnSevenDay log", "debug").Infof(fmt.Sprintf("v: %#v", v))
					// 发送礼包
					sendReq := &presentPb.SendPresentReq{
						FsourceId: fsourceId,
						PresentId: presentId,
						RoleInfo:  gameRoleList.RoleList[0],
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
					}
					if sendErr != nil {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						fmt.Println("---------------sendErr---------------")
						fmt.Printf("%#v\n", sendErr.Error())
						log.WithFieldsContext(newCtx, "log_type", "exobrn_sevenday").Infof(fmt.Sprintf("[SendPresent] service err111:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						if strings.Contains(sendErr.Error(), "package limit left not enough") {
							DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						}
					}
					log.WithFieldsContext(newCtx, "log_type", "exobrn_sevenday").Infof(fmt.Sprintf("[SendPresent] service err:%v", sendErr))
					// 修改发货状态
				}
				wg.Done()
			}(v)

		}
		wg.Wait()

	}
	return
}
