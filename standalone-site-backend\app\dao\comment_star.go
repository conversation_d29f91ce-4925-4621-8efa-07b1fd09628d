package dao

import (
	"errors"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

func CommentStarGet(id, commentId int64, intlOpenID string) (*model.CommentStar, error) {
	var star model.CommentStar
	db := DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if commentId > 0 {
		db = db.Where("comment_id = ?", commentId)
	}
	if intlOpenID != "" {
		db = db.Where("intl_openid =  ?", intlOpenID)
	}

	if err := db.First(&star).Error; err != nil {
		return nil, err
	}
	return &star, nil
}

func CommentStarGetV2(id int64, communityCommentId string, intlOpenID string) (*model.CommentStar, error) {
	var star model.CommentStar
	db := DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if communityCommentId != "" {
		db = db.Where("comment_uuid = ?", communityCommentId)
	}
	if intlOpenID != "" {
		db = db.Where("intl_openid =  ?", intlOpenID)
	}

	if err := db.First(&star).Error; err != nil {
		return nil, err
	}
	return &star, nil
}

func CommentStarCheckUserCommentsStar2(commentUUID, intlOpenID string) ([]int64, error) {
	var starCommentIds []int64
	if err := DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName()).Where("comment_uuid = ?", commentUUID).Where("intl_openid =  ?", intlOpenID).Where("is_del = 0").Pluck("id", &starCommentIds).Error; err != nil {
		return starCommentIds, err
	}
	return starCommentIds, nil
}

func BatchCheckUserCommentsStar(commentUUIDs []string, intlOpenID string) ([]string, error) {
	var starCommentUUIDs []string
	if err := DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName()).Where("comment_uuid in ?", commentUUIDs).Where("intl_openid =  ?", intlOpenID).Where("is_del = 0").Pluck("comment_uuid", &starCommentUUIDs).Error; err != nil {
		return starCommentUUIDs, err
	}
	return starCommentUUIDs, nil
}

func CommentStarCheckUserCommentsStarNew(commentIDs []string, intlOpenID string) ([]string, error) {
	var starCommentIds []string
	if err := DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName()).Where("community_comment_id in ?", commentIDs).Where("user_id = ?", intlOpenID).Where("is_del = 0").Pluck("community_comment_id", &starCommentIds).Error; err != nil {
		return starCommentIds, err
	}
	return starCommentIds, nil
}

func CommentStarCreate(commentStar *model.CommentStar) error {
	// 判断是否存在这个取消点赞，存在的话更新，不存在的创建
	var commentStarData model.CommentStar
	err := DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName()).Where("comment_uuid = ? and intl_openid = ?", commentStar.CommentUUID, commentStar.IntlOpenid).First(&commentStarData).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if commentStarData.Model != nil && commentStarData.IsDel == 1 {
		return DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName()).Where("comment_uuid = ?", commentStar.CommentUUID).Updates(map[string]interface{}{
			"is_del":      0,
			"delete_on":   0,
			"modified_on": time.Now().Unix(),
		}).Error
	}
	if commentStarData.Model == nil {
		return DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName()).Omit("Comment").Create(&commentStar).Error
	}
	return nil
}

func CommentStarDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName()).Omit("Comment").Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func CommentStarDeleteNew(commentUUID string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName()).Where("comment_uuid = ? AND is_del = ?", commentUUID, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func CommentStarUpdateCommentMoveInfo(db *gorm.DB, commentID int64, communityCommentID string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentStar{}).TableName()).Where("comment_id = ?", commentID).Updates(map[string]interface{}{
		"community_comment_id": communityCommentID,
	}).Error
}
