package user

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/logic/creatorhub"
	"trpc.publishing_application.standalonesite/app/pkg/metadatadecode"
)

// GetCreatorHubUserInfo 绑定的用户信息
func (s *UserImpl) GetCreatorHubUserInfo(c context.Context, req *pb.GetCreatorHubUserInfoReq) (*pb.GetCreatorHubUserInfoRsp, error) {
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	return creatorhub.GetBoundCreatorHubUserInfoByOpenID(c, openid)
}

// BindCreatorHubUser 绑定创作者号用户
func (s *UserImpl) BindCreatorHubAccount(c context.Context, req *pb.BindCreatorHubAccountReq) (*pb.BindCreatorHubAccountRsp, error) {
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	language := metadata.GetLangType(c)
	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	err = creatorhub.BindCreatorHubAccount(c, openid, req.Token, req.Uid, language, gameId, areaId)
	rsp := &pb.BindCreatorHubAccountRsp{}
	return rsp, err
}

// 修改My Submission 同步状态
func (s *UserImpl) ChangeSyncStatus(c context.Context, req *pb.ChangeSyncStatusReq) (*pb.ChangeSyncStatusRsp, error) {
	rsp := &pb.ChangeSyncStatusRsp{}
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	language := metadata.GetLangType(c)
	err = creatorhub.ChangeSyncStatus(c, openid, int(req.Status), language)
	return rsp, err
}
