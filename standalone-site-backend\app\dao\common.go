package dao

import (
	"fmt"
	"strings"
)

type OrderConditions struct {
	Column string // 排序字段
	IsDesc bool   // 是否倒排
}

// ConditionInterface 是一个通用接口，用于表示条件
type ConditionInterface interface {
	ToSQL() string
}

type Conditions struct {
	Column string      // 字段名称
	Symbol string      // 符号
	Value  interface{} // 值
}

type CompositeCondition struct {
	Operator   string
	Conditions []ConditionInterface
}

// 组装排序字段
func AssembleOrder(orderData []*OrderConditions) string {
	if len(orderData) == 0 {
		return ""
	}
	var orderList = make([]string, 0, len(orderData))
	for _, data := range orderData {
		var sortStr = "ASC"
		if data.IsDesc {
			sortStr = "DESC"
		}
		orderList = append(orderList, fmt.Sprintf("%s %s", data.Column, sortStr))
	}
	return strings.Join(orderList, ",")
}

func (c Conditions) ToSQL() string {
	return fmt.Sprintf("%s %s %s", c.<PERSON>umn, c.Symbol, formatInterface(c.Value))
}

// 组装or查询语句
func (cc CompositeCondition) ToSQL() string {
	var sqlParts []string

	for _, condition := range cc.Conditions {
		sqlPart := condition.ToSQL()
		if sqlPart != "" {
			sqlParts = append(sqlParts, sqlPart)
		}
	}

	return fmt.Sprintf("(%s)", strings.Join(sqlParts, fmt.Sprintf(" %s ", cc.Operator)))
}

// formatInterface 函数用于格式化 interface{} 类型的值
func formatInterface(value interface{}) string {
	switch v := value.(type) {
	case string:
		return fmt.Sprintf("%q", v) // 使用 %q 格式化字符串，带引号
	default:
		return fmt.Sprintf("%v", v) // 使用 %v 格式化其他类型
	}
}
