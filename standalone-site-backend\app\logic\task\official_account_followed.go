package task

import (
	"context"
	"encoding/json"
	"errors"
	"html"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetFollowTaskOfficialAccounts(ctx context.Context, intlOpenid string, language string) ([]*pbUser.UserCollectionItem, error) {
	var followList []*pbUser.UserCollectionItem
	// 先从缓存中获取
	followTaskOfficialAccountCacheKey := cache.GetOfficialAccountsListCacheKey(intlOpenid, language)
	followTaskOfficialAccountStr, err := redis.GetClient().Get(ctx, followTaskOfficialAccountCacheKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(followTaskOfficialAccountStr), &followList)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetOfficialAccountsListCacheKey err: %v\n", err)
		} else {
			return followList, nil
		}
	}
	// 缓存未获取，db中获取
	officialAccounts, _ := getOfficialAccounts(ctx)
	userInfos, err := dao.GetUserListByOpenid(officialAccounts)
	userFollows := []*model.UserCollection{}
	if intlOpenid != "" {
		userFollows, err = dao.UserCollectionList(&dao.UserCollectionConditions{
			IntlOpenid:    intlOpenid,
			ToIntlOpenids: officialAccounts,
		}, 0)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserPostsComments err: %v\n", err)
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, code.GetOfficialAccountError, "GetOfficialAccountsListCacheKey | Failed to get follow list.")
		}
	}

	for _, userInfo := range userInfos {
		userCollectionItem := &pbUser.UserCollectionItem{
			IntlOpenid:   intlOpenid,
			ToIntlOpenid: userInfo.IntlOpenid,
		}
		userFormated := userInfo.Format()
		userCollectionItem.UserInfo = formatted.ReturnDynamicProtoUserInfoFormatted(userFormated)
		// 判断是否已经关注
		if intlOpenid != "" {
			for _, userFollow := range userFollows {
				if userFollow.ToIntlOpenid == userInfo.IntlOpenid {
					userCollectionItem.IsFollow = true
					userCollectionItem.IsMutualFollow = userFollow.IsMutual == 1
				}
			}
		}
		// 重置认证用户昵称个签多语言
		authUserName, authUserRemark, _ := user.GetUserCerificationUserLanguage(ctx, userInfo.IntlOpenid, language)
		if authUserName != "" {
			userCollectionItem.UserInfo.Username = html.UnescapeString(authUserName)
		}
		if authUserRemark != "" {
			userCollectionItem.UserInfo.Remark = html.UnescapeString(authUserRemark)
		}
		authType := formatted.GetUserAuth(userFormated.IntlOpenid)
		authDesc := formatted.GetUserAuthDesc(ctx, userInfo.IntlOpenid, authType, language)
		userCollectionItem.UserInfo.AuthDesc = authDesc
		userCollectionItem.UserInfo.AuthType = authType
		avatarPendant, _ := user.GetUserCurWearedAvatarPendantIcon(ctx, userFormated.IntlOpenid)
		userCollectionItem.UserInfo.AvatarPendant = avatarPendant
		followList = append(followList, userCollectionItem)
	}
	// 写入缓存
	followTaskOfficialAccountByte, _ := json.Marshal(followList)
	redis.GetClient().Set(ctx, followTaskOfficialAccountCacheKey, string(followTaskOfficialAccountByte), 2*time.Minute)
	if intlOpenid != "" {
		cacheKey := cache.UserFollowsCacheKeyKeys(intlOpenid)
		redis.GetClient().SAdd(ctx, cacheKey, followTaskOfficialAccountCacheKey)
	}
	return followList, nil
}

func QuicklyFollowAllOfficialAccounts(ctx context.Context, intlOpenid string, gameId string, areaId string) error {
	officialAccounts, _ := getOfficialAccounts(ctx)
	collections, err := dao.UserCollectionsGet(0, []string{intlOpenid}, officialAccounts, false, false)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetUserCollectionUser err: %v\n", err)
			return errs.NewCustomError(ctx, code.QuicklyFollowOfficialAccountError, "GetUserCollectionUser failed")
		}
	}
	needFollowIntlOpenids := []string{}
	for _, officiaAccount := range officialAccounts {
		isFound := false
		for _, collection := range collections {
			if collection.ToIntlOpenid == officiaAccount {
				isFound = true
				break
			}
		}
		if !isFound {
			if officiaAccount == intlOpenid {
				continue
			}
			needFollowIntlOpenids = append(needFollowIntlOpenids, officiaAccount)
		}
	}
	if len(needFollowIntlOpenids) == 0 {
		return nil
	}
	err = user.UserBatchFollowOthersUsers(ctx, intlOpenid, needFollowIntlOpenids, gameId, areaId, true)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("UserBatchFollowOthersUsers err: %v\n", err)
		return err
	}
	for _, officialAccount := range needFollowIntlOpenids {
		user.DeleteUserInfoCache(ctx, officialAccount)
	}
	user.DeleteUserInfoCache(ctx, intlOpenid)
	// 先清除HasFollowedOfficialAccountsCacheKey 缓存
	hasFollowedOfficialAccountsCacheKey := cache.HasFollowedOfficialAccountsCacheKey(intlOpenid)
	redis.GetClient().Del(ctx, hasFollowedOfficialAccountsCacheKey)
	// cache.RemoveUserFollowsCache(ctx, intlOpenid)
	return nil
}

func HasFollowedOfficialAccounts(ctx context.Context, intlOpenid string) error {
	if intlOpenid == "" {
		return errs.NewCustomError(ctx, code.HasNotCompletedOfficialAccountFollow, "intlOpenid is empty")
	}
	// 先从缓存中获取
	hasFollowedOfficialAccountsCacheKey := cache.HasFollowedOfficialAccountsCacheKey(intlOpenid)
	hasFollowedOfficialAccountsCacheStr, err := redis.GetClient().Get(ctx, hasFollowedOfficialAccountsCacheKey).Result()
	if err == nil {
		if hasFollowedOfficialAccountsCacheStr == "true" {
			return nil
		} else if hasFollowedOfficialAccountsCacheStr == "false" {
			return errs.NewCustomError(ctx, code.HasNotCompletedOfficialAccountFollow, "not follow")
		}
	}
	// 缓存不存在
	officialAccounts, _ := getOfficialAccounts(ctx)
	var userFollows = []*model.UserCollection{}
	if intlOpenid != "" {
		userFollows, err = dao.UserCollectionList(&dao.UserCollectionConditions{
			IntlOpenid:    intlOpenid,
			ToIntlOpenids: officialAccounts,
		}, 0)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("UserCollectionList err: %v\n", err)
			redis.GetClient().Set(ctx, hasFollowedOfficialAccountsCacheKey, "false", 2*time.Minute)
			if intlOpenid != "" {
				cacheKey := cache.UserFollowsCacheKeyKeys(intlOpenid)
				redis.GetClient().SAdd(ctx, cacheKey, hasFollowedOfficialAccountsCacheKey)
			}
			return errs.NewCustomError(ctx, code.HasNotCompletedOfficialAccountFollow, "not follow")
		}
	}

	for _, officialAccount := range officialAccounts {
		if officialAccount == intlOpenid {
			continue
		}
		isFollow := false
		for _, userFollow := range userFollows {
			if userFollow.ToIntlOpenid == officialAccount {
				isFollow = true
			}
		}
		if !isFollow {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("intlOpenid: %s, officialAccount: %s, not follow", intlOpenid, officialAccount)
			redis.GetClient().Set(ctx, hasFollowedOfficialAccountsCacheKey, "false", 2*time.Minute)
			if intlOpenid != "" {
				cacheKey := cache.UserFollowsCacheKeyKeys(intlOpenid)
				redis.GetClient().SAdd(ctx, cacheKey, hasFollowedOfficialAccountsCacheKey)
			}
			return errs.NewCustomError(ctx, code.HasNotCompletedOfficialAccountFollow, "not follow")
		}
	}
	redis.GetClient().Set(ctx, hasFollowedOfficialAccountsCacheKey, "true", 2*time.Minute)
	if intlOpenid != "" {
		cacheKey := cache.UserFollowsCacheKeyKeys(intlOpenid)
		redis.GetClient().SAdd(ctx, cacheKey, hasFollowedOfficialAccountsCacheKey)
	}
	return nil
}

func getOfficialAccounts(ctx context.Context) ([]string, error) {
	conf := config.GetConfig()
	if conf.TasksConf == nil || conf.TasksConf.OfficialFollowTask == nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("official follow task config is nil")
		return []string{}, nil
	}
	return conf.TasksConf.OfficialFollowTask.OfficialIntlOpenids, nil
}
