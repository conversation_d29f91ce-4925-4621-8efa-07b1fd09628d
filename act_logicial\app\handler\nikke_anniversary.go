package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_nikke_tmp"
	"github.com/Shopify/sarama"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/common"
	"trpc.act.logicial/app/logic/nikketmp"
)

// AnniversaryVoteHandler 处理
func AnniversaryVoteHandler(ctx context.Context, msgArray []*sarama.ConsumerMessage) (err error) {
	fmt.Println("---------------AnniversaryVoteHandler---------------")
	var handlers []func() error
	allVoteList := make([]*pb.VoteItem, 0)
	for _, v := range msgArray {
		value := string(v.Value)
		// 解构数据
		var voteList []*pb.VoteItem
		errU := json.Unmarshal([]byte(value), &voteList)
		if errU != nil {
			// 告警LOG
			errs.NewCustomError(ctx, code.GetKafkaJsonErr, "json unmarshal error, error = %v", errU.Error())
		} else {
			allVoteList = append(allVoteList, voteList...)
		}

	}
	handlers = append(handlers, func() error {
		err := nikketmp.AddAnniversaryVoteFromKafka(common.GetRollBackCtx(), allVoteList)
		return err
	})
	err = trpc.GoAndWait(handlers...)
	return
}

// Anniversary2Point5thVoteHandler 2.5周年获取kafka投票处理
func Anniversary2Point5thVoteHandler(ctx context.Context, msgArray []*sarama.ConsumerMessage) (err error) {
	fmt.Println("---------------Anniversary25thVoteHandler---------------")
	log.WithFieldsContext(ctx, "log_type", "df_redeem_key", "int_field_1", "Anniversary2Point5thVoteHandler").
		Info("Anniversary25thVoteHandler start")
	var handlers []func() error
	allVoteList := make([]*pb.UserAnniversaryVoteList, 0, len(msgArray))
	for _, v := range msgArray {
		var userAnniversaryVoteList pb.UserAnniversaryVoteList
		value := string(v.Value)
		// 解构数据
		errU := json.Unmarshal([]byte(value), &userAnniversaryVoteList)
		if errU != nil {
			// 告警LOG
			errs.NewCustomError(ctx, code.GetKafkaJsonErr, "json unmarshal error, error = %v", errU.Error())
		} else {
			allVoteList = append(allVoteList, &userAnniversaryVoteList)
			log.WithFieldsContext(ctx, "log_type", "kafka_data", "int_field_1", cast.ToString(userAnniversaryVoteList.DayTimestamp),
				"int_field_2", cast.ToString(userAnniversaryVoteList.VoteType)).
				Infof("Anniversary2Point5thVoteHandler show data: VoteList:[%v]", userAnniversaryVoteList.VoteList)
		}
	}
	handlers = append(handlers, func() error {
		err = nikketmp.Anniversary25thVoteFromKafka(common.GetRollBackCtx(), allVoteList)
		return err
	})
	err = trpc.GoAndWait(handlers...)
	return
}
