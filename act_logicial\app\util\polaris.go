package util

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"
	"trpc.act.logicial/app/config"

	"git.code.oa.com/polaris/polaris-go/api"
)

// namespace 空间
//
//	|- Production: 生产环境
//	|- Pre-release: 预发布环境
//	|- Test: 测试环境
//	|- Development: 开发环境
//	|- Polaris: 北极星系统自用，请勿选择
//
// GetInstance 单例模式
var once sync.Once
var instance api.ConsumerAPI

// GetInstance TODO
func GetInstance() api.ConsumerAPI {
	once.Do(func() {
		fmt.Println("---------------once---------------")
		cfg := api.NewConfiguration()
		host := config.GetConfig().PolarisConf.Host
		//客户端内网IP：************
		//客户端公网IP：*************
		// cfg.GetGlobal().GetServerConnector().SetAddresses([]string{"************:8090"})
		// cfg.GetGlobal().GetServerConnector().SetProtocol("http")
		cfg.GetGlobal().GetServerConnector().SetAddresses([]string{host})
		//cfg.GetGlobal().GetServerConnector().SetAddresses([]string{"*************:8091"}) // 公网
		// cfg.GetGlobal().GetServerConnector().SetProtocol("trpc")
		// cfg.GetGlobal().GetAPI().SetBindIP("************")
		cfg.GetGlobal().GetServerConnector().SetMessageTimeout(5 * time.Second)
		cfg.GetGlobal().GetServerConnector().SetConnectTimeout(5 * time.Second)
		cfg.GetGlobal().GetAPI().SetTimeout(5 * time.Second)
		// cfg.GetGlobal().GetServerConnector().SetJoinPoint("singapore")
		consumer, err := api.NewConsumerAPIByConfig(cfg)
		// 如果出错了就返回错误信息
		if err != nil {
			panic(fmt.Sprintf("polaris get instance err: %v", err))
		}
		instance = consumer
	})
	return instance
}

// InArray TODO
func InArray(slice []string, elem string) bool {
	for _, v := range slice {
		if v == elem {
			return true
		}
	}
	return false
}

// GetServer 获取polaris ip
func GetServer(service string, namespace string) (ipPort string, err error) {

	// 未指定的命名空间
	exist := InArray([]string{"Production", "Pre-release", "Test", "Development"}, namespace)
	if !exist {
		err = fmt.Errorf("set polaris err not in {Production,Pre-release,Test,Development},val=%v", namespace)
		return
	}
	consumer := GetInstance()
	// consumer, errs := api.NewConsumerAPI()
	// defer consumer.Destroy()
	// if errs != nil {
	// 	// panic(fmt.Sprintf("polaris get consumer err: %v", err))
	// 	err = errs
	// 	return
	// }
	fmt.Println("---------------consumer---------------")
	fmt.Printf("%#v\n", consumer)
	var flowID uint64
	var getInstancesReq *api.GetOneInstanceRequest
	getInstancesReq = &api.GetOneInstanceRequest{}
	getInstancesReq.FlowID = atomic.AddUint64(&flowID, 1)
	// getInstancesReq.Namespace = "Production"
	// getInstancesReq.Service = "trpc.interact_video.iv_service.pro"
	getInstancesReq.Namespace = namespace
	getInstancesReq.Service = service
	fmt.Println("---------------getInstancesReq---------------")
	fmt.Printf("%#v\n", getInstancesReq)
	getInstResp, err := consumer.GetOneInstance(getInstancesReq)
	fmt.Printf("%#v\n", getInstResp)
	if nil != err {
		err = fmt.Errorf("fail to sync GetOneInstance, err is %v", err)
		return
	}
	targetInstance := getInstResp.Instances[0]
	ipPort = fmt.Sprintf("%s:%d", targetInstance.GetHost(), targetInstance.GetPort())
	return
}
