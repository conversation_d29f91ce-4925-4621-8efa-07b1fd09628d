package cloud

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	asts "github.com/aws/aws-sdk-go-v2/service/sts"
	sts "github.com/tencentyun/qcloud-cos-sts-sdk/go"
	"trpc.publishing_application.standalonesite/app/code"
	stconfig "trpc.publishing_application.standalonesite/app/config"
)

func GetCosSts(c context.Context, gameId, areaId string) (*pb.GetCosStsRsp, error) {
	cosConfig, err := getCosConfig(c, gameId, areaId)
	if err != nil {
		return nil, err
	}
	if cosConfig.CloudType == "tencentcloud" {
		return GetTencentCosSts(c, cosConfig)
	} else if cosConfig.CloudType == "awss3" {
		return GetAwsS3Sts(c, cosConfig)
	}

	return nil, nil
}

// 获取腾讯云 COS 临时密钥
func GetTencentCosSts(c context.Context, cosConfig *stconfig.COSSetting) (*pb.GetCosStsRsp, error) {

	client := sts.NewClient(
		cosConfig.SecretID,
		cosConfig.SecretKey,
		nil,
		sts.Host("sts.internal.tencentcloudapi.com"),
	)

	qcs := "qcs::cos:" + cosConfig.Region + ":uid/" + cosConfig.AppId + ":" + cosConfig.Bucket + "/standalonesite/ugc/public/"

	opt := &sts.CredentialOptions{
		DurationSeconds: int64(getDuration(cosConfig.STSDuration, 300)),
		Region:          cosConfig.Region,
		Policy: &sts.CredentialPolicy{
			Statement: []sts.CredentialPolicyStatement{
				{
					// 密钥的权限列表。简单上传和分片需要以下的权限
					// 其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
					Action: []string{
						"name/cos:HeadObject",
						// 简单上传
						"name/cos:PostObject",
						"name/cos:PutObject",
						// 分片上传
						"name/cos:InitiateMultipartUpload",
						"name/cos:ListMultipartUploads",
						"name/cos:ListParts",
						"name/cos:UploadPart",
						"name/cos:CompleteMultipartUpload",
					},
					Effect: "allow",
					Resource: []string{
						qcs + "image/*",
						qcs + "video/*",
					},
				},
			},
		},
	}

	res, credentialErr := client.GetCredential(opt)
	if credentialErr != nil {
		return nil, credentialErr
	}

	sts := &pb.GetCosStsRsp{
		AppId:        cosConfig.AppId,
		Bucket:       cosConfig.Bucket,
		Region:       cosConfig.Region,
		BucketDomain: cosConfig.BucketDomain,
		CdnDomain:    cosConfig.CDNDomain,
		CloudType:    cosConfig.CloudType,
		TmpSecretId:  res.Credentials.TmpSecretID,
		TmpSecretKey: res.Credentials.TmpSecretKey,
		Token:        res.Credentials.SessionToken,
		ExpiredTime:  int64(res.ExpiredTime),
		StartTime:    int64(res.StartTime),
		Expiration:   res.Expiration,
	}
	return sts, nil
}

// 获取 AWS S3 临时密钥
func GetAwsS3Sts(c context.Context, cosConfig *stconfig.COSSetting) (*pb.GetCosStsRsp, error) {
	cfg, err := config.LoadDefaultConfig(
		context.TODO(),
		config.WithRegion(cosConfig.Region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			cosConfig.SecretID,
			cosConfig.SecretKey,
			"",
		)),
	)

	if err != nil {
		return nil, err
	}

	stsClient := asts.NewFromConfig(cfg)

	arn := "arn:aws:s3:::" + cosConfig.Bucket + "/standalonesite/ugc/public"

	duration := getDuration(cosConfig.STSDuration, 900)

	// 调用 GetFederationToken 以获取临时密钥
	res, err := stsClient.GetFederationToken(context.TODO(), &asts.GetFederationTokenInput{
		Name:            aws.String("lip"),
		DurationSeconds: aws.Int32(int32(duration)),
		Policy: aws.String(fmt.Sprintf(`{
			"Version": "2012-10-17",
			"Statement": [
				{
					"Sid": "VisualEditor0",
					"Effect": "Allow",
					"Action": [
						"s3:HeadObject",
						"s3:GetObject",
						"s3:PutObject"
					],
					"Resource": [
						"%s/image/*",
						"%s/video/*"
					]
				}
			]
		}`, arn, arn)),
	})

	if err != nil {
		return nil, err
	}

	sts := &pb.GetCosStsRsp{
		AppId:        cosConfig.AppId,
		Bucket:       cosConfig.Bucket,
		Region:       cosConfig.Region,
		CdnDomain:    cosConfig.CDNDomain,
		CloudType:    cosConfig.CloudType,
		TmpSecretId:  *res.Credentials.AccessKeyId,
		TmpSecretKey: *res.Credentials.SecretAccessKey,
		Token:        *res.Credentials.SessionToken,
		Expiration:   res.Credentials.Expiration.Format(time.RFC3339),
		StartTime:    time.Now().Unix(),
		ExpiredTime:  res.Credentials.Expiration.Unix(),
	}
	return sts, nil
}

// 读取 COS 的配置
func getCosConfig(c context.Context, gameId, areaId string) (*stconfig.COSSetting, error) {
	conf := stconfig.GetConfig()
	var cosConfig *stconfig.COSSetting
	for _, v := range conf.COS {
		cmsGameidS := strconv.Itoa(v.CmsGameId)
		if cmsGameidS == gameId && v.CmsAreaId == areaId {
			cosConfig = v
		}
	}

	if cosConfig == nil || cosConfig.SecretID == "" || cosConfig.SecretKey == "" {
		return cosConfig, errs.NewCustomError(c, code.GetCOSConfigError, "getCosConfig | get cos failed")
	}

	if cosConfig.CloudType != "tencentcloud" && cosConfig.CloudType != "awss3" {
		return cosConfig, errs.NewCustomError(c, code.IllegalCloudObjectType, "getCosConfig | error cloud type")
	}

	return cosConfig, nil
}

// 计算过期时长
func getDuration(d, de int) int {
	if d <= 0 {
		return de
	}
	return d
}
