package nikketmp

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_nikke_tmp"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/logic/common"
	"trpc.act.logicial/app/logic/nikketmp"
	"trpc.act.logicial/app/logic/sensitivewords"
)

// NikkeTmpImpl TODO
type NikkeTmpImpl struct {
	pb.UnimplementedNikkeTmp
}

// AddSendLog0101 NIKKE0101
func (s *NikkeTmpImpl) AddSendLog0101(ctx context.Context, req *pb.AddSendLog0101Req) (rsp *pb.AddSendLog0101Rsp,
	err error) {
	rsp = &pb.AddSendLog0101Rsp{}

	err = nikketmp.AddSendLog0101(ctx, req.FsourceId, req.PresentId, req.LangType)
	return
}

// ScheduledSend0101 NIKKE0101
func (s *NikkeTmpImpl) ScheduledSend0101(ctx context.Context, req *pb.ScheduledSend0101Req) (
	rsp *pb.ScheduledSend0101Rsp, err error) {
	rsp = &pb.ScheduledSend0101Rsp{}
	fmt.Println(">>>>>>>>>>>>>>ScheduledSend0101<<<<<<<<<<<<<<<<<")
	go nikketmp.ScheduledSend(ctx)
	return
}

// RecordMessage0101 NIKKE0101
func (s *NikkeTmpImpl) RecordMessage0101(ctx context.Context, req *pb.RecordMessage0101Req) (
	rsp *pb.RecordMessage0101Rsp, err error) {
	rsp = &pb.RecordMessage0101Rsp{}
	// 判断是不是包含敏感词
	// 获取随机uuid
	sensitiveGameId := "2797"
	openId := common.GetUUID()
	nick := strings.Join([]string{sensitivewords.NICK, openId}, "")
	isSensitive, err := sensitivewords.CheckSensitive(ctx, req.Message, sensitiveGameId, openId, nick, "0")
	if err != nil {
		return
	}
	if isSensitive {
		err = errs.NewCustomError(ctx, code.IsSensitive, "is sensitive")
		return
	}
	// 判断有没有匹配
	level, err := nikketmp.CheckNikkeMessageEasterEggs(ctx, req.Message)
	if err != nil {
		return
	}
	if level > 0 {
		rsp.Result = level
		return
	}
	// 情感分析
	sentimentType, err := nikketmp.GetSentimentType(ctx, req.Message)
	if err != nil {
		return
	}
	rsp.SentimentType = sentimentType
	return
}

// RecordMessage0101Pressure 情感分析
func (s *NikkeTmpImpl) RecordMessage0101Pressure(ctx context.Context, req *pb.RecordMessage0101Req) (
	rsp *pb.RecordMessage0101Rsp, err error) {
	rsp = &pb.RecordMessage0101Rsp{}
	// 活动结束关闭
	if time.Now().Unix() > 1705589999 {
		return
	}
	// 情感分析
	sentimentType, err := nikketmp.GetSentimentType(ctx, req.Message)
	if err != nil {
		return
	}
	rsp.SentimentType = sentimentType
	return

}
func filter(arr []int32, condition func(int32) bool) []int32 {
	var result []int32
	for _, v := range arr {
		if condition(v) {
			result = append(result, v)
		}
	}
	return result
}

// AddSendLogValentine nikke 情人节活动添加发货记录
func (s *NikkeTmpImpl) AddSendLogValentine(ctx context.Context, req *pb.AddSendLogValentineReq) (
	rsp *pb.AddSendLogValentineRsp, err error) {
	rsp = &pb.AddSendLogValentineRsp{}

	err = nikketmp.AddSendLogValentine(ctx, req.FsourceId, req.Tag, req.LangType)
	if err != nil {
		rsp.LogHasAdd = false
		return
	}
	var sendLogs []int32
	err, sendLogs = nikketmp.ValentineLogs(ctx, req.FsourceId)
	var filterLogs []int32
	for _, v := range sendLogs {
		if v > 2 && v < 13 {
			filterLogs = append(filterLogs, v)
		}
	}
	if len(filterLogs) == 10 {
		// 全部
		err = nikketmp.AddSendLogValentine(ctx, req.FsourceId, 13, req.LangType)
	}
	// 首次和全部完成添加记录发奖
	rsp.LogHasAdd = true
	return
}

// ScheduledSendValentine nikke 情人节定时发货
func (s *NikkeTmpImpl) ScheduledSendValentine(ctx context.Context, req *pb.ScheduledSendValentineReq) (
	rsp *pb.ScheduledSendValentineRsp, err error) {
	rsp = &pb.ScheduledSendValentineRsp{}
	fmt.Println(">>>>>>>>>>>>>>ScheduledSendValentine<<<<<<<<<<<<<<<<<")
	go nikketmp.ScheduledSendValentine(ctx)
	return
}

// GetSendLogValentine nikke 情人节活动获取发货记录
func (s *NikkeTmpImpl) GetSendLogValentine(ctx context.Context, req *pb.GetSendLogValentineReq) (
	rsp *pb.GetSendLogValentineRsp, err error) {
	rsp = &pb.GetSendLogValentineRsp{}
	err, rsp.TodayMake = nikketmp.IsSendValentineToday(ctx, req.FsourceId)
	if err != nil {
		return
	}
	err, rsp.Makeds = nikketmp.ValentineLogs(ctx, req.FsourceId)
	return
}

// RecordGuess NIKKE 1.5周年记录竞猜
func (s *NikkeTmpImpl) RecordGuess(ctx context.Context, req *pb.RecordGuessReq) (
	rsp *pb.RecordGuessRsp, err error) {
	if err = nikketmp.RecordGuessOrVote(ctx, req.GuessList, nikketmp.GuessTagId); err != nil {
		return
	}
	return &pb.RecordGuessRsp{}, nil
}

// RecordVote NIKKE 1.5周年记录投票
func (s *NikkeTmpImpl) RecordVote(ctx context.Context, req *pb.RecordVoteReq) (
	rsp *pb.RecordVoteRsp, err error) {
	if err = nikketmp.RecordGuessOrVote(ctx, req.VoteList, nikketmp.VoteTagId); err != nil {
		return
	}
	return &pb.RecordVoteRsp{}, nil
}

// GetVoteRecords NIKKE 1.5周年竞猜记录、投票记录
func (s *NikkeTmpImpl) GetVoteRecords(ctx context.Context, req *pb.GetVoteRecordsReq) (
	rsp *pb.GetVoteRecordsRsp, err error) {

	tagId, err := cast.ToIntE(req.GetTagId())
	if err != nil {
		return nil, errs.NewCustomError(ctx, code.TagTypeErr, "TagId Type Err")
	}
	voteRecords, err := nikketmp.GetVoteRecords(ctx, tagId)
	if err != nil {
		return nil, err
	}
	return &pb.GetVoteRecordsRsp{
		VoteList: voteRecords,
	}, nil
}

// RecordGift NIKKE 1.5周年记录礼包
func (s *NikkeTmpImpl) RecordGift(ctx context.Context, req *pb.RecordGiftReq) (
	rsp *pb.RecordGiftRsp, err error) {
	if err = nikketmp.RecordGift(ctx, req.PresentId, req.RoleInfo, req.TagId, req.VoteList, req.GuessList); err != nil {
		return nil, err
	}
	return &pb.RecordGiftRsp{}, nil
}

// ShowVoteList NIKKE 1.5周年展示投票列表
func (s *NikkeTmpImpl) ShowVoteList(ctx context.Context, req *pb.ShowVoteListReq) (
	rsp *pb.ShowVoteListRsp, err error) {
	list, err := nikketmp.ShowVoteList(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.ShowVoteListRsp{
		VoteList: list,
	}, nil
}

// RecordUserActivity NIKKE 1.5周年记录参加活动的用户
func (s *NikkeTmpImpl) RecordUserActivity(ctx context.Context, req *pb.RecordUserActivityReq) (
	rsp *pb.RecordUserActivityRsp, err error) {
	if err = nikketmp.RecordUserActivity(ctx); err != nil {
		return nil, err
	}
	return &pb.RecordUserActivityRsp{}, nil
}

// CountNumberParticipantsRegularly NIKKE 1.5周年定时统计参与活动人数
func (s *NikkeTmpImpl) CountNumberParticipantsRegularly(ctx context.Context,
	req *pb.CountNumberParticipantsRegularlyReq) (
	rsp *pb.CountNumberParticipantsRegularlyRsp, err error) {
	if err = nikketmp.CountNumberParticipantsRegularly(ctx); err != nil {
		return nil, err
	}
	return &pb.CountNumberParticipantsRegularlyRsp{}, nil
}

// VotesTalliedHourly NIKKE 1.5周年每小时统计得票数
func (s *NikkeTmpImpl) VotesTalliedHourly(ctx context.Context, req *pb.VotesTalliedHourlyReq) (
	rsp *pb.VotesTalliedHourlyRsp, err error) {
	if err = nikketmp.VotesTalliedHourlySendBot(ctx); err != nil {
		return nil, err
	}
	return &pb.VotesTalliedHourlyRsp{}, nil
}

// InitializeDatabase 初始化数据库
func (s *NikkeTmpImpl) InitializeDatabase(ctx context.Context, req *pb.InitializeDatabaseReq) (
	rsp *pb.InitializeDatabaseRsp, err error) {
	if err = nikketmp.InitializeDatabase(ctx); err != nil {
		return nil, err
	}
	return &pb.InitializeDatabaseRsp{}, nil
}

// VoteResultsUpdateCache 定时任务更新每天总票数到缓存
func (s *NikkeTmpImpl) VoteResultsUpdateCache(ctx context.Context, req *pb.VoteResultsUpdateCacheReq) (
	rsp *pb.VoteResultsUpdateCacheRsp, err error) {
	if err = nikketmp.VoteResultsUpdateCache(ctx); err != nil {
		return nil, err
	}
	return &pb.VoteResultsUpdateCacheRsp{}, nil
}

// ScheduledSendAnniversary 1.5周年定时发货任务
func (s *NikkeTmpImpl) ScheduledSendAnniversary(ctx context.Context, req *pb.ScheduledSendAnniversaryReq) (
	rsp *pb.ScheduledSendAnniversaryRsp, err error) {
	if err = nikketmp.ScheduledSendAnniversary(ctx); err != nil {
		return nil, err
	}
	return &pb.ScheduledSendAnniversaryRsp{}, nil
}

// DelUserVotingHistory 测试用接口
func (s *NikkeTmpImpl) DelUserVotingHistory(ctx context.Context, req *pb.DelUserVotingHistoryReq) (
	rsp *pb.DelUserVotingHistoryRsp, err error) {
	if err = nikketmp.DelUserVotingHistory(ctx, req.DelType, req.OpenId); err != nil {
		return nil, err
	}
	return &pb.DelUserVotingHistoryRsp{}, nil
}

// RefreshCache 测试用接口
func (s *NikkeTmpImpl) RefreshCache(ctx context.Context, req *pb.RefreshCacheReq) (
	rsp *pb.RefreshCacheRsp, err error) {
	if err = nikketmp.RefreshCache(ctx); err != nil {
		return nil, err
	}
	return &pb.RefreshCacheRsp{}, nil
}

// MovedForwardOneDay 调用接口数据前移一天
func (s *NikkeTmpImpl) MovedForwardOneDay(ctx context.Context, req *pb.MovedForwardOneDayReq) (
	rsp *pb.MovedForwardOneDayRsp, err error) {
	if err = nikketmp.MovedForwardOneDay(ctx, req.OpenId); err != nil {
		return nil, err
	}
	return &pb.MovedForwardOneDayRsp{}, nil
}

// DelTestUserVotingHistory TODO
func (s *NikkeTmpImpl) DelTestUserVotingHistory(ctx context.Context, req *pb.DelTestUserVotingHistoryReq) (
	rsp *pb.DelTestUserVotingHistoryRsp, err error) {
	if err = nikketmp.DelTestUserVotingHistory(ctx, req.DelType); err != nil {
		return nil, err
	}
	return &pb.DelTestUserVotingHistoryRsp{}, nil
}

// UserGiftCollectionRecord 用户礼包获取状态
func (s *NikkeTmpImpl) UserGiftCollectionRecord(ctx context.Context, req *pb.UserGiftCollectionRecordReq) (
	rsp *pb.UserGiftCollectionRecordRsp, err error) {
	rsp, err = nikketmp.UserGiftCollectionRecord(ctx, req.RoleInfo)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// CountVoteByAreaId TODO
func (s *NikkeTmpImpl) CountVoteByAreaId(ctx context.Context, req *pb.CountVoteByAreaIdReq) (
	rsp *pb.CountVoteByAreaIdRsp, err error) {
	if err = nikketmp.CountVoteByAreaId(ctx); err != nil {
		return nil, err
	}
	return &pb.CountVoteByAreaIdRsp{}, nil
}

// InitializeAreaVotingCountDb TODO
func (s *NikkeTmpImpl) InitializeAreaVotingCountDb(ctx context.Context, req *pb.InitializeAreaVotingCountDbReq) (
	rsp *pb.InitializeAreaVotingCountDbRsp, err error) {
	if err = nikketmp.InitializeAreaVotingCountDb(ctx); err != nil {
		return nil, err
	}
	return &pb.InitializeAreaVotingCountDbRsp{}, nil
}

// BunnyGirlRecordGift TODO
func (s *NikkeTmpImpl) BunnyGirlRecordGift(ctx context.Context, req *pb.BunnyGirlRecordGiftReq) (
	rsp *pb.BunnyGirlRecordGiftRsp, err error) {
	var isFirst bool
	if isFirst, err = nikketmp.BunnyGirlRecordGift(ctx, req.PresentId, req.RoleInfo); err != nil {
		return nil, err
	}
	return &pb.BunnyGirlRecordGiftRsp{
		IsFirst: isFirst,
	}, nil
}

// ScheduledSendBunnyGirl 兔女郎定时发货任务
func (s *NikkeTmpImpl) ScheduledSendBunnyGirl(ctx context.Context, req *pb.ScheduledSendBunnyGirlReq) (
	rsp *pb.ScheduledSendBunnyGirlRsp, err error) {
	log.WithFieldsContext(ctx, "log_type", "debug").
		Infof("==== ScheduledSendBunnyGirl start ====")
	if err = nikketmp.ScheduledSendBunnyGirl(ctx); err != nil {
		return nil, err
	}
	return &pb.ScheduledSendBunnyGirlRsp{}, nil
}

// RedHoodSkinVoteRecordGift 小红帽皮肤投票活动
func (s *NikkeTmpImpl) RedHoodSkinVoteRecordGift(ctx context.Context, req *pb.RedHoodSkinVoteRecordGiftReq) (
	rsp *pb.RedHoodSkinVoteRecordGiftRsp, err error) {
	if err = nikketmp.RedHoodSkinVoteRecordGift(ctx, req.PresentId, req.RoleInfo); err != nil {
		return nil, err
	}
	return &pb.RedHoodSkinVoteRecordGiftRsp{}, nil
}

// ScheduledSendRedHoodSkinVote 小红帽皮肤投票定时发货任务
func (s *NikkeTmpImpl) ScheduledSendRedHoodSkinVote(ctx context.Context, req *pb.ScheduledSendRedHoodSkinVoteReq) (
	rsp *pb.ScheduledSendRedHoodSkinVoteRsp, err error) {
	log.WithFieldsContext(ctx, "log_type", "debug").
		Infof("==== ScheduledSendRedHoodSkinVote start ====")
	if err = nikketmp.ScheduledSendRedHoodSkinVote(ctx); err != nil {
		return nil, err
	}
	return &pb.ScheduledSendRedHoodSkinVoteRsp{}, nil
}

// CheckUserGiftPackReceived nikke通用判断当前用户是否已获取当前礼包
func (s *NikkeTmpImpl) CheckUserGiftPackReceived(ctx context.Context, req *pb.CheckUserGiftPackReceivedReq) (
	rsp *pb.CheckUserGiftPackReceivedRsp, err error) {

	var exist bool
	if exist, err = nikketmp.CheckUserGiftPackReceived(ctx, req.TableName, req.PresentId, req.HasSent); err != nil {
		return nil, err
	}
	return &pb.CheckUserGiftPackReceivedRsp{
		Exist: exist,
	}, nil
}

// RedHoodVotesTalliedHourlySendBot 小红帽定时获取票数
func (s *NikkeTmpImpl) RedHoodVotesTalliedHourlySendBot(ctx context.Context,
	req *pb.RedHoodVotesTalliedHourlySendBotReq) (
	rsp *pb.RedHoodVotesTalliedHourlySendBotRsp, err error) {
	rsp = &pb.RedHoodVotesTalliedHourlySendBotRsp{}
	if err = nikketmp.RedHoodVotesTalliedHourlySendBot(ctx); err != nil {
		return nil, err
	}
	return
}

// IndependentRecordGift 独立站记录礼包信息
func (s *NikkeTmpImpl) IndependentRecordGift(ctx context.Context, req *pb.IndependentRecordGiftReq) (
	rsp *pb.IndependentRecordGiftRsp, err error) {
	var isFirst bool
	if isFirst, err = nikketmp.IndependentRecordGift(ctx, req.PresentId, req.RoleInfo); err != nil {
		return nil, err
	}
	return &pb.IndependentRecordGiftRsp{
		IsFirst: isFirst,
	}, nil
}

// CheckSensitive 判断是否是敏感词
func (s *NikkeTmpImpl) CheckSensitive(ctx context.Context, req *pb.CheckSensitiveReq) (
	rsp *pb.CheckSensitiveRsp, err error) {
	rsp = &pb.CheckSensitiveRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	isSensitive, err := sensitivewords.CheckSensitive(ctx, req.Message, req.RoleInfo.GameId, strings.Split(userAccount.Uid,
		"-")[1], req.RoleInfo.RoleName, "0")
	if err != nil {
		return
	}
	rsp.IsSensitive = isSensitive
	return
}

// UpdateKillDataPeriodically 无限莱彻定时更新击杀数据
func (s *NikkeTmpImpl) UpdateKillDataPeriodically(ctx context.Context, req *pb.UpdateKillDataPeriodicallyReq) (
	rsp *pb.UpdateKillDataPeriodicallyRsp, err error) {
	rsp = &pb.UpdateKillDataPeriodicallyRsp{}

	if err = nikketmp.UpdateKillDataPeriodically(ctx, req.A1Num, req.A2Num, req.RandomRange); err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetLecherKillItem NIKKE 获取击杀数据
func (s *NikkeTmpImpl) GetLecherKillItem(ctx context.Context, req *pb.GetLecherKillItemReq) (
	rsp *pb.GetLecherKillItemRsp, err error) {

	killItem, err := nikketmp.GetLecherKillItem(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.GetLecherKillItemRsp{
		CurrentKillNum: killItem.CurrentKillNum,
		CurrentLeftNum: killItem.CurrentLeftNum,
		LastKillNum:    killItem.LastKillNum,
		LastLeftNum:    killItem.LastLeftNum,
	}, nil
}

// GetKillLecherUserItem 获取用户击杀数据详情
func (s *NikkeTmpImpl) GetKillLecherUserItem(ctx context.Context, req *pb.GetKillLecherUserItemReq) (
	rsp *pb.GetKillLecherUserItemRsp, err error) {

	lecherUserItem, err := nikketmp.GetKillLecherUserItem(ctx, req.RoleInfo)
	if err != nil {
		return nil, err
	}
	if lecherUserItem.FinishTimeKillNum <= 105929 {
		lecherUserItem.FinishTimeKillNum = 105929
	}
	return &pb.GetKillLecherUserItemRsp{
		HasFinish:             lecherUserItem.HasFinish,
		FinishTime:            lecherUserItem.ClearanceTime,
		AccumulatedOnlineTime: lecherUserItem.OnlineTimeNum,
		PassNum:               lecherUserItem.CompletedNum,
		FinishTimeKillNum:     lecherUserItem.FinishTimeKillNum,
	}, nil
}

// LecherShareRecordGift 无限莱彻分享礼包记录
func (s *NikkeTmpImpl) LecherShareRecordGift(ctx context.Context, req *pb.LecherShareRecordGiftReq) (
	rsp *pb.LecherShareRecordGiftRsp, err error) {
	rsp = &pb.LecherShareRecordGiftRsp{}
	var isFirst bool
	if isFirst, err = nikketmp.LecherShareRecordGift(ctx, req.PresentId, req.RoleInfo); err != nil {
		return nil, err
	}
	rsp.IsFirst = isFirst
	return rsp, nil
}

// ScheduledSendLecherShareGift 无限莱彻定时发送分享礼包
func (s *NikkeTmpImpl) ScheduledSendLecherShareGift(ctx context.Context, req *pb.ScheduledSendLecherShareGiftReq) (
	rsp *pb.ScheduledSendLecherShareGiftRsp, err error) {
	rsp = &pb.ScheduledSendLecherShareGiftRsp{}

	if err = nikketmp.ScheduledSendLecherShareGift(ctx); err != nil {
		return nil, err
	}
	return rsp, nil
}

// NikkeLecherUserGiftCollectionRecord TODO
func (s *NikkeTmpImpl) NikkeLecherUserGiftCollectionRecord(ctx context.Context,
	req *pb.NikkeLecherUserGiftCollectionRecordReq) (
	rsp *pb.NikkeLecherUserGiftCollectionRecordRsp, err error) {
	rsp = &pb.NikkeLecherUserGiftCollectionRecordRsp{}

	record, err := nikketmp.NikkeLecherUserGiftCollectionRecord(ctx)
	if err != nil {
		return nil, err
	}
	rsp.UserHasPresentList = record
	return rsp, nil
}

// CheckUserPostByNIKKEH5Page TODO
func (s *NikkeTmpImpl) CheckUserPostByNIKKEH5Page(ctx context.Context, req *pb.CheckUserPostByNIKKEH5PageReq) (
	*pb.CheckUserPostByNIKKEH5PageRsp, error) {
	rsp := &pb.CheckUserPostByNIKKEH5PageRsp{}
	err := nikketmp.CheckUserPostByNIKKEH5Page(ctx)
	if err != nil {
		return nil, err
	}

	return rsp, nil
}

// RecordAnniversary2Point5Vote 记录2.5周年普通投票
func (s *NikkeTmpImpl) RecordAnniversary2Point5Vote(ctx context.Context, req *pb.RecordAnniversary2Point5VoteReq) (
	rsp *pb.RecordAnniversary2Point5VoteRsp, err error) {
	err = nikketmp.RecordAnniversary2Point5thVote(ctx, req.GetVoteList(), req.GetRoleInfo())
	if err != nil {
		return nil, err
	}
	return &pb.RecordAnniversary2Point5VoteRsp{}, nil
}

// GetAnniversary2Point5VoteRecords 2.5周年 获取投票记录
func (s *NikkeTmpImpl) GetAnniversary2Point5VoteRecords(ctx context.Context,
	req *pb.GetAnniversary2Point5VoteRecordsReq) (
	rsp *pb.GetAnniversary2Point5VoteRecordsRsp, err error) {
	voteRecords, err := nikketmp.GetAnniversary2Point5thVoteRecords(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.GetAnniversary2Point5VoteRecordsRsp{
		VoteList: voteRecords,
	}, nil
}

// ShowAnniversary2Point5VoteList 2.5周年 展示普通投票列表
func (s *NikkeTmpImpl) ShowAnniversary2Point5VoteList(ctx context.Context, req *pb.ShowAnniversary2Point5VoteListReq) (
	rsp *pb.ShowAnniversary2Point5VoteListRsp, err error) {
	voteList, err := nikketmp.ShowAnniversary2Point5thVoteList(ctx)
	if err != nil {
		return nil, err
	}

	return &pb.ShowAnniversary2Point5VoteListRsp{
		VoteList: voteList,
	}, nil
}

// RecordAnniversary2Point5ParticipationUser NIKKE 2.5周年记录参加活动的用户
func (s *NikkeTmpImpl) RecordAnniversary2Point5ParticipationUser(ctx context.Context,
	req *pb.RecordAnniversary2Point5ParticipationUserReq) (
	rsp *pb.RecordAnniversary2Point5ParticipationUserRsp, err error) {
	err = nikketmp.RecordAnniversary2Point5ParticipationUser(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.RecordAnniversary2Point5ParticipationUserRsp{}, nil
}

// CalculateAnniversary2Point5ParticipationCount NIKKE 2.5周年定时统计参与活动人数
func (s *NikkeTmpImpl) CalculateAnniversary2Point5ParticipationCount(ctx context.Context,
	req *pb.CalculateAnniversary2Point5ParticipationCountReq) (
	rsp *pb.CalculateAnniversary2Point5ParticipationCountRsp, err error) {
	err = nikketmp.CalculateAnniversary2Point5ParticipationCount(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CalculateAnniversary2Point5ParticipationCountRsp{}, nil
}

// TallyAnniversary2Point5VotesHourly NIKKE 2.5周年每小时统计得票数
func (s *NikkeTmpImpl) TallyAnniversary2Point5VotesHourly(ctx context.Context,
	req *pb.TallyAnniversary2Point5VotesHourlyReq) (
	rsp *pb.TallyAnniversary2Point5VotesHourlyRsp, err error) {
	err = nikketmp.TallyAnniversary2Point5thVotesHourly(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.TallyAnniversary2Point5VotesHourlyRsp{}, nil
}

// UpdateDailyVoteTotalsCacheForAnniversary2Point5 NIKKE 2.5周年定时任务更新每天总票数到缓存
func (s *NikkeTmpImpl) UpdateDailyVoteTotalsCacheForAnniversary2Point5(ctx context.Context,
	req *pb.UpdateDailyVoteTotalsCacheForAnniversary2Point5Req) (
	rsp *pb.UpdateDailyVoteTotalsCacheForAnniversary2Point5Rsp, err error) {
	err = nikketmp.UpdateDailyVoteTotalsCacheForAnniversary2Point5th(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.UpdateDailyVoteTotalsCacheForAnniversary2Point5Rsp{}, nil
}

// Anniversary2Point5InitializeDatabase 初始化数据库
func (s *NikkeTmpImpl) Anniversary2Point5InitializeDatabase(ctx context.Context,
	req *pb.Anniversary2Point5InitializeDatabaseReq) (*pb.Anniversary2Point5InitializeDatabaseRsp, error) {
	err := nikketmp.Anniversary2Point5thInitializeDatabase(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.Anniversary2Point5InitializeDatabaseRsp{}, nil
}

// WriteSpecialVoteToKafka 特殊投票计入kafka
func (s *NikkeTmpImpl) WriteSpecialVoteToKafka(ctx context.Context,
	req *pb.WriteSpecialVoteToKafkaReq) (*pb.WriteSpecialVoteToKafkaRsp, error) {
	err := nikketmp.WriteSpecialVoteToKafka(ctx, req.TagId, req.RoleInfo)
	if err != nil {
		return nil, err
	}
	return &pb.WriteSpecialVoteToKafkaRsp{}, nil
}

// MovedForwardOneDay2Point5Anniversary 测试用 - 主投票数据前移一天
func (s *NikkeTmpImpl) MovedForwardOneDay2Point5Anniversary(ctx context.Context,
	req *pb.MovedForwardOneDay2Point5AnniversaryReq) (*pb.MovedForwardOneDay2Point5AnniversaryRsp, error) {
	err := nikketmp.MovedForwardOneDay2Point5Anniversary(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	return &pb.MovedForwardOneDay2Point5AnniversaryRsp{}, nil
}

// RefreshingNumVote2Point5Anniversary 测试用 - 刷新主投票数
func (s *NikkeTmpImpl) RefreshingNumVote2Point5Anniversary(ctx context.Context,
	req *pb.RefreshingNumVote2Point5AnniversaryReq) (*pb.RefreshingNumVote2Point5AnniversaryRsp, error) {
	err := nikketmp.RefreshingNumVote2Point5Anniversary(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.RefreshingNumVote2Point5AnniversaryRsp{}, nil
}

// GetLipPointNikke2Point5 Lip 2.5周年累计充值积分
func (s *NikkeTmpImpl) GetLipPointNikke2Point5(ctx context.Context, req *pb.GetLipPointNikke2Point5Req) (
	rsp *pb.GetLipPointNikke2Point5Rsp, err error) {
	rsp = &pb.GetLipPointNikke2Point5Rsp{}
	point, err := nikketmp.GetLipPointNikke2Point5(ctx, req.GameId)
	if err != nil {
		return
	}
	rsp.Point = point
	return
}
