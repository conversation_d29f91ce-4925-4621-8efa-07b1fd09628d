package constants

type MessageT int8

/*
	通知类型:

1动态，2动态评论，3动态回复，4动态私信，5动态点赞，6用户关注你 ，7官方删除动态，8官方删除动态评论，9动态评论点赞，10动态回复点赞，
11官方忽略用户昵称，12官方忽略用户签名，13官方忽略动态，14官方忽略评论，15官方对用户评论封禁，16官方对用户动态封禁，
17官方对用户账号封禁，18官方删除昵称，19官方删除签名  20站内信, 21举报反馈, 22官方对用户账号禁言
99系统通知
*/
const (
	MsgTypePost MessageT = iota + 1
	MsgTypeComment
	MsgTypeReply
	MsgTypeWhisper
	MsgTypeStar
	MsgTypeFollow
	MsgTypeOfficialDeletePost
	MsgTypeOfficialDeleteComment
	MsgTypeCommentStar
	MsgTypeCommentReplyStar
	MsgTypeOfficialIgnoreUserName
	MsgTypeOfficialIgnoreUserRemark
	MsgTypeOfficialIgnorePost
	MsgTypeOfficialIgnoreComment
	MsgTypeOfficialCommentBan
	MsgTypeOfficialPostBan
	MsgTypeOfficialAccountBan
	MsgTypeOfficialDeleteUserName
	MsgTypeOfficialDeleteUserRemark
	MsgTypeSiteMessage
	MsgTypeReportMessage
	MsgTypeOfficialAccountMute
	MsgTypeSystem MessageT = 99
	//MsgTypeRequestingFriend

	MsgStatusUnread = 0
	MsgStatusReaded = 1
)

type MessageReadType int8

const (
	UnReadMessage MessageReadType = iota
	ReadMessage
)

const (
	CommentMessageCount = "comment_message_count"
	FollowMessageCount  = "follow_message_count"
	LikeMessageCount    = "like_message_count"
	SiteMessageCount    = "site_message_count"
)

type GetMessageType int8

const (
	CommentMessageType GetMessageType = iota + 1
	FollowMessageType
	LikeMessageType
	SiteMessageType
)

type SiteMessageSubType int32

const (
	SiteMessageTypeNormal SiteMessageSubType = iota + 1
	SIteMessageTypeAvatarPendant
	SiteMessageTypeCommentBubble // 评论气泡
)
