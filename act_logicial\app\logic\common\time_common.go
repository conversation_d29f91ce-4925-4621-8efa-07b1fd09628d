package common

import (
	"errors"
	"fmt"
	"time"
)

// GetStartOfDay 获取当天起始时间
func GetStartOfDay() int64 {
	now := time.Now()
	location, _ := time.LoadLocation("Asia/Tokyo") // 设置时区为 UTC+9
	nowInUTC9 := now.In(location)
	startOfDay := time.Date(nowInUTC9.Year(), nowInUTC9.Month(), nowInUTC9.Day(), 0, 0, 0, 0, location)
	return startOfDay.Unix()
}

// GetCurrentTimestamp 获取当前时间
func GetCurrentTimestamp() int64 {
	// 获取当前时间
	now := time.Now()
	location, _ := time.LoadLocation("Asia/Tokyo") // 设置时区为 UTC+9
	nowInUTC9 := now.In(location)
	return nowInUTC9.Unix()
}

func GetTimestampAt18() int64 {
	now := time.Now()
	location, _ := time.LoadLocation("Asia/Tokyo")
	locationNow := now.In(location)
	// 设置时间为当天的18:00
	timeAt18 := time.Date(locationNow.Year(), locationNow.Month(), locationNow.Day(), 18, 0, 0, 0, location)
	timestamp := timeAt18.Unix()
	return timestamp
}

func GetCurrentTimestampStr() string {
	now := time.Now()
	location, _ := time.LoadLocation("Asia/Tokyo")
	locationNow := now.In(location)
	// 设置时间为当天的18:00
	timeAt18 := time.Date(locationNow.Year(), locationNow.Month(), locationNow.Day(), 0, 0, 0, 0, location)
	timestamp := timeAt18.Format("2006-01-02")
	return timestamp
}

func GetPreviousDay() string {
	// 获取当前日期
	now := time.Now()
	location, _ := time.LoadLocation("Asia/Tokyo")
	locationNow := now.In(location)
	currentTime := time.Date(locationNow.Year(), locationNow.Month(), locationNow.Day(), 0, 0, 0, 0, location)
	previousDay := currentTime.AddDate(0, 0, -1)
	// 格式化日期为字符串
	previousDayStr := previousDay.Format("2006-01-02")
	return previousDayStr
}

func GetNextNumDays(num uint32) []string {
	// 加载时区
	location, _ := time.LoadLocation("Asia/Tokyo")
	// 获取当前时间
	now := time.Now().In(location)
	result := make([]string, 0)
	// 获取当前时间及以后十天的时间
	for i := 0; i < int(num); i++ {
		nextDay := now.AddDate(0, 0, i)
		nextDayStr := nextDay.Format("2006-01-02")
		result = append(result, nextDayStr)
	}
	return result
}

// GetCurrentMinuteTimestamp 获取当前分钟的时间戳
func GetCurrentMinuteTimestamp() int64 {
	now := time.Now()
	// 将秒和纳秒部分设置为0，表示当前分钟的开始时间
	currentMinute := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, now.Location())
	return currentMinute.Unix()
}

func ValidateDateTimeFormat(dateTimeStr string) error {
	// 定义期望的时间格式
	const layout = "2006-01-02 15:04:05"

	// 解析日期时间字符串
	_, err := time.Parse(layout, dateTimeStr)
	if err != nil {
		return errors.New("invalid date time format, expected format is '2006-01-02 15:04:05'")
	}
	return nil
}

// ConvertToMinuteTimestamp 将 "2006-01-02 15:04:05" 格式的时间字符串转换为分钟级别的时间戳
func ConvertToMinuteTimestamp(dateTimeStr string) (int64, error) {
	// 定义期望的时间格式
	const layout = "2006-01-02 15:04:05"

	// 解析日期时间字符串
	parsedTime, err := time.Parse(layout, dateTimeStr)
	if err != nil {
		return 0, fmt.Errorf("invalid date time format: %v", err)
	}

	// 将秒和纳秒部分设置为0，表示当前分钟的开始时间
	minuteStart := time.Date(parsedTime.Year(), parsedTime.Month(), parsedTime.Day(), parsedTime.Hour(), parsedTime.Minute(), 0, 0, parsedTime.Location())

	// 返回分钟级别的 Unix 时间戳
	return minuteStart.Unix(), nil
}

// GetYesterdayEndTimestamp 根据传入的时区（-11 到 12）获取昨天 23:59:59 的时间戳
func GetYesterdayEndTimestamp(timezone int32) (int64, error) {
	if timezone < -11 || timezone > 12 {
		return 0, fmt.Errorf("invalid timezone: %d. Timezone must be between -11 and 12", timezone)
	}
	now := time.Now()
	// 创建指定时区的 Location
	location := time.FixedZone("Custom", int(timezone*3600))
	nowInLocation := now.In(location)
	// 计算昨天的日期
	yesterday := nowInLocation.AddDate(0, 0, -1)
	// 创建昨天 23:59:59 的时间
	yesterdayEnd := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 23, 59, 59, 0, location)

	return yesterdayEnd.Unix(), nil
}

// GenerateTimestamp 根据传入的小时数和时区生成对应时区时间的时间戳
func GenerateTimestamp(hour int, timezoneOffset int) (int64, error) {
	if hour < 0 || hour > 23 {
		return 0, fmt.Errorf("hour must be between 0 and 23")
	}
	now := time.Now()
	// 创建对应时区的时间
	location := time.FixedZone("Custom", timezoneOffset*3600)
	in := now.In(location)
	targetTime := time.Date(in.Year(), in.Month(), in.Day(), hour, 0, 0, 0, location)

	// 返回时间戳（秒级）
	return targetTime.Unix(), nil
}

// GenerateYesterdayTimestamp 根据传入的小时数和时区生成昨天对应时区时间的时间戳
func GenerateYesterdayTimestamp(hour int, timezoneOffset int) (int64, error) {
	if hour < 0 || hour > 23 {
		return 0, fmt.Errorf("hour must be between 0 and 23")
	}
	now := time.Now()
	// 创建对应时区的时间
	location := time.FixedZone("Custom", timezoneOffset*3600)
	in := now.In(location)
	// 生成昨天的时间
	yesterday := in.AddDate(0, 0, -1)
	targetTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), hour, 0, 0, 0, location)

	// 返回时间戳（秒级）
	return targetTime.Unix(), nil
}

// DaysDifference 以天为单位计算差值
func DaysDifference(timestamp int64) int {
	t := time.Unix(timestamp, 0)
	now := time.Now()
	duration := now.Sub(t)
	days := int(duration.Hours() / 24)

	return days
}

func GetTokyoZeroTimestamps(days int) ([]int64, error) {
	// 参数校验
	if days <= 0 {
		return nil, fmt.Errorf("invalid days: %d, must be greater than 0", days)
	}

	// 加载东京时区
	loc, err := time.LoadLocation("Asia/Tokyo")
	if err != nil {
		return nil, fmt.Errorf("time zone loading failure: %v", err)
	}
	// 获取当前东京时间
	now := time.Now().In(loc)
	// 计算当天零点时间
	startOfDay := time.Date(
		now.Year(),
		now.Month(),
		now.Day(),
		0, 0, 0, 0,
		loc,
	)

	// 生成时间戳列表
	timestamps := make([]int64, 0, days)
	for i := 0; i < days; i++ {
		// 计算第i天的零点时间
		t := startOfDay.AddDate(0, 0, i)
		// 转换为Unix时间戳
		timestamps = append(timestamps, t.Unix())
	}

	return timestamps, nil
}
