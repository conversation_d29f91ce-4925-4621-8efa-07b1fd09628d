package user

import (
	"context"
	"encoding/json"
	"fmt"
	"html"
	"strings"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go"
	"trpc.publishing_application.standalonesite/app/common"

	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/privacy"
	"trpc.publishing_application.standalonesite/app/pkg/metadatadecode"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"github.com/spf13/cast"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/model"
)

type UserImpl struct {
	pb.UnimplementedUser
}

func (u *UserImpl) SignPrivacyProtocol(c context.Context, req *pb.SignPrivacyProtocolReq) (*pb.SignPrivacyProtocolRsp, error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	if err := privacy.UpdateUserSignPrivacy(c, openid); err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.UpdateUserSignPrivacy err: %v", err)
		return &pb.SignPrivacyProtocolRsp{}, err
	}
	user.DeleteUserInfoCache(c, openid)
	return &pb.SignPrivacyProtocolRsp{}, nil
}

func (u *UserImpl) GetUserInfo(c context.Context, req *pb.GetUserInfoReq) (*pb.GetUserInfoRsp, error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId

	// openid := "2670566212109452541"

	userInfo, err := user.GetUserInfoWithAudit(c, openid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserInfo err: %v", err)
		return &pb.GetUserInfoRsp{}, err
	}
	return &pb.GetUserInfoRsp{
		Id:              userInfo.ID,
		IntlOpenid:      userInfo.IntlOpenid,
		IsFirstRegister: userInfo.IsFirstRegister,
		AuditUsername:   userInfo.AuditUsername,
		AuditRemark:     userInfo.AuditRemark,
		AuditAvatar:     userInfo.AuditAvatar,
		Username:        userInfo.Username,
		UsernameOn:      userInfo.UsernameOn,
		Remark:          userInfo.Remark,
		RemarkOn:        userInfo.RemarkOn,
		Avatar:          userInfo.Avatar,
		AvatarOn:        userInfo.AvatarOn,
		IsAuditUsername: userInfo.IsAuditUsername,
		IsAuditRemark:   userInfo.IsAuditRemark,
		IsAuditAvatar:   userInfo.IsAuditAvatar,
		HasSignPrivacy:  userInfo.HasSignPrivacy,
		Language:        userInfo.Language,
	}, nil
}

func (u *UserImpl) CheckUsername(c context.Context, req *pb.CheckUsernameReq) (*pb.CheckUsernameRsp, error) {
	if req.Username == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "username is required")
	}
	// 用户名检查 用户昵称不允许重复，全局唯一
	if err := user.ValidUsernameHasExisted(c, req.Username); err != nil {

		return &pb.CheckUsernameRsp{IsExist: true}, nil
	}
	return &pb.CheckUsernameRsp{IsExist: false}, nil
}

func (u *UserImpl) ModifyInfo(c context.Context, req *pb.ModifyInfoReq) (*pb.ModifyInfoRsp, error) {
	var userData *model.UserContent
	var err error

	language := metadata.GetLangType(c)
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid

	gameId, areaId := metadatadecode.GetGameIdAndAreaId(c)
	if gameId == "" || areaId == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param gameid or areaid Parameter error")
	}

	// openid := "29080-12945745392039390084"
	// gameId := "16"
	// areaId := "global"
	userData, err = user.GetUserInfoByOpenid(c, openid, false)
	if err != nil || userData == nil || userData.Model == nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("ModifyInfo GetUserInfoByOpenid err: %v\n", err)
		return nil, errs.NewCustomError(c, code.NoExistUsername, "ModifyInfo | User does not exist")
	}

	doc := map[string]interface{}{}
	var newAvatar, newName, newRemark string
	var setRemarkEmpty bool
	currentTime := time.Now().Unix()
	req.Username = strings.Trim(req.Username, " ")
	if len(req.Username) > 0 {
		// 校验昵称长度
		if utf8.RuneCountInString(req.Username) < 1 || utf8.RuneCountInString(req.Username) > 20 {
			return &pb.ModifyInfoRsp{}, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.NicknameLengthLimit, "Nickname length 1~20")
		}
		// match, _ := regexp.MatchString("^[a-zA-Z][a-zA-Z0-9_]{5,15}$", param.Username)
		// r, _ := regexp.Compile("^[a-zA-Z][a-zA-Z0-9_]{5,15}$")
		// match := r.MatchString(req.Username)
		// if !match {
		// 	return &pb.ModifyInfoRsp{}, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.MathchUserNameFailed, "Format Error (1. Start with a letter; 2. 6-16 characters; 3. Only contain letters, numbers, or underscores)")
		// }
		req.Username = html.EscapeString(req.Username)
	}
	req.Avatar = strings.Trim(req.Avatar, " ")
	if req.Avatar == "" {
		// 适配某些用户头像缺失，更新用户信息的时候报错
		avatarNew, err := user.CreateRandomAvatarNew(c)
		if err != nil {
			return &pb.ModifyInfoRsp{}, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.AvatarGetListFailed, "Get avatar list failed")
		}
		//response.ToErrorResponse(c, errcode.AvatarLintIsEmptyError)
		//return
		req.Avatar = avatarNew
	}

	isOfficialAuthUser, errCode := user.IsOfficialAuthUser(c, gameId, areaId, openid)
	if errCode != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.ChangeInfo err: %v\n", errCode)
		return &pb.ModifyInfoRsp{}, errCode
	}
	// 如果是官方认证、机构认证的用户则不需要审批
	needAudit := !isOfficialAuthUser

	if userData.Avatar != req.Avatar && req.Avatar != "" {
		if err := user.CheckUserAvatar(c, userData, req.Avatar); err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CheckUserAvatar err: %v\n", err)
			return &pb.ModifyInfoRsp{}, err
		}
		newAvatar = req.Avatar
		doc["avatar"] = req.Avatar
		doc["avatar_on"] = currentTime
	}
	if isOfficialAuthUser {
		// 认证用户多语言修改
		err = user.UpdateDBCertificationUserLanguagesToC(c, language, req.Username, req.Remark, userData.IntlOpenid)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.ModifyInfo UpdateDBCertificationUserLanguagesToC err: %v\n", err)
			return &pb.ModifyInfoRsp{}, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.ModifyUserInfoAuthUserError, "Modify user information failed")
		}
	} else {
		req.Remark = strings.Trim(req.Remark, " ")
		if userData.Remark != req.Remark {
			// XSS注入漏洞攻击防护 html.EscapeString这个函数它只会修改五个字符：<>&'"
			req.Remark = html.EscapeString(req.Remark)
			if req.Remark == "" {
				setRemarkEmpty = true
				// 如果清空备注信息，则直接通过，不需要审核
				doc["remark"] = ""
				doc["remark_on"] = currentTime
			} else {
				// userAdultStatus := metadatadecode.ParseHeaderCookie(c, "game_adult_status")
				// if userAdultStatus != "1" {
				// 	return &pb.ModifyInfoRsp{}, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.UserIsNotAdultFailed, "To protect our younger users, we are unable to offer you this function. We apologize for any inconvenience caused.")
				// }
				// 执行绑定 并且检查最近一个月是否修改过名称
				if _, err = user.CreateUserRemarkAudit(c, userData, req.Remark, needAudit, language); err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreateUserRemarkAudit err: %v\n", err)
					return &pb.ModifyInfoRsp{}, err
				}
				if !needAudit {
					//go user.PushUserInfoToSecurityDetection(c, req.Remark, userAudit, 101, userData.IntlOpenid, userData.Username)
					newRemark = req.Remark
					doc["remark"] = req.Remark
					doc["remark_on"] = currentTime
				}
			}
		}
		oldUsername := userData.Username
		if userData.Username != req.Username && req.Username != "" {
			// 用户名检查 用户昵称不允许重复，全局唯一
			if err := user.ValidUsernameHasExisted(c, req.Username); err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.ValidUsernameHasExisted err: %v\n", err)
				return &pb.ModifyInfoRsp{}, err
			}
			if _, err = user.CreateUserNameAudit(c, userData, req.Username, needAudit, language); err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CreateUserNameAudit err: %v\n", err)
				return &pb.ModifyInfoRsp{}, err
			}
			if !needAudit {
				//go user.PushUserInfoToSecurityDetection(c, req.Username, userAudit, 102, userData.IntlOpenid, userData.Username)
				//} else {
				newName = req.Username
				doc["username"] = req.Username
				if !(language == "en" || language == "ko" || language == "ja" || language == "zh" || language == "zh-TW") {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("ModifyUserInfo The language is not supported language: %v\n", language)
					return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidLanguageParams, "The language is not supported")
				}
				for _, langItem := range constants.AllPostLanguages {
					userNameKey := fmt.Sprintf("username_%s", language)
					if langItem == language {
						doc[userNameKey] = req.Username
						doc["username_on"] = currentTime
					} else {
						doc[userNameKey] = ""
						doc["username_on"] = currentTime
					}
				}
			}
		}

		if !needAudit {
			if oldUsername != "" {
				go func() {
					defer recovery.CatchGoroutinePanic(context.Background())
					redis.GetClient().SAdd(context.Background(), cache.GetUsernameCacheKey(), userData.Username)
					redis.GetClient().SRem(context.Background(), cache.GetUsernameCacheKey(), oldUsername)
				}()
			}
		}
	}
	// 执行绑定
	err = user.UpdateUserContentInfo(c, userData.IntlOpenid, newName, newRemark, newAvatar, setRemarkEmpty)
	if err != nil {
		go func(ctx context.Context, err2 error) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportUserModifyInfo(c, openid, newName, newRemark, newAvatar, err2)
		}(c, err)
	}
	go func(ctx context.Context) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportUserModifyInfo(c, openid, req.Username, req.Remark, req.Avatar, nil)
	}(c)
	// es更新用户信息
	if len(doc) > 0 {
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, userData.IntlOpenid, doc)
		dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	}
	// 更新缓存
	// user.GetUserInfoByOpenid(c, userData.IntlOpenid, true)
	user.DeleteUserInfoCache(c, userData.IntlOpenid)
	go cache.RemoveUserPostCacheKeys(openid)
	return &pb.ModifyInfoRsp{}, nil
}

func (u *UserImpl) GetUserProfile(c context.Context, req *pb.GetUserProfileReq) (*pb.GetUserProfileRsp, error) {
	var res = &pb.GetUserProfileRsp{}
	// 获取openid
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	openid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	// openid := "2670566212109452541"

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	if req.IntlOpenid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "user openid is required")
	}

	userData, err := user.GetUserDetailInfoByOpenid(c, openid, req.IntlOpenid, language)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserProfile GetUserDetailInfoByOpenid err: %v\n", err)

		return &pb.GetUserProfileRsp{}, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.GetUserProfileError, "An error occurred while obtaining user information")
	}
	res.Info = userData
	return res, nil
}

func (u *UserImpl) GetUserInfoNew(c context.Context, req *pb.GetUserInfoNewReq) (*pb.GetUserInfoNewRsp, error) {
	var res = &pb.GetUserInfoNewRsp{}
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	// openid := userAccount.IntlAccount.OpenId

	// var err error
	// openid := "29080-12945745392039390084"
	// gameId := "16"
	// areaId := "global"

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	res.Info, err = user.GetUserDetailInfoByOpenid(c, openid, openid, language)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfoNew GetUserDetailInfoByOpenid err: %v\n", err)
		return &pb.GetUserInfoNewRsp{}, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.GetUserInfoNewError, "User does not exist")
	}

	if language != "" {
		var tempUserLanguage = res.Info.Language
		go func(uLang string, currentLang string, intlOpenID string) {
			defer recovery.CatchGoroutinePanic(context.Background())
			if uLang != currentLang {
				lErr := user.SetUserLanguage(c, currentLang, intlOpenID)
				if lErr != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfoNew set user language err: %v\n", lErr)
				}
			}
		}(tempUserLanguage, language, openid)
		// 回包立马更新语言
		res.Info.Language = language
	}
	return res, nil
}

func (u *UserImpl) UserLoginInquiry(c context.Context, req *pb.UserLoginInquiryReq) (*pb.UserLoginInquiryRsp, error) {
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	_, err := user.GetUID(c, req, language)
	if err != nil {
		return nil, err
	}
	// 添加积分
	// go message.LIPUserFirstGetPoints(c, userInfo.IntlOpenid)
	return &pb.UserLoginInquiryRsp{}, nil
}

func (u *UserImpl) GetUserPrivacySetting(c context.Context, req *pb.GetUserPrivacySettingReq) (*pb.GetUserPrivacySettingRsp, error) {
	// 改成获取指定用户的隐私协议
	if req.IntlOpenid == "" {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParamsErr, "intl openid is required")
	}
	// openid := "29080-12945745392039390084"
	privacySwitch, err := privacy.GetUserPrivacySwitch(c, req.IntlOpenid)
	if err != nil {
		return nil, err
	}
	return &pb.GetUserPrivacySettingRsp{
		ShowMyPosts:       privacySwitch.ShowMyPosts,
		ShowMyCollection:  privacySwitch.ShowMyCollection,
		ShowMyFollow:      privacySwitch.ShowMyFollow,
		ShowMyFans:        privacySwitch.ShowMyFans,
		ShowMyGameCard:    privacySwitch.ShowMyGameCard,
		ReceiveTweetEmail: privacySwitch.ReceiveTweetEmail,
		MsgCommentNotify:  privacySwitch.MsgCommentNotify,
		MsgLikeNotify:     privacySwitch.MsgLikeNotify,
		MsgFollowNotify:   privacySwitch.MsgFollowNotify,
		MsgSystemNotify:   privacySwitch.MsgSystemNotify,
		MsgActivityNotify: privacySwitch.MsgActivityNotify,
		ShowMyComment:     privacySwitch.ShowMyComment,
	}, nil
}

func (u *UserImpl) UserPrivacySet(c context.Context, req *pb.UserPrivacySetReq) (*pb.UserPrivacySetRsp, error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	// openid := "29080-12945745392039390084"
	// var err error
	if req.IsOff != 0 && req.IsOff != 1 {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "User Privacy setting parameter error")
	}
	err = privacy.SetUserPrivacySwitch(c, openid, constants.PrivacySwitchType(req.Type), req.IsOff)
	if err != nil {
		return nil, err
	}
	return &pb.UserPrivacySetRsp{
		Status: 1,
	}, nil
}

func (u *UserImpl) InternalGetGameCardSetting(c context.Context, req *pb.InternalGetGameCardSettingReq) (*pb.InternalGetGameCardSettingRsp, error) {
	if req.IntlOpenid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "user openid is required")
	}
	privacySwitch, err := privacy.GetUserPrivacySwitch(c, req.IntlOpenid)
	if err != nil {
		return nil, err
	}
	return &pb.InternalGetGameCardSettingRsp{ShowGameCard: cast.ToBool(privacySwitch.ShowMyGameCard)}, nil
}

func (u *UserImpl) GetUserLinks(c context.Context, req *pb.GetUserLinksReq) (*pb.GetUserLinksRsp, error) {
	// 获取openid
	// userAccount, err := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	// openid := userAccount.Uid
	if req.IntlOpenid == "" {
		return nil, errs.NewCustomError(c, code.InvalidParams, "user openid is required")
	}
	return user.GetUserLinks(c, req.IntlOpenid)
}

func (u *UserImpl) SetUserLinks(c context.Context, req *pb.SetUserLinksReq) (*pb.SetUserLinksRsp, error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid

	err = user.SetUserLinks(c, openid, req)
	if err != nil {
		return nil, err
	}
	return &pb.SetUserLinksRsp{}, nil
}

func (u *UserImpl) SetUserMood(c context.Context, req *pb.SetUserMoodReq) (*pb.SetUserMoodRsp, error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid

	err = user.SetUserMoodInfo(c, openid, req)
	if err != nil {
		return nil, err
	}
	return &pb.SetUserMoodRsp{}, nil
}

func (u *UserImpl) GetUserAvatars(c context.Context, req *pb.GetUserAvatarsReq) (*pb.GetUserAvatarsRsp, error) {
	// 暂时不区分绑定游戏的头像，直接全量返回
	//intlGameId := metadatadecode.GetIntlGameId(c)
	//if intlGameId == "" {
	//	return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "x-common-param intlGameId Parameter error")
	//}
	intlGameId := ""
	return user.GetAvatarList(c, intlGameId)
}

func (u *UserImpl) GetUserGamePlayerInfo(c context.Context, req *pb.GetUserGamePlayerInfoReq) (*pb.GetUserGamePlayerInfoRsp, error) {
	rsp := &pb.GetUserGamePlayerInfoRsp{}
	var err error
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid

	if req.IntlOpenid == "" {
		req.IntlOpenid = myOpenid
	}
	// 如果是客态，则需要判断用户是否有关闭分享的开关设置，关闭则不返回数据
	if myOpenid != req.IntlOpenid {
		userPrivacySwitch, err := privacy.GetUserPrivacySwitch(c, req.IntlOpenid)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserGamePlayerInfo GetUserPrivacySwitch err: %v\n", err)
			return rsp, errs.NewCustomError(c, code.GetUserPrivacySwitchFailed, "Query user Nikke private information abnormality")
		}
		if userPrivacySwitch.ShowMyGameCard == 0 {
			// log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("Query user Nikke private information abnormality2")
			// return resp, errs.NewCustomError(c, code.GetUserDisableGameCardError, "Query user Nikke private information abnormality")
			return rsp, nil
		}
	}
	rsp, err = user.GetUserNikkeBasicInfo(c, req.IntlOpenid, req.Version)
	return rsp, err
}

func (u *UserImpl) SetUserGameTag(c context.Context, req *pb.SetUserGameTagReq) (*pb.SetUserGameTagRsp, error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid
	// myOpenid := "29080-12945745392039390084"
	if req.GameTag < 0 || req.GameTag > 20 {
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "Game tag parameter error")
	}
	setErr := user.SetUserGameTagInfo(c, myOpenid, req.GameTag)
	if setErr != nil {
		return nil, setErr
	}
	return &pb.SetUserGameTagRsp{}, nil
}

func (u *UserImpl) GetUserAvatarPendantList(c context.Context, req *pb.GetUserAvatarPendantListReq) (rsp *pb.GetUserAvatarPendantListRsp, err error) {
	rsp = &pb.GetUserAvatarPendantListRsp{}
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	myOpenid := userAccount.Uid
	// myOpenid := "29080-12945745392039390084"
	// userAvatarPendants, err := user.GetUserAvatarPendants(c, myOpenid, language)
	// rsp.UserAvatarPendants = userAvatarPendants
	rsp, err = user.GetUserAvatarPendantsV2(c, myOpenid, language, req.NextPageCursor, req.Limit, req.PreviousPageCursor)
	return rsp, err
}

func (u *UserImpl) SetUserAvatarPendant(c context.Context, req *pb.SetUserAvatarPendantReq) (rsp *pb.SetUserAvatarPendantRsp, err error) {
	rsp = &pb.SetUserAvatarPendantRsp{}
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid
	// myOpenid := "29080-12945745392039390084"
	err = user.SetUserAvatarPendants(c, myOpenid, req.AvatarPendantId, req.SetWearStatus)
	return rsp, err
}

// SetUserShiftyspadPrivacy 个人Shiftyspad隐私开关设置
func (u *UserImpl) SetUserShiftyspadPrivacy(c context.Context, req *pb.SetUserShiftyspadPrivacyReq) (rsp *pb.SetUserShiftyspadPrivacyRsp, err error) {
	rsp = &pb.SetUserShiftyspadPrivacyRsp{
		IsOk: false,
	}
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return
	}
	//if (req.ShowDailyInfo != 0 && req.ShowDailyInfo != 1) || (req.ShowNikkeInfo != 0 && req.ShowNikkeInfo != 1) || (req.ShowOutpostInfo != 0 && req.ShowOutpostInfo != 1) || (req.ShowResourceInfo != 0 && req.ShowResourceInfo != 1) {
	//	return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "User Privacy setting parameter error")
	//}
	err = user.SetUserShiftyspadPrivacy(c, userAccount.Uid, req)
	if err != nil {
		return
	}
	rsp.IsOk = true
	return
}

// GetUserShiftyspadPrivacy 获取个人Shiftyspad隐私开关
func (u *UserImpl) GetUserShiftyspadPrivacy(c context.Context, req *pb.GetUserShiftyspadPrivacyReq) (rsp *pb.GetUserShiftyspadPrivacyRsp, err error) {
	rsp = &pb.GetUserShiftyspadPrivacyRsp{}
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return
	}

	privacySwitch, err := privacy.GetUserPrivacySwitch(c, userAccount.Uid)
	if err != nil {
		return nil, err
	}

	if privacySwitch.ShiftyspadSwitchStr != "" {
		privacy := &model.ShiftyspadPrivacySwitch{}
		err = json.Unmarshal([]byte(privacySwitch.ShiftyspadSwitchStr), &privacy)
		if err != nil {
			return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.ParseShiftyspadSwitchError, "Parsing user shiftypad privacy configuration data anomaly")
		}
		rsp.ShowDailyInfo = pb.UserInfoVisibility(privacy.ShowDailyInfo)
		rsp.ShowOutpostInfo = pb.UserInfoVisibility(privacy.ShowOutpostInfo)
		rsp.ShowResourceInfo = pb.UserInfoVisibility(privacy.ShowResourceInfo)
		rsp.ShowNikkeInfo = pb.UserInfoVisibility(privacy.ShowNikkeInfo)
	}
	rsp.ShowMyGameCard = pb.GameCardVisibility(privacySwitch.ShowMyGameCard)
	return
}

// SearchUser 根据用户昵称搜索用户信息
func (u *UserImpl) SearchUser(c context.Context, req *pb.SearchUserReq) (rsp *pb.SearchUserRsp, err error) {
	rsp = &pb.SearchUserRsp{}
	// 默认写死分页类型和分页数
	req.PageType = 0
	if req.Limit < 1 || req.Limit > 50 {
		req.Limit = 10
	}

	// 获取openid
	var myOpenid string
	userAccount, err := metadata.GetUserAccount(c)
	if err == nil {
		myOpenid = userAccount.Uid
	}
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	userList, err := user.SearchUserInfoList(c, req, myOpenid, language)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.SearchUser err: %v\n", err)
		return nil, err
	}
	return userList, nil
}

// 设置用户评论气泡
func (u *UserImpl) SetUserCommentBubble(c context.Context, req *pb.SetUserCommentBubbleReq) (rsp *pb.SetUserCommentBubbleRsp, err error) {
	rsp = &pb.SetUserCommentBubbleRsp{}
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	myOpenid := userAccount.Uid
	// myOpenid := "29080-12945745392039390084"
	err = user.SetUserCommentBubbles(c, myOpenid, req.CommentBubbleId, req.SetWearStatus)
	return rsp, err
}

func (u *UserImpl) GetUserCommentBubbleList(c context.Context, req *pb.GetUserCommentBubbleListReq) (rsp *pb.GetUserCommentBubbleListRsp, err error) {
	rsp = &pb.GetUserCommentBubbleListRsp{}
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	myOpenid := userAccount.Uid
	// myOpenid := "29080-12945745392039390084"
	// language := "en"
	rsp, err = user.GetUserCommentBubblesV2(c, myOpenid, language, req.NextPageCursor, req.Limit, req.PreviousPageCursor)
	return rsp, err
}

// func (u *UserImpl) GetIntlGameUserStatus(c context.Context, req *pb.GetIntlGameUserStatusReq) (rsp *pb.GetIntlGameUserStatusRsp, err error) {
// 	rsp = &pb.GetIntlGameUserStatusRsp{}
// 	// 获取openid
// 	userAccount, err := metadata.GetUserAccount(c)
// 	if err != nil {
// 		return nil, err
// 	}
// 	myOpenid := userAccount.Uid
// 	// myOpenid := "29080-12945745392039390084"
// 	rsp, err = user.GetUserMinorcerStatus(c, myOpenid)
// 	return rsp, err
// }

func (u *UserImpl) QueryGuildUserInfos(c context.Context, req *pb.QueryGuildUserInfosReq) (rsp *pb.QueryGuildUserInfosRsp, err error) {
	return user.GetUserAvatarsByIntlOpenids(c, req.IntlOpenids)
}
