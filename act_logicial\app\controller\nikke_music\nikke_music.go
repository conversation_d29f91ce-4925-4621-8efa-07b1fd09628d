// Package nikkemusic nikke音乐节
package nikkemusic

import (
	"context"

	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_music"
	nikkemusic "trpc.act.logicial/app/logic/nikke_music"
)

// NikkeMusicImpl  nikke 音乐节结构体
type NikkeMusicImpl struct {
	pb.UnimplementedNikkeMusic
}

// NikkeMusicAddLog mbti添加发奖记录
func (s *NikkeMusicImpl) NikkeMusicAddLog(
	ctx context.Context,
	req *pb.NikkeMusicAddLogReq,
) (*pb.NikkeMusicAddLogRsp, error) {
	rsp := &pb.NikkeMusicAddLogRsp{}

	err := nikkemusic.NikkeMusicAddLog(ctx, req)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

// ScheduledSendMusic mbti发奖
func (s *NikkeMusicImpl) ScheduledSendMusic(
	ctx context.Context,
	req *pb.ScheduledSendMusicReq,
) (rsp *pb.ScheduledSendMusicRsp, err error) {
	rsp = &pb.ScheduledSendMusicRsp{}

	go nikkemusic.ScheduledSendMusic(ctx)

	return
}
