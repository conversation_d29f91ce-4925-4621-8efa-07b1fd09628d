package cag_tmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_cag_tmp"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/constant"
	"trpc.act.logicial/app/logic/cag_common"
)

// GetCreateRolePrizeStatusProc
type GetCreateRolePrizeStatusProc struct {
	userInfo *accountPb.UserAccount
}

// GetCreateRolePrizeStatus 领取创建角色奖励
func GetCreateRolePrizeStatus(ctx context.Context, req *pb.GetCreateRolePrizeStatusReq) (*pb.GetCreateRolePrizeStatusRsp, error) {
	log.DebugContextf(ctx, "GetCreateRolePrizeStatus enter, req: %v", req)
	rsp := &pb.GetCreateRolePrizeStatusRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "GetCreateRolePrizeStatus get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	proc := &GetCreateRolePrizeStatusProc{
		userInfo: &userAccount,
	}
	defer func() {
		if nil != err {
			log.ErrorContextf(ctx, "GetCreateRolePrizeStatus rsp error:%v", err)
		}
	}()
	err = proc.Process(ctx, rsp)
	if nil != err {
		return nil, err
	}
	return rsp, nil
}

func (p *GetCreateRolePrizeStatusProc) Process(ctx context.Context,
	rsp *pb.GetCreateRolePrizeStatusRsp) error {
	// 查询奖励记录
	res, err := cag_common.GetCagNewRolePrizeRecord(ctx, p.userInfo.Uid, constant.CAG_NEW_ROLE_PRIZE_TYPE_ROLE, 0, 10)
	if err != nil {
		log.ErrorContextf(ctx, "GetCreateRolePrizeStatus getCagNewRolePrizeRecord failed, err:%v, userId: %v",
			err, p.userInfo.Uid)
		return code.ErrSystemError
	}
	for _, v := range res {
		rsp.Prizes = append(rsp.Prizes, &pb.PrizeInfo{
			Type:   cast.ToString(constant.CAG_NEW_ROLE_PRIZE_TYPE_ROLE),
			Level:  uint32(v.Level),
			HasGet: true,
		})
	}
	if len(res) == 0 { // 没有数据
		rsp.Prizes = append(rsp.Prizes, &pb.PrizeInfo{
			Type:   cast.ToString(constant.CAG_NEW_ROLE_PRIZE_TYPE_ROLE),
			HasGet: false,
		})
	}
	return nil
}
