package dao

import (
	"errors"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

var (
	mediaContentType = []constants.PostContentT{
		constants.CONTENT_TYPE_IMAGE,
		constants.CONTENT_TYPE_VIDEO,
		constants.CONTENT_TYPE_AUDIO,
		constants.CONTENT_TYPE_ATTACHMENT,
		constants.CONTENT_TYPE_CHARGE_ATTACHMENT,
	}
)

func PostContentDeleteByPostId(postId int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostContent{}).TableName()).Where("post_uuid = ?", postId).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func MediaContentsByPostId(postId int64) (contents []string, err error) {
	err = DB.SelectConnect("db_standalonesite").Table((&model.PostContent{}).TableName()).Where("post_uuid = ? AND type IN ?", postId, mediaContentType).Select("content").Find(&contents).Error
	return
}

func PostContentCreate(content *model.PostContent) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.PostContent{}).TableName()).Create(&content).Error
}

// 默认获取最新的那条数据
func PostContentGet(postUUID string, language string) (*model.PostContent, error) {
	var content model.PostContent
	if postUUID == "" {
		return nil, gorm.ErrRecordNotFound

	}
	tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUID)
	if err != nil {
		return nil, err
	}
	db := DB.SelectConnect("db_standalonesite").Table(tableName)
	if language != "" {
		db.Where("language = ?", language)
	}
	err = db.Where("post_uuid = ? AND is_del = ?", postUUID, 0).Order("`order` asc").First(&content).Error

	if err != nil {
		return &content, err
	}

	return &content, nil
}

// 获取全量的数据
func GetPostContentList(postUUID string, isIgnoreDelete bool) ([]*model.PostContent, error) {
	var content []*model.PostContent
	if postUUID == "" {
		return nil, gorm.ErrRecordNotFound

	}
	tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUID)
	if err != nil {
		return nil, err
	}
	db := DB.SelectConnect("db_standalonesite").Table(tableName)

	if isIgnoreDelete {
		db = db.Unscoped()
	}

	err = db.Where("post_uuid = ?", postUUID).Order("`order` asc").Find(&content).Error

	if err != nil {
		return content, err
	}

	return content, nil
}

func PostContentGetIgnoreDelete(postUUID string) (*model.PostContent, error) {
	var content model.PostContent
	if postUUID == "" {
		return nil, gorm.ErrRecordNotFound

	}
	tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUID)
	if err != nil {
		return nil, err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Unscoped().Where("post_uuid = ? ", postUUID).First(&content).Error

	if err != nil {
		return &content, err
	}

	return &content, nil
}

func PostContentGetIgnoreDeleteWithLanguage(postUUID, language string) (*model.PostContent, error) {
	var content model.PostContent
	if postUUID == "" {
		return nil, gorm.ErrRecordNotFound

	}
	tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUID)
	if err != nil {
		return nil, err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Unscoped().Where("post_uuid = ? ", postUUID).Where("language = ? ", language).First(&content).Error

	if err != nil {
		return &content, err
	}

	return &content, nil
}

func PostContentGetIgnoreDeleteV2(postUUID string) ([]*model.PostContentTemp, error) {
	var content []*model.PostContentTemp
	if postUUID == "" {
		return nil, gorm.ErrRecordNotFound

	}
	tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUID)
	if err != nil {
		return nil, err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Unscoped().Where("post_uuid = ? ", postUUID).Find(&content).Error

	if err != nil {
		return content, err
	}

	return content, nil
}

// CreatePostContent 创建动态内容
func CreatePostContent(content *model.PostContent) error {
	tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(content.PostUUID)
	if err != nil {
		return err
	}
	return DB.SelectConnect("db_standalonesite").Table(tableName).Create(&content).Error
}

// 更新动态内容表
func UpdatePostContent(content *model.PostContent) error {
	tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(content.PostUUID)
	if err != nil {
		return err
	}
	return DB.SelectConnect("db_standalonesite").Table(tableName).Updates(&content).Error
}

// UpdatePostContentInfoByLanguage 更新post_content
func UpdatePostContentInfoByLanguage(postUUID, language string, updateData map[string]interface{}) error {
	if postUUID == "" {
		return gorm.ErrRecordNotFound
	}
	tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUID)
	if err != nil {
		return err
	}

	return DB.SelectConnect("db_standalonesite").Table(tableName).Where("post_uuid = ? and is_del = 0", postUUID).Where("language = ?", language).Updates(updateData).Error
}

// UpdatePostContentInfo 更新post_content
func UpdatePostContentInfo(postUUID string, updateData map[string]interface{}) error {
	if postUUID == "" {
		return gorm.ErrRecordNotFound
	}
	tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUID)
	if err != nil {
		return err
	}

	return DB.SelectConnect("db_standalonesite").Table(tableName).Where("post_uuid = ? and is_del = 0", postUUID).Updates(updateData).Error
}

// UpdatePostContentInfo 更新post_content
func UpdatePostContentInfoIgnoreDel(postUUID string, updateData map[string]interface{}) error {
	if postUUID == "" {
		return gorm.ErrRecordNotFound
	}
	tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUID)
	if err != nil {
		return err
	}

	return DB.SelectConnect("db_standalonesite").Table(tableName).Unscoped().Where("post_uuid = ?", postUUID).Updates(updateData).Error
}

// 批量根据帖子id获取帖子内容
func GetPostContentByPostOpenids(postOpenids []string) ([]*model.PostContent, error) {
	var postContents []*model.PostContent
	var tables = make(map[string][]string)
	for _, openid := range postOpenids {
		table, err := (&model.PostContent{}).GetPostContentSubMeterTable(openid)
		if err != nil {
			return nil, err
		}
		tables[table] = append(tables[table], openid)
	}
	for table, postUuids := range tables {
		var postContentItems []*model.PostContent
		err := DB.SelectConnect("db_standalonesite").Table(table).Where("post_uuid in ?", postUuids).Find(&postContentItems).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			return nil, err
		}
		postContents = append(postContents, postContentItems...)
	}
	return postContents, nil
}

// 新语言存在，更新新内容，删除原语言，新语言不存在，直接修改语言字段
func ChangePostContentLanguage(postUUid string, language string, newLanguage string) error {
	if postUUid == "" || language == "" || newLanguage == "" {
		return nil
	}
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		table, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUid)
		if err != nil {
			return err
		}
		oldPostContent := &model.PostContent{}
		err = DB.SelectConnect("db_standalonesite").Table(table).Where("post_uuid = ? and language = ? and is_del = 0", postUUid, language).First(&oldPostContent).Error
		if err != nil {
			return err
		}
		newPostContent := &model.PostContent{}
		err = DB.SelectConnect("db_standalonesite").Table(table).Where("post_uuid = ? and language = ? and is_del = 0", postUUid, newLanguage).First(&newPostContent).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		} else if newPostContent != nil && newPostContent.Model != nil && newPostContent.ID > 0 {
			oldPostContent.Language = newLanguage
			oldPostContent.ID = newPostContent.ID
			// 新的多语言内容存在，更新
			err := DB.SelectConnect("db_standalonesite").Table(table).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "post_uuid"}, {Name: "language"}, {Name: "is_del"}},
				DoUpdates: clause.AssignmentColumns(util.GetUpdateColumns(&model.PostContentTemp{}, "id", "post_uuid")),
			}).Create(oldPostContent).Error
			if err != nil {
				return err
			}
			// 删除原来的数据
			err = DB.SelectConnect("db_standalonesite").Table(table).Where("post_uuid = ? and language = ? and is_del = 0", postUUid, language).Update("is_del", 1).Error
			return err
		} else {
			// 直接修改原来旧的数据多语言
			oldPostContent.Language = newLanguage
			DB.SelectConnect("db_standalonesite").Table(table).Where("id = ?", oldPostContent.ID).Save(oldPostContent)
		}
		return nil
	})
}

func BatchGetPostContentsWithLanguage(postUuids []string) ([]*model.PostContent, error) {
	var postContents []*model.PostContent
	var tables = make(map[string][]string)
	for _, openid := range postUuids {
		table, err := (&model.PostContent{}).GetPostContentSubMeterTable(openid)
		if err != nil {
			return nil, err
		}
		tables[table] = append(tables[table], openid)
	}
	for table, postUuids := range tables {
		var postContentItems []*model.PostContent
		err := DB.SelectConnect("db_standalonesite").Table(table).Where("post_uuid in ?", postUuids).Find(&postContentItems).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			return nil, err
		}
		postContents = append(postContents, postContentItems...)
	}
	return postContents, nil
}

func UpdatePostStatement(postUUid string, creatorStatementType constants.ECreatorStatementType, riskRemindType constants.ERiskRemindType, aiContentType constants.EAiContentType, originalUrl string) error {
	table, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(table).Where("post_uuid = ? and is_del = 0", postUUid).Updates(map[string]interface{}{
		"creator_statement_type": int32(creatorStatementType),
		"risk_remind_type":       int32(riskRemindType),
		"ai_content_type":        int32(aiContentType),
		"original_url":           originalUrl,
	}).Error
	return err
}
