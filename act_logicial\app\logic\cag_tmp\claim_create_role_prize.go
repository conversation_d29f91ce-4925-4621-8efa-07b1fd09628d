package cag_tmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_cag_tmp"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/constant"
	"trpc.act.logicial/app/logic/cag_common"
	"trpc.act.logicial/app/redis"
	"trpc.act.logicial/app/service"
	"trpc.act.logicial/app/viewmodel"
)

// ClaimCreateRolePrizeProc
type ClaimCreateRolePrizeProc struct {
	userInfo *accountPb.UserAccount
	langType string
}

// ClaimCreateRolePrize 领取创建角色奖励
func ClaimCreateRolePrize(ctx context.Context, req *pb.ClaimCreateRolePrizeReq) (*pb.ClaimCreateRolePrizeRsp, error) {
	log.DebugContextf(ctx, "ClaimCreateRolePrize enter, req: %v", req)
	rsp := &pb.ClaimCreateRolePrizeRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ClaimCreateRolePrize get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	proc := &ClaimCreateRolePrizeProc{
		userInfo: &userAccount,
	}
	// 语言
	proc.langType = metadata.GetLangType(ctx)
	defer func() {
		if nil != err {
			log.ErrorContextf(ctx, "ClaimCreateRolePrize rsp error:%v", err)
		}
	}()
	err = proc.Process(ctx, rsp)
	if nil != err {
		return nil, err
	}
	return rsp, nil
}

func (p *ClaimCreateRolePrizeProc) Process(ctx context.Context,
	rsp *pb.ClaimCreateRolePrizeRsp) error {
	// 检查活动是时间
	err := cag_common.CheckNewRoleActTime(ctx)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimCreateRolePrize check act time error:%v, userId: %v", err, p.userInfo.Uid)
		return err
	}
	// 加锁，频率控制以及避免并发
	key := redis.GetCagCreateRolePrizeLockKey(p.userInfo.Uid)
	ok := redis.LockByKey(ctx, key, 10)
	if !ok {
		log.ErrorContextf(ctx, "ClaimCreateRolePrize LockByKey fail, key: %v", key)
		return code.ErrRequestFrequencyExceededLimitError
	}

	// 判断是否已经领取过奖励
	err = cag_common.HasCagNewRolePrize(ctx, p.userInfo.Uid, constant.CAG_NEW_ROLE_PRIZE_TYPE_ROLE, 0)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimCreateRolePrize hasCagNewRolePrize error:%v", err)
		return err
	}

	// 检查ab等级是否达到领奖条件
	err = checkAbRoleLevel(ctx, p.userInfo.Uid, 5)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimCreateRolePrize checkAbRoleLevel failed, err:%v, userId: %v", err, p.userInfo.Uid)
		return err
	}

	// 查询用户的cag游戏用户id
	cagOpenId, err := p.GetCagOpenId(ctx)
	if nil != err {
		log.ErrorContextf(ctx, "GetCagOpenId error:%v, userId: %v", err, p.userInfo.Uid)
		return err
	}

	status := constant.CagRoleLevelPrizeStatusWaitSend
	if "" == cagOpenId { // 角色未创建，等待创建后再发奖
		status = constant.CagRoleLevelPrizeStatusWaitCreateRole
	}

	// 领取奖励
	err = cag_common.ClaimNewRolePrize(ctx, p.userInfo.Uid, p.userInfo.IntlAccount.OpenId,
		p.langType, cagOpenId, constant.CAG_NEW_ROLE_PRIZE_TYPE_ROLE, 0, status)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimCreateRolePrize claimNewRolePrize error:%v, userId: %v, cagOpenId: %v",
			err, p.userInfo.Uid, cagOpenId)
		return err
	}

	// 回包
	rsp.Prizes = []*pb.PrizeInfo{
		{
			Type:   cast.ToString(constant.CAG_NEW_ROLE_PRIZE_TYPE_ROLE),
			HasGet: true,
		},
	}
	return nil
}

// GetCagOpenId 发放奖励获取cag游戏openid
func (p *ClaimCreateRolePrizeProc) GetCagOpenId(ctx context.Context) (cagOpenId string, err error) {
	// 查询用户的cag游戏用户id
	cagOpenId, err = service.GetGameOpenId(ctx, constant.GAMEID_CAG)
	if nil != err {
		log.ErrorContextf(ctx, "GetCagOpenId error:%v, userId: %v", err, p.userInfo.Uid)
		return "", err
	}
	if "" == cagOpenId {
		log.DebugContextf(ctx, "GetCagOpenId cag openid is empty, userId: %v", p.userInfo.Uid)
		// 角色未创建
		return "", nil
	}

	// 检查cag游戏用户创建角色时间
	areaId := ""
	id, ok := config.GetConfig().GameAreaIds[constant.GAMEID_CAG]
	if ok {
		areaId = cast.ToString(id)
	}

	gameInfo := &viewmodel.CagUserGameInfoItem{}
	err = service.GetUserGameInfoByOpenid(ctx, constant.GAMEID_CAG, constant.IDIP_CMD_CAG_GET_USER_INFO, areaId, cagOpenId, gameInfo)
	if code.ErrHasNoGameRole == err {
		log.DebugContextf(ctx, "GetCagOpenId cag openid has no game role, userId: %v, cagOpenId: %v",
			p.userInfo.Uid, cagOpenId)
		return "", nil
	} else if nil != err {
		log.ErrorContextf(ctx, "GetCagOpenId get cag game user info error:%v, userId: %v, cagOpenId: %v",
			err, p.userInfo.Uid, cagOpenId)
		return "", err
	}
	log.DebugContextf(ctx, "GetCagOpenId cag game user info: %+v, userId: %v", gameInfo, p.userInfo.Uid)

	// 判断创建角色时间是否在活动开始之后
	err = checkCagRoleRegTime(ctx, gameInfo, p.userInfo.Uid, cagOpenId)
	if nil != err {
		return "", err
	}
	return cagOpenId, nil
}
