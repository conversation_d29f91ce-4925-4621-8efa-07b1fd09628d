// Package userdata TODO
package userdata

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/datadump"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	addressPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_address"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_userdata"
	"google.golang.org/protobuf/proto"
	"trpc.act.logicial/app/model/address"
)

var (
	// PresentTableName TODO
	PresentTableName = "presentsend_account_present_openid"
	// PresentV2TableName TODO
	PresentV2TableName = "presentsendv2_account_present_openid"
	// AddressTableName TODO
	AddressTableName = "address_account_log_openid"
	addressProxy     = addressPb.NewAddressClientProxy()
	gameProxy        = gamePb.NewGameClientProxy()
)

// GetUserAddressInfo TODO
func GetUserAddressInfo(ctx context.Context, req *pb.GetUserAddressInfoReq) (err error) {
	tableNameList := GetHandradTableName(AddressTableName)
	var allAddressList []address.AddressAccountLog

	for _, tableName := range tableNameList {
		var addressList []address.AddressAccountLog
		DB.SelectConnect("logicial").Table(tableName).Debug().Where("`Fsource_id` = ?", req.FsourceId).Find(&addressList)
		allAddressList = append(allAddressList, addressList...)
	}

	data := make([][]string, 0)
	title := []string{"gameid", "openid", "name", "phone", "email", "zip_code", "address"}
	data = append(data, title)
	for _, item := range allAddressList {
		userAccount := accountPb.UserAccount{
			Uid:         item.UID,
			AccountType: accountPb.AccountType(item.AccountType),
			IntlAccount: &accountPb.IntlAccount{},
		}
		uidList := strings.Split(item.UID, "-")
		oneData := make([]string, 6)
		if len(uidList) == 2 {
			oneData[0] = uidList[0]
			oneData[1] = uidList[1]
		} else {
			oneData[0] = ""
			oneData[1] = uidList[1]
		}
		// metadata.SetUserAccount(ctx, userAccount)
		accountData, _ := proto.Marshal(&userAccount)
		callopts := []client.Option{
			client.WithMetaData(metadata.UserAccount, accountData),
		}
		rsp, errA := addressProxy.GetAccountAddressInfo(ctx, &addressPb.GetAccountAddressInfoReq{FsourceId: req.FsourceId},
			callopts...)
		if errA == nil {
			oneData[2] = rsp.Name
			oneData[3] = rsp.PhoneNumber
			oneData[4] = rsp.Email
			oneData[5] = rsp.ZipCode
			oneData[6] = rsp.Address
		}
		data = append(data, oneData)
	}

	err = SendWxBot(ctx, req.FileName, data, req.QywxChatid, req.QywxBotid)
	return
}

// GetUserPresentInfo TODO
func GetUserPresentInfo(ctx context.Context, req *pb.GetUserPresentInfoReq) (err error) {
	tableNameList := GetHandradTableName(PresentTableName)
	levelList := []int32{}
	presentConfigMap := make(map[int32]string)
	for _, item := range req.PresentConfig {
		levelList = append(levelList, item.Level)
		presentConfigMap[item.Level] = item.ShowName
	}
	type UserPresent struct {
		Uid         string
		AccountType int32
		Level       int32
		PresentName string
	}
	userList := make([]UserPresent, 0)
	for _, item := range tableNameList {
		oneUserList := make([]UserPresent, 0)
		// 查询中奖名单
		DB.SelectConnect("present").Table(item).Debug().Where("`Fsource_id` = ? and `level` in (?)", req.FsourceId,
			levelList).Find(&oneUserList)
		userList = append(userList, oneUserList...)
	}
	data := make([][]string, 0)
	title := []string{"gameid", "openid", "present"}

	if req.WithRole {
		title = append(title, []string{"area_id", "zone_id", "role_id"}...)
	}
	if req.WithAddress {
		title = append(title, []string{"name", "phone", "email", "zip_code", "address"}...)
	}
	data = append(data, title)
	for _, item := range userList {
		userAccount := accountPb.UserAccount{
			Uid:         item.Uid,
			AccountType: accountPb.AccountType(item.AccountType),
			IntlAccount: &accountPb.IntlAccount{},
		}
		uidList := strings.Split(item.Uid, "-")
		oneData := make([]string, 3)
		if len(uidList) == 2 {
			oneData[0] = uidList[0]
			oneData[1] = uidList[1]
		} else {
			oneData[0] = ""
			oneData[1] = uidList[1]
		}
		oneData[2] = presentConfigMap[item.Level]
		// metadata.SetUserAccount(ctx, userAccount)
		accountData, _ := proto.Marshal(&userAccount)
		callopts := []client.Option{
			client.WithMetaData(metadata.UserAccount, accountData),
		}
		if req.WithRole {
			rsp, errA := gameProxy.GetSavedRoleInfo(ctx, &gamePb.GetSavedRoleInfoReq{
				FsourceId: req.FsourceId,
			}, callopts...)
			if errA == nil {
				oneData = append(oneData, fmt.Sprintf("%d", rsp.AreaId), fmt.Sprintf("%d", rsp.ZoneId), rsp.RoleId)
			} else {
				oneData = append(oneData, "", "", "")
			}
		}
		if req.WithAddress {
			userAddress, errA := addressProxy.GetAccountAddressInfo(ctx, &addressPb.GetAccountAddressInfoReq{
				FsourceId: req.FsourceId,
			}, callopts...)
			if errA == nil {
				oneData = append(oneData, userAddress.Name, userAddress.PhoneNumber, userAddress.Email, userAddress.ZipCode,
					userAddress.Address)
			} else {
				oneData = append(oneData, "", "", "", "", "")
			}
		}
		data = append(data, oneData)

	}
	err = SendWxBot(ctx, req.FileName, data, req.QywxChatid, req.QywxBotid)

	return
}

// ByDate TODO
type ByDate [][]string

// Len 用于排序
func (a ByDate) Len() int {
	return len(a)
}

// Less 用于排序
func (a ByDate) Less(i, j int) bool {
	// 解析第三个字符串为时间
	fmt.Println("---------------a[i][3]---------------")
	fmt.Printf("%#v\n", a[i][3])
	fmt.Printf("%#v\n", a[j][3])
	timeI, errI := time.Parse("2006-01-02 15:04:05", a[i][3])
	timeJ, errJ := time.Parse("2006-01-02 15:04:05", a[j][3])
	fmt.Printf("%#v\n", timeI)
	fmt.Printf("%#v\n", timeJ)
	// 如果解析失败，可以根据需要处理错误
	if errI != nil || errJ != nil {
		return false // 或者根据需要处理错误
	}

	// 比较时间
	return timeI.Before(timeJ)
}

// Swap 用于排序
func (a ByDate) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}

// GetUserPresentV2Info TODO
// v2版本礼包记录
func GetUserPresentV2Info(ctx context.Context, req *pb.GetUserPresentInfoReq) (err error) {
	tableNameList := GetHandradTableName(PresentV2TableName)
	levelList := []int32{}
	presentConfigMap := make(map[int32]string)
	for _, item := range req.PresentConfig {
		levelList = append(levelList, item.Level)
		presentConfigMap[item.Level] = item.ShowName
	}
	type UserPresentV2 struct {
		Uid          string
		AccountType  int32
		LotteryIndex int32
		PresentName  string
		CreatedAt    int64 `gorm:"column:created_at;autoCreateTime" json:"created_at,omitempty"`
	}
	userList := make([]UserPresentV2, 0)
	for _, item := range tableNameList {
		oneUserList := make([]UserPresentV2, 0)
		// 查询中奖名单
		DB.SelectConnect("present").Table(item).Debug().Where("`Fsource_id` = ? and `lottery_index` in (?)", req.FsourceId,
			levelList).Find(&oneUserList)
		userList = append(userList, oneUserList...)
	}
	data := make([][]string, 0)
	title := []string{"gameid", "openid", "present", "time(utc+0)"}

	if req.WithRole {
		title = append(title, []string{"area_id", "zone_id", "role_id", "role_name"}...)
	}
	if req.WithAddress {
		title = append(title, []string{"name", "phone", "email", "zip_code", "address"}...)
	}
	// data = append(data, title)
	for _, item := range userList {
		userAccount := accountPb.UserAccount{
			Uid:         item.Uid,
			AccountType: accountPb.AccountType(item.AccountType),
			IntlAccount: &accountPb.IntlAccount{},
		}
		uidList := strings.Split(item.Uid, "-")
		oneData := make([]string, 4)
		if len(uidList) == 2 {
			oneData[0] = uidList[0]
			oneData[1] = uidList[1]
		} else {
			oneData[0] = ""
			oneData[1] = uidList[1]
		}
		oneData[2] = presentConfigMap[item.LotteryIndex]
		oneData[3] = time.Unix(item.CreatedAt, 0).Format("2006-01-02 15:04:05")
		// metadata.SetUserAccount(ctx, userAccount)
		accountData, _ := proto.Marshal(&userAccount)
		callopts := []client.Option{
			client.WithMetaData(metadata.UserAccount, accountData),
		}
		if req.WithRole {
			rsp, errA := gameProxy.GetSavedRoleInfo(ctx, &gamePb.GetSavedRoleInfoReq{
				FsourceId: req.FsourceId,
			}, callopts...)
			if errA == nil {
				oneData = append(oneData, fmt.Sprintf("%d", rsp.AreaId), fmt.Sprintf("%d", rsp.ZoneId), rsp.RoleId, rsp.RoleName)
			} else {
				oneData = append(oneData, "", "", "")
			}
		}
		if req.WithAddress {
			userAddress, errA := addressProxy.GetAccountAddressInfo(ctx, &addressPb.GetAccountAddressInfoReq{
				FsourceId: req.FsourceId,
			}, callopts...)
			if errA == nil {
				oneData = append(oneData, userAddress.Name, userAddress.PhoneNumber, userAddress.Email, userAddress.ZipCode,
					userAddress.Address)
			} else {
				oneData = append(oneData, "", "", "", "", "")
			}
		}
		data = append(data, oneData)
	}

	// data 按时间排序
	sort.Sort(ByDate(data))

	sortData := make([][]string, 0)
	sortData = append(sortData, title)
	sortData = append(sortData, data...)
	err = SendWxBot(ctx, req.FileName, sortData, req.QywxChatid, req.QywxBotid)

	return
}

// SendWxBot TODO
func SendWxBot(ctx context.Context, fileName string, sendData [][]string, chatId string, botId string) (err error) {
	dateStr := time.Now().Format("**************")
	excelFileName := fmt.Sprintf("%s_%s.xlsx", fileName, dateStr)
	filePath, err := datadump.CreateExcel(ctx, fileName, sendData)
	if err != nil {
		return err
	}
	mediaId, err := datadump.QWXUploadMedia(ctx, filePath, botId, excelFileName)
	if err != nil {
		return
	}
	option := &datadump.SendOption{
		Msgtype: "file",
		Chatid:  chatId,
		File: &datadump.FileType{
			MediaId: mediaId,
		},
	}
	res, err := datadump.QWXSend(ctx, option, botId)
	log.WithFieldsContext(ctx, "log_type", "SendWxBot").Infof(
		"res=%v,err=%v", res, err)
	return
}

// GetHandradTableName TODO
func GetHandradTableName(tableName string) (tableNameList []string) {
	for i := 0; i < 100; i++ {
		// i 要2为数，补齐 例 01 - 99

		tableNameList = append(tableNameList, tableName+fmt.Sprintf("_%02d", i))

	}
	return
}
