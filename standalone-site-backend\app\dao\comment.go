package dao

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"

	"trpc.publishing_application.standalonesite/app/constants"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

func CommentGet(id int64, postUUID string, intlOpenID string, replyId int64, isAudit int) (*model.Comment, error) {
	var comment model.Comment

	db := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}

	if postUUID != "" {
		db = db.Where("post_uuid = ?", postUUID)
	}
	if intlOpenID != "" {
		db = db.Where("intl_openid =  ?", intlOpenID)
	}
	if replyId > 0 {
		db = db.Where("reply_id = ?", replyId)
	}

	if isAudit > 0 {
		db = db.Where("is_audit = ?", isAudit)
	}

	if id == 0 && postUUID == "" && intlOpenID == "" && replyId == 0 {
		return nil, gorm.ErrRecordNotFound
	}

	err := db.First(&comment).Error
	if err != nil {
		return &comment, err
	}

	return &comment, nil
}

func CommentGetNoIgnoreDel(id, postId int64, intlOpenID string, replyId int64, isAudit int) (*model.Comment, error) {
	var comment model.Comment
	db := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName())
	if id > 0 {
		db = db.Where("id = ?", id)
	}

	if postId > 0 {
		db = db.Where("post_uuid = ?", postId)
	}
	if intlOpenID != "" {
		db = db.Where("intl_openid =  ?", intlOpenID)
	}
	if replyId > 0 {
		db = db.Where("reply_id = ?", replyId)
	}

	if isAudit > 0 {
		db = db.Where("is_audit = ?", isAudit)
	}

	if id == 0 && postId == 0 && intlOpenID == "" && replyId == 0 {
		return nil, gorm.ErrRecordNotFound
	}

	err := db.Unscoped().First(&comment).Error
	if err != nil {
		return &comment, err
	}

	return &comment, nil
}

func GetCommentByUUID(commentUUID string) (*model.Comment, error) {
	if commentUUID == "" {
		return nil, gorm.ErrRecordNotFound
	}
	var comment model.Comment
	err := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("comment_uuid = ? AND is_del = 0", commentUUID).First(&comment).Error
	if err != nil {
		return nil, err
	}
	return &comment, nil
}

func GetCommentByUUIDWithIgnoreDelete(commentUUID string) (*model.Comment, error) {
	if commentUUID == "" {
		return nil, gorm.ErrRecordNotFound
	}
	var comment model.Comment
	err := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("comment_uuid = ?", commentUUID).Unscoped().First(&comment).Error
	if err != nil {
		return nil, err
	}
	return &comment, nil
}

type CommentListConditions struct {
	PostUuid              string
	Type                  int
	Order                 []*OrderConditions
	LtId                  int64
	IsAudit               int
	IntlOpenid            string
	ReplyUuid             string
	CompositeCondition    *CompositeCondition
	CommentUuid           string
	IsDel                 int
	CommentUuids          []string
	PostUuids             []string
	IgnoreDel             bool
	LteHotNumStr          string
	LteIdStr              string
	PosStatus             string
	HotPageLimitCondition *CompositeCondition // 热度分页区分
}

func CommentList(conditions *CommentListConditions, limit int) ([]*model.Comment, error) {
	var comments []*model.Comment
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName())
	if limit > 0 {
		db = db.Limit(limit)
	}
	if conditions.PostUuid != "" {
		db = db.Where("post_uuid = ?", conditions.PostUuid)
	}
	if conditions.Type > 0 {
		db = db.Where("type = ?", conditions.Type)
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if conditions.IsAudit > 0 {
		db = db.Where("is_audit = ?", conditions.IsAudit)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.ReplyUuid != "" {
		db = db.Where("reply_uuid = ?", conditions.ReplyUuid)
	}
	if conditions.IsDel > 0 {
		db = db.Where("is_del = ?", conditions.IsDel)
	}
	if conditions.CompositeCondition != nil {
		db = db.Where(conditions.CompositeCondition.ToSQL())
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if len(conditions.CommentUuids) > 0 {
		db = db.Where("comment_uuid in ?", conditions.CommentUuids)
	}
	if len(conditions.PostUuids) > 0 {
		db = db.Where("post_uuid in ?", conditions.PostUuids)
	}
	if conditions.LteHotNumStr != "" {
		db = db.Where("hot_num <= ?", conditions.LteHotNumStr)
	}
	if conditions.LteIdStr != "" {
		db = db.Where("id < ?", conditions.LteIdStr)
	}
	if conditions.IgnoreDel {
		db = db.Unscoped()
	}
	if err = db.Find(&comments).Error; err != nil {
		return nil, err
	}

	return comments, nil
}

func CommentListNotTopBottom(conditions *CommentListConditions, limit int) ([]*model.Comment, error) {
	var comments []*model.Comment
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName())
	if limit > 0 {
		db = db.Limit(limit)
	}
	if conditions.PostUuid != "" {
		db = db.Where("post_uuid = ?", conditions.PostUuid)
	}
	if conditions.Type > 0 {
		db = db.Where("type = ?", conditions.Type)
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if conditions.IsAudit > 0 {
		db = db.Where("is_audit = ?", conditions.IsAudit)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.ReplyUuid != "" {
		db = db.Where("reply_uuid = ?", conditions.ReplyUuid)
	}
	if conditions.IsDel > 0 {
		db = db.Where("is_del = ?", conditions.IsDel)
	}
	if conditions.CompositeCondition != nil {
		db = db.Where(conditions.CompositeCondition.ToSQL())
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if len(conditions.CommentUuids) > 0 {
		db = db.Where("comment_uuid in ?", conditions.CommentUuids)
	}
	if len(conditions.PostUuids) > 0 {
		db = db.Where("post_uuid in ?", conditions.PostUuids)
	}
	if conditions.LteHotNumStr != "" {
		db = db.Where("hot_num <= ?", conditions.LteHotNumStr)
	}
	if conditions.LteIdStr != "" {
		db = db.Where("id < ?", conditions.LteIdStr)
	}
	if conditions.IgnoreDel {
		db = db.Unscoped()
	}
	if conditions.PosStatus != "" {
		db = db.Where("pos_status = ?", conditions.PosStatus)
	}
	if conditions.HotPageLimitCondition != nil {
		db = db.Where(conditions.HotPageLimitCondition.ToSQL())
	}
	//db = db.Where("pos_status = ?", constants.CommentPosStatusDefault)
	if err = db.Find(&comments).Error; err != nil {
		return nil, err
	}

	return comments, nil
}

func CommentCount(conditions *CommentListConditions) (int64, error) {
	var count int64
	db := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName())
	if conditions.PostUuid != "" {
		db = db.Where("post_uuid = ?", conditions.PostUuid)
	}
	if conditions.Type > 0 {
		db = db.Where("type = ?", conditions.Type)
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if conditions.IsAudit > 0 {
		db = db.Where("is_audit = ?", conditions.IsAudit)
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.ReplyUuid != "" {
		db = db.Where("reply_uuid = ?", conditions.ReplyUuid)
	}
	if conditions.IsDel > 0 {
		db = db.Where("is_del = ?", conditions.IsDel)
	}
	if conditions.CompositeCondition != nil {
		db = db.Where(conditions.CompositeCondition.ToSQL())
	}
	if len(conditions.CommentUuids) > 0 {
		db = db.Where("comment_uuid in ?", conditions.CommentUuids)
	}
	if len(conditions.PostUuids) > 0 {
		db = db.Where("post_uuid in ?", conditions.PostUuids)
	}
	if err := db.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func CommentCreate(comment *model.Comment, currentTime time.Time) error {
	db := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName())
	err := db.InstanceSet("now_time", currentTime).Create(&comment).Error

	return err
}

// UpdateComment 更新单个评论记录
func UpdateComment(ctx context.Context, commentUUID string, updateData map[string]interface{}) error {
	err := DB.SelectConnect("db_standalonesite").WithContext(ctx).
		Table((&model.Comment{}).TableName()).
		Where("comment_uuid = ?", commentUUID).
		Updates(updateData).Error
	if err != nil {
		return fmt.Errorf("updateComment error:%w", err)
	}
	return nil
}

// GetTopBottomComments 查询置顶或置底评论列表
func GetTopBottomComments(ctx context.Context, postUuid string, posStatusList []int) ([]*model.Comment, error) {
	if len(posStatusList) == 0 {
		return nil, nil
	}
	var comments []*model.Comment
	err := DB.SelectConnect("db_standalonesite").WithContext(ctx).
		Table((&model.Comment{}).TableName()).
		Where("post_uuid = ?", postUuid).
		Where("pos_status in ?", posStatusList).
		Where("is_del = ?", 0).
		Order("pos_set_time desc").
		Find(&comments).Error
	if err != nil {
		return nil, fmt.Errorf("getTopBottomComments error:%w", err)
	}
	return comments, nil
}

func CommentContentCreate(commentContent *model.CommentContent) error {
	tableName, err := (&model.Comment{}).GetCommentSubMeterTable(commentContent.CommentUUID)
	if err != nil {
		return err
	}
	db := DB.SelectConnect("db_standalonesite").Table(tableName)
	err = db.Create(&commentContent).Error

	return err
}

func CommentDelete(commentUUID string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("comment_uuid = ?", commentUUID).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func CommentContentDelete(commentUUID string, intlOpenid string) error {
	tableName, err := (&model.Comment{}).GetCommentSubMeterTable(commentUUID)
	if err != nil {
		return err
	}
	return DB.SelectConnect("db_standalonesite").Table(tableName).Where("comment_uuid = ?", commentUUID).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func CommentDeleteCommentByReplyIds(replyIds []int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("reply_id IN ?", replyIds).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func CommentIdsByPostId(postId int64) (ids []int64, err error) {
	err = DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("post_uuid = ? and type = 1", postId).Select("id").Find(&ids).Error
	return
}

// CommentReplyIdsByPostId获取p_comment表中reply_id字段，存的评论回复表p_comment_reply的id
func CommentReplyIdsByPostId(postId int64) (ids []int64, err error) {
	err = DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("post_uuid = ? and type = 2", postId).Select("reply_id").Find(&ids).Error
	return
}

// CommentReplyCommentIdsByPostId获取p_comment表中自增id列表，存的评论的回复的id
func CommentReplyCommentIdsByPostId(postId int64) (ids []int64, err error) {
	err = DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("post_uuid = ? and type = 2", postId).Select("id").Find(&ids).Error
	return
}

// CommentDeleteByPostId 根据动态id删除回复和评论
func CommentDeleteByPostId(postUUID string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("post_uuid = ?", postUUID).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

// CommentDeleteByIds 根据评论的id删除
func CommentDeleteByIds(ids []int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("id in ?", ids).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

func CommentContentUpdate(comment *model.CommentContent) error {
	tableName, err := (&model.Comment{}).GetCommentSubMeterTable(comment.CommentUUID)
	if err != nil {
		return err
	}
	return DB.SelectConnect("db_standalonesite").Table(tableName).Where("comment_uuid = ? AND is_del = ?", comment.CommentUUID, 0).Updates(&comment).Error
}

func CommentUpdate(commentUuid string, updateData map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("comment_uuid = ? ", commentUuid).Updates(updateData).Error
}

func BatchCommentDeleteAndContent(commentUuids []string) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		err := tx.Table((&model.Comment{}).TableName()).Where("comment_uuid in ? ", commentUuids).Updates(map[string]interface{}{
			"deleted_on": time.Now().Unix(),
			"is_del":     1,
		}).Error
		if err != nil {
			return err
		}
		for _, uuid := range commentUuids {
			tableName, err := (&model.Comment{}).GetCommentSubMeterTable(uuid)
			if err != nil {
				return err
			}
			err = tx.Table(tableName).Where("comment_uuid = ?", uuid).Updates(map[string]interface{}{
				"deleted_on": time.Now().Unix(),
				"is_del":     1,
			}).Error
			if err != nil {
				return err
			}
		}
		return nil
	})

}

func GetCommentInfoByUUID(commentUUID string) (*model.CommentContent, error) {
	var commentContent model.CommentContent
	uTable, err := (&model.Comment{}).GetCommentSubMeterTable(commentUUID)
	if err != nil {
		return nil, err
	}
	db := DB.SelectConnect("db_standalonesite").Table(uTable)

	db = db.Where("comment_uuid = ? AND is_del = ?", commentUUID, 0)

	err = db.First(&commentContent).Error
	if err != nil {
		return &commentContent, err
	}

	return &commentContent, nil
}

func GetCommentInfoByUUIDWithIgnoreDelete(commentUUID string) (*model.CommentContent, error) {
	var commentContent model.CommentContent
	uTable, err := (&model.Comment{}).GetCommentSubMeterTable(commentUUID)
	if err != nil {
		return nil, err
	}
	db := DB.SelectConnect("db_standalonesite").Table(uTable)

	db = db.Where("comment_uuid = ?", commentUUID)

	err = db.Unscoped().First(&commentContent).Error
	if err != nil {
		return &commentContent, err
	}

	return &commentContent, nil
}

// 获取某条评论的数据，查询回复以及回复的回复
func GetAuditedCommentListByCommentUUID(commentUUID string) ([]string, error) {
	var commentList []string
	err := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("comment_uuid = ? AND is_del = 0 AND is_audit = 1", commentUUID).Or("reply_uuid = ? AND is_del = 0 AND is_audit = 1", commentUUID).Pluck("comment_uuid", &commentList).Error
	if err != nil {
		return nil, err
	}
	return commentList, nil
}

// 获取某条回复的数据，查询回复的回复
func GetAuditedReplyListByCommentUUID(commentUUID string) ([]string, error) {
	var commentList []string
	err := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("comment_uuid = ? AND type = ? AND is_del = 0 AND is_audit = 1", commentUUID, constants.DYNAMIC_REPLY).Or("reply2reply_uuid = ? AND type = ? AND is_del = 0 AND is_audit = 1", commentUUID, constants.DYNAMIC_REPLY).Pluck("comment_uuid", &commentList).Error
	if err != nil {
		return nil, err
	}
	return commentList, nil
}

// 获取某条评论的数据，查询回复以及回复的回复
func GetUnAuditCommentListByCommentUUID(commentUUID string) ([]string, error) {
	var commentList []string
	err := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("reply_uuid = ? AND is_del = 0 AND is_audit = 2", commentUUID).Pluck("comment_uuid", &commentList).Error
	if err != nil {
		return nil, err
	}

	return commentList, nil
}

// 获取某条回复的数据，查询回复的回复
func GetUnAuditReplyListByCommentUUID(commentUUID string) ([]string, error) {
	var commentList []string
	err := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("reply2reply_uuid = ? AND type = ? AND is_del = 0 AND is_audit = 2", commentUUID, constants.DYNAMIC_REPLY).Pluck("comment_uuid", &commentList).Error
	if err != nil {
		return nil, err
	}
	return commentList, nil
}

// 同步获取全量的评论数据
func SyncGetAllComment(limit int) ([]*model.Comment, error) {
	var comments []*model.Comment
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName())
	if limit > 0 {
		db = db.Limit(limit)
	}

	if err = db.Unscoped().Find(&comments).Error; err != nil {
		return nil, err
	}
	return comments, nil
}

func BatchUpdateCommentLanguage(list map[string][]string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Transaction(func(tx *gorm.DB) error {
		for language, commentUuid := range list {
			err := tx.Unscoped().Where("comment_uuid in ?", commentUuid).Update("language", language).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func GetCommentContentList(commentUuid []string) []*model.CommentContent {
	var commentContents []*model.CommentContent

	var tables = make(map[string][]string)

	for _, uuid := range commentUuid {
		uTable, err := (&model.Comment{}).GetCommentSubMeterTable(uuid)
		if err == nil {
			tables[uTable] = append(tables[uTable], uuid)
		}

	}

	for table, uuids := range tables {
		var commentContentItems []*model.CommentContent
		err := DB.SelectConnect("db_standalonesite").Table(table).Where("comment_uuid in ?", uuids).Find(&commentContentItems).Error
		if err == nil {
			commentContents = append(commentContents, commentContentItems...)
		}
	}
	return commentContents
}

// 批量获取一系列评论的回复
func GetReplaysOfComments(commentUuids []string, intlOpenid string, limit int) (map[string][]*model.Comment, error) {
	comments := map[string][]*model.Comment{}
	if len(commentUuids) == 0 {
		return comments, nil
	}
	var err error
	// Limit the number of concurrent goroutines
	maxConcurrent := 5
	sem := make(chan struct{}, maxConcurrent)

	var wg sync.WaitGroup
	// 是否需要定成len(commentUuids) * 3的长度？
	results := make(chan []*model.Comment, len(commentUuids))
	errChan := make(chan error, len(commentUuids))

	// Split UUIDs into batches
	batchSize := 2
	queryStr := "reply_uuid = ? and type = 2 AND is_del = 0 AND is_audit = 1"
	if intlOpenid != "" {
		queryStr = fmt.Sprintf("reply_uuid = ? and type = 2 AND is_del = 0 AND (is_audit = 1 OR (is_audit = 2 AND intl_openid = '%s'))", intlOpenid)
	}
	for i := 0; i < len(commentUuids); i += batchSize {
		end := i + batchSize
		if end > len(commentUuids) {
			end = len(commentUuids)
		}
		batch := commentUuids[i:end]
		wg.Add(1)
		sem <- struct{}{} // Acquire semaphore
		go func(batch []string) {
			defer wg.Done()
			queryReplaysOfComments(batch, queryStr, limit, results, sem, errChan)
		}(batch)
	}
	go func() {
		recovery.CatchGoroutinePanic(context.Background())
		wg.Wait()
		close(results)
		close(errChan)
	}()

	for result := range results {
		for _, comment := range result {
			comments[comment.ReplyUUID] = append(comments[comment.ReplyUUID], comment)
		}
	}
	// Handle errors
	for queryErr := range errChan {
		err = queryErr
	}
	//if intlOpenid != "" {
	//	//commentList := make([]*model.Comment, 0)
	//	//db := DB.SelectConnect("db_standalonesite")
	//	//subquery := db.Table(fmt.Sprintf("`%s` as r", (&model.Comment{}).TableName())).Select("count(*)").Where("r.reply_uuid = c.reply_uuid and r.id <= c.id")
	//	//query := db.Table(fmt.Sprintf("`%s` as c", (&model.Comment{}).TableName())).Where("reply_uuid in ? and type = 2 AND is_del = 0 AND is_audit = 1", commentUuids)
	//	//err = query.Where("(?) <= ?", subquery, limit).Order("id DESC").Find(&commentList).Error
	//	//for _, comment := range commentList {
	//	//	comments[comment.ReplyUUID] = append(comments[comment.ReplyUUID], comment)
	//	//}
	//	for _, commentUUid := range commentUuids {
	//		curCommentReplies := []*model.Comment{}
	//		err = DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("reply_uuid = ? and type = 2 AND is_del = 0 AND is_audit = 1", commentUUid).Order("id DESC").Limit(limit).Find(&curCommentReplies).Error
	//		comments[commentUUid] = curCommentReplies
	//	}
	//
	//} else {
	//	//commentList := make([]*model.Comment, 0)
	//	//db := DB.SelectConnect("db_standalonesite")
	//	//subquery := db.Table(fmt.Sprintf("`%s` as r", (&model.Comment{}).TableName())).Select("count(*)").Where("r.reply_uuid = c.reply_uuid and r.id <= c.id")
	//	//query := db.Table(fmt.Sprintf("`%s` as c", (&model.Comment{}).TableName())).Where("reply_uuid in ? and type = 2 AND is_del = 0 AND (is_audit = 1 OR (is_audit = 2 AND intl_openid = ?))", commentUuids, intlOpenid)
	//	//err = query.Where("(?) <= ?", subquery, limit).Order("id DESC").Find(&commentList).Error
	//	//for _, comment := range commentList {
	//	//	comments[comment.ReplyUUID] = append(comments[comment.ReplyUUID], comment)
	//	//}
	//	for _, commentUUid := range commentUuids {
	//		curCommentReplies := []*model.Comment{}
	//		err = DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where("reply_uuid = ? and type = 2 AND is_del = 0 AND (is_audit = 1 OR (is_audit = 2 AND intl_openid = ?))", commentUUid, intlOpenid).Order("id DESC").Limit(limit).Find(&curCommentReplies).Error
	//		comments[commentUUid] = curCommentReplies
	//	}
	//}
	return comments, err
}

func queryReplaysOfComments(commentUuid []string, queryWhere string, limit int, results chan<- []*model.Comment, sem chan struct{}, err chan<- error) {
	defer func() {
		recovery.CatchGoroutinePanic(context.Background())
		<-sem
	}()

	for _, replyUuid := range commentUuid {
		var curCommentReplies []*model.Comment
		queryErr := DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Where(queryWhere, replyUuid).Order("id DESC").Limit(limit).Find(&curCommentReplies).Error
		if queryErr != nil {
			err <- queryErr
			return
		}
		results <- curCommentReplies
	}

}

type ReplaysOfCommentsCount struct {
	ReplyUuid  string `json:"reply_uuid"`
	ReplyCount int64  `json:"reply_count"`
}

func GetReplaysOfCommentsCount(commentUuids []string, intlOpenid string) (map[string]int64, error) {
	var replyCounts = map[string]int64{}
	var err error
	if intlOpenid != "" {
		var replaysOfCommentsCount = make([]ReplaysOfCommentsCount, 0)
		err = DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Select("reply_uuid, count(*) as reply_count").Where("reply_uuid in ? and type = 2 AND is_del = 0 AND is_audit = 1", commentUuids).Group("reply_uuid").Find(&replaysOfCommentsCount).Error
		for _, replyCount := range replaysOfCommentsCount {
			replyCounts[replyCount.ReplyUuid] = replyCount.ReplyCount
		}
	} else {
		var replaysOfCommentsCount = make([]ReplaysOfCommentsCount, 0)
		err = DB.SelectConnect("db_standalonesite").Table((&model.Comment{}).TableName()).Select("reply_uuid, count(*) as reply_count").Where("reply_uuid in ? and type = 2 AND is_del = 0 AND (is_audit = 1 OR (is_audit = 2 AND intl_openid = ?))", commentUuids, intlOpenid).Group("reply_uuid").Find(&replaysOfCommentsCount).Error
		for _, replyCount := range replaysOfCommentsCount {
			replyCounts[replyCount.ReplyUuid] = replyCount.ReplyCount
		}
	}
	return replyCounts, err
}
