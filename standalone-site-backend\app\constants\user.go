package constants

const (
	UserStatusNormal int = iota + 1
	UserStatusClosed
)

// UserBanT 1=新增动态封禁；2=新增评论封禁；3=新增账号封禁；4=移除动态封禁；5=移除评论封禁；6=移除账号封禁
type UserBanT uint8

const (
	USER_BAN_TYPE_POST UserBanT = iota + 1
	USER_BAN_TYPE_COMMENT
	USER_BAN_TYPE_ACCOUNT
	USER_BAN_TYPE_POST_DELETE
	USER_BAN_TYPE_COMMENT_DELETE
	USER_BAN_TYPE_ACCOUNT_DELETE
)

// UserWhiteListT 1=新增用户白名单；2=删除用户白名单
type UserWhiteListT uint8

const (
	USER_WHITE_LIST_ADD UserWhiteListT = iota + 1
	USER_WHITE_LIST_DELETE
)

// 用户隐私协议开关枚举
type PrivacySwitchType int8

const (
	ShowMyPosts PrivacySwitchType = iota + 1
	ShowMyCollection
	ShowMyFollow
	ShowMyFans
	ShowMyGameCard
	ReceiveTweetEmail
	MsgCommentNotify
	MsgLikeNotify
	MsgFollowNotify
	MsgSystemNotify
	MsgActivityNotify
	ShowMyComment
)

// 用户多语言类型： 1 昵称，2 个签
const (
	USER_LANGUAGE_TYPE_USERNAME = iota + 1
	USER_LANGUAGE_TYPE_REMARK
)

// 用户认证类型 1=官方；2=创作者；3=机构认证; 4= 板块管理员(仅添加称号，无实际权限)
const (
	USER_AUTH_TYPE_OFFICIAL int32 = iota + 1
	USER_AUTH_TYPE_CREATOR
	USER_AUTH_TYPE_MECHANISM
	USER_AUTH_TYPE_SECTION_MANAGER
)

const (
	USER_UNDERAGE int32 = iota - 1 // 未成年
	USER_NOT_SET                   // 未设置
	USER_ADULT                     // 成年人
)
