package user

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	es7 "github.com/olivere/elastic/v7"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

//同步用户审核表、用户评论表、用户举报-评论类型的语言

func SyncUserLanguageToUserAudit(c context.Context) {
	// 获取用户审核表数据
	var limit, offset, index, gtId = 100, 0, 1, 0

	var userInfo = make(map[string]string)
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("sync user audit language start, at: %d", time.Now().Unix())
	for {
		index++
		list, err := dao.UserAuditList(&dao.UserAuditConditions{
			LtId: int64(gtId),
			Order: []*dao.OrderConditions{
				&dao.OrderConditions{
					Column: "id",
					IsDesc: true,
				},
			},
		}, offset, limit, true)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("get user audit list failed, gtId: %d, limit: %d, offset: %d, err:%v", gtId, limit, offset, err)
			break
		}
		var userIntlOpenids = make([]string, 0)
		for _, audit := range list {
			fmt.Println("id", audit.ID)
			if _, ok := userInfo[audit.IntlOpenid]; ok {
				continue
			}
			userIntlOpenids = append(userIntlOpenids, audit.IntlOpenid)
		}

		if len(userIntlOpenids) == 0 {
			break
		}

		userList, err := dao.GetUserListByOpenidV2(userIntlOpenids, true)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("get user list failed, intl_openids: %v, err:%v", userIntlOpenids, err)
			continue
		}
		for _, content := range userList {
			userInfo[content.IntlOpenid] = content.Language
		}
		var updateData = make(map[string][]string)
		var updateEsData = make(map[int64]string)
		for _, audit := range list {
			if language, ok := userInfo[audit.IntlOpenid]; ok {
				updateEsData[audit.ID] = language
				if openids, ok2 := updateData[language]; ok2 {
					var isExist bool
					for _, openid := range openids {
						if openid == audit.IntlOpenid {
							isExist = true
							break
						}
					}
					if !isExist {
						updateData[language] = append(updateData[language], audit.IntlOpenid)
					}
				} else {
					updateData[language] = append(updateData[language], audit.IntlOpenid)
				}
			}

		}
		err = dao.BatchUpdateUserAuditLanguage(updateData)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("update user audit language failed, update_data: %+v, err:%v", updateData, err)
			continue
		}
		// 更新es

		for id, lang := range updateEsData {
			_, err = dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserAuditIndex, fmt.Sprintf("%d", id), map[string]interface{}{
				"language": lang,
			})

			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("sync user audit language es failed, id: %v,err: %v", id, err)
			}
		}
		dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserAuditIndex)
		if len(list) < limit {
			break
		}
		gtId = int(list[len(list)-1].ID)
	}

	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("sync user audit language end, at: %d", time.Now().Unix())
}

// 同步用户点赞数量和粉丝数量、关注数量、发帖数量等数据到统计表中
func SyncUserStateDataToUserState(c context.Context) {
	var limit = 100
	var ltId = 0
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("sync user audit language start, at: %d", time.Now().Unix())
	for {
		list, err := dao.GetUserList(limit, ltId)
		if err != nil {
			// 记录错误
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncUserStateDataToUserState | get user list failed, limit: %d, ltId: %d, err: %v", limit, ltId, err)
			continue
		}
		userOpenids := make([]string, 0, len(list))
		for _, owner := range list {
			userOpenids = append(userOpenids, owner.IntlOpenid)
		}
		userList, err := dao.GetUserListBySyncUserState(userOpenids, true)
		if err != nil {
			// 记录错误
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncUserStateDataToUserState | get user content list failed, limit: %d, ltId: %d, err: %v", limit, ltId, err)
			continue
		}
		var userStates []*model.UserState
		for _, content := range userList {
			userStates = append(userStates, &model.UserState{
				IntlOpenid:  content.IntlOpenid,
				FansNum:     content.FansNum,
				FollowNum:   content.FollowNum,
				AllPostNum:  content.AllPostNum,
				PostStarNum: content.PostStarNum,
				PostNum:     content.PostNum,
			})
		}
		err = dao.BatchCreateUserState(userStates)
		if err != nil {
			// 记录错误
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncUserStateDataToUserState | create user state failed, limit: %d, ltId: %d, err: %v", limit, ltId, err)
			continue
		}
		if len(list) < limit {
			// 获取到的数据小于limit，代表获取完了
			break
		}
		ltId = int(list[len(list)-1].ID)
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("sync user state end, at: %d", time.Now().Unix())

}

func SyncUserAuditStatus(c context.Context) {
	nextPageCursor := ""
	var index = 0
	for {
		if index >= 43 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserAuditStatus | larger than 43 break, next_page_cursor: %s", nextPageCursor)
			break
		}
		index++
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserAuditStatus | start, next_page_cursor: %s", nextPageCursor)
		rsp, err := getUserAuditList(c, nextPageCursor, 100)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncUserAuditStatus | getUserAuditList failed, err: %v", err)
			return
		}
		//machine_status 0未处理，1审核通过，2审核不通过
		// audit_status 1 未处理 2已发布 3已忽略
		lastTimeStame := 1740492814 // 2025-02-25 22:13:34 之前的用户审核
		for _, item := range rsp.List {
			// 再次状态检查
			// 不满足条件直接返回
			if !(item.AuditStatus >= 2 && item.ArtificialStatus == 0 && item.MachineStatus == 0 && item.CreatedOn <= int32(lastTimeStame)) {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserAuditStatus | skip item: %d", item.Id)
				continue
			}
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserAuditStatus | item: %d", item.Id)
			data := map[string]interface{}{}
			if item.AuditStatus == 2 {
				data["machine_status"] = 1
				dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserAuditIndex, fmt.Sprintf("%d", item.Id), data)
				dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserAuditIndex)
			} else if item.AuditStatus == 3 {
				data["machine_status"] = 2
				dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserAuditIndex, fmt.Sprintf("%d", item.Id), data)
				dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserAuditIndex)
			} else {
				continue
			}
		}
		if rsp.PageInfo.IsFinish {
			break
		}
		nextPageCursor = rsp.PageInfo.NextPageCursor
		time.Sleep(1 * time.Second)
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserAuditStatus | end, next_page_cursor: %s", nextPageCursor)

}

func getUserAuditList(c context.Context, nextPageCursor string, limit int64) (*pbUser.CMSGetUserAuditListRsp, error) {
	getUserListRsp := &pbUser.CMSGetUserAuditListRsp{
		List:     make([]*pbUser.CMSGetUserAuditRsp, 0),
		PageInfo: &pbUser.UserPageInfo{},
	}
	lastTimeStame := 1740492814 // 2025-02-25 22:13:34 之前的用户审核
	boolQuery := es7.NewBoolQuery()
	// 已发布/已忽略状态内容
	itemQuery := es7.NewRangeQuery("audit_status").Gte(2)
	boolQuery.Must(itemQuery)
	boolQuery.Must(es7.NewTermQuery("artificial_status", 0))
	boolQuery.Must(es7.NewTermQuery("machine_status", 0))
	boolQuery.Must(es7.NewTermQuery("is_del", 0))
	boolQuery.Must(es7.NewRangeQuery("created_on").Lte(lastTimeStame))
	var sortBys []es7.Sorter
	sortBys = append(sortBys, es7.NewFieldSort("audit_on").Desc())
	var lastSortValue []interface{}
	if nextPageCursor != "" {
		cursorStr, err := util.DecryptPageCursorS(nextPageCursor)
		if err != nil {
			return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
		}
		err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "CMSGetUserAuditInfoList | Failed to get idCursor")
		}
	}
	resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.UserAuditIndex, boolQuery, sortBys, lastSortValue, limit)
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.Hits == nil {
		getUserListRsp.PageInfo.IsFinish = true
		return getUserListRsp, nil
	}
	for _, hit := range resp.Hits.Hits {
		esUserAuditInfo := &model.ESUserAuditInfo{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(raw, esUserAuditInfo); err != nil {
			return nil, err
		}

		item := &pbUser.CMSGetUserAuditRsp{
			Id:               esUserAuditInfo.Id,
			IntlOpenid:       esUserAuditInfo.IntlOpenid,
			TextRiskLevel:    esUserAuditInfo.TextRiskLevel,
			TextRiskType:     esUserAuditInfo.TextRiskType,
			PicRiskLevel:     esUserAuditInfo.PicRiskLevel,
			PicRiskType:      esUserAuditInfo.PicRiskType,
			AuditStatus:      esUserAuditInfo.AuditStatus,
			Type:             esUserAuditInfo.Type,
			Context:          esUserAuditInfo.Context,
			AuditUser:        esUserAuditInfo.AuditUser,
			AuditOn:          esUserAuditInfo.AuditOn,
			CreatedOn:        esUserAuditInfo.CreatedOn,
			Language:         esUserAuditInfo.Language,
			ArtificialStatus: esUserAuditInfo.ArtificialStatus,
			MachineStatus:    esUserAuditInfo.MachineStatus,
			DelType:          esUserAuditInfo.DelType,
			DelReason:        esUserAuditInfo.DelReason,
		}

		getUserListRsp.List = append(getUserListRsp.List, item)
	}
	if len(resp.Hits.Hits) > 0 {
		jsonData, err := json.Marshal(resp.Hits.Hits[len(resp.Hits.Hits)-1].Sort)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSGetUserAuditInfoList json.Marshal err: %v", err)
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "CMSGetUserAuditInfoList | Failed to json.Marshal EncryptPageCursorS")
		}
		if len(resp.Hits.Hits) == 0 || int64(len(resp.Hits.Hits)) < limit {
			getUserListRsp.PageInfo.IsFinish = true
		} else {
			nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
			if err != nil {
				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "CMSGetUserAuditInfoList | Failed to postsFrom EncryptPageCursorS")
			}
			getUserListRsp.PageInfo.NextPageCursor = nextPageCursor
		}
	} else {
		getUserListRsp.PageInfo.IsFinish = true
	}
	getUserListRsp.PageInfo.PreviousPageCursor = nextPageCursor

	return getUserListRsp, nil
}
