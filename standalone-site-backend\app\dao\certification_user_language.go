package dao

import (
	"fmt"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm/clause"
	"trpc.publishing_application.standalonesite/app/model"
)

type CertificationUserLanguage struct {
	GameId     string   `json:"game_id"`
	AreaId     string   `json:"area_id"`
	Type       int32    `json:"type"`
	Languages  []string `json:"languages"`
	IntlOpenid string   `json:"intl_openid"`
}

// 更新多语言
func UpdateCertificationUserLanguages(languages []*model.CertificationUserLanguage) error {
	db := DB.SelectConnect("db_standalonesite").Table((&model.CertificationUserLanguage{}).TableName())
	err := db.Clauses((clause.OnConflict{
		Columns:   []clause.Column{{Name: "game_id"}, {Name: "area_id"}, {Name: "type"}, {Name: "language"}, {Name: "intl_openid"}},
		DoUpdates: clause.AssignmentColumns([]string{"content", "creator", "updater", "modified_on", "deleted_on", "is_del"}),
	})).Create(&languages).Error
	if err != nil {
		return err
	}
	updateLanguages := make([]string, 0)
	for _, languageItem := range languages {
		updateLanguages = append(updateLanguages, languageItem.Language)
	}
	db = DB.SelectConnect("db_standalonesite").Table((&model.CertificationUserLanguage{}).TableName())
	err = db.Where("game_id = ? AND area_id = ? AND type = ? AND language NOT IN ?", languages[0].GameId, languages[0].AreaId, languages[0].Type, updateLanguages).Updates(map[string]interface{}{}).Error
	// 删除不在languages内的记录
	// err = deleteNotInLanguages(languages)
	return err
}

// 删除所有认证多语言
func DeleteAllCerticationUserLanguages(intlOpenids []string, gameId string, areaId string) error {
	db := DB.SelectConnect("db_standalonesite").Table((&model.CertificationUserLanguage{}).TableName())
	err := db.Where("intl_openid IN ? AND game_id = ? AND area_id = ?", intlOpenids, gameId, areaId).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
	return err
}

func deleteNotInLanguages(languages []*model.CertificationUserLanguage) error {
	gameAreaUserPermissionMap := make(map[string]*CertificationUserLanguage, 0)
	for _, languageItem := range languages {
		key := fmt.Sprintf("%s-%s-%s-%s", languageItem.GameId, languageItem.AreaId, languageItem.Type, languageItem.IntlOpenid)
		if _, ok := gameAreaUserPermissionMap[key]; !ok {
			gameAreaUserPermissionMap[key] = &CertificationUserLanguage{
				GameId:    languageItem.GameId,
				AreaId:    languageItem.AreaId,
				Type:      languageItem.Type,
				Languages: []string{languageItem.Language},
			}
		} else {
			items := gameAreaUserPermissionMap[key]
			items.Languages = append(items.Languages, languageItem.Language)
		}
	}
	for _, languageItem := range gameAreaUserPermissionMap {
		db := DB.SelectConnect("db_standalonesite").Table((&model.CertificationUserLanguage{}).TableName())
		err := db.Where("game_id = ? AND area_id = ? AND intl_openid = ? AND type = ? AND language NOT IN ?", languageItem.GameId, languageItem.AreaId, languageItem.IntlOpenid, languageItem.Type, languageItem.Languages).Updates(map[string]interface{}{
			"deleted_on": time.Now().Unix(),
			"is_del":     1,
		}).Error
		if err != nil {
			// db.Rollback()
			return err
		}
	}
	// db.Commit()
	return nil
}

// 获取某个用户的认证多语言数据
func GetCertificationUserLanguages(intlOpenid string, language string, gameId string, areaId string) ([]*model.CertificationUserLanguage, error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.CertificationUserLanguage{}).TableName())
	var languages []*model.CertificationUserLanguage
	err := db.Where("intl_openid = ? AND game_id = ? AND area_id = ? AND is_del = ? AND language = ?", intlOpenid, gameId, areaId, 0, language).Find(&languages).Error
	return languages, err
}

// 获取某个用户所有认证多语言用户数据
func GetCertificationUserAllLanguages(intlOpenid string, gameId string, areaId string) ([]*model.CertificationUserLanguage, error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.CertificationUserLanguage{}).TableName())
	var languages []*model.CertificationUserLanguage
	err := db.Where("intl_openid = ? AND game_id = ? AND area_id = ? AND is_del = ?", intlOpenid, gameId, areaId, 0).Find(&languages).Error
	return languages, err
}
