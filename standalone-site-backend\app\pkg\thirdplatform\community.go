package thirdplatform

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/pkg/httpclient"
)

const (
	GetCommentListUrl             = "/trpc.wegame_app_global.comment_svr.Comment/GetCommentList"
	IssueCommentUrl               = "/trpc.wegame_app_global.comment_svr.Comment/IssueComment"
	DelCommentInner               = "/trpc.wegame_app_global.comment_svr.Comment/DelCommentInner"
	ReviewCommentBatchUrl         = "/trpc.wegame_app_global.comment_svr.Comment/ReviewCommentBatch"
	GetCommentListByCommendIDsUrl = "/trpc.wegame_app_global.comment_svr.Comment/GetCommentListByCommendIds"
	UpdateCommentUrl              = "/trpc.wegame_app_global.comment_svr.Comment/UpdateComment"
	ReviewCommentUrl              = "/trpc.wegame_app_global.comment_svr.Comment/ReviewComment"
	PunishCommentBatchUrl         = "/trpc.wegame_app_global.comment_svr.Comment/PunishCommentBatch"
	PunishCommentByUidUrl         = "/trpc.wegame_app_global.comment_svr.Comment/PunishCommentByUid"
)

type GetCommentParam struct {
	BizID           constants.PostCommentT
	ContentID       string
	CommentID       string
	Uid             string
	GameID          string
	AreaID          string
	StartSeq        string
	NeedNotReviewed bool
	OptType         constants.PostCommentOptT
}

type AddCommentParam struct {
	BizID          constants.PostCommentT
	ContentID      string
	ContentText    string
	Uid            string
	ContentExtInfo string
	GameID         string
	AreaID         string
	NotNeedReview  bool // 是否不需要审核 true不需要审核
}

type UpdateCommentParam struct {
	BizID          constants.PostCommentT
	CommentID      string
	ContentExtInfo string
	GameID         string
	AreaID         string
}

type DelCommentParam struct {
	BizID     constants.PostCommentT
	ContentID string
	CommentID string
	GameID    string
	AreaID    string
}

type BatchReviewCommentParam struct {
	CommentIndexList []CommentIndex
	UpdateUser       string
	ReviewStatus     int    // 审核状态 1：审核成功 2：1审成功 3：1审驳回 4：2审驳回
	GameID           string `json:"game_id"`
	AreaID           string `json:"area_id"`
}

type BatchPunishCommentParam struct {
	CommentIndexList []CommentIndex
	UpdateUser       string
	PunishReason     string
	GameID           string `json:"game_id"`
	AreaID           string `json:"area_id"`
}

type ReviewCommentConditionParam struct {
	CommentIndex CommentIndex
	UpdateUser   string
	ReviewStatus int // 审核状态 1：审核成功 2：1审成功 3：1审驳回 4：2审驳回
	OptType      constants.ReviewCommentOptT
	GameID       string `json:"game_id"`
	AreaID       string `json:"area_id"`
	ReviewReason string `json:"review_reason"`
}

type PunishCommentConditionParam struct {
	UID               string
	ParentID          string
	UpdateUser        string
	PunishReason      string
	PunishCommentType constants.PunishCommentByUidOptT
	BeginTime         string `json:"begin_time"` // 起始时间, 格式yyyy-mm-dd HH:MM:SS
	EndTime           string `json:"end_time"`   // 终止时间, 格式yyyy-mm-dd HH:MM:SS
	GameID            string `json:"game_id"`
	AreaID            string `json:"area_id"`
}

type CommentIndex struct {
	BizID     constants.PostCommentT `json:"biz_id"`
	CommentID string                 `json:"comment_id"` // 评论id
	GameID    string                 `json:"game_id"`
	AreaID    string                 `json:"area_id"`
	UID       string                 `json:"uid"`
	ParentID  string                 `json:"parent_id"`
}

type CommunityCommentExtInfo struct {
	PostUrl        string `json:"post_url"` // 该评论对应的附内容的访问链接
	GameID         string `json:"game_id"`
	AreaID         string `json:"area_id"`
	UpvoteCount    int64  `json:"upvote_count"`     // 点赞总数
	IsParentDelete bool   `json:"is_parent_delete"` // 父内容是否已删除
}

/*
GetCommentList 调用内容资讯的接口，获取CMS内容资讯评论列表
参考：https://trpc.rick.woa.com/rick/pb/detail?id=35357
目前只支持滚动分页，每页10条
*/
func GetCommentList(param GetCommentParam) (commentListData model.CommunityCommentListData, err error) {
	postData := map[string]interface{}{
		"biz_id":     param.BizID, // LIP动态
		"content_id": param.ContentID,
		"start_seq":  param.StartSeq,
		"order_type": 0,   // 0=最新评论 1=最老评论
		"source":     "1", // LIP
		"comment_id": param.CommentID,
		"opt_type":   param.OptType,
	}
	if param.Uid != "" {
		userInfo := map[string]interface{}{
			"uid": param.Uid,
		}
		postData["user_info"] = userInfo
	}
	apiResultString, err := callCommunityApi(postData, GetCommentListUrl, param.GameID, param.AreaID)
	if err != nil {
		return
	}
	var jsonError error
	conf := config.GetConfig()
	if conf.CommunitySetting.IsInnerAPI != 1 {
		var commentResult model.CommunityCommentListResp
		jsonError = json.Unmarshal([]byte(apiResultString), &commentResult)
		commentListData = commentResult.Data
	} else {
		jsonError = json.Unmarshal([]byte(apiResultString), &commentListData)
	}

	if jsonError != nil || commentListData.Result != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCommentList return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCommentList return error, %s", apiResultString)
		err = errs.NewCustomError(context.Background(), code.GetCommunityCommentInvalidResp, "GetCommentList | Exception occurred while processing the response body of the GetCommentList API call.")
		return
	}
	return
}

// GetCommentListByCommentIDs 根据评论id列表查询评论列表信息
func GetCommentListByCommentIDs(commentIDs []string, gameID, areaID string) (comments []model.CommentItem, err error) {
	if len(commentIDs) == 0 {
		return
	}
	postData := map[string]interface{}{
		"comment_id_list": commentIDs,
	}
	apiResultString, err := callCommunityApi(postData, GetCommentListByCommendIDsUrl, gameID, areaID)
	if err != nil {
		return
	}
	var commentListData model.CommunityCommentListData
	var jsonError error
	conf := config.GetConfig()
	if conf.CommunitySetting.IsInnerAPI != 1 {
		var commentResult model.CommunityCommentListResp
		jsonError = json.Unmarshal([]byte(apiResultString), &commentResult)
		commentListData = commentResult.Data
	} else {
		jsonError = json.Unmarshal([]byte(apiResultString), &commentListData)
	}

	if jsonError != nil || commentListData.Result != 0 || len(commentListData.Comments) == 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCommentListByCommentIDs return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCommentListByCommentIDs return error, %s", apiResultString)
		err = errs.NewCustomError(context.Background(), code.GetCommunityCommentsInvalidResp, "GetCommentListByCommentIDs | Exception occurred while processing the response body of the GetCommentListByCommentIDs API call.")
		return
	}
	comments = commentListData.Comments
	return
}

// AddComment 新增评论记录
func AddComment(param AddCommentParam) (comment model.CommentItem, err error) {
	userInfo := map[string]interface{}{
		"uid": param.Uid,
	}
	commentItem := map[string]interface{}{
		"content": param.ContentText,
	}
	postData := map[string]interface{}{
		"biz_id":           param.BizID, // LIP动态
		"content_id":       param.ContentID,
		"comment_item":     commentItem,
		"content_ext_info": param.ContentExtInfo,
		"user_info":        userInfo,
		"source":           "1", // LIP
		"not_need_review":  param.NotNeedReview,
	}
	apiResultString, err := callCommunityApi(postData, IssueCommentUrl, param.GameID, param.AreaID)
	if err != nil {
		return
	}
	var commentAddData model.CommunityCommentAddData
	var jsonError error
	conf := config.GetConfig()
	if conf.CommunitySetting.IsInnerAPI != 1 {
		var commentAdd model.CommunityCommentAddResp
		jsonError = json.Unmarshal([]byte(apiResultString), &commentAdd)
		commentAddData = commentAdd.Data
	} else {
		jsonError = json.Unmarshal([]byte(apiResultString), &commentAddData)
	}

	if jsonError != nil || commentAddData.Result != 0 || commentAddData.Comment.CommentID == "" {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("AddComment return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("AddComment return error, %s", apiResultString)
		err = errs.NewCustomError(context.Background(), code.AddCommunityCommentInvalidResp, "AddComment | Exception occurred while processing the response body of the AddComment API call.")
		return
	}
	comment = commentAddData.Comment
	return
}

// UpdateComment 更新评论记录
func UpdateComment(param UpdateCommentParam) error {
	postData := map[string]interface{}{
		"biz_id":           param.BizID, // LIP动态
		"comment_id":       param.CommentID,
		"content_ext_info": param.ContentExtInfo,
		"source":           "1", // LIP
	}
	apiResultString, err := callCommunityApi(postData, UpdateCommentUrl, param.GameID, param.AreaID)
	if err != nil {
		return err
	}
	var commentUpdateData model.CommunityCommentUpdateData
	var jsonError error
	conf := config.GetConfig()
	if conf.CommunitySetting.IsInnerAPI != 1 {
		var commentUpdate model.CommunityCommentUpdateResp
		jsonError = json.Unmarshal([]byte(apiResultString), &commentUpdate)
		commentUpdateData = commentUpdate.Data
	} else {
		jsonError = json.Unmarshal([]byte(apiResultString), &commentUpdateData)

	}
	if jsonError != nil || commentUpdateData.Result != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UpdateComment return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UpdateComment return error, %s", apiResultString)
		err = errs.NewCustomError(context.Background(), code.UpdateCommunityCommentInvalidResp, "UpdateComment | Exception occurred while processing the response body of the UpdateComment API call.")
		return err
	}
	return nil
}

// DeleteComment 删除评论记录
func DeleteComment(param DelCommentParam) error {
	postData := map[string]interface{}{
		"biz_id":     param.BizID, // LIP动态
		"content_id": param.ContentID,
		"comment_id": param.CommentID,
		"source":     "1",
	}
	apiResultString, err := callCommunityApi(postData, DelCommentInner, param.GameID, param.AreaID)
	if err != nil {
		return err
	}
	var commentDeleteData model.CommunityCommentDelData
	var jsonError error
	conf := config.GetConfig()
	if conf.CommunitySetting.IsInnerAPI != 1 {
		var commentDel model.CommunityCommentDelResp
		jsonError = json.Unmarshal([]byte(apiResultString), &commentDel)
		commentDeleteData = commentDel.Data
	} else {
		jsonError = json.Unmarshal([]byte(apiResultString), &commentDeleteData)
	}
	if jsonError != nil || commentDeleteData.Result != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("DeleteComment return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("DeleteComment return error, %s", apiResultString)
		err = errs.NewCustomError(context.Background(), code.DeleteCommunityCommentInvalidResp, "DeleteComment | Exception occurred while processing the response body of the DeleteComment API call.")
		return err
	}

	return nil
}

// BatchReviewComment 批量审核评论
func BatchReviewComment(param BatchReviewCommentParam) error {
	postData := map[string]interface{}{
		"comment_indexs": param.CommentIndexList, // 评论索引列表, 单次最大支持20个
		"update_user":    param.UpdateUser,
		"review_status":  param.ReviewStatus, // 审核状态 1：审核成功 2：1审成功 3：1审驳回 4：2审驳回
	}
	apiResultString, err := callCommunityApi(postData, ReviewCommentBatchUrl, param.GameID, param.AreaID)
	if err != nil {
		return err
	}
	var commentReviewData model.CommunityCommentReviewData
	var jsonError error
	conf := config.GetConfig()
	if conf.CommunitySetting.IsInnerAPI != 1 {
		var commentReview model.CommunityCommentReviewResp
		jsonError = json.Unmarshal([]byte(apiResultString), &commentReview)
		commentReviewData = commentReview.Data
	} else {
		jsonError = json.Unmarshal([]byte(apiResultString), &commentReviewData)

	}
	if jsonError != nil || commentReviewData.Result != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("BatchReviewComment return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("BatchReviewComment return error, %s", apiResultString)
		err = errs.NewCustomError(context.Background(), code.BatchReviewCommentInvalidResp, "BatchReviewComment | Exception occurred while processing the response body of the DeleteComment API call.")
		return err
	}

	return nil
}

/*
		ReviewCommentByCondition 根据用户uid或者parent_id来批量审批相关评论。
	  主要用于：1.CMS管理端删除动态后，将未审批评论设置为已忽略；2.CMS评论封禁用户，未处理的评论需要设置为已忽略
*/
func ReviewCommentByCondition(param ReviewCommentConditionParam) error {
	postData := map[string]interface{}{
		"comment_index": param.CommentIndex, // 评论索引列表, 单次最大支持20个
		"update_user":   param.UpdateUser,
		"review_status": param.ReviewStatus, // 审核状态 1：审核成功 2：1审成功 3：1审驳回 4：2审驳回
		"opt_type":      param.OptType,
		"review_reason": param.ReviewReason,
	}
	apiResultString, err := callCommunityApi(postData, ReviewCommentUrl, param.GameID, param.AreaID)
	if err != nil {
		return nil
	}
	var commentReviewData model.CommunityCommentReviewData
	var jsonError error
	conf := config.GetConfig()
	if conf.CommunitySetting.IsInnerAPI != 1 {
		var commentReview model.CommunityCommentReviewResp
		jsonError = json.Unmarshal([]byte(apiResultString), &commentReview)
		commentReviewData = commentReview.Data
	} else {
		jsonError = json.Unmarshal([]byte(apiResultString), &commentReviewData)

	}
	if jsonError != nil || commentReviewData.Result != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("ReviewCommentByCondition return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("ReviewCommentByCondition return error, %s", apiResultString)
		err = errs.NewCustomError(context.Background(), code.ReviewCommentInvalidResp, "ReviewCommentByCondition | Exception occurred while processing the response body of the DeleteComment API call.")
		return err
	}

	return nil
}

// BatchPunishComment 批量审批不通过评论，会在MongoDB中删除数据，getlist就查不到了
func BatchPunishComment(param BatchPunishCommentParam) error {
	postData := map[string]interface{}{
		"comment_indexs": param.CommentIndexList, // 评论索引列表, 单次最大支持20个
		"update_user":    param.UpdateUser,
		"punish_reason":  param.PunishReason,
	}
	apiResultString, err := callCommunityApi(postData, PunishCommentBatchUrl, param.GameID, param.AreaID)
	if err != nil {
		return err
	}
	var commentPunishData model.CommunityCommentPunishData
	var jsonError error
	conf := config.GetConfig()
	if conf.CommunitySetting.IsInnerAPI != 1 {
		var commentPunishResp model.CommunityCommentPunishResp
		jsonError = json.Unmarshal([]byte(apiResultString), &commentPunishResp)
		commentPunishData = commentPunishResp.Data
	} else {
		jsonError = json.Unmarshal([]byte(apiResultString), &commentPunishData)

	}
	if jsonError != nil || commentPunishData.Result != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("BatchPunishComment return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("BatchPunishComment return error, %s", apiResultString)
		err = errs.NewCustomError(context.Background(), code.BatchReviewCommentInvalidResp, "BatchPunishComment | Exception occurred while processing the response body of the DeleteComment API call.")
		return err
	}

	return nil
}

/*
		PunishCommentByUid 根据用户uid或者parent_id来批量审批不通过相关评论。
	  主要用于：1.CMS管理端删除动态后，将未审批评论设置为已忽略；2.CMS评论封禁用户，未处理的评论需要设置为已忽略
*/
func PunishCommentByUid(param PunishCommentConditionParam) error {
	postData := map[string]interface{}{
		"uid":                 param.UID,
		"parent_id":           param.ParentID,
		"update_user":         param.UpdateUser,
		"punish_reason":       param.PunishReason,
		"punish_comment_type": param.PunishCommentType,
		"begin_time":          param.BeginTime,
		"end_time":            param.EndTime,
	}
	apiResultString, err := callCommunityApi(postData, PunishCommentByUidUrl, param.GameID, param.AreaID)
	if err != nil {
		return err
	}
	var commentpunishData model.CommunityCommentPunishByUidData
	var jsonError error
	conf := config.GetConfig()
	if conf.CommunitySetting.IsInnerAPI != 1 {
		var commentPunishResp model.CommunityCommentPunishByUidResp
		jsonError = json.Unmarshal([]byte(apiResultString), &commentPunishResp)
		commentpunishData = commentPunishResp.Data
	} else {
		jsonError = json.Unmarshal([]byte(apiResultString), &commentpunishData)

	}
	if jsonError != nil || commentpunishData.Result != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PunishCommentByUid return error, postData %v", postData)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PunishCommentByUid return error, %s", apiResultString)
		err = errs.NewCustomError(context.Background(), code.ReviewCommentInvalidResp, "PunishCommentByUid | Exception occurred while processing the response body of the DeleteComment API call.")
		return err
	}

	return nil
}

func callCommunityApi(postData interface{}, apiPath, gameID, areaID string) (apiResultString string, err error) {
	postJSON, err := json.Marshal(postData)
	postDataStr := string(postJSON)
	if err != nil {
		err = errs.NewCustomError(context.Background(), code.GetCommunityDataInvalidParams, "Incorrect request parameters.")
		return
	}
	currentTimeStamp := fmt.Sprintf("%d", time.Now().Unix())
	conf := config.GetConfig()
	if conf.CommunitySetting.IsInnerAPI != 1 {
		apiPath = fmt.Sprintf("%s%s", conf.CommunitySetting.ServicePathPrefix, apiPath)
	}
	requestURL := fmt.Sprintf("%s%s", conf.CommunitySetting.ServiceUrl, apiPath)

	optionOne := httpclient.ClientOption{
		URL:     requestURL,
		Type:    http.MethodPost,
		Timeout: 3 * time.Second,
		Header: map[string]string{
			"Content-Type":     "application/json; charset=utf-8",
			"X-AUTH-Timestamp": currentTimeStamp,
			"X-GameID":         gameID,
			"X-AreaID":         areaID,
		},
		PostString: postDataStr,
	}
	if conf.CommunitySetting.IsInnerAPI != 1 {
		// 如果是外网调用，则需要生成签名，调用cms内容资讯平台接口
		appKey := conf.CommunitySetting.AppKey
		appSecret := conf.CommunitySetting.AppSecret
		signature := hexEncodeHmacSHA256(appSecret, fmt.Sprintf("%s\n%s\n%s", apiPath, appKey, currentTimeStamp))
		optionOne.Header["X-AUTH-Sign"] = signature
		optionOne.Header["X-AUTH-Version"] = "v1.0"
		optionOne.Header["X-AUTH-Appkey"] = appKey
	}

	// 发请求
	resultOption := httpclient.RequestOne(optionOne)
	// 结果判断
	if resultOption.RequestError != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call Community api return error, url %s", requestURL)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call Community api return error, postDataStr %s", postDataStr)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call Community api http request error, %v", resultOption.RequestError)

		err = errs.NewCustomError(context.Background(), code.GetCommunityDataHttpError, "callCommunityApi | Exception occurred while calling the Community API.")
		return
	}

	apiResultString = resultOption.Result
	return
}
