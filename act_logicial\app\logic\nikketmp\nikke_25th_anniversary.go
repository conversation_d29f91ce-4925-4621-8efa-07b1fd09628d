package nikketmp

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	kafkaDB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/kafka"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	utilModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_nikke_tmp"
	redisOrigin "github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/kafka"
	"trpc.act.logicial/app/logic/common"
	"trpc.act.logicial/app/model"
	"trpc.act.logicial/app/model/general"
	"trpc.act.logicial/app/model/nikke"
)

// UserCompleteOneTypeTaskAndAddPointsParam TODO
type UserCompleteOneTypeTaskAndAddPointsParam struct {
	GameOpenId       string // 游戏id
	LipOpenId        string // 用户openid
	LoginIntlGameId  int64  // 登录的游戏id
	UserEventTimeStr string // 时间节点
	TaskType         int    // 任务类型
	Amount           string // 用户消费金额
	Scene            string // 支付场景
}

// VoteAnniversary25thListRedisKey TODO
const VoteAnniversary25thListRedisKey = "NikkeVoteAnniversary2Point5thList"

// RecordAnniversary2Point5thVote 记录2.5周年普通投票
func RecordAnniversary2Point5thVote(ctx context.Context, voteList []int32, roleInfo *gamePb.RoleInfo) error {
	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	langType := metadata.GetLangType(ctx)
	if len(voteList) != 5 {
		return errs.NewCustomError(ctx, code.NikkeTemVoteCountErr, "Abnormal number of votes")
	}
	// 检查当前用户今天是否已投票
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
	}
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.Nikke2503UserVotingHistoryModel{}.TableName()).Where(condition)
	voteListStr := int32SliceToStr(voteList)
	// 判断今天是否已参与投票
	startOfDay := common.GetStartOfDay()
	currentTimestamp := common.GetCurrentTimestamp()

	tx = tx.Where("created_at >= ? and created_at <= ?", startOfDay, currentTimestamp)
	var count int64
	if err = tx.Count(&count).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RecordAnniversary2Point5thVote Count db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		// 已参与过投票
		log.WithFieldsContext(ctx, "log_type", "err", "m", "RecordAnniversary2Point5thVote").
			Infof(fmt.Sprintf("RecordAnniversary2Point5thVote count: [%v]", count))
		return errs.NewCustomError(ctx, code.NikkeVoteDayLimit, "RecordAnniversary2Point5thVote VoteDayLimit")
	}
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.Nikke2503UserVotingHistoryModel{}.TableName()).
		Create(&nikke.Nikke2503UserVotingHistory{
			UID:         userAccount.Uid,
			AccountType: int16(userAccount.AccountType),
			VoteIdList:  voteListStr,
			LangType:    langType,
		}).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RecordAnniversary2Point5thVote Create db error, \t [Error]:{%v} ", err)
	}

	// 调用kafka更新总票数数据
	voteItems := &pb.UserAnniversaryVoteList{
		VoteList:     []*pb.AnniversaryVoteItem{},
		VoteType:     1,
		LangType:     langType,
		AreaId:       int32(roleInfo.GetAreaId()),
		DayTimestamp: startOfDay,
	}
	voteMap := make(map[int32]int32)
	for _, voteId := range voteList {
		if voteNum, ok := voteMap[voteId]; !ok {
			voteMap[voteId] = 1
		} else {
			voteMap[voteId] = voteNum + 1
		}
	}
	for voteId, voteNum := range voteMap {
		voteItems.VoteList = append(voteItems.VoteList, &pb.AnniversaryVoteItem{
			VoteId:  cast.ToString(voteId),
			VoteNum: voteNum,
		})
	}
	if err = sendToAnniversary25thVote(ctx, voteItems); err != nil {
		return err
	}
	return nil
}

// GetAnniversary2Point5thVoteRecords TODO
func GetAnniversary2Point5thVoteRecords(ctx context.Context) ([]int32, error) {
	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}

	// 获取投票记录
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.Nikke2503UserVotingHistoryModel{}.TableName()).
		Select("vote_id_list").
		Where("uid = ? and account_type = ?", userAccount.Uid, userAccount.AccountType)
	// 投票只查当天
	startOfDay := common.GetStartOfDay()
	currentTimestamp := common.GetCurrentTimestamp()
	tx = tx.Where("created_at >= ? and created_at <= ?", startOfDay, currentTimestamp)
	var voteListStr string
	if err = tx.Limit(1).Find(&voteListStr).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"GetAnniversary25thVoteRecords First db error, \t [Error]:{%v} ", err)
	}
	if voteListStr == "" {
		// 没有投票信息
		return nil, nil
	}
	// 格式化投票信息
	voteListSli := strings.Split(voteListStr, ",")
	voteList := make([]int32, len(voteListSli))
	for i, voteId := range voteListSli {
		intVal, err := strconv.Atoi(voteId)
		if err != nil {
			return nil, errs.NewCustomError(ctx, code.VoteIdTypeErr, "Vote Id Type Err;err:[%v]", err)
		}
		voteList[i] = int32(intVal)
	}
	return voteList, nil
}

// ShowAnniversary2Point5thVoteList TODO
// 2.5 周年展示普通投票列表
func ShowAnniversary2Point5thVoteList(ctx context.Context) ([]*pb.VoteItem, error) {

	now := time.Now()
	// redis 获取投票列表数据
	cacheKey := getRedisKey(VoteAnniversary25thListRedisKey)
	exist, err := redis.GetClient().Exists(ctx, cacheKey).Result()
	if err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"ShowAnniversary25thVoteList db error, \t [Error]:{%v} ", err)
	}
	if exist != 0 {
		// 取缓存数据
		resultMap, err := redis.GetClient().HGetAll(ctx, cacheKey).Result()
		if err != nil {
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
				"ShowAnniversary25thVoteList HGetAll db error, \t [Error]:{%v} ", err)
		}
		voteItem := make([]*pb.VoteItem, 0, len(resultMap))
		for voteIdStr, voteNumStr := range resultMap {
			voteId := cast.ToInt32(voteIdStr)
			voteNum := cast.ToInt32(voteNumStr)
			voteItem = append(voteItem, &pb.VoteItem{
				VoteId:  voteId,
				VoteNum: voteNum,
			})
		}
		// 排序
		log.WithFieldsContext(ctx, "log_type", "debug").Infof("sortVoteItemsByVoteNum before time:%v", time.Since(now))
		voteItemsDesc := sortVoteItemsByVoteNum(voteItem)
		log.WithFieldsContext(ctx, "log_type", "debug").Infof("sortVoteItemsByVoteNum after time:%v", time.Since(now))
		return voteItemsDesc, nil
	}

	// 调用同步接口,同步数据至redis中并返回数据
	voteListCache, err := countAnniversary2Point5thVoteList(ctx, true)
	if err != nil {
		// 同步数据异常
		return nil, err
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(
		"ShowAnniversary25thVoteList sortVoteItemsByVoteNum all time[:%v]", time.Since(now))
	return voteListCache, nil
}

// countAnniversary2Point5thVoteList 计算普通投票票数
func countAnniversary2Point5thVoteList(ctx context.Context, syncCache bool) ([]*pb.VoteItem, error) {
	// 根据当前时间获取数据库数据
	voteList := make([]*nikke.VoteItem, 0)
	sel := "vote_id, SUM(vote_num) AS vote_num"
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.Nikke2503AllVotingRecordsModel{}.TableName()).Select(sel)
	if err := tx.Group("vote_id").Order("vote_num desc").Find(&voteList).Error; err != nil &&
		err.Error() != gorm.ErrRecordNotFound.Error() {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"countAnniversary25thVoteList SyncVoteListCache Find db error, \t [Error]:{%v} ", err)
	}
	if len(voteList) == 0 {
		return []*pb.VoteItem{}, nil
	}
	if !syncCache {
		// 不写缓存
		return buildVoteItem(voteList), nil
	}
	// 写入缓存
	voteListMap := make(map[string]interface{})
	for _, voteItem := range voteList {
		voteListMap[cast.ToString(voteItem.VoteID)] = voteItem.VoteNum
	}
	cacheKey := getRedisKey(VoteAnniversary25thListRedisKey)
	if err := redis.GetClient().HSet(ctx, cacheKey, voteListMap).Err(); err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
			"countAnniversary25thVoteList redis HMSet error, \t [Error]:{%v}", err)
	}
	// 设置超时时间/7天
	expiration := 168 * time.Hour
	if err := redis.GetClient().Expire(ctx, cacheKey, expiration).Err(); err != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeRedis, DB.MySqlConnectErr,
			"countAnniversary25thVoteList redis HMSet Expire error, \t [Error]:{%v}", err)
	}

	return buildVoteItem(voteList), nil
}

// RecordAnniversary2Point5ParticipationUser TODO
// NIKKE 2.5周年记录参加活动的用户
func RecordAnniversary2Point5ParticipationUser(ctx context.Context) error {
	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}

	// 记录参与活动的用户
	var userParticipation nikke.Nikke2503UserParticipation
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.Nikke2503UserParticipationModel{}.TableName()).
		Where(nikke.Nikke2503UserParticipation{
			UID:         userAccount.Uid,
			AccountType: int16(userAccount.AccountType),
		}).FirstOrCreate(&userParticipation).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RecordAnniversary25ParticipationUser FirstOrCreate db error, \t [Error]:{%v} ", err)
	}
	return nil
}

// sendToAnniversary25thVote TODO
// 2.5周年普通投票写入kafka
func sendToAnniversary25thVote(ctx context.Context, voteItem *pb.UserAnniversaryVoteList) (err error) {
	kafkaByte, _ := json.Marshal(voteItem)
	errK := kafka.Produce(ctx, kafka.NIKKEAnniversary25thVoteProducer, "", string(kafkaByte))
	if errK != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeKafka, kafkaDB.KafkaConnectErr, "kafka produce error, error = %v",
			errK.Error())
	}
	return
}

// WriteSpecialVoteToKafka 特殊投票
func WriteSpecialVoteToKafka(ctx context.Context, tagId int32, roleInfo *gamePb.RoleInfo) error {

	langType := metadata.GetLangType(ctx)
	startOfDay := common.GetStartOfDay()
	err := sendToAnniversary25thVote(ctx, &pb.UserAnniversaryVoteList{
		VoteList: []*pb.AnniversaryVoteItem{
			{
				VoteId:  cast.ToString(tagId),
				VoteNum: 1,
			},
		},
		VoteType:     2,
		LangType:     langType,
		AreaId:       int32(roleInfo.GetAreaId()),
		DayTimestamp: startOfDay,
	})
	if err != nil {
		return err
	}
	return nil
}

// Anniversary25thVoteFromKafka 2.5周年 从kafka获取投票数据
func Anniversary25thVoteFromKafka(ctx context.Context, userVoteList []*pb.UserAnniversaryVoteList) error {
	for _, v := range userVoteList {
		if v.VoteType == 1 {
			// 普通
			err := normalVotesWrittenGeneralTable(ctx, v)
			if err != nil {
				return err
			}
		}
		// 分区统计
		err := areaCountingVote(ctx, v)
		if err != nil {
			return err
		}
		// 分语言统计
		err = linguisticCountingVote(ctx, v)
		if err != nil {
			return err
		}
	}

	return nil
}

// areaCountingVote 分区计票
func areaCountingVote(ctx context.Context, voteList *pb.UserAnniversaryVoteList) error {
	var err error
	switch voteList.VoteType {
	case 1, 2: // 普通 特殊
		// 获取配置的映射角色
		err = areaUpsertVoteCount(ctx, voteList.AreaId, voteList.VoteType, voteList.GetVoteList())
	default:
		log.WithFieldsContext(ctx, "log_type", "districtCountingVote_err").Errorf(
			"districtCountingVote not case userVote:[%v]", voteList)
	}
	if err != nil {
		return err
	}
	return nil
}

// linguisticCountingVote 分语言计票
func linguisticCountingVote(ctx context.Context, voteList *pb.UserAnniversaryVoteList) error {
	var err error
	switch voteList.VoteType {
	case 1, 2: // 普通 特殊
		// 获取配置的映射角色
		err = languageUpsertVoteCount(ctx, voteList.LangType, voteList.VoteType, voteList.GetVoteList())
	default:
		log.WithFieldsContext(ctx, "log_type", "linguisticCountingVote_err").Errorf(
			"linguisticCountingVote not case userVote:[%v]", voteList)
	}
	if err != nil {
		return err
	}
	return nil
}

func languageUpsertVoteCount(ctx context.Context, lang string, voteType int32, list []*pb.AnniversaryVoteItem) error {

	now := time.Now().Unix()
	votingCountsList := make([]*nikke.Nikke2503LangVotingCount, 0, len(list))
	voteRoleList := config.GetConfig().Nikke2Point5Anniversary.VoteRoleList
	specialVoteMap := config.GetConfig().Nikke2Point5Anniversary.SpecialVoteMap
	for _, v := range list {
		var voteName string
		switch voteType {
		case 1: // 普通
			voteName = voteRoleList[v.VoteId]
		case 2: // 特殊
			voteName = specialVoteMap[v.VoteId]
		default:
			log.WithFieldsContext(ctx, "log_type", "voteType").Errorf(
				"languageUpsertVoteCount not case voteType:[%v]", voteType)
		}
		record := &nikke.Nikke2503LangVotingCount{
			Nikke2503LangVotingCountModel: nikke.Nikke2503LangVotingCountModel{
				AppModel: model.AppModel{
					CreatedAt: now,
					UpdatedAt: now,
				},
			},
			LangType: lang,
			VoteName: voteName,
			VoteType: int16(voteType),
			VoteId:   v.VoteId,
			CountNum: uint64(v.VoteNum),
		}
		votingCountsList = append(votingCountsList, record)
	}
	db := DB.DefaultConnect().WithContext(ctx).Debug().Table(nikke.Nikke2503LangVotingCountModel{}.TableName())
	err := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "lang_type"},
			{Name: "vote_type"},
			{Name: "vote_id"},
		},
		DoUpdates: clause.Assignments(map[string]interface{}{
			"count_num": gorm.Expr(fmt.Sprintf("%s.count_num + VALUES(count_num)",
				nikke.Nikke2503LangVotingCountModel{}.TableName())), // 累加
			"updated_at": now,
		}),
	}).Create(&votingCountsList).Error
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"languageUpsertVoteCount Updates db error, \t [Error]:{%v} ", err)
	}
	return nil
}

func areaUpsertVoteCount(ctx context.Context, areaId int32, voteType int32, list []*pb.AnniversaryVoteItem) error {

	log.WithFieldsContext(ctx, "log_type", "areaUpsertVoteCount_info").Infof(
		"areaUpsertVoteCount start areaId:[%v] voteType:[%v] list:[%v]", areaId, voteType, list)
	voteRoleList := config.GetConfig().Nikke2Point5Anniversary.VoteRoleList
	specialVoteMap := config.GetConfig().Nikke2Point5Anniversary.SpecialVoteMap
	now := time.Now().Unix()
	votingCountsList := make([]*nikke.Nikke2503AreaVotingCount, 0, len(list))
	for _, v := range list {
		var voteName string
		switch voteType {
		case 1: // 普通
			voteName = voteRoleList[v.VoteId]
		case 2: // 特殊
			voteName = specialVoteMap[v.VoteId]
		default:
			log.WithFieldsContext(ctx, "log_type", "voteType").Errorf(
				"areaUpsertVoteCount not case voteType:[%v]", voteType)
		}
		record := &nikke.Nikke2503AreaVotingCount{
			Nikke2503AreaVotingCountModel: nikke.Nikke2503AreaVotingCountModel{
				AppModel: model.AppModel{
					CreatedAt: now,
					UpdatedAt: now,
				},
			},
			AreaId:   cast.ToString(areaId),
			VoteName: voteName,
			VoteType: int16(voteType),
			VoteId:   v.VoteId,
			CountNum: uint64(v.VoteNum),
		}
		votingCountsList = append(votingCountsList, record)
	}
	db := DB.DefaultConnect().WithContext(ctx).Debug()
	err := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "area_id"},
			{Name: "vote_type"},
			{Name: "vote_id"},
		},
		DoUpdates: clause.Assignments(map[string]interface{}{
			"count_num": gorm.Expr(fmt.Sprintf("%s.count_num + VALUES(count_num)",
				nikke.Nikke2503AreaVotingCountModel{}.TableName())), // 累加
			"updated_at": now,
		}),
	}).Create(&votingCountsList).Error
	if err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"areaUpsertVoteCount Updates db error, \t [Error]:{%v} ", err)
	}
	return nil
}

func normalVotesWrittenGeneralTable(ctx context.Context, voteItem *pb.UserAnniversaryVoteList) error {
	for _, v := range voteItem.GetVoteList() {
		condition := map[string]interface{}{
			"vote_id":   v.VoteId,
			"vote_time": voteItem.DayTimestamp,
		}
		updateData := map[string]interface{}{
			"vote_num": gorm.Expr("vote_num + ?", v.VoteNum),
		}
		tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.Nikke2503AllVotingRecordsModel{}.TableName()).Limit(1).
			Where(condition).Updates(updateData)
		if err := tx.Error; err != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"normalVotesWrittenGeneralTable Updates db error, \t [Error]:{%v} ", err)
			continue
		}
		if tx.RowsAffected == 0 {
			errs.NewCustomError(ctx, code.FailToVote, "AddAnniversaryVoteFromKafka Updates num not enough")
			log.WithFieldsContext(ctx, "log_type", "update_vote_err").Errorf(
				"normalVotesWrittenGeneralTable Updates db err:[%v]", v)
			continue
		}
	}
	return nil
}

// CalculateAnniversary2Point5ParticipationCount TODO
// NIKKE 2.5周年定时统计参与活动人数
func CalculateAnniversary2Point5ParticipationCount(ctx context.Context) error {

	var count int64
	// 设置redis锁
	redisKey := getRedisKey("CalculateAnniversary2Point5ParticipationCount-lock")
	result, err := redis.GetClient().Get(ctx, redisKey).Result()
	if err != nil && err != redisOrigin.Nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"CalculateAnniversary25ParticipationCount get redis error, \t [Error]:{%v} ", err)
	}
	// 锁定存在期间只触发一次
	if result != "" {
		return nil
	}

	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.Nikke2503UserParticipationModel{}.TableName()).
		Count(&count).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"CalculateAnniversary2Point5ParticipationCount count db error, \t [Error]:{%v} ", err)
	}
	if count >= 500000 {
		// 触发接口
		botUrl := config.GetConfig().Nikke2Point5Anniversary.BotUrl
		sendMsg := fmt.Sprintf("**参与活动人数已达到: %d 人**", count)
		// 推送企微消息
		if err = SendMarkdownToWxWorkRobot(ctx, botUrl, sendMsg); err != nil {
			return err
		}

		// 设置redis锁
		lookTime := 20 * 24 * time.Hour
		isGetLock, err := redis.GetClient().SetNX(ctx, redisKey, "1", lookTime).Result()
		if err != nil {
			return errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
				"isGetLock db error, \t [Error]:{%v} ", err)
		}
		if !isGetLock {
			return errs.NewCustomError(ctx, code.HasTriggeredInterface,
				"The current interface has been triggered error not get lock skip")
		}
	}
	return nil
}

// TallyAnniversary2Point5thVotesHourly TODO
// NIKKE 2.5周年每小时统计得票数
func TallyAnniversary2Point5thVotesHourly(ctx context.Context) error {

	timestamp := common.GetCurrentTimestamp()
	if timestamp < 1744084810 {
		// 小于 utc*******-04-08 12:00:10 不推送
		return nil
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof("TallyAnniversary2Point5thVotesHourly start")
	anniversaryConf := config.GetConfig().Nikke2Point5Anniversary
	botUrl := anniversaryConf.BotUrl
	voteListConf := anniversaryConf.VoteRoleList
	specialVoteMap := anniversaryConf.SpecialVoteMap

	// 特殊投票 - 计算票数
	voteMap := make(map[string]string)
	var fsourceId string
	var storageKey = "vote"
	switch {
	// 根据活动时间配置 TEST 2025-03-27 00:00:00
	case timestamp > 1744124410 && timestamp <= 1744210810:
		// 第一天
		for k, v := range specialVoteMap {
			if ok := common.StartsWithNumber(k, 1); ok {
				voteMap[k] = v
			}
		}
		fsourceId = "flowGroupV3-34-teshu1"
	case timestamp > 1744210810 && timestamp <= 1744297210:
		// 第二天
		for k, v := range specialVoteMap {
			if ok := common.StartsWithNumber(k, 2); ok {
				voteMap[k] = v
			}
		}
		fsourceId = "flowGroupV3-34-teshu2"
	case timestamp > 1744297210 && timestamp <= 1744383610:
		// 第三天
		for k, v := range specialVoteMap {
			if ok := common.StartsWithNumber(k, 3); ok {
				voteMap[k] = v
			}
		}
		fsourceId = "flowGroupV3-34-teshu3"
	case timestamp > 1744383610 && timestamp <= 1744470010:
		// 第四天
		for k, v := range specialVoteMap {
			if ok := common.StartsWithNumber(k, 4); ok {
				voteMap[k] = v
			}
		}
		fsourceId = "flowGroupV3-34-teshu4"
	case timestamp > 1744470010 && timestamp <= 1744556410:
		// 第五天
		for k, v := range specialVoteMap {
			if ok := common.StartsWithNumber(k, 5); ok {
				voteMap[k] = v
			}
		}
		fsourceId = "flowGroupV3-34-teshu5"
	}

	tableName, err := utilModel.GetTabNameWithGeneral(ctx, (&general.ConfigModel{}).TableName(), fsourceId, storageKey,
		(&general.LogModel{}).TableName(), 100)
	if err != nil {
		return err
	}

	if len(voteMap) != 0 && tableName != "" && fsourceId != "" {
		specialVote, err := CountSpecialVote(ctx, tableName, fsourceId)
		if err != nil {
			errs.NewCustomError(ctx, code.NikkeCountSpecialVoteError,
				"Nikke TallyAnniversary25thVotesHourly Error, tableName:[%v],fsourceId:[%v]", tableName, fsourceId)
		}
		specialVoteStr := make([]string, 0, len(voteMap))
		specialVoteStr = append(specialVoteStr, "**特殊投票:**\n")
		for idx, v := range specialVote {
			if roleName, ok := voteMap[v.StorageTag]; !ok {
				errs.NewCustomError(ctx, code.CurrentRoleNotExist,
					"TallyAnniversary25thVotesHourly Current Role Not Exist, VoteId:[%v]", v.StorageTag)
			} else {
				ranking := idx + 1
				var msg string
				if ranking%4 == 0 {
					msg = fmt.Sprintf("(%d)%s:%d\n", ranking, roleName, v.CountNum)
				} else {
					msg = fmt.Sprintf("(%d)%s:%d\t", ranking, roleName, v.CountNum)
				}
				specialVoteStr = append(specialVoteStr, msg)
			}
		}
		sendMsg := strings.Join(specialVoteStr, "")
		// 推送特殊投票企微消息
		if err = SendMarkdownToWxWorkRobot(ctx, botUrl, sendMsg); err != nil {
			return err
		}
	}

	// 主投票-计算票数
	voteList, err := countAnniversary2Point5thVoteList(ctx, false)
	if err != nil {
		return err
	}
	voteStr := make([]string, 0, len(voteList))
	voteStr = append(voteStr, "**普通投票:**\n")
	for idx, vote := range voteList {
		if roleName, ok := voteListConf[cast.ToString(vote.VoteId)]; !ok {
			return errs.NewCustomError(ctx, code.CurrentRoleNotExist, "Current Role Not Exist, VoteId:[%v]", vote.VoteId)
		} else {
			ranking := idx + 1
			var msg string
			if ranking%4 == 0 {
				msg = fmt.Sprintf("(%d)%s:%d\n", ranking, roleName, vote.VoteNum)
			} else {
				msg = fmt.Sprintf("(%d)%s:%d\t", ranking, roleName, vote.VoteNum)
			}
			voteStr = append(voteStr, msg)
		}
	}
	sendMsg := strings.Join(voteStr, "")
	// 推送主投票企微消息
	if err = SendMarkdownToWxWorkRobot(ctx, botUrl, sendMsg); err != nil {
		return err
	}
	return nil
}

// UpdateDailyVoteTotalsCacheForAnniversary2Point5th TODO
func UpdateDailyVoteTotalsCacheForAnniversary2Point5th(ctx context.Context) error {
	// 调用同步接口,同步数据至redis中并返回数据
	_, err := countAnniversary2Point5thVoteList(ctx, true)
	if err != nil {
		return err
	}
	return nil

}

// Anniversary2Point5thInitializeDatabase TODO
func Anniversary2Point5thInitializeDatabase(ctx context.Context) error {
	var count int64
	if err := DB.DefaultConnect().WithContext(ctx).Table(nikke.Nikke2503AllVotingRecordsModel{}.TableName()).
		Count(&count).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"Anniversary25thInitializeDatabase Count db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		// 已初始化过
		return nil
	}
	// 获取当前时间及以后30天的时间标识
	timeList, err := common.GetTokyoZeroTimestamps(30)
	if err != nil {
		return errs.NewCustomError(ctx, code.ObtainingTimestampListAbnormal,
			"Obtaining the timestamp list is abnormal; err:[%v]", err)
	}

	insertMap := make([]map[string]interface{}, 0, len(timeList))
	// nikkeId 1-120
	for voteId := 1; voteId <= 120; voteId++ {
		for _, dayTime := range timeList {
			insertMap = append(insertMap, map[string]interface{}{
				"vote_time": dayTime,
				"vote_id":   voteId,
			})
		}
	}
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.Nikke2503AllVotingRecordsModel{}.TableName()).
		Create(insertMap).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"Anniversary25thInitializeDatabase Create db error, \t [Error]:{%v} ", err)
	}
	return nil
}

// MovedForwardOneDay2Point5Anniversary 投票数据前移一天
func MovedForwardOneDay2Point5Anniversary(ctx context.Context, uid string) error {

	if trpc.GlobalConfig().Global.EnvName == "prod" {
		return nil
	}

	// 修改投票数据时间
	updateData := map[string]interface{}{
		"created_at": gorm.Expr("created_at - ?", 86400),
	}
	if err := DB.DefaultConnect().WithContext(ctx).Table(nikke.Nikke2503UserVotingHistoryModel{}.TableName()).
		Where("uid = ?", uid).Updates(updateData).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"MovedForwardOneDay2Point5Anniversary Updates db error, \t openId:[%v],[Error]:{%v}", uid, err)
	}
	return nil
}

// RefreshingNumVote2Point5Anniversary TODO
func RefreshingNumVote2Point5Anniversary(ctx context.Context) error {
	_, err := countAnniversary2Point5thVoteList(ctx, true)
	if err != nil {
		return err
	}
	return nil
}
