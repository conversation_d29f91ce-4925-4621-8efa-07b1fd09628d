package dao

import (
	"errors"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

func CreateChWorkPost(intlOpenid string, PostUid string, workId int64, chUid string) error {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChWorkPost{}).TableName())
	err := tx.Create(&model.ChWorkPost{
		IntlOpenid: intlOpenid,
		PostUid:    PostUid,
		WorkId:     workId,
		ChUid:      chUid,
	}).Error
	return err
}

func IsWorkIdExisted(workId int64) bool {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChWorkPost{}).TableName())
	workPost := &model.ChWorkPost{}
	err := tx.Model(&model.ChWorkPost{}).Where("work_id = ?", workId).First(&workPost).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return false
	}
	return true
}

func GetWorkPostsByWorkIds(workIds []int64) ([]*model.ChWorkPost, error) {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChWorkPost{}).TableName())
	workPosts := make([]*model.ChWorkPost, 0)
	err := tx.Model(&model.ChWorkPost{}).Where("work_id in ?", workIds).Find(&workPosts).Error
	return workPosts, err
}

func GetWorkPostInfoByIntlOpenidAndWorkId(intlOpenid string, workId int64) (*model.ChWorkPost, error) {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChWorkPost{}).TableName())
	workPost := &model.ChWorkPost{}
	err := tx.Model(&model.ChWorkPost{}).Where("intl_openid = ? AND work_id = ?", intlOpenid, workId).Find(&workPost).Error
	return workPost, err
}
