package nikketmp

import (
	"context"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
)

// CheckUserGiftPackReceived nikke通用 根据礼包id判断当前用户是否已获取当前礼包
func CheckUserGiftPackReceived(ctx context.Context, tableName, presentId string, hasSent bool) (bool, error) {

	var exist bool
	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return exist, err
	}
	if presentId == "" || tableName == "" {
		return exist, errs.NewCustomError(ctx, code.NikkeParameterMissing,
			"CheckUserGiftPackReceived NikkeParameterMissing, \t [presentId]:{%v},[tableName]:{%v} ", presentId, tableName)
	}

	// 是否已获取礼包
	tx := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where("uid = ? and account_type = ?", userAccount.Uid, int(userAccount.AccountType)).
		Where("present_id = ?", presentId)
	if hasSent {
		tx = tx.Where("status = ?", 1)
	}
	var count int64
	if err = tx.Count(&count).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return exist, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"CheckUserGiftPackReceived Count db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		exist = true
		return exist, nil
	}
	return exist, nil
}
