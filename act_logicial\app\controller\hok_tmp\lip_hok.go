package hoktmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_hok_tmp"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/logic/hoktmp"
	"trpc.act.logicial/app/logic/lip"
)

// RedDotLogicalJudgment 红点逻辑判断
func (s *HokTmpImpl) RedDotLogicalJudgment(ctx context.Context, req *pb.RedDotLogicalJudgmentReq) (
	*pb.RedDotLogicalJudgmentRsp, error) {

	logicalJudgment, err := hoktmp.RedDotLogicalJudgment(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.RedDotLogicalJudgmentRsp{
		HasUnclaimedPoints: logicalJudgment,
	}, nil
}

// ClaimSingleTaskPointRewards 单个任务领取
func (s *HokTmpImpl) ClaimSingleTaskPointRewards(ctx context.Context, req *pb.ClaimSingleTaskPointRewardsReq) (
	*pb.ClaimSingleTaskPointRewardsRsp, error) {

	singleTaskPointRewards, err := hoktmp.ClaimSingleTaskPointRewards(ctx, req.GetTaskUuid(), req.GetStartStopTime())
	if err != nil {
		return nil, err
	}
	return &pb.ClaimSingleTaskPointRewardsRsp{
		Tasks: singleTaskPointRewards,
	}, nil
}

// BatchGetHOKMetricsData [HOK集群] 批量获取指标数据
func (s *HokTmpImpl) BatchGetHOKMetricsData(ctx context.Context, req *pb.BatchGetHOKMetricsDataReq) (
	*pb.BatchGetHOKMetricsDataRsp, error) {
	dailyMetrics, err := hoktmp.BatchGetRangeTimeMetricsData(ctx, req.Uid, req.GetMetricsList())
	if err != nil {
		return nil, err
	}
	dataInfos := make([]*pb.DataInfo, 0, len(dailyMetrics))
	for _, v := range dailyMetrics {
		dataInfos = append(dataInfos, &pb.DataInfo{
			Code: v.Code,
			Msg:  v.Msg,
			Target: &pb.TargetInfo{
				TargetID: v.GetTarget().GetTargetID(),
				StartDay: v.GetTarget().GetStartDay(),
				EndDay:   v.GetTarget().GetEndDay(),
			},
			TargetValue: v.GetTargetValue(),
		})
	}
	return &pb.BatchGetHOKMetricsDataRsp{
		MetricsDataList: dataInfos,
	}, nil
}

// GetLipBandingMetricsTaskStatus LIP集群-获取指标任务状态
func (s *HokTmpImpl) GetLipBandingMetricsTaskStatus(ctx context.Context, req *pb.GetLipBandingMetricsTaskStatusReq) (
	*pb.GetLipBandingMetricsTaskStatusRsp, error) {

	metricsTaskStatus, err := hoktmp.GetLipBandingMetricsTaskStatus(ctx, req)
	if err != nil {
		return nil, err
	}
	return metricsTaskStatus, nil
}

// LIPBandingHokClaimAllTaskPointRewards 一键领取任务积分奖励
func (s *HokTmpImpl) LIPBandingHokClaimAllTaskPointRewards(ctx context.Context, req *pb.LIPBandingHokClaimAllTaskPointRewardsReq) (
	*pb.LIPBandingHokClaimAllTaskPointRewardsRsp, error) {

	pointRewards, err := hoktmp.ClaimAllTaskPointRewards(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.LIPBandingHokClaimAllTaskPointRewardsRsp{
		Tasks: pointRewards,
	}, nil
}

// GetCompletedTeamMatchesInRange 范围时间内完成的组队对局数量
func (s *HokTmpImpl) GetCompletedTeamMatchesInRange(ctx context.Context, req *pb.GetCompletedTeamMatchesInRangeReq) (
	*pb.GetCompletedTeamMatchesInRangeRsp, error) {
	matchesInRange, err := hoktmp.GetCompletedTeamMatchesInRange(ctx, req.GetTimeRange())
	if err != nil {
		return nil, err
	}
	if len(matchesInRange) != 1 {
		return nil, errs.NewCustomError(ctx, code.HokTmpGetDataGroupMetricsError,
			"The number of indicators returned is abnormal")
	}
	playNum := matchesInRange[0].TargetValue
	return &pb.GetCompletedTeamMatchesInRangeRsp{
		CompletedMatchesThisWeek: cast.ToInt32(playNum),
	}, nil
}

// BatchGetTaskStatusByTaskUUIDs 根据taskUUID批量获取积分任务状态
func (s *HokTmpImpl) BatchGetTaskStatusByTaskUUIDs(ctx context.Context, req *pb.BatchGetTaskStatusByTaskUUIDsReq) (
	*pb.BatchGetTaskStatusByTaskUUIDsRsp, error) {

	account, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "BatchGetTaskStatusByTaskUUIDs get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}

	taskPoints := &lip.TaskPoints{
		TaskUuids:    req.TaskUuidList,
		IntegralType: req.TaskType,
		LipOpenid:    account.IntlAccount.OpenId,
		IntlGameId:   cast.ToInt64(req.GameId),
	}
	taskCompletionStatus, err := taskPoints.QueryTaskCompletionStatus(ctx)
	if err != nil {
		return nil, err
	}
	taskStatusItems := make([]*pb.TaskStatusItem, 0, len(taskCompletionStatus))
	for _, v := range taskCompletionStatus {
		taskStatusItems = append(taskStatusItems, &pb.TaskStatusItem{
			TaskUuid: v.TaskUuid,
			Status:   v.Status,
		})
	}
	return &pb.BatchGetTaskStatusByTaskUUIDsRsp{
		Tasks: taskStatusItems,
	}, nil
}

//// LIPGetLipBandingHokDailyMetrics LIP集群调用 获取每日任务指标
//func (s *HokTmpImpl) LIPGetLipBandingHokDailyMetrics(ctx context.Context, req *pb.GetLipBandingHokDailyMetricsReq) (
//	*pb.GetLipBandingHokDailyMetricsRsp, error) {
//
//}
//
//// LIPGetCompletedTeamMatchesInRange LIP集群调用 范围时间内完成的组队对局数量
//func (s *HokTmpImpl) LIPGetCompletedTeamMatchesInRange(ctx context.Context, req *pb.GetCompletedTeamMatchesInRangeReq) (
//	*pb.GetCompletedTeamMatchesInRangeRsp, error) {
//
//}
