package tweet

import (
	"context"
	"math"
	"strconv"
	"sync"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

func CommentHotPostEventExec() {
	c := context.Background()
	defer recovery.CatchGoroutinePanic(context.Background())
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CommentHotPostEventExec ticker at %v", time.Now())
	// 通过redis，先判断定时任务是否还在运行中
	commentHotCalculationTask := cache.GetCommentHotCalculationTaskKey()
	if ok, _ := redis.GetClient().SetNX(c, commentHotCalculationTask, 1, time.Duration(config.GetConfig().Dynamic.HotPostEventTimeoutDuration)*time.Second).Result(); ok {
		defer redis.GetClient().Del(context.Background(), commentHotCalculationTask)
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CommentHotPostEventExec start at %v", time.Now())

		var total int64 = 100
		commentHotCalculationUUIDsKey := cache.GetCommentHotCalculationUUIDKey()
		for {
			resultData := redis.GetClient().SPopN(context.Background(), commentHotCalculationUUIDsKey, total)
			commentUUIDs, err := resultData.Result()
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CommentHotPostEventExec to read list err: %v", err)
				_ = errs.NewCustomError(context.Background(), code.CommentHotUUIDsGetCacheFailed, "CommentHotPostEventExec | Comment Hot get cache failed")
			}
			// 如果是没有数据那就退出循环
			if len(commentUUIDs) == 0 {
				return
			}

			var wg sync.WaitGroup
			// 按照每组100个值将数组分成多个小数组
			batchSize := 10
			for i := 0; i < len(commentUUIDs); i += batchSize {
				end := i + batchSize
				if end > len(commentUUIDs) {
					end = len(commentUUIDs)
				}
				batchCommentUUIDs := commentUUIDs[i:end]
				wg.Add(1) // 增加 WaitGroup 的计数器
				go func(commentUUIDList []string) {
					defer recovery.CatchGoroutinePanic(context.Background())
					defer wg.Done() // 函数结束时减少计数器
					commentStates, err := dao.GetCommentStatesByCommentUUids(commentUUIDList)
					if err != nil {
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CommentHotPostEventExec GetCommentStatesByCommentUUids err: %v", err)
						return
					}
					for _, commentStateItem := range commentStates {
						hotNum := CalculatingCommentHotNum(commentStateItem)
						doc := map[string]interface{}{
							"hot_num": hotNum,
						}
						// 更新 mysql 数据
						err = dao.UpdateComment(c, commentStateItem.CommentUUID, doc)
						if err != nil {
							log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CommentHotPostEventExec UpdateComment err: %v, commentuuid: %s, doc: %v", err, commentStateItem.CommentUUID, doc)
							return
						}
						dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, commentStateItem.CommentUUID, doc)
					}
				}(batchCommentUUIDs)
			}
			// 等待所有 goroutine 完成
			wg.Wait()
			time.Sleep(1 * time.Second)
			// 如果是读取出来的用户数量小于定义的数量那就是读取完了
			if len(commentUUIDs) < int(total) {
				break
			}
		}

		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("HotPostEventExec end at %v", time.Now())
	}

}

func CalculatingCommentHotNum(commentStats *model.CommentState) int64 {
	if commentStats == nil {
		return 0
	}
	// 计算热度
	// 初始权重、是否是白名单用户、是否拥有称号
	// initialValue := post.PowerNum
	var initialValue float64 = calculatingInitialCommentHotValue(commentStats.CommentUUID)

	// 互动热度
	interactiveHeat := float64(initialValue)*0.7 + (float64(commentStats.UpvoteCount)*3+float64(commentStats.ReplyCount)*7)*0.3

	// 设置缓存
	if interactiveHeat < 0 {
		interactiveHeat = 0
	}
	return int64(math.Ceil(interactiveHeat))
}

/*
综合分 = 0.7 * 基础质量分 + 0.3 * 热度分

基础质量分
文本长度：
文本字数 (0,10)  ：低质评论 = 字数 * 0.2
字数 > 10 ：基础分 = 2，每增加 10 字 + 5 （上限 30）{min（2 + (count-10) % 10 +1),30)
公式待这个整理
图片内容：包含图片 ：基础分+ 5
纯表情惩罚：纯表情符号 基础分 -20

热度分
点赞权重 0.3，回复权重 0.7（评论互动更难得，但是后面要注意节奏骂战的评论的问题）
heat_score = 3 * likes + 7 * comments
*/
func calculatingInitialCommentHotValue(commentUUid string) float64 {
	var initialValue float64
	if commentUUid == "" {
		return 0
	}
	// 缓存中获取
	redisKey := cache.GetCommentInitialHotValueKey(commentUUid)
	cacheValue, err := redis.GetClient().Get(context.Background(), redisKey).Result()
	// 缓存中获取成功，直接返回
	if err == nil && cacheValue != "" {
		initialValue, _ = strconv.ParseFloat(cacheValue, 64)
		return initialValue
	}

	commentContent, err := dao.GetCommentInfoByUUID(commentUUid)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log type", constants.LogType_Standalonesite).Errorf("calculatingInitialCommentHotValue GetCommentInfoByUUID err, comment_uuid:(%s), err=(%v)", commentUUid, err)
	} else {
		if commentContent.Content != "" {
			initialValue = calculatingInitialCommentContentScore(commentContent)
		}
	}
	// 设置缓存
	if initialValue < 0 {
		initialValue = 0
	}
	redis.GetClient().Set(context.Background(), redisKey, strconv.FormatFloat(initialValue, 'f', 6, 64), 24*time.Hour)
	return initialValue
}

func calculatingInitialCommentContentScore(commentContent *model.CommentContent) float64 {
	var initialValue float64
	contentLen, imgLen := calculatingContentLen(commentContent.Content)
	// 如果是纯表情
	if contentLen == 0 && imgLen == 0 {
		initialValue = -20
	} else {
		if contentLen >= 0 && contentLen <= 10 {
			initialValue += float64(contentLen) * 0.2
		} else if contentLen > 10 && contentLen <= 70 {
			initialValue += 2 + float64((contentLen-10)/10*5)
		} else if contentLen > 70 {
			initialValue += 30
		}

		if len(commentContent.PicUrls) > 0 {
			initialValue += 5
		}
	}
	// 设置缓存
	if initialValue < 0 {
		initialValue = 0
	}
	return initialValue
}

// CalculatingInitCommentHotNum 只限定于初始化使用的函数
func CalculatingInitCommentHotNum(commentContent *model.CommentContent) int64 {
	if commentContent == nil {
		return 0
	}
	// 计算热度
	// 初始权重、是否是白名单用户、是否拥有称号
	var initialValue float64 = calculatingInitialCommentContentScore(commentContent)

	// 互动热度
	interactiveHeat := float64(initialValue) * 0.7

	return int64(math.Ceil(interactiveHeat))
}
