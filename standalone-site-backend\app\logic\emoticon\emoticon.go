package emoticon

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
)

// func GetEmoticonGroupList(c context.Context, req *pb.GetEmoticonGroupListReq, gameId, areaId string) (*pb.GetEmoticonGroupListRsp, error) {
// 	rsp := &pb.GetEmoticonGroupListRsp{
// 		List:     make([]*pb.EmoticonGroupItem, 0),
// 		PageInfo: &pb.PageInfo{},
// 	}
// 	var err error
// 	var emoticonGroup []*model.EmoticonGroup
// 	var nextPageCursor, previousPageCursor string
// 	conditions := &model.ConditionsT{
// 		"status = ?":  2,
// 		"game_id = ?": gameId,
// 		"area_id = ?": areaId,
// 		"ORDER":       "id desc",
// 	}
// 	// 查询类型：下一页数据
// 	if req.PageType == pb.PageType_NEXTPAGE {
// 		var idCursor int64
// 		// 如果是首页
// 		if req.NextPageCursor == "" {
// 			idCursor = 0
// 		} else {
// 			idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
// 			if err != nil {
// 				return nil, errs.NewCustomError(c, code.GetEmoticonListError, "GetEmoticonGroupList | Failed to get idCursor")
// 			}
// 			(*conditions)["id > ?"] = idCursor
// 		}
// 		emoticonGroup, err = dao.GetEmoticonGroupList(conditions, int(req.Limit))
// 		// 生成下一页的游标
// 		if len(emoticonGroup) > 0 {
// 			nextPageCursor, err = util.EncryptPageCursorI(emoticonGroup[len(emoticonGroup)-1].ID)
// 			if err != nil {
// 				return nil, errs.NewCustomError(c, code.GetEmoticonListError, "GetEmoticonGroupList | Failed to create comments nextPageCursor")
// 			}
// 		}
// 		if len(emoticonGroup) == 0 {
// 			return rsp, nil
// 		}
// 	}

// 	groupIds := make([]int64, 0, len(emoticonGroup))

// 	for _, item := range emoticonGroup {
// 		groupIds = append(groupIds, item.ID)
// 	}
// 	// 获取icon关联关系
// 	relations, err := dao.GetEmoticonGroupRelation(&model.ConditionsT{
// 		"group_id in ?": groupIds,
// 	}, 0)
// 	if err != nil {
// 		return nil, errs.NewCustomError(c, code.GetEmoticonIconGroupRelationError, "GetEmoticonGroupList | Failed to get emoticon icon relation list")
// 	}
// 	iconIds := make([]int64, 0, len(relations))
// 	for _, relation := range relations {
// 		iconIds = append(iconIds, int64(relation.IconID))
// 	}
// 	if len(iconIds) == 0 {
// 		return rsp, nil
// 	}
// 	// 获取表情包列表
// 	list, err := dao.GetEmoticonIconList(iconIds)
// 	if err != nil {
// 		return nil, errs.NewCustomError(c, code.GetEmoticonListError, "GetEmoticonIconList | Failed to get emoticon icon list")
// 	}
// 	for _, icon := range list {
// 		rsp.List = append(rsp.List, &pb.EmoticonGroupItem{
// 			Id:     icon.ID,
// 			PicUrl: icon.Icon,
// 		})
// 	}

// 	if len(rsp.List) == 0 || len(rsp.List) < int(req.Limit) {
// 		rsp.PageInfo.IsFinish = true
// 	} else {
// 		rsp.PageInfo.NextPageCursor = nextPageCursor
// 	}
// 	rsp.PageInfo.PreviousPageCursor = previousPageCursor
// 	return rsp, nil
// }

// func GetEmoticonIconList(c context.Context, req *pb.GetEmoticonIconListReq, gameId, areaId string) (*pb.GetEmoticonIconListRsp, error) {
// 	rsp := &pb.GetEmoticonIconListRsp{
// 		List:     make([]string, 0),
// 		PageInfo: &pb.PageInfo{},
// 	}
// 	var err error
// 	var emoticonGroup []*model.EmoticonIconGroup
// 	var nextPageCursor, previousPageCursor string
// 	conditions := &model.ConditionsT{
// 		"group_id = ?": req.GroupId,
// 		"ORDER":        "id desc",
// 	}
// 	// 查询类型：下一页数据
// 	if req.PageType == pb.PageType_NEXTPAGE {
// 		var idCursor int64
// 		// 如果是首页
// 		if req.NextPageCursor == "" {
// 			idCursor = 0
// 		} else {
// 			previousPageCursor = req.NextPageCursor
// 			idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
// 			if err != nil {
// 				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetEmoticonIconList | Failed to get idCursor")
// 			}
// 			(*conditions)["id > ?"] = idCursor
// 		}
// 		emoticonGroup, err = dao.GetEmoticonGroupRelation(conditions, int(req.Limit))
// 		// 生成下一页的游标
// 		if len(emoticonGroup) > 0 {
// 			nextPageCursor, err = util.EncryptPageCursorI(emoticonGroup[len(emoticonGroup)-1].ID)
// 			if err != nil {
// 				return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetEmoticonIconList | Failed to create comments nextPageCursor")
// 			}
// 		}
// 		if len(emoticonGroup) == 0 {
// 			return rsp, nil
// 		}
// 	}
// 	var iconIds = make([]int64, 0, len(emoticonGroup))
// 	for _, item := range emoticonGroup {
// 		iconIds = append(iconIds, int64(item.IconID))
// 	}
// 	list, err := dao.GetEmoticonIconList(iconIds)
// 	if err != nil {
// 		return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetEmoticonIconList | Failed to get emoticon icon list")
// 	}
// 	for _, icon := range list {
// 		rsp.List = append(rsp.List, icon.Icon)
// 	}
// 	if len(rsp.List) == 0 || len(rsp.List) < int(req.Limit) {
// 		rsp.PageInfo.IsFinish = true
// 	} else {
// 		rsp.PageInfo.NextPageCursor = nextPageCursor
// 	}
// 	rsp.PageInfo.PreviousPageCursor = previousPageCursor
// 	return rsp, nil
// }

func GetAllEmoticonList(c context.Context, req *pb.GetAllEmoticonsReq) (*pb.GetAllEmoticonsRsp, error) {
	rsp := &pb.GetAllEmoticonsRsp{
		List:     make([]*pb.EmoticonGroupItem, 0),
		PageInfo: &pb.PageInfo{},
	}
	// 先从缓存中获取
	allEmoticonListCacheKey := cache.GetAllEmoticonCacheKey()
	cacheStr, err := redis.GetClient().Get(c, allEmoticonListCacheKey).Result()
	if err == nil && cacheStr != "" {
		rsp.List = make([]*pb.EmoticonGroupItem, 0)
		err = json.Unmarshal([]byte(cacheStr), &rsp.List)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetAllEmoticonList | Failed to unmarshal emoticon list")
		}
		return rsp, nil
	}
	emoticonGroups, err := dao.GetAllEmoticonInfo()
	if err != nil {
		return nil, errs.NewCustomError(c, code.GetIndexPostFailed, "GetAllEmoticonList | Failed to get emoticon icon list")
	}

	if len(emoticonGroups) == 0 {
		return rsp, nil
	}

	for _, emoticonGroup := range emoticonGroups {
		emoticonGroupItem := &pb.EmoticonGroupItem{
			Id:       emoticonGroup.ID,
			Name:     emoticonGroup.Name,
			PicUrl:   emoticonGroup.PicURL,
			IconList: make([]*pb.EmoticonIconItem, 0),
		}
		for _, emoticonIconInfo := range emoticonGroup.EmoticonIconInfos {
			emoticonGroupItem.IconList = append(emoticonGroupItem.IconList, &pb.EmoticonIconItem{
				Id:     emoticonIconInfo.ID,
				Name:   emoticonIconInfo.Name,
				PicUrl: emoticonIconInfo.Icon,
			})
		}
		rsp.List = append(rsp.List, emoticonGroupItem)
	}

	emoticonStr, err := json.Marshal(rsp.List)
	if err == nil {
		redis.GetClient().Set(c, allEmoticonListCacheKey, string(emoticonStr), 5*time.Minute)
	}
	return rsp, nil
}

func RemoveEmoticonCache(c context.Context) {
	defer recovery.CatchGoroutinePanic(c)
	allEmoticonListCacheKey := cache.GetAllEmoticonCacheKey()
	redis.GetClient().Del(c, allEmoticonListCacheKey)
	return
}
