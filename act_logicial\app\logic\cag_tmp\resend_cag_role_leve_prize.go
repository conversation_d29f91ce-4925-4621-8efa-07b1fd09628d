package cag_tmp

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_cag_tmp"
	"trpc.act.logicial/app/task/cron"
)

// ResendCagRoleLevePrize cag角色等级奖励重试发放
func ResendCagRoleLevePrize(ctx context.Context, req *pb.ResendCagRoleLevePrizeReq) (*pb.ResendCagRoleLevePrizeRsp, error) {
	log.DebugContextf(ctx, "ResendCagRoleLevePrize enter, req: %v", req)
	go func() { // 启动一个协程来处理
		cron.CagRoleLevelPrizeTask(context.Background())
	}()
	rsp := &pb.ResendCagRoleLevePrizeRsp{}
	return rsp, nil
}
