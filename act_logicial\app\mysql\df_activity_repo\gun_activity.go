package df_activity_repo

import (
	"context"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.act.logicial/app/model/df_activity"
)

var GunActivityRepoClient = GunActivityRepo{}

type GunActivityRepo struct {
}

// GetGunActivityConf 获取拼枪活动配置
func (p GunActivityRepo) GetGunActivityConf(ctx context.Context,
	limit int) (rlt []*df_activity.GunActivityConf, err error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityConf{}).
		Where("status = ?", df_activity.ActivityStatusOK)

	query.Limit(limit)
	err = query.Find(&rlt).Error
	return
}

// GetUserTaskNumByGunId 根据gun_id获取用户任务数
func (p GunActivityRepo) GetUserTaskNumByGunId(ctx context.Context, gameId, activityId,
	userId, gunId string, notInStatus []int) (count int64, err error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTask{}).
		Where("activity_id = ?", activityId).Where("user_id = ?", userId).
		Where("game_id = ?", gameId).Where("gun_id = ?", gunId)
	if len(notInStatus) > 0 {
		query = query.Where("status not in ?", notInStatus)
	}

	result := query.Count(&count)

	if result.Error != nil {
		log.ErrorContextf(ctx, "GetUserTaskNumByGunId error, gameID: %s, "+
			"activityID: %s, userID: %s, gun_id: %v, err: %v", gameId,
			activityId, userId, gunId, result.Error)
		return 0, result.Error
	}
	return
}

// AddUserTask 添加用户任务
func (p GunActivityRepo) AddUserTask(ctx context.Context,
	task *df_activity.GunActivityTask) error {
	result := DB.DefaultConnect().WithContext(ctx).Create(task)
	if result.Error != nil {
		log.ErrorContextf(ctx, "AddUserTask error, gameID: %s, "+
			"activityID: %s, userID: %s, taskId: %v, err: %v", task.GameId,
			task.ActivityId, task.UserId, task.TaskId, result.Error)
		return result.Error
	}
	return nil
}

// GetTaskByTaskId 根据任务id获取任务
func (p GunActivityRepo) GetTaskByTaskId(ctx context.Context,
	taskId string) (rlt []*df_activity.GunActivityTask, err error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTask{}).
		Where("task_id = ?", taskId)
	err = query.Find(&rlt).Error
	if nil != err {
		log.ErrorContextf(ctx, "GetTaskByTaskId error, taskId: %s, err: %v", taskId, err)
		return nil, err
	}
	return
}

// GetExpireTasks 获取超时过期的任务
func (p GunActivityRepo) GetExpireTasks(ctx context.Context,
	gameId, activityId string, status int, expireTime int64, limit int) (rlt []*df_activity.GunActivityTask, err error) {
	//expire := time.Unix(time.Now().Unix()-expireTime, 0)
	query := DB.DefaultConnect().Debug().WithContext(ctx).Model(&df_activity.GunActivityTask{}).
		Where("game_id = ?", gameId).Where("activity_id = ?", activityId).
		Where("status = ?", status).Where("create_time < from_unixtime(unix_timestamp()-?)", expireTime).Limit(limit)

	err = query.Find(&rlt).Error
	if nil != err {
		log.ErrorContextf(ctx, "GetExpireTasks error, expireTime: %v, err: %v", expireTime, err)
		return nil, err
	}
	return
}

// GetSendPrizeFailTasks 获取发奖失败的任务
func (p GunActivityRepo) GetSendPrizeFailTasks(ctx context.Context,
	gameId, activityId string, status int, maxSendTimes, limit int) (rlt []*df_activity.GunActivityTask, err error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTask{}).
		Where("game_id = ?", gameId).Where("activity_id = ?", activityId).
		Where("status = ?", status).Where("send_prize_times <= ?", maxSendTimes).Limit(limit)
	err = query.Find(&rlt).Error
	if nil != err {
		log.ErrorContextf(ctx, "GetSendPrizeFailTasks error, err: %v", err)
		return nil, err
	}
	return
}

// CancelTask 取消任务
func (p GunActivityRepo) CancelTask(ctx context.Context,
	taskId string) (affectRows int64, err error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTask{}).
		Where("task_id = ?", taskId).
		Where("status = ?", df_activity.GunTaskStatusProcessing) // 只能取消正在助力中的任务

	// 根据条件更新字段
	updates := map[string]interface{}{
		"status": df_activity.GunTaskStatusCancel,
	}
	res := query.Updates(updates)
	if res.Error != nil {
		log.ErrorContextf(ctx, "CancelTask error, taskID: %s, err: %v", taskId, res.Error)
		return 0, res.Error
	}
	return res.RowsAffected, nil
}

// GetTasksByUser 根据用户获取任务
func (p GunActivityRepo) GetTasksByUser(ctx context.Context,
	gameId, activityId, userId, gunId string, limit int) (rlt []*df_activity.GunActivityTask, err error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTask{}).
		Where("user_id = ?", userId).
		Where("game_id = ?", gameId).
		Where("activity_id = ?", activityId).
		Where("status != ?", df_activity.GunTaskStatusCancel)
	if len(gunId) > 0 {
		query = query.Where("gun_id = ?", gunId)
	}
	query.Order("create_time desc").Limit(limit)
	err = query.Find(&rlt).Error
	if nil != err {
		log.ErrorContextf(ctx, "GetTasksByUser error, userId: %s, gunId: %v err: %v",
			userId, gunId, err)
		return nil, err
	}
	return
}

// AddTaskHelpRecord 添加用户助力记录
func (p GunActivityRepo) AddTaskHelpRecord(ctx context.Context,
	taskHelp *df_activity.GunActivityTaskHelp) error {
	result := DB.DefaultConnect().WithContext(ctx).Create(taskHelp)
	if result.Error != nil {
		log.ErrorContextf(ctx, "AddTaskHelpRecord error, gameID: %s, "+
			"activityID: %s, userID: %s, taskId: %v, err: %v", taskHelp.GameId,
			taskHelp.ActivityId, taskHelp.UserId, taskHelp.TaskId, result.Error)
		return result.Error
	}
	return nil
}

// GetTaskHelpNum 查询助力数量
func (p GunActivityRepo) GetTaskHelpNum(ctx context.Context, taskId string,
	status int) (count int64, err error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTaskHelp{}).
		Where("task_id = ?", taskId).Where("status = ?", status)

	result := query.Count(&count)

	if result.Error != nil {
		log.ErrorContextf(ctx, "GetTaskHelpNum error, taskId: %v, err: %v", taskId, result.Error)
		return 0, result.Error
	}
	return
}

// GetTaskHelpUsersByTaskId 根据任务id获取助力用户列表
func (p GunActivityRepo) GetTaskHelpUsersByTaskId(ctx context.Context,
	taskId string) (rlt []*df_activity.GunActivityTaskHelp, err error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTaskHelp{}).
		Where("task_id = ?", taskId)
	err = query.Find(&rlt).Error
	if nil != err {
		log.ErrorContextf(ctx, "GetTaskHelpUsersByTaskId error, taskId: %s, err: %v", taskId, err)
		return nil, err
	}
	return
}

// UpdateTaskStatus 更新任务状态
func (p GunActivityRepo) UpdateTaskStatus(ctx context.Context, taskId string,
	status int, originalStatus int) error {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTask{}).
		Where("task_id = ?", taskId).Where("status = ?", originalStatus)

	// 根据条件更新字段
	updates := map[string]interface{}{
		"status": status,
	}

	res := query.Updates(updates)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateTaskStatus error, taskId: %s, err: %v", taskId, res.Error)
		return res.Error
	}
	return nil
}

// UpdateTaskHasPrizeFlag 设置奖励已经发放标志
func (p GunActivityRepo) UpdateTaskHasPrizeFlag(ctx context.Context, taskId string,
	flag int) error {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTask{}).
		Where("task_id = ?", taskId)

	// 根据条件更新字段
	updates := map[string]interface{}{
		"has_prize_flag": flag,
	}

	res := query.Updates(updates)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateTaskHasPrizeFlag error, taskId: %s, err: %v", taskId, res.Error)
		return res.Error
	}
	return nil
}

// UpdateTaskSendPrizeTimes 更新任务发奖次数
func (p GunActivityRepo) UpdateTaskSendPrizeTimes(ctx context.Context, taskId string,
	times uint32) error {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTask{}).
		Where("task_id = ?", taskId)

	// 根据条件更新字段
	updates := map[string]interface{}{
		"send_prize_times": times,
	}

	res := query.Updates(updates)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateTaskSendPrizeTimes error, taskId: %s, err: %v", taskId, res.Error)
		return res.Error
	}
	return nil
}

// GetTaskHelpNumByUser 获取用户助力数量
func (p GunActivityRepo) GetTaskHelpNumByUser(ctx context.Context, gameId, activityId,
	userId string, helpType uint32) (count int64, err error) {
	query := DB.DefaultConnect().WithContext(ctx).Model(&df_activity.GunActivityTaskHelp{}).
		Where("activity_id = ?", activityId).Where("user_id = ?", userId).
		Where("game_id = ?", gameId).Where("status = ?", 0).Where("help_type = ?", helpType)

	result := query.Count(&count)

	if result.Error != nil {
		log.ErrorContextf(ctx, "GetTaskHelpNumByUser error, gameID: %s, "+
			"activityID: %s, userID: %s,  err: %v", gameId,
			activityId, userId, result.Error)
		return 0, result.Error
	}
	return
}
