package comment

import (
	"context"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	redis2 "github.com/go-redis/redis/v8"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/kafka"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

// 同步评论下的用户语言
func SyncPostCommentLanguage(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("sync post comment language start, at: %d", time.Now().Unix())
	// 获取用户审核表数据
	var limit, index, ltId = 100, 0, 0
	var userInfo = make(map[string]string)
	for {
		index++
		list, err := dao.CommentList(&dao.CommentListConditions{
			Order: []*dao.OrderConditions{
				&dao.OrderConditions{
					Column: "id",
					IsDesc: true,
				},
			},
			IgnoreDel: true,
			LtId:      int64(ltId),
		}, limit)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostCommentLanguage | get post comment list failed, ltId: %d, limit: %d, err:%v", ltId, limit, err)
			break
		}
		var userIntlOpenids = make([]string, 0)
		for _, comment := range list {
			if _, ok := userInfo[comment.IntlOpenid]; ok {
				continue
			}
			userIntlOpenids = append(userIntlOpenids, comment.IntlOpenid)
		}

		if len(userIntlOpenids) == 0 {
			continue
		}

		userList, err := dao.GetUserListByOpenidV2(userIntlOpenids, true)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostCommentLanguage | get user list failed, intl_openids: %v, err:%v", userIntlOpenids, err)
			continue
		}
		for _, content := range userList {
			userInfo[content.IntlOpenid] = content.Language
		}
		var updateData = make(map[string][]string)
		for _, comment := range list {
			if language, ok := userInfo[comment.IntlOpenid]; ok {
				updateData[language] = append(updateData[language], comment.CommentUUID)
			}
		}
		err = dao.BatchUpdateCommentLanguage(updateData)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostCommentLanguage | update post comment language failed, update_data: %+v, err:%v", updateData, err)
			continue
		}

		for lang, commentUuids := range updateData {
			for _, uuid := range commentUuids {
				_, err = dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, uuid, map[string]interface{}{
					"language": lang,
				})
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("sync comment audit language es failed, comment_openids: %v,err: %v", commentUuids, err)
				}
			}

		}
		dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetCommentIndex)
		if len(list) < limit {
			break
		}
		ltId = int(list[len(list)-1].ID)
	}

	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("sync post comment language end, at: %d", time.Now().Unix())

}

// 处理评论审核拿去机审
func CommentAuditByMachineAudit(c context.Context) {
	// 分布式锁，只能有一个抢占成功
	redisKey := cache.GetCommentContentMachineReviewCacheNxKey()
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CommentAuditByMachineAudit | review post comment by machine start, at: %d", time.Now().Unix())
	if ok, _ := redis.GetClient().SetNX(c, redisKey, "1", config.GetConfig().Dynamic.PostMachineReviewTimeoutDuration*time.Second).Result(); ok {
		defer func() {
			recovery.CatchGoroutinePanic(context.Background())
			redis.GetClient().Del(context.Background(), redisKey)
		}()
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CommentAuditByMachineAudit | review post comment by machine get lock, at: %d", time.Now().Unix())
		var limit = 299
		auditCommentListKey := cache.WaitingForReviewCommentToKafkaKeys()
		commentUuidList, cErr := redis.GetClient().ZRange(c, auditCommentListKey, 0, int64(limit)).Result()
		if cErr != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CommentAuditByMachineAuditc Err:%v, commentUuidList:%v", cErr, commentUuidList)
			if errors.Is(cErr, redis2.Nil) {
				// 没有这个key，或者没有数据
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CommentAuditByMachineAudit | empty comment audit cache list")
				return
			}
		}

		// 将结果分成 30 批，每批 10 条数据
		batchSize := 10
		for i := 0; i < len(commentUuidList); i += batchSize {
			end := i + batchSize
			if end > len(commentUuidList) {
				end = len(commentUuidList)
			}
			var batchCommentUUIDs []interface{}
			for _, needAuditCommentUUID := range commentUuidList[i:end] {
				batchCommentUUIDs = append(batchCommentUUIDs, needAuditCommentUUID)
			}
			kErr := WaitingForReviewCommentToKafka(c, batchCommentUUIDs, auditCommentListKey)
			if kErr != nil {
				return
			}
		}

		// commentUuids := make([]interface{}, 0, 10)
		// for _, commentUuid := range commentUuidList {
		// 	if len(commentUuids)%10 == 0 {
		// 		kErr := WaitingForReviewCommentToKafka(c, commentUuids, auditCommentListKey)
		// 		if kErr != nil {
		// 			return
		// 		}
		// 		commentUuids = make([]interface{}, 0, 10)
		// 	}
		// 	commentUuids = append(commentUuids, commentUuid)
		// }
		// if len(commentUuids) > 0 {
		// 	// 投递到kafka
		// 	kErr := WaitingForReviewCommentToKafka(c, commentUuids, auditCommentListKey)
		// 	if kErr != nil {
		// 		return
		// 	}
		// }

		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CommentAuditByMachineAudit | review comment by machine end, at: %d", time.Now().Unix())
	}
	return
}

// 同步评论下的用户点赞
func SyncPostCommentUpVote(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("sync post comment upvote start, at: %d", time.Now().Unix())
	// 获取用户审核表数据
	var limit, index, ltId = 10000, 0, 0
	for {
		index++
		list, err := dao.CommentList(&dao.CommentListConditions{
			Order: []*dao.OrderConditions{
				&dao.OrderConditions{
					Column: "id",
					IsDesc: true,
				},
			},
			IgnoreDel: true,
			LtId:      int64(ltId),
		}, limit)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostCommentLanguage | get post comment list failed, ltId: %d, limit: %d, err:%v", ltId, limit, err)
			break
		}
		var commentUuids = make([]string, 0)
		for _, comment := range list {
			commentUuids = append(commentUuids, comment.CommentUUID)
		}

		if len(commentUuids) == 0 {
			continue
		}

		// 获取审核的数据
		commentContentList := dao.GetCommentContentList(commentUuids)

		if len(commentContentList) == 0 {
			continue
		}

		var commentStatList = make([]*model.CommentState, 0, len(commentContentList))

		for _, content := range commentContentList {
			commentStatList = append(commentStatList, &model.CommentState{
				CommentUUID: content.CommentUUID,
				UpvoteCount: int(content.UpvoteCount),
			})
		}

		err = dao.BatchCreateCommentState(commentStatList)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostCommentLanguage | create comment state failed, ltId: %d, limit: %d, err:%v", ltId, limit, err)
			break
		}

		if len(list) < limit {
			break
		}
		ltId = int(list[len(list)-1].ID)
	}

	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("sync post comment state end, at: %d", time.Now().Unix())

}

// 未审核的数据同步到redis种
func SyncCommentContentAuditToRedis(c context.Context) {
	// 获取未送审的数据
	list, err := dao.CommentContentAuditListV2(1)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CommentAuditByMachineAudit | get comment content not audit failed, err: %s", err)
		return
	}
	cacheKey := cache.WaitingForReviewCommentToKafkaKeys()
	for _, audit := range list {
		err = redis.GetClient().ZAdd(context.Background(), cacheKey, &redis2.Z{
			Score:  float64(audit.CreatedOn),
			Member: audit.CommentUUID,
		}).Err()
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CommentAuditByMachineAudit | comment to redis list failed, err: %s", err)
			continue
		}
	}
}

// kafka投递
func WaitingForReviewCommentToKafka(c context.Context, commentUuids []interface{}, key string) error {
	// 投递到kafka
	kafkaData, jErr := json.Marshal(commentUuids)
	if jErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CommentAuditByMachineAudit | comment audit to kafka json encode failed, comment_uuid:%v,err:%v", commentUuids, jErr)
		return jErr
	}
	err := kafka.Produce(context.Background(), constants.MachieAuditCommentProducer, "", string(kafkaData))
	if err != nil {
		// 写入kafka失败
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CommentAuditByMachineAudit | comment audit list batch write queue failed: %v, message: %v", err, string(kafkaData))
		return err
	}
	// 删除redis中的值
	redis.GetClient().ZRem(context.Background(), key, commentUuids...)
	return nil
}

// kafka消费需要机审的评论
func ConsumeWaitingForReviewComment(c context.Context, commentUuids []string) error {
	// 先判断获取到的数据是否为空
	if len(commentUuids) == 0 {
		// 没有获取到数据，直接退出
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Info("CommentAuditByMachineAudit | empty comment audit list")
		return errs.NewCustomError(c, code.InvalidParams, "comment_uuid is required!")
	}
	// 获取审核的数据
	commentContentList := dao.GetCommentContentList(commentUuids)

	if len(commentContentList) == 0 {
		// 加入日志
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("CommentAuditByMachineAudit | empty comment content list, comment_uuid list:%v", commentUuids)
		// 接着往下循环查询未审核数据
		return errs.NewCustomError(c, code.GetCommentFailed, "get comment info failed")
	}
	for _, commentUUID := range commentUuids {
		for _, content := range commentContentList {
			if commentUUID == content.CommentUUID {
				PushCommentToSecurityDetection(c, content.IntlOpenid, CommentCreationReq{
					Content: content.Content,
					PicUrls: strings.Split(content.PicUrls, ","),
				}, commentUUID)
				break
			}
		}

	}
	return nil
}

// 刷新评论审核已经审核的评论
func SyncReviewedToCommentContentAudit(c context.Context) {
	var limit = 100
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToCommentContentAudit | start time at:%v", time.Now().Unix())
	for {
		// 查询单表，查询已审核的数据，status = 1 and (machine_status != 0 and artificial_status = 0) and is_del = 0
		commentAuditList, err := dao.GetReviewedByNotPublishList(limit)
		if err != nil {
			// 记录报错
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToCommentContentAudit | get reviewed comment audit list failed, err: %v", err)
			return
		}
		var commentUuidList []string
		for _, audit := range commentAuditList {
			commentUuidList = append(commentUuidList, audit.CommentUUID)
		}

		// 获取评论的数据
		list, err := dao.CommentList(&dao.CommentListConditions{
			CommentUuids: commentUuidList,
			IgnoreDel:    true,
		}, len(commentUuidList))
		if err != nil {
			// 记录报错
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToCommentContentAudit | get comment list failed, comment_uuid:%v, err:%v", commentUuidList, err)
			return
		}
		// 机审/人审通过的评论
		// var publishCommentUuid []string
		var humanPublishCommentUuid []string
		var machinePublishCommentUuid []string
		// 机审/人审未通过的评论
		// var rejectCommentUuid []string
		var humanRejectCommentUUId []string
		var machineRejectCommentUUId []string
		for _, audit := range commentAuditList {
			for _, comment := range list {
				if audit.CommentUUID == comment.CommentUUID && comment.IsAudit == 1 {
					// 人审通过
					if audit.ArtificialStatus == 1 {
						humanPublishCommentUuid = append(humanPublishCommentUuid, audit.CommentUUID)
					} else if audit.MachineStatus == 2 {
						// 人审未通过
						humanRejectCommentUUId = append(humanRejectCommentUUId, audit.CommentUUID)
					} else if audit.MachineStatus == 1 {
						// 机审通过
						machinePublishCommentUuid = append(machinePublishCommentUuid, audit.CommentUUID)
					} else if audit.MachineStatus == 2 {
						// 机审不通过
						machineRejectCommentUUId = append(machineRejectCommentUUId, audit.CommentUUID)
					}
					break
				}
			}
		}
		// 人审通过 (现网数据未0不处理)
		// if len(humanPublishCommentUuid) == 0 {
		// 	// 记录日志
		// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToCommentContentAudit | empty humanPublishCommentUuid list")
		// 	// return
		// } else {
		// 	// 更新为已发布
		// 	err = dao.UpdateCommentAuditV2(humanPublishCommentUuid, map[string]interface{}{
		// 		"status": 2,
		// 	})
		// 	if err != nil {
		// 		// 记录报错
		// 		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToCommentContentAudit | UpdateCommentAuditV2 failed, comment_uuid:%v, err:%v", humanPublishCommentUuid, err)
		// 		return
		// 	}
		// 	for _, commentUuid := range humanPublishCommentUuid {
		// 		doc := map[string]interface{}{
		// 			"status":            2,
		// 			"artificial_status": 1,
		// 		}
		// 		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, commentUuid, doc)
		// 	}
		// }
		// 机审通过
		if len(machinePublishCommentUuid) == 0 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToCommentContentAudit | empty machinePublishCommentUuid list")
		} else {
			err = dao.UpdateCommentAuditV2(humanRejectCommentUUId, map[string]interface{}{
				"status": 2,
			})
			if err != nil {
				// 记录报错
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToCommentContentAudit | UpdateCommentAuditV2 failed, comment_uuid:%v, err:%v", humanRejectCommentUUId, err)
				return
			}
			// for _, commentUuid := range humanRejectCommentUUId {
			// 	doc := map[string]interface{}{
			// 		"status":         2,
			// 		"machine_status": 1,
			// 	}
			// 	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, commentUuid, doc)
			// }
		}
		// 人审不通过 (现网不存在这类数据)
		// if len(humanRejectCommentUUId) == 0 {
		// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToCommentContentAudit | empty humanRejectCommentUUId list")
		// } else {
		// 	err = dao.UpdateCommentAuditV2(humanRejectCommentUUId, map[string]interface{}{
		// 		"status": 3,
		// 	})
		// 	if err != nil {
		// 		// 记录报错
		// 		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToCommentContentAudit | UpdateCommentAuditV2 failed, comment_uuid:%v, err:%v", humanRejectCommentUUId, err)
		// 		return
		// 	}
		// 	for _, commentUuid := range humanRejectCommentUUId {
		// 		doc := map[string]interface{}{
		// 			"status":            3,
		// 			"artificial_status": 2,
		// 			"is_del":            1, // 删除评论
		// 		}
		// 		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, commentUuid, doc)
		// 	}
		// }
		// 机审不通过 （不需要更新db）
		if len(machineRejectCommentUUId) == 0 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToCommentContentAudit | empty machineRejectCommentUUId list")
		} else {
			// err = dao.UpdateCommentAuditV2(machineRejectCommentUUId, map[string]interface{}{
			// 	"status": 3,
			// })
			// if err != nil {
			// 	// 记录报错
			// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToCommentContentAudit | UpdateCommentAuditV2 failed, comment_uuid:%v, err:%v", machineRejectCommentUUId, err)
			// 	return
			// }
			// for _, commentUuid := range machineRejectCommentUUId {
			// 	doc := map[string]interface{}{
			// 		// "status":         3,
			// 		"machine_status": 2,
			// 	}
			// 	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, commentUuid, doc)
			// }
		}
		if limit > len(commentAuditList) {
			break
		}
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToCommentContentAudit | end time at:%v", time.Now().Unix())
	// dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetCommentIndex)

}

func SyncInsertCommentToHotCache(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncInsertCommentToHotCache | start time at:%v", time.Now().Unix())
	commentHotCalculationUUIDsKey := cache.GetCommentHotCalculationUUIDKey()
	var ltId int64
	for {
		list, err := dao.CommentList(&dao.CommentListConditions{
			Type:  int(constants.DYNAMIC_COMMENT),
			IsDel: 0,
			LtId:  ltId,
			Order: []*dao.OrderConditions{
				&dao.OrderConditions{
					Column: "id",
					IsDesc: true,
				},
			},
		}, 1000)
		if err != nil {
			log.WithFieldsContext(c, "logs_types", constants.LogType_Standalonesite).Errorf("SyncInsertCommentToHotCache | get comment list ")
			return
		}
		var commentUuid []interface{}
		for _, comment := range list {
			commentUuid = append(commentUuid, comment.CommentUUID)
			if len(commentUuid) >= 100 {
				redis.GetClient().SAdd(context.Background(), commentHotCalculationUUIDsKey, commentUuid...)
				commentUuid = make([]interface{}, 0)
			}
		}
		if len(list) < 1000 {
			break
		}
		ltId = list[len(list)-1].ID
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncInsertCommentToHotCache | end time at:%v", time.Now().Unix())

}
