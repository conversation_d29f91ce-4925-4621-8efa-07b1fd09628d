package model

type UserPermission struct {
	*Model
	IntlOpenid  string `json:"intl_openid"`
	ActionType  int    `json:"action_type"`  // 用户操作类型: 1：管理员，2：认证，3：白名单，4：禁言, 5: 降权
	ActionValue int    `json:"action_value"` // 认证类型时：1为官方认证，2为创作认证，3为机构认证；管理员类型时：1为所有板块管理员；禁言:禁言原因
	ValidOn     int64  `json:"valid_on"`     // 精确到秒，以名单生效开始计算有效时限；到期后状态变为已失效
	Introduce   string `json:"introduce"`
	Creator     string `json:"creator"`
	Updater     string `json:"updater"`
	GameId      string `json:"game_id"`
	AreaId      string `json:"area_id"`
}

func (p *UserPermission) TableName() string {
	return "p_user_permission"
}
