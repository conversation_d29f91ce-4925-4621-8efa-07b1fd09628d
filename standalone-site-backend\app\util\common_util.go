package util

func RemoveDuplicateInt64(arr []int64) []int64 {
	// 创建一个 map 来记录已经存在的元素
	seen := make(map[int64]bool)
	result := []int64{} // 用于存储去重后的结果

	for _, value := range arr {
		if _, exists := seen[value]; !exists {
			seen[value] = true             // 标记为已存在
			result = append(result, value) // 添加到结果中
		}
	}

	return result
}

func RemoveDuplicateString(arr []string) []string {
	// 创建一个 map 来记录已经存在的元素
	seen := make(map[string]bool)
	result := []string{} // 用于存储去重后的结果

	for _, value := range arr {
		if _, exists := seen[value]; !exists {
			seen[value] = true             // 标记为已存在
			result = append(result, value) // 添加到结果中
		}
	}

	return result
}

// SafeSubstring 安全裁剪字符串，确保不会裁剪到半个字符
func SafeSubstring(s string, size int) []string {
	var result []string
	runes := []rune(s) // 将字符串转换为rune切片，以处理Unicode字符

	for len(runes) > 0 {
		if len(runes) < size {
			result = append(result, string(runes)) // 剩余部分直接添加
			break
		}
		result = append(result, string(runes[:size])) // 添加前size个rune
		runes = runes[size:]                          // 剩余部分
	}

	return result
}
