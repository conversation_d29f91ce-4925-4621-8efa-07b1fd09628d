package constant

// 游戏id定义
const (
	GAMEID_AB  = "29013" // 暗区突围
	GAMEID_CAG = "30061" // 暗区突围pc
	GAMEID_HOK = "29134" // HOK
)

// idip命令字定义
const (
	IDIP_CMD_AB_GET_USER_INFO  = "20113000" // ab暗区突围获取用户信息
	IDIP_CMD_CAG_GET_USER_INFO = "29232000" // cag获取用户信息
)

// cag新角色等级活动奖励类型
const (
	CAG_NEW_ROLE_PRIZE_TYPE_ROLE  = 1 // cag创建角色奖励
	CAG_NEW_ROLE_PRIZE_TYPE_LEVEL = 2 // cag等级奖励
)

// cag新角色等级活动奖励状态
const (
	CagRoleLevelPrizeStatusWaitCreateRole = 0 // 等待创建角色
	CagRoleLevelPrizeStatusWaitSend       = 1 // 等待发奖
	CagRoleLevelPrizeStatusSendSuccess    = 2 // 发奖成功
	CagRoleLevelPrizeStatusSendFail       = 3 // 发奖失败
)

// 调用发放奖励游戏名称
const (
	CAG_PRIZE_GAME_NAME = "abi" // cag发放奖励游戏名称
)

// 发奖回包错误码
const (
	SEND_PRIZE_ERR_CODE_HAS_PRIZE = 400061 // 用户已经发放过奖励
	SEND_PRIZE_ERR_CODE_LIMIT     = 400018 // 个人已到达礼包领取总限
)

// 账号服务错误码
const (
	GAME_HAS_NO_OPEN_ID   = 303035 // 用户没有openid
	SRC_OPEN_ID_NOT_EXIST = 303037 // 源openid不存在

	GAME_OPEN_ID_EMPTY        = 303044 // 游戏openid为空
	INTL_RET_ERROR            = 303045 // intl返回错误
	NO_GAME_OPEN_ID           = 303046 // 没有游戏openid
	SRC_LIP_OPEN_ID_NOT_EXIST = 303047 // 源openid不存在
	INTL_OTHER_ERROR          = 303048 // intl其他错误
)

// idip代理服务错误码
const (
	IDIP_GAME_HAS_NO_ROLE = 114002 // 游戏没有角色
)
