package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetOfficialDataByPostUuid(postUuid string) ([]*model.PostOfficialLanguage, error) {
	var postOfficialData []*model.PostOfficialLanguage
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostOfficialLanguage{}).TableName()).Where("post_uuid = ? AND is_del = ?", postUuid, 0).Order("`order` asc").Find(&postOfficialData).Error
	if err != nil {
		return nil, err
	}
	return postOfficialData, nil
}

func GetPostOfficialList(isIgnoreDelete bool, gtId int64, limit int) ([]*model.PostOfficialLanguage, error) {
	var postOfficialData []*model.PostOfficialLanguage
	db := DB.SelectConnect("db_standalonesite").Table((&model.PostOfficialLanguage{}).TableName())
	if isIgnoreDelete {
		db = db.Unscoped()
	}
	if gtId > 0 {
		db = db.Where("id > ?", gtId)
	}
	err := db.Order("id asc").Limit(limit).Find(&postOfficialData).Error
	if err != nil {
		return nil, err
	}
	return postOfficialData, nil
}

// 根据动态id获取帖子内容或者官方多语言
func GetPostContentOrOfficialData(postUuid string, isOfficial int32, language string, isPocket bool) (*model.PostContent, error) {
	var data *model.PostContent
	postContent, err := GetPostContentList(postUuid, false)
	if err != nil {
		return nil, err
	}
	if len(postContent) == 0 {
		return &model.PostContent{}, nil
	}

	hasLanguage := false
	for _, content := range postContent {
		if content.Language == language {
			hasLanguage = true
			data = content
			break
		}
	}
	// 是官方动态的话或者是需要兜底语言的话
	if isOfficial == 1 || isPocket {
		// 没有当前语言的话需要采用兜底语言
		if !hasLanguage {
			data = postContent[0]
		}
	}
	return data, nil
}

func UpdatePostOfficialLanguage(postUuid string, id int64, data map[string]interface{}) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.PostOfficialLanguage{}).TableName()).Where("id = ?", id).Where("post_uuid = ? AND is_del = ?", postUuid, 0).Updates(&data).Error
	if err != nil {
		return err
	}
	return nil
}
