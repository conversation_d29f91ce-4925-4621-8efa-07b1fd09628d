package nikketmp

import (
	"context"
	"encoding/json"
	"fmt"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	utilModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"math"
	"strings"
	"sync"
	"time"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/model/general"
	"trpc.act.logicial/app/model/nikke"
)

// RedHoodSkinVoteRecordGift 小红帽皮肤投票礼包记录
func RedHoodSkinVoteRecordGift(ctx context.Context, presentId string, roleInfo *gamePb.RoleInfo) error {

	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	langType := metadata.GetLangType(ctx)
	// 是否已获取礼包
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeRedHoodSkinVoteGiftDistributionRecordModel{}.TableName()).
		Where("uid = ? and account_type = ?", userAccount.Uid, int(userAccount.AccountType)).
		Where("present_id = ?", presentId)
	var count int64
	if err = tx.Count(&count).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RedHoodSkinVoteRecordGift Count db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		return nil
	}
	// 记录礼包信息
	info := buildRoleInfo(roleInfo)
	roleInfoStr, err := json.Marshal(info)
	if err != nil {
		return errs.NewCustomError(ctx, code.CommonParamJsonError, fmt.Sprintf("roleInfo json Marshal error, err:[%v]", err))
	}
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeRedHoodSkinVoteGiftDistributionRecordModel{}.TableName()).
		Create(&nikke.NikkeRedHoodSkinVoteGiftDistributionRecord{
			UID:         userAccount.Uid,
			AccountType: int(userAccount.AccountType),
			RoleInfo:    string(roleInfoStr),
			PresentID:   presentId,
			LangType:    langType,
			Status:      0, // 未发货
		}).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RedHoodSkinVoteRecordGift Create db error, \t [Error]:{%v} ", err)
	}
	return nil

}

// ScheduledSendRedHoodSkinVote 小红帽皮肤投票定时发货
func ScheduledSendRedHoodSkinVote(ctx context.Context) (err error) {
	scheduleCtx := context.Background()
	tableName := nikke.NikkeRedHoodSkinVoteGiftDistributionRecordModel{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"ScheduledSendRedHoodSkinVote db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("ScheduledSendRedHoodSkinVote:[%v]", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页200条
	pageSize := 200
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		offset := (pageNumber - 1) * pageSize
		var logData = []nikke.NikkeRedHoodSkinVoteGiftDistributionRecord{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).Where(condition).Offset(offset).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"ScheduledSendRedHoodSkinVote Find db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("ScheduledSendRedHoodSkinVote logData:[%v]", logData))
		for _, v := range logData {
			wg.Add(1)
			go func(data nikke.NikkeRedHoodSkinVoteGiftDistributionRecord) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(string(accountData))
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				// 获取角色信息
				var gameRoleInfo *gamePb.RoleInfo
				if data.RoleInfo == "" {
					err = errs.NewCustomError(ctx, code.CurrentUserRoleAbnormal, "ScheduledSendRedHoodSkinVote CurrentUserRoleAbnormal err, data:[%v]", data)
				} else {
					gameRoleInfo, err = buildPBRoleInfo(data.RoleInfo)
				}
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf(
					"ScheduledSendRedHoodSkinVote buildPBRoleInfo：%v", gameRoleInfo))

				if err == nil {
					log.WithFieldsContext(ctx, "ScheduledSendRedHoodSkinVote log", "debug").Infof(fmt.Sprintf("ScheduledSendRedHoodSkinVote dataInfo: %#v", data))
					// 发送礼包
					sendReq := &presentPb.SendPresentReq{
						PresentId: data.PresentID,
						RoleInfo:  gameRoleInfo,
					}
					accountData, _ = proto.Marshal(&accountPb.UserAccount{
						Uid:         data.UID,
						AccountType: accountPb.AccountType(data.AccountType),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							GameId:    gameRoleInfo.GameId,
							ChannelId: 3,
						},
					})
					callopts = []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
						client.WithMetaData(metadata.LangType, []byte(data.LangType)),
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)

					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// 记录日志
						log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
							"ScheduledSendRedHoodSkinVote ReportTlog succ: AreaId:[%v],GameId:[%v],openID:[%v],PresentID:[%v]",
							gameRoleInfo.AreaId, gameRoleInfo.GameId, openID, data.PresentID))
					} else {
						log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf(
							"ScheduledSendRedHoodSkinVote ReportTlog fail: AreaId:[%v],GameId:[%v],openID:[%v],PresentID:[%v]",
							gameRoleInfo.AreaId, gameRoleInfo.GameId, openID, data.PresentID))
					}
					delErr := errs.ParseError(ctx, sendErr)
					if delErr.Code == 400018 || delErr.Code == 400042 {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[ScheduledSendRedHoodSkinVote SendPresent] service err222:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// }
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[ScheduledSendRedHoodSkinVote SendPresent] service err:%v", sendErr))
					// 修改发货状态
				} else {
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("ScheduledSendRedHoodSkinVote GetRoleInfo] service err:%v", err))
				}
				wg.Done()
			}(v)

		}
		wg.Wait()

	}
	return
}

// RedHoodVotesTalliedHourlySendBot 小红帽皮肤投票定时统计票数
func RedHoodVotesTalliedHourlySendBot(ctx context.Context) error {
	fsourceId := "page-26628"
	storageKey := "vote"

	botUrl := config.GetConfig().NikkeRedHoodVotesBotUrl

	tableName, err := utilModel.GetTabNameWithGeneral(ctx, (&general.ConfigModel{}).TableName(), fsourceId, storageKey,
		(&general.LogModel{}).TableName(), 100)
	if err != nil {
		return err
	}

	specialVote, err := CountSpecialVote(ctx, tableName, fsourceId)
	if err != nil {
		err = errs.NewCustomError(ctx, code.NikkeCountSpecialVoteError,
			"Nikke RedHoodVotesTalliedHourlySendBot Error, tableName:[%v],fsourceId:[%v]", tableName, fsourceId)
		return err
	}
	specialVoteStr := make([]string, 0, 5)
	specialVoteStr = append(specialVoteStr, "**小红帽皮肤投票统计:**\n")
	var planName string
	for idx, v := range specialVote {
		switch v.StorageTag {
		case "1":
			planName = "plan A"
		case "2":
			planName = "plan B"
		case "3":
			planName = "plan C"
		}
		ranking := idx + 1
		msg := fmt.Sprintf("(%d)%s: %d\n", ranking, planName, v.CountNum)
		specialVoteStr = append(specialVoteStr, msg)
	}
	sendMsg := strings.Join(specialVoteStr, "")
	// 推送特殊投票企微消息
	if err = SendMarkdownToWxWorkRobot(ctx, botUrl, sendMsg); err != nil {
		return err
	}
	return nil
}
