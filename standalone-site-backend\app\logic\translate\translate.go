package translate

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"github.com/spf13/cast"
	"trpc.publishing_application.standalonesite/app/util"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	redis2 "github.com/go-redis/redis/v8"
	redisOrgin "github.com/go-redis/redis/v8"
	"golang.org/x/net/html"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

type TranslateParam struct {
	Contents     []string `json:"contents"`
	LanguageCode string   `json:"language_code"`
}

type TranslateText struct {
	Text string `json:"text"`
}

type TranslateParamV2 struct {
	Game         string        `json:"game"`              // 接口分配的游戏ID
	SourceLang   string        `json:"source_lang"`       // 文字所属的语言
	TargetLang   string        `json:"target_lang"`       // 文字需要翻译的语言
	Source       TranslateText `json:"source"`            // 翻译的内容
	FromLanguage []string      `json:"source_from_langs"` // 检测语种的范围
}

type SegmentTextParam struct {
	Lang      string   `json:"lang"`       // 语种
	FromLangs []string `json:"from_langs"` // 语种检测范围
	CharLimit int      `json:"char_limit"` // 断句之后的长度
	Text      string   `json:"text"`       // 需要断句文案
}

type TranslateRsp struct {
	Code int           `json:"code"`
	Data TranslateData `json:"data"`
}

type TranslateData struct {
	Result []string `json:"result"`
}

type TranslateRspV2 struct {
	Code    int           `json:"code"`
	Msg     string        `json:"msg"`
	Data    TranslateText `json:"data"`
	TraceId string        `json:"trace_id"`
}

type SegmentText struct {
	TextList []string `json:"text_list"`
}

type SegmentRsp struct {
	Code    int         `json:"code"`
	Msg     string      `json:"msg"`
	Data    SegmentText `json:"data"`
	TraceId string      `json:"trace_id"`
}

var LanguageConvert = map[string]string{
	constants.PostLanguageZh:   "chs",
	constants.PostLanguageKo:   "ko",
	constants.PostLanguageZhTw: "chtw",
	constants.PostLanguageJa:   "ja",
	constants.PostLanguageEn:   "en",
}

var FromLanguage = []string{"chs", "ko", "chtw", "ja", "en"}

// 支持翻译的语言
var translatableLanguages = []string{"ko", "zh-TW", "ja", "en"}

const (
	enCharSize    = 400 // 英语字符
	otherCharSize = 130 // 其他字符数
)

// currentLanguage 系统当前语言，获取需要翻译的多语言文案
func TranslateContent(c context.Context, req *pb.TranslateContentReq, currenLanguage string, isGoroutine bool) (*pb.TranslateContentRsp, error) {

	rsp := &pb.TranslateContentRsp{}
	// 先获取缓存数据
	redisKey := cache.GetTranslateContentKey(req.Type, req.ContentUuid, req.Language)
	translateCacheStr, err := redis.GetClient().Get(c, redisKey).Result()
	if err != nil && !errors.Is(err, redisOrgin.Nil) {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("TranslateContent get translate cache err, req:(%v), err=(%v)", req, err)
	} else if translateCacheStr != "" {
		err := json.Unmarshal([]byte(translateCacheStr), &rsp)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("TranslateContent get translate cache err, req:(%v), err=(%v)", req, err)
		} else {
			return rsp, nil
		}
	}
	var title, content, language string
	var exitsLanguage = make([]string, 0)
	if constants.TranslateContentType(req.Type) == constants.Post {
		// 翻译动态详情内容
		postItem, err := dao.GetPost(req.ContentUuid)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetPostsFailed, "Failed to get dynamic details")
		}
		// 获取语言,如果是官方贴就取当前用户选择的语言，如果不是官方贴就用帖子语言

		postContentList, err := dao.GetPostContentList(req.ContentUuid, false)
		if err != nil || len(postContentList) == 0 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostInfo err, post_uuid:(%s), err=(%v)", postItem.PostUUID, err)
			return rsp, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.GetPostContentsFailed, "Failed to get dynamic content details")
		} else {

			var postContent *model.PostContent
			var isExistLanguage bool
			for _, content := range postContentList {
				exitsLanguage = append(exitsLanguage, content.Language)
				if content.Language == req.Language {
					postContent = content
					isExistLanguage = true
					break
				}
			}
			if !isExistLanguage {
				// 直接取最靠前的一条数据，从数据库拿出来都是排好序的，第一条就是兜底语言
				postContent = postContentList[0]
			}
			title = postContent.Title
			content = postContent.Content
			language = postContent.Language
		}

		// var nowLanguage = postItem.Language
		// if postItem.IsOfficial == 1 {
		// 	nowLanguage = currenLanguage
		// }
		// postContent, err := dao.PostContentGet(req.ContentUuid, nowLanguage)
		// if err != nil {
		// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("TranslateContent PostContentGet err, post_uuid:(%s), err=(%v)", req.ContentUuid, err)
		// 	return rsp, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.GetPostContentsFailed, "Failed to get dynamic content details")
		// } else {
		// 	title = postContent.Title
		// 	content = postContent.Content
		// 	language = postItem.Language
		// }
	} else if constants.TranslateContentType(req.Type) == constants.Comment {
		// 翻译评论内容
		comment, err := dao.GetCommentByUUID(req.ContentUuid)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("TranslateContent GetCommentByUUID err, comment_uuid:(%s), err=(%v)", req.ContentUuid, err)
			return rsp, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.GetCommentContentFailed, "Failed to get content")
		}
		commentContent, err := dao.GetCommentInfoByUUID(req.ContentUuid)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("TranslateContent GetCommentInfoByUUID err, comment_uuid:(%s), err=(%v)", req.ContentUuid, err)
			return rsp, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.GetCommentContentFailed, "Failed to get content details")
		} else {
			title = commentContent.Title
			content = commentContent.Content
			language = comment.Language
			exitsLanguage = append(exitsLanguage, language)
		}
	} else {
		return rsp, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParamsErr, "Illegal content parameters")
	}

	// 如果语言类型一样，则直接返回原内容
	if language == req.Language {
		rsp.Title = title
		rsp.Content = content
	} else {
		var texts []string
		var doc *html.Node
		if len(content) > 0 {
			htmlStr := strings.Join([]string{"<html><body>", content, "</body></html>"}, "")
			// 解析 HTML
			doc, err = html.Parse(strings.NewReader(htmlStr))
			if err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("TranslateContent html.Parse err, comment_uuid:(%s), err=(%v)", req.ContentUuid, err)
				return rsp, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParamsErr, "Translate failed, the content parse error")
			}
			contentTexts := extractText(doc)
			//util.ExtractTextFromP(doc, &texts)
			texts = append(texts, contentTexts...)
		}
		// 批量翻译
		translationMap, err := translateBatchByAIGCV2(c, texts, req.Language, language, title)
		if err != nil {
			return rsp, errs.NewCustomError(c, code.TranslateContentFailed, "translate content failed")
		}

		if len(title) > 0 {
			rsp.Title = translationMap[title]
		}
		if len(content) > 0 {
			// 替换 HTML 中的文本
			replaceText(doc, translationMap)

			// 输出替换后的 HTML
			var buf bytes.Buffer
			if err := html.Render(&buf, doc); err != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("TranslateContent html.Render2 err, comment_uuid:(%s), err=(%v)", req.ContentUuid, err)
				return rsp, errs.NewCustomError(c, code.TranslateContentFailed, "Translate failed, the content parse error2")
			}
			htmlStr := buf.String()
			htmlStr = strings.TrimPrefix(htmlStr, "<html><head></head><body>")
			htmlStr = strings.TrimSuffix(htmlStr, "</body></html>")
			rsp.Content = htmlStr
		}
		// 翻译成功之后需要翻译其他语言
		if !isGoroutine {
			go func() {
				var needTranslateLang = make([]string, 0)
				for _, translatableLanguage := range translatableLanguages {
					if translatableLanguage == req.Language {
						continue
					}
					// 判断是否存在对应的语言了
					var isExistTranslateLang bool
					for _, lang := range exitsLanguage {
						if translatableLanguage == lang {
							// 已经多语言了
							break
						}
					}
					if isExistTranslateLang {
						continue
					}
					// 判断是否支持翻译
					if _, ok := LanguageConvert[translatableLanguage]; !ok {
						continue
					}
					// 判断是否有缓存
					key := cache.GetTranslateContentKey(req.Type, req.ContentUuid, translatableLanguage)
					result, err := redis.GetClient().Exists(context.Background(), key).Result()
					if err != nil && !errors.Is(err, redis2.Nil) {
						log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("TranslateContent get redis key err, comment_uuid:(%s), language:(%s),err=(%v)", req.ContentUuid, translatableLanguage, err)
						return
					}
					if result > 0 {
						// 当前语言已经翻译了，直接退出
						continue
					}
					needTranslateLang = append(needTranslateLang, translatableLanguage)
				}
				if len(needTranslateLang) > 0 {
					// 存在需要翻译的其他语言
					var reqList = make([]*pb.TranslateContentReq, 0, len(needTranslateLang))
					for _, lang := range needTranslateLang {
						reqList = append(reqList, &pb.TranslateContentReq{
							ContentUuid: req.ContentUuid,
							Type:        req.Type,
							Language:    lang,
						})
					}
					job := util.NewJob(TranslateSteam[*pb.TranslateContentRsp](), reqList)
					util.WorkerPoolDo(job)
				}

			}()
		}
	}
	// 写入redis缓存
	rspStr, err := json.Marshal(rsp)
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("TranslateContent json.Marshal err, comment_uuid:(%s), err=(%v)", req.ContentUuid, err)
	}
	if _, err = redis.GetClient().SetEX(context.Background(), redisKey, string(rspStr), time.Duration(7*24*3600)*time.Second).Result(); err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("TranslateContent set redis cache err, comment_uuid:(%s), err=(%v)", req.ContentUuid, err)
	}
	return rsp, nil
}

func TranslateSteam[T any]() func(req *pb.TranslateContentReq) (T, error) {
	return func(req *pb.TranslateContentReq) (T, error) {
		var result T
		_, err := TranslateContent(context.Background(), req, "", true)
		return result, err
	}
}

// 解析 HTML 并提取文本
func extractText(n *html.Node) []string {
	var texts []string
	if n.Type == html.TextNode {
		if len(strings.Trim(n.Data, " ")) > 0 {
			texts = append(texts, n.Data)
		}
	}
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		texts = append(texts, extractText(c)...)
	}
	return texts
}

// 调用组内aigc服务，批量翻译文本
func translateBatchByAIGC(c context.Context, texts []string, language string) (map[string]string, error) {
	// 创建翻译映射
	translationMap := make(map[string]string)
	var subTexts []string
	totalLen := 0
	for _, text := range texts {
		// TODO 这里如果文字超长，则需要考虑截断，然后逐段翻译，主要是超长文本AI翻译偶尔会异常
		// 如果单条内容字数超过指定字符数，则单条翻译，免得aigc服务超时异常
		if len(text) > 2000 {
			subTranslateTexts, err := callTranslateApi(c, []string{text}, language)
			if err != nil {
				return translationMap, err
			}
			translationMap[text] = subTranslateTexts[0]
			continue
		}
		// 如果多条数据超过指定字符数了，也触发一次翻译
		totalLen += len(text)
		if totalLen > 2000 {
			subTranslateTexts, err := callTranslateApi(c, subTexts, language)
			if err != nil {
				return translationMap, err
			}
			for i, subText := range subTexts {
				translationMap[subText] = subTranslateTexts[i]
			}
			subTexts = []string{text}
			totalLen = len(text)
			continue
		}
		subTexts = append(subTexts, text)
	}

	// 最后一批数据
	if len(subTexts) > 0 {
		subTranslateTexts, err := callTranslateApi(c, subTexts, language)
		if err != nil {
			return translationMap, err
		}
		for i, subText := range subTexts {
			translationMap[subText] = subTranslateTexts[i]
		}
	}
	return translationMap, nil
}

func callTranslateApi(c context.Context, texts []string, language string) ([]string, error) {
	var translateTexts []string
	requestURL := fmt.Sprintf("%s%s", config.GetConfig().TranslateConf.AIGCHost, config.GetConfig().TranslateConf.AIGCPath)
	translateParam := &TranslateParam{
		Contents:     texts,
		LanguageCode: language,
	}
	marshal, _ := json.Marshal(translateParam)
	optionOne := httpclient.ClientOption{
		URL:     requestURL,
		Type:    http.MethodPost,
		Timeout: 3 * time.Minute,
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		PostString: string(marshal),
	}

	resultOption := httpclient.RequestOne(context.Background(), optionOne)
	// 请求失败
	if resultOption.RequestError != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("TranslateContent httpclient.RequestOne err, err=(%v)", resultOption.RequestError)
		return translateTexts, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.CallAIGCTranslateApiFailed, "translate system error")
	}

	var translateRsp TranslateRsp
	jsonError := json.Unmarshal([]byte(resultOption.Result), &translateRsp)
	if jsonError != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("TranslateContent json.Unmarshal err, jsonError=(%v)", jsonError)
		return translateTexts, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.ParseAIGCTranslateApiResultFailed, "translate system error")
	}
	translateTexts = translateRsp.Data.Result

	// 传过去的文本，跟翻译回来的文本数对不上，则报错
	if len(texts) != len(translateTexts) {
		return translateTexts, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.TranslateTextCountNotMatchErr, "translate system error")
	}
	return translateTexts, nil
}

// 调用外部ai服务，批量翻译文本
func translateBatchByAIGCV2(c context.Context, texts []string, language string, currLanguage string, title string) (map[string]string, error) {
	// 创建翻译映射
	//translationMap := make(map[string]string)
	var resultData = make(map[string]string)
	var subTexts []string
	if title != "" {
		texts = append(texts, title)
	}
	//totalLen := 0
	// 长文本切割的文字大小
	size := enCharSize
	if currLanguage != constants.PostLanguageEn {
		size = otherCharSize
	}
	result := groupStringsByMaxSize(texts, size)
	for _, group := range result {
		var groupText string
		//var index int
		for i, t := range group {
			if i%2 != 0 && i > 0 {
				t = fmt.Sprintf("<color=%s>%s</color>", util.RandomColor(), t)
				//index++
			}
			groupText = fmt.Sprintf("%s%s", groupText, t)
		}
		subTexts = append(subTexts, groupText)
	}
	translateText, err := batchTranslateText(subTexts, language, currLanguage)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("translateBatchByAIGCV2 | translate err, texts:%v, targetLang:%s, sourceLang:%s,err:%v", subTexts, language, currLanguage, err)
		return resultData, err
	}
	var newTranslateMap = make(map[string]string)
	for i, text := range subTexts {
		keyMatches := ExtractingLabelData(text)
		valueMatches := ExtractingLabelData(translateText[i])
		for j, match := range keyMatches {
			mapKey := strings.TrimSpace(match)
			if mapKey == "" {
				continue
			}
			if j < len(valueMatches) {
				newTranslateMap[mapKey] = valueMatches[j]
			} else {
				newTranslateMap[mapKey] = ""
			}
		}
		newTranslateMap[text] = translateText[i]
	}

	return newTranslateMap, nil
}

// 请求接口翻译内容
func callTranslateApiV2(c context.Context, text string, targetLang string, sourceLang string) (string, error) {

	var translateText string
	path := config.GetConfig().TranslateConf.WelocAiTranslatePath
	requestURL := fmt.Sprintf("%s%s", config.GetConfig().TranslateConf.WelocAiHost, path)

	var sourceLangConvert, targetLangConvert string

	if lang, ok := LanguageConvert[sourceLang]; ok {
		sourceLangConvert = lang
	} else {
		sourceLangConvert = "auto"
	}
	if lang, ok := LanguageConvert[targetLang]; !ok {
		// 没有对应的目标语言
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("TranslateContent not target language, target language: %s", targetLang)
		return translateText, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.CallAIGCTranslateApiFailed, "translate system error")
	} else {
		targetLangConvert = lang
	}

	translateParam := &TranslateParamV2{
		Game:         config.GetConfig().TranslateConf.WelocAiAppKey,
		SourceLang:   sourceLangConvert,
		TargetLang:   targetLangConvert,
		Source:       TranslateText{Text: text},
		FromLanguage: FromLanguage,
	}

	marshal, _ := json.Marshal(translateParam)
	translateRsp, err := RequestWelocApi[TranslateRspV2](context.Background(), string(marshal), requestURL, path)
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("callTranslateApiV2 request api failed, result=(%+v)", translateRsp)
		return translateText, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.CallAIGCTranslateApiFailed, "translate system error")
	}
	return translateRsp.Data.Text, nil
}

// 替换 HTML 中的文本
func replaceText(n *html.Node, translations map[string]string) {
	if n.Type == html.TextNode {
		data := strings.TrimSpace(n.Data)
		if translated, ok := translations[data]; ok {
			n.Data = translated
		}

	}
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		replaceText(c, translations)
	}
}

// 接口签名
func GenAuthSignV11(ctx context.Context, requestPath, appKey, appSecret, timestamp, nonce string) string {
	if requestPath == "" || appKey == "" || appSecret == "" || timestamp == "" {
		return ""
	}
	// 校验 checkSum
	h := hmac.New(sha256.New, []byte(appSecret))
	h.Write([]byte(fmt.Sprintf("%s\n%s\n%s\n%s", requestPath, appKey, timestamp, nonce)))
	checkSum := hex.EncodeToString(h.Sum([]byte{}))
	return checkSum
}

// 断句切割
func callTranslateTextSplit(c context.Context, text string, sourceLang string) ([]string, error) {
	path := config.GetConfig().TranslateConf.WelocAiSegmentPath
	requestURL := fmt.Sprintf("%s%s", config.GetConfig().TranslateConf.WelocAiHost, path)

	var sourceLangConvert string

	if lang, ok := LanguageConvert[sourceLang]; ok {
		sourceLangConvert = lang
	} else {
		sourceLangConvert = "auto"
	}

	translateParam := &SegmentTextParam{
		Lang:      sourceLangConvert,
		FromLangs: FromLanguage,
		CharLimit: 100,
		Text:      text,
	}

	marshal, _ := json.Marshal(translateParam)
	splitTextRes, err := RequestWelocApi[SegmentRsp](c, string(marshal), requestURL, path)
	if err != nil {
		return nil, err
	}
	if splitTextRes.Code != 0 {
		// 调用失败
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("callTranslateTextSplit request api failed, result=(%+v)", splitTextRes)
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.CallAIGCTranslateApiFailed, "translate system error")
	}
	return splitTextRes.Data.TextList, nil
}

// 请求WelocApi
func RequestWelocApi[T any](c context.Context, reqStr string, requestURL string, path string) (T, error) {
	var result T
	uid, err := util.GetRandomStrByUuid()
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("RequestWelocApi create uuid err, err=(%v)", err)
		return result, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.CallAIGCTranslateApiFailed, "translate system error")
	}
	millTime := fmt.Sprintf("%d", time.Now().UnixMilli())
	optionOne := httpclient.ClientOption{
		URL:     requestURL,
		Type:    http.MethodPost,
		Timeout: 3 * time.Minute,
		Header: map[string]string{
			"Content-Type":     "application/json",
			"Content-type":     "application/json",
			"X-Auth-Platform":  config.GetConfig().TranslateConf.WelocAiAppKey,
			"X-Auth-Version":   "v1.1",
			"X-Auth-TimeStamp": millTime,
			"X-Auth-Nonce":     uid,
			"X-Auth-Sign":      GenAuthSignV11(c, path, config.GetConfig().TranslateConf.WelocAiAppKey, config.GetConfig().TranslateConf.WelocAiAppSecret, millTime, uid),
		},
		PostString: reqStr,
	}
	resultOption := httpclient.RequestOne(context.Background(), optionOne)
	// 请求失败
	if resultOption.RequestError != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("RequestWelocApi httpclient.RequestOne err, err=(%v)", resultOption.RequestError)
		return result, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.CallAIGCTranslateApiFailed, "translate system error")
	}
	jsonError := json.Unmarshal([]byte(resultOption.Result), &result)
	if jsonError != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("RequestWelocApi json.Unmarshal err, jsonError=(%v)", jsonError)
		return result, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.ParseAIGCTranslateApiResultFailed, "translate system error")
	}
	return result, nil
}

type TranslateIndexRes struct {
	Index int
	Text  string
}

// 断句之后并发翻译
func batchTranslateText(texts []string, targetLang string, sourceLang string) ([]string, error) {
	var textChannel = make(chan TranslateIndexRes, len(texts))
	semaphore := make(chan struct{}, 8) // 限制最大并发数为8
	var wg sync.WaitGroup
	errChannel := make(chan error, 1) // 用于传递错误
	// 设置超时时间为 2 分钟
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	for i, text := range texts {
		wg.Add(1)
		semaphore <- struct{}{}

		go func(ctx context.Context, translateText string, translateLang string, sLang string, index int, results chan<- TranslateIndexRes, errChannel chan<- error) {
			defer func() {
				wg.Done()
				<-semaphore
				recovery.CatchGoroutinePanic(context.Background())
			}()
			for {
				select {
				case <-ctx.Done():
					// 超时了，直接退出
					errChannel <- errors.New("waiting timeout")
					return
				default:
					res, err := redis.GetClient().Get(context.Background(), cache.GetTranslateBatchRequestCountKey()).Result()
					if err != nil && !errors.Is(err, redis2.Nil) {
						// redis报错了
						errChannel <- err
						return
					}
					count := cast.ToInt(res)
					if count < config.GetConfig().TranslateConf.MaxRequestCount {
						// 有空位可以请求
						redis.GetClient().Incr(context.Background(), cache.GetTranslateBatchRequestCountKey()).Result()
						translateRes, err := callTranslateApiV2(context.Background(), translateText, translateLang, sLang)
						if err != nil {
							// 翻译失败
							errChannel <- err
							return
						}
						results <- TranslateIndexRes{
							Index: index,
							Text:  translateRes,
						}
						redis.GetClient().Decr(context.Background(), cache.GetTranslateBatchRequestCountKey()).Result()
						return
					}
					// 睡眠1s
					time.Sleep(time.Duration(1) * time.Second)
					break
				}

			}

		}(ctx, text, targetLang, sourceLang, i, textChannel, errChannel)
	}
	// 等待所有工作完成
	go func() {
		wg.Wait()
		close(textChannel)
		close(errChannel)
		<-ctx.Done()
	}()

	var translateResultList = make([]string, len(texts))
	var firstError error

	for res := range textChannel {
		translateResultList[res.Index] = res.Text
	}
	for err := range errChannel {
		if err != nil {
			return translateResultList, firstError
		}
	}

	return translateResultList, nil
}

func groupStringsByMaxSize(strs []string, maxSize int) [][]string {
	// 计算总字符数
	totalSize := 0
	for _, s := range strs {
		totalSize += utf8.RuneCountInString(s)
	}

	// 计算每组的最大字符数限制
	maxGroupSize := maxSize
	if percentageLimit := totalSize / 5; percentageLimit < maxSize {
		maxGroupSize = percentageLimit
	}

	// 分组
	var groups [][]string
	var currentGroup []string
	currentGroupSize := 0

	for _, s := range strs {
		strSize := utf8.RuneCountInString(s)
		if currentGroupSize+strSize > maxGroupSize {
			// 当前组已满，开始新组
			groups = append(groups, currentGroup)
			currentGroup = []string{s}
			currentGroupSize = strSize
		} else {
			// 添加到当前组
			currentGroup = append(currentGroup, s)
			currentGroupSize += strSize
		}
	}

	// 添加最后一组
	if len(currentGroup) > 0 {
		groups = append(groups, currentGroup)
	}

	return groups
}

// 提取标签数据
func ExtractingLabelData(label string) []string {
	// 正则表达式，用于匹配 <color> 标签内的内容和不在任何标签内的内容
	re := regexp.MustCompile(`(<color=[^>]*>(.*?)</color>)|([^<]+)`)

	// 使用正则表达式查找所有匹配的文本节点
	matches := re.FindAllStringSubmatch(label, -1)

	// 提取匹配结果中的文本部分
	var texts []string
	for _, match := range matches {
		if len(match) > 2 && match[2] != "" {
			texts = append(texts, match[2]) // <color> 标签内的内容
		} else if len(match) > 3 && match[3] != "" {
			texts = append(texts, match[3]) // 不在任何标签内的内容
		}
	}
	return texts
}
