package common

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/spf13/cast"
)

// GetRollBackCtx 获取回滚使用Ctx，限时30s
func GetRollBackCtx() (ctx context.Context) {
	ctx = context.Background()
	return
}

// ReportTlog 上报
func ReportTlog(ctx context.Context, action string, subAction string, gameId int32,
	ExtentContent map[string]interface{}) (err error) {
	langType := metadata.GetLangType(ctx)
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: langType,
			XGameId:   cast.ToInt(gameId),
			XSource:   "pc_web",
		},
		Action:         action,
		SubAction:      subAction,
		OriginalGameId: cast.ToString(gameId),
		ExtentContent:  ExtentContent,
	}
	report.ReportTlog(ctx, tlogData)
	log.WithFieldsContext(ctx, "log_type", action).Infof(
		"vote ReportTlog action:[%v], subAction: [%v],gameId:[%v],ExtentContent:[%+v] ",
		action, subAction, gameId, ExtentContent)
	return
}
