package tweet

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"html/template"
	"io"
	"os"
	"time"
	"trpc.publishing_application.standalonesite/app/common"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

const (
	REQUESTPATH_GETCOMMODITYDATA = "/direct/commodity/Commodity/GetCommodityData"
)

const (
	SUMMARY = "summary"             // https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/summary
	BIGCARD = "summary_large_image" // https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/summary-card-with-large-image
	VIDEO   = "player"              // https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/player-card
)

type UserShareReq struct {
	ShareType   model.UserShareT `json:"share_type" binding:"required"`
	ShareDataID string           `json:"share_data_id" binding:"required"`
}

func CreateTagShareHtmlInfo(c context.Context, tagId int32, language string) string {
	card := model.ShareHtmlCard{
		CardType: BIGCARD,
	}
	tagShareRedisKey := cache.GetTagShareHtmlKey(tagId, language)
	cardCacheInfo, err := redis.GetClient().Get(c, tagShareRedisKey).Result()
	if err == nil {
		if cardCacheInfo != "" {
			err = json.Unmarshal([]byte(cardCacheInfo), &card)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreateTagShareHtmlInfo Unmarshal err: %v, tagShareRedisKey is %s", err, tagShareRedisKey)
			}
		}
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreateTagShareHtmlInfo Redis.Get err:%v, tagShareRedisKey is %s", err, tagShareRedisKey)
	}
	if card.CardImage == "" {
		// 获取动态详情信息
		req := &pb.GetTagReq{
			Id: tagId,
		}
		tagFormated, err := GetTag(c, req, language)

		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreateTagShareHtmlInfo GetTagInfo err: %v, tagId is %d", err, tagId)
			return ""
		}
		card.CardImage = template.HTML(tagFormated.PicUrl)
		card.CardUrl = template.HTML(fmt.Sprintf("%s%d&lang=%s", config.GetConfig().ShareConf.TagDetailUrl, tagId, language))
		shareActionI18nMap := getI18nMap(c)
		var languageInfoMap map[string]string
		var ok bool
		if languageInfoMap, ok = shareActionI18nMap[language]; !ok {
			languageInfoMap = shareActionI18nMap["en"]
		}
		var cardTitle string
		if shareTitle, ok := languageInfoMap["tag_share_title"]; ok {
			cardTitle = fmt.Sprintf("%s%s", shareTitle, tagFormated.TagName)
		}
		card.CardTitle = cardTitle
		card.SiteName = cardTitle
		card.CardDesc = cardTitle
		cardString, err := json.Marshal(card)
		if err == nil {
			redis.GetClient().SetEX(c, tagShareRedisKey, cardString, 10*time.Minute).Result()
		}
	}

	// 生成渠道爬虫可爬取的html内容
	return createShareHtml(c, card)

}

func CreateRewardsShareHtmlInfo(c context.Context, language string) string {
	card := model.ShareHtmlCard{
		CardType: BIGCARD,
	}
	rewardsShareRedisKey := cache.GetRewardsShareHtmlKey(language)
	cardCacheInfo, err := redis.GetClient().Get(c, rewardsShareRedisKey).Result()
	if err == nil {
		if cardCacheInfo != "" {
			err = json.Unmarshal([]byte(cardCacheInfo), &card)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreateRewardsShareHtmlInfo Unmarshal err: %v, tagShareRedisKey is %s", err, rewardsShareRedisKey)
			}
		}
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreateRewardsShareHtmlInfo Redis.Get err:%v, tagShareRedisKey is %s", err, rewardsShareRedisKey)
	}
	if card.CardImage == "" {
		card.CardImage = template.HTML(config.GetConfig().ShareConf.RewardsIconUrl)
		card.CardUrl = template.HTML(config.GetConfig().ShareConf.RewardsPageUrl)
		card.CardUrl = template.HTML(fmt.Sprintf("%s%s", config.GetConfig().ShareConf.RewardsPageUrl, language))
		shareActionI18nMap := getI18nMap(c)
		var languageInfoMap map[string]string
		var ok bool
		if languageInfoMap, ok = shareActionI18nMap[language]; !ok {
			languageInfoMap = shareActionI18nMap["en"]
		}
		var cardTitle string
		if shareTitle, ok := languageInfoMap["rewards_share_title"]; ok {
			cardTitle = shareTitle
		}
		card.CardTitle = cardTitle
		card.SiteName = cardTitle
		card.CardDesc = cardTitle
		cardString, err := json.Marshal(card)
		if err == nil {
			redis.GetClient().SetEX(c, rewardsShareRedisKey, cardString, 10*time.Minute).Result()
		}
	}

	// 生成渠道爬虫可爬取的html内容
	return createShareHtml(c, card)

}

func CreatePostShareHtmlInfo(c context.Context, postUuid, language string) string {
	card := model.ShareHtmlCard{
		CardType: BIGCARD,
	}
	postShareRedisKey := cache.GetPostShareHtmlKey(postUuid)
	cardCacheInfo, err := redis.GetClient().Get(c, postShareRedisKey).Result()
	if err == nil {
		if cardCacheInfo != "" {
			err = json.Unmarshal([]byte(cardCacheInfo), &card)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostShareHtmlInfo Unmarshal err: %v, postShareRedisKey is %s", err, postShareRedisKey)
			}
		}
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostShareHtmlInfo Redis.Get err:%v, postShareRedisKey is %s", err, postShareRedisKey)
	}
	if card.CardImage == "" {
		// 获取动态详情信息
		postFormated, err := GetPostInfo(c, "", postUuid, "en", 0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostShareHtmlInfo GetPostInfo err: %v, postUuid is %s", err, postUuid)
			return ""
		}
		if len(postFormated.PicUrls) > 0 {
			card.CardImage = template.HTML(postFormated.PicUrls[0])
		}

		card.CardUrl = template.HTML(fmt.Sprintf("%s%s", config.GetConfig().ShareConf.PostDetailUrl, postUuid))
		card.CardTitle = postFormated.Title
		card.SiteName = postFormated.Title
		card.CardDesc = postFormated.ContentSummary

		cardString, err := json.Marshal(card)
		if err == nil {
			redis.GetClient().SetEX(c, postShareRedisKey, cardString, 10*time.Minute).Result()
		}

		go func(ctx context.Context, postUuid string) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportPostShareLog(c, "", postUuid, nil)
		}(c, postUuid)

	}

	// 生成渠道爬虫可爬取的html内容
	return createShareHtml(c, card)

}

func createShareHtml(c context.Context, shareContent model.ShareHtmlCard) string {
	// 获取模板template
	parseFile, err := template.ParseFiles("./assets/share.html")
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("createShareHtml ParseFiles failed: err:%v", err)
		return ""
	}
	t := template.Must(parseFile, err)
	// 创建一个字节缓冲区
	var buf bytes.Buffer
	err = t.Execute(&buf, shareContent)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("createShareHtml Execute error, error: %v", err)
		return ""
	}
	// 将 *bytes.Buffer 转换成 string
	resultStr := buf.String()

	return resultStr
}

func getI18nMap(c context.Context) map[string]map[string]string {
	var shareActionI18nMap = make(map[string]map[string]string)
	filename := config.GetConfig().ShareConf.I18NFileName
	file, err := os.Open(filename)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getI18nMap os.Open error, error: %v", err)
		return shareActionI18nMap
	}
	defer file.Close()
	// 检查文件内容
	fileInfo, err := file.Stat()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getI18nMap file.Stat error, error: %v", err)
		return shareActionI18nMap
	}
	fileContent := make([]byte, fileInfo.Size())
	_, err = file.Read(fileContent)
	if err != nil && err != io.EOF {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getI18nMap file.Read error, error: %v", err)
		return shareActionI18nMap
	}
	// 解析 JSON 数据到 Person 结构体
	err = json.Unmarshal(fileContent, &shareActionI18nMap)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("getI18nMap json.Unmarshal error, error: %v", err)
		return shareActionI18nMap
	}

	return shareActionI18nMap
}
