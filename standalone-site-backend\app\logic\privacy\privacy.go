package privacy

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

// 用户隐私相关

// UpdateUserSignPrivacy 签署隐私协议
func UpdateUserSignPrivacy(ctx context.Context, intlOpenID string) error {
	err := dao.UpdateUserSignPrivacy(intlOpenID)

	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.UpdateUserSignPrivacy %s", err)
		return errs.NewCustomError(ctx, code.UpdateUserSignProtocolError, "UpdateUserSignPrivacy | Failed to update user privacy agreement signing status, please check")
	}
	// 删除缓存,这里只删除主态的缓存
	userInfoGuestRedisKey := cache.GetUserInfoAllKey(intlOpenID, "guest")
	redis.GetClient().Del(context.Background(), userInfoGuestRedisKey)
	userInfoHostRedisKey := cache.GetUserInfoAllKey(intlOpenID, "host")
	redis.GetClient().Del(context.Background(), userInfoHostRedisKey)
	userBaseRedisKey := cache.GetUserInfoKey(intlOpenID)
	redis.GetClient().Del(context.Background(), userBaseRedisKey)

	return nil
}

// 获取隐私开关
func GetUserPrivacySwitch(c context.Context, intlOpenID string) (*model.UserPrivacySwitch, error) {
	var userPrivacySwitch *model.UserPrivacySwitch
	redisKey := cache.GetUserPrivacySwitchCacheKey(intlOpenID)
	cacheRes, err := redis.GetClient().Get(c, redisKey).Result()
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserPrivacySwitch get cache error, intlOpenID:%s, err: %s", intlOpenID, err)
	} else {
		// 有缓存
		err = json.Unmarshal([]byte(cacheRes), &userPrivacySwitch)
		if err != nil {
			// 反序列化失败
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserPrivacySwitch | Failed to json decode, intlOpenID:%s, err: %s", intlOpenID, err)
			// return nil, errs.NewCustomError(c, code.UserPrivacySwitchJsonDecodeFailed, "GetUserPrivacySwitch | Failed to json decode")
		}
		return userPrivacySwitch, nil
	}
	userPrivacySwitch, err = dao.GetUserPrivacySwitch(intlOpenID)
	if err != nil {
		return nil, errs.NewCustomError(c, code.GetUserPrivacySwitchFailed, "Exception in obtaining user privacy settings configuration data")
	}
	privacySwitchStr, _ := json.Marshal(userPrivacySwitch)
	redis.GetClient().Set(c, redisKey, privacySwitchStr, time.Hour*24) //缓存一天，这个开关基本上不怎么会频繁开启
	return userPrivacySwitch, nil
}

func SetUserPrivacySwitch(c context.Context, intlOpenid string, privacySwitchType constants.PrivacySwitchType, isOff int32) error {
	privacySwitch, err := GetUserPrivacySwitch(c, intlOpenid)
	if err != nil {
		return err
	}
	var updateSwitchMap = make(map[string]int32)
	switch privacySwitchType {
	case constants.ShowMyPosts:
		updateSwitchMap["show_my_posts"] = privacySwitch.ShowMyPosts
		break
	case constants.ShowMyCollection:
		updateSwitchMap["show_my_collection"] = privacySwitch.ShowMyCollection
		break
	case constants.ShowMyFollow:
		updateSwitchMap["show_my_follow"] = privacySwitch.ShowMyFollow
		break
	case constants.ShowMyFans:
		updateSwitchMap["show_my_fans"] = privacySwitch.ShowMyFans
		break
	case constants.ShowMyGameCard:
		updateSwitchMap["show_my_game_card"] = privacySwitch.ShowMyGameCard
		break
	case constants.ReceiveTweetEmail:
		updateSwitchMap["receive_tweet_email"] = privacySwitch.ReceiveTweetEmail
		break
	case constants.MsgCommentNotify:
		updateSwitchMap["msg_comment_notify"] = privacySwitch.MsgCommentNotify
		break
	case constants.MsgLikeNotify:
		updateSwitchMap["msg_like_notify"] = privacySwitch.MsgLikeNotify
		break
	case constants.MsgFollowNotify:
		updateSwitchMap["msg_follow_notify"] = privacySwitch.MsgFollowNotify
		break
	case constants.MsgSystemNotify:
		updateSwitchMap["msg_system_notify"] = privacySwitch.MsgSystemNotify
		break
	case constants.MsgActivityNotify:
		updateSwitchMap["msg_activity_notify"] = privacySwitch.MsgActivityNotify
		break
	case constants.ShowMyComment:
		updateSwitchMap["show_my_comment"] = privacySwitch.ShowMyComment
		break
	}
	if len(updateSwitchMap) > 1 || len(updateSwitchMap) == 0 {
		// 不合法
		return errs.NewCustomError(c, code.UserPrivacySwitchTypeFailed, "SetUserPrivacySwitch | user privacy switch type illegality")
	}
	var updateData = make(map[string]interface{})
	for k, _ := range updateSwitchMap {
		val := 1 - isOff
		updateData[k] = val
		// 主开关关闭 不影响子开关
		//if k == "show_my_game_card" {
		//	myGameCardSubParam := map[string]int32{
		//		"show_daily_info":    val,
		//		"show_outpost_info":  val,
		//		"show_resource_info": val,
		//		"show_nikke_info":    val,
		//	}
		//	// 关闭开关时同步更改数据
		//	shiftyspadPrivacySwitchStr, err := json.Marshal(myGameCardSubParam)
		//	if err != nil {
		//		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SetUserPrivacySwitch | Failed to json marshal, intlOpenID:%s, err: %s", intlOpenid, err)
		//		err = errs.NewCustomError(c, code.ShiftyspadPrivacySwitchJsonErr, "Shiftyspad Privacy Switch json error")
		//		return err
		//	}
		//	updateData["shiftyspad_switch_str"] = shiftyspadPrivacySwitchStr
		//}
	}

	err = dao.UpdateUserPrivacySwitch(intlOpenid, updateData)
	if err != nil {
		return err
	}
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		redisKey := cache.GetUserPrivacySwitchCacheKey(intlOpenid)
		redis.GetClient().Del(context.Background(), redisKey)
	}()
	return nil
}
