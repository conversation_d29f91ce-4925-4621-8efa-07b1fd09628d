package cag_tmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_cag_tmp"
	"github.com/spf13/cast"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/constant"
	"trpc.act.logicial/app/logic/cag_common"
	"trpc.act.logicial/app/redis"
	"trpc.act.logicial/app/service"
	"trpc.act.logicial/app/viewmodel"
)

// ClaimLevelPrizeProc
type ClaimLevelPrizeProc struct {
	userInfo *accountPb.UserAccount
	langType string
}

// ClaimLevelPrize 领取等级奖励
func ClaimLevelPrize(ctx context.Context, req *pb.ClaimLevelPrizeReq) (*pb.ClaimLevelPrizeRsp, error) {
	log.DebugContextf(ctx, "ClaimLevelPrize enter, req: %v", req)
	rsp := &pb.ClaimLevelPrizeRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ClaimLevelPrize get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	proc := &ClaimLevelPrizeProc{
		userInfo: &userAccount,
	}
	// 语言
	proc.langType = metadata.GetLangType(ctx)

	defer func() {
		if nil != err {
			log.ErrorContextf(ctx, "ClaimLevelPrize rsp error:%v", err)
		}
	}()
	if req.Level != 10 && req.Level != 20 && req.Level != 30 {
		err = code.ErrParamError
		return nil, err
	}
	err = proc.Process(ctx, req, rsp)
	if nil != err {
		return nil, err
	}
	return rsp, nil
}

func (p *ClaimLevelPrizeProc) Process(ctx context.Context,
	req *pb.ClaimLevelPrizeReq, rsp *pb.ClaimLevelPrizeRsp) error {
	// 检查活动是时间
	err := cag_common.CheckNewRoleActTime(ctx)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimLevelPrize check act time error:%v, userId: %v", err, p.userInfo.Uid)
		return err
	}
	// 加锁，频率控制以及避免并发
	key := redis.GetCagGetRoleLevelPrizeLockKey(p.userInfo.Uid)
	ok := redis.LockByKey(ctx, key, 10)
	if !ok {
		log.ErrorContextf(ctx, "ClaimLevelPrize LockByKey fail, key: %v", key)
		return code.ErrRequestFrequencyExceededLimitError
	}

	// 判断是否已经领取过奖励
	err = cag_common.HasCagNewRolePrize(ctx, p.userInfo.Uid, constant.CAG_NEW_ROLE_PRIZE_TYPE_LEVEL, int(req.Level))
	if nil != err {
		log.ErrorContextf(ctx, "ClaimLevelPrize hasCagNewRolePrize error:%v", err)
		return err
	}

	// 检查ab等级是否达到领奖条件
	err = checkAbRoleLevel(ctx, p.userInfo.Uid, 5)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimLevelPrize checkAbRoleLevel failed, err:%v, userId: %v", err, p.userInfo.Uid)
		return err
	}

	// 查询用户的cag游戏用户id
	cagOpenId, err := service.GetGameOpenId(ctx, constant.GAMEID_CAG)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimLevelPrize GetCagOpenId error:%v, userId: %v", err, p.userInfo.Uid)
		return err
	}

	if "" == cagOpenId {
		log.DebugContextf(ctx, "ClaimLevelPrize GetCagOpenId cag openid is empty, userId: %v", p.userInfo.Uid)
		// 角色未创建
		return code.ErrHasNoCagRole
	}

	// 查询游戏角色等级
	areaId := ""
	id, ok := config.GetConfig().GameAreaIds[constant.GAMEID_CAG]
	if ok {
		areaId = cast.ToString(id)
	}
	gameInfo := &viewmodel.CagUserGameInfoItem{}
	err = service.GetUserGameInfoByOpenid(ctx, constant.GAMEID_CAG, constant.IDIP_CMD_CAG_GET_USER_INFO, areaId, cagOpenId, gameInfo)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimLevelPrize get cag game user info error:%v, userId: %v, cagOpenId: %v",
			err, p.userInfo.Uid, cagOpenId)
		return err
	}

	// 判断创建角色时间是否在活动开始之后
	err = checkCagRoleRegTime(ctx, gameInfo, p.userInfo.Uid, cagOpenId)
	if nil != err {
		return err
	}

	// 判断角色等级是否满足要求
	reachLevel := true
	switch req.Level {
	case 10:
		if cast.ToInt(gameInfo.Level) < 10 {
			reachLevel = false
		}
	case 20:
		if cast.ToInt(gameInfo.Level) < 20 {
			reachLevel = false
		}
	case 30:
		if cast.ToInt(gameInfo.Level) < 30 {
			reachLevel = false
		}
	default:
		log.ErrorContextf(ctx, "ClaimLevelPrize level invalid, userId: %v, level: %v", p.userInfo.Uid, req.Level)
		return code.ErrParamError
	}
	if !reachLevel {
		log.ErrorContextf(ctx, "ClaimLevelPrize level invalid, userId: %v, cagOpenId: %v, reqLevel: %v, userLevel: %v",
			p.userInfo.Uid, cagOpenId, req.Level, gameInfo.Level)
		return code.ErrNotReachLevel
	}

	// 领取奖励
	err = cag_common.ClaimNewRolePrize(ctx, p.userInfo.Uid, p.userInfo.IntlAccount.OpenId, p.langType, cagOpenId,
		constant.CAG_NEW_ROLE_PRIZE_TYPE_LEVEL, int(req.Level), constant.CagRoleLevelPrizeStatusWaitSend)
	if nil != err {
		log.ErrorContextf(ctx, "ClaimLevelPrize claimNewRolePrize error:%v, userId: %v, cagOpenId: %v",
			err, p.userInfo.Uid, cagOpenId)
		return err
	}
	// 回包
	rsp.Prizes = []*pb.PrizeInfo{
		{
			Type:   cast.ToString(constant.CAG_NEW_ROLE_PRIZE_TYPE_LEVEL),
			HasGet: true,
			Level:  req.Level,
		},
	}
	return nil
}
