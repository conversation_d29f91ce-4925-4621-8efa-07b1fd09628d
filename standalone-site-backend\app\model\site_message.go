package model

type SiteMessage struct {
	*Model
	GameID          string `json:"game_id"`
	AreaID          string `json:"area_id"`
	SendTime        int64  `json:"send_time"`
	Href            string `json:"href"`
	Status          int    `json:"status"` // 状态：1=待发布；2=发送中；3=已发送；4=已撤回
	RegionalScope   string `json:"regional_scope"`
	CreatedData     int    `json:"created_data"` // 是否完整生成用户数据0-未完成1-已完成
	Creator         string `json:"creator"`
	Updater         string `json:"updater"`
	Title           string `json:"title"`
	Type            int    `json:"type"` // 消息类型：1=普通站内信：2=挂件消息, 3=评论气泡发放消息
	AvatarPendantID int64  `json:"avatar_pendant_id"`
	SendCount       int64  `json:"send_count"`        //推送人数
	ReadCount       int64  `json:"read_count"`        //阅读人数人数
	CommentBubbleID int64  `json:"comment_bubble_id"` // 评论气泡id
}

type RegionalScope struct {
	IntlGameId           string `json:"intl_game_id"`
	PushScope            int    `json:"push_scope"`              // 站内信消息推送类型：1=全量用户推送；2=用户号码包推送
	NumberPackageFileUrl string `json:"number_package_file_url"` // 号码包文件地址
}

type SiteMessageLanguage struct {
	*Model
	GameID      string `json:"game_id"`
	AreaID      string `json:"area_id"`
	MsgID       int64  `json:"msg_id"`
	Title       string `json:"title"`
	MessageBody string `json:"message_body"`
	Language    string `json:"language"`
	ImgUrl      string `json:"img_url"`
	Creator     string `json:"creator"`
	Updater     string `json:"updater"`
}

type UserJoinCdkey struct {
	IntlOpenid string `json:"intl_openid"`
	Cdkey      string `json:"cdkey"`
}

type SiteMessageKafkaData struct {
	MsgID               int64            `json:"msg_id"`
	IntlOpenidList      []string         `json:"intl_openid_list"`
	PushType            int              `json:"push_type"`
	IntlOpenidJoinCdkey []*UserJoinCdkey `json:"intl_openid_join_cdkey"`
}

type SiteMessageWithLang struct {
	SiteMessage
	Language []*SiteMessageLanguage `json:"language" gorm:"-"`
}

type SiteMessageWithLangMap struct {
	*SiteMessage
	LanguageMap map[string]*SiteMessageLanguage `json:"language_map" gorm:"-"`
}

func (s *SiteMessageLanguage) TableName() string {
	return "p_site_message_language"
}
func (s *SiteMessage) TableName() string {
	return "p_site_message"
}
