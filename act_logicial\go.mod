module trpc.act.logicial

go 1.17

require (
	cloud.google.com/go/bigquery v1.50.0
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/ams v0.2.2
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/database v0.0.16-beta2
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/deltaverse v0.0.6
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs v1.0.1
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter v0.0.14
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient v0.0.27
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/intlgame v0.0.14
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata v1.0.16
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/report v1.0.5
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/tglog v0.0.7-beta
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/util v1.1.5
	git.code.oa.com/polaris/polaris-go v0.12.13
	git.code.oa.com/trpc-go/trpc-config-tconf v0.1.10
	git.code.oa.com/trpc-go/trpc-database/kafka v0.2.7
	git.code.oa.com/trpc-go/trpc-database/localcache v0.1.14
	git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.6
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.4
	git.code.oa.com/trpc-go/trpc-filter/validation v0.1.3
	git.code.oa.com/trpc-go/trpc-go v0.11.1
	git.code.oa.com/trpc-go/trpc-log-cls v0.0.0-**************-3be80a92c1fc
	git.code.oa.com/trpc-go/trpc-metrics-prometheus v0.1.8
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.3.3
	git.code.oa.com/trpc-go/trpc-opentracing-skywalking v0.2.0
	git.code.oa.com/trpcprotocol/publishing_marketing/account v1.1.31
	git.code.oa.com/trpcprotocol/publishing_marketing/game v1.1.29
	git.code.oa.com/trpcprotocol/publishing_marketing/game_ams v1.1.22
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_address v1.1.20
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_card v1.1.1
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_common v1.1.78
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_creatorhub v1.1.6
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_dragonact0704 v1.1.34
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_lottery v1.1.16
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_mission v1.1.7
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_nikke_tmp v1.1.71
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_reservation v1.1.24
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_share v1.1.26
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_signin v1.1.7
	git.code.oa.com/trpcprotocol/publishing_marketing/logicial_vote v1.1.14
	git.code.oa.com/trpcprotocol/publishing_marketing/present_ams v1.1.22
	git.woa.com/sec-api/go/scurl v0.2.9
	git.woa.com/trpcprotocol/publishing_application/cnbot_operations v1.1.64
	git.woa.com/trpcprotocol/publishing_marketing/account_launcher v1.1.4
	git.woa.com/trpcprotocol/publishing_marketing/account_team v1.1.19
	git.woa.com/trpcprotocol/publishing_marketing/account_wegame v1.1.1
	git.woa.com/trpcprotocol/publishing_marketing/aigc_sentiment v1.1.3
	git.woa.com/trpcprotocol/publishing_marketing/game_df v1.1.24
	git.woa.com/trpcprotocol/publishing_marketing/game_hok v1.1.3
	git.woa.com/trpcprotocol/publishing_marketing/game_x1 v1.1.3
	git.woa.com/trpcprotocol/publishing_marketing/logicial_df_account_tranfer v1.1.25
	git.woa.com/trpcprotocol/publishing_marketing/logicial_df_bhd v1.1.8
	git.woa.com/trpcprotocol/publishing_marketing/logicial_df_global_challenge v1.1.35
	git.woa.com/trpcprotocol/publishing_marketing/logicial_df_pve_speed v1.1.2
	git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp v1.1.54
	git.woa.com/trpcprotocol/publishing_marketing/logicial_exobrn_tmp v1.1.1
	git.woa.com/trpcprotocol/publishing_marketing/logicial_hok_tmp v1.1.56
	git.woa.com/trpcprotocol/publishing_marketing/logicial_lip_points v1.1.2
	git.woa.com/trpcprotocol/publishing_marketing/logicial_nba_tmp v1.1.1
	git.woa.com/trpcprotocol/publishing_marketing/logicial_network_agent v1.1.4
	git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_2nd_anniversary v1.1.3
	git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_mbti v1.1.1
	git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_music v1.1.2
	git.woa.com/trpcprotocol/publishing_marketing/logicial_nikkevote0615 v1.1.21
	git.woa.com/trpcprotocol/publishing_marketing/logicial_record v1.1.12
	git.woa.com/trpcprotocol/publishing_marketing/logicial_share_team v1.1.30
	git.woa.com/trpcprotocol/publishing_marketing/logicial_signup v1.1.2
	git.woa.com/trpcprotocol/publishing_marketing/logicial_taris_invitation v1.1.17
	git.woa.com/trpcprotocol/publishing_marketing/logicial_taristmp v1.1.42
	git.woa.com/trpcprotocol/publishing_marketing/logicial_team v1.1.10
	git.woa.com/trpcprotocol/publishing_marketing/logicial_warframe_tmp v1.1.20
	git.woa.com/wegame_app_go/common/trpc-config-local v0.1.2
	github.com/Shopify/sarama v1.29.1
	github.com/fsnotify/fsnotify v1.5.4
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-sql-driver/mysql v1.6.0
	github.com/google/uuid v1.3.0
	github.com/olivere/elastic/v7 v7.0.32
	github.com/pkg/errors v0.9.1
	github.com/shopspring/decimal v1.3.1
	github.com/spf13/cast v1.5.0
	github.com/spf13/viper v1.13.0
	go.uber.org/automaxprocs v1.4.0
	golang.org/x/net v0.10.0
	google.golang.org/protobuf v1.30.0
	gorm.io/gorm v1.24.1
	gorm.io/plugin/soft_delete v1.2.0
)

require (
	cloud.google.com/go/compute/metadata v0.2.3 // indirect
	cloud.google.com/go/storage v1.30.0 // indirect
	git.code.oa.com/trpcprotocol/publishing_marketing/account_intlgame v1.1.29 // indirect
	git.woa.com/trpcprotocol/publishing_marketing/logicial_cag_tmp v1.1.6 // indirect
	git.woa.com/trpcprotocol/publishing_marketing/logicial_point v1.1.3 // indirect
	github.com/apache/arrow/go/v11 v11.0.0 // indirect
	github.com/apache/thrift v0.16.0 // indirect
	github.com/goccy/go-json v0.9.11 // indirect
	github.com/googleapis/enterprise-certificate-proxy v0.2.3 // indirect
	github.com/klauspost/asmfmt v1.3.2 // indirect
	github.com/klauspost/cpuid/v2 v2.0.9 // indirect
	github.com/minio/asm2plan9s v0.0.0-**************-cdd76441f9d8 // indirect
	github.com/minio/c2goasm v0.0.0-**************-36a3d3bbc4f3 // indirect
	github.com/pierrec/lz4/v4 v4.1.15 // indirect
	github.com/zeebo/xxh3 v1.0.2 // indirect
	golang.org/x/mod v0.8.0 // indirect
	golang.org/x/tools v0.6.0 // indirect
)

require (
	cloud.google.com/go v0.110.0 // indirect
	cloud.google.com/go/compute v1.19.0 // indirect
	cloud.google.com/go/iam v0.13.0 // indirect
	// cloud.google.com/go v0.100.2 // indirect
	// cloud.google.com/go/compute v1.6.1 // indirect
	git.code.oa.com/devsec/protoc-gen-secv v0.3.4 // indirect
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/constants v0.0.7 // indirect
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/metric v0.0.5 // indirect
	git.code.oa.com/trpc-go/trpc v0.1.2 // indirect
	git.code.oa.com/trpc-go/trpc-selector-dsn v0.1.3 // indirect
	git.code.oa.com/trpcprotocol/tconf/tconfserver v1.1.2 // indirect
	git.woa.com/datacenter/tgloggo v1.1.5 // indirect
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/polaris/polaris-go/v2 v2.6.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.3 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.3 // indirect
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	git.woa.com/trpc-go/tnet v0.0.6 // indirect
	git.woa.com/trpcprotocol/publishing_application/standalonesite_user v1.2.9 // indirect
	git.woa.com/wegame_app_global/common/config-tconf v0.1.3 // indirect
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/RussellLuo/timingwheel v0.0.0-20191022104228-f534fd34a762 // indirect
	github.com/SkyAPM/go2sky v1.3.0 // indirect
	github.com/andybalholm/brotli v1.0.4 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/eapache/go-resiliency v1.2.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20180814174437-776d5712da21 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/go-playground/form v3.1.4+incompatible // indirect
	github.com/gofrs/uuid v4.3.1+incompatible // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/flatbuffers v2.0.8+incompatible // indirect
	github.com/google/go-cmp v0.5.9 // indirect
	github.com/googleapis/gax-go/v2 v2.7.1 // indirect
	github.com/hashicorp/errwrap v1.0.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-uuid v1.0.2 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.0.0 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.2 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jonboulle/clockwork v0.3.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.15.9 // indirect
	github.com/konsorten/go-windows-terminal-sequences v1.0.3 // indirect
	github.com/lestrrat-go/file-rotatelogs v2.4.0+incompatible // indirect
	github.com/lestrrat-go/strftime v1.0.4 // indirect
	github.com/magiconair/properties v1.8.6 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.1 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/panjf2000/ants/v2 v2.4.6 // indirect
	github.com/pelletier/go-toml v1.9.5 // indirect
	github.com/pelletier/go-toml/v2 v2.0.5 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/prometheus/client_golang v1.11.1 // indirect
	github.com/prometheus/client_model v0.2.0 // indirect
	github.com/prometheus/common v0.26.0 // indirect
	github.com/prometheus/procfs v0.6.0 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/rifflock/lfshook v0.0.0-20180920164130-b9218ef580f5 // indirect
	github.com/sirupsen/logrus v1.6.0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/afero v1.8.2 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/stretchr/testify v1.8.4 // indirect
	github.com/subosito/gotenv v1.4.1 // indirect
	github.com/tencentcloud/tencentcloud-cls-sdk-go v1.0.11 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.28.0 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.0.2 // indirect
	github.com/xdg-go/stringprep v1.0.2 // indirect
	go.opencensus.io v0.24.0 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.6.0 // indirect
	go.uber.org/zap v1.21.0 // indirect
	golang.org/x/crypto v0.19.0 // indirect
	golang.org/x/oauth2 v0.6.0 // indirect
	golang.org/x/sync v0.1.0 // indirect
	golang.org/x/sys v0.17.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2 // indirect
	google.golang.org/appengine v1.6.7 // indirect
	google.golang.org/genproto v0.0.0-20230330154414-c0448cd141ea // indirect
	google.golang.org/grpc v1.54.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.4.3 // indirect
	skywalking.apache.org/repo/goapi v0.0.0-20210401062122-a049ca15c62d // indirect

)

require (
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/captcha v0.0.10-beta
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/datadump v0.0.2
	// indirect
	git.code.oa.com/trpcprotocol/publishing_marketing/present v1.1.52
	git.woa.com/gpts/baselib v0.0.0-20221219051650-998310dc994b
	git.woa.com/trpcprotocol/publishing_application/lipass_common v1.1.12
	git.woa.com/trpcprotocol/publishing_application/lipass_points v1.2.46
	git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics v1.3.3
	git.woa.com/trpcprotocol/publishing_marketing/aigc_mail v1.1.10
	git.woa.com/trpcprotocol/publishing_marketing/logicial_exoborne_friends_invite v1.1.14
	git.woa.com/trpcprotocol/publishing_marketing/logicial_userdata v1.1.3
	git.woa.com/trpcprotocol/publishing_marketing/present_present_v2 v1.1.48
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	google.golang.org/api v0.114.0
)

replace google.golang.org/protobuf v1.30.0 => google.golang.org/protobuf v1.25.0
