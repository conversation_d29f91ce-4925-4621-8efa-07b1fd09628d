package nikketmp

import (
	"context"
	"encoding/json"
	"fmt"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/model/nikke"
)

// IndependentRecordGift 独立站礼包记录
func IndependentRecordGift(ctx context.Context, presentId string, roleInfo *gamePb.RoleInfo) (bool, error) {
	var isFirst bool
	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return isFirst, err
	}
	langType := metadata.GetLangType(ctx)
	// 是否已获取礼包
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeIndependentGiftRecordModel{}.TableName()).
		Where("uid = ? and account_type = ?", userAccount.Uid, int(userAccount.AccountType)).
		Where("present_id = ?", presentId)
	var count int64
	if err = tx.Count(&count).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return isFirst, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"IndependentRecordGift Count db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		return isFirst, nil
	}
	// 首次记录
	isFirst = true
	// 记录礼包信息
	info := buildRoleInfo(roleInfo)
	roleInfoStr, err := json.Marshal(info)
	if err != nil {
		return isFirst, errs.NewCustomError(ctx, code.CommonParamJsonError, fmt.Sprintf(
			"roleInfo json Marshal error, err:[%v]", err))
	}
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeIndependentGiftRecordModel{}.TableName()).
		Create(&nikke.NikkeIndependentGiftRecord{
			UID:         userAccount.Uid,
			AccountType: int(userAccount.AccountType),
			RoleInfo:    string(roleInfoStr),
			PresentID:   presentId,
			LangType:    langType,
			Status:      0, // 未发货
		}).Error; err != nil {
		return isFirst, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"IndependentRecordGift Create db error, \t [Error]:{%v} ", err)
	}
	return isFirst, nil

}
