// Package record 记录
package record

import (
	"context"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	utilModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"trpc.act.logicial/app/code"
	baseLogic "trpc.act.logicial/app/logic/base"
	"trpc.act.logicial/app/model/base"
)

const (
	// RecordTagKey 记录标签Key
	RecordTagKey = "record_tag"
	// RecordTagStatus_Initial 初始状态
	RecordTagStatus_Initial = 0
	// RecordTagStatus_Finish 完成状态
	RecordTagStatus_Finish = 1
)

// RecordUniqueTag 记录唯一标签
func RecordUniqueTag(ctx context.Context, sourceId string, tagId string, status int32) error {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	var baseLogTableModel base.BaseTotalModel
	var baseLogTable base.BaseTotalLogModel
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   sourceId,
		"storage_key":  RecordTagKey,
	}
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLogTableModel.TableName())
	dbErr := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Where(condition).Assign(
		base.BaseTotalLogModel{
			Fday:        time.Now(),
			Uid:         userAccount.Uid,
			AccountType: int32(userAccount.AccountType),
			StorageKey:  RecordTagKey,
			FsourceId:   sourceId,
			TagId:       tagId,
			Status:      int(status),
		}).FirstOrCreate(&baseLogTable).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RecordUniqueTag db error, \t [Error]:{%v} ", dbErr.Error())
		return err
	}
	return nil
}

// DoRecordTag 记录标签
func DoRecordTag(ctx context.Context, sourceId string, tagId string, status int32) (err error) {
	// 查询最近一天记录
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   sourceId,
		"storage_key":  RecordTagKey,
		"tag_id":       tagId,
	}
	hasData, _, errB := baseLogic.GetData(ctx, condition)
	if errB != nil {
		err = errB
		return
	}
	if hasData {
		err = errs.NewCustomError(ctx, code.HasRecordTag, "has record tag")
		return
	}
	createData := base.BaseTotalLogModel{
		Fday:        time.Now(),
		Uid:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		StorageKey:  RecordTagKey,
		FsourceId:   sourceId,
		TagId:       tagId,
		Status:      int(status),
	}
	var baseLogTable base.BaseTotalModel
	accountTableName, err := utilModel.GetTableNameWithAccount(ctx, &userAccount, baseLogTable.TableName())
	dbErr := DB.DefaultConnect().WithContext(ctx).Table(accountTableName).Create(&createData).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	return
}

// HasRecordTagList 已记录标签列表
func HasRecordTagList(ctx context.Context, sourceId string, onlyFinish bool) (tagIds []string, err error) {
	// 查询最近一天记录
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   sourceId,
		"storage_key":  RecordTagKey,
	}
	if onlyFinish {
		condition["status"] = RecordTagStatus_Finish
	}
	allData, errB := baseLogic.GetAllData(ctx, condition)
	if errB != nil {
		err = errB
		return
	}
	tagIds = make([]string, 0)
	for _, item := range allData {
		tagIds = append(tagIds, item.TagId)
	}
	return
}

// CheckHasRecordTag 检查是否记录某个标签
func CheckHasRecordTag(ctx context.Context, sourceId string, tagId string, onlyFinish bool) (hasRecord bool,
	err error) {
	// 查询最近一天记录
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   sourceId,
		"storage_key":  RecordTagKey,
		"tag_id":       tagId,
	}
	hasData, lastData, errB := baseLogic.GetData(ctx, condition)
	if errB != nil {
		err = errB
		return
	}
	if !hasData {
		return
	}
	if !onlyFinish || lastData.Status == RecordTagStatus_Finish {
		hasRecord = true
	}
	return
}
