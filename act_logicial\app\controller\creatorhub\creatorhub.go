// Package creatorhub creatorhub专项方法
package creatorhub

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_creatorhub"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	addressModel "trpc.act.logicial/app/model/address"
	userModel "trpc.act.logicial/app/model/creatorhub_user"
)

// CreatorhubImpl 结构体
type CreatorhubImpl struct{}

// AddCreatorhubLog 添加到Creatorhub礼包记录
func (s *CreatorhubImpl) AddCreatorhubLog(ctx context.Context, req *pb.AddCreatorhubLogReq) (
	rsp *pb.AddCreatorhubLogRsp,
	err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var PUBGMData addressModel.PUBGMAdressData
	if errM := DB.DefaultConnect().WithContext(ctx).Table(PUBGMData.TableName()).Where("uid = ?", userAccount.Uid).
		Where("account_type = ?",
			userAccount.AccountType).Where("game_id = ?", userAccount.IntlAccount.GameId).First(&PUBGMData).Error; errM != nil {
		if errors.Is(errM, gorm.ErrRecordNotFound) {
		} else {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", errM.Error())
			return
		}
	}
	timeStamp := time.Now().Unix()
	var UserRewardInfoList []userModel.UserRewardInfo
	proxy := presentPb.NewPresentClientProxy()

	for _, v := range req.PresentList {
		reqPresentName := &presentPb.GetPresentNameReq{
			EncryptId:  v.PresentId,
			EncryptCid: v.PresentCid,
		}
		nameRsp, err1 := proxy.GetPresentName(ctx, reqPresentName)
		if err1 != nil {
			errs.NewCustomError(ctx, code.GetPresentNameError, "error, \t [Error]:{%v} ", err1.Error())
			return
		}
		UserRewardInfoData := userModel.UserRewardInfo{
			GameID:          req.GameId,
			AreaID:          req.AreaId,
			Uid:             userAccount.IntlAccount.OpenId,
			ProjectID:       req.ProjectId,
			RewardStr:       nameRsp.Name,
			RewardTime:      timeStamp,
			DeliveryCountry: PUBGMData.Country,
			DeliveryAddr:    PUBGMData.Address,
			DeliveryZip:     PUBGMData.ZipCode,
			DeliveryPhone:   PUBGMData.PhoneNumber,
			DeliveryReciver: PUBGMData.Name,
		}
		UserRewardInfoList = append(UserRewardInfoList, UserRewardInfoData)
	}
	db := DB.SelectConnect("db_creatorhub_extend").Debug()
	dbErr := db.Create(&UserRewardInfoList).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	rsp = &pb.AddCreatorhubLogRsp{}
	return
}

// UpdateCreatorhubLogAddres 修改Creatorhub礼包地址信息
func (s *CreatorhubImpl) UpdateCreatorhubLogAddres(ctx context.Context, req *pb.UpdateCreatorhubLogAddresReq) (
	rsp *pb.UpdateCreatorhubLogAddresRsp, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var PUBGMData addressModel.PUBGMAdressData
	if errM := DB.DefaultConnect().WithContext(ctx).Table(PUBGMData.TableName()).Where("uid = ?", userAccount.Uid).
		Where("account_type = ?",
			userAccount.AccountType).Where("game_id = ?", userAccount.IntlAccount.GameId).First(&PUBGMData).Error; errM != nil {
		if errors.Is(errM, gorm.ErrRecordNotFound) {
			rsp = &pb.UpdateCreatorhubLogAddresRsp{
				HasUpdateCreatorlog: false,
			}
			return
		} else {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", errM.Error())
			return
		}
	}
	userRewardInfoData := map[string]interface{}{
		"delivery_country": PUBGMData.Country,
		"delivery_addr":    PUBGMData.Address,
		"delivery_zip":     PUBGMData.ZipCode,
		"delivery_phone":   PUBGMData.PhoneNumber,
		"delivery_reciver": PUBGMData.Name,
	}
	db := DB.SelectConnect("db_creatorhub_extend").Debug()
	dbErr := db.Table("user_reward_info").Where("uid = ?", userAccount.IntlAccount.OpenId).Updates(userRewardInfoData).
		Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error())
		return
	}
	rsp = &pb.UpdateCreatorhubLogAddresRsp{
		HasUpdateCreatorlog: true,
	}
	return
}

// ResponseInfo 返回数据
type ResponseInfo struct {
	Ret  int `json:"ret"`
	Msg  int `json:"msg"`
	Data struct {
		UserList []struct {
			UserInfo struct {
				ChannelID  int    `json:"channelid"`
				IsAdult    int32  `json:"is_adult"`
				UserID     string `json:"user_id"`
				ShowName   string `json:"show_name"`
				PictureUrl string `json:"picture_url"`
			} `json:"user_info"`
		} `json:"user_list"`
	} `json:"data"`
}

// GetUserIsAdult 查询玩家是否成年
func (s *CreatorhubImpl) GetUserIsAdult(ctx context.Context, req *pb.GetUserIsAdultReq) (rsp *pb.GetUserIsAdultRsp,
	err error) {
	var conf = config.GetConfig()
	var Host = conf.PubgmHttpHost
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	userIDList := []string{userAccount.IntlAccount.OpenId}
	postData := map[string]interface{}{
		"user_id_list": userIDList,
	}
	commonParmasData := map[string]interface{}{
		"game_id": req.GameId,
		"area_id": req.AreaId,
	}
	commonParmas, _ := json.Marshal(commonParmasData)
	url := fmt.Sprintf("%v%v", Host, "/wandapi/v1/pubgmproj/batch_get_user_info")
	optionOne := httpclient.ClientOption{
		URL: url,
		// Timeout: 2 * time.Second,
		Header: map[string]string{
			"Content-Type":    "application/json",
			"X-Common-Params": string(commonParmas),
		},
		Type:     "POST",
		PostData: postData,
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	if resultOption.RequestError != nil {
		// 请求失败
		err = errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"http error, \t [Error]:{%v} ", url)
		return
	}
	response := resultOption.Result
	var respData ResponseInfo
	jsonErr := json.Unmarshal([]byte(response), &respData)
	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.JsonParseError,
			"parse result,result=%v, \t [Error]:{%v} ", response, jsonErr)
		return
	}
	if respData.Ret != 0 {
		err = errs.NewCustomError(ctx, code.PubgHttpError, "ret = %v ", respData.Ret)
		return
	}
	if len(respData.Data.UserList) == 0 {
		err = errs.NewCustomError(ctx, code.PubgHttpError, "ret = %v ", respData.Ret)
		return
	}
	if respData.Data.UserList[0].UserInfo.IsAdult != 1 {
		err = errs.NewCustomError(ctx, code.NotAdult,
			"not adult=%v", respData.Data.UserList[0].UserInfo.IsAdult)
		return
	}
	rsp = &pb.GetUserIsAdultRsp{
		IsAdult: true,
	}
	// 返回[]byte类型，之后再做转换
	return
}
