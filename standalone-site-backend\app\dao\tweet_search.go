package dao

import (
	"context"
	"encoding/json"
	"fmt"

	ES "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/elasticsearch"
	"git.code.oa.com/trpc-go/trpc-go/log"
	es7 "github.com/olivere/elastic/v7"
	"trpc.publishing_application.standalonesite/app/constants"
)

func EsQuery(indexName string, queryItem es7.Query, sortBys []es7.Sorter, lastSortValue []interface{}, size int64) (*es7.SearchResult, error) {
	ctx := context.Background()

	esObj := ES.SelectConnect("es_standalonesite").Search().Index(indexName). // 设置索引名
											Query(queryItem).   // 设置查询条件
											SortBy(sortBys...). // 设置排序字段，根据Created字段升序排序，第二个参数false表示逆序
											Size(int(size)).    // 设置分页参数 - 每页大小
											Pretty(true)        // 查询结果返回可读性较好的JSON格式
	if len(lastSortValue) > 0 {
		esObj = esObj.SearchAfter(lastSortValue...) // 搜索分页的游标
	}
	searchResult, err := esObj.Do(ctx) // 执行请求

	// log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("Elastic Search EsQuery result:%v", searchResult)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search EsQuery error:%v", err)
		return nil, err
	}
	return searchResult, nil
}

func EsQueryAll(indexName string, queryItem es7.Query, sortBys []es7.Sorter) (*es7.SearchResult, error) {
	ctx := context.Background()
	searchResult, err := ES.SelectConnect("es_standalonesite").Search().Index(indexName). // 设置索引名
												Query(queryItem).   // 设置查询条件
												SortBy(sortBys...). // 设置排序字段，根据Created字段升序排序，第二个参数false表示逆序
												Pretty(true).       // 查询结果返回可读性较好的JSON格式
												Do(ctx)             // 执行请求

	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("Elastic Search EsQuery result:%v", searchResult)
	if err != nil {
		fmt.Println(err.Error())
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search EsQuery error:%v", err)
		return nil, err
	}
	return searchResult, nil
}

// 根据id来更新文档某些字段
func EsUpdateDoc(indexName string, itemId string, doc map[string]interface{}) (bool, error) {
	_, err := ES.SelectConnect("es_standalonesite").Update().
		Index(indexName).        // 设置索引名称
		Id(itemId).              // 设置文档id
		Doc(doc).                // 更新Title="新的文章标题"，支持传入键值结构
		Do(context.Background()) // 执行请求，需要传入一个上下文对象

	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search UpdateDoc error:%v, indexName:%s, itemId:%s, doc:%v", err, indexName, itemId, doc)
		return false, err
	}
	return true, nil
}

// 根据条件来更新文档某些字段
func EsUpdateDocByCondition(indexName string, queryItem es7.Query, scriptStr string, doc map[string]interface{}) (bool, error) {
	script := es7.NewScript(scriptStr).Params(doc)
	_, err := ES.SelectConnect("es_standalonesite").UpdateByQuery().
		Index(indexName).        // 设置索引名称
		Query(queryItem).        // 设置搜索条件
		Script(script).          // 执行更新脚本
		Do(context.Background()) // 执行请求，需要传入一个上下文对象

	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search EsUpdateDocByCondition error:%v", err)
		return false, err
	}
	return true, nil
}

// 批量新增文档
func EsBulkPushDoc(indexName string, itemKey string, docs []map[string]interface{}) (bool, error) {
	ctx := context.Background()
	req := ES.SelectConnect("es_standalonesite").Bulk().Index(indexName)
	for _, item := range docs {
		var doc *es7.BulkIndexRequest
		if itemId, isOk := item[itemKey]; isOk {
			itemIdStr := fmt.Sprintf("%v", itemId)
			doc = es7.NewBulkIndexRequest().Id(itemIdStr).Doc(item)
		} else {
			doc = es7.NewBulkIndexRequest().Doc(item)
		}
		req.Add(doc)
	}
	if req.NumberOfActions() < 0 {
		return true, nil
	}
	// var result *es7.BulkResponse
	if result, err := req.Do(ctx); err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search BulkPushDoc error:%v", result)
		return false, err
	}
	return true, nil
}

func EsDelDoc(indexName, itemId string) error {
	ctx := context.Background()
	_, err := ES.SelectConnect("es_standalonesite").Delete().
		Index(indexName).
		Id(itemId).
		Do(ctx)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search DelDoc error:%v,indexName:%s, itemId:%s", err, indexName, itemId)
		return err
	}
	return nil
}

// 新增/删除后新增覆盖文档
func EsPutDoc(name string, itemId string, doc interface{}) (bool, error) {
	ctx := context.Background()
	esDocBody, err := json.Marshal(doc)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search PutDoc Marshal error:%v", err)
	}
	esDocBodyStr := string(esDocBody)
	if itemId == "" {
		_, err = ES.SelectConnect("es_standalonesite").Index().
			Index(name).            // 设置索引名称
			BodyJson(esDocBodyStr). // 指定前面声明的微博内容
			Do(ctx)                 // 执行请求，需要传入一个上下文对象
	} else {
		_, err = ES.SelectConnect("es_standalonesite").Index().
			Index(name).            // 设置索引名称
			Id(itemId).             // 设置文档id
			BodyJson(esDocBodyStr). // 指定前面声明的微博内容
			Do(ctx)                 // 执行请求，需要传入一个上下文对象
	}
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search PutDoc error:%v", err)
		return false, err
	}

	return true, nil
}

func EsRefreshIndex(indexName string) error {
	ctx := context.Background()
	_, err := ES.SelectConnect("es_standalonesite").Refresh().
		Index(indexName).
		Do(ctx)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search Refresh index error:%v, index: %s", err, indexName)
		return err
	}
	return nil
}
