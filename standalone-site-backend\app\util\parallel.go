package util

import (
	"fmt"
	"runtime/debug"
	"sync"

	"github.com/panjf2000/ants/v2"
	"github.com/thoas/go-funk"
)

const (
	StackLen = 4096
)

// 并行执行任务, 所有任务执行完成返回
// 返回error: 任一task 存在panic或返回not nil err, 取第一个panic或not nil err作为Parallel error返回
// 参数size: 允许开启最大goroutine数量, 达到最大数量时，等待其他task执行完成释放goroutine后继续执行其他task
// 参数tasks: 任务列表
func Parallel(size int, tasks []func() error) error {
	var (
		wg   sync.WaitGroup
		once sync.Once
		// err: Parallel error
		err error
	)
	if size <= 0 {
		return fmt.Errorf("size must greather than 0")
	}
	if len(tasks) == 0 {
		return fmt.Errorf("tasks' length must greather than 0")
	}
	pool, perr := ants.NewPool(size,
		// Deal task panic
		ants.WithPanicHandler(func(i interface{}) {
			once.Do(func() {
				err = fmt.Errorf("%v", i)
			})
		}))
	if perr != nil {
		// perr: ants.NewPool error
		return perr
	}
	// Release pool
	defer pool.Release()
	for i := range tasks {
		idx := i
		task := tasks[idx]
		wg.Add(1)
		pool.Submit(func() {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					s := debug.Stack()
					length := funk.MinInt([]int{StackLen, len(s)})
					panic(fmt.Sprintf("Parallel task[%v] panic: \n%s", idx, string(s[:length])))
				}
			}()
			// terr: task error
			terr := task()
			if terr != nil {
				once.Do(func() {
					err = terr
				})
			}
		})
	}
	wg.Wait()
	return err
}
