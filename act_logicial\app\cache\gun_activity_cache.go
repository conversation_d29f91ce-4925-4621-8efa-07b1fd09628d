package cache

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-database/localcache"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/model/df_activity"
)

var GunActivityCache localcache.Cache // 拼枪活动配置缓存

// 初始化内容标签缓存
func init() {
	GunActivityCache = localcache.New(localcache.WithCapacity(1000),
		localcache.WithExpiration(3600))
}

// GetGunActivityFromCache 从缓存获取活动信息
func GetGunActivityFromCache(ctx context.Context, key string) (*df_activity.GunActivityConf, error) {
	val, exist := GunActivityCache.Get(key)
	if !exist {
		return nil, nil
	}
	// 缓存里找到
	res, ok := val.(*df_activity.GunActivityConf)
	if ok {
		log.DebugContextf(ctx, "GetGunActivityFromCache from cache, key: %v",
			key)
		return res, nil
	}
	log.ErrorContextf(ctx, "GetGunActivityFromCache cache not string, key: %v", key)
	GunActivityCache.Del(key)
	return nil, code.ErrSystemError
}

// SetGunActivityToCache 保存活动信息到缓存
func SetGunActivityToCache(ctx context.Context, key string, val *df_activity.GunActivityConf) {
	ok := GunActivityCache.SetWithExpire(key, val, 300)
	if !ok {
		log.ErrorContextf(ctx, "SetGunActivityToCache fail, key: %v", key)
	}
}
