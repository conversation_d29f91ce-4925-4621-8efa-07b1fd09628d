// Package df_bhd TODO
package df_bhd

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	deltaversePb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/deltaverse"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gameAmsPb "git.code.oa.com/trpcprotocol/publishing_marketing/game_ams"
	LotteryPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_lottery"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_bhd"
	redisOrgin "github.com/go-redis/redis/v8"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/global"
	model "trpc.act.logicial/app/model/df_bhd"
)

// OboRankTotalHttp TODO
type OboRankTotalHttp struct {
	Data OborankTotalType `json:"data"`
}

// OboRanklndHttp TODO
type OboRanklndHttp struct {
	Data RankInfoItem `json:"data"`
}

// OborankTotalType TODO
type OborankTotalType struct {
	Dtstatdate         string `json:"dtstatdate"`
	Izoneareaid        string `json:"izoneareaid"`
	RankListInfo       string `json:"rank_list_info"`
	TotalRankPlayerNum int64  `json:"total_rank_player_num"`
}

// RankInfoItem TODO
type RankInfoItem struct {
	Vopenid      string `json:"vopenid"`
	Roomid       string `json:"roomid"`
	Izoneareaid  string `json:"izoneareaid"`
	Izoneid      int32  `json:"izoneid"`
	Playtime     string `json:"playtime"`
	Dteventtime  string `json:"dteventtime"`
	PlaytimeRank int64  `json:"playtime_rank"`
	Memberid1    string `json:"memberid1"`
	Memberid2    string `json:"memberid2"`
	Memberid3    string `json:"memberid3"`
	Memberid4    string `json:"memberid4"`
}

// RankInfoListItem TODO
type RankInfoListItem struct {
	Vopenid      string `json:"vopenid"`
	Roomid       string `json:"roomid"`
	Izoneareaid  string `json:"izoneareaid"`
	Playtime     string `json:"playtime"`
	Izoneid      string `json:"izoneid"`
	Dteventtime  string `json:"dteventtime"`
	PlaytimeRank string `json:"playtime_rank"`
	Memberid1    string `json:"memberid1"`
	Memberid2    string `json:"memberid2"`
	Memberid3    string `json:"memberid3"`
	Memberid4    string `json:"memberid4"`
}

// IdipInfoItem TODO
type IdipInfoItem struct {
	Name   string
	Avatar string
}

// MapIdMap TODO
var MapIdMap = []string{
	"10001", "10003", "10006", "10007", "10005",
	"10001", "10003", "10006", "10007", "10005",
}

var loc = time.FixedZone("UTC+0", 0)

// SyncBHDSpeedRank 同步黑鹰竞速排行数据
func SyncBHDSpeedRank(ctx context.Context) (err error) {
	scheduleCtx := context.Background()

	// 先判断是否有数据
	dateStr, err := GetDateStr(scheduleCtx)
	if err != nil {
		return
	}

	for index, item := range MapIdMap {
		rankType := "total"
		if index > 4 {
			rankType = "sol"
		}
		SyncBHDSpeedRankItem(scheduleCtx, item, dateStr, rankType)
	}
	return
}

// SyncBHDSpeedRankItem TODO
func SyncBHDSpeedRankItem(ctx context.Context, mapId string, dateStr string, rankType string) (err error) {
	scheduleCtx := context.Background()
	now := time.Now()
	endTime := time.Date(2025, 3, 27, 23, 59, 59, 0, loc)

	if now.Unix() >= endTime.Unix() {
		dateStr = endTime.Format("********")
	}
	paramMap := make(map[string]interface{})

	paramMap["date"] = dateStr
	paramMap["zoneareaid"] = "official"
	// paramMap["zoneareaid"] = "304"
	// paramMap["zoneareaid"] = "503"
	paramMap["mapid"] = mapId
	path := "/dmfeature/13568/bhdRankTotalHi"
	// paramMap["date"] = "********"
	if rankType == "sol" {
		// paramMap["date"] = dateStr
		path = "/dmfeature/13568/bhdRankSoloTotalHi"
	}
	response, err := deltaversePb.SendRequest(scheduleCtx, deltaversePb.SendRequestParam{
		ServiceType:        "projectd_oversea",
		DestinationService: "dmfeature-13568",
		Path:               path,
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "http_error").Errorf(
			"oboRankTotal SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		return
	}

	log.WithFieldsContext(scheduleCtx, "log_type", "SyncBHDSpeedData").Infof("rsp: %v", response)
	var rspData OboRankTotalHttp
	err = json.Unmarshal([]byte(response), &rspData)
	if err != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "SyncBHDSpeedData").Infof("err: %v", err)
		_ = DelRedis(scheduleCtx, fmt.Sprintf("df-bhd-speed-total-num-%v-%v", mapId, rankType))
		_ = SetRedis(scheduleCtx, fmt.Sprintf("df-bhd-speed-total-num-%v-%v", mapId, rankType), fmt.Sprintf("%v", 0))
		return
	}

	rankList := make([]*RankInfoItem, 0)
	listInfoStr := strings.Split(rspData.Data.RankListInfo, "#")
	for _, itemStr := range listInfoStr {
		log.WithFieldsContext(scheduleCtx, "log_type", "SyncBHDSpeedData").Infof("itemStr: %v", itemStr)
		var rankInfoItem *RankInfoListItem
		err = json.Unmarshal([]byte(strings.ReplaceAll(itemStr, "'", "\"")), &rankInfoItem)
		if err != nil {
			log.WithFieldsContext(scheduleCtx, "log_type", "SyncBHDSpeedData").Infof("err: %v", err)
			return
		}
		insertInto := false
		for _, haveItem := range rankList {
			if rankInfoItem.Roomid == haveItem.Roomid {
				insertInto = true
			}
		}
		if !insertInto {
			log.WithFieldsContext(scheduleCtx, "log_type", "SyncBHDSpeedData").Infof("rankInfoItem: %+v", rankInfoItem)
			rank, err := strconv.ParseInt(rankInfoItem.PlaytimeRank, 10, 64)
			if err != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "SetInfoListByAms_int64").Infof("PlaytimeRank: %v, err: %v",
					rankInfoItem.PlaytimeRank, err)
				return err
			}

			izoneId, err := strconv.ParseInt(rankInfoItem.Izoneid, 10, 32)
			if err != nil {
				log.WithFieldsContext(ctx, "log_type", "GetSelfPveSpeedData_int32").Infof("Playtime: %v, err: %v",
					rankInfoItem.Izoneid, err)
				return err
			}
			rankListItem := &RankInfoItem{
				Vopenid:      rankInfoItem.Vopenid,
				Roomid:       rankInfoItem.Roomid,
				Izoneareaid:  rankInfoItem.Izoneareaid,
				Izoneid:      int32(izoneId),
				Playtime:     rankInfoItem.Playtime,
				Dteventtime:  rankInfoItem.Dteventtime,
				PlaytimeRank: rank,
				Memberid1:    rankInfoItem.Memberid1,
				Memberid2:    rankInfoItem.Memberid2,
				Memberid3:    rankInfoItem.Memberid3,
				Memberid4:    rankInfoItem.Memberid4,
			}
			if rankListItem.Memberid1 == "0" {
				rankListItem.Memberid1 = rankInfoItem.Vopenid
			}
			rankList = append(rankList, rankListItem)
		}
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "SyncBHDSpeedData").Infof("TotalRankPlayerNum: %v",
		rspData.Data.TotalRankPlayerNum)

	err = DelRedis(scheduleCtx, fmt.Sprintf("df-bhd-speed-total-num-%v-%v", mapId, rankType))
	if err != nil {
		return
	}
	err = SetRedis(scheduleCtx, fmt.Sprintf("df-bhd-speed-total-num-%v-%v", mapId, rankType), fmt.Sprintf("%v",
		rspData.Data.TotalRankPlayerNum))
	if err != nil {
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "SyncBHDSpeedData").Infof("rankList: %+v", rankList)
	err = SetUserInfo(scheduleCtx, rankList, mapId, dateStr, rankType)
	if err != nil {
		return
	}
	return
}

// SetUserInfo 获取各个队友数据
func SetUserInfo(ctx context.Context, rankList []*RankInfoItem, mapId string, dateStr string,
	rankType string) (err error) {
	scheduleCtx := context.Background()

	var wg sync.WaitGroup
	semaphore := make(chan struct{}, 50)
	for _, item := range rankList {
		wg.Add(1)
		go func(item *RankInfoItem) (err error) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			playTime, err := strconv.ParseInt(item.Playtime, 10, 64)
			if err != nil {
				log.WithFieldsContext(scheduleCtx, "log_type", "SetInfoListByAms_int64").Infof("PlaytimeRank: %v, err: %v",
					item.Playtime, err)
				return
			}
			insertInfo := &model.DfBhdSpeedRank{
				Memberid1:   item.Memberid1,
				Memberid2:   item.Memberid2,
				Memberid3:   item.Memberid3,
				Memberid4:   item.Memberid4,
				Rank:        item.PlaytimeRank,
				Playtime:    playTime,
				Dteventtime: item.Dteventtime,
				Dtstatdate:  dateStr,
				MapId:       mapId,
				RankType:    rankType,
			}
			for i := 1; i <= 4; i++ {
				openId := item.Memberid1
				if i == 2 {
					openId = item.Memberid2
				}
				if i == 3 {
					openId = item.Memberid3
				}
				if i == 4 {
					openId = item.Memberid4
				}
				if openId != "0" {
					if len(openId) > 0 {
						info, err := GetInfoByOpenid(scheduleCtx, openId, fmt.Sprintf("%v", item.Izoneid))
						if err != nil {
							return err
						}
						if i == 1 {
							insertInfo.Name1 = info.Name
							insertInfo.Avatar1 = info.Avatar
						}
						if i == 2 {
							insertInfo.Name2 = info.Name
							insertInfo.Avatar2 = info.Avatar
						}
						if i == 3 {
							insertInfo.Name3 = info.Name
							insertInfo.Avatar3 = info.Avatar
						}
						if i == 4 {
							insertInfo.Name4 = info.Name
							insertInfo.Avatar4 = info.Avatar
						}
					}
				}
			}
			var alreadyItem *model.DfBhdSpeedRank
			db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfBhdSpeedRank{}.TableName()).
				Where("map_id = ? and dtstatdate = ? and `rank` = ? and rank_type = ?",
					insertInfo.MapId, insertInfo.Dtstatdate, insertInfo.Rank, rankType).
				First(&alreadyItem)
			if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", db.Error.Error())
				return
			}
			if db.Error == gorm.ErrRecordNotFound {
				db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfBhdSpeedRank{}.TableName()).
					Where("dtstatdate = ? and memberid1 = ? and memberid2 = ? and memberid3 = ? and memberid4 = ? and map_id = ?",
						insertInfo.Dtstatdate,
						insertInfo.Memberid1, insertInfo.Memberid2,
						insertInfo.Memberid3, insertInfo.Memberid4, mapId).
					FirstOrCreate(&insertInfo)
				if db.Error != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error, \t [Error]:{%v} ", db.Error.Error())
					return
				}
			} else {
				insertInfo.ID = alreadyItem.ID
				db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfBhdSpeedRank{}.TableName()).
					Save(&insertInfo)
				if db.Error != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error, \t [Error]:{%v} ", db.Error.Error())
					return
				}
			}

			return
		}(item)
	}
	wg.Wait()
	err = DelRedis(scheduleCtx, fmt.Sprintf("df-bhd-speed-rank-list-%v", rankType))
	if err != nil {
		return
	}
	return
}

// DelRedis 删除redis
func DelRedis(ctx context.Context, key string) (err error) {
	redisPrefixKey := global.GetPrefix()
	hashKey := fmt.Sprintf("%v-%v", redisPrefixKey, key)
	scheduleCtx := context.Background()
	keys, errs := redis.GetClient().Del(scheduleCtx, hashKey).Result()
	if errs != nil {
		err = errs
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[df] rank delete redis keys: [%v]",
		keys))
	return
}

// GetInfoByOpenid 通过openid获取用户信息
func GetInfoByOpenid(ctx context.Context, openId string, areaId string) (info *IdipInfoItem, err error) {

	info = &IdipInfoItem{}
	amsProxy := gameAmsPb.NewAmsClientProxy()

	gameId := "29158"
	cmdId := "********"
	if areaId == "56" {
		gameId = "30150"
		cmdId = "********"
	}
	if areaId == "503" {
		gameId = "30150"
		cmdId = "********"
	}

	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         fmt.Sprintf("%v-%v", gameId, openId),
		AccountType: accountPb.AccountType(1),
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    openId,
			GameId:    gameId,
			ChannelId: 131,
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	IdipParam := make([]*gameAmsPb.IdipGetItem, 0)
	IdipParam = append(IdipParam, &gameAmsPb.IdipGetItem{
		Key:   "AreaId",
		Value: areaId,
	})
	log.WithFieldsContext(ctx, "log_type", "GetInfoByOpenid").Infof("cmdId: %v, gameId: %v, areaId: %v", cmdId, gameId,
		areaId)
	amsInfo, err := amsProxy.GetInfoListByAms(ctx, &gameAmsPb.GetInfoListByAmsReq{
		SelectParam: &gameAmsPb.IdipDBParam{
			CmdId:  cmdId,
			GameId: gameId,
		},
		IdipParam: IdipParam,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "GetInfoListByAms_err", "str_field_1", openId).Infof("err: %v", err)
		return
	}

	for _, item := range amsInfo.Info {
		if item.Key == "roleName" {
			info.Name = item.Value
		}
		if item.Key == "avatar" {
			info.Avatar = item.Value
		}
	}
	return
}

// SetRedis 设置redis缓存
func SetRedis(ctx context.Context, key string, value string) (err error) {
	redisPrefixKey := global.GetPrefix()
	redisKey := fmt.Sprintf("%v-%v", redisPrefixKey, key)

	ok, errR := redis.GetClient().SetNX(ctx, redisKey, value, 0).Result()
	if errR != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "SetNX redis error err=[%v]", errR.Error())
		return
	}
	if !ok {
		log.WithFieldsContext(ctx, "error").Errorf("set redis unsuccess key:[%v], value:[%v]", redisKey,
			value)
		return
	}
	return
}

// GetRedis 获取reids缓存
func GetRedis(ctx context.Context, key string) (result string, err error) {
	redisPrefixKey := global.GetPrefix()
	redisKey := fmt.Sprintf("%v-%v", redisPrefixKey, key)

	resStr, errR := redis.GetClient().Get(ctx, redisKey).Result()
	if errR != nil && errR != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "getRedis redis error err=[%v]",
			errR.Error())
		return
	}

	result = resStr
	return
}

// GetBHDSpeedRankRedis 获取redis排行榜列表
func GetBHDSpeedRankRedis(ctx context.Context, mapId string, rankType string) (rankList []*pb.BHDRankItem, err error) {
	rankList = make([]*pb.BHDRankItem, 0)
	redisPrefixKey := global.GetPrefix()
	rankListRedisKey := fmt.Sprintf("%v-df-bhd-speed-rank-list-%v", redisPrefixKey, rankType)
	result, err := redis.GetClient().HGetAll(ctx, rankListRedisKey).Result()
	if err != nil && err != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			err.Error())
		return
	}

	dataKey := fmt.Sprintf("%v", mapId)
	if _, ok := result[dataKey]; ok {
		err = json.Unmarshal([]byte(result[dataKey]), &rankList)
		if err != nil {
			// 告警
			errs.NewCustomError(ctx, 611010, "redis get err,redisKey=%v,val=%v,err=%v", dataKey,
				result[dataKey],
				err)
			return
		}
	} else {
		rankList, err = GetBHDSpeedRank(ctx, mapId, rankType)
		if err != nil {
			return
		}

		newRankListJsonStr, errJ := json.Marshal(rankList)
		if errJ != nil {
			err = errJ
			return
		}
		result[dataKey] = string(newRankListJsonStr)
		err = redis.GetClient().HMSet(ctx, rankListRedisKey, result).Err()
		if err != nil {
			return
		}
		expiration := 2 * 24 * time.Hour
		err = redis.GetClient().Expire(ctx, rankListRedisKey, expiration).Err()
		if err != nil {
			return
		}
	}

	return
}

// GetBHDSpeedRank 获取排行榜列表
func GetBHDSpeedRank(ctx context.Context, mapId string, rankType string) (rankList []*pb.BHDRankItem, err error) {
	dateStr, err := GetDateStr(ctx)
	if err != nil {
		return
	}
	rankList = make([]*pb.BHDRankItem, 0)

	speedRankList := make([]*model.DfBhdSpeedRank, 0)
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfBhdSpeedRank{}.TableName()).
		Where("dtstatdate = ? and map_id = ? and rank_type = ?", dateStr, mapId, rankType).
		Limit(10).Offset(0).
		Order("`rank` asc").Find(&speedRankList)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	for _, item := range speedRankList {
		names := make([]string, 0)
		avatars := make([]string, 0)
		names = append(names, item.Name1)
		names = append(names, item.Name2)
		names = append(names, item.Name3)
		names = append(names, item.Name4)
		avatars = append(avatars, item.Avatar1)
		avatars = append(avatars, item.Avatar2)
		avatars = append(avatars, item.Avatar3)
		avatars = append(avatars, item.Avatar4)
		rankList = append(rankList, &pb.BHDRankItem{
			PlaytimeRank: int32(item.Rank),
			Playtime:     int32(item.Playtime),
			Names:        names,
			Avatars:      avatars,
		})
	}

	return
}

// GetSelfBhdSpeedData 获取当前用户排行榜信息
func GetSelfBhdSpeedData(ctx context.Context, mapId string, dateStr string, rankType string) (selfItem *pb.BHDRankItem,
	err error) {
	selfItem = &pb.BHDRankItem{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	paramMap := make(map[string]interface{})

	paramMap["date"] = dateStr
	paramMap["zoneareaid"] = "official"
	// paramMap["zoneareaid"] = "304"
	// paramMap["zoneareaid"] = "503"
	paramMap["mapid"] = mapId
	paramMap["openid"] = strings.Split(userAccount.Uid, "-")[1]
	path := "/dmfeature/13568/bhdRankIndHi"
	// paramMap["date"] = "********"
	if rankType == "sol" {
		// paramMap["date"] = dateStr
		path = "/dmfeature/13568/bhdRankIndSoloHi"
	}
	response, err := deltaversePb.SendRequest(ctx, deltaversePb.SendRequestParam{
		ServiceType:        "projectd_oversea",
		DestinationService: "dmfeature-13568",
		Path:               path,
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "http_error").Errorf(
			"oboRankTotal SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		return
	}
	var rspData OboRanklndHttp
	err = json.Unmarshal([]byte(response), &rspData)
	if err != nil {
		return
	}
	if rspData.Data.Memberid1 == "" {
		return
	}
	firstOpenid := rspData.Data.Memberid1
	if rspData.Data.Memberid1 == "0" {
		firstOpenid = rspData.Data.Vopenid
	}

	info1, err := GetInfoByOpenid(ctx, firstOpenid, fmt.Sprintf("%v", rspData.Data.Izoneid))
	if err != nil {
		return
	}

	names := make([]string, 0)
	avatars := make([]string, 0)
	names = append(names, info1.Name)
	avatars = append(avatars, info1.Avatar)
	if rspData.Data.Memberid2 != "0" {
		info2, err := GetInfoByOpenid(ctx, rspData.Data.Memberid2, fmt.Sprintf("%v", rspData.Data.Izoneid))
		if err != nil {
			return selfItem, err
		}
		names = append(names, info2.Name)
		avatars = append(avatars, info2.Avatar)
	}
	if rspData.Data.Memberid3 != "0" {
		info3, err := GetInfoByOpenid(ctx, rspData.Data.Memberid3, fmt.Sprintf("%v", rspData.Data.Izoneid))
		if err != nil {
			return selfItem, err
		}
		names = append(names, info3.Name)
		avatars = append(avatars, info3.Avatar)
	}
	if rspData.Data.Memberid4 != "0" {
		info4, err := GetInfoByOpenid(ctx, rspData.Data.Memberid4, fmt.Sprintf("%v", rspData.Data.Izoneid))
		if err != nil {
			return selfItem, err
		}
		names = append(names, info4.Name)
		avatars = append(avatars, info4.Avatar)
	}

	selfItem.Names = names
	selfItem.Avatars = avatars
	playTime, err := strconv.ParseInt(rspData.Data.Playtime, 10, 32)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "GetSelfPveSpeedData_int32").Infof("Playtime: %v, err: %v",
			rspData.Data.Playtime, err)
		return
	}
	selfItem.Playtime = int32(playTime)
	selfItem.PlaytimeRank = int32(rspData.Data.PlaytimeRank)

	return
}

// SyncAddLottery TODO
func SyncAddLottery(ctx context.Context) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	dateStr, err := GetDateStr(ctx)
	if err != nil {
		return
	}

	data := &model.DfBhdSelfPassTimes{
		Times:      0,
		Dtstatdate: "",
	}

	// 查询sql表数据 对比增加抽奖机会
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfBhdSelfPassTimes{}.TableName()).
		Where("uid = ? and dtstatdate = ?", userAccount.Uid, dateStr).First(&data)
	if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return err
	}
	if db.Error == gorm.ErrRecordNotFound {
		data = &model.DfBhdSelfPassTimes{
			Times:      0,
			Dtstatdate: "",
		}
	}

	if data.Dtstatdate != "" {
		return
	}

	passTimes := 0
	// 遍历 mapid 判断每个关卡是否通关过
	for i := 1; i <= 7; i += 1 {
		mapId := fmt.Sprintf("1000%v", i)
		item, err := GetSelfBhdSpeedData(ctx, mapId, dateStr, "total")
		if err != nil {
			return err
		}
		if item.PlaytimeRank != 0 {
			passTimes = passTimes + 1
		} else {
			// 多判断一下sol榜单
			item, err := GetSelfBhdSpeedData(ctx, mapId, dateStr, "sol")
			if err != nil {
				return err
			}
			if item.PlaytimeRank != 0 {
				passTimes = passTimes + 1
			} else {
				break
			}
		}

	}

	if passTimes != 0 {
		// 查询sql表数据 对比增加抽奖机会
		db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfBhdSelfPassTimes{}.TableName()).
			Where("uid = ?", userAccount.Uid).Order("dtstatdate").First(&data)
		if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return err
		}

		if db.Error == gorm.ErrRecordNotFound || passTimes-int(data.Times) > 0 {
			// 代表没有
			err = AddLottery(ctx, userAccount.Uid, int64(passTimes-int(data.Times)))
			if err != nil {
				return
			}
		}
		if db.Error == gorm.ErrRecordNotFound {
			db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfBhdSelfPassTimes{}.TableName()).
				Where("uid = ? and dtstatdate = ?", userAccount.Uid, dateStr).FirstOrCreate(&model.DfBhdSelfPassTimes{
				Uid:        userAccount.Uid,
				Times:      int32(passTimes),
				Dtstatdate: dateStr,
			})
			if db.Error != nil {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", db.Error.Error())
				return err
			}
		} else {
			data.Times = int32(passTimes)
			db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfBhdSelfPassTimes{}.TableName()).
				Where("uid = ? and dtstatdate = ?", userAccount.Uid, data.Dtstatdate).Save(&data)
			if db.Error != nil {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", db.Error.Error())
				return err
			}
		}

	} else {
		db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfBhdSelfPassTimes{}.TableName()).
			Where("uid = ? and dtstatdate = ?", userAccount.Uid, dateStr).FirstOrCreate(&model.DfBhdSelfPassTimes{
			Uid:        userAccount.Uid,
			Times:      int32(passTimes),
			Dtstatdate: dateStr,
		})
		if db.Error != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return err
		}
	}
	return
}

// AddLottery 同步抽奖机会
func AddLottery(ctx context.Context, uid string, addNum int64) (err error) {

	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         uid,
		AccountType: accountPb.AccountType(1),
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    strings.Split(uid, "-")[1],
			ChannelId: 3,
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	// 给邀请人添加发奖机会 inviteeOpenid 添加抽奖机会
	lottery := LotteryPb.NewLotteryClientProxy()

	realAddNum, err := lottery.AddNumsLottery(ctx, &LotteryPb.AddNumsLotteryReq{
		FsourceId:  "pageV3-1917",
		LotteryId:  1,
		DayLimit:   0,
		TotalLimit: 7,
		AddNum:     addNum,
	}, callopts...)

	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "bhd add nums error", "str_field_1", uid).Errorf(fmt.Sprintf(
			"[df] add openid lottery add  err:%v",
			err.Error()))
		return
	}
	if realAddNum.RealAddNum == 0 {
		log.WithFieldsContext(ctx, "log_type", "bhd add nums debug", "str_field_1", uid).Errorf(fmt.Sprintf(
			"[df] add openid lottery add real num: %v",
			addNum))
	}

	return
}

// GetDateStr TODO
func GetDateStr(ctx context.Context) (dateStr string, err error) {
	now := time.Now()
	dateStr = now.Format("********")
	paramMap := make(map[string]interface{})
	paramMap["date"] = dateStr
	// paramMap["zoneareaid"] = "503"
	// paramMap["zoneareaid"] = "304"
	paramMap["zoneareaid"] = "official"
	paramMap["mapid"] = "10001"
	response, err := deltaversePb.SendRequest(ctx, deltaversePb.SendRequestParam{
		ServiceType:        "projectd_oversea",
		DestinationService: "dmfeature-13568",
		Path:               "/dmfeature/13568/bhdRankTotalHi",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "http_error").Errorf(
			"oboRankTotal SendRequest err; paramMap:[%v],err:[%v]", paramMap, err)
		return
	}

	log.WithFieldsContext(ctx, "log_type", "SyncBHDSpeedData").Infof("rsp: %v", response)
	var rspData OboRankTotalHttp
	err = json.Unmarshal([]byte(response), &rspData)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "is_have_data").Infof("err: %v", err)
		return
	}
	log.WithFieldsContext(ctx, "log_type", "GetDateStr", "str_field_1", "campater").
		Infof("dateStr: %v, httpDate: %v, now: %v", dateStr, rspData.Data.Dtstatdate, now.Unix())
	if rspData.Data.Dtstatdate != dateStr {
		dateStr = now.AddDate(0, 0, -1).Format("********")
	}

	endTime := time.Date(2025, 3, 27, 23, 59, 59, 0, loc)
	if now.Unix() >= endTime.Unix() {
		dateStr = endTime.Format("********")
	}
	return
}
