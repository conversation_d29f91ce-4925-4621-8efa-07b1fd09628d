package cms

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/model"
)

// GetCMSNewsByColumnIds 按栏目获取CMS数据
func GetCMSNewsByColumnIds(ctx context.Context, primaryId int64, secondaryId int64, language string, limit int64, offset int64) (newsRsp *model.CMSNewsRsp, err error) {
	cmsConf := config.GetConfig().CMSSetting
	gameId := cmsConf.GameId
	postBody := map[string]interface{}{
		"primary_label_id":   primaryId,
		"secondary_label_id": secondaryId,
		"language":           []string{language},
		"gameid":             gameId,
		"get_num":            limit,
		"offset":             offset,
		"content_class":      0, // 资讯类
		"sort_by_list": []map[string]interface{}{
			{
				"key": "start_timestamp",
				"asc": 0,
			},
		},
	}
	postBodyByte, err := json.Marshal(postBody)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("json marshal postBody err: %v", err)
		return
	}
	rsp, err := requestCMSData(ctx, cmsConf.GetContentByLabelPath, gameId, cmsConf.AreaId, language, cmsConf.Source, postBodyByte)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("request cms data err: %v", err)
		return
	}
	err = json.Unmarshal([]byte(rsp), &newsRsp)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("json unmarshal rsp err: %v", err)
		return
	}
	if newsRsp.Code != 0 {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("cms news rsp result is not 0, result: %d, Errmsg: %s", newsRsp.Code, newsRsp.Msg)
		return
	}
	return
}

// 请求CMS数据
func requestCMSData(ctx context.Context, urlPath, gameId, areaId, language, source string, postByte []byte) (string, error) {
	log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("request cms data urlPath: %s, gameId: %s, areaId: %s, language: %s, source: %s, postByte: %s", urlPath,
		gameId, areaId, language, source, string(postByte))
	cmsConf := config.GetConfig().CMSSetting
	if cmsConf == nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("request cms data cmsConf is nil")
		return "", errors.New("request cms data cmsConf is nil")
	}
	headerData := map[string]string{
		"Content-Type": "application/json; charset=utf-8",
		"X-areaid":     areaId,
		"X-Gameid":     gameId,
		"X-Language":   language,
		"X-Source":     source,
	}
	requestURL := fmt.Sprintf("%s%s", cmsConf.ServiceHost, urlPath)
	optionOne := httpclient.ClientOption{
		URL:        requestURL,
		Type:       http.MethodPost,
		Timeout:    30 * time.Second,
		Header:     headerData,
		PostString: string(postByte),
	}
	// 发请求
	resultOption := httpclient.RequestOne(ctx, optionOne)
	// 结果判断
	if resultOption.RequestError != nil {
		// 请求返回错误直接记录错误信息进入下一次循环
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call Community api return error, url %s", requestURL)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call Community api http request error, %v", resultOption.RequestError)
		return "", resultOption.RequestError
	}
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("request result data: %s", resultOption.Result)
	return resultOption.Result, nil
}
