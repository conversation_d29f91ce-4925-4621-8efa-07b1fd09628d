package user

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"html"
	"math/rand"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountIntlGamePB "git.code.oa.com/trpcprotocol/publishing_marketing/account_intlgame"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	redisClient "github.com/go-redis/redis/v8"
	es7 "github.com/olivere/elastic/v7"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/pkg/metadatadecode"
	"trpc.publishing_application.standalonesite/app/pkg/security"
	"trpc.publishing_application.standalonesite/app/util"
)

var intlgameProxy = accountIntlGamePB.NewIntlgameClientProxy()

// CheckPermission 检查是否拥有者或管理员
func CheckPermission(c context.Context, user *model.UserContent, targetUserIntlOpenid string) error {
	if user == nil || (user.IntlOpenid != targetUserIntlOpenid && !user.IsAdmin) {
		return errs.NewCustomError(c, code.NoPermission, "No permission to perform this request")
	}
	return nil
}

// GetUserInfoByOpenid 根据intlsdk登录的openid获取用户信息
func GetUserInfoByOpenid(c context.Context, openid string, needRefresh bool) (*model.UserContent, error) {
	return GetUserInfoWithCache(c, openid, needRefresh)
}

// GetUserInfoWithCache 根据intlsdk登录的openid获取用户信息
func GetUserInfoWithCache(c context.Context, openid string, needRefresh bool) (*model.UserContent, error) {
	if openid == "" {
		return nil, nil
	}
	userRedisKey := cache.GetUserInfoKey(openid)
	if !needRefresh {
		if userCacheInfo, err := redis.GetClient().Get(c, userRedisKey).Result(); err == nil {
			if userCacheInfo == "" {
				return nil, gorm.ErrRecordNotFound
			}
			userInfo := &model.UserContent{}
			err = json.Unmarshal([]byte(userCacheInfo), userInfo)
			if err == nil {
				return userInfo, nil
			} else {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfoByOpenid json.Unmarshal err: %v", err)
			}
		} else {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfoByOpenid redis err: %v", err)
		}
	}

	user, err := dao.GetUserByIntlOpenid(openid)
	if err != nil {
		if err.Error() == "record not found" {
			redis.GetClient().SetEX(c, userRedisKey, "", 2*time.Minute).Result()
		}
		return nil, err
	}

	userCacheByte, err := json.Marshal(user)
	if err == nil {
		redis.GetClient().SetEX(c, userRedisKey, string(userCacheByte), 2*time.Minute).Result()
	}

	return user, err
}

// DeleteUserInfoCache 删除用户相关信息Redis缓存
func DeleteUserInfoCache(c context.Context, userIntlOpenID string) {
	if len(userIntlOpenID) == 0 {
		return
	}
	var err error
	_, err = GetUserInfoByOpenid(c, userIntlOpenID, false)
	if err != nil {
		return
	}

	userInfoGuestRedisKey := cache.GetUserInfoAllKey(userIntlOpenID, "guest")
	redis.GetClient().Del(context.Background(), userInfoGuestRedisKey)
	userInfoHostRedisKey := cache.GetUserInfoAllKey(userIntlOpenID, "host")
	redis.GetClient().Del(context.Background(), userInfoHostRedisKey)
	userBaseRedisKey := cache.GetUserInfoKey(userIntlOpenID)
	redis.GetClient().Del(context.Background(), userBaseRedisKey)
	userBaseInfoRedisKey := cache.GetUserBaseInfoKey(userIntlOpenID)
	redis.GetClient().Del(context.Background(), userBaseInfoRedisKey)
	for _, language := range constants.AllPostLanguages {
		userProfileGuestRedisKey := cache.GetUserGuestInfoKey(userIntlOpenID, language)
		redis.GetClient().Del(context.Background(), userProfileGuestRedisKey)
		userProfileHostRedisKey := cache.GetUserHostInfoKey(userIntlOpenID, language)
		redis.GetClient().Del(context.Background(), userProfileHostRedisKey)
	}

}

// GetUserInfo 获取用户信息，带有待审批信息
func GetUserInfoWithAudit(ctx context.Context, intlOpenID string) (*model.UserInfo, error) {
	// 先获取缓存数据
	userInfo := &model.UserInfo{}
	userInfoRedisKey := cache.GetUserInfoWithAuditKey(intlOpenID)
	if userCacheInfo, err := redis.GetClient().Get(ctx, userInfoRedisKey).Result(); err == nil {
		if userCacheInfo == "" {
			return nil, nil
		}
		err = json.Unmarshal([]byte(userCacheInfo), userInfo)
		if err == nil {
			return userInfo, nil
		} else {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfoWithAudit json.Unmarshal err: %v", err)
		}
	} else {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfoWithAudit redis err: %v", err)
	}

	// 获取用户基础信息
	userInfoItem, err := GetUserInfoByOpenid(ctx, intlOpenID, false)
	if err != nil || userInfoItem == nil || userInfoItem.Model == nil || userInfoItem.ID == 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserInfo err: %v, userInfoItem: %v", err, userInfoItem)
		return nil, errs.NewCustomError(ctx, code.GetUserInfoError, "GetUserInfoWithAudit | Failed to get the user information, please check")
	}
	// es搜索数据
	//map[string]*ParamsItem{
	//	"intl_openid": &ParamsItem{
	//		Value: userInfoItem.IntlOpenid,
	//	},
	//	"audit_status": &ParamsItem{
	//		Value: int(constants.USER_AUDIT_STATUS_TYPE_PENDING),
	//	},
	//}
	// db搜索数据
	conditions := &dao.UserAuditConditions{
		IntlOpenid:  userInfoItem.IntlOpenid,
		AuditStatus: constants.USER_AUDIT_STATUS_TYPE_PENDING,
	}
	// 获取用户待审批数据
	userAuditData, err := GetUserAuditListByDB(conditions, 0, 0, false)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfo service.GetUserAuditList err: %v", err)
		return nil, errs.NewCustomError(ctx, code.GetUserInfoError, "GetUserInfoWithAudit | Failed to get the user information, please check")
	}
	var auditUsername, auditAvatar, auditRemark string
	var isAuditUsername, isAuditAvatar, isAuditRemark bool
	// 拿到昵称 头像 签名, es中查询数据同步正确的话最多只有3条数据
	for _, audit := range userAuditData.Items {
		if audit.Type == 1 {
			auditUsername = audit.Context
			isAuditUsername = true
		} else if audit.Type == 2 {
			auditRemark = audit.Context
			isAuditAvatar = true
		} else if audit.Type == 3 {
			auditAvatar = audit.Context
			isAuditRemark = true
		}
	}

	if userInfoItem.HasSignPrivacy == 1 {
		userInfo.HasSignPrivacy = true
	}
	// 组装数据
	userInfo.ID = userInfoItem.ID
	userInfo.IntlOpenid = intlOpenID
	userInfo.AuditUsername = auditUsername
	userInfo.AuditRemark = auditRemark
	userInfo.AuditAvatar = auditAvatar
	userInfo.Username = userInfoItem.Username
	userInfo.UsernameOn = userInfoItem.UsernameOn
	userInfo.Remark = userInfoItem.Remark
	userInfo.RemarkOn = userInfoItem.RemarkOn
	userInfo.Avatar = userInfoItem.Avatar
	userInfo.AvatarOn = userInfoItem.AvatarOn
	userInfo.IsAuditUsername = isAuditUsername
	userInfo.IsAuditRemark = isAuditRemark
	userInfo.IsAuditAvatar = isAuditAvatar
	userInfo.IsFirstRegister = 0

	userCacheByte, err := json.Marshal(userInfo)
	if err == nil {
		redis.GetClient().SetEX(ctx, userInfoRedisKey, string(userCacheByte), 2*time.Minute).Result()
	}

	// 查看用户是否是在LIP首次登录注册，如果是则返回IsFirstRegister=1标识，并删除缓存，因为只消费一次
	userFirstRedisKey := cache.GetUserIsFirstRegisterKey(intlOpenID)
	exists, err := redis.GetClient().Exists(ctx, userFirstRedisKey).Result()
	if err != nil {
		return nil, errs.NewCustomError(ctx, code.GetUserIsFirstRegisterError, "GetUserInfoWithAudit | Failed to obtain user first register information, please check")
	}
	if exists > 0 {
		userInfo.IsFirstRegister = 1
		redis.GetClient().Del(ctx, userFirstRedisKey)
	}

	return userInfo, nil

}

// 即将废弃
func GetUserStatsByOpenid(c context.Context, queryOpenid, intlOpenID, language string) (*model.UserProFile, error) {
	// 先获取查询参数中的用户信息
	queryUser, err := GetUserInfoByOpenid(c, queryOpenid, false)
	if err != nil {
		return nil, err
	}

	if queryUser.Model == nil || queryUser.ID == 0 {
		return nil, errs.NewCustomError(c, code.NoExistUsername, "GetUserStatsByOpenid | User does not exist")
	}

	userProFile := &model.UserProFile{UserContent: queryUser}
	myUserID := intlOpenID
	// 判断是主态还是客态
	if myUserID != queryUser.IntlOpenid {
		if myUserID != "" {
			// 查看当前用户是否有关注被查询用户
			followUser, err := GetUserFollowUser(queryUser.IntlOpenid, myUserID)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserStatsByOpenid GetUserFollowUser %s", err)
				return userProFile, nil
			}
			userProFile.IsFollow = followUser > 0
		}
	} else {
		// 如果是主态的话，则展示所有动态数：已审批+未审批的动态总数
		//userState, err := dao.GetUserStateByUserOpenid(userProFile.IntlOpenid)
		//if err != nil {
		//	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserStatsByOpenid GetUserStateByUserOpenid %s", err)
		//	return userProFile, nil
		//}
		//userProFile.PostNum = userState.AllPostNum
	}
	// 重置认证用户昵称个签多语言
	authUserName, authUserRemark, _ := GetUserCerificationUserLanguage(c, queryOpenid, language)
	if authUserName != "" {
		userProFile.Username = authUserName
	}
	if authUserRemark != "" {
		userProFile.Remark = authUserRemark
	}

	return userProFile, nil
}

func GetUserDetailInfoByOpenid(c context.Context, myOpenid, queryOpenid, language string) (*pbUser.UserInfo, error) {
	var userProfile *pbUser.UserInfo
	userProfileRedisKey := cache.GetUserGuestInfoKey(queryOpenid, language)
	if myOpenid == queryOpenid {
		userProfileRedisKey = cache.GetUserHostInfoKey(queryOpenid, language)
	}
	userProfileCacheInfo, err := redis.GetClient().Get(c, userProfileRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(userProfileCacheInfo), &userProfile)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserDetailInfoByOpenid cache json.Unmarshal error.userProfileRedisKey: %s, err: %v", userProfileRedisKey, err)
			return userProfile, errs.NewCustomError(c, code.GetUserProfileGuestJsonUnmarshalError, "Failed to obtain user info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserDetailInfoByOpenid redis err: %v", err)
		}

		userBaseInfo, err := GetUserBaseInfoByOpenid(c, queryOpenid)
		if err != nil {
			return userProfile, err
		}
		userProfile = &pbUser.UserInfo{
			Id:            userBaseInfo.ID,
			Username:      html.UnescapeString(userBaseInfo.Username),
			Status:        int32(userBaseInfo.Status),
			Avatar:        userBaseInfo.Avatar,
			IntlOpenid:    userBaseInfo.IntlOpenid,
			Remark:        html.UnescapeString(userBaseInfo.Remark),
			Language:      userBaseInfo.Language,
			Titles:        nil,
			FansNum:       userBaseInfo.FansNum,
			FollowNum:     userBaseInfo.FollowNum,
			PostNum:       userBaseInfo.PostNum,
			AllPostNum:    userBaseInfo.AllPostNum,
			Mood:          userBaseInfo.Mood,
			HomePageLinks: userBaseInfo.HomePageLinks,
			GameTag:       userBaseInfo.GameTag,
			GameTagNum:    userBaseInfo.GameTagNum,
			IsFollowed:    0,
			IsAdmin:       userBaseInfo.IsAdmin,
			AuthType:      userBaseInfo.AuthType,
			IsMute:        userBaseInfo.IsMute,
			AvatarPendant: userBaseInfo.AvatarPendant,
		}
		userAdultStatus := metadatadecode.ParseHeaderCookie(c, "game_adult_status")
		s, _ := strconv.ParseInt(userAdultStatus, 10, 32)
		userProfile.GameAdultStatus = int32(s)

		userProfile.AuthDesc = formatted.GetUserAuthDesc(c, queryOpenid, userBaseInfo.AuthType, language)
		if userBaseInfo.HasSignPrivacy == 1 {
			userProfile.HasSignPrivacy = true
		}
		// 重置认证用户昵称个签多语言
		authUserName, authUserRemark, _ := GetUserCerificationUserLanguage(c, queryOpenid, language)
		if authUserName != "" {
			userProfile.Username = html.UnescapeString(authUserName)
		}
		if authUserRemark != "" {
			userProfile.Remark = html.UnescapeString(authUserRemark)
		}

		if myOpenid == queryOpenid {
			// db 搜索
			conditions := &dao.UserAuditConditions{
				IntlOpenid:  queryOpenid,
				AuditStatus: constants.USER_AUDIT_STATUS_TYPE_PENDING,
			}
			// 获取用户待审批数据
			userAudit, err := GetUserAuditListByDB(conditions, 0, 0, false)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfo service.GetUserAuditList err: %v", err)
				return nil, errs.NewCustomError(c, code.GetUserInfoError, "GetUserDetailInfoByOpenid | Failed to get the user information, please check")
			}
			var auditUsername, auditRemark string
			var isAuditUsername, isAuditRemark bool
			// 拿到昵称 头像 签名
			for _, audit := range userAudit.Items {
				if audit.Type == 1 {
					auditUsername = audit.Context
					isAuditUsername = true
				} else if audit.Type == 2 {
					auditRemark = audit.Context
					isAuditRemark = true
				}
			}
			userProfile.AuditUsername = html.UnescapeString(auditUsername)
			userProfile.AuditRemark = html.UnescapeString(auditRemark)
			userProfile.IsAuditUsername = isAuditUsername
			userProfile.IsAuditRemark = isAuditRemark

		}

		// 判断用户是否修改过昵称
		if userBaseInfo.UsernameOn > 0 || (userBaseInfo.UsernameOn == 0 && userProfile.AuditUsername != "") {
			userProfile.HadModifiedUsername = true
		}
		userProfileByte, err := json.Marshal(userProfile)
		if err == nil {
			redis.GetClient().SetEX(c, userProfileRedisKey, string(userProfileByte), 2*time.Minute).Result()
		}
	}
	// TODO这个要拆出去，不放在基础信息查询这里，在上层不同业务逻辑场景对应单个或者批量查询
	// 判断是主态还是客态
	if myOpenid != queryOpenid {
		if myOpenid != "" {
			// 查看当前用户是否有关注被查询用户
			followUser, err := GetUserFollowUser(queryOpenid, myOpenid)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfoByOpenidNew GetUserFollowUser %s", err)
				return userProfile, nil
			}
			if followUser > 0 {
				userProfile.IsFollowed = 1
			}
		}
	} else {
		// 如果是主态的话，则展示所有动态数：已审批+未审批的动态总数
		userProfile.PostNum = userProfile.AllPostNum

		// 查看用户是否是在LIP首次登录注册，如果是则返回IsFirstRegister=1标识，并删除缓存，因为只消费一次
		userFirstRedisKey := cache.GetUserIsFirstRegisterKey(myOpenid)
		exists, err := redis.GetClient().Exists(c, userFirstRedisKey).Result()
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetUserIsFirstRegisterError, "Failed to obtain user first register information, please check")
		}
		if exists > 0 {
			userProfile.IsFirstRegister = true
			redis.GetClient().Del(c, userFirstRedisKey)
		}
	}

	return userProfile, nil
}

// 获取用户最基础的信息，跟语言无关
func GetUserBaseInfoByOpenid(c context.Context, queryOpenid string) (*model.UserBaseInfo, error) {
	userBaseInfo := &model.UserBaseInfo{}
	userBaseInfoRedisKey := cache.GetUserBaseInfoKey(queryOpenid)
	userBaseInfoCacheInfo, err := redis.GetClient().Get(c, userBaseInfoRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(userBaseInfoCacheInfo), &userBaseInfo)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserBaseInfoByOpenid cache json.Unmarshal error.userBaseInfoRedisKey: %s, err: %v", userBaseInfoRedisKey, err)
			return userBaseInfo, errs.NewCustomError(c, code.GetUseBaseJsonUnmarshalError, "Failed to obtain user info, data parsing exception")
		}
		if userBaseInfo.UserContent == nil {
			return userBaseInfo, errs.NewCustomError(c, code.GetUserCacheBaseInfoByOpenidNotExist, "User does not exist")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserBaseInfoByOpenid redis err: %v", err)
		}
		// 先获取DB查询参数中的用户信息
		queryUser, err := dao.GetUserByIntlOpenid(queryOpenid)
		if err != nil || queryUser.Model == nil || queryUser.ID == 0 {
			if err.Error() == "record not found" {
				userBaseInfoByte, err := json.Marshal(userBaseInfo)
				if err == nil {
					redis.GetClient().SetEX(c, userBaseInfoRedisKey, string(userBaseInfoByte), 2*time.Minute).Result()
				}
				return userBaseInfo, errs.NewCustomError(c, code.GetUserBaseInfoByOpenidNotExist, "User does not exist")
			}
			return userBaseInfo, errs.NewCustomError(c, code.QueryUserInfoDBException, "Query user information record exception")
		}

		userBaseInfo.UserContent = queryUser

		// 获取用户管理员、认证、禁言标识
		authType := formatted.GetUserAuth(queryOpenid)
		isAdmin := formatted.GetUserAdmin(queryOpenid)
		isMute := formatted.GetUserMute(queryOpenid)
		userBaseInfo.IsAdmin = isAdmin
		userBaseInfo.AuthType = authType
		userBaseInfo.IsMute = isMute
		// 获取头像挂件
		avatarPendant, _ := GetUserCurWearedAvatarPendantIcon(c, queryOpenid)
		userBaseInfo.AvatarPendant = avatarPendant

		// 获取用户统计数据
		userState, err := dao.GetUserStateByUserOpenid(queryOpenid)
		if err == nil {
			userBaseInfo.FansNum = userState.FansNum
			userBaseInfo.FollowNum = userState.FollowNum
			userBaseInfo.PostNum = userState.PostNum
			userBaseInfo.AllPostNum = userState.AllPostNum
		}

		userBaseInfoByte, err := json.Marshal(userBaseInfo)
		if err == nil {
			redis.GetClient().SetEX(c, userBaseInfoRedisKey, string(userBaseInfoByte), 2*time.Minute).Result()
		}
	}
	if userBaseInfo == nil {
		return userBaseInfo, errs.NewCustomError(c, code.GetUserBaseInfoByOpenidNotExist, "The user no longer exists")
	}

	//查询用户标签值
	getUserGamePlayerInfoRsp, err := GetUserNikkeBasicInfo(c, queryOpenid, "")
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserBaseInfoByOpenid GetUserNikkeBasicInfo err: %v, queryOpenid:%s", err, queryOpenid)
		_ = errs.NewCustomError(c, code.GetUserPlayerBasicInfoError, "GetUserBaseInfoByOpenid | Failed to obtain user nikke game information, please check")
		// return nil, errs.NewCustomError(c, code.GetUserPlayerBasicInfoError, "GetUserAllInfoByOpenid | Failed to obtain user nikke game information, please check")
	} else {
		if userBaseInfo.GameTag == 1 {
			userBaseInfo.GameTagNum = getUserGamePlayerInfoRsp.TowerFloor
		} else if userBaseInfo.GameTag == 2 {
			userBaseInfo.GameTagNum = getUserGamePlayerInfoRsp.NormalProgress
		} else if userBaseInfo.GameTag == 3 {
			userBaseInfo.GameTagNum = getUserGamePlayerInfoRsp.HardProgress
		} else if userBaseInfo.GameTag == 4 {
			userBaseInfo.GameTagNum = getUserGamePlayerInfoRsp.OwnNikkeCnt
		} else if userBaseInfo.GameTag == 5 {
			userBaseInfo.GameTagNum = getUserGamePlayerInfoRsp.AvatarFrame
		} else if userBaseInfo.GameTag == 6 {
			userBaseInfo.GameTagNum = getUserGamePlayerInfoRsp.Costume
		}
	}
	return userBaseInfo, nil
}

// 即将废弃
func GetUserAllInfoByOpenid(c context.Context, queryOpenid, language string, myOpenid string) (*model.UserInfoNew, error) {
	userInfo := &model.UserInfoNew{}
	if queryOpenid == "" {
		queryOpenid = myOpenid
	}
	if queryOpenid == "" {
		return userInfo, nil
	}

	// 先获取缓存数据
	userInfoRedisKey := cache.GetUserInfoAllKey(queryOpenid, "guest")
	// 判断是主态还是客态
	if myOpenid == queryOpenid {
		userInfoRedisKey = cache.GetUserInfoAllKey(queryOpenid, "host")
	}
	userCacheInfo, err := redis.GetClient().Get(c, userInfoRedisKey).Result()
	if err == nil {
		if userCacheInfo == "" {
			return nil, nil
		}
		err = json.Unmarshal([]byte(userCacheInfo), userInfo)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserAllInfoByOpenid cache json.Unmarshal err: %v", err)
			return nil, err
		}
	} else {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserAllInfoByOpenid redis err: %v", err)
		// 先获取查询参数中的用户信息
		queryUser, err := GetUserInfoByOpenid(c, queryOpenid, false)
		if err != nil {
			return nil, err
		}

		if queryUser.Model == nil || queryUser.ID == 0 {
			return nil, errs.NewCustomError(c, code.NoExistUsername, "GetUserAllInfoByOpenid | User does not exist")
		}

		if queryUser.HasSignPrivacy == 1 {
			userInfo.HasSignPrivacy = true
		}

		// 组装数据
		userInfo.ID = queryUser.ID
		userInfo.IntlOpenid = queryOpenid
		userInfo.Username = queryUser.Username
		userInfo.UsernameOn = queryUser.UsernameOn
		userInfo.Nickname = queryUser.Nickname
		userInfo.Remark = queryUser.Remark
		userInfo.RemarkOn = queryUser.RemarkOn
		userInfo.Avatar = queryUser.Avatar
		userInfo.AvatarOn = queryUser.AvatarOn
		userInfo.IsFirstRegister = 0
		userInfo.PostNum = queryUser.PostNum
		userInfo.AllPostNum = queryUser.AllPostNum
		userInfo.PostStarNum = queryUser.PostStarNum
		userInfo.FollowNum = queryUser.FollowNum
		userInfo.FansNum = queryUser.FansNum
		//userInfo.HomePageUrl = queryUser.HomePageURL
		userInfo.Language = queryUser.Language
		userInfo.Mood = queryUser.Mood
		userInfo.HomePageLinks = queryUser.HomePageLinks
		userInfo.GameTag = queryUser.GameTag

		if userInfo.UsernameOn > queryUser.CreatedOn {
			userInfo.ChangeNameCount = 1
		}

		// userTitle, err := GetUserTitleNewOne(c, userInfo.IntlOpenid, language)
		// if err != nil {
		// 	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserAllInfoByOpenid service.GetUserTitleNewOne err: %v\n", err)
		// 	return nil, errs.NewCustomError(c, code.GetUserTitleError, "GetUserAllInfoByOpenid | Failed to get the user's title information, please check")
		// }
		// userInfo.Titles = userTitle

		// 判断是主态还是客态
		if myOpenid == queryOpenid {
			userAdultStatus := metadatadecode.ParseHeaderCookie(c, "game_adult_status")
			// if userAdultStatus != "1" {
			// 	userInfo.Username = queryUser.Nickname
			// 	userInfo.Avatar = ""
			// }
			// 如果是主态的话，则展示所有动态数：已审批+未审批的动态总数
			userInfo.PostNum = userInfo.AllPostNum

			// es搜索
			//map[string]*ParamsItem{
			//	"intl_openid": &ParamsItem{
			//		Value: queryOpenid,
			//	},
			//	"audit_status": &ParamsItem{
			//		Value: int(constants.USER_AUDIT_STATUS_TYPE_PENDING),
			//	},
			//}
			// db 搜索
			conditions := &dao.UserAuditConditions{
				IntlOpenid:  queryOpenid,
				AuditStatus: constants.USER_AUDIT_STATUS_TYPE_PENDING,
			}
			// 获取用户待审批数据
			userAudit, err := GetUserAuditListByDB(conditions, 0, 0, false)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfo service.GetUserAuditList err: %v", err)
				return nil, errs.NewCustomError(c, code.GetUserInfoError, "GetUserAllInfoByOpenid | Failed to get the user information, please check")
			}
			var auditUsername, auditAvatar, auditRemark string
			var isAuditUsername, isAuditAvatar, isAuditRemark bool
			// 拿到昵称 头像 签名
			for _, audit := range userAudit.Items {
				if audit.Type == 1 {
					auditUsername = audit.Context
					isAuditUsername = true
				} else if audit.Type == 2 {
					auditRemark = audit.Context
					isAuditRemark = true
				} else if audit.Type == 3 {
					auditAvatar = audit.Context
					isAuditAvatar = true
				}
			}
			userInfo.AuditUsername = auditUsername
			userInfo.AuditRemark = auditRemark
			userInfo.AuditAvatar = auditAvatar
			userInfo.IsAuditUsername = isAuditUsername
			userInfo.IsAuditRemark = isAuditRemark
			userInfo.IsAuditAvatar = isAuditAvatar
			adultStatus, _ := strconv.ParseInt(userAdultStatus, 10, 64)
			userInfo.AdultStatus = adultStatus
		}
		// 写入缓存数据
		userCacheByte, err := json.Marshal(userInfo)
		if err == nil {
			redis.GetClient().SetEX(c, userInfoRedisKey, string(userCacheByte), 2*time.Minute).Result()
		}
	}
	// 重置认证用户昵称个签多语言
	authUserName, authUserRemark, _ := GetUserCerificationUserLanguage(c, userInfo.IntlOpenid, language)
	if authUserName != "" {
		userInfo.Username = authUserName
	}
	if authUserRemark != "" {
		userInfo.Remark = authUserRemark
	}
	// 判断是主态还是客态
	if myOpenid != queryOpenid {
		if myOpenid != "" {
			// 查看当前用户是否有关注被查询用户
			followUser, err := GetUserFollowUser(userInfo.IntlOpenid, myOpenid)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.GetUserStatsByOpenid GetUserFollowUser %s", err)
				return userInfo, nil
			}
			userInfo.IsFollow = followUser > 0
		}
	} else {
		// 查看用户是否是在LIP首次登录注册，如果是则返回IsFirstRegister=1标识，并删除缓存，因为只消费一次
		userFirstRedisKey := cache.GetUserIsFirstRegisterKey(myOpenid)
		exists, err := redis.GetClient().Exists(c, userFirstRedisKey).Result()
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetUserIsFirstRegisterError, "GetUserAllInfoByOpenid | Failed to obtain user first register information, please check")
		}
		if exists > 0 {
			userInfo.IsFirstRegister = 1
			redis.GetClient().Del(c, userFirstRedisKey)
		}
	}

	//查询用户标签值
	getUserGamePlayerInfoRsp, err := GetUserNikkeBasicInfo(c, queryOpenid, "")
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserAllInfoByOpenid GetUserNikkeBasicInfo err: %v, myOpenid:%s, queryOpenid:%s", err, myOpenid, queryOpenid)
		// return nil, errs.NewCustomError(c, code.GetUserPlayerBasicInfoError, "GetUserAllInfoByOpenid | Failed to obtain user nikke game information, please check")
	} else {
		if userInfo.GameTag == 1 {
			userInfo.GameTagNum = getUserGamePlayerInfoRsp.TowerFloor
		} else if userInfo.GameTag == 2 {
			userInfo.GameTagNum = getUserGamePlayerInfoRsp.NormalProgress
		} else if userInfo.GameTag == 3 {
			userInfo.GameTagNum = getUserGamePlayerInfoRsp.HardProgress
		} else if userInfo.GameTag == 4 {
			userInfo.GameTagNum = getUserGamePlayerInfoRsp.OwnNikkeCnt
		} else if userInfo.GameTag == 5 {
			userInfo.GameTagNum = getUserGamePlayerInfoRsp.AvatarFrame
		} else if userInfo.GameTag == 6 {
			userInfo.GameTagNum = getUserGamePlayerInfoRsp.Costume
		}
	}

	return userInfo, nil
}

// 重置认证用户昵称和个性签名多语言
func GetUserCerificationUserLanguage(c context.Context, intlOpenid, language string) (string, string, error) {
	if intlOpenid == "" {
		return "", "", nil
	}
	var userName, remark string
	authType := formatted.GetUserAuth(intlOpenid)
	if authType == constants.USER_AUTH_TYPE_OFFICIAL || authType == constants.USER_AUTH_TYPE_MECHANISM {
		certificationUserInfos, err := formatted.GetCertificationUserLanguageInfo(c, intlOpenid, language)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetCertificationUserLanguageInfo %v", err)
			return "", "", err
		}
		for _, certificationItem := range certificationUserInfos {
			// username
			if certificationItem.Type == constants.USER_LANGUAGE_TYPE_USERNAME {
				userName = certificationItem.Content
			}
			if certificationItem.Type == constants.USER_LANGUAGE_TYPE_REMARK {
				remark = certificationItem.Content
			}
		}
	}
	return userName, remark, nil
}

func createUserByOpenid(c context.Context, openid, userName, userEmail string, channelID int32, language string) (*model.UserContent, error) {
	if openid == "" {
		return nil, errs.NewCustomError(c, code.UserRegisterFailed, "createUserByOpenid | User registration failed")
	}

	userContent := &model.UserContent{}
	userContent.IntlOpenid = openid
	userContent.IntlGameid = strings.Split(userContent.IntlOpenid, "-")[0]
	userContent.Status = 1
	userContent.Language = language
	avatar, aErr := CreateRandomAvatarNew(c)
	if aErr != nil {
		return nil, errs.NewCustomError(c, code.AvatarGetListFailed, "createUserByOpenid | Get avatar list failed")
	}
	userContent.Avatar = avatar
	if userName == "" {
		var errCode error
		userName, errCode = CreateRandomName(c)
		if errCode != nil {
			return nil, errCode
		}
	} else {
		userContent.UsernameOn = time.Now().Unix()
		// 透传过来的昵称判断是否含有Player，含有就不设置username_on
		matched, err := regexp.MatchString("^Player?", userName)
		if err == nil && matched {
			userContent.UsernameOn = 0
		}

	}
	userContent.Username = userName
	// 原始昵称 删除昵称时显示该名称
	userContent.Nickname = userName

	// 查看是否是自建号登录
	if channelID == 131 {
		// 获取邮箱地址
		userEmail, _ = url.QueryUnescape(userEmail)
		if userEmail != "" && VerifyEmailFormat(userEmail) {
			userContent.Email = userEmail
		}
	}
	user := &model.UserOwner{
		IntlOpenid:     userContent.IntlOpenid,
		IntlGameid:     userContent.IntlGameid,
		IntlUserOpenid: strings.Split(userContent.IntlOpenid, "-")[1],
	}
	createErr := dao.UserCreate(user, userContent)

	if createErr != nil || user.Model == nil || user.ID == 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.createUserByOpenid failed: %s", createErr)
		return nil, errs.NewCustomError(c, code.CreateUsernameError, "createUserByOpenid | Failed to create user")
	}

	// 创建空的数据统计，防止后续查询报错
	aErr = dao.CreateEmptyUserState(userContent.IntlOpenid)
	if aErr != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.createUserstate failed: %s", aErr)
	}
	redis.GetClient().SAdd(c, cache.GetUsernameCacheKey(), userContent.Username)

	go PushUserToESSearch(user, userContent)
	return userContent, nil
}

func PushUserToESSearch(user *model.UserOwner, userContent *model.UserContent) {

	data := []map[string]interface{}{{
		"id":               user.ID,
		"intl_openid":      userContent.IntlOpenid,
		"intl_user_openid": user.IntlUserOpenid,
		"intl_gameid":      userContent.IntlGameid,
		"language":         userContent.Language,
		"nickname":         userContent.Nickname,
		"username":         userContent.Username,
		"username_on":      userContent.UsernameOn,
		"avatar":           userContent.Avatar,
		"avatar_on":        0,
		"fans_num":         0,
		"follow_num":       0,
		"all_post_num":     0,
		"post_star_num":    0,
		"post_num":         0,
		"remark":           "",
		"remark_on":        0,
		"is_admin":         0,
		"admin_on":         0,
		"is_mute":          0,
		"mute_on":          0,
		"auth_type":        0,
		"auth_on":          0,
		"mood":             "",
		"game_tag":         0,
		"created_on":       userContent.CreatedOn,
		"modified_on":      userContent.ModifiedOn,
		"deleted_on":       0,
		"is_del":           0,
	}}
	// 多语言分词
	for _, langItem := range constants.AllPostLanguages {
		usernameLanguagekey := fmt.Sprintf("username_%s", langItem)
		data[0][usernameLanguagekey] = userContent.Username
	}
	dao.EsBulkPushDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, "intl_openid", data)
}

// CreateRandomAvatar 随机头像，先写死在代码，后续废弃
func CreateRandomAvatarNew(c context.Context) (string, error) {
	list, err := getBindingAllGameList(c)
	if err != nil {
		return "", err
	}
	// 保底操作，如果db里一个头像信息都没有，则写死返回一个头像
	if len(list) == 0 {
		return "https://sg-lipcommunity.playerinfinite.com/lip/ugc/public/avatar/xiuma.jpg", nil
	}
	rand.Seed(time.Now().UnixNano())
	n := rand.Intn(len(list))
	return list[n], nil
}

// 生成随机昵称
func CreateRandomName(c context.Context) (name string, err error) {
	// 只重试20次
	rand.Seed(time.Now().UnixNano())
	for i := 0; i < 20; i++ {
		var b []string
		b = append(b, "Player_")
		for i := 0; i < 10; i++ {
			n := rand.Intn(10)
			b = append(b, strconv.Itoa(n))
		}

		str := strings.Join(b, "")
		isExist, err := redis.GetClient().SIsMember(c, cache.GetUsernameCacheKey(), str).Result()
		//user, err := dao.GetUserByUsername(str)
		if err == nil && !isExist {
			return str, nil
		}
	}

	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.createRandomName failed: %s", err)
	return "", errs.NewCustomError(c, code.MaxCreateUsernameError, "CreateRandomName | Failed to create maximum number of user random names")
}

func VerifyEmailFormat(email string) bool {
	pattern := `\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*` //匹配电子邮箱
	reg := regexp.MustCompile(pattern)
	return reg.MatchString(email)
}

func UpdateUserInfo(c context.Context, user *model.UserContent) error {
	if err := dao.UserUpdate(user); err != nil {
		return errs.NewCustomError(c, code.UpdateUserInfoErr, "UpdateUserInfo |Failed to update user")
	}
	return nil
}

// UpdateUserContentInfo 更新用户信息：头像、昵称、个签
func UpdateUserContentInfo(c context.Context, intlOpenid string, userName string, remark string, avatar string, setEmptyRemark bool) error {
	if err := dao.UserUpdateConent(intlOpenid, userName, remark, avatar, setEmptyRemark); err != nil {
		return errs.NewCustomError(c, code.UpdateUserInfoErr, "UpdateUserContentInfo |Failed to update user")
	}
	return nil
}

func CreateUserNameAudit(c context.Context, user *model.UserContent, username string, needAudit bool, language string) (userAudit *model.UserAudit, err error) {
	// 限制用户昵称，30天内只允许修改一次
	// 暂时不限制用户修改
	//if user.UsernameOn != 0 && user.UsernameOn > time.Now().AddDate(0, 0, -30).Unix() {
	//	return nil, errs.NewCustomError(c, code.UpdateUsernameError, "CreateUserNameAudit | The user name can only be modified once within 30 days")
	//}
	if user.IntlOpenid == "" {
		// 没有intl_openid返回错误
		return nil, errs.NewCustomError(c, code.CreateUserNameAuditNotOpenidFailed, "CreateUserNameAudit | Create user name audit not user intl_openid")
	}
	// es搜索
	//conditionsT := map[string]*ParamsItem{
	//	"intl_openid":  {Value: user.IntlOpenid},
	//	"audit_status": {Value: 1},
	//	"type":         {Value: 1},
	//}
	// db搜索
	conditionsT := &dao.UserAuditConditions{
		IntlOpenid:  user.IntlOpenid,
		AuditStatus: 1,
		Type:        1,
	}

	userAudits, err := UserAuditListByDb(conditionsT, 0, 1, false)
	if err != nil {
		return nil, err
	}

	if len(userAudits) > 0 {
		return nil, errs.NewCustomError(c, code.UpdateUsernameAuditExistsError, "CreateUserNameAudit | The user name is already under review, please submit again after reviewing by the administrator")
	}
	var auditOn int64
	auditOn = 0
	auditStatus := 1
	textRiskLevel := 0
	textRiskType := 0
	picRiskLevel := 0
	picRiskType := 0
	if !needAudit {
		auditStatus = 2
		auditOn = time.Now().Unix()
		textRiskLevel = 1
		textRiskType = 100
		picRiskLevel = 1
		picRiskType = 100
	}
	userAudit = &model.UserAudit{
		TextRiskLevel: textRiskLevel,
		TextRiskType:  textRiskType,
		PicRiskLevel:  picRiskLevel,
		PicRiskType:   picRiskType,
		AuditStatus:   auditStatus,
		Type:          1,
		AuditOn:       auditOn,
		Context:       username,
		IntlOpenid:    user.IntlOpenid,
		Language:      language,
	}

	err = UserAuditCreate(userAudit)
	if err != nil {
		return nil, err
	}
	// 把审核的名字写入redis
	redis.GetClient().SAdd(c, cache.GetUsernameCacheKey(), username)

	return
}

func CreateUserRemarkAudit(c context.Context, user *model.UserContent, remark string, needAudit bool, language string) (userAudit *model.UserAudit, err error) {
	// 30天前 是否修改过，先注释掉
	// if time.Now().AddDate(0, 0, -30).Unix() < user.RemarkOn && user.RemarkOn != 0 {
	// 	return errcode.UpdateUserRemarkError
	// }
	if user.IntlOpenid == "" {
		// 没有intl_openid返回错误
		return nil, errs.NewCustomError(c, code.CreateUserRemarkAuditNotOpenidFailed, "CreateUserRemarkAudit | Create user remark audit not user intl_openid")
	}
	//conditionsT := map[string]*ParamsItem{
	//	"intl_openid":  {Value: user.IntlOpenid},
	//	"audit_status": {Value: 1},
	//	"type":         {Value: 2},
	//}

	// db搜索
	conditionsT := &dao.UserAuditConditions{
		IntlOpenid:  user.IntlOpenid,
		AuditStatus: 1,
		Type:        2,
	}

	userAudits, _ := UserAuditListByDb(conditionsT, 0, 1, false)
	if len(userAudits) > 0 {
		return nil, errs.NewCustomError(c, code.UpdateUserRemarkAuditExistsError, "CreateUserRemarkAudit | User notes are already under review, please submit again after the administrator review")
	}

	var auditOn int64
	auditOn = 0
	auditStatus := 1
	if !needAudit {
		auditStatus = 2
		auditOn = time.Now().Unix()
	}
	userAudit = &model.UserAudit{
		TextRiskLevel: 0,
		TextRiskType:  0,
		PicRiskLevel:  0,
		PicRiskType:   0,
		AuditStatus:   auditStatus,
		Type:          2,
		AuditOn:       auditOn,
		Context:       html.UnescapeString(remark),
		IntlOpenid:    user.IntlOpenid,
		Language:      language,
	}

	err = UserAuditCreate(userAudit)
	if err != nil {
		return nil, err
	}

	return
}

// PushUserInfoToSecurityDetection 用户信息修改走内容安全审核
func PushUserInfoToSecurityDetection(c context.Context, content string, sceneId int64, intlOpenID string, username string, auditId int64) {
	defer recovery.CatchGoroutinePanic(context.Background())
	// logrus.Debugln("PushUserInfoToSecurityDetection start")
	accountInfo := security.AccountInfo{
		Account:  intlOpenID,
		RoleName: username,
		PlatId:   3,
	}
	var userAudit = &model.UserAudit{
		Model: &model.Model{ID: auditId},
	}
	// 1.先检查文本内容
	userAudit.TextRiskLevel, userAudit.TextRiskType = security.PushTextToSecurityDetection(content, "", "", userAudit.ID, sceneId, accountInfo)
	if userAudit.TextRiskType == 100 && userAudit.TextRiskLevel == 1 {
		// 机审通过，整个流程结束
		userAudit.MachineStatus = 1
	} else {
		// 机审失败，进入人工审核
		userAudit.MachineStatus = 2
	}
	if err := UserAuditUpdate(userAudit); err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushUserInfoToSecurityDetection ds.UpdateCommentAudit err: %v", err)
	}
	auditDoc := map[string]interface{}{
		"text_risk_level": userAudit.TextRiskLevel,
		"text_risk_type":  userAudit.TextRiskType,
		"pic_risk_level":  1,
		"pic_risk_type":   100,
		"machine_status":  userAudit.MachineStatus,
		"modified_on":     time.Now().Unix(),
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserAuditIndex, fmt.Sprintf("%d", userAudit.ID), auditDoc)

	if userAudit.MachineStatus == 1 {
		// 更新es的缓存
		dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
		// 通过cms的调用
		err := CMSReviewUserInfo(c, &pbUser.CMSReviewUserInfoReq{
			UserAuditIds: []int64{userAudit.ID},
			Type:         1,
			UpdateUser:   "admin",
		}, true)
		if err != nil {
			// 记录用户审核失败
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PushUserInfoToSecurityDetection | review user info failed, id: %d, err: %v", userAudit.ID, err)
		}
		// 如果是机审通过，同时更新用户信息的ES
		// UpdateUserInfoESAfterAuditPassed(c, userAudit)
	}
}

// 审核通过后更新用户名ES 分词,
func UpdateUserInfoESAfterAuditPassed(c context.Context, userAudit *model.UserAudit) {
	if userAudit == nil {
		return
	}
	// 判断是否为认证用户，如果已经是认证用户，无需更新
	isOfficialAuthUser, _ := IsOfficialAuthUser(c, "", "", userAudit.IntlOpenid)
	if isOfficialAuthUser {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UpdateUserNameInfoESAfterAuditPassed | user is official auth user skip, id: %d", userAudit.ID)
		return
	}
	if constants.UserAuditT(userAudit.Type) == constants.USER_AUDIT_TYPE_NAME {
		doc := map[string]interface{}{}
		for _, langItem := range constants.AllPostLanguages {
			userNameKey := fmt.Sprintf("username_%s", langItem)
			doc[userNameKey] = userAudit.Context
		}
		doc["username_on"] = time.Now().Unix()
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, userAudit.IntlOpenid, doc)
		dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	} else if constants.UserAuditT(userAudit.Type) == constants.USER_AUDIT_TYPE_REMARK {
		doc := map[string]interface{}{}
		doc["remark"] = userAudit.Context
		doc["remark_on"] = time.Now().Unix()
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, userAudit.IntlOpenid, doc)
		dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	}
}

// ValidUsernameHasExisted 验证用户
func ValidUsernameHasExisted(ctx context.Context, username string) error {
	// 重复检查
	isExist, err := redis.GetClient().SIsMember(ctx, cache.GetUsernameCacheKey(), username).Result()
	//user, _ := dao.GetUserByUsername(username)

	//if user.Model != nil && user.ID > 0 {
	//	return errs.NewCustomError(ctx, code.UsernameHasExisted, "ValidUsernameHasExisted | Username already exists.")
	//}
	if err != nil {
		return errs.NewCustomError(ctx, code.UsernameHasExisted, "ValidUsernameHasExisted | Failed to check username.")
	}
	if isExist {
		return errs.NewCustomError(ctx, code.UsernameHasExisted, "ValidUsernameHasExisted | Username already exists.")
	}
	return nil
}

// UpdateUserRemark 更新用户签名
func UpdateUserRemark(user *model.UserContent) error {
	return dao.UpdateUserRemark(user)
}

func SetUserLanguage(c context.Context, language string, intlOpenID string) error {
	if intlOpenID == "" {
		return errs.NewCustomError(c, code.GetUserInfoError, "SetUserLanguage | Failed to get the user information, please check")
	}

	// 先获取缓存数据
	user, err := dao.GetUserByIntlOpenid(intlOpenID)
	if err != nil {
		return errs.NewCustomError(c, code.GetUserInfoError, "SetUserLanguage | Failed to get the user information, please check")
	}
	if user.Language != language {
		user.Language = language
		err = dao.UserUpdate(user)
		if err != nil {
			return errs.NewCustomError(c, code.UpdateUserInfoError, "SetUserLanguage | Failed to update user information")
		}
		// 删除缓存
		redis.GetClient().Del(context.Background(), cache.GetUserInfoAllKey(intlOpenID, "guest"))
		redis.GetClient().Del(context.Background(), cache.GetUserInfoAllKey(intlOpenID, "host"))
	}
	return nil
}

// GetUID 提供给登录接口使用，如果是首次LIP注册登录的账号，则会先在p_user表创建一条用户记录
func GetUID(ctx context.Context, loginReq *pbUser.UserLoginInquiryReq, language string) (*model.UserContent, error) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("GetUID111 loginReq: %v", loginReq)
	if loginReq.IntlOpenid == "" {
		return nil, nil
	}
	if strings.Contains(loginReq.IntlOpenid, "com") || strings.Contains(loginReq.IntlOpenid, "_") {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.GetUID err: login openid is wrong, loginReq: %v", loginReq)
	}
	// 查看是否有用户信息，没有则创建用户信息
	userInfo, err := GetUserInfoByOpenid(ctx, loginReq.IntlOpenid, false)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.GetUID err: %v", err)
		return nil, errs.NewCustomError(ctx, code.GetUserInfoError, "GetUID| Failed to get the user information, please check")
	}
	if userInfo != nil && userInfo.Model != nil && userInfo.ID > 0 {
		if loginReq.InGameLogin == 1 {
			//一直没修改过昵称（没有审核过且没有审核中）直接修改昵称为传递
			var hasEditUsername bool
			// 如果已经修改并且审核通过了的，则可以直接返回
			if userInfo.UsernameOn > 0 {
				return userInfo, nil
			}
			// 获取用户待审批数据
			conditions := &dao.UserAuditConditions{
				IntlOpenid:  userInfo.IntlOpenid,
				AuditStatus: constants.USER_AUDIT_STATUS_TYPE_PENDING,
			}
			// 忽略掉delete的筛选，避免修改了但是审核被拒绝了然后还能改昵称
			userAudit, err := GetUserAuditListByDB(conditions, 0, 0, true)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserInfo service.GetUserAuditList err: %v", err)
			} else {
				for _, item := range userAudit.Items {
					if item.Type == 1 {
						hasEditUsername = true
						break
					}
				}
			}
			// 只能判断是否有修改过昵称数据，usernameon需要支持端外社媒登录透传昵称的场景，因为社媒登录的话带了昵称也是不需要弹窗修改昵称的
			if !hasEditUsername {
				username := loginReq.Username
				if username == "" {
					username, err = CreateRandomName(ctx)
					if err != nil {
						return nil, err
					}
				}
				userInfo.Username = username
				userInfo.UsernameOn = time.Now().Unix()
				if err := dao.UserUpdate(userInfo); err == nil {
					// 删除上一次查询为空的缓存
					userRedisKey := cache.GetUserInfoKey(loginReq.IntlOpenid)
					redis.GetClient().Del(ctx, userRedisKey)
				}
			}
		}

		return userInfo, nil
	}

	// 只能有一个执行创建用户信息，加锁机制
	createUserRedisKey := cache.GetCreateUserInfoKey(loginReq.IntlOpenid)
	if ok, _ := redis.GetClient().SetNX(context.Background(), createUserRedisKey, 1, 10*time.Minute).Result(); !ok {
		// 如果没有获取到锁则三秒后再试一下获取看是否能获取用户数据
		time.Sleep(3 * time.Second)
		// 查看是否有用户信息，没有则创建用户信息
		userInfo, err := GetUserInfoByOpenid(ctx, loginReq.IntlOpenid, false)
		if err != nil && err.Error() != "record not found" {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUID GetUserInfoByOpenid err: %v", err)
			return nil, err
		}
		return userInfo, nil
	}

	// 没有查询到说明是首次注册的用户，则新增用户数据入库, email为空
	newUser, errCode := createUserByOpenid(ctx, loginReq.IntlOpenid, loginReq.Username, "", loginReq.ChannelId, language)
	if errCode != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("service.Register err: %v", errCode)
		return nil, errCode
	}
	// 新用户注册任务
	err = UserRegisterActivity(ctx, loginReq.IntlOpenid)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UserRegisterActivity err: %v", err)
	}

	// 删除上一次查询为空的缓存
	userRedisKey := cache.GetUserInfoKey(loginReq.IntlOpenid)
	redis.GetClient().Del(ctx, userRedisKey)
	go DeleteUserInfoCache(ctx, loginReq.IntlOpenid)

	// 设置首次注册登录标识，用于user/info接口
	userFirstRedisKey := cache.GetUserIsFirstRegisterKey(loginReq.IntlOpenid)
	redis.GetClient().SetNX(ctx, userFirstRedisKey, 1, 0)

	return newUser, nil
}

func SetUserMoodInfo(c context.Context, intlOpenid string, req *pbUser.SetUserMoodReq) error {
	err := dao.SetUserMood(intlOpenid, req.Mood)
	if err != nil {
		return errs.NewCustomError(c, code.SetUserMoodError, "SetUserMoodInfo| Failed to set the user mood, please check")
	}
	// 删除缓存,这里只删除主态的缓存
	DeleteUserInfoCache(c, intlOpenid)
	go cache.RemoveUserCommentCacheKeys(intlOpenid)
	go cache.RemoveUserPostCacheKeys(intlOpenid)
	return nil
}

func SetUserGameTagInfo(c context.Context, intlOpenid string, gameTag int64) error {
	err := dao.SetUserGameTag(intlOpenid, gameTag)
	if err != nil {
		return errs.NewCustomError(c, code.SetUserGameTagError, "Failed to set the user game tag, please check")
	}
	// 删除缓存,这里只删除主态的缓存
	DeleteUserInfoCache(c, intlOpenid)
	cache.DeleteUserPostsCache(intlOpenid, "", 10)
	go cache.RemoveUserCommentCacheKeys(intlOpenid)
	go cache.RemoveUserPostCacheKeys(intlOpenid)
	return nil
}

func SetUserShiftyspadPrivacy(c context.Context, intlOpenid string, req *pbUser.SetUserShiftyspadPrivacyReq) (err error) {

	jsonData, err := json.Marshal(&map[string]int{
		"show_daily_info":    int(req.ShowDailyInfo),
		"show_outpost_info":  int(req.ShowOutpostInfo),
		"show_resource_info": int(req.ShowResourceInfo),
		"show_nikke_info":    int(req.ShowNikkeInfo),
	})
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf(
			"SetUserShiftyspadPrivacy Shiftyspad Privacy Switch json error, err: %v", err)
		err = errs.NewCustomError(c, code.ShiftyspadPrivacySwitchJsonErr, "Shiftyspad Privacy Switch json error")
		return
	}
	privacySwitch := string(jsonData)
	log.DebugContextf(c, "SetUserShiftyspadPrivacy show set sql data:[%v]",
		privacySwitch)
	err = dao.UpdateUserShiftyspadPrivacySwitch(c, intlOpenid, privacySwitch)
	if err != nil {
		return errs.NewCustomError(c, code.UpdateUserShiftyspadPrivacySwitchErr, "update Shiftyspad privacy switch err")
	}
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		redisKey := cache.GetUserPrivacySwitchCacheKey(intlOpenid)
		redis.GetClient().Del(context.Background(), redisKey)
	}()
	return
}

func GetUserShiftyspadPrivacy(c context.Context, intlOpenid string) (privacySwitchStr string, err error) {
	privacySwitchStr, err = dao.GetUserShiftyspadPrivacySwitch(c, intlOpenid)
	if err != nil {
		err = errs.NewCustomError(c, code.SetUserShiftyspadPrivacySwitchErr, "set Shiftyspad privacy switch err")
		return
	}
	return
}

// SearchUserInfoList 根据昵称搜索用户信息
func SearchUserInfoList(c context.Context, req *pbUser.SearchUserReq, myOpenid, language string) (*pbUser.SearchUserRsp, error) {
	getUserListRsp := &pbUser.SearchUserRsp{
		List:     make([]*pbUser.UserInfo, 0),
		PageInfo: &pbUser.UserPageInfo{},
	}

	if req.UserName == "" {
		getUserListRsp.PageInfo.IsFinish = true
		return getUserListRsp, nil
	}
	// 先获取缓存数据
	searchUserRedisKey := cache.GetSearchUserKey(req.UserName, req.NextPageCursor, req.Limit)
	searchUserCacheInfo, err := redis.GetClient().Get(c, searchUserRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(searchUserCacheInfo), &getUserListRsp)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SearchUserInfoList cache json.Unmarshal error.searchUserRedisKey: %s, err: %v", searchUserRedisKey, err)
			return nil, errs.NewCustomError(c, code.SearchUserJsonUnmarshalError, "Failed to obtain user info, data parsing exception")
		}
	} else {
		// 没有缓存则直接查db数据
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SearchUserInfoList redis err: %v", err)
		}
		boolQuery := es7.NewBoolQuery()
		isDelQuery := es7.NewTermQuery("is_del", 0)
		boolQuery.Must(isDelQuery)

		if (len(req.UserName) > 17 && len(req.UserName) < 22) && util.IsPureNumber(req.UserName) {
			intlUserOpenidQuery := es7.NewTermQuery("intl_user_openid", req.UserName)
			boolQuery.Must(intlUserOpenidQuery)
		} else {
			usernameQuery := es7.NewBoolQuery()
			for _, systemLanguage := range constants.AllPostLanguages {
				queryKey := fmt.Sprintf("username_%s", systemLanguage)
				usernameQuery.Should(es7.NewQueryStringQuery("*" + req.UserName + "*").Field(queryKey))

			}
			usernameQuery.MinimumNumberShouldMatch(1)
			boolQuery.Must(usernameQuery)
		}

		var sortBys []es7.Sorter
		sortQuery := es7.NewFieldSort("created_on").Desc()
		sortBys = append(sortBys, sortQuery)
		var lastSortValue []interface{}
		if req.NextPageCursor != "" {
			cursorStr, err := util.DecryptPageCursorS(req.NextPageCursor)
			if err != nil {
				return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
			}
			err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
			if err != nil {
				return nil, errs.NewCustomError(c, code.SearchUserUnMarshalCursorFailed, "SearchUserInfoList | Failed to get idCursor")
			}
		}
		resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.UserInfoIndex, boolQuery, sortBys, lastSortValue, req.Limit)
		if err != nil {
			return nil, err
		}

		if resp == nil || resp.Hits == nil {
			getUserListRsp.PageInfo.IsFinish = true
			return getUserListRsp, nil
		}
		currentTime := time.Now().Unix()
		// 缓冲通道，同一时间最多并发5个查询
		maxConcurrent := 5
		sem := make(chan struct{}, maxConcurrent)
		var wg sync.WaitGroup
		userInfoChan := make(chan *pbUser.UserInfo, len(resp.Hits.Hits))
		errChan := make(chan error, len(resp.Hits.Hits))
		for _, hit := range resp.Hits.Hits {
			wg.Add(1)
			sem <- struct{}{} // Acquire semaphore
			go func(hitItem *es7.SearchHit) {
				defer func() {
					recovery.CatchGoroutinePanic(context.Background())
					<-sem
					wg.Done()
				}()
				esUserInfo := &model.ESUserInfo{}
				raw, err := json.Marshal(hitItem.Source)
				if err != nil {
					errChan <- err
					// return nil, err
				}
				// fmt.Println(string(raw))
				// log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("user es result data: %s", string(raw))
				if err = json.Unmarshal(raw, esUserInfo); err != nil {
					errChan <- err
					// return nil, err
				}

				if esUserInfo.AdminOn < currentTime {
					esUserInfo.IsAdmin = 0
				}
				if esUserInfo.MuteOn < currentTime {
					esUserInfo.IsMute = 0
				}
				if esUserInfo.AuthOn < currentTime {
					esUserInfo.AuthType = 0
				}
				authLanguages := make([]*pbUser.LanguageItem, 0)
				if esUserInfo.AuthLanguages != "" {
					json.Unmarshal([]byte(esUserInfo.AuthLanguages), &authLanguages)
				}
				userInfosLanguages := make([]*pbUser.UserInfoLanguageItem, 0)
				if esUserInfo.CertificationUserLanguages != "" {
					json.Unmarshal([]byte(esUserInfo.CertificationUserLanguages), &userInfosLanguages)
				}
				item := &pbUser.UserInfo{
					IntlOpenid:         esUserInfo.IntlOpenid,
					Username:           html.UnescapeString(esUserInfo.Username),
					Status:             1,
					Avatar:             esUserInfo.Avatar,
					IsAdmin:            esUserInfo.IsAdmin > 0,
					AuthType:           esUserInfo.AuthType,
					Remark:             html.UnescapeString(esUserInfo.Remark),
					Language:           esUserInfo.Language,
					FansNum:            esUserInfo.FansNum,
					FollowNum:          esUserInfo.FollowNum,
					PostNum:            esUserInfo.PostNum,
					AllPostNum:         esUserInfo.AllPostNum,
					IsMute:             esUserInfo.IsMute > 0,
					Mood:               esUserInfo.Mood,
					HomePageLinks:      esUserInfo.HomePageLinks,
					AuthLanguages:      authLanguages,
					UserInfosLanguages: userInfosLanguages,
					CreatedOn:          int32(esUserInfo.CreatedOn),
				}

				for _, authLanguage := range authLanguages {
					if authLanguage.Language == language {
						item.AuthDesc = authLanguage.Desc
					}
				}
				// 获取头像挂件
				avatarPendant, err := GetUserCurWearedAvatarPendantIcon(c, item.IntlOpenid)
				if err != nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("SearchUserInfoList GetUserCurWearedAvatarPendantIcon err, intl_openid:(%s), err=(%v)", item.IntlOpenid, err)
				} else {
					item.AvatarPendant = avatarPendant
				}
				//查询用户标签值
				getUserGamePlayerInfoRsp, err := GetUserNikkeBasicInfo(c, item.IntlOpenid, "")
				if err != nil {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SearchUserInfoList GetUserNikkeBasicInfo err: %v, queryOpenid:%s", err, item.IntlOpenid)
					// _ = errs.NewCustomError(c, code.GetUserPlayerBasicInfoError, "GetUserBaseInfoByOpenid | Failed to obtain user nikke game information, please check")
					// return nil, errs.NewCustomError(c, code.GetUserPlayerBasicInfoError, "GetUserAllInfoByOpenid | Failed to obtain user nikke game information, please check")
				} else {
					if item.GameTag == 1 {
						item.GameTagNum = getUserGamePlayerInfoRsp.TowerFloor
					} else if item.GameTag == 2 {
						item.GameTagNum = getUserGamePlayerInfoRsp.NormalProgress
					} else if item.GameTag == 3 {
						item.GameTagNum = getUserGamePlayerInfoRsp.HardProgress
					} else if item.GameTag == 4 {
						item.GameTagNum = getUserGamePlayerInfoRsp.OwnNikkeCnt
					} else if item.GameTag == 5 {
						item.GameTagNum = getUserGamePlayerInfoRsp.AvatarFrame
					} else if item.GameTag == 6 {
						item.GameTagNum = getUserGamePlayerInfoRsp.Costume
					}
				}

				// 这个逻辑查的数据太多，影响性能
				// userInfo, err := GetUserDetailInfoByOpenid(c, myOpenid, item.IntlOpenid, language)
				// if err != nil {
				// 	log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("SearchUserInfoList GetUserDetailInfoByOpenid err, intl_openid:(%s), err=(%v)", item.IntlOpenid, err)
				// } else {
				// 	item.Username = userInfo.Username
				// 	item.Remark = userInfo.Remark
				// 	item.GameTagNum = userInfo.GameTagNum
				// 	item.IsAdmin = userInfo.IsAdmin
				// 	item.IsMute = userInfo.IsMute
				// 	item.AuthDesc = userInfo.AuthDesc
				// 	item.AvatarPendant = userInfo.AvatarPendant
				// }

				userInfoChan <- item
			}(hit)
		}
		go func() {
			recovery.CatchGoroutinePanic(context.Background())
			wg.Wait()
			close(userInfoChan)
			close(errChan)
		}()

		for queryErr := range errChan {
			if queryErr != nil {
				log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("SearchUserInfoList queryErr, err=(%v)", queryErr)
				return nil, errs.NewCustomError(c, code.SearchUserParseUserESDataFailed, "SearchUserInfoList | Failed to parse user information")
			}
		}
		for userContent := range userInfoChan {
			getUserListRsp.List = append(getUserListRsp.List, userContent)
		}
		if len(resp.Hits.Hits) > 0 {
			jsonData, err := json.Marshal(resp.Hits.Hits[len(resp.Hits.Hits)-1].Sort)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SearchUserInfoList json.Marshal err: %v", err)
				return nil, errs.NewCustomError(c, code.SearchUserJsonMarshalCursorFailed, "SearchUserInfoList | Failed to set idCursor")
			}
			if len(resp.Hits.Hits) == 0 || int64(len(resp.Hits.Hits)) < req.Limit {
				getUserListRsp.PageInfo.IsFinish = true
			} else {
				nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
				if err != nil {
					return nil, errs.NewCustomError(c, code.SearchUserEncryptPageCursorSFailed, "SearchUserInfoList | Failed to postsFrom EncryptPageCursorS")
				}
				getUserListRsp.PageInfo.NextPageCursor = nextPageCursor
			}
		} else {
			getUserListRsp.PageInfo.IsFinish = true
		}
		getUserListRsp.PageInfo.PreviousPageCursor = req.NextPageCursor

		getUserListRspByte, err := json.Marshal(getUserListRsp)
		if err == nil {
			redis.GetClient().SetEX(c, searchUserRedisKey, string(getUserListRspByte), 2*time.Minute).Result()
		}
	}

	if myOpenid != "" {
		var userFollowIds = make([]string, 0, len(getUserListRsp.List))
		for _, searchUserItem := range getUserListRsp.List {
			userFollowIds = append(userFollowIds, searchUserItem.IntlOpenid)
		}
		if len(userFollowIds) > 0 {
			// 我关注的人列表
			userFollows, err := dao.UserCollectionList(&dao.UserCollectionConditions{
				IntlOpenid:    myOpenid,
				ToIntlOpenids: userFollowIds,
			}, 0)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SearchUserInfoList UserCollectionList err: %v\n", err)
				return nil, errs.NewCustomError(c, code.GetUserCollectionListError, "GetPostListMyIsFollow | Failed to get follow list.")
			}
			for _, getUserItem := range getUserListRsp.List {
				for _, follow := range userFollows {
					if getUserItem.IntlOpenid == follow.ToIntlOpenid {
						getUserItem.IsFollowed = 1
						if follow.IsMutual == 1 {
							getUserItem.IsMutualFollow = 1
						}
					}
				}
			}
		}
	}
	return getUserListRsp, nil
}

// 创建用户同步数据
func BatchSaveUserByTest() {
	id := 0
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Info("add user content start")
	for j := 0; j < 300; j++ {
		var users []*model.UserOwner
		var userContents []*model.UserContent
		for i := 0; i < 10000; i++ {
			id++
			openid := fmt.Sprintf("%d", 1000000000000+id)
			intlOpenid := fmt.Sprintf("29080-%s", openid)
			var user = &model.UserOwner{
				IntlOpenid:     intlOpenid,
				IntlGameid:     "29080",
				IntlUserOpenid: openid,
			}
			users = append(users, user)
			userContent := &model.UserContent{
				IntlGameid:     "29080",
				Nickname:       fmt.Sprintf("nTest-%s", openid),
				Username:       fmt.Sprintf("uTest-%s", openid),
				UsernameOn:     0,
				IntlOpenid:     intlOpenid,
				Type:           0,
				Phone:          "",
				Email:          "",
				Password:       "",
				Salt:           "",
				Status:         1,
				Avatar:         "https://sg-lipcommunity.playerinfinite.com/lip/ugc/public/avatar/xiuma.jpg",
				AvatarOn:       0,
				Remark:         "",
				RemarkOn:       0,
				HasSignPrivacy: 0,
				Balance:        0,
				Mood:           "",
				GameTag:        0,
				HomePageLinks:  "",
				TagId:          "",
				IsAdmin:        false,
				CreatedOn:      time.Now().Unix(),
				ModifiedOn:     0,
				DeletedOn:      0,
				IsDel:          0,
				Language:       "en",
			}
			userContents = append(userContents, userContent)
			if len(users) >= 100 {
				// 协程创建用户主表
				err := dao.BatchSaveUser(users)
				if err != nil {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("add user failed, user list: %+v", users)
				}

				err = dao.BatchSaveUserContent(userContents)
				if err != nil {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("add user content failed, user content list: %+v", userContents)
				}

				users = make([]*model.UserOwner, 0)
				userContents = make([]*model.UserContent, 0)
			}

		}
		// 避免最后还有数据残留
		if len(users) > 0 {
			err := dao.BatchSaveUser(users)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("add user failed, user list: %+v", users)
			}

			err = dao.BatchSaveUserContent(userContents)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("add user content failed, user content list: %+v", userContents)
			}

			users = make([]*model.UserOwner, 0)
			userContents = make([]*model.UserContent, 0)
		}
	}
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("add user content success, add total user: %d", id)
}

// // 获取intl用户认证信息
// func GetUserMinorcerStatus(ctx context.Context, intlOpenid string) (rsp *pbUser.GetIntlGameUserStatusRsp, err error) {
// 	// 获取openid
// 	// intlGameid := metadatadecode.GetIntlGameId(ctx)
// 	intlGameid := metadatadecode.ParseHeaderCookie(ctx, "game_gameid")
// 	redisKey := cache.GetIntlGameUserStatusKey(intlOpenid)
// 	rsp = &pbUser.GetIntlGameUserStatusRsp{}
// 	getIntlGameUserStatusFunc := func(c context.Context) (interface{}, error) {
// 		res, err := intlgameProxy.GetCrtificationRegion(ctx, &accountIntlGamePB.GetCrtificationRegionReq{
// 			GameId: intlGameid,
// 		})
// 		if err != nil {
// 			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("GetUserMinorcerStatus intlGameId: %s, err: %s", intlGameid, err.Error())
// 			return nil, err
// 		}
// 		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("GetUserMinorcerStatus intlGameId: %s, res: %v", intlGameid, res)
// 		rsp = &pbUser.GetIntlGameUserStatusRsp{
// 			AdultCheckStatus:        res.AdultCheckStatus,
// 			ParentCertificateStatus: res.ParentCertificateStatus,
// 		}
// 		cache.SetCacheWithMarshal(c, redisKey, rsp, 2*time.Minute)
// 		return rsp, nil
// 	}
// 	u, err := cache.GetCacheWithUnmarshal(ctx, redisKey, &rsp, nil, &getIntlGameUserStatusFunc, false)
// 	if err != nil {
// 		return nil, err
// 	}
// 	if u != nil {
// 		rsp = u.(*pbUser.GetIntlGameUserStatusRsp)
// 	}
// 	return rsp, nil
// }

// func CheckUserAdultStatus(ctx context.Context, intlOpenid string) error {
// 	userStatus, err := GetUserMinorcerStatus(ctx, intlOpenid)
// 	if err != nil {
// 		return err
// 	}
// 	if userStatus.AdultCheckStatus == -1 && userStatus.ParentCertificateStatus != 1 {
// 		return errs.NewCustomError(ctx, code.ErrCodeUserNotAdult, "user not adult")
// 	}
// 	return nil
// }

// GetUserAvatarsByIntlOpenids 根据intlopenid列表获取对应用户头像信息
func GetUserAvatarsByIntlOpenids(c context.Context, openids []string) (*pbUser.QueryGuildUserInfosRsp, error) {
	resp := &pbUser.QueryGuildUserInfosRsp{
		GuildUserInfos: make([]*pbUser.GuildUserInfo, 0),
	}
	openids = util.RemoveEmptyStrings(openids)
	if len(openids) == 0 {
		return resp, nil
	}
	var openidsStr string
	for _, openid := range openids {
		openidsStr = fmt.Sprintf("%s_%s", openidsStr, openid)
	}

	hash := sha256.New()
	hash.Write([]byte(openidsStr))
	hashValue := hash.Sum(nil)
	intlOpenidsMd5Str := hex.EncodeToString(hashValue)

	// 先获取缓存数据
	userAvatarsRedisKey := cache.GetGuildSupportUserAvatar(intlOpenidsMd5Str)
	userAvatarsCacheInfo, err := redis.GetClient().Get(c, userAvatarsRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(userAvatarsCacheInfo), &resp)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarsByIntlOpenids cache json.Unmarshal error.userAvatarsRedisKey: %s, err: %v", userAvatarsRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetGuildUserAvatarsJsonUnmarshalError, "Failed to obtain user avatar info, data parsing exception")
		}
	} else {
		userInfos, err := dao.GetGuildUserListByOpenids(openids)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserAvatarsByIntlOpenids GetUserListByOpenid error, openids: %v, err: %v", openids, err)
			return nil, errs.NewCustomError(c, code.GetGuildUserAvatarsDBError, "Failed to obtain user avatar info, mysql error")
		}
		for _, userInfoItem := range userInfos {
			resp.GuildUserInfos = append(resp.GuildUserInfos, &pbUser.GuildUserInfo{
				IntlOpenid:           userInfoItem.IntlOpenid,
				Avatar:               userInfoItem.Avatar,
				ShowFriendCardDetail: userInfoItem.ShowMyGameCard == 1,
			})
		}
		respByte, err := json.Marshal(resp)
		if err == nil {
			redis.GetClient().SetEX(c, userAvatarsRedisKey, string(respByte), 2*time.Minute).Result()
		}
	}

	return resp, nil
}
