// Package df_global_challenge TODO
package df_global_challenge

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/datadump"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	amsPb "git.code.oa.com/trpcprotocol/publishing_marketing/game_ams"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	accountTeam "git.woa.com/trpcprotocol/publishing_marketing/account_team"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_global_challenge"
	redisOrgin "github.com/go-redis/redis/v8"
	"github.com/go-sql-driver/mysql"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/global"
	"trpc.act.logicial/app/logic/sensitivewords"
	model "trpc.act.logicial/app/model/df_global_challenge"
	"trpc.act.logicial/app/model/invitation"
	"trpc.act.logicial/app/util"
)

// SecondSendList TODO
type SecondSendList struct {
	LangType    string
	Tag         string
	Uid         string
	AccountType int32
}

// SaveSelfData 保存个人信息
func SaveSelfData(ctx context.Context, data *model.DfGlobalChallengeSelf) (err error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	data.UID = userAccount.Uid
	var alreadyData *model.DfGlobalChallengeSelf
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeSelf{}.TableName()).
		Where("uid = ?", userAccount.Uid).
		First(&alreadyData)
	if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	db = DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeSelf{}.TableName()).
		Where("uid = ?", userAccount.Uid).
		Save(&data)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	return
}

// AddCountryPointToSql 添加国家积分到mysql中
func AddCountryPointToSql(ctx context.Context, stage int32) (err error) {
	scheduleCtx := context.Background()
	var data []*model.DfGlobalChallengeRegion
	db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeRegion{}.TableName()).
		Find(&data)
	if db.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	var wg sync.WaitGroup
	semaphore := make(chan struct{}, 50)
	for _, item := range data {
		wg.Add(1)
		go func(item *model.DfGlobalChallengeRegion) (err error) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			redisPrefixKey := global.GetPrefix()
			addRegionIdRedisKey := fmt.Sprintf("%v-df-global-challenge-region-%v", redisPrefixKey, item.ID)
			numStr, err1 := redis.GetClient().GetSet(scheduleCtx, addRegionIdRedisKey, 0).Result()
			if err1 != nil && err1 != redisOrgin.Nil {
				if err1 != redisOrgin.Nil {
					return
				} else {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeRedis, redis.RedisConnectErr,
						"AddCountryPointToSql get redis error, \t [key]: {%v} [Error]:{%v} ", addRegionIdRedisKey, err1)
				}
				return
			}
			num, err1 := strconv.ParseInt(numStr, 10, 64)

			if err1 != nil {
				log.WithFieldsContext(ctx, "log_type", "error").
					Infof("Error converting string to int64: %v, %v", numStr, err1)
				return
			}
			if num > 0 {
				updateData := &model.DfGlobalChallengeRegion{
					ID:            item.ID,
					Country:       item.Country,
					RegionId:      item.RegionId,
					Region:        item.Region,
					CountryPoint1: item.CountryPoint1,
					CountryPoint2: item.CountryPoint2,
					CountryPoint3: item.CountryPoint3,
				}
				if stage == 1 || stage == 2 {
					updateData.CountryPoint1 = item.CountryPoint1 + int64(num)
				}
				if stage == 3 || stage == 4 {
					updateData.CountryPoint2 = item.CountryPoint2 + int64(num)
				}
				if stage == 5 || stage == 6 {
					updateData.CountryPoint3 = item.CountryPoint3 + int64(num)
				}
				log.WithFieldsContext(ctx, "INFO").
					Infof("AddCountryPointToSql countryId: %v, num: %v, CountryPoint1: %v, data: %+v", item.ID, num,
						item.CountryPoint1,
						updateData)
				db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeRegion{}.TableName()).
					Where("id = ? and country = ? and region = ? and region_id = ?", item.ID, item.Country, item.Region,
						item.RegionId).
					Save(&updateData)
				if db.Error != nil {
					err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error, \t [Error]:{%v} ", db.Error.Error())
					return
				}
			}
			return
		}(item)
	}
	wg.Wait()
	err = DelRedis(scheduleCtx, "df-global-challenge-region-list")
	if err != nil {
		return
	}
	_, err = GetRegionListByRedis(scheduleCtx, stage)
	if err != nil {
		return err
	}
	_, err = GetCountryListRedis(scheduleCtx, stage)
	if err != nil {
		return err
	}
	return
}

// UpdateCountryPoint 更新城市在区域内排名
func UpdateCountryPoint(ctx context.Context, stage int32) (err error) {
	cheduleCtx := context.Background()
	orderKey := "country_point_1"
	if stage == 3 || stage == 4 {
		orderKey = "country_point_2"
	}
	if stage == 5 || stage == 6 {
		orderKey = "country_point_3"
	}
	maxGoroutines := 50
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	for i := 0; i <= 3; i += 1 {
		var data []*model.DfGlobalChallengeRegion
		db := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeRegion{}.TableName()).
			Where("region_id = ?", i).
			Order(fmt.Sprintf("%v desc, country asc", orderKey)).
			Find(&data)
		if db.Error != nil {
			err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}

		for index, item := range data {
			wg.Add(1)
			go func(item *model.DfGlobalChallengeRegion, index int) {
				semaphore <- struct{}{}
				defer func() {
					<-semaphore
					wg.Done()
				}()
				if stage == 1 || stage == 2 {
					item.CountryRank1 = int64(index + 1)
				}
				if stage == 3 || stage == 4 {
					item.CountryRank2 = int64(index + 1)
				}
				if stage == 5 || stage == 6 {
					item.CountryRank3 = int64(index + 1)
				}
				db = DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeRegion{}.TableName()).
					Where("id = ?", item.ID).
					Save(&item)
				if db.Error != nil {
					err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error, \t [Error]:{%v} ", db.Error.Error())
					return
				}
			}(item, index)
		}
		wg.Wait()
	}
	return
}

// UpdateTeamRank 更新队伍排名
func UpdateTeamRank(ctx context.Context, orderKey string) (err error) {
	cheduleCtx := context.Background()
	var data []*model.DfGlobalChallengeTeam
	db := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeTeam{}.TableName()).
		Order(fmt.Sprintf("%v desc, id asc", orderKey)).
		Find(&data)
	if db.Error != nil {
		err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	for index, item := range data {
		if orderKey == "sol_point" {
			item.SolRank = int64(index + 1)
		}
		if orderKey == "battlefield_point" {
			item.BattlefieldRank = int64(index + 1)
		}
		db = DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeTeam{}.TableName()).
			Where("id = ?", item.ID).
			Save(&item)
		if db.Error != nil {
			err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
	}
	return
}

// AddCountryPointToRedis 添加国家积分到redis中
func AddCountryPointToRedis(ctx context.Context, point int64) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	var selfData *model.DfGlobalChallengeSelf
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeSelf{}.TableName()).
		Where("uid = ?", userAccount.Uid).
		Find(&selfData)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	redisPrefixKey := global.GetPrefix()
	addRegionIdRedisKey := fmt.Sprintf("%v-df-global-challenge-region-%v", redisPrefixKey, selfData.CountryId)
	err = redis.GetClient().IncrBy(ctx, addRegionIdRedisKey, point).Err()
	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"AddReport incr redis error, \t[Key]: {%v} [Error]:{%v} ", addRegionIdRedisKey, err)
		return
	}
	return
}

// GetRegionList 获取region
func GetRegionList(ctx context.Context, stage int32) (regionList []*pb.RegionList, err error) {
	regionList = make([]*pb.RegionList, 0)
	orderKey := "country_point_1"
	if stage == 3 || stage == 4 {
		orderKey = "country_point_2"
	}
	if stage == 5 || stage == 6 {
		orderKey = "country_point_3"
	}
	var data []*model.DfGlobalChallengeRegion
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeRegion{}.TableName()).
		Order(fmt.Sprintf("%v desc, country asc", orderKey)).
		Find(&data)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	for _, item := range data {
		insert := false
		countryItem := &pb.CountryList{
			CountryId:    int32(item.ID),
			Country:      item.Country,
			CountryPoint: item.CountryPoint1 * 100,
		}
		// 根据阶段返回不同的point
		if stage == 3 {
			countryItem.CountryPoint = item.CountryPoint2
		}
		if stage == 5 {
			countryItem.CountryPoint = item.CountryPoint3
		}
		for _, regionItem := range regionList {
			if item.RegionId == regionItem.RegionId {
				insert = true
				if (len(regionItem.CountryList) < 8 && (stage == 5 || stage == 6)) || stage < 5 {
					regionItem.CountryList = append(regionItem.CountryList, countryItem)
				}
			}
		}
		if !insert {
			countryList := make([]*pb.CountryList, 0)
			countryList = append(countryList, countryItem)
			regionList = append(regionList, &pb.RegionList{
				RegionId:    item.RegionId,
				Region:      item.Region,
				CountryList: countryList,
			})
		}
	}

	return
}

// GetRegionListByRedis 获取国家列表从redis中
func GetRegionListByRedis(ctx context.Context, stage int32) (regionList []*pb.RegionList, err error) {
	regionList = make([]*pb.RegionList, 0)
	if stage >= 5 {
		return
	}
	redisPrefixKey := global.GetPrefix()
	regionListRedisKey := fmt.Sprintf("%v-df-global-challenge-region-list", redisPrefixKey)
	regionListJsonStr, err := redis.GetClient().Get(ctx, regionListRedisKey).Result()
	if err != nil && err != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			err.Error())
		return
	}

	if regionListJsonStr != "" {
		err = json.Unmarshal([]byte(regionListJsonStr), &regionList)
		if err != nil {
			// 告警
			errs.NewCustomError(ctx, 611010, "redis get err,redisKey=%v,val=%v,err=%v", regionListRedisKey,
				regionListJsonStr,
				err)
		}
	} else {
		regionList, err = GetRegionList(ctx, stage)
		if err != nil {
			return
		}

		newRegionListJsonStr, errJ := json.Marshal(regionList)
		if errJ != nil {
			err = errJ
			return
		}
		ok, errR := redis.GetClient().SetNX(ctx, regionListRedisKey, newRegionListJsonStr, 12*time.Hour).Result()
		if errR != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis error err=[%v]", errR.Error())
			return
		}
		if !ok {
			log.WithFieldsContext(ctx, "error").Errorf("set redis unsuccess key:[%v], value:[%v]", regionListRedisKey,
				newRegionListJsonStr)
			return
		}

	}
	return
}

// GetSelfData 获取个人数据
func GetSelfData(ctx context.Context) (data *model.DfGlobalChallengeSelf, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	data = &model.DfGlobalChallengeSelf{}
	var challengeData *model.DfGlobalChallengeSelf

	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeSelf{}.TableName()).
		Where("uid = ?", userAccount.Uid).
		First(&challengeData)
	if db.Error != nil {
		if db.Error == gorm.ErrRecordNotFound {
			return data, nil
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	data = challengeData
	return
}

// GetTeamName 获取小队名称
func GetTeamName(ctx context.Context, shareCode string, FsourceID string) (teamName string, err error) {

	var teamNameData *model.DfGlobalChallengeTeam
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeTeam{}.TableName()).
		Where("share_code = ? and Fsource_id = ?", shareCode, FsourceID).
		Find(&teamNameData)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	teamName = teamNameData.TeamName
	return
}

// TeamNameIsRepeat 小队名称是否重复
func TeamNameIsRepeat(ctx context.Context, teamName string, FsourceId string) (isRepeat bool, err error) {
	var num int64
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeTeam{}.TableName()).
		Where("team_name = ? and Fsource_id = ?", teamName, FsourceId).
		Count(&num)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	isRepeat = num > 0
	return
}

// IsSafeTeamName 判断小队名称是否合法
func IsSafeTeamName(ctx context.Context, teamName string, gameId string) (safe bool, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	openId := strings.Split(userAccount.Uid, "-")[1]
	nick := strings.Join([]string{sensitivewords.NICK, openId}, "")
	isSensitive, err := sensitivewords.CheckSensitive(ctx, teamName, gameId, openId, nick, "0")
	if err != nil {
		return
	}
	safe = isSensitive
	return
}

// SaveTeamNameDB 保存小队名称
func SaveTeamNameDB(ctx context.Context, teamName string, shareCode string, FsourceId string) (
	err error,
) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	accountData, _ := proto.Marshal(&accountPb.UserAccount{
		Uid:         userAccount.Uid,
		AccountType: accountPb.AccountType(1),
		IntlAccount: &accountPb.IntlAccount{
			OpenId:    strings.Split(userAccount.Uid, "-")[1],
			ChannelId: 131,
			GameId:    strings.Split(userAccount.Uid, "-")[0],
		},
	})
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}
	accountInfoProxy := accountTeam.NewTeamClientProxy()
	accountInfoRsp, errG := accountInfoProxy.GetAccountInfo(ctx, &accountTeam.GetAccountInfoReq{
		FsourceId: "pageV3-1841",
		GameId:    strings.Split(userAccount.Uid, "-")[0],
	}, callopts...)
	if errG != nil {
		err = errG
		return
	}

	data := &model.DfGlobalChallengeTeam{
		FsourceID:    FsourceId,
		TeamName:     teamName,
		ShareCode:    shareCode,
		LeaderAvatar: accountInfoRsp.UserInfo.UserAvatar,
		LeaderUid:    userAccount.Uid,
	}
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeTeam{}.TableName()).
		Where("share_code = ? and Fsource_id = ?", shareCode, FsourceId).
		FirstOrCreate(&data)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	selfData, err := GetSelfData(ctx)
	if err != nil {
		return
	}
	ReportTlog(ctx, "globalchallenge_backup_squard_create", "cm_click", 29158, map[string]interface{}{
		"squard_name": teamName,
		"area_id":     selfData.RegionId,
		"country":     selfData.CountryId,
		"open_id":     selfData.UID,
	})

	return
}

// GetSolRankList 获取sol排行榜列表
func GetSolRankList(ctx context.Context, pageNum int32, pageSize int32, regionId int32) (list []*pb.RankListItem,
	err error) {
	list = make([]*pb.RankListItem, 0)
	data := make([]*model.DfGlobalChallengeTeam, 0)
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeTeam{}.TableName()).
		Where("sol_point > 0").
		Order("sol_point desc").
		Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize)).
		Find(&data)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	for _, item := range data {
		list = append(list, &pb.RankListItem{
			TeamName:     item.TeamName,
			Point:        int32(item.SolPoint),
			Rank:         int32(item.SolRank),
			LeaderAvatar: item.LeaderAvatar,
		})
	}
	return
}

// GetTeamTotal 获取小队总数
func GetTeamTotal(ctx context.Context, regionId int32, key string) (total int64, err error) {
	var totalNum int64

	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeTeam{}.TableName()).
		// Where("region_id = ?", regionId).
		Where(fmt.Sprintf("%v > 0", key)).
		Limit(100).
		Count(&totalNum)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	total = totalNum
	return
}

// GetSelfSolRank 获取小队的sol积分
func GetSelfSolRank(ctx context.Context) (item *pb.RankListItem, err error) {
	item = &pb.RankListItem{}
	userAccount, err := metadata.GetUserAccount(ctx)
	// if err != nil {
	// 	return
	// }
	delErr := errs.ParseError(ctx, err)
	if delErr.Code == 103001 {
		err = nil
	}
	if userAccount.Uid != "" {
		// 先要获取小队的share code才可以
		shareCode, err := GetTeamShareCode(ctx, userAccount.Uid, int32(userAccount.AccountType))
		if err != nil {
			return item, err
		}
		if shareCode != "" {
			data := &model.DfGlobalChallengeTeam{}
			db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeTeam{}.TableName()).
				Where("share_code = ? ", shareCode).
				First(&data)
			if db.Error != nil {
				if db.Error == gorm.ErrRecordNotFound {
					return item, nil
				}
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", db.Error.Error())
				return item, err
			}
			item.Point = int32(data.SolPoint)
			item.Rank = int32(data.SolRank)
			item.TeamName = data.TeamName
			item.LeaderAvatar = data.LeaderAvatar
		}
	}
	return
}

// GetBattlefieldRankList 获取Battlefield排行榜列表
func GetBattlefieldRankList(ctx context.Context, pageNum int32, pageSize int32,
	regionId int32) (list []*pb.RankListItem, err error) {
	list = make([]*pb.RankListItem, 0)
	data := make([]*model.DfGlobalChallengeTeam, 0)
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeTeam{}.TableName()).
		Where("battlefield_point > 0").
		Order("battlefield_point desc").
		Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize)).
		Find(&data)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	for _, item := range data {
		list = append(list, &pb.RankListItem{
			TeamName:     item.TeamName,
			Point:        int32(item.BattlefieldPoint),
			Rank:         int32(item.BattlefieldRank),
			LeaderAvatar: item.LeaderAvatar,
		})
	}
	return
}

// GetSelfBattlefieldRank 获取小队的Battlefield积分
func GetSelfBattlefieldRank(ctx context.Context) (item *pb.RankListItem, err error) {
	// 先要获取小队的share code才可以
	item = &pb.RankListItem{}
	userAccount, err := metadata.GetUserAccount(ctx)
	// if err != nil {
	// 	return
	// }
	delErr := errs.ParseError(ctx, err)
	if delErr.Code == 103001 {
		err = nil
	}
	if userAccount.Uid != "" {
		// 先要获取小队的share code才可以
		shareCode, err := GetTeamShareCode(ctx, userAccount.Uid, int32(userAccount.AccountType))
		if err != nil {
			return item, err
		}
		if shareCode != "" {
			data := &model.DfGlobalChallengeTeam{}
			db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeTeam{}.TableName()).
				Where("share_code = ? ", shareCode).
				First(&data)
			if db.Error != nil {
				if db.Error == gorm.ErrRecordNotFound {
					return item, nil
				}
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", db.Error.Error())
				return item, err
			}
			item.Point = int32(data.BattlefieldPoint)
			item.Rank = int32(data.BattlefieldRank)
			item.TeamName = data.TeamName
			item.LeaderAvatar = data.LeaderAvatar
		}

	}
	return
}

// GetTeamShareCode 获取自己队伍的shareCode
func GetTeamShareCode(ctx context.Context, uid string, accountType int32) (shareCode string, err error) {
	// 先查表有没有shareCode 没有就是被邀请的
	shareCode = ""
	FsourceId := "pageV3-1841"
	var shareCodeData *invitation.ShortCode
	shareTableName, err := util.GetShortShareTableName(ctx, FsourceId)
	if err != nil {
		return
	}
	findDb := DB.DefaultConnect().Debug().WithContext(ctx).Table(shareTableName).Where("uid = ? and Fsource_id = ?", uid,
		FsourceId).
		First(&shareCodeData)

	if findDb.Error != nil && findDb.Error != gorm.ErrRecordNotFound {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}

	if findDb.Error != gorm.ErrRecordNotFound {
		shareCode = shareCodeData.ShareCode
	} else {
		// 被邀请的
		where := invitation.Invitation{
			InviteeUid:         uid,
			InviteeAccountType: accountType,
			FsourceId:          FsourceId,
			Status:             1,
			IsDelete:           0,
		}
		tableName, err := util.GetShareTableName(ctx, FsourceId)
		if err != nil {
			return "", err
		}
		var invitedData *invitation.Invitation
		findDb := DB.DefaultConnect().Debug().WithContext(ctx).Table(tableName).Where(&where).First(&invitedData)

		if findDb.Error != nil {
			if findDb.Error == gorm.ErrRecordNotFound {
				return "", nil
			}
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql db error, \t [Error]:{%v} ", findDb.Error)
			return "", err
		}
		var shareCodeData *invitation.ShortCode
		shareTableName, err := util.GetShortShareTableName(ctx, FsourceId)
		if err != nil {
			return "", err
		}
		findDb = DB.DefaultConnect().Debug().WithContext(ctx).Table(shareTableName).Where("uid = ? and Fsource_id = ?",
			invitedData.UID, FsourceId).
			First(&shareCodeData)

		if findDb.Error != nil {
			if findDb.Error == gorm.ErrRecordNotFound {
				return "", nil
			}
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"mysql db error, \t [Error]:{%v} ", findDb.Error)
			return "", err
		}
		shareCode = shareCodeData.ShareCode

	}
	return
}

// SyncSolPoint 同步sol积分
func SyncSolPoint(ctx context.Context) (err error) {
	stage := 1
	now := time.Now()
	if now.Unix() >= 1733356800 {
		stage = 3
	}
	if now.Unix() >= 1733962600 {
		stage = 5
	}
	if now.Unix() >= 1734566600 {
		stage = 7
	}

	// if stage == 1 {
	// 	return
	// }
	// 添加时间判断
	scheduleCtx := context.Background()

	timeData := &model.DfGlobalChallengeTime{}
	findDb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeTime{}.TableName()).
		Where("type = ?", fmt.Sprintf("sol-%v", stage)).
		Order("id desc").
		First(&timeData)

	if findDb.Error != nil && !errors.Is(findDb.Error, gorm.ErrRecordNotFound) {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}

	if timeData.Time == 0 {
		// 设置目标日期和时间
		targetDate := time.Date(2024, time.December, 5, 0, 0, 0, 0, time.UTC)
		// targetDate := time.Date(2024, time.December, 10, 0, 0, 0, 0, time.UTC)
		if stage == 5 {
			targetDate = time.Date(2024, time.December, 12, 0, 0, 0, 0, time.UTC)
			// targetDate = time.Date(2024, time.December, 10, 0, 0, 0, 0, time.UTC)
		}

		// 获取时间戳（秒）
		timestampSeconds := targetDate.Unix()
		timeData.Time = timestampSeconds
	}
	startTime := time.Unix(timeData.Time, 0)
	// newTime := startTime.Add(4 * time.Hour)

	// 获取新时间的时间戳（秒）
	now = now.Truncate(time.Minute).Add(-time.Duration(now.Minute()) * time.Minute)
	endTime := now.Unix()
	// endTime := newTime.Unix()
	findDb = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeTime{}.TableName()).
		Save(&model.DfGlobalChallengeTime{
			Time: endTime,
			Type: fmt.Sprintf("sol-%v", stage),
		})

	if findDb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}
	// GetInfoListByAms
	// 查询出来绑定国家的玩家 然后查询他们的sol积分添加上去
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
		Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	// 查询数据添加
	amsProxy := amsPb.NewAmsClientProxy()
	maxGoroutines := 100
	semaphore := make(chan struct{}, maxGoroutines)
	// 检查文档是否已存在
	var wg sync.WaitGroup
	// 分页 每页50条
	pageSize := 50
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		peopleData := make([]*model.DfGlobalChallengeSelf, 0)
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Limit(pageSize).
			Offset((pageNumber - 1) * pageSize).
			Order("id asc").
			Find(&peopleData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		for _, item := range peopleData {
			wg.Add(1)
			go func(item *model.DfGlobalChallengeSelf) (err error) {
				semaphore <- struct{}{}
				defer func() {
					<-semaphore
					wg.Done()
				}()
				openID := strings.Split(item.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         item.UID,
					AccountType: accountPb.AccountType(1),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						GameId:    "29158",
						ChannelId: 131,
					},
				})
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
				}
				IdipParam := make([]*amsPb.IdipGetItem, 0)
				IdipParam = append(IdipParam, &amsPb.IdipGetItem{
					Key:   "start_time",
					Value: fmt.Sprintf("%v", startTime.Unix()),
				})
				IdipParam = append(IdipParam, &amsPb.IdipGetItem{
					Key:   "end_time",
					Value: fmt.Sprintf("%v", endTime),
				})
				IdipParam = append(IdipParam, &amsPb.IdipGetItem{
					Key:   "AreaId",
					Value: "66",
				})
				info, err := amsProxy.GetInfoListByAms(scheduleCtx, &amsPb.GetInfoListByAmsReq{
					SelectParam: &amsPb.IdipDBParam{
						FeatureType: 12,
						GameId:      "29158",
					},
					IdipParam: IdipParam,
				}, callopts...)
				if err != nil {
					return
				}
				allPoint := 0
				for _, data := range info.Info {
					if data.Key == "property" {
						for _, numStr := range data.List {
							// 添加积分
							num, errG := strconv.Atoi(numStr)
							if errG != nil {
								log.WithFieldsContext(scheduleCtx, "error").Errorf("error str SyncSolPoint: %v", numStr)
								return errG
							}
							allPoint = allPoint + int(num)
						}
					}
				}

				if allPoint > 0 {
					var challengeData *model.DfGlobalChallengeSelf

					db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
						Where("uid = ?", item.UID).
						Find(&challengeData)
					if db.Error != nil {
						err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"db error, \t [Error]:{%v} ", db.Error.Error())
						return err
					}
					challengeData.SolPoint = int64(allPoint) + challengeData.SolPoint
					db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
						Where("uid = ?", item.UID).
						Save(&challengeData)
					if db.Error != nil {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"db error, \t [Error]:{%v} ", db.Error.Error())
						return err
					}
					var selfData *model.DfGlobalChallengeSelf
					db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
						Where("uid = ?", item.UID).
						Find(&selfData)
					if db.Error != nil {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"db error, \t [Error]:{%v} ", db.Error.Error())
						return
					}
					// 更新对应的小队积分
					shareCode, errS := GetTeamShareCode(scheduleCtx, item.UID, int32(accountPb.AccountType(1)))
					if errS != nil {
						return errS
					}

					// teamData := &model.DfGlobalChallengeTeam{
					// 	SolPoint: 0,
					// }
					// db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeTeam{}.TableName()).
					// 	Where("share_code = ?", shareCode).
					// 	Find(&teamData)
					// if db.Error != nil {
					// 	err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					// 		"db error, \t [Error]:{%v} ", db.Error.Error())
					// 	return
					// }

					if shareCode != "" {
						db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeTeam{}.TableName()).
							Where("share_code = ?", shareCode).
							UpdateColumn("sol_point", gorm.Expr("sol_point + ?", int64(math.Ceil(float64(allPoint)*1.5))))
						if db.Error != nil {
							err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
								"db error, \t [Error]:{%v} ", db.Error.Error())
							return
						}
						if stage >= 5 {
							isFinal, errF := CountryIdIsFinal(scheduleCtx, selfData.CountryId, selfData.RegionId, "sol")
							if errF != nil {
								err = errF
								return
							}
							if isFinal {
								redisPrefixKey := global.GetPrefix()
								addRegionIdRedisKey := fmt.Sprintf("%v-df-global-challenge-region-%v", redisPrefixKey, selfData.CountryId)
								err = redis.GetClient().IncrBy(scheduleCtx, addRegionIdRedisKey, int64(math.Ceil(float64(allPoint)/
									5000))).Err()
								if err != nil {
									err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeRedis, redis.RedisConnectErr,
										"AddReport incr redis error, \t[Key]: {%v} [Error]:{%v} ", addRegionIdRedisKey, err)
									return
								}
							}
							// 判断一下是否是前8不是就算了
						} else {
							log.WithFieldsContext(scheduleCtx, "log_type").Infof("Sol countryId: %v, point: %v", selfData.CountryId,
								int64(math.Ceil(float64(allPoint)/5000)))

							redisPrefixKey := global.GetPrefix()
							addRegionIdRedisKey := fmt.Sprintf("%v-df-global-challenge-region-%v", redisPrefixKey, selfData.CountryId)
							err = redis.GetClient().IncrBy(scheduleCtx, addRegionIdRedisKey, int64(math.Ceil(float64(allPoint)/5000))).
								Err()
							if err != nil {
								err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeRedis, redis.RedisConnectErr,
									"AddReport incr redis error, \t[Key]: {%v} [Error]:{%v} ", addRegionIdRedisKey, err)
								return
							}
						}
					}
				}
				return
			}(item)
		}
		wg.Wait()
	}
	err = UpdateTeamRank(scheduleCtx, "sol_point")
	if err != nil {
		return
	}

	if stage < 7 {
		err = AddCountryPointToSql(scheduleCtx, int32(stage))
		if err != nil {
			return
		}
		err = UpdateCountryPoint(scheduleCtx, int32(stage))
		if err != nil {
			return
		}
	}

	err = DelRedis(scheduleCtx, "dfm-sol-rank")
	if err != nil {
		return
	}
	return
}

// CountryIdIsFinal 当前国家是否进入决赛
func CountryIdIsFinal(ctx context.Context, countryId int32, regionId int32, keyType string) (
	isFinal bool, err error,
) {
	isFinal = true
	scheduleCtx := context.Background()
	var data *model.DfGlobalChallengeRegion
	findDb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeRegion{}.TableName()).
		Where("region_id = ? and id = ?", regionId, countryId).
		Where("country_rank_2 <= 8").
		First(&data)

	if findDb.Error != nil {
		if findDb.Error == gorm.ErrRecordNotFound {
			isFinal = false
			return
		}
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}
	return
}

// SyncBattlefieldPoint 同步大战场积分
func SyncBattlefieldPoint(ctx context.Context) (err error) {
	stage := 5
	now := time.Now()
	if now.Unix() >= 1733356800 {
		stage = 3
	}
	if now.Unix() >= 1733962600 {
		stage = 5
	}
	if now.Unix() >= 1734566600 {
		stage = 7
	}
	// GetInfoListByAms
	scheduleCtx := context.Background()
	timeData := &model.DfGlobalChallengeTime{}
	findDb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeTime{}.TableName()).
		Where("type = ?", fmt.Sprintf("battlefield-%v", stage)).
		Order("id desc").
		First(&timeData)

	if findDb.Error != nil && !errors.Is(findDb.Error, gorm.ErrRecordNotFound) {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}

	if timeData.Time == 0 {
		// 设置目标日期和时间
		targetDate := time.Date(2024, time.December, 5, 0, 0, 0, 0, time.UTC)
		// targetDate := time.Date(2024, time.December, 10, 0, 0, 0, 0, time.UTC)
		if stage == 5 {
			targetDate = time.Date(2024, time.December, 12, 0, 0, 0, 0, time.UTC)
			// targetDate = time.Date(2024, time.December, 10, 0, 0, 0, 0, time.UTC)
		}

		// 获取时间戳（秒）
		timestampSeconds := targetDate.Unix()
		timeData.Time = timestampSeconds
	}
	startTime := time.Unix(timeData.Time, 0)

	// 加上4个小时
	// newTime := startTime.Add(4 * time.Hour)
	// newTime := startTime.Add(24 * time.Hour)

	now = now.Truncate(time.Minute).Add(-time.Duration(now.Minute()) * time.Minute)
	// 获取新时间的时间戳（秒）
	endTime := now.Unix()
	// endTime := newTime.Unix()
	findDb = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeTime{}.TableName()).
		Save(&model.DfGlobalChallengeTime{
			Time: endTime,
			Type: fmt.Sprintf("battlefield-%v", stage),
		})

	if findDb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql db error, \t [Error]:{%v} ", findDb.Error)
		return
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
		Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	// 查询数据添加
	amsProxy := amsPb.NewAmsClientProxy()
	maxGoroutines := 100
	semaphore := make(chan struct{}, maxGoroutines)
	// 检查文档是否已存在
	var wg sync.WaitGroup
	// 分页 每页50条
	pageSize := 50
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		peopleData := make([]*model.DfGlobalChallengeSelf, 0)
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Limit(pageSize).
			Offset((pageNumber - 1) * pageSize).
			Order("id asc").
			Find(&peopleData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		for _, item := range peopleData {
			wg.Add(1)
			go func(item *model.DfGlobalChallengeSelf) (err error) {
				semaphore <- struct{}{}
				defer func() {
					<-semaphore
					wg.Done()
				}()
				openID := strings.Split(item.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         item.UID,
					AccountType: accountPb.AccountType(1),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						GameId:    "29158",
						ChannelId: 131,
					},
				})
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
				}
				IdipParam := make([]*amsPb.IdipGetItem, 0)
				IdipParam = append(IdipParam, &amsPb.IdipGetItem{
					Key:   "start_time",
					Value: fmt.Sprintf("%v", startTime.Unix()),
				})
				IdipParam = append(IdipParam, &amsPb.IdipGetItem{
					Key:   "end_time",
					Value: fmt.Sprintf("%v", endTime),
				})
				IdipParam = append(IdipParam, &amsPb.IdipGetItem{
					Key:   "AreaId",
					Value: "66",
				})
				info, err := amsProxy.GetInfoListByAms(scheduleCtx, &amsPb.GetInfoListByAmsReq{
					SelectParam: &amsPb.IdipDBParam{
						FeatureType: 11,
						GameId:      "29158",
					},
					IdipParam: IdipParam,
				}, callopts...)
				if err != nil {
					return
				}
				allPoint := 0
				for _, data := range info.Info {
					if data.Key == "score" {
						for _, numStr := range data.List {
							// 添加积分
							num, errG := strconv.Atoi(numStr)
							if errG != nil {
								log.WithFieldsContext(scheduleCtx, "error").Errorf("error str SyncSolPoint: %v", numStr)
								return errG
							}
							allPoint = allPoint + int(num)
						}
					}
				}
				if allPoint > 0 {
					// data, err := GetSelfData(scheduleCtx)
					var challengeData *model.DfGlobalChallengeSelf

					db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
						Where("uid = ?", item.UID).
						Find(&challengeData)
					if db.Error != nil {
						err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"db error, \t [Error]:{%v} ", db.Error.Error())
						return err
					}
					challengeData.BattlefieldPoint = int64(allPoint) + challengeData.BattlefieldPoint
					db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
						Where("uid = ?", item.UID).
						Save(&challengeData)
					if db.Error != nil {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"db error, \t [Error]:{%v} ", db.Error.Error())
						return err
					}
					var selfData *model.DfGlobalChallengeSelf
					db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
						Where("uid = ?", item.UID).
						Find(&selfData)
					if db.Error != nil {
						err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"db error, \t [Error]:{%v} ", db.Error.Error())
						return
					}

					// 更新对应的小队积分
					shareCode, errS := GetTeamShareCode(scheduleCtx, item.UID, int32(accountPb.AccountType(1)))
					if errS != nil {
						return errS
					}

					if shareCode != "" {
						db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeTeam{}.TableName()).
							Where("share_code = ?", shareCode).
							UpdateColumn("battlefield_point", gorm.Expr("battlefield_point + ?", int64(allPoint)))
						if db.Error != nil {
							err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
								"db error, \t [Error]:{%v} ", db.Error.Error())
							return
						}

						if stage >= 5 {
							isFinal, errF := CountryIdIsFinal(scheduleCtx, selfData.CountryId, selfData.RegionId, "battlefield")
							if errF != nil {
								err = errF
								return
							}
							log.WithFieldsContext(scheduleCtx, "log_type", "CountryIdIsFinal").Infof(
								"CountryIdIsFinal_isFinal: %v, err: %v, countryId: %v, regionId: %v, point: %v, id: %v", isFinal, err,
								selfData.CountryId, selfData.RegionId, int64(math.Ceil(float64(allPoint)/100)), selfData.UID)
							if isFinal {
								redisPrefixKey := global.GetPrefix()
								addRegionIdRedisKey := fmt.Sprintf("%v-df-global-challenge-region-%v", redisPrefixKey, selfData.CountryId)
								err = redis.GetClient().IncrBy(scheduleCtx, addRegionIdRedisKey, int64(math.Ceil(float64(allPoint)/100))).
									Err()
								if err != nil {
									err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeRedis, redis.RedisConnectErr,
										"AddReport incr redis error, \t[Key]: {%v} [Error]:{%v} ", addRegionIdRedisKey, err)
									return
								}
							}
						} else {
							redisPrefixKey := global.GetPrefix()
							addRegionIdRedisKey := fmt.Sprintf("%v-df-global-challenge-region-%v", redisPrefixKey, selfData.CountryId)
							err = redis.GetClient().IncrBy(scheduleCtx, addRegionIdRedisKey, int64(math.Ceil(float64(allPoint)/100))).
								Err()
							if err != nil {
								err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeRedis, redis.RedisConnectErr,
									"AddReport incr redis error, \t[Key]: {%v} [Error]:{%v} ", addRegionIdRedisKey, err)
								return
							}
							// err = AddCountryPointToRedis(scheduleCtx, int64(math.Ceil(float64(int64(allPoint)/100))))
							// if err != nil {
							// 	return
							// }
						}
					}
				}
				return
			}(item)

		}
		wg.Wait()
	}
	err = UpdateTeamRank(scheduleCtx, "battlefield_point")
	if err != nil {
		return
	}

	if stage < 7 {
		err = AddCountryPointToSql(ctx, int32(stage))
		if err != nil {
			return
		}
		err = UpdateCountryPoint(ctx, int32(stage))
		if err != nil {
			return
		}
	}
	err = DelRedis(scheduleCtx, "dfm-battlefield-rank")
	if err != nil {
		return
	}
	return
}

// CheckRoleSendPresent 查询是否有角色并且发奖
func CheckRoleSendPresent(ctx context.Context, FsourceId string) (err error) {
	scheduleCtx := context.Background()
	tableName := model.DfGlobalChallengeSend{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
		Where(condition).Where("tag in ('23', '24', '25', '26', '27')").Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", totalRecords))
	if totalRecords == 0 {
		return
	}

	tagsMap := map[string]interface{}{
		"23": "Wand-20241122033108-Pfe82a0ea818a",
		"24": "Wand-20241122033429-Pc73013cfb78b",
		"25": "Wand-20241122033606-P42988a2cbb22",
		"26": "Wand-20241122033705-P955ba7e27089",
		"27": "Wand-20241122034013-Pc790bb723c74",
	}
	// 分页 每页50条
	pageSize := 50
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	var lastId int64
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		// offset := (pageNumber - 1) * pageSize
		var logData = []*model.DfGlobalChallengeSend{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Where(condition).Where("id > ?", lastId).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("%v", logData))
		if len(logData) == 0 {
			break
		}
		lastId = int64(logData[len(logData)-1].ID)
		for _, v := range logData {
			wg.Add(1)
			go func(data *model.DfGlobalChallengeSend) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				gameRoleInfo := &gamePb.RoleInfo{
					GameId:   "29158",
					AreaId:   66,
					PlatId:   0,
					ZoneId:   0,
					RoleId:   "",
					RoleName: "",
					GameName: "projectd_oversea",
				}
				tag, ok := tagsMap[data.Tag].(string)
				if !ok {
					return
				}
				// 发送礼包
				sendReq := &presentPb.SendPresentReq{
					FsourceId: data.FsourceID,
					PresentId: tag,
					RoleInfo:  gameRoleInfo,
				}
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						GameId:    gameRoleInfo.GameId,
						ChannelId: 131,
					},
				})
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
				if sendErr == nil {
					updates := map[string]interface{}{
						"status":     1,
						"created_at": time.Now().Unix(),
					}
					DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id = ?", data.ID).Updates(updates)
				}
				delErr := errs.ParseError(newCtx, sendErr)

				if sendErr != nil && (delErr.Code == 400018 || delErr.Code == 400021) {
					updates := map[string]interface{}{
						"status":     2,
						"created_at": time.Now().Unix(),
					}
					log.WithFieldsContext(ctx, "log_type", "DFGlobalChallengeSendLandMark", "str_field_1", tag, "str_field_2", openID).
						Infof("DFGlobalChallengeSendError")
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err111:%v",
						sendErr.Error()))
					// 如果是已发货 兼容处理
					// if strings.Contains(sendErr.Error(), "package limit left not enough") {
					DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id = ?", data.ID).Updates(updates)
					// }
				}
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err:%v", sendErr))
				// 修改发货状态
				wg.Done()
			}(v)

		}
		wg.Wait()

	}
	return
}

// AddSendLandMarkPresent 添加发奖记录
func AddSendLandMarkPresent(ctx context.Context, FsourceId string, tag string, langType string) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var data model.DfGlobalChallengeSend

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": int32(userAccount.AccountType),
		"tag":          tag,
		"Fsource_id":   FsourceId,
	}

	db := DB.DefaultConnect().WithContext(ctx).Where(condition).Attrs(&model.DfGlobalChallengeSend{
		LangType: langType,
	}).FirstOrCreate(&data)
	if db.Error != nil {
		// 检查 err 是否为重复插入错误
		if isNotDuplicateInsertError(db.Error) {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", err)
			return
		}
	}
	log.WithFieldsContext(ctx, "log_type", "AddSendLandMarkPresent", "str_field_1", tag).
		Infof("AddSendLandMarkPresent")
	num, err := strconv.Atoi(tag)
	if err != nil {
		return
	}
	ReportTlog(ctx, "globalchallenge_backup_gift_receive", "cm_click", 29158, map[string]interface{}{
		"gift_id":   tag,
		"gift_name": fmt.Sprintf("里程碑%v", num-22),
		"open_id":   userAccount.Uid,
	})
	return
}

// isNotDuplicateInsertError 是否不是mysql重复插入报错
func isNotDuplicateInsertError(err error) bool {
	mysqlErr, ok := err.(*mysql.MySQLError)
	if !ok {
		return false
	}
	// Error 1062: Duplicate entry for key
	return mysqlErr.Number != 1062
}

// DelRedis TODO
func DelRedis(ctx context.Context, key string) (err error) {
	redisPrefixKey := global.GetPrefix()
	hashKey := fmt.Sprintf("%v-%v", redisPrefixKey, key)
	scheduleCtx := context.Background()
	keys, errs := redis.GetClient().Del(scheduleCtx, hashKey).Result()
	if errs != nil {
		err = errs
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[df] rank delete redis keys: [%v]",
		keys))
	return
}

// SyncTwoCountryPoint 将第一阶段数据同步到第二阶段
func SyncTwoCountryPoint(ctx context.Context) (err error) {
	cheduleCtx := context.Background()
	maxGoroutines := 50
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	for i := 0; i <= 3; i += 1 {
		var data []*model.DfGlobalChallengeRegion
		db := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeRegion{}.TableName()).
			Where("region_id = ?", i).
			Order("id asc").
			Find(&data)
		if db.Error != nil {
			err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}

		for index, item := range data {
			wg.Add(1)
			go func(item *model.DfGlobalChallengeRegion, index int) {
				semaphore <- struct{}{}
				defer func() {
					<-semaphore
					wg.Done()
				}()
				item.CountryPoint2 = item.CountryPoint1
				db = DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeRegion{}.TableName()).
					Where("id = ?", item.ID).
					Save(&item)
				if db.Error != nil {
					err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error, \t [Error]:{%v} ", db.Error.Error())
					return
				}
			}(item, index)
		}
		wg.Wait()
	}
	return
}

// ReportTlog 上报
func ReportTlog(ctx context.Context, action string, subAction string, gameId int32,
	ExtentContent map[string]interface{}) (err error) {
	langType := metadata.GetLangType(ctx)
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: langType,
			XGameId:   cast.ToInt(gameId),
			XSource:   "pc_web",
		},
		Action:         action,
		SubAction:      subAction,
		OriginalGameId: cast.ToString(gameId),
		ExtentContent:  ExtentContent,
	}
	report.ReportTlog(ctx, tlogData)
	log.WithFieldsContext(ctx, "log_type", action).Infof(
		"vote ReportTlog action:[%v],gameId:[%v],ExtentContent:[%+v] ",
		action, gameId, ExtentContent)
	return
}

// SecondSendPresent 第二阶段发奖
func SecondSendPresent(ctx context.Context, stage int32) (err error) {
	// 按区域 前1W
	cheduleCtx := context.Background()
	FsourceId := "pageV3-1841"
	tableName := model.DfGlobalChallengeSend{}.TableName()

	maxGoroutines := 100
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	solData := make([]*model.DfGlobalChallengeTeam, 0)
	teamName := model.DfGlobalChallengeTeam{}.TableName()
	if stage == 2 {
		teamName = fmt.Sprintf("%v_bak_two", teamName)
	}
	db := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(teamName).
		// Where("sol_point = 1").
		Order("sol_point desc").
		Limit(10000).
		Find(&solData)
	if db.Error != nil {
		err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	sendData := make([]*SecondSendList, 0)

	// for index, item := range solData {
	// 	wg.Add(1)
	// 	go func(index int, item *model.DfGlobalChallengeTeam) (err error) {
	// 		semaphore <- struct{}{}
	// 		defer func() {
	// 			<-semaphore
	// 			wg.Done()
	// 		}()
	// 		// 查询小队成员
	// 		var shareCodeData *invitation.ShortCode
	// 		shareTableName, err := util.GetShortShareTableName(cheduleCtx, FsourceId)
	// 		if err != nil {
	// 			return
	// 		}
	// 		findDb := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(shareTableName).
	// 			Where("share_code = ? and Fsource_id = ?", item.ShareCode,
	// 				FsourceId).
	// 			First(&shareCodeData)

	// 		if findDb.Error != nil && findDb.Error != gorm.ErrRecordNotFound {
	// 			err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
	// 				"mysql db error, \t [Error]:{%v} ", findDb.Error)
	// 			return
	// 		}
	// 		if findDb.Error == gorm.ErrRecordNotFound {
	// 			return
	// 		}
	// 		tableName, err := util.GetShareTableName(cheduleCtx, FsourceId)
	// 		if err != nil {
	// 			return
	// 		}

	// 		invitedData := make([]*invitation.Invitation, 0)
	// 		findDb = DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(tableName).Where(
	// 			"uid = ? and account_type = ? and Fsource_id = ?", shareCodeData.UID, 1,
	// 			FsourceId).
	// 			Find(&invitedData)

	// 		if findDb.Error != nil && findDb.Error != gorm.ErrRecordNotFound {
	// 			err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
	// 				"mysql db error, \t [Error]:{%v} ", findDb.Error)
	// 			return
	// 		}

	// 		if len(invitedData) > 0 {

	// 			for _, dataItem := range invitedData {
	// 				// 需要找一下每个人的数据
	// 				var selfData *model.DfGlobalChallengeSelf
	// 				findDb := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
	// 					Where("uid = ?", dataItem.InviteeUid).
	// 					First(&selfData)

	// 				if findDb.Error != nil && findDb.Error != gorm.ErrRecordNotFound {
	// 					err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
	// 						"mysql db error, \t [Error]:{%v} ", findDb.Error)
	// 					return
	// 				}
	// 				if findDb.Error != gorm.ErrRecordNotFound {
	// 					langType := selfData.LangType
	// 					if langType == "" {
	// 						langType = "en"
	// 					}
	// 					tag := "Wand-**************-P45aa75cbd7e4"
	// 					if stage == 3 {
	// 						tag = "Wand-**************-Pdff93871b459"
	// 					}
	// 					if index < 99 {
	// 						tag = "Wand-**************-P8a74b80200e4"
	// 						if stage == 3 {
	// 							tag = "Wand-**************-P6e9a4c434f00"
	// 						}
	// 					}
	// 					if index <= 199 && index >= 100 {
	// 						tag = "Wand-**************-Pb5024050f4c8"
	// 						if stage == 3 {
	// 							tag = "Wand-**************-Pd4a8eebed05d"
	// 						}
	// 					}
	// 					if index <= 499 && index >= 200 {
	// 						tag = "Wand-**************-Pc0afa71b5891"
	// 						if stage == 3 {
	// 							tag = "Wand-**************-P962cb0ecec50"
	// 						}
	// 					}
	// 					sendData = append(sendData, &SecondSendList{
	// 						Uid:         dataItem.InviteeUid,
	// 						AccountType: dataItem.InviteeAccountType,
	// 						Tag:         tag,
	// 						LangType:    langType,
	// 					})

	// 				}
	// 			}

	// 		}
	// 		var selfData *model.DfGlobalChallengeSelf
	// 		findDb = DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
	// 			Where("uid = ?", shareCodeData.UID).
	// 			First(&selfData)

	// 		if findDb.Error != nil && findDb.Error != gorm.ErrRecordNotFound {
	// 			err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
	// 				"mysql db error, \t [Error]:{%v} ", findDb.Error)
	// 			return
	// 		}
	// 		if findDb.Error != gorm.ErrRecordNotFound {
	// 			langType := selfData.LangType
	// 			if langType == "" {
	// 				langType = "en"
	// 			}
	// 			tag := "Wand-**************-P45aa75cbd7e4"
	// 			if stage == 3 {
	// 				tag = "Wand-**************-Pdff93871b459"
	// 			}
	// 			if index < 99 {
	// 				tag = "Wand-**************-P8a74b80200e4"
	// 				if stage == 3 {
	// 					tag = "Wand-**************-P6e9a4c434f00"
	// 				}
	// 			}
	// 			if index <= 199 && index >= 100 {
	// 				tag = "Wand-**************-Pb5024050f4c8"
	// 				if stage == 3 {
	// 					tag = "Wand-**************-Pd4a8eebed05d"
	// 				}
	// 			}
	// 			if index <= 499 && index >= 200 {
	// 				tag = "Wand-**************-Pc0afa71b5891"
	// 				if stage == 3 {
	// 					tag = "Wand-**************-P962cb0ecec50"
	// 				}
	// 			}
	// 			sendData = append(sendData, &SecondSendList{
	// 				Uid:         shareCodeData.UID,
	// 				AccountType: shareCodeData.AccountType,
	// 				Tag:         tag,
	// 				LangType:    langType,
	// 			})

	// 		}
	// 		return
	// 	}(index, item)
	// }
	// wg.Wait()
	log.WithFieldsContext(cheduleCtx, "log_type").Infof("sendData: %+v", sendData)
	batData := make([]*model.DfGlobalChallengeTeam, 0)
	db = DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(teamName).
		Order("battlefield_point desc").
		Limit(10000).
		Find(&batData)
	if db.Error != nil {
		err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}
	for index, item := range batData {
		wg.Add(1)
		go func(index int, item *model.DfGlobalChallengeTeam) (err error) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			// 查询小队成员
			var shareCodeData *invitation.ShortCode
			shareTableName, err := util.GetShortShareTableName(cheduleCtx, FsourceId)
			if err != nil {
				return
			}
			findDb := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(shareTableName).
				Where("share_code = ? and Fsource_id = ?", item.ShareCode,
					FsourceId).
				First(&shareCodeData)

			if findDb.Error != nil && findDb.Error != gorm.ErrRecordNotFound {
				err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql db error, \t [Error]:{%v} ", findDb.Error)
				return
			}
			if findDb.Error == gorm.ErrRecordNotFound {
				return
			}
			tableName, err := util.GetShareTableName(cheduleCtx, FsourceId)
			if err != nil {
				return
			}

			invitedData := make([]*invitation.Invitation, 0)
			findDb = DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(tableName).Where(
				"uid = ? and account_type = ? and Fsource_id = ? and is_delete = 0", shareCodeData.UID, 1,
				FsourceId).
				Find(&invitedData)

			if findDb.Error != nil && findDb.Error != gorm.ErrRecordNotFound {
				err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql db error, \t [Error]:{%v} ", findDb.Error)
				return
			}

			if len(invitedData) > 0 {

				for _, dataItem := range invitedData {
					// 需要找一下每个人的数据
					var selfData *model.DfGlobalChallengeSelf
					findDb := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
						Where("uid = ?", dataItem.InviteeUid).
						First(&selfData)

					if findDb.Error != nil && findDb.Error != gorm.ErrRecordNotFound {
						err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
							"mysql db error, \t [Error]:{%v} ", findDb.Error)
						return
					}
					if findDb.Error != gorm.ErrRecordNotFound {
						langType := selfData.LangType
						if langType == "" {
							langType = "en"
						}
						tag := "Wand-**************-P333d95fc0b4f"
						if stage == 3 {
							tag = "Wand-**************-P140d36aab015"
						}
						if index < 99 {
							tag = "Wand-**************-P540eb00ee826"
							if stage == 3 {
								tag = "Wand-**************-P82e1889bcdef"
							}
						}
						if index <= 199 && index >= 100 {
							tag = "Wand-**************-Pc77f74e52baa"
							if stage == 3 {
								tag = "Wand-**************-P347b29793ef2"
							}
						}
						if index <= 499 && index >= 200 {
							tag = "Wand-**************-Pdfd1529a8b41"
							if stage == 3 {
								tag = "Wand-**************-P58ffa913bcdf"
							}
						}
						sendData = append(sendData, &SecondSendList{
							Uid:         dataItem.InviteeUid,
							AccountType: dataItem.InviteeAccountType,
							Tag:         tag,
							LangType:    langType,
						})

					}
				}

			}
			var selfData *model.DfGlobalChallengeSelf
			findDb = DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
				Where("uid = ?", shareCodeData.UID).
				First(&selfData)

			if findDb.Error != nil && findDb.Error != gorm.ErrRecordNotFound {
				err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql db error, \t [Error]:{%v} ", findDb.Error)
				return
			}
			if findDb.Error != gorm.ErrRecordNotFound {
				langType := selfData.LangType
				if langType == "" {
					langType = "en"
				}
				tag := "Wand-**************-P333d95fc0b4f"
				if stage == 3 {
					tag = "Wand-**************-P140d36aab015"
				}
				if index < 99 {
					tag = "Wand-**************-P540eb00ee826"
					if stage == 3 {
						tag = "Wand-**************-P82e1889bcdef"
					}
				}
				if index <= 199 && index >= 100 {
					tag = "Wand-**************-Pc77f74e52baa"
					if stage == 3 {
						tag = "Wand-**************-P347b29793ef2"
					}
				}
				if index <= 499 && index >= 200 {
					tag = "Wand-**************-Pdfd1529a8b41"
					if stage == 3 {
						tag = "Wand-**************-P58ffa913bcdf"
					}
				}
				sendData = append(sendData, &SecondSendList{
					Uid:         shareCodeData.UID,
					AccountType: shareCodeData.AccountType,
					Tag:         tag,
					LangType:    langType,
				})
			}
			return
		}(index, item)
	}
	wg.Wait()
	sendProxy := presentPb.NewPresentClientProxy()

	for _, v := range sendData {
		wg.Add(1)
		go func(data *SecondSendList) (err error) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			newCtx := context.Background()
			openID := strings.Split(data.Uid, "-")[1]
			gameRoleInfo := &gamePb.RoleInfo{
				GameId:   "29158",
				AreaId:   66,
				PlatId:   0,
				ZoneId:   0,
				RoleId:   "",
				RoleName: "",
				GameName: "projectd_oversea",
			}
			// 发送礼包
			sendReq := &presentPb.SendPresentReq{
				FsourceId: FsourceId,
				PresentId: data.Tag,
				RoleInfo:  gameRoleInfo,
			}
			accountData, _ := proto.Marshal(&accountPb.UserAccount{
				Uid:         data.Uid,
				AccountType: accountPb.AccountType(data.AccountType),
				IntlAccount: &accountPb.IntlAccount{
					OpenId:    openID,
					GameId:    gameRoleInfo.GameId,
					ChannelId: 131,
				},
			})
			callopts := []client.Option{
				client.WithMetaData(metadata.UserAccount, accountData),
				client.WithMetaData(metadata.LangType, []byte(data.LangType)),
			}
			_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
			if sendErr == nil {
				updates := &model.DfGlobalChallengeSend{
					Status:      1,
					UID:         data.Uid,
					AccountType: int(data.AccountType),
					FsourceID:   FsourceId,
					Tag:         data.Tag,
					LangType:    data.LangType,
				}
				DB.DefaultConnect().WithContext(newCtx).Table(tableName).Save(updates)

			}
			delErr := errs.ParseError(newCtx, sendErr)

			if sendErr != nil && (delErr.Code == 400018 || delErr.Code == 400021) {
				updates := &model.DfGlobalChallengeSend{
					Status:      2,
					UID:         data.Uid,
					AccountType: int(data.AccountType),
					FsourceID:   FsourceId,
					Tag:         data.Tag,
					LangType:    data.LangType,
				}
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err111:%v",
					sendErr.Error()))
				// 如果是已发货 兼容处理
				// if strings.Contains(sendErr.Error(), "package limit left not enough") {
				DB.DefaultConnect().WithContext(newCtx).Table(tableName).Save(updates)
				// }
			}
			log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err:%v", sendErr))
			// 修改发货状态
			return
		}(v)

	}
	wg.Wait()

	// 前100
	// 100-200
	// 200-500
	return
}

// CountrySendPresent TODO
func CountrySendPresent(ctx context.Context, stage int32) (err error) {
	cheduleCtx := context.Background()
	FsourceId := "pageV3-1841"
	tableName := model.DfGlobalChallengeSend{}.TableName()
	allCountryData := make([]int64, 0)

	if stage == 2 {
		for i := 0; i <= 3; i += 1 {
			countryData := make([]*model.DfGlobalChallengeRegion, 0)
			db := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeRegion{}.TableName()).
				Where("region_id = ?", i).
				Order("country_point_2 desc").
				Limit(3).
				Find(&countryData)
			if db.Error != nil {
				err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", db.Error.Error())
				return
			}
			for _, item := range countryData {
				insert := false
				for _, countryId := range allCountryData {
					if item.ID == countryId {
						insert = true
					}
				}
				if !insert {
					allCountryData = append(allCountryData, item.ID)
				}
			}
		}
	}
	if stage == 3 {
		countryData := make([]*model.DfGlobalChallengeRegion, 0)
		db := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeRegion{}.TableName()).
			Order("country_point_3 desc").
			Limit(3).
			Find(&countryData)
		if db.Error != nil {
			err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		for _, item := range countryData {
			insert := false
			for _, countryId := range allCountryData {
				if item.ID == countryId {
					insert = true
				}
			}
			if !insert {
				allCountryData = append(allCountryData, item.ID)
			}
		}
	}
	sendData := make([]*SecondSendList, 0)
	// 查找到国家下所有玩家发奖
	for _, countryId := range allCountryData {
		peopleData := make([]*model.DfGlobalChallengeSelf, 0)
		db := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
			Where("country_id = ?", countryId).
			Find(&peopleData)
		if db.Error != nil {
			err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		for _, people := range peopleData {
			insert := false
			for _, sendItem := range sendData {
				if people.UID == sendItem.Uid {
					log.WithFieldsContext(cheduleCtx, "log_type", "insert_into", "str_field_1", people.UID).Infof("insert_into")
					insert = true
				}
			}
			if !insert {
				langtype := people.LangType
				if langtype == "" {
					langtype = "en"
				}
				tag := "Wand-**************-P7851464bf3a5"
				if stage == 3 {
					tag = "Wand-**************-P77a86e55fd74"
				}
				log.WithFieldsContext(cheduleCtx, "log_type", "CountrySendPresent_3", "str_field_1", people.UID, "str_field_2",
					tag).Infof("CountrySendPresent_3")
				sendData = append(sendData, &SecondSendList{
					Uid:         people.UID,
					LangType:    langtype,
					Tag:         tag,
					AccountType: 1,
				})

			}
		}
	}

	sendProxy := presentPb.NewPresentClientProxy()

	maxGoroutines := 100
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	for _, v := range sendData {
		wg.Add(1)
		go func(data *SecondSendList) (err error) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			newCtx := context.Background()
			openID := strings.Split(data.Uid, "-")[1]
			gameRoleInfo := &gamePb.RoleInfo{
				GameId:   "29158",
				AreaId:   66,
				PlatId:   0,
				ZoneId:   0,
				RoleId:   "",
				RoleName: "",
				GameName: "projectd_oversea",
			}
			// 发送礼包
			sendReq := &presentPb.SendPresentReq{
				FsourceId: FsourceId,
				PresentId: data.Tag,
				RoleInfo:  gameRoleInfo,
			}
			accountData, _ := proto.Marshal(&accountPb.UserAccount{
				Uid:         data.Uid,
				AccountType: accountPb.AccountType(data.AccountType),
				IntlAccount: &accountPb.IntlAccount{
					OpenId:    openID,
					GameId:    gameRoleInfo.GameId,
					ChannelId: 131,
				},
			})
			callopts := []client.Option{
				client.WithMetaData(metadata.UserAccount, accountData),
				client.WithMetaData(metadata.LangType, []byte(data.LangType)),
			}
			_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
			if sendErr == nil {
				updates := &model.DfGlobalChallengeSend{
					Status:      1,
					UID:         data.Uid,
					AccountType: int(data.AccountType),
					FsourceID:   FsourceId,
					Tag:         data.Tag,
					LangType:    data.LangType,
				}
				DB.DefaultConnect().WithContext(newCtx).Table(tableName).Save(updates)

			}
			delErr := errs.ParseError(newCtx, sendErr)

			if sendErr != nil && (delErr.Code == 400018 || delErr.Code == 400021) {
				updates := &model.DfGlobalChallengeSend{
					Status:      2,
					UID:         data.Uid,
					AccountType: int(data.AccountType),
					FsourceID:   FsourceId,
					Tag:         data.Tag,
					LangType:    data.LangType,
				}
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err111:%v",
					sendErr.Error()))
				// 如果是已发货 兼容处理
				// if strings.Contains(sendErr.Error(), "package limit left not enough") {
				DB.DefaultConnect().WithContext(newCtx).Table(tableName).Save(updates)
				// }
			}
			log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("[SendPresent] service err:%v", sendErr))
			// 修改发货状态
			return
		}(v)

	}
	wg.Wait()

	return
}

// AddTeamSyncTeamPoint 加入队伍时更改同步积分
func AddTeamSyncTeamPoint(ctx context.Context, shareCode string) (err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var item *model.DfGlobalChallengeTeam
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeTeam{}.TableName()).
		Where("share_code = ?", shareCode).
		First(&item)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	bPoint := item.BattlefieldPoint
	sPoint := item.SolPoint

	var self *model.DfGlobalChallengeSelf
	db = DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeSelf{}.TableName()).
		Where("uid = ?", userAccount.Uid).
		First(&self)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	bPoint = bPoint + self.BattlefieldPoint
	sPoint = sPoint + self.SolPoint

	db = DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeTeam{}.TableName()).
		Where("share_code = ?", shareCode).
		Updates(&model.DfGlobalChallengeTeam{
			BattlefieldPoint: bPoint,
			SolPoint:         sPoint,
		})
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	return
}

// SyncTeamLeaderAvatar 同步队长头像
func SyncTeamLeaderAvatar(ctx context.Context) (err error) {
	cheduleCtx := context.Background()
	data := make([]*model.DfGlobalChallengeTeam, 0)
	db := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeTeam{}.TableName()).
		Find(&data)
	if db.Error != nil {
		err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	maxGoroutines := 100
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup

	for _, item := range data {
		if item.LeaderUid != "" {
			wg.Add(1)
			go func(item *model.DfGlobalChallengeTeam) {
				semaphore <- struct{}{}
				defer func() {
					<-semaphore
					wg.Done()
				}()
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         item.LeaderUid,
					AccountType: accountPb.AccountType(1),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    strings.Split(item.LeaderUid, "-")[1],
						ChannelId: 131,
						GameId:    strings.Split(item.LeaderUid, "-")[0],
					},
				})
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
				}
				accountInfoProxy := accountTeam.NewTeamClientProxy()
				accountInfoRsp, errG := accountInfoProxy.GetAccountInfo(cheduleCtx, &accountTeam.GetAccountInfoReq{
					FsourceId: "pageV3-1841",
					GameId:    strings.Split(item.LeaderUid, "-")[0],
				}, callopts...)
				if errG != nil {
					err = errG
					return
				}
				db := DB.DefaultConnect().Debug().WithContext(cheduleCtx).Table(model.DfGlobalChallengeTeam{}.TableName()).
					Where("id = ?", item.ID).
					Updates(&model.DfGlobalChallengeTeam{
						LeaderAvatar: accountInfoRsp.UserInfo.UserAvatar,
					})
				if db.Error != nil {
					err = errs.NewSystemError(cheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
						"db error, \t [Error]:{%v} ", db.Error.Error())
					return
				}
			}(item)
		}
	}
	wg.Wait()

	return
}

// GetCountryList TODO
func GetCountryList(ctx context.Context, stage int32) (countryList []*pb.CountryList, err error) {
	countryList = make([]*pb.CountryList, 0)
	data := make([]*model.DfGlobalChallengeRegion, 0)
	db := DB.DefaultConnect().Debug().WithContext(ctx).Table(model.DfGlobalChallengeRegion{}.TableName()).
		Where("country_rank_2 <= ?", 8).
		Order("country_point_3 desc").
		Find(&data)
	if db.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	for _, item := range data {
		countryList = append(countryList, &pb.CountryList{
			CountryId:    int32(item.ID),
			CountryPoint: item.CountryPoint3,
			Country:      item.Country,
		})
	}

	return
}

// GetCountryListRedis TODO
func GetCountryListRedis(ctx context.Context, stage int32) (countryList []*pb.CountryList, err error) {
	redisPrefixKey := global.GetPrefix()
	if stage < 5 {
		return
	}
	countryListRedisKey := fmt.Sprintf("%v-df-global-challenge-country-list", redisPrefixKey)
	countryListJsonStr, err := redis.GetClient().Get(ctx, countryListRedisKey).Result()
	if err != nil && err != redisOrgin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			err.Error())
		return
	}

	if countryListJsonStr != "" {
		err = json.Unmarshal([]byte(countryListJsonStr), &countryList)
		if err != nil {
			// 告警
			errs.NewCustomError(ctx, 611010, "redis get err,redisKey=%v,val=%v,err=%v", countryListRedisKey,
				countryListJsonStr,
				err)
		}
	} else {
		countryList, err = GetCountryList(ctx, stage)
		if err != nil {
			return
		}

		newCountryListJsonStr, errJ := json.Marshal(countryList)
		if errJ != nil {
			err = errJ
			return
		}
		ok, errR := redis.GetClient().SetNX(ctx, countryListRedisKey, newCountryListJsonStr, 12*time.Hour).Result()
		if errR != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis error err=[%v]", errR.Error())
			return
		}
		if !ok {
			log.WithFieldsContext(ctx, "error").Errorf("set redis unsuccess key:[%v], value:[%v]", countryListRedisKey,
				newCountryListJsonStr)
			return
		}

	}
	return
}

// SyncPresentExcel TODO
func SyncPresentExcel(ctx context.Context) (err error) {
	// 查询发了什么奖并且是哪个国家的
	scheduleCtx := context.Background()
	taIds := []string{
		"Wand-**************-P7851464bf3a5",
		"Wand-**************-P45aa75cbd7e4",
		"Wand-**************-Pdff93871b459",
		"Wand-**************-P77a86e55fd74",
		"Wand-**************-Pc0afa71b5891",
		"Wand-**************-Pb5024050f4c8",
		"Wand-**************-P962cb0ecec50",
		"Wand-**************-P8a74b80200e4",
		"Wand-**************-P6e9a4c434f00",
		"Wand-**************-Pd4a8eebed05d",
	}
	data := make([]*model.DfGlobalChallengeSend, 0)
	db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeSend{}.TableName()).
		Where("tag in ?", taIds).
		Find(&data)
	if db.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", db.Error.Error())
		return
	}

	excelData := make([][]string, 0)
	excelData = append(excelData, []string{
		"uid", "country", "present",
	})
	for _, item := range data {
		present := ""
		if item.Tag == "Wand-**************-P7851464bf3a5" {
			present = "预赛前三名国家"
		}
		if item.Tag == "Wand-**************-P77a86e55fd74" {
			present = "总决赛前三名国家"
		}
		if item.Tag == "Wand-**************-Pdff93871b459" {
			present = "总决赛阳光普照"
		}
		if item.Tag == "Wand-**************-P45aa75cbd7e4" {
			present = "预赛阳光普照"
		}
		if item.Tag == "Wand-**************-Pc0afa71b5891" {
			present = "预赛201-500"
		}
		if item.Tag == "Wand-**************-Pb5024050f4c8" {
			present = "预赛101-200"
		}
		if item.Tag == "Wand-**************-P962cb0ecec50" {
			present = "决赛201-500"
		}
		if item.Tag == "Wand-**************-P8a74b80200e4" {
			present = "预赛0-100"
		}
		if item.Tag == "Wand-**************-P6e9a4c434f00" {
			present = "决赛0-100"
		}
		if item.Tag == "Wand-**************-Pd4a8eebed05d" {
			present = "决赛101-200"
		}
		selfData := &model.DfGlobalChallengeSelf{}
		db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeSelf{}.TableName()).
			Where("uid = ?", item.UID).
			First(&selfData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		countryData := model.DfGlobalChallengeRegion{}
		db = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(model.DfGlobalChallengeRegion{}.TableName()).
			Where("id = ?", selfData.CountryId).
			First(&countryData)
		if db.Error != nil && db.Error != gorm.ErrRecordNotFound {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		if db.Error != gorm.ErrRecordNotFound {
			excelData = append(excelData, []string{
				strings.Split(item.UID, "-")[1], countryData.Country, present,
			})
		}
	}
	filepath, err := datadump.CreateExcel(scheduleCtx, "challenge-data.xlsx", excelData)
	if err != nil {
		return
	}
	mediaId, err := datadump.QWXUploadMedia(scheduleCtx, filepath, "268e5d13-f0db-4bf4-afa2-3112dabdd29c",
		"challenge-data.xlsx")
	if err != nil {
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_info").Infof("mediaId: %v", mediaId)
	option := &datadump.SendOption{
		Msgtype: "file",
		Chatid:  "wrkSFfCgAA9d0ZmRiwpSS3Y-xrxLcH4g",
		File: &datadump.FileType{
			MediaId: mediaId,
		},
	}
	_, err = datadump.QWXSend(scheduleCtx, option, "268e5d13-f0db-4bf4-afa2-3112dabdd29c")
	if err != nil {
		return
	}

	return
}
