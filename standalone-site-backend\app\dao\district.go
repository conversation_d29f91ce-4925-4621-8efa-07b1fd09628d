package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

type DistrictConditions struct {
	GameId string
	AreaId string
	Order  []*OrderConditions
	GtId   int64
}

func GetDistrictList(conditions *DistrictConditions, limit int) ([]*model.District, error) {
	var districtList []*model.District
	db := DB.SelectConnect("db_standalonesite").Table((&model.District{}).TableName())
	if limit > 0 {
		db.Limit(limit)
	}
	if conditions.GtId > 0 {
		db = db.Where("id > ?", conditions.GtId)
	}
	if conditions.GameId != "" {
		db = db.Where("game_id = ?", conditions.GameId)
	}
	if conditions.AreaId != "" {
		db = db.Where("area_id = ?", conditions.AreaId)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	err := db.Find(&districtList).Error
	return districtList, err
}

func GetDistrictLanguageList(districtIds []int64) ([]*model.DistrictLanguage, error) {
	var districLanguagList []*model.DistrictLanguage
	db := DB.SelectConnect("db_standalonesite").Table((&model.DistrictLanguage{}).TableName())
	err := db.Where("district_id in ?", districtIds).Find(&districLanguagList).Error
	return districLanguagList, err
}
