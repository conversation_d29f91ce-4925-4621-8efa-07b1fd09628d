package logic

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"

	pb "git.woa.com/trpcprotocol/pytest/testdemo_logic"

	sd "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"

	user "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
)

type PostsImpl struct {
	pb.UnimplementedLogic
}

// GetPosts retrieves posts based on the given request parameters.
// It takes a context and GetPostReq as input, and returns GetPostRsp with post list and count.
// Returns nil error if successful.
func (s *PostsImpl) GetPost(ctx context.Context, req *pb.GetPostDataReq) (*pb.GetPostDataRsp, error) {
	name, page_info := req.Name, req.PageInfo                                                   // 获取请求参数（相当于 body 参数）
	log.Info("Received request:", name, page_info.NextPageCursor, page_info.PreviousPageCursor) // 打印日志

	// 构造两条测试数据
	item1 := &sd.RepliesItem{
		CommentUuid:  "comment-uuid-1",
		PostUuid:     "post-uuid-1",
		ReplyUuid:    "reply-uuid-1",
		IntlOpenid:   "openid-1",
		IsAudit:      1,
		IsStar:       true,
		IsParentDel:  0,
		AtIntlOpenid: "at-openid-1",
		GameId:       "game-1",
		AreaId:       "area-1",
		Title:        "测试标题1",
		Content:      "这是第一条测试内容",
		PicUrls:      []string{"http://example.com/pic1.jpg", "http://example.com/pic2.jpg"},
		UpvoteCount:  100,
		CreatedOn:    1684200000,
		ModifiedOn:   1684203600,
		CanDelete:    true,
		CanReport:    false,
		IsMine:       true,
		IsAuthor:     false,
		User: &user.UserInfo{
			Id:         1,
			Username:   "用户1",
			Avatar:     "http://example.com/avatar1.jpg",
			IsAdmin:    false,
			IntlOpenid: "openid-1",
		},
	}

	item2 := &sd.RepliesItem{
		CommentUuid:  "comment-uuid-2",
		PostUuid:     "post-uuid-2",
		ReplyUuid:    "reply-uuid-2",
		IntlOpenid:   "openid-2",
		IsAudit:      0,
		IsStar:       false,
		IsParentDel:  0,
		AtIntlOpenid: "at-openid-2",
		GameId:       "game-2",
		AreaId:       "area-2",
		Title:        "测试标题2",
		Content:      "这是第二条测试内容",
		PicUrls:      []string{"http://example.com/pic3.jpg"},
		UpvoteCount:  50,
		CreatedOn:    1684300000,
		ModifiedOn:   1684303600,
		CanDelete:    false,
		CanReport:    true,
		IsMine:       false,
		IsAuthor:     true,
		User: &user.UserInfo{
			Id:         2,
			Username:   "用户2",
			Avatar:     "http://example.com/avatar2.jpg",
			IsAdmin:    true,
			IntlOpenid: "openid-2",
		},
	}

	rsp := &pb.GetPostDataRsp{
		Count: 2,
		List:  []*sd.RepliesItem{item1, item2},
	}

	return rsp, nil
}
