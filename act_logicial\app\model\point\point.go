// Package point 个人积分
package point

import "trpc.act.logicial/app/model"

// UserPointConfModel Model
type UserPointConfModel struct {
	model.AppModel
}

// TableName .
func (UserPointConfModel) TableName() string {
	return "user_point_conf"
}

// UserPointConf 临时结构
type UserPointConf struct {
	UserPointConfModel
	ID        int64  `gorm:"type:int(11);column:id;primary_key"`
	FsourceId string `gorm:"type:varchar(255);column:Fsource_id;"`
}

// UserPointTotalModel Model
type UserPointTotalModel struct {
	model.AppModel
}

// TableName .
func (UserPointTotalModel) TableName() string {
	return "user_point_total"
}

// UserPointTotal 临时结构
type UserPointTotal struct {
	UserPointTotalModel
	ID        int64  `gorm:"type:int(11);column:id;primary_key"`
	FsourceId string `gorm:"type:varchar(255);column:Fsource_id;"`
	Point     int32  `gorm:"type:int(11);column:point;"`
	Uid       string `gorm:"type:varchar(255);column:uid;"`
}

// UserPointLogModel Model
type UserPointLogModel struct {
	model.AppModel
}

// TableName .
func (UserPointLogModel) TableName() string {
	return "user_point_log"
}

// UserPointLog 临时结构
type UserPointLog struct {
	UserPointLogModel
	ID         int64  `gorm:"type:int(11);column:id;primary_key"`
	FsourceId  string `gorm:"type:varchar(255);column:Fsource_id;"`
	Point      int32  `gorm:"type:int(11);column:point;"`
	Uid        string `gorm:"type:varchar(255);column:uid;"`
	ActionType string `gorm:"type:varchar(255);column:action_type;"`
	Tag        string `gorm:"type:varchar(255);column:tag;"`
}
