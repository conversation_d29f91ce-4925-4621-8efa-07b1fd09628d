// Package reservation 预约
package reservation

import (
	"trpc.act.logicial/app/model"
)

// ReservationModel 预约记录Model
type ReservationModel struct {
	model.AppModel
}

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名  AllReservationData = all_reservation_data
func (ReservationModel) TableName() string {
	return "reservation_account"
}

// ReservationData 预约
type ReservationData struct {
	ReservationModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(64);column:uid;"`
	AccountType int32  `gorm:"type:tinyint(4);column:account_type;0"`
	SourceID    string `gorm:"type:varchar(255);column:source_id;not null"`
	Status      int32  `gorm:"type:int(1);column:status;not null"`
}

// EmailReservationModel 邮件预约记录Model
type EmailReservationModel struct {
	model.AppModel
}

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名  AllReservationData = all_reservation_data
func (EmailReservationModel) TableName() string {
	return "reservation_account"
}

// EmailReservationData 预约
type EmailReservationData struct {
	EmailReservationModel
	ID              int64  `gorm:"type:int(11);column:id;primary_key"`
	Email           string `gorm:"type:varchar(64);column:email;"`
	LangType        string `gorm:"type:varchar(64);column:lang_type;"`
	ReservationInfo string `gorm:"type:varchar(255);column:reservation_info;not null"`
	SourceID        string `gorm:"type:varchar(255);column:source_id;not null"`
	Status          int32  `gorm:"type:int(1);column:status;not null"`
}
