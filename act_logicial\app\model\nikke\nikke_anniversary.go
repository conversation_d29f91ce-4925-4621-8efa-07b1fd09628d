package nikke

import (
	"time"
	"trpc.act.logicial/app/model"
)

type UserVotingHistoryModel struct {
	model.AppModel
}

func (UserVotingHistoryModel) TableName() string {
	return "nikke0407_user_voting_history"
}

// 竞猜-投票记录表
type UserVotingHistory struct {
	UserVotingHistoryModel
	ID          uint   `gorm:"type:int(11);column:id;primary_key" comment:"primary key"`
	UID         string `gorm:"type:varchar(64);column:uid" comment:"用户ID"`
	AccountType int    `gorm:"type:smallint(6);column:account_type" comment:"用户类型"`
	TagID       int    `gorm:"column:tag_id" comment:"活动标识id,1:竞猜，2:投票"`
	VoteIDList  string `gorm:"type:varchar(512);column:vote_id_list" comment:"被投票对象id列表"`
}

type NikkeAllVotingRecordModel struct {
	model.AppModel
}

func (NikkeAllVotingRecordModel) TableName() string {
	return "nikke0407_all_voting_records"
}

// 总体普通投票记录表
type NikkeAllVotingRecord struct {
	NikkeAllVotingRecordModel
	ID      uint      `gorm:"type:int(11);column:id;primary_key" comment:"primary key"`
	Fday    time.Time `gorm:"type:date;column:Fday" comment:"日期格式2006-01-02"`
	VoteID  int32     `gorm:"column:vote_id" comment:"被投票对象id"`
	VoteNum uint64    `gorm:"type:int(64) unsigned;column:vote_num" comment:"得票数"`
}

type VoteItem struct {
	VoteID  int32 `json:"vote_id"`
	VoteNum int32 `json:"vote_num"`
}

type NikkeGiftDistributionRecordModel struct {
	model.AppModel
}

func (NikkeGiftDistributionRecordModel) TableName() string {
	return "nikke0407_gift_distribution_record"
}

// 礼包发放记录表
type NikkeGiftDistributionRecord struct {
	NikkeGiftDistributionRecordModel
	ID          uint   `gorm:"type:int;column:id;primary_key" comment:"primary key"`
	UID         string `gorm:"type:varchar(64);column:uid" comment:"用户ID"`
	AccountType int    `gorm:"type:smallint;column:account_type" comment:"用户类型"`
	RoleInfo    string `gorm:"type:varchar(512);column:role_info" comment:"角色信息"`
	PresentID   string `gorm:"type:varchar(64);column:present_id" comment:"礼包id"`
	LangType    string `gorm:"type:varchar(64);column:lang_type" comment:"语言类型"`
	Status      int8   `gorm:"type:tinyint;column:status" comment:"当前状态0:未发货;1:已发货"`
}

type UserParticipationModel struct {
	model.AppModel
}

func (UserParticipationModel) TableName() string {
	return "nikke0407_user_participation"
}

// 用户参与表
type UserParticipation struct {
	UserParticipationModel
	ID          uint   `gorm:"type:int(11);column:id;primary_key" comment:"primary key"`
	UID         string `gorm:"type:varchar(64);column:uid" comment:"用户ID"`
	AccountType int    `gorm:"type:smallint(6);column:account_type" comment:"用户类型"`
}

type RoleInfo struct {
	GameId   string `json:"game_id,omitempty"`   // 游戏id
	AreaId   int64  `json:"area_id,omitempty"`   // 大区id
	ZoneId   int64  `json:"zone_id,omitempty"`   // 小区id
	PlatId   int64  `json:"plat_id,omitempty"`   //平台id
	RoleId   string `json:"role_id,omitempty"`   // 角色id
	RoleName string `json:"role_name,omitempty"` // 角色名
	GameName string `json:"game_name,omitempty"` // 游戏名称
}

type AreaVotingCountModel struct {
	model.AppModel
}

func (AreaVotingCountModel) TableName() string {
	return "nikke0407_area_voting_count"
}

type AreaVotingCount struct {
	ID       uint   `gorm:"primary_key" json:"id"`                               // primary key
	AreaID   string `gorm:"type:varchar(50);not null;default:''" json:"area_id"` // 大区ID
	VoteType int    `gorm:"type:smallint;not null;default:0" json:"vote_type"`   // 投票类型1:竞猜，2:普通，3:特殊
	VoteID   int    `gorm:"not null;default:0" json:"vote_id"`                   // 被投票对象id
	CountNum uint64 `gorm:"not null;default:0" json:"count_num"`                 // 得票数
}
