// Package nikkembti 活动
package nikkembti

import (
	"context"

	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_mbti"
	nikkembti "trpc.act.logicial/app/logic/nikke_mbti"
)

// NikkeMbtiImpl TODO
type NikkeMbtiImpl struct {
	pb.UnimplementedNikkeMbti
}

// NikkeMbtiAddLog mbti添加发奖记录
func (s *NikkeMbtiImpl) NikkeMbtiAddLog(
	ctx context.Context,
	req *pb.NikkeMbtiAddLogReq,
) (*pb.NikkeMbtiAddLogRsp, error) {
	rsp := &pb.NikkeMbtiAddLogRsp{}

	err := nikkembti.NikkeMbtiAddLog(ctx, req)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

// ScheduledSendMbti mbti发奖
func (s *NikkeMbtiImpl) ScheduledSendMbti(
	ctx context.Context,
	req *pb.ScheduledSendMbtiReq,
) (rsp *pb.ScheduledSendMbtiRsp, err error) {
	rsp = &pb.ScheduledSendMbtiRsp{}

	go nikkembti.ScheduledSendMbti(ctx)

	return
}
