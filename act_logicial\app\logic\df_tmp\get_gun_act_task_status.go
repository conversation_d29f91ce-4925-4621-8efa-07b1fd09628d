package df_tmp

import (
	"context"
	"fmt"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"time"
	"trpc.act.logicial/app/cache"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/logic/df_tmp_common"
	"trpc.act.logicial/app/model/df_activity"
	"trpc.act.logicial/app/mysql/df_activity_repo"
)

// GetGunActTaskStatusProc
type GetGunActTaskStatusProc struct {
	userInfo *accountPb.UserAccount
}

// GetGunActTaskStatus 查询拼枪活动任务状态
func GetGunActTaskStatus(ctx context.Context, req *pb.GetGunActTaskStatusReq) (*pb.GetGunActTaskStatusRsp, error) {
	log.DebugContextf(ctx, "GetGunActTaskStatus enter, req: %v", req)
	rsp := &pb.GetGunActTaskStatusRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "GetGunActTaskStatus get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	proc := &GetGunActTaskStatusProc{
		userInfo: &userAccount,
	}
	defer func() {
		if nil != err {
			log.ErrorContextf(ctx, "GetGunActTaskStatus rsp error:%v", err)
		}
	}()
	err = proc.Process(ctx, req, rsp)
	if nil != err {
		return nil, err
	}
	return rsp, nil
}
func (p *GetGunActTaskStatusProc) Process(ctx context.Context,
	req *pb.GetGunActTaskStatusReq, rsp *pb.GetGunActTaskStatusRsp) error {
	// 查询任务
	tasks, err := df_activity_repo.GunActivityRepoClient.GetTasksByUser(ctx,
		req.GameId, req.ActivityId, p.userInfo.Uid, req.GunId, 1) // 只取最新的一个任务
	if nil != err {
		log.ErrorContextf(ctx, "GetGunActTaskStatusProc GetTasksByUser error: %v", err)
		return code.ErrSystemError
	}
	if tasks == nil || len(tasks) <= 0 {
		log.ErrorContextf(ctx, "GetGunActTaskStatusProc GetTasksByUser error: %v", err)
		return code.ErrActivityNotFindTask
	}
	task := tasks[0]
	rsp.TaskId = task.TaskId
	switch task.Status {
	case df_activity.GunTaskStatusProcessing:
		rsp.Status = pb.GunActTaskStatus_Processing
	case df_activity.GunTaskStatusWaitClaimPrize:
		rsp.Status = pb.GunActTaskStatus_WaitClaimPrize
	case df_activity.GunTaskStatusWaitDistributePrize:
		rsp.Status = pb.GunActTaskStatus_WaitDistributePrize
	case df_activity.GunTaskStatusGetPrizeOk,
		df_activity.GunTaskStatusGetPrizeFail: // 发奖错误状态，也是完成状态，后台会重试发奖
		rsp.Status = pb.GunActTaskStatus_Finish
	case df_activity.GunTaskStatusNotCompleted:
		rsp.Status = pb.GunActTaskStatus_NotComplete
	}
	rsp.GunId = task.GunId

	if rsp.Status == pb.GunActTaskStatus_Processing {
		// 拼枪剩余时间
		remainTime := p.getRemainTime(ctx, task)
		if remainTime < 0 {
			remainTime = 0
		}
		rsp.RemainTime = uint32(remainTime)
		// 拼枪任务还差多少人
		remainNum := p.getRemainNum(ctx, task)
		if remainNum < 0 {
			remainNum = 1 // 异常，返回1
		}
		rsp.RemainNum = uint32(remainNum)
		rsp.ShareLink = df_tmp_common.GetGunActShareUrl(req.GameId, req.ActivityId, task.TaskId)
	}
	return nil
}
func (p *GetGunActTaskStatusProc) getRemainTime(ctx context.Context, task *df_activity.GunActivityTask) int64 {
	//查询活动
	key := fmt.Sprintf("%s_%s", task.GameId, task.ActivityId)
	act, err := cache.GetGunActivityFromCache(ctx, key)
	if err != nil {
		log.ErrorContextf(ctx, "getRemainTime GetGunActivityFromCache err: %v, key: %v",
			err, key)
		return 0
	}
	if nil == act { // 活动不存在
		return 0
	}

	// 任务本来结束的时间
	createTimeStamp := df_tmp_common.GetMysqlTimeStamp(task.CreateTime) // 修正时间
	finishTime := createTimeStamp + int64(config.GetConfig().GunActTaskExpireTime)
	if finishTime > int64(act.EndTimestamp) { // 但是必须在活动结束之前完成
		finishTime = int64(act.EndTimestamp)
	}
	loc, _ := time.LoadLocation("Asia/Shanghai")
	localTime := time.Now().In(loc)
	log.DebugContextf(ctx, "getRemainTime finishTime: %v, CreateTime: %v, createTimeStamp: %v, fixCreateTimeStamp: %v"+
		"localTime: %v, localTimeStamp: %v, taskId: %v",
		finishTime, task.CreateTime, task.CreateTime.Unix(), createTimeStamp, localTime, localTime.Unix(), task.TaskId)
	return finishTime - time.Now().Unix()
}

func (p *GetGunActTaskStatusProc) getRemainNum(ctx context.Context, task *df_activity.GunActivityTask) int32 {
	// 获取枪械拼枪完成任务数
	needHelpNum, err := df_tmp_common.GetHelpNumByGunId(task.GunId)
	if nil != err {
		log.ErrorContextf(ctx, "GetGunActTaskStatusProc GetHelpNumByGunId error:%v, taskId: %v", err, task.TaskId)
		return -1
	}

	// 查询任务已经完成的助力数
	num, err := df_activity_repo.GunActivityRepoClient.GetTaskHelpNum(ctx, task.TaskId, 0)
	if nil != err {
		log.ErrorContextf(ctx, "GetGunActTaskStatusProc GetTaskHelpNum error:%v, taskId: %v",
			err, task.TaskId)
		return -1
	}
	if int32(num) < needHelpNum { // 还未完成，返回剩余人数
		return needHelpNum - int32(num)
	}
	return 0
}
