package nba_tmp

import (
	"context"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nba_tmp"
	. "trpc.act.logicial/app/logic/nba_tmp"
)

type NbaTmpImpl struct {
	pb.UnimplementedNbaTmp
}

// SendRequestByRegionCode 根据地区编码发送请求
func (s *NbaTmpImpl) SendRequestByRegionCode(ctx context.Context, req *pb.SendRequestByRegionCodeReq) (
	rsp *pb.SendRequestByRegionCodeRsp, err error) {
	regionRsp, err := SendRequestByRegionCode(ctx, req.RegionCode, req.InvitationCode)
	if err != nil {
		return nil, err
	}
	return &pb.SendRequestByRegionCodeRsp{
		Ret: regionRsp.Data.Ret,
	}, nil
}
