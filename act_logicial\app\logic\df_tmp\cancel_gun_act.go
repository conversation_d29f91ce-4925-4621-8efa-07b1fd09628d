package df_tmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/mysql/df_activity_repo"
)

// CancelGunActProc
type CancelGunActProc struct {
	userInfo *accountPb.UserAccount
}

// CancelGunAct 取消拼枪活动任务
func CancelGunAct(ctx context.Context, req *pb.CancelGunActReq) (*pb.CancelGunActRsp, error) {
	log.DebugContextf(ctx, "CancelGunAct enter, req: %v", req)
	rsp := &pb.CancelGunActRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "CancelGunAct get userAccount error:%v", err)
		return nil, code.ErrUserNotLoginError
	}
	proc := &CancelGunActProc{
		userInfo: &userAccount,
	}
	defer func() {
		if nil != err {
			log.ErrorContextf(ctx, "CancelGunAct rsp error:%v", err)
		}
	}()
	err = proc.Process(ctx, req)
	if nil != err {
		return nil, err
	}
	return rsp, nil
}
func (p *CancelGunActProc) Process(ctx context.Context,
	req *pb.CancelGunActReq) error {
	// 查询任务
	tasks, err := df_activity_repo.GunActivityRepoClient.GetTaskByTaskId(ctx, req.TaskId)
	if nil != err {
		log.ErrorContextf(ctx, "CancelGunActProc GetTaskByTaskId error: %v", err)
		return code.ErrSystemError
	}
	if tasks == nil || len(tasks) <= 0 {
		log.ErrorContextf(ctx, "CancelGunActProc GetTaskByTaskId error: %v", err)
		return code.ErrActivityNotFindTask
	}
	task := tasks[0]
	if task.UserId != p.userInfo.Uid {
		log.ErrorContextf(ctx, "CancelGunActProc GetTaskByTaskId user id not match, task userId: %v,"+
			"req userId: %v", task.UserId, p.userInfo.Uid)
		return code.ErrActivityNotFindTask
	}

	// 更新状态
	affectRows, err := df_activity_repo.GunActivityRepoClient.CancelTask(ctx, req.TaskId)
	if nil != err {
		log.ErrorContextf(ctx, "CancelGunActProc CancelTask error: %v", err)
		return code.ErrSystemError
	}
	if affectRows <= 0 { // 说明任务已经不是助力状态，无法取消
		log.ErrorContextf(ctx, "CancelGunActProc CancelTask affectRows <= 0")
		return code.ErrActivityTaskCanNotCancel
	}
	return nil
}
