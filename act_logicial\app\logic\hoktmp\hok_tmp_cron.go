package hoktmp

import (
	"context"
	"encoding/json"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	deltaversePb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/deltaverse"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	"git.code.oa.com/trpcprotocol/publishing_marketing/game"
	gameHokPb "git.woa.com/trpcprotocol/publishing_marketing/game_hok"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/global"
)

// HokRankItem TODO
type HokRankItem struct {
	Rank      int32   `json:"rank"`
	Name      string  `json:"name"`
	HeadUrl   string  `json:"head_url"`   // 头像链接
	HeatValue float64 `json:"heat_value"` // 热度值
	OpenId    string  `json:"open_id"`
}

// HokRankResponse TODO
type HokRankResponse struct {
	Code    int               `json:"code"`
	Msg     string            `json:"msg"`
	TraceID string            `json:"traceId"`
	Data    map[string]string `json:"data"`
}

// Data TODO
type Data struct {
	A1 string `json:"a1"`
}

// HokGameId TODO
const HokGameId = "29134"

func isHTTPPrefix(s string) bool {
	return strings.HasPrefix(s, "http")
}

func getHokHeadUrl(headUrl string) string {
	var (
		// GameSysAvatarPrefix 游戏预置头像前缀
		GameSysAvatarPrefix = "https://res.sgameglobal.com/Global/Common/UGUI/SystemRes/044_Idip/PlayerHeadIcon/"
		// GameSysAvatarMap 游戏预置头像映射
		GameSysAvatarMap = map[string]string{
			"@99":  GameSysAvatarPrefix + "1.png",
			"@100": GameSysAvatarPrefix + "2.png",
			"@101": GameSysAvatarPrefix + "3.png",
			"@102": GameSysAvatarPrefix + "4.png",
			"@103": GameSysAvatarPrefix + "5.png",
			"@120": GameSysAvatarPrefix + "6.png",
			"@130": GameSysAvatarPrefix + "7.png",
		}
	)
	headUrl = decodeURIComponent(headUrl)
	ok := isHTTPPrefix(headUrl)
	headUrlMapStr, ok2 := GameSysAvatarMap[headUrl]
	switch {
	case ok:
		return headUrl
	case ok2:
		return headUrlMapStr
	default:
		return "https://res.sgameglobal.com/Global/Common/UGUI/SystemRes/044_Idip/PlayerHeadIcon/empty.png"
	}
}

func decodeURIComponent(str string) string {
	unescape, _ := url.QueryUnescape(str)
	return unescape
}

func getAreaItemByEnv(envName string) (int64, int64, int64) {
	var areaId, zoneId, platId int64
	if envName == "dev" {
		//areaId, zoneId, platId = 88, 108003, 1
		areaId, zoneId, platId = 88, 108001, 8
	} else {
		areaId, zoneId, platId = 8, 108011, 1
	}
	return areaId, zoneId, platId
}

func getHOKMoreUserInfo(ctx context.Context, list []HokRankItem) ([]HokRankItem, error) {
	log.WithFieldsContext(ctx, "log_type", "debug").Infof("getHOKMoreUserInfo show list:[%v]", list)
	hokEventsClub := config.GetConfig().HOKEventsClub
	envName := hokEventsClub.Env
	areaId, zoneId, platId := getAreaItemByEnv(envName)
	proxy := gameHokPb.NewHokClientProxy()
	pageSize := 10
	var wg sync.WaitGroup
	var userRoleItemMap sync.Map
	var allHokRankList []HokRankItem
	for i := 0; i < len(list); i += pageSize {
		end := i + pageSize
		if end > len(list) {
			end = len(list)
		}
		for _, userInfo := range list[i:end] {
			wg.Add(1)
			go func(userItem HokRankItem) {
				defer wg.Done()
				uid := strings.Join([]string{HokGameId, userItem.OpenId}, "-")
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         uid,
					AccountType: accountPb.AccountType_INTL,
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    userItem.OpenId,
						ChannelId: 3,
					},
				})
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					// client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				req := &game.GetRoleInfoReq{
					GameId:    HokGameId,
					AreaId:    areaId,
					ZoneId:    zoneId,
					PlatId:    platId,
					Partition: 0,
				}
				moreRoleInfo, err := proxy.GetHOKMoreRoleInfo(ctx, req, callopts...)
				if err != nil {
					log.WithFieldsContext(ctx, "log_type", "debug").Infof(
						"getHOKMoreUserInfo GetHOKMoreRoleInfo req:[%v], callopts:[%v], err:[%v]", req, callopts, err)
					return
				}
				// 处理头像
				headUrl := getHokHeadUrl(moreRoleInfo.HeadUrl)
				userRoleItemMap.Store(userItem.OpenId, HokRankItem{
					Name:    decodeURIComponent(moreRoleInfo.RoleInfo.RoleName),
					HeadUrl: headUrl,
					OpenId:  userItem.OpenId,
				})
			}(userInfo)
		}
		wg.Wait()
	}
	for _, v := range list {
		value, ok := userRoleItemMap.Load(v.OpenId)
		if !ok {
			log.WithFieldsContext(ctx, "log_type", "debug").Infof(
				"getHOKMoreUserInfo GetHOKMoreRoleInfo openid not exit, v.OpenId:[%v]", v.OpenId)
			// 未获取到用户信息放入空数据
			allHokRankList = append(allHokRankList, HokRankItem{
				Rank:      v.Rank,
				HeadUrl:   getHokHeadUrl(""),
				HeatValue: v.HeatValue,
				OpenId:    v.OpenId,
			})
			continue
		}
		rankItem := value.(HokRankItem)
		rankItem.Rank = v.Rank
		rankItem.HeatValue = v.HeatValue
		allHokRankList = append(allHokRankList, rankItem)
	}
	return allHokRankList, nil
}

func getHokRankCacheKey() string {
	return global.GetRedisKey("club_hot_hok_rank_v2")
}

// SyncHOKHotRankV2 HOK二期排行榜数据定时同步
func SyncHOKHotRankV2(ctx context.Context) error {

	hokEventsClub := config.GetConfig().HOKEventsClub
	envName := hokEventsClub.Env
	var env int
	if envName == "dev" {
		env = 1
	}
	paramMap := make(map[string]interface{})
	paramMap["env"] = env

	response, err := deltaversePb.SendRequest(ctx, deltaversePb.SendRequestParam{
		ServiceType:        "hok",
		DestinationService: "dmfeature-13484",
		Path:               "/dmfeature/13484/clubHotRank",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		return err
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof("SyncHOKHotRankV2 show response:[%v]", response)
	var hokRankResponse HokRankResponse
	jsonErr := json.Unmarshal([]byte(response), &hokRankResponse)
	if jsonErr != nil {
		err = errs.NewCustomError(ctx, code.JsonParseError,
			"SyncHOKHotRankV2 Unmarshal err,result=%v, \t [Error]:{%v} ", response, jsonErr)
		return err
	}
	// 获取排行榜数据
	var hokRankList []HokRankItem
	var rspStr string
	for _, v := range hokRankResponse.Data {
		rspStr = v
		break
	}
	hokRankListStr := strings.Split(rspStr, ";")
	for _, v := range hokRankListStr {
		if v == "" || v == "[" || v == "]" {
			continue
		}
		userRankItem := strings.Split(v, ",")
		if len(userRankItem) != 3 {
			continue
		}
		hokRankList = append(hokRankList, HokRankItem{
			Rank:      cast.ToInt32(userRankItem[2]),
			HeatValue: cast.ToFloat64(userRankItem[1]),
			OpenId:    userRankItem[0],
		})
	}
	// 根据openid批量获取用户数据
	hokRankItems, err := getHOKMoreUserInfo(ctx, hokRankList)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "debug").Infof(
			"SyncHOKHotRankV2 getHOKMoreUserInfo; hokRankList:[%v],err:[%v]", hokRankList, err)
		return err
	}
	// 数据缓存redis
	cacheMap := make(map[string]interface{}, len(hokRankItems))
	for _, v := range hokRankItems {
		hokRankItemStr, _ := json.Marshal(v)
		if err != nil {
			return err
		}
		cacheMap[v.OpenId] = string(hokRankItemStr)
	}
	if len(cacheMap) == 0 {
		log.WithFieldsContext(ctx, "log_type", "debug").Infof(
			"SyncHOKHotRankV2 cacheMap len == 0; hokRankItems:[%v]", hokRankItems)
		return nil
	}
	cacheKey := getHokRankCacheKey()
	// 缓存1小时
	expiration := 10 * 24 * time.Hour
	// 先删除旧数据再新增
	pipeline := redis.GetClient().Pipeline()
	pipeline.Del(ctx, cacheKey)
	pipeline.HSet(ctx, cacheKey, cacheMap)
	pipeline.Expire(ctx, cacheKey, expiration)
	if _, err = pipeline.Exec(ctx); err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr,
			"db error, \t [Error]:{%v} ", err)
		return err
	}

	return nil
}
