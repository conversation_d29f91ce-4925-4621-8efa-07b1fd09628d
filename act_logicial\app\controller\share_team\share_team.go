// Package share_team 组队
package share_team

import (
	"context"
	"fmt"
	"strings"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/report"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/tglog"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	accountTeam "git.woa.com/trpcprotocol/publishing_marketing/account_team"
	recordPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_record"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_share_team"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/controller/record"
	"trpc.act.logicial/app/model/invitation"
	"trpc.act.logicial/app/util"
)

// TeamServiceImpl 结构体
type TeamServiceImpl struct{}

func (s *TeamServiceImpl) getShareInfoList(ctx context.Context, shareCodeStruct *util.AutoCode,
	sourceId string) ([]*invitation.Invitation, error) {
	var shareInfoList []*invitation.Invitation
	// 获取车队信息
	tableName, err := util.GetShareTableName(ctx, sourceId)
	if err != nil {
		return shareInfoList, err
	}
	condition := map[string]interface{}{
		"uid":          shareCodeStruct.Ai,
		"account_type": shareCodeStruct.At,
		"Fsource_id":   sourceId,
		"status":       1,
		"is_delete":    0,
	}
	err = DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition).Find(&shareInfoList).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return shareInfoList, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}

	return shareInfoList, nil
}

func (s *TeamServiceImpl) getTeamInfoList(ctx context.Context, shareCode *util.AutoCode,
	shareInfoList []*invitation.Invitation, sourceId string, gameId string) ([]*accountTeam.UserInfo, error) {

	var accountUsers []*accountPb.UserAccount

	// 插入队长
	accountUsers = append(accountUsers, &accountPb.UserAccount{
		Uid:         shareCode.Ai,
		AccountType: accountPb.AccountType(shareCode.At),
	})

	for _, item := range shareInfoList {
		accountUsers = append(accountUsers, &accountPb.UserAccount{
			Uid:         item.InviteeUid,
			AccountType: accountPb.AccountType(item.InviteeAccountType),
		})
	}
	accountTeamProxy := accountTeam.NewTeamClientProxy()
	accountTeamRsp, err := accountTeamProxy.GetTeamInfoList(ctx, &accountTeam.GetTeamInfoListReq{Users: accountUsers,
		FsourceId: sourceId, GameId: gameId})
	if err != nil {
		return nil, err
	}
	return accountTeamRsp.GetUsers(), nil
}

// GetTeamListNoLogin 获取队伍列表（无登录态）
func (s *TeamServiceImpl) GetTeamListNoLogin(ctx context.Context, req *pb.GetTeamListReq) (*pb.GetTeamListNoLoginRsp,
	error) {
	rsp := &pb.GetTeamListNoLoginRsp{ShareCode: req.ShareCode}
	if len(req.ShareCode) == 0 {
		return rsp, nil
	}
	// 解析分享码
	shareCodeIdStruct, codeErr := util.ParseCodeById(req.ShareCode)
	if codeErr != nil {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode, "[GetTeamListNoLogin] decode shareCode err",
			codeErr))
		return rsp, nil
	}
	if shareCodeIdStruct.Si != req.FsourceId {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode,
			"[GetTeamListNoLogin] shareCode source Id not match, shareCode.Si:%s", shareCodeIdStruct.Si))
		return rsp, nil
	}
	recordService := &record.RecordImpl{}
	initRecordRsp, errR := recordService.GetRecordDataById(ctx, &recordPb.GetRecordDataByIdReq{
		FsourceId: shareCodeIdStruct.Si, RecordId: shareCodeIdStruct.Id})
	if errR != nil {
		return rsp, errR
	}
	shareCodeAutoStruct := &util.AutoCode{
		Ai: initRecordRsp.Uid,
		At: initRecordRsp.AccountType,
		Si: shareCodeIdStruct.Si,
	}
	shareInfoList, err := s.getShareInfoList(ctx, shareCodeAutoStruct, req.FsourceId)
	if err != nil {
		log.WithContext(ctx).Errorf("[GetTeamListNoLogin] shareInfoList err {%v}", err)
		return rsp, err
	}
	teamInfoList, err := s.getTeamInfoList(ctx, shareCodeAutoStruct, shareInfoList, req.FsourceId, req.GameId)
	if err != nil {
		log.WithContext(ctx).Errorf("[GetTeamListNoLogin] teamInfoList err{%v}", err)
		return rsp, err
	}
	rsp.TeamList = teamInfoList
	return rsp, nil
}

func (s *TeamServiceImpl) getMyShareCode(ctx context.Context, sourceId string) (shareCode string, err error) {

	recordService := &record.RecordImpl{}
	initRecordRsp, errR := recordService.InitRecord(ctx, &recordPb.InitRecordReq{FsourceId: sourceId})
	if errR != nil {
		err = errR
		return
	}

	shareCode, err = util.GetShareCodeById(initRecordRsp.RecordId, sourceId)
	if err != nil {
		return
	}
	return
}

// GetTeamList 获取队伍列表
func (s *TeamServiceImpl) GetTeamList(ctx context.Context, req *pb.GetTeamListReq) (*pb.GetTeamListRsp, error) {
	rsp := &pb.GetTeamListRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return rsp, err
	}

	// ShareCode为空就拿自己的车队
	shareCode := req.ShareCode
	if len(shareCode) == 0 {
		shareCode, err = s.getMyShareCode(ctx, req.FsourceId)
		if err != nil {
			return rsp, err
		}
	}

	// 解析分享码
	shareCodeIdStruct, codeErr := util.ParseCodeById(shareCode)

	// 解析分享码错误，就拿自己的队伍信息
	if codeErr != nil {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode, "[GetTeamList] decode shareCode err",
			codeErr))
		shareCode, err = s.getMyShareCode(ctx, req.FsourceId)
		if err != nil {
			return rsp, err
		}
		shareCodeIdStruct, codeErr = util.ParseCodeById(shareCode)
		if codeErr != nil {
			log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode, "[GetTeamList] self decode shareCode err",
				codeErr))
			return rsp, codeErr
		}
	}

	if shareCodeIdStruct.Si != req.FsourceId {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode,
			"[GetTeamList] shareCode source Id not match, shareCode.Si:%s", shareCodeIdStruct.Si))
		return rsp, nil
	}
	recordService := &record.RecordImpl{}
	initRecordRsp, errR := recordService.GetRecordDataById(ctx, &recordPb.GetRecordDataByIdReq{
		FsourceId: shareCodeIdStruct.Si, RecordId: shareCodeIdStruct.Id})
	if errR != nil {
		return rsp, errR
	}
	shareCodeAutoStruct := &util.AutoCode{
		Ai: initRecordRsp.Uid,
		At: initRecordRsp.AccountType,
		Si: shareCodeIdStruct.Si,
	}

	shareInfoList, err := s.getShareInfoList(ctx, shareCodeAutoStruct, req.FsourceId)
	if err != nil {
		return rsp, err
	}
	teamInfoList, err := s.getTeamInfoList(ctx, shareCodeAutoStruct, shareInfoList, req.FsourceId, req.GameId)
	if err != nil {
		return rsp, err
	}

	rsp.TeamList = teamInfoList
	isLeader := userAccount.Uid == shareCodeAutoStruct.Ai && int32(userAccount.AccountType) == shareCodeAutoStruct.At
	selfIndex := -1
	if isLeader {
		selfIndex = 0
	} else {
		for i := range shareInfoList {
			info := shareInfoList[i]
			if userAccount.Uid == info.InviteeUid && int32(userAccount.AccountType) == info.InviteeAccountType {
				selfIndex = i + 1
			}
		}
	}

	rsp.IsLeader = isLeader
	rsp.SelfIndex = int32(selfIndex)
	rsp.ShareCode = shareCode

	return rsp, nil
}

// JoinTeam 加入队伍 1
func (s *TeamServiceImpl) JoinTeam(ctx context.Context, req *pb.JoinTeamReq) (*pb.JoinTeamRsp, error) {
	rsp := &pb.JoinTeamRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return rsp, err
	}
	if len(req.ShareCode) == 0 {
		log.WithContext(ctx).Errorf("[JoinTeam] ShareCode.length = 0")
		return rsp, nil
	}
	// 检查share code合法性
	shareCodeIdStruct, err := util.ParseCodeById(req.ShareCode)
	if err != nil {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode, "[JoinTeam] decode shareCode err", err))
		return rsp, nil
	}
	if shareCodeIdStruct.Si != req.FsourceId {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode,
			"[JoinTeam] shareCode source Id not match, shareCode.Si:%s", shareCodeIdStruct.Si))
		return rsp, nil
	}

	recordService := &record.RecordImpl{}
	initRecordRsp, errR := recordService.GetRecordDataById(ctx, &recordPb.GetRecordDataByIdReq{
		FsourceId: shareCodeIdStruct.Si, RecordId: shareCodeIdStruct.Id})
	if errR != nil {
		return rsp, errR
	}
	shareCodeAutoStruct := &util.AutoCode{
		Ai: initRecordRsp.Uid,
		At: initRecordRsp.AccountType,
		Si: shareCodeIdStruct.Si,
	}

	// 自己是队长
	if userAccount.Uid == shareCodeAutoStruct.Ai && int32(userAccount.AccountType) == shareCodeAutoStruct.At {
		rsp.JoinSuccess = false
		return rsp, nil
	}

	tableName, err := util.GetShareTableName(ctx, req.FsourceId)
	if err != nil {
		return rsp, err
	}

	// 检查是否已加入
	checkCondition := map[string]interface{}{
		"uid":                  shareCodeAutoStruct.Ai,
		"account_type":         shareCodeAutoStruct.At,
		"invitee_uid":          userAccount.Uid,
		"invitee_account_type": userAccount.AccountType,
		"Fsource_id":           req.FsourceId,
		"status":               1,
		"is_delete":            0,
	}
	first := &invitation.Invitation{}
	result := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(checkCondition).First(&first)
	if result.Error == nil {
		log.WithContext(ctx).Infof("[JoinTeam] user:%s already join team", userAccount.Uid)
		return rsp, nil

	} else if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return rsp, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}

	log.WithContext(ctx).Infof("[JoinTeam] user:%s not join team yet", userAccount.Uid)

	// 检查玩家加入车队上限
	canJoinRsp, err := s.GetCanJoinTeamNum(ctx, &pb.GetCanJoinTeamNumReq{FsourceId: req.FsourceId,
		MaxJoinCount: req.MaxJoinCount})
	if err != nil {
		return rsp, err
	}
	if canJoinRsp.CanJoinTeamNum <= 0 {
		return rsp, errs.NewCustomError(ctx, code.MaxJoinCountErr, "[JoinTeam] CanJoinTeamNum: %d ",
			canJoinRsp.CanJoinTeamNum)
	}

	log.WithContext(ctx).Infof("[JoinTeam] user:%s CanJoinTeamNum: %d, try to join", userAccount.Uid,
		canJoinRsp.CanJoinTeamNum)

	// 检查车队是否满人
	teamCondition := map[string]interface{}{
		"uid":          shareCodeAutoStruct.Ai,
		"account_type": shareCodeAutoStruct.At,
		"Fsource_id":   req.FsourceId,
		"status":       1,
		"is_delete":    0,
	}
	var shareNum int64
	err = DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(teamCondition).Count(&shareNum).Error
	if err != nil {
		return rsp, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}

	if shareNum >= int64(req.MaxShareCount) {
		return rsp, errs.NewCustomError(ctx, code.TeamCountEnough, "[JoinTeam] shareNum: %d >= 4", shareNum,
			req.MaxShareCount)
	}

	log.WithContext(ctx).Infof("[JoinTeam] shareNum: %d, try to join", shareNum)

	// 加入车队
	invitationData := invitation.Invitation{
		UID:                shareCodeAutoStruct.Ai,
		AccountType:        shareCodeAutoStruct.At,
		InviteeUid:         userAccount.Uid,
		InviteeAccountType: int32(userAccount.AccountType),
		FsourceId:          shareCodeAutoStruct.Si,
		Status:             1,
		IsDelete:           0,
	}
	err = DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(checkCondition).FirstOrCreate(&invitationData).Error
	if err != nil {
		return rsp, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	rsp.JoinSuccess = true
	log.WithFieldsContext(ctx, "log_type", "logicial_team", "source_id", req.FsourceId, "logicial_team_type", "join_team").
		Infof("join team")
	return rsp, nil
}

// CreateTeam 创建队伍（空实现，用于上报)
func (s *TeamServiceImpl) CreateTeam(ctx context.Context, _ *pb.CreateTeamReq) (*pb.CreateTeamRsp, error) {
	rsp := &pb.CreateTeamRsp{}
	// userAccount, err := metadata.GetUserAccount(ctx)
	// if err != nil {
	// 	return rsp, err
	// }
	// log.WithContext(ctx).Infof("[CreateTeam] user:%s CreateTeam", userAccount.Uid)

	return rsp, nil
}

// IsMyTeam 是否是我的队伍
func (s *TeamServiceImpl) IsMyTeam(ctx context.Context, req *pb.IsMyTeamReq) (*pb.IsMyTeamRsp, error) {
	rsp := &pb.IsMyTeamRsp{}
	if !req.ToConfigureValue.IsLeader {
		return rsp, errs.NewCustomError(ctx, code.IsNotMyTeam, "[IsMyTeam] IsNotMyTeam")
	}

	rsp.IsMyTeam = true
	return rsp, nil
}

// GetCanJoinTeamNum 活动可以加入队伍数
func (s *TeamServiceImpl) GetCanJoinTeamNum(ctx context.Context, req *pb.GetCanJoinTeamNumReq) (
	*pb.GetCanJoinTeamNumRsp, error) {
	rsp := &pb.GetCanJoinTeamNumRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return rsp, err
	}
	tableName, err := util.GetShareTableName(ctx, req.FsourceId)
	if err != nil {
		return rsp, err
	}
	inviteeCondition := map[string]interface{}{
		"invitee_uid":          userAccount.Uid,
		"invitee_account_type": userAccount.AccountType,
		"Fsource_id":           req.FsourceId,
		"status":               1,
		"is_delete":            0,
	}
	var joinCount int64
	err = DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(inviteeCondition).Count(&joinCount).Error
	if err != nil {
		return rsp, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}

	rsp.CanJoinTeamNum = req.MaxJoinCount - int32(joinCount)
	return rsp, nil
}

// GetMyTeamAllowedJoinNum 获取可以加入队伍数
func (s *TeamServiceImpl) GetMyTeamAllowedJoinNum(ctx context.Context, req *pb.GetMyTeamAllowedJoinNumReq) (
	*pb.GetMyTeamAllowedJoinNumRsp, error) {
	rsp := &pb.GetMyTeamAllowedJoinNumRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return rsp, err
	}
	tableName, err := util.GetShareTableName(ctx, req.FsourceId)
	if err != nil {
		return rsp, err
	}
	teamCondition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
		"status":       1,
		"is_delete":    0,
	}
	var shareNum int64
	err = DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(teamCondition).Count(&shareNum).Error
	if err != nil {
		return rsp, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	rsp.AllowedJoinNum = req.MaxShareCount - int32(shareNum)
	return rsp, nil
}

// CreateTeamTGlog 创建队伍TGlog
func (s *TeamServiceImpl) CreateTeamTGlog(ctx context.Context, req *pb.CreateTeamTGlogReq) (
	rsp *pb.CreateTeamTGlogRsp, err error) {
	// fmt.Println(">>>>>>>>>>>>>>>CreateTeamTGlog<<<<<<<<<<<<<<<")
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	tableName := "HokTeamAct"
	userUidList := strings.Split(userAccount.Uid, "-")
	if len(userUidList) < 2 {
		return
	}
	// 判断删档账号 不含n为老用户
	var oldLeader, newLeader string

	if userUidList[0] == userAccount.IntlAccount.GameId {
		oldLeader = userUidList[1]
	} else {
		oldLeader = " "
	}
	newLeader = userAccount.IntlAccount.OpenId
	leader := userAccount.IntlAccount.OpenId
	// leader := "*****************"
	dtEventTime := time.Now().In(time.UTC).Format("2006-01-02 15:04:05")
	TeamId := leader
	TeamName := leader
	createTeam := fmt.Sprintf("%s|%s|%s|%s|%s|%s|%d|%s|%s", tableName, oldLeader, " ", dtEventTime, TeamId, TeamName, 1,
		newLeader, " ")
	if tglog.SelectTGlog("hokteamact") != nil {
		log.Infof("tglog value=%v", createTeam)
		tglog.SelectTGlog("hokteamact").LogStringAsync(createTeam)
	}
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: "pt",
			XGameId:   9,
			XSource:   "pc_web",
		},
		Action:         "hokteam_svr_team",
		SubAction:      "create_team",
		OriginalGameId: "29134",
		ExtentContent: map[string]interface{}{
			"leader": userAccount.IntlAccount.OpenId,
			"member": "",
		},
	}

	report.ReportTlog(ctx, tlogData)
	rsp = &pb.CreateTeamTGlogRsp{}
	return
}

// JoinTeamTGlog 加入队伍TGLog上报
func (s *TeamServiceImpl) JoinTeamTGlog(ctx context.Context, req *pb.JoinTeamTGlogReq) (
	rsp *pb.JoinTeamTGlogRsp, err error) {
	rsp = &pb.JoinTeamTGlogRsp{}
	if req.JoinSuccess == false {
		return
	}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	shareCodeIdStruct, codeErr := util.ParseCodeById(req.ShareCode)
	if codeErr != nil {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode, "[GetTeamList] self decode shareCode err",
			codeErr))
		return
	}

	recordService := &record.RecordImpl{}
	initRecordRsp, errR := recordService.GetRecordDataById(ctx, &recordPb.GetRecordDataByIdReq{
		FsourceId: shareCodeIdStruct.Si, RecordId: shareCodeIdStruct.Id})
	if errR != nil {
		return
	}
	reqUidList := strings.Split(initRecordRsp.Uid, "-")
	if len(reqUidList) < 2 {
		return
	}
	var oldLeader, newLeader, teamName, teamId string
	// 判断删档账号
	if reqUidList[0] == userAccount.IntlAccount.GameId {
		oldLeader = reqUidList[1]
		teamId = oldLeader
		teamName = oldLeader
	} else {
		newLeader = reqUidList[1]
		teamId = newLeader
		teamName = newLeader
	}
	var oldMember, newMember string
	userUidList := strings.Split(userAccount.Uid, "-")
	if len(userUidList) < 2 {
		return
	}
	// 判断删档账号 不含n为老用户
	if userUidList[0] == userAccount.IntlAccount.GameId {
		oldMember = userUidList[1]
	} else {
		oldMember = " "
	}
	newMember = userAccount.IntlAccount.OpenId

	tableName := "HokTeamAct"
	dtEventTime := time.Now().In(time.UTC).Format("2006-01-02 15:04:05")

	joinTeam := fmt.Sprintf("%s|%s|%s|%s|%s|%s|%d|%s|%s", tableName, oldLeader, oldMember, dtEventTime, teamId, teamName,
		2, newLeader, newMember)
	if tglog.SelectTGlog("hokteamact") != nil {
		log.Infof("tglog value=%v", joinTeam)
		tglog.SelectTGlog("hokteamact").LogStringAsync(joinTeam)
	}
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: "pt",
			XGameId:   9,
			XSource:   "pc_web",
		},
		Action:         "hokteam_svr_team",
		SubAction:      "join_team",
		OriginalGameId: "29134",
		ExtentContent: map[string]interface{}{
			"leader": reqUidList[1],
			"member": userAccount.IntlAccount.OpenId,
		},
	}
	report.ReportTlog(ctx, tlogData)
	return
}

// GeneralCreateTeamTGlog 通用创建队伍TGlog
func (s *TeamServiceImpl) GeneralCreateTeamTGlog(ctx context.Context, req *pb.GeneralCreateTeamTGlogReq) (
	rsp *pb.GeneralCreateTeamTGlogRsp, err error) {
	rsp = &pb.GeneralCreateTeamTGlogRsp{}
	// fmt.Println(">>>>>>>>>>>>>>>CreateTeamTGlog<<<<<<<<<<<<<<<")
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	leader := ""
	switch userAccount.AccountType {
	case accountPb.AccountType_INTL:
		leader = userAccount.IntlAccount.OpenId
		break
	case accountPb.AccountType_WeGameApp:
		leader = userAccount.WegameAppAccount.UserId
		break
	default:
		return
	}
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: "pt",
			XGameId:   int(req.XGameId),
			XSource:   "pc_web",
		},
		Action:         req.Action,
		SubAction:      req.SubAction,
		OriginalGameId: req.OriginalGameId,
		ExtentContent: map[string]interface{}{
			"leader": leader,
			"member": "",
		},
	}

	report.ReportTlog(ctx, tlogData)
	return
}

// GeneralJoinTeamTGlog 通用加入队伍TGLog上报
func (s *TeamServiceImpl) GeneralJoinTeamTGlog(ctx context.Context, req *pb.GeneralJoinTeamTGlogReq) (
	rsp *pb.GeneralJoinTeamTGlogRsp, err error) {
	rsp = &pb.GeneralJoinTeamTGlogRsp{}
	if req.JoinSuccess == false {
		return
	}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	shareCodeIdStruct, codeErr := util.ParseCodeById(req.ShareCode)
	if codeErr != nil {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode, "[GetTeamList] self decode shareCode err",
			codeErr))
		return
	}
	recordService := &record.RecordImpl{}
	initRecordRsp, errR := recordService.GetRecordDataById(ctx, &recordPb.GetRecordDataByIdReq{
		FsourceId: shareCodeIdStruct.Si, RecordId: shareCodeIdStruct.Id})
	if errR != nil {
		return
	}
	reqUidList := strings.Split(initRecordRsp.Uid, "-")
	if len(reqUidList) < 2 {
		return
	}

	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: "pt",
			XGameId:   int(req.XGameId),
			XSource:   "pc_web",
		},
		Action:         req.Action,
		SubAction:      req.SubAction,
		OriginalGameId: req.OriginalGameId,
		ExtentContent: map[string]interface{}{
			"leader": reqUidList[1],
			"member": userAccount.IntlAccount.OpenId,
		},
	}
	report.ReportTlog(ctx, tlogData)
	return
}

// GeneralShortCodeTGlog 通用短码记录邀请TGLog上报
func (s *TeamServiceImpl) GeneralShortCodeTGlog(ctx context.Context, req *pb.GeneralShortCodeTGlogReq) (
	rsp *pb.GeneralShortCodeTGlogRsp, err error) {
	// metadata.SetUserAccount(ctx, accountPb.UserAccount{
	// 	Uid:         "29080-212554473218401583801",
	// 	AccountType: accountPb.AccountType_INTL,
	// 	IntlAccount: &accountPb.IntlAccount{
	// 		OpenId:    "16657983015453317121",
	// 		GameId:    "29080",
	// 		ChannelId: 6,
	// 		Token:     "f7e7475e8f3add68a447b5d4171888f481cac1fb",
	// 	},
	// })
	rsp = &pb.GeneralShortCodeTGlogRsp{}
	if req.JoinSuccess == false {
		return
	}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	shortTableName, err := util.GetShortShareTableName(ctx, req.FsourceId)

	var shortCode invitation.ShortCode
	selectdb := DB.DefaultConnect().WithContext(ctx).Table(shortTableName).Where("Fsource_id = ? and share_code = ?",
		req.FsourceId, req.ShareCode).First(&shortCode)
	if errors.Is(selectdb.Error, gorm.ErrRecordNotFound) {
		log.WithContext(ctx).Error(errs.NewCustomError(ctx, code.ErrorShareCode,
			"[GeneralShortCodeTGlog] self decode shareCode not found"))
		return
	}
	if selectdb.Error != nil {
		log.WithContext(ctx).Error(errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err))
		return
	}
	member := ""
	switch userAccount.AccountType {
	case accountPb.AccountType_INTL:
		member = userAccount.IntlAccount.OpenId
		break
	case accountPb.AccountType_WeGameApp:
		member = userAccount.WegameAppAccount.UserId
		break
	default:
		return
	}
	tlogData := report.ReportTlogData{
		Header: report.ReportTlogHeader{
			XLanguage: "pt",
			XGameId:   int(req.XGameId),
			XSource:   "pc_web",
		},
		Action:         req.Action,
		SubAction:      req.SubAction,
		OriginalGameId: req.OriginalGameId,
		ExtentContent: map[string]interface{}{
			"leader": shortCode.UID,
			"member": member,
		},
	}
	report.ReportTlog(ctx, tlogData)
	return
}
