package tweet

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	pbUser "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	es7 "github.com/olivere/elastic/v7"
	"github.com/thoas/go-funk"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

// CMS 设置评论位置信息（置顶或置底）
func CMSSetCommentPositionInfo(ctx context.Context,
	req *pb.CMSSetCommentPositionInfoReq) (*pb.CMSSetCommentPositionInfoRsp, error) {
	// 参数检查
	if req.GetCommentUuid() == "" {
		return nil, errs.NewCustomError(ctx, code.InvalidParams, "comment uuid empty")
	}
	supports := []int{
		constants.CommentPosStatusTop,
		constants.CommentPosStatusBottom,
		constants.CommentPosStatusGeneral,
	}
	if !funk.InInts(supports, int(req.GetPosStatus())) {
		return nil, errs.NewCustomError(ctx, code.InvalidParams, "pos status invalid")
	}
	posStatus := int(req.GetPosStatus())
	if posStatus == constants.CommentPosStatusGeneral {
		// 转为取消状态
		posStatus = constants.CommentPosStatusDefault
		req.PosStatus = int32(posStatus)
	}
	err := HandleSetCommentTopOrButtomData(ctx, req.GetCommentUuid(), constants.ETopBottomAction(posStatus))
	// now := time.Now()
	// posStatus := int(req.GetPosStatus())
	// posSetTime := now.Unix()
	// if posStatus == constants.CommentPosStatusGeneral {
	// 	// 转为取消状态
	// 	posStatus = constants.CommentPosStatusDefault
	// 	req.PosStatus = int32(posStatus)
	// 	posSetTime = 0
	// }

	// docID := req.GetCommentUuid()
	// index := config.GetConfig().ElasticSearchSetting.TweetCommentIndex
	// comment, err := QueryCommentByDocId(ctx, index, docID)
	// if err != nil {
	// 	log.ErrorContextf(ctx, "QueryCommentByDocId error:%v, index:%v, docID:%v", index, docID)
	// 	return nil, errs.NewCustomError(ctx, code.DefaultErr, "QueryCommentByDocId error")
	// }
	// log.InfoContextf(ctx, "comment detail:%v", util.ToJson(comment))

	// // 只有正常状态(已发布)的评论可以进行【置底或置顶或取消操作】
	// if comment.Status != constants.CommentStatusPublished {
	// 	log.ErrorContextf(ctx, "comment status must be normal(value is 2), index:%v, docID:%v, isDel:%v, status:%v",
	// 		index, docID, comment.IsDel, comment.Status)
	// 	return nil, errs.NewCustomError(ctx, code.ErrCodeCommentStatusMustBeNormal, "comment status must be normal")
	// }
	// // 查看评论是否存在
	// if comment.IsDel != constants.CommentDelStatusNotDeleted {
	// 	log.ErrorContextf(ctx, "comment is deleted, index:%v, docID:%v, isDel:%v",
	// 		index, docID, comment.IsDel)
	// 	return nil, errs.NewCustomError(ctx, code.ErrCodeCommentNotExist, "comment is deleted")
	// }
	// // 评论类型才可以【置底或置顶或取消操作】
	// if comment.Type != int32(constants.DYNAMIC_COMMENT) {
	// 	log.ErrorContextf(ctx, "comment type must comment but no reply, index:%v, docID:%v, isDel:%v, type:%v",
	// 		index, docID, comment.IsDel, comment.Type)
	// 	return nil, errs.NewCustomError(ctx, code.ErrCodeCommentTypeNotComment, "comment type must comment but no reply")
	// }

	// // 【置底或置顶或取消操作】
	// updateData := map[string]interface{}{
	// 	"pos_status":   posStatus,
	// 	"pos_set_time": posSetTime,
	// }
	// doc := &Document{
	// 	Index:   index,
	// 	DocId:   docID,
	// 	DocData: updateData,
	// }
	// // 更新 es 数据
	// err = UpdateSingleEsDoc(ctx, doc)
	// if err != nil {
	// 	log.ErrorContextf(ctx, "UpdateSingleEsDoc error:%v, index:%v, docID:%v, data:%+v",
	// 		index, docID, doc.DocData)
	// 	return nil, errs.NewCustomError(ctx, code.DefaultErr, "UpdateSingleEsDoc error")
	// }
	// // 更新 mysql 数据
	// err = dao.UpdateComment(ctx, req.GetCommentUuid(), updateData)
	// if err != nil {
	// 	log.ErrorContextf(ctx, "UpdateComment mysql error:%v, index:%v, docID:%v, data:%+v",
	// 		index, docID, doc.DocData)
	// 	return nil, errs.NewCustomError(ctx, code.DefaultErr, "UpdateComment mysql error")
	// }
	rsp := &pb.CMSSetCommentPositionInfoRsp{}
	rsp.TraceId = metadata.GetCtxTraceID(ctx)
	return rsp, err
}

func HandleSetCommentTopOrButtomData(ctx context.Context, commentUid string, actionType constants.ETopBottomAction) error {
	// 参数检查
	if commentUid == "" {
		return errs.NewCustomError(ctx, code.InvalidParams, "comment uuid empty")
	}
	now := time.Now()
	posSetTime := now.Unix()
	supports := []int{
		int(constants.ETopBottomAction_CMSSetBottom),
		int(constants.ETopBottomAction_CMSSetTop),
		int(constants.ETopBottomAction_CancelTopButtom),
		int(constants.ETopBottomAction_ModeratorSetBottom),
		int(constants.ETopBottomAction_ModeratorSetTop),
		int(constants.ETopBottomAction_PosterSetBottom),
		int(constants.ETopBottomAction_PosterSetTop),
	}
	if !funk.InInts(supports, int(actionType)) {
		return errs.NewCustomError(ctx, code.InvalidParams, "pos status invalid")
	}
	if actionType == constants.ETopBottomAction_CancelTopButtom {
		posSetTime = 0
	}

	posStatus := int(actionType)

	docID := commentUid
	index := config.GetConfig().ElasticSearchSetting.TweetCommentIndex
	comment, err := QueryCommentByDocId(ctx, index, docID)
	if err != nil {
		log.ErrorContextf(ctx, "QueryCommentByDocId error:%v, index:%v, docID:%v", index, docID)
		return errs.NewCustomError(ctx, code.DefaultErr, "QueryCommentByDocId error")
	}
	log.InfoContextf(ctx, "comment detail:%v", util.ToJson(comment))
	// 沙盒消息也能置顶置底
	// // 只有正常状态(已发布)的评论可以进行【置底或置顶或取消操作】
	// if comment.Status != constants.CommentStatusPublished {
	// 	log.ErrorContextf(ctx, "comment status must be normal(value is 2), index:%v, docID:%v, isDel:%v, status:%v",
	// 		index, docID, comment.IsDel, comment.Status)
	// 	return errs.NewCustomError(ctx, code.ErrCodeCommentStatusMustBeNormal, "comment status must be normal")
	// }
	// 查看评论是否存在
	if comment.IsDel != constants.CommentDelStatusNotDeleted {
		log.ErrorContextf(ctx, "comment is deleted, index:%v, docID:%v, isDel:%v",
			index, docID, comment.IsDel)
		return errs.NewCustomError(ctx, code.ErrCodeCommentNotExist, "comment is deleted")
	}
	// 评论类型才可以【置底或置顶或取消操作】
	if comment.Type != int32(constants.DYNAMIC_COMMENT) {
		log.ErrorContextf(ctx, "comment type must comment but no reply, index:%v, docID:%v, isDel:%v, type:%v",
			index, docID, comment.IsDel, comment.Type)
		return errs.NewCustomError(ctx, code.ErrCodeCommentTypeNotComment, "comment type must comment but no reply")
	}

	// 【置底或置顶或取消操作】
	updateData := map[string]interface{}{
		"pos_status":   posStatus,
		"pos_set_time": posSetTime,
	}
	doc := &Document{
		Index:   index,
		DocId:   docID,
		DocData: updateData,
	}
	// 更新 es 数据
	err = UpdateSingleEsDoc(ctx, doc)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateSingleEsDoc error:%v, index:%v, docID:%v, data:%+v",
			index, docID, doc.DocData)
		return errs.NewCustomError(ctx, code.DefaultErr, "UpdateSingleEsDoc error")
	}
	// 更新 mysql 数据
	err = dao.UpdateComment(ctx, docID, updateData)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateComment mysql error:%v, index:%v, docID:%v, data:%+v",
			index, docID, doc.DocData)
		return errs.NewCustomError(ctx, code.DefaultErr, "UpdateComment mysql error")
	}
	// rsp.TraceId = metadata.GetCtxTraceID(ctx)
	return nil
}

// CMS 查询评论列表信息
func CMSGetPostComments(ctx context.Context, req *pb.CMSGetPostCommentsReq) (*pb.CMSGetPostCommentsRsp, error) {
	// 参数检查
	if req.GetLimit() <= 0 {
		return nil, errs.NewCustomError(ctx, code.InvalidParams, "limit invalid")
	}
	originLimit := req.GetLimit()
	req.Limit = req.GetLimit() + 1
	index := config.GetConfig().ElasticSearchSetting.TweetCommentIndex
	// 构造查询源
	searchSource, err := genCMSGetPostCommentsQuery(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "genCMSGetPostCommentsQuery error:%v", err)
		return nil, errs.NewCustomError(ctx, code.DefaultErr, "UpdateSingleEsDoc error")
	}
	// 查询评论列表信息
	comments, err := QueryComments(ctx, []string{index}, searchSource)
	if err != nil {
		log.ErrorContextf(ctx, "QueryComments error:%v")
		return nil, errs.NewCustomError(ctx, code.DefaultErr, "QueryComments error")
	}
	var intlOpenIds, postUUIDs []string
	for _, comment := range comments {
		intlOpenIds = append(intlOpenIds, comment.IntlOpenid)
		postUUIDs = append(postUUIDs, comment.PostUuid)
	}
	// 查询用户关联信息
	userInfos, err := GetUserInfoParallel(ctx, intlOpenIds)
	if err != nil {
		log.ErrorContextf(ctx, "GetUserInfoParallel erro:%v, intlOpenIds:%v", err, intlOpenIds)
		return nil, errs.NewCustomError(ctx, code.DefaultErr, "GetUserInfoParallel error")
	}
	// 查询帖子关联信息
	postInfos, err := GetPostInfos(ctx, postUUIDs)
	if err != nil {
		log.ErrorContextf(ctx, "GetPostInfos erro:%v, postUUIDs:%v", err, postUUIDs)
		return nil, errs.NewCustomError(ctx, code.DefaultErr, "GetPostInfos error")
	}

	rsp := &pb.CMSGetPostCommentsRsp{}
	commentOriginLen := len(comments)
	if len(comments) > int(originLimit) {
		// limit先做了+1操作，获取记录数最大为 参数传的originLimit条记录
		comments = comments[:originLimit]
	}
	for _, comment := range comments {
		intlOpenId, postUUID := comment.IntlOpenid, comment.PostUuid
		userInfo, ok := userInfos[intlOpenId]
		if !ok {
			log.ErrorContextf(ctx, "userInfo not exist intlOpenId:%v", intlOpenId)
			continue
		}
		postInfo, ok := postInfos[postUUID]
		if !ok {
			log.ErrorContextf(ctx, "postInfo not exist postUUID:%v", postUUID)
			continue
		}
		// 置顶置底多种状态： 1:(CMS 操作)置顶, 2:(CMS操作)置底，4:(版主操作)置顶，5：(版主操作)取消置顶；6: 贴主置顶  7：贴主取消置顶;0,3 未置顶置底
		posStatus := comment.PosStatus
		if posStatus == int32(constants.ETopBottomAction_CMSSetTop) || posStatus == int32(constants.ETopBottomAction_ModeratorSetTop) || posStatus == int32(constants.ETopBottomAction_PosterSetTop) {
			posStatus = 1
		} else if posStatus == int32(constants.ETopBottomAction_CMSSetBottom) || posStatus == int32(constants.ETopBottomAction_ModeratorSetBottom) || posStatus == int32(constants.ETopBottomAction_PosterSetBottom) {
			posStatus = 2
		} else {
			posStatus = 0
		}
		rsp.Commments = append(rsp.Commments, &pb.GetPostCommentAuditForCMSRsp{
			CommentItem: &pb.UserCommentItem{
				CommentUuid: comment.CommentUuid,
				PostUuid:    comment.PostUuid,
				IntlOpenid:  comment.IntlOpenid,
				Type:        comment.Type,
				Title:       comment.Title,
				Content:     comment.Content,
				PicUrls:     strings.Split(comment.PicUrls, ","),
				CreatedOn:   comment.CreatedOn,
				ModifiedOn:  comment.ModifiedOn,
				GameId:      comment.GameId,
				AreaId:      comment.AreaId,
				IsDel:       comment.IsDel,
				Language:    comment.Language,
				DelReason:   comment.DelReason,
				DelType:     comment.DelType,
				User:        userInfo,
				PostDel:     int32(postInfo.IsDel),
				PosStatus:   posStatus,
				PosSetTime:  comment.PosSetTime,
			},
			AuditInfo: &pb.PostAuditInfo{
				TextRiskLevel:    comment.TextRiskLevel,
				TextRiskType:     comment.TextRiskType,
				PicRiskLevel:     comment.PicRiskLevel,
				PicRiskType:      comment.PicRiskType,
				Status:           comment.Status,
				AuditUser:        comment.AuditUser,
				AuditIntroduce:   comment.AuditIntroduce,
				AuditOn:          comment.AuditOn,
				MachineStatus:    comment.MachineStatus,
				ArtificialStatus: comment.ArtificialStatus,
			},
		})
	}
	isFinished := false
	if commentOriginLen <= int(originLimit) {
		isFinished = true
	}
	nextPageCursor, err := getNextPageCursor(ctx, comments)
	if err != nil {
		log.ErrorContextf(ctx, "getNextPageCursor error:%v", err)
		return nil, err
	}
	pageInfo := &pb.PageInfo{
		NextPageCursor: nextPageCursor,
		IsFinish:       isFinished,
	}
	rsp.PageInfo = pageInfo
	rsp.TraceId = metadata.GetCtxTraceID(ctx)
	return rsp, nil
}

func getNextPageCursor(ctx context.Context, comments []*model.ESComment) (string, error) {
	if len(comments) == 0 {
		return "", nil
	}
	hit := comments[len(comments)-1]
	jsonData, err := JSON.Marshal(hit.Sort)
	if err != nil {
		log.ErrorContextf(ctx, "getNextPageCursor JSON.Marshal error:%v, hit sort:%v",
			err, util.ToJson(hit.Sort))
		return "", err
	}
	nextPageCursor, err := util.EncryptPageCursorS(string(jsonData))
	if err != nil {
		return "", errs.NewCustomError(ctx, code.GetIndexPostFailed,
			"GetIndexPosts | Failed to postsFrom EncryptPageCursorS")
	}
	return nextPageCursor, nil
}

func genCMSGetPostCommentsQuery(ctx context.Context, req *pb.CMSGetPostCommentsReq) (*es7.SearchSource, error) {
	query := es7.NewBoolQuery()
	var filters, shoulds []es7.Query
	// filter
	if req.GetIntlUserOpenid() != "" {
		filters = append(filters, es7.NewTermQuery("intl_user_openid", req.GetIntlUserOpenid()))
	}
	if req.GetCommentUuid() != "" {
		filters = append(filters, es7.NewTermQuery("comment_uuid", req.GetCommentUuid()))
	}
	if req.GetPostUuid() != "" {
		filters = append(filters, es7.NewTermQuery("post_uuid", req.GetPostUuid()))
	}
	if req.GetStartTime() > 0 {
		filters = append(filters, es7.NewRangeQuery("created_on").Gte(req.GetStartTime()))
	}
	if req.GetEndTime() > 0 {
		filters = append(filters, es7.NewRangeQuery("created_on").Lte(req.GetEndTime()))
	}
	if req.GetCommentType() > 0 {
		filters = append(filters, es7.NewTermQuery("type", req.GetCommentType()))
	}
	if req.GetPosStatus() > 0 {
		filters = append(filters, es7.NewTermQuery("pos_status", req.GetPosStatus()))
	}
	// 内容状态查询
	contentStatusQuery := genContentStatusQuery(req.GetContentStatus())
	if contentStatusQuery != nil {
		filters = append(filters, contentStatusQuery)
	}
	if funk.InInt32s([]int32{0, 1}, req.GetIsDel()) {
		filters = append(filters, es7.NewTermQuery("is_del", req.GetIsDel()))
	}
	// 筛选语言
	if req.GetLanguage() != "" {
		filters = append(filters, es7.NewTermQuery("language", req.GetLanguage()))
	}

	// should
	if req.GetKeyword() != "" {
		slop, boost := 3, float64(1) // 调优参数
		shoulds = append(shoulds, es7.NewMultiMatchQuery(req.GetKeyword(), "content"))
		shoulds = append(shoulds, es7.NewMatchPhraseQuery("content", req.GetKeyword()).Slop(slop).Boost(boost))
		shoulds = append(shoulds, es7.NewWildcardQuery("content", "*"+req.GetKeyword()+"*"))
	}

	// search after下一页
	var lastSortValue []interface{}
	if req.GetNextPageCursor() != "" {
		cursorStr, err := util.DecryptPageCursorS(req.GetNextPageCursor())
		if err != nil {
			return nil, errs.NewCustomError(ctx, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
		}
		err = JSON.Unmarshal([]byte(cursorStr), &lastSortValue)
		if err != nil {
			return nil, errs.NewCustomError(ctx, code.GetIndexPostFailed, "queryByContent2 | Failed to get idCursor")
		}
	}
	// 构造查询源 source
	source := es7.NewSearchSource()
	if len(filters) > 0 {
		query = query.Filter(filters...)
	}
	if len(shoulds) > 0 {
		query = query.Should(shoulds...)
		// 存在 should 语句，才可以配置minimum_should_match
		// 不存在 should 语句，配置了 minimum_should_match 会导致查询结果为空
		query = query.MinimumNumberShouldMatch(1)
	}
	source = source.Query(query)
	if req.GetLimit() > 0 {
		source = source.Size(int(req.GetLimit()))
	}
	// 排序
	if len(genCommentQuerySorters()) > 0 {
		source = source.SortBy(genCommentQuerySorters()...)
	}
	// 分页
	if len(lastSortValue) > 0 {
		source = source.SearchAfter(lastSortValue...)
	}
	return source, nil
}

func genCommentQuerySorters() []es7.Sorter {
	var defaultSorters []es7.Sorter
	defaultSorters = append(defaultSorters, es7.NewFieldSort("_score").Desc())
	defaultSorters = append(defaultSorters, es7.NewFieldSort("created_on_ms").Desc())
	defaultSorters = append(defaultSorters, es7.NewFieldSort("_id").Desc())
	return defaultSorters
}

func genContentStatusQuery(contentStatus int32) *es7.TermQuery {
	var itemQuery *es7.TermQuery
	switch constants.PostContentStatus(contentStatus) {
	case constants.NormalPost:
		itemQuery = es7.NewTermQuery("status", 2)
	case constants.SandboxPost:
		itemQuery = es7.NewTermQuery("status", 1)
	case constants.UserDeletedPost:
		itemQuery = es7.NewTermQuery("del_type", constants.UserDeleted)
	case constants.CAdminDeletedPost:
		itemQuery = es7.NewTermQuery("del_type", constants.CAdminDeleted)
	case constants.BReviewDeletedPost:
		itemQuery = es7.NewTermQuery("del_type", constants.BReviewDeleted)
	case constants.BAdminDeletedPost:
		itemQuery = es7.NewTermQuery("del_type", constants.BAdminDeleted)
	case constants.BReportDeletedPost:
		itemQuery = es7.NewTermQuery("del_type", constants.ReportBAdminDeleted)
	}

	return itemQuery
}

// GetUserInfoParallel 查询用户相关信息
func GetUserInfoParallel(ctx context.Context, intlOpenIds []string) (map[string]*pbUser.UserInfo, error) {
	intlOpenIds = funk.UniqString(intlOpenIds)
	if len(intlOpenIds) == 0 {
		return nil, nil
	}

	datas := make([]*pbUser.UserInfo, len(intlOpenIds))
	tasks := make([]func() error, 0)

	for i, id := range intlOpenIds {
		task := func(i int, intlOpenId string) func() error {
			return func() error {
				userInfo, err := dao.GetUserByIntlOpenid(intlOpenId)
				if err != nil {
					log.WithFieldsContext(ctx, "log type", constants.LogType_Standalonesite).
						Errorf("dao.GetUserByIntlOpenid err, i:%v, intl_openid:(%s), err=(%v)", i, intlOpenId, err)
					// 未报错
					return nil
				}
				// 获取用户是否禁言
				isMute := formatted.GetUserMute(intlOpenId)
				datas[i] = &pbUser.UserInfo{
					Username:   userInfo.Username,
					IntlOpenid: userInfo.IntlOpenid,
					Avatar:     userInfo.Avatar,
					Mood:       userInfo.Mood,
					IsMute:     isMute,
				}
				return nil
			}
		}
		tasks = append(tasks, task(i, id))
	}
	err := util.Parallel(20, tasks)
	if err != nil {
		log.ErrorContextf(ctx, "GetUserInfoParallel util.Parallel error:%v", err)
		return nil, err
	}
	result := make(map[string]*pbUser.UserInfo)
	for _, data := range datas {
		result[data.IntlOpenid] = data
	}
	return result, nil
}

// GetPostInfoParallel 查询帖子相关信息
func GetPostInfoParallel(ctx context.Context, postUuids []string) (map[string]*model.Post, error) {
	postUuids = funk.UniqString(postUuids)
	if len(postUuids) == 0 {
		return nil, nil
	}

	datas := make([]*model.Post, len(postUuids))
	tasks := make([]func() error, 0)

	for i, id := range postUuids {
		task := func(i int, postUuid string) func() error {
			return func() error {
				// 查询动态
				postInfo, err := dao.GetPostNoIgnoreDel(postUuid)
				if err != nil {
					log.WithFieldsContext(ctx, "log type", constants.LogType_Standalonesite).
						Errorf("GetPostNoIgnoreDel err, i:%v, postuuid:(%s), err=(%v)", i, postUuid, err)
				}
				datas[i] = postInfo
				return nil
			}
		}
		tasks = append(tasks, task(i, id))
	}
	err := util.Parallel(20, tasks)
	if err != nil {
		log.ErrorContextf(ctx, "GetPostInfoParallel util.Parallel error:%v", err)
		return nil, err
	}
	result := make(map[string]*model.Post)
	for _, data := range datas {
		result[data.PostUUID] = data
	}
	return result, nil
}

// GetPostInfos 查询帖子相关信息
func GetPostInfos(ctx context.Context, postUuids []string) (map[string]*model.Post, error) {
	// 查询帖子信息列表
	postInfos, err := dao.GetPostNoIgnoreDels(postUuids)
	if err != nil {
		log.WithFieldsContext(ctx, "log type", constants.LogType_Standalonesite).
			Errorf("GetPostNoIgnoreDels err, postUuid:(%s), err=(%v)", postUuids, err)
		return nil, err
	}

	result := make(map[string]*model.Post)
	for _, data := range postInfos {
		result[data.PostUUID] = data
	}
	return result, nil
}
