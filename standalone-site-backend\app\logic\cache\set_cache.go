package cache

import (
	"context"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"trpc.publishing_application.standalonesite/app/code"
)

func SetCacheStringBool(ctx context.Context, key string, value bool, timeout time.Duration) error {
	_, err := redis.GetClient().SetEX(ctx, key, value, timeout).Result()
	if err != nil {
		return err
	}
	return nil
}

// AcquireLock 获取分布式锁
func AcquireLock(ctx context.Context, lockKey string, expiration time.Duration) (bool, error) {
	// 尝试获取锁
	result, err := redis.GetClient().SetNX(ctx, lockKey, "1", expiration).Result()
	if err != nil {
		return false, errs.NewCustomError(ctx, code.RedisLockErr,
			"AcquireLock error, lockKey:(%s),err=%v", lockKey, err)
	}
	return result, nil
}

func ReleaseLock(ctx context.Context, lockKey string) error {
	// 释放锁
	_, err := redis.GetClient().Del(ctx, lockKey).Result()
	if err != nil {
		return errs.NewCustomError(ctx, code.RedisUnlockErr,
			"ReleaseLock error, lockKey:(%s),err=%v", lockKey, err)
	}
	return nil
}

// 字符串命令的写入操作
func StringSet(ctx context.Context, key string, value string) error {
	_, err := redis.GetClient().Set(ctx, key, value, 0).Result()
	if err != nil {
		return err
	}
	return nil
}

// 集合
func SetCacheAggregate(ctx context.Context, key string, value string) error {
	_, err := redis.GetClient().SAdd(ctx, key, value).Result()
	if err != nil {
		return err
	}
	return nil
}
