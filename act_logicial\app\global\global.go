// Package global 全局配置
package global

import (
	"fmt"
	"reflect"

	"git.code.oa.com/trpc-go/trpc-go"
)

// GetRedisKey 获取
func GetRedisKey(key string) (redisKey string) {
	globalConfig := trpc.GlobalConfig()
	prefix := fmt.Sprintf("%v_%v_%v", globalConfig.Server.App, globalConfig.Server.Server, globalConfig.Global.EnvName)
	redisKey = fmt.Sprintf("%v_%v", prefix, key)
	return
}

// GetPrefix 前缀
func GetPrefix() (prefix string) {
	globalConfig := trpc.GlobalConfig()
	prefix = fmt.Sprintf("%v_%v", globalConfig.Server.App, globalConfig.Server.Server)
	return
}

// InArray 通用判断是否在数组接口
func InArray(need interface{}, haystack interface{}) (exists bool, index int) {
	index = -1
	exists = false
	switch need.(type) {
	case int:
		for i, item := range haystack.([]int) {
			if item == need {
				index = i
				exists = true
				return
			}
		}
	case string:
		for i, item := range haystack.([]string) {
			if item == need {
				index = i
				exists = true
				return
			}
		}
	case int64:
		for i, item := range haystack.([]int64) {
			if item == need {
				index = i
				exists = true
				return
			}
		}
	case float64:
		for i, item := range haystack.([]float64) {
			if item == need {
				index = i
				exists = true
				return
			}
		}
	default:
		switch reflect.TypeOf(haystack).Kind() {
		case reflect.Slice:
			s := reflect.ValueOf(haystack)

			for i := 0; i < s.Len(); i++ {
				if reflect.DeepEqual(need, s.Index(i).Interface()) == true {
					index = i
					exists = true
					return
				}
			}
		}

		return
	}
	return
}
