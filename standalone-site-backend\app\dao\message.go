package dao

import (
	"fmt"
	"time"
	"trpc.publishing_application.standalonesite/app/constants"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

type MessageConditions struct {
	ReceiverUserIntlOpenid string
	IsRead                 string
	Type                   []constants.MessageT
	LtId                   int64
	Order                  []*OrderConditions
}

func MessageCreate(messageData *model.Message) error {
	tableName, err := (&model.Message{}).GetTableName(messageData.ReceiverUserIntlOpenid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Create(&messageData).Error
	return err
}

func MessageUpdate(messageData *model.Message) error {
	tableName, err := (&model.Message{}).GetTableName(messageData.ReceiverUserIntlOpenid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Model(&model.Message{}).Where("id = ? AND is_del = ?", messageData.Model.ID, 0).Save(messageData).Error
	return err
}

func MessageUpdateIn(messageData *model.Message, ids []int64) error {
	tableName, err := (&model.Message{}).GetTableName(messageData.ReceiverUserIntlOpenid)
	// 没有表名的情况下退出写入分表
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Model(&model.Message{}).Where("id in ? AND is_del = ?", ids, 0).Update("is_read", 1).Error
	return err
}

func MessageGet(id int64, receiverUserIntlOpenid string) (*model.Message, error) {
	var message model.Message
	tableName, err := (&model.Message{}).GetTableName(receiverUserIntlOpenid)
	if err != nil {
		return &message, err
	}
	db := DB.SelectConnect("db_standalonesite").Table(tableName)
	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	err = db.First(&message).Error
	if err != nil {
		return &message, err
	}

	return &message, nil
}

func MessageList(conditions *MessageConditions, offset, limit int, receiverUserIntlOpenid string) ([]*model.Message, error) {
	var messages []*model.Message
	var err error
	tableName, err := (&model.Message{}).GetTableName(receiverUserIntlOpenid)
	if err != nil {
		return messages, err
	}
	db := DB.SelectConnect("db_standalonesite").Table(tableName)
	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	if len(conditions.Type) > 0 {
		db = db.Where("type in ?", conditions.Type)
	}
	if conditions.IsRead != "" {
		db = db.Where("is_read = ?", conditions.IsRead)
	}
	if conditions.ReceiverUserIntlOpenid != "" {
		db = db.Where("receiver_user_intl_openid = ?", conditions.ReceiverUserIntlOpenid)
	}

	if err = db.Where("is_del = ?", 0).Find(&messages).Error; err != nil {
		return nil, err
	}

	return messages, nil
}

func MessageListNew(conditions *MessageConditions, limit int, receiverUserIntlOpenid string) ([]*model.Message, error) {

	var messages []*model.Message
	var err error
	tableName, err := (&model.Message{}).GetTableName(receiverUserIntlOpenid)
	if err != nil {
		return messages, err
	}
	db := DB.SelectConnect("db_standalonesite").Table(tableName)
	if limit > 0 {
		db = db.Limit(limit)
	}
	if conditions.IsRead != "" {
		db = db.Where("is_read = ?", conditions.IsRead)
	}
	if conditions.ReceiverUserIntlOpenid != "" {
		db = db.Where("receiver_user_intl_openid = ?", conditions.ReceiverUserIntlOpenid)
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if len(conditions.Type) > 0 {
		db = db.Where("type in ?", conditions.Type)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}

	if err = db.Where("is_del = ?", 0).Find(&messages).Error; err != nil {
		return nil, err
	}

	return messages, nil
}

func GetMessageCountAndID(conditions *MessageConditions, receiverUserIntlOpenid string) (int64, []int64, error) {

	var count int64
	var ids []int64
	tableName, err := (&model.Message{}).GetTableName(receiverUserIntlOpenid)
	if err != nil {
		return count, ids, err
	}
	db := DB.SelectConnect("db_standalonesite").Table(tableName)
	if conditions.IsRead != "" {
		db = db.Where("is_read = ?", conditions.IsRead)
	}
	if conditions.ReceiverUserIntlOpenid != "" {
		db = db.Where("receiver_user_intl_openid = ?", conditions.ReceiverUserIntlOpenid)
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if len(conditions.Type) > 0 {
		db = db.Where("type in ?", conditions.Type)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if cErr := db.Count(&count).Error; cErr != nil {
		return 0, ids, cErr
	}
	if err := db.Pluck("id", &ids).Error; err != nil {
		return 0, ids, err
	}

	return count, ids, nil
}

func MessageCountV2(receiverUserIntlOpenid string) (*model.NikkeMessageTabCount, error) {
	var msgCount = &model.NikkeMessageTabCount{
		Comment:     0,
		Follow:      0,
		Like:        0,
		SiteMessage: 0,
	}
	tableName, err := (&model.Message{}).GetTableName(receiverUserIntlOpenid)
	if err != nil {
		return msgCount, err
	}
	// 获取评论的消息未读数
	var commentType = []int32{int32(constants.MsgTypeComment), int32(constants.MsgTypeReply)}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("`type` in ?", commentType).Where("is_read = 0 and receiver_user_intl_openid = ?", receiverUserIntlOpenid).Count(&msgCount.Comment).Error
	if err != nil {
		// todo 记录错误
		msgCount.Comment = 0
	}
	// 获取关注的消息未读数
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("`type` = ?", constants.MsgTypeFollow).Where("is_read = 0 and receiver_user_intl_openid = ?", receiverUserIntlOpenid).Count(&msgCount.Follow).Error
	if err != nil {
		// todo 记录错误
		msgCount.Follow = 0
	}
	// 获取点赞的消息未读数
	var likeType = []int32{int32(constants.MsgTypeCommentStar), int32(constants.MsgTypeCommentReplyStar), int32(constants.MsgTypeStar)}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("`type` in ?", likeType).Where("is_read = 0 and receiver_user_intl_openid = ?", receiverUserIntlOpenid).Count(&msgCount.Like).Error
	if err != nil {
		// todo 记录错误
		msgCount.Like = 0
	}
	// 获取站内信的消息未读数
	var siteType = []int32{
		int32(constants.MsgTypeSiteMessage),
		int32(constants.MsgTypeOfficialIgnorePost),
		int32(constants.MsgTypeOfficialDeleteComment),
		int32(constants.MsgTypeReportMessage),
		int32(constants.MsgTypeOfficialDeletePost),
		int32(constants.MsgTypeOfficialIgnoreUserName),
		int32(constants.MsgTypeOfficialDeleteUserName),
		int32(constants.MsgTypeOfficialIgnoreUserRemark),
		int32(constants.MsgTypeOfficialDeleteUserRemark),
		int32(constants.MsgTypeOfficialIgnoreUserName),
		int32(constants.MsgTypeOfficialAccountMute),
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("`type` in ?", siteType).Where("is_read = 0 and receiver_user_intl_openid = ?", receiverUserIntlOpenid).Count(&msgCount.SiteMessage).Error
	if err != nil {
		// todo 记录错误
		msgCount.SiteMessage = 0
	}
	return msgCount, err
}

// MessageDeleteByPostIdNew 根据postId删除消息
func MessageDeleteByPostIdNew(postUUID string, msgTypes []int64) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		for i := 0; i < 100; i++ {
			err := tx.Table(fmt.Sprintf("%s_%d", (&model.Message{}).TableName(), i)).Where("type in ?", msgTypes).Where("post_uuid = ?", postUUID).Updates(map[string]interface{}{
				"deleted_on": time.Now().Unix(),
				"is_del":     1,
			}).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func MessageDeleteByFeedIDs(feedIDs []string, msgTypes []int64) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		for i := 0; i < 100; i++ {
			err := tx.Table(fmt.Sprintf("%s_%d", (&model.Message{}).TableName(), i)).Where("type in ?", msgTypes).Where("community_post_id in ?", feedIDs).Updates(map[string]interface{}{
				"deleted_on": time.Now().Unix(),
				"is_del":     1,
			}).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func MessageDeleteByCommentIdNew(commentId int64) (int64, error) {
	var affectedCount int64
	err := DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		for i := 0; i < 100; i++ {
			res := tx.Table(fmt.Sprintf("%s_%d", (&model.Message{}).TableName(), i)).Where("community_comment_id = ?", commentId).Updates(map[string]interface{}{
				"deleted_on": time.Now().Unix(),
				"is_del":     1,
			})
			if res.Error != nil {
				return res.Error
			}
			affectedCount += res.RowsAffected
		}
		return nil
	})

	return affectedCount, err
}

func MessageUpdateMessageMoveInfo(postID, commentID int64, communityCommentID string) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		for i := 0; i < 1000; i++ {
			err := tx.Table(fmt.Sprintf("%s_%d", (&model.Message{}).TableName(), i)).Where("post_uuid = ? AND comment_id = ?", postID, commentID).Updates(map[string]interface{}{
				"community_post_id":    fmt.Sprintf("%d", postID),
				"community_comment_id": communityCommentID,
			}).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func BatchSaveMessage(data []*model.Message) error {
	// 分表新增
	var batchMap = make(map[string][]*model.Message)
	for _, item := range data {
		name, tErr := (&model.Message{}).GetTableName(item.ReceiverUserIntlOpenid)
		if tErr != nil {
			continue
		}
		batchMap[name] = append(batchMap[name], item)
	}
	for tn, messages := range batchMap {
		bErr := DB.SelectConnect("db_standalonesite").Table(tn).CreateInBatches(messages, len(messages)).Error
		if bErr != nil {
			return bErr
		}
	}
	return nil
}

func GetAllUserUnreadCountInfo(tableIndex int64) ([]*model.MessageUnreadInfo, error) {
	var err error
	var messageUnreadInfo []*model.MessageUnreadInfo
	sql := fmt.Sprintf("select receiver_user_intl_openid,count(*) as unread_count from p_message_%d where is_read=0 and is_del=0 group by receiver_user_intl_openid;", tableIndex)

	if err = DB.SelectConnect("db_standalonesite").Raw(sql).Find(&messageUnreadInfo).Error; err != nil {
		return nil, err
	}

	return messageUnreadInfo, nil
}
