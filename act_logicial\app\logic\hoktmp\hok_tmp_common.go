package hoktmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"
	"net/http"
	"strings"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/logic/common"
	hok_request "trpc.act.logicial/app/logic/hoktmp/hok_query_batch_request_pb"
)

type GetDataGroupMetricsParam struct {
	Host       string
	Path       string
	UID        string
	ActivityID string                    // 活动id
	Targets    []*hok_request.TargetInfo // 指标ID
	XBid       string                    // 必须字段，唯一调用标识，与三组同学约定好，并配置到网关的七彩石配置中
	Caller     string                    // 多个活动同时存在需区分
}

type LIPCallsHOKClusterProxyParam struct {
	MethodName  string
	PostString  string
	RequestType string
}

// GetDataGroupMetrics 获取HOK数据指标
func (r *GetDataGroupMetricsParam) GetDataGroupMetrics(ctx context.Context) (*hok_request.QueryBatchResponse, error) {
	host := r.Host
	path := r.Path
	url := strings.Join([]string{"http://", host, path}, "")
	uuid, _ := common.GenerateUUID()
	header := map[string]string{
		"Content-Type": "application/x-rec-bytes",
		"X-Bid":        r.XBid,
		"trpc-caller":  r.Caller,
		"X-Ext-Uid":    r.UID,
		"X-Request-Id": uuid,
	}
	req := &hok_request.QueryBatchRequest{
		ActivityID: r.ActivityID,
		Targets:    r.Targets,
	}

	// 序列化
	marshal, err := proto.Marshal(req)
	if err != nil {
		log.ErrorContextf(ctx, "GetDataGroupMetrics proto.Marshal err: %v", err)
		return nil, err
	}

	data := httpclient.ClientOption{
		URL:        url,
		Header:     header,
		Type:       "POST",
		PostString: string(marshal),
	}
	result := httpclient.RequestOne(ctx, data)
	if result.RequestError != nil {
		// 请求失败
		log.WithFieldsContext(ctx, "log_type", "err", "uuid", uuid).Errorf(
			"GetDataGroupMetrics err data: %v", data)
		return nil, errs.NewCustomError(ctx, code.HokTmpHttpError, "http error, \t [Error]:{%v} ",
			result.RequestError)
	}
	response := result.Result
	var queryBatchResponse hok_request.QueryBatchResponse
	if err = proto.Unmarshal([]byte(response), &queryBatchResponse); err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "uuid", uuid).Infof(
			"GetDataGroupMetrics proto Unmarshal err: %v", err)
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.HokTmpHttpError, "http error, \t [Error]:{%v} ", err)
	}
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "GetDataGroupMetrics").Infof(
		"GetDataGroupMetrics success; req marshal:[%v] response: %v,queryBatchResponse:[%v]", string(marshal), response, &queryBatchResponse)
	return &queryBatchResponse, nil
}

// LIPCallsHOKClusterProxy LIP调用HOK代理
func LIPCallsHOKClusterProxy(ctx context.Context, param LIPCallsHOKClusterProxyParam) (string, error) {

	//// LIP UID 转为 游戏UID
	//hokOpenId, err := service.GetGameOpenId(ctx, constant.GAMEID_HOK)
	//if nil != err {
	//	log.ErrorContextf(ctx, "LIPCallsHOKClusterProxy get game openid error:%v", err)
	//	return "", err
	//}
	//if hokOpenId == "" {
	//	log.ErrorContextf(ctx, "LIPCallsHOKClusterProxy hok openid is empty")
	//	return "", errs.NewCustomError(ctx, code.GameIDNotObtained, "GameID Not Obtained")
	//}
	conf := config.GetConfig().LIPBandingHOK2504Conf
	host := conf.HOKClusterProxyHost
	path := conf.HOKClusterProxyPath
	if param.RequestType == "" {
		param.RequestType = http.MethodPost
	}
	url := strings.Join([]string{host, path, "/", param.MethodName}, "")
	header := map[string]string{
		"Content-Type": "application/json",
	}
	data := httpclient.ClientOption{
		URL:        url,
		Header:     header,
		Type:       "POST",
		PostString: param.PostString,
	}
	result := httpclient.RequestOne(ctx, data)
	if result.RequestError != nil {
		// 请求失败
		log.WithFieldsContext(ctx, "log_type", "err").Errorf("LIPCallsHOKClusterProxy err data: %v", data)
		return "", errs.NewCustomError(ctx, code.HokTmpHttpError, "http error, \t [Error]:{%v} ",
			result.RequestError)
	}
	response := result.Result
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "LIPCallsHOKClusterProxy").Infof(
		"LIPCallsHOKClusterProxy success response: %v", response)
	return response, nil
}
