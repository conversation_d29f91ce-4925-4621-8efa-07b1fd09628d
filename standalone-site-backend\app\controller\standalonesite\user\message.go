package user

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/message"
)

func (u *UserImpl) GetMessage(c context.Context, req *pb.GetMessageReq) (rsp *pb.GetMessageRsp, err error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	// openid := "29080-12945745392039390084"
	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}
	// language = "zh"
	messages, err := message.GetMessagesNew(c, req, openid, language)

	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetMessages err: %v\n", err)
		return
	}
	return messages, nil
}

func (u *UserImpl) ReadMessageAll(c context.Context, req *pb.ReadMessageAllReq) (rsp *pb.ReadMessageAllRsp, err error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	// openid := "2670566212109452541"

	err = message.ReadMessageAll(openid, req.Type)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.ReadMessage err: %v\n", err)
		return
	}
	return &pb.ReadMessageAllRsp{}, nil
}

func (u *UserImpl) ReadMessage(c context.Context, req *pb.ReadMessageReq) (rsp *pb.ReadMessageRsp, err error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	// openid := "14508101851624013246"

	err = message.ReadMessage(c, req.Id, openid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.ReadMessage err: %v\n", err)
		return
	}
	return &pb.ReadMessageRsp{}, nil
}

func (u *UserImpl) UnReadMessage(c context.Context, req *pb.UnReadMessageCountReq) (rsp *pb.UnReadMessageCountRsp, err error) {
	// 获取openid
	userAccount, err := metadata.GetUserAccount(c)
	if err != nil {
		return nil, err
	}
	openid := userAccount.Uid
	//openid := userAccount.IntlAccount.OpenId
	//openid := "29080-10691528869772811045"
	rsp, err = message.GetUnreadCount(openid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.ReadMessage err: %v\n", err)
		return
	}
	return
}
