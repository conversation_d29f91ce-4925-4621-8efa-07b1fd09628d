package cache

import (
	"context"
	"errors"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	redisClient "github.com/go-redis/redis/v8"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
)

// DelCacheByKey 根据key删除缓存 - 失败重试3次
func DelCacheByKey(ctx context.Context, key string) error {
	var err error
	for i := 0; i < constants.MaxRetries; i++ {
		err = redis.GetClient().Del(ctx, key).Err()
		if err == nil {
			return nil
		}
	}
	_ = errs.NewCustomError(ctx, code.DelCacheByKeyErr, "DelCacheByKey Del cache; key:(%s), err=%v", key, err)
	return err
}

func DeleteUserPostsCache(queryIntlOpenID, nextPageCursor string, limit int64) {
	c := context.Background()
	defer recovery.CatchGoroutinePanic(c)
	// 删除所有语言下的缓存
	for _, language := range constants.AllPostLanguages {
		postBasesRedisKey := GetUserPostBaseListGuestKey(queryIntlOpenID, nextPageCursor, limit, language)
		redis.GetClient().Del(c, postBasesRedisKey)
		postBasesRedisKey = GetUserPostBaseListHostKey(queryIntlOpenID, nextPageCursor, limit, language)
		redis.GetClient().Del(c, postBasesRedisKey)
	}

}

// 清除客态的帖子缓存
func RemoveUserPostCacheKeys(intlOpenid string) {
	c := context.Background()
	defer recovery.CatchGoroutinePanic(c)
	keysKey := GetUserPostCacheKeysKey(intlOpenid)
	keys, err := redis.GetClient().SMembers(c, keysKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveUserPostCacheKeys err by get post cache keys, intl_openid:(%s), err=(%v)", intlOpenid, err)
		}
		return
	}
	for _, key := range keys {
		redis.GetClient().Del(c, key)
	}
	redis.GetClient().Del(c, keysKey)
}

// 清除客态的帖子缓存
func RemovePostTagsCacheKey(postUuid string) {
	c := context.Background()
	defer recovery.CatchGoroutinePanic(c)
	for _, language := range constants.AllPostLanguages {
		postTagsRedisKey := GetTagIdByPostUUIDKey(postUuid, language)
		redis.GetClient().Del(c, postTagsRedisKey)
	}
}

// 请求帖子翻译信息
func RemovePostTranslational(postUuid string) {
	c := context.Background()
	defer recovery.CatchGoroutinePanic(c)
	keys := make([]string, 0)
	for _, postLanguage := range constants.AllPostLanguages {
		keys = append(keys, GetTranslateContentKey(1, postUuid, postLanguage))
	}
	redis.GetClient().Del(context.Background(), keys...)
}

func RemoveUserCommentCacheKeys(intlOpenid string) {
	c := context.Background()
	defer recovery.CatchGoroutinePanic(c)
	keysKey := GetUserCommentCacheKeysKey(intlOpenid)
	keys, err := redis.GetClient().SMembers(c, keysKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveUserCommentCacheKeys err by get post cache keys, intl_openid:(%s), err=(%v)", intlOpenid, err)
		}
		return
	}
	for _, key := range keys {
		redis.GetClient().Del(c, key)
	}
	redis.GetClient().Del(c, keysKey)
}

func DeletePostCache(ctx context.Context, postUUID string) {
	defer recovery.CatchGoroutinePanic(context.Background())
	for _, language := range constants.AllPostLanguages {
		postBasesRedisKey := GetPostInfoKey(postUUID, language)
		_, err := redis.GetClient().Del(ctx, postBasesRedisKey).Result()
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("DeletePostCache err: %v, postUUID: %s, language: %s", err, postUUID, language)
		}
	}
}

func RemoveDistrictListCache(c context.Context) error {
	defer recovery.CatchGoroutinePanic(context.Background())
	for _, language := range constants.AllPostLanguages {
		postBasesRedisKey := GetDistrictListKey(language, "", 500)
		_, err := redis.GetClient().Del(c, postBasesRedisKey).Result()
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveDistrictListCache err: %v, language: %s", err, language)
		}
	}
	return nil
}

// 用户关注相关key
func RemoveUserFollowsCache(c context.Context, intlOpenid string) {
	defer recovery.CatchGoroutinePanic(context.Background())
	cacheKey := UserFollowsCacheKeyKeys(intlOpenid)
	keys, err := redis.GetClient().SMembers(c, cacheKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveUserFollowsCache err by get post cache keys, intl_openid:(%s), err=(%v)", intlOpenid, err)
			return
		}
	}
	for _, key := range keys {
		redis.GetClient().Del(c, key)
	}
	redis.GetClient().Del(c, cacheKey)
}

// 移除用户fans 相关接口
func RemoveUserFansCache(c context.Context, intlOpenid string) {
	defer recovery.CatchGoroutinePanic(context.Background())
	cacheKeys := GetUserFansKeysKey(intlOpenid)
	keys, err := redis.GetClient().SMembers(c, cacheKeys).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RemoveUserFollowCache err by get post cache keys, intl_openid:(%s), err=(%v)", intlOpenid, err)
			return
		}
	}
	for _, key := range keys {
		redis.GetClient().Del(c, key)
	}
}

func RemovePostContentCache(c context.Context, postUuid, language string) {
	defer recovery.CatchGoroutinePanic(c)
	cacheKey := GetPostContentKey(postUuid, language)
	redis.GetClient().Del(context.Background(), cacheKey)

}

func RemoveUserHostInfoKey(c context.Context, intlOpenid string) {
	languageList := []string{"en", "ko", "ja", "zh", "zh-TW", ""}
	for _, language := range languageList {
		userProfileRedisKey := GetUserHostInfoKey(intlOpenid, language)
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("RemoveUserHostInfoKey, intlOpenid:(%s), language:(%s), key: (%s)", intlOpenid, language, userProfileRedisKey)
		redis.GetClient().Del(c, userProfileRedisKey)
	}
}

func DeleteUserInfoCache(c context.Context, userIntlOpenID string) {
	if len(userIntlOpenID) == 0 {
		return
	}
	userInfoGuestRedisKey := GetUserInfoAllKey(userIntlOpenID, "guest")
	redis.GetClient().Del(context.Background(), userInfoGuestRedisKey)
	userInfoHostRedisKey := GetUserInfoAllKey(userIntlOpenID, "host")
	redis.GetClient().Del(context.Background(), userInfoHostRedisKey)
	userBaseRedisKey := GetUserInfoKey(userIntlOpenID)
	redis.GetClient().Del(context.Background(), userBaseRedisKey)
	userBaseInfoRedisKey := GetUserBaseInfoKey(userIntlOpenID)
	redis.GetClient().Del(context.Background(), userBaseInfoRedisKey)
	for _, language := range constants.AllPostLanguages {
		userProfileGuestRedisKey := GetUserGuestInfoKey(userIntlOpenID, language)
		redis.GetClient().Del(context.Background(), userProfileGuestRedisKey)
		userProfileHostRedisKey := GetUserHostInfoKey(userIntlOpenID, language)
		redis.GetClient().Del(context.Background(), userProfileHostRedisKey)
	}

}

func BatchDeleteUserInfosCache(c context.Context, userIntlOpenIDs []string) {
	for _, userIntlOpenID := range userIntlOpenIDs {
		DeleteUserInfoCache(c, userIntlOpenID)
	}
}

func DeleteCreatorHubUserBoundInfoCache(c context.Context, intlOpenid string) {
	redisKey := GetCreatorHubUserBoundInfoKey(intlOpenid)
	redis.GetClient().Del(c, redisKey)
}

// 清理话题详情页缓存
func DeleteTagPostsCache(tagId int64) {
	c := context.Background()
	defer recovery.CatchGoroutinePanic(c)
	keysKey := GetTagPostCacheKeysKey(tagId)
	keys, err := redis.GetClient().SMembers(c, keysKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeleteTagPostsCache err by get tag cache keys, tagId:(%s), err=(%v)", tagId, err)
		}
		return
	}
	for _, key := range keys {
		redis.GetClient().Del(c, key)
	}
	redis.GetClient().Del(c, keysKey)
}

// 帖子列表缓存
func DeletePostsListWithOpenidCache(openid string) {
	c := context.Background()
	defer recovery.CatchGoroutinePanic(c)
	keysKey := GetPostsListCacheKeyWithOpenid(openid)
	keys, err := redis.GetClient().SMembers(c, keysKey).Result()
	if err != nil {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeletePostsListCache err by get post cache keys, openid:(%s), err=(%v)", openid, err)
		}
		return
	}
	for _, key := range keys {
		redis.GetClient().Del(c, key)
	}
	redis.GetClient().Del(c, keysKey)
	return
}
