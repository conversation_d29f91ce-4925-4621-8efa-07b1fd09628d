// Package nikkemusic nikke 音乐节
package nikkemusic

import "trpc.act.logicial/app/model"

// NikkeMusicSendModel Model
type NikkeMusicSendModel struct {
	model.AppModel
}

// TableName .
func (NikkeMusicSendModel) TableName() string {
	return "nikke_music_send"
}

// NikkeMusicSend 临时结构
type NikkeMusicSend struct {
	NikkeMusicSendModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(255);column:uid;"`
	Status      int    `gorm:"type:int(1);column:status;not null"`
	Tag         string `gorm:"type:varchar(255);column:tag;not null"`
	FsourceID   string `gorm:"type:varchar(255);column:Fsource_id;not null"`
	LangType    string `gorm:"type:varchar(255);column:lang_type;"`
	AccountType int    `gorm:"type:smallint(6);column:account_type" comment:"用户类型"`
}
