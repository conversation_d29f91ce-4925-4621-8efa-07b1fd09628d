package report

import (
	"context"
	"strings"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/comment"
	"trpc.publishing_application.standalonesite/app/logic/tweet"
	"trpc.publishing_application.standalonesite/app/model"
)

// PostReportService 创建举报记录
func PostReportService(c context.Context, req *pb.ContentReportReq, myOpenid, gameId, areaId, language string) error {
	if req.ContentUuid == "" {
		return errs.NewCustomError(c, code.InvalidParams, "content id is required")
	}
	postReportInfo, getErr := dao.PostReportGet(0, myOpenid, req.ContentUuid, req.ContentType)
	if getErr == nil && postReportInfo.Model != nil && postReportInfo.ID > 0 {
		// 已经存在相同举报内容，请勿重复操作举报
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("The same report content already exists, please do not repeat the report.req: %v", req)
		return errs.NewCustomError(c, code.RepeatedReportError, "Please do not report repeatedly")
	}

	var reportedIntlOpenid string
	esReport := &model.ESReportInfo{}
	switch constants.PostReportContentT(req.ContentType) {
	case constants.REPORT_CONTENT_TYPE_POST:
		postFormated, err := tweet.GetPostInfo(c, "", req.ContentUuid, "en", 0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostReportService GetPostByID err: %v", err)
			return errs.NewCustomError(c, code.GetPostFailed, "Failed to get dynamic info")
		}
		var tagIds []int64
		for _, postFormatedTag := range postFormated.Tags {
			tagIds = append(tagIds, postFormatedTag.Id)
		}
		reportedIntlOpenid = postFormated.IntlOpenid
		esReport.PlateID = postFormated.PlateId
		esReport.Language = postFormated.Language
		esReport.SubContentType = postFormated.Type
		esReport.Title = postFormated.Title
		esReport.Content = postFormated.Content
		esReport.PicUrls = postFormated.PicUrls
		esReport.Tags = tagIds
		esReport.ExtInfo = postFormated.ExtInfo
		esReport.Platform = postFormated.Platform
	case constants.REPORT_CONTENT_TYPE_COMMENT:
		postCommentRsp, err := comment.GetPostCommentInfo(c, "", req.ContentUuid, language)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetPostComments err: %v\n", err)
			return errs.NewCustomError(c, code.GetCommentFailed, " Failed to get comment base info")
		}
		reportedIntlOpenid = postCommentRsp.Comment.IntlOpenid
		esReport.Title = postCommentRsp.Comment.Title
		esReport.Content = postCommentRsp.Comment.Content
		esReport.PicUrls = postCommentRsp.Comment.PicUrls
		esReport.Language = postCommentRsp.Comment.Language
	default:
		return errs.NewCustomError(c, code.InvalidPostReportContentType, "Report Content type is incorrect")
	}

	// 自己不能举报自己的内容
	if myOpenid == reportedIntlOpenid {
		return errs.NewCustomError(c, code.NoNeedToReportSelfPost, "Operation failed, not allowed to report the content posted by me")
	}
	postReport := &model.PostReport{
		ContentUuid:        req.ContentUuid,
		ContentType:        constants.PostReportContentT(req.ContentType),
		ReportType:         constants.PostReportT(req.ReportType),
		Reason:             req.Reason,
		GameId:             gameId,
		AreaId:             areaId,
		Status:             1,
		ReportIntlOpenid:   myOpenid,
		ReportedIntlOpenid: reportedIntlOpenid,
		Language:           esReport.Language, //使用帖子或者当前评论的语言填充
	}
	err := dao.PostReportCreate(postReport)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostReportServiceNew PostReportCreate err: %v", err)
		return errs.NewCustomError(c, code.CreatePostReportError, "Failed to Report.")
	}

	esReport.Id = postReport.ID
	esReport.ReportType = int64(postReport.ReportType)
	esReport.ContentType = int64(postReport.ContentType)
	esReport.ContentUuid = req.ContentUuid
	esReport.ReportIntlOpenid = myOpenid
	esReport.ReportedIntlOpenid = reportedIntlOpenid
	esReport.Reason = req.Reason
	esReport.GameId = gameId
	esReport.AreaId = areaId
	esReport.Status = 1
	esReport.UpdateUser = postReport.UpdateUser
	esReport.CreatedOn = postReport.CreatedOn
	esReport.ModifiedOn = postReport.ModifiedOn

	// 处理用户intl_user_openid, 区别于intl_openid; ps: 这个intl_openid是一定会有的。
	if strings.Contains(esReport.ReportIntlOpenid, "-") && len(strings.Split(esReport.ReportIntlOpenid, "-")) > 1 {
		esReport.ReportIntlUserOpenid = strings.Split(esReport.ReportIntlOpenid, "-")[1]
	}
	// 处理用户intl_user_openid, 区别于intl_openid; ps: 这个intl_openid是一定会有的。
	if strings.Contains(esReport.ReportedIntlOpenid, "-") && len(strings.Split(esReport.ReportedIntlOpenid, "-")) > 1 {
		esReport.ReportedIntlUserOpenid = strings.Split(esReport.ReportedIntlOpenid, "-")[1]
	}

	PushReportContentToES(esReport)

	return nil
}

func PushReportContentToES(esReport *model.ESReportInfo) {
	data := []map[string]interface{}{{
		"id":                        esReport.Id,
		"report_type":               esReport.ReportType,
		"content_type":              esReport.ContentType,
		"content_uuid":              esReport.ContentUuid,
		"plate_id":                  esReport.PlateID,
		"reported_intl_openid":      esReport.ReportedIntlOpenid,
		"report_intl_openid":        esReport.ReportIntlOpenid,
		"reported_intl_user_openid": esReport.ReportedIntlUserOpenid,
		"report_intl_user_openid":   esReport.ReportIntlUserOpenid,
		"language":                  esReport.Language,
		"sub_content_type":          esReport.SubContentType,
		"title":                     esReport.Title,
		"content":                   esReport.Content,
		"pic_urls":                  esReport.PicUrls,
		"tags":                      esReport.Tags,
		"ext_info":                  esReport.ExtInfo,
		"platform":                  esReport.Platform,
		"reason":                    esReport.Reason,
		"game_id":                   esReport.GameId,
		"area_id":                   esReport.AreaId,
		"status":                    esReport.Status,
		"update_user":               esReport.UpdateUser,
		"created_on":                esReport.CreatedOn,
		"modified_on":               esReport.ModifiedOn,
		"is_del":                    0,
	}}

	dao.EsBulkPushDoc(config.GetConfig().ElasticSearchSetting.ReportIndex, "id", data)
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.ReportIndex)
}
