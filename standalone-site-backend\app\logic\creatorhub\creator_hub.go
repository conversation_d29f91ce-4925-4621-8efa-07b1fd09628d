package creatorhub

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
)

const (
	APIXVersionV10 = "APIX1-HMAC-SHA256"
)

var CreatorhubLanguageMap = map[string]string{
	"cnt": "zh-TW", ///** 繁体中文（台湾） */
	"cnh": "zh-TW", ///** 繁体中文（香港） */
	"cn":  "zh",    //  /** 简体中文 */
	"ar":  "ar",    // /** Arabic (阿拉伯语) */
	"de":  "de",    // /** German (德语) */
	"es":  "es",    // /** 西班牙语（西班牙） */
	"fr":  "fr",    ///** 法语（法国） */
	"in":  "in",    // /** 印尼语 */
	"ma":  "ma",    //  /** 马来语 */
	"po":  "po",    /** 葡萄牙语（葡萄牙） */
	"py":  "py",    ///** 俄语 */
	"ta":  "ta",    // /** 泰语 */
	"tu":  "tu",    // /** 土耳其语 */
	"yu":  "yu",    ///** 越南语 */
	"en":  "en",    ///** 英语 */
	"ja":  "ja",    // /** 日语 */
	"ko":  "ko",    // /** 韩语 */
	"it":  "it",    //  /** 意大利语 */
}

func RequestCreateHub(c context.Context, gameId, areaId, urlPath string, postByte []byte) (string, error) {
	// if trpc.GlobalConfig().Global.EnvName == "pre" {
	// 	gameId = "10000"
	// 	postData := map[string]interface{}{}
	// 	json.Unmarshal(postByte, &postData)
	// 	postData["gameid"] = "10000"
	// 	postByte, _ = json.Marshal(postData)

	// }
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("gameId: %s, areaId: %s, urlPath: %s, postByte:%s", gameId, areaId, urlPath, string(postByte))
	// 生成签名，调用creatorhub网红平台接口
	creatorhubConf := (config.GetConfig()).CreatorHubSetting
	currentTimeStamp := fmt.Sprintf("%d", time.Now().Unix())
	signature := GenAuthSignV10(urlPath, http.MethodPost, "", currentTimeStamp, creatorhubConf.SecretId, creatorhubConf.SecretKey, postByte)
	authHeaderStr := fmt.Sprintf("%s Signature=%s", APIXVersionV10, signature)
	requestURL := fmt.Sprintf("%s%s", creatorhubConf.ServiceUrl, urlPath)
	// 预发布用10000验证

	xCommonParamsStr := fmt.Sprintf("{\"game_id\":\"%s\",\"area_id\":\"%s\",\"time_zone_offset\":-480}", gameId, areaId)

	headerData := map[string]string{
		"Content-Type":       "application/json; charset=utf-8",
		"Authorization":      authHeaderStr,
		"Host":               creatorhubConf.ServiceHost,
		"X-APIX-Timestamp":   currentTimeStamp,
		"X-APIX-Application": creatorhubConf.SecretId,
		"X-Common-Params":    xCommonParamsStr,
	}
	// 测试环境添加这个判断，现网不需要
	if trpc.GlobalConfig().Global.EnvName == "test" {
		headerData["ch-env"] = "test-a"
	}

	optionOne := httpclient.ClientOption{
		URL:        requestURL,
		Type:       http.MethodPost,
		Timeout:    30 * time.Second,
		Header:     headerData,
		PostString: string(postByte),
	}

	// 发请求
	resultOption := httpclient.RequestOne(c, optionOne)
	// 结果判断
	if resultOption.RequestError != nil {
		// 请求返回错误直接记录错误信息进入下一次循环
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call Community api return error, url %s", requestURL)
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Call Community api http request error, %v", resultOption.RequestError)
		return "", resultOption.RequestError
	}
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("request result data: %s", resultOption.Result)
	return resultOption.Result, nil
}

// GenAuthSignV10 生成认证签名v10版本
func GenAuthSignV10(uri, method, rawQuery, reqTimeStamp, secretId, secretKey string, requestBody []byte) string {
	if uri == "" || secretKey == "" {
		return ""
	}
	var hashedRequestPayload string
	if len(requestBody) != 0 {
		hashedRequestPayload = hexEncodeSHA256ToLowercase(requestBody)
	}
	canonicalRequest := strings.Join([]string{method, rawQuery, hashedRequestPayload}, "\n")
	hashedCanonicalRequest := hexEncodeSHA256ToLowercase([]byte(canonicalRequest))
	stringToSign := strings.Join([]string{APIXVersionV10, reqTimeStamp, hashedCanonicalRequest}, "\n")
	secretTimestamp := hmacSHA256(secretKey, reqTimeStamp)
	secretSigning := hmacSHA256(secretTimestamp, uri)
	signature := hexEncodeHmacSHA256(secretSigning, stringToSign)
	return signature
}

func hexEncodeSHA256ToLowercase(data []byte) string {
	if len(data) == 0 {
		return ""
	}
	h := sha256.New()
	h.Write(data)
	return strings.ToLower(hex.EncodeToString(h.Sum(nil)))
}

func hmacSHA256(secret, data string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return string(h.Sum(nil))
}

func hexEncodeHmacSHA256(secret, data string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}
