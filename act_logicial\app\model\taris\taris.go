// Package taris taris
package taris

import "trpc.act.logicial/app/model"

// TarisRankTempModel Model
type TarisRankTempModel struct {
	model.AppModel
}

// TableName .
func (TarisRankTempModel) TableName() string {
	return "taris_rank_temp"
}

// TarisRankTemp 临时结构
type TarisRankTemp struct {
	TarisRankTempModel
	ID           int64  `gorm:"type:int(11);column:id;primary_key"`
	RoleName     string `gorm:"type:varchar(255);column:role_name;"`
	RoleID       string `gorm:"type:varchar(255);column:role_id;"`
	AreaID       int64  `gorm:"type:int(11);column:area_id;"`
	ZoneID       int64  `gorm:"type:int(11);column:zone_id;"`
	ClearLevelAt int64  `gorm:"type:int(11);column:clear_level_at;"`   // 通关时间
	InstanceID   string `gorm:"type:varchar(255);column:instance_id;"` // 副本id
	UnionName    string `gorm:"type:varchar(255);column:union_name;"`  // 工会名称
	Rank         int32  `gorm:"type:int(11);column:rank;"`             // 工会排名
	Now          int64  `gorm:"type:int(11);column:now;"`              // 数据拉取时间
	DtEventTime  int64  `gorm:"type:int(11);column:dteventtime;"`      // 通过完成时间戳
}

// TarisSignUpRoleTempModel TODO
type TarisSignUpRoleTempModel struct {
	model.AppModel
}

// TableName .
func (TarisSignUpRoleTempModel) TableName() string {
	return "taris_sign_up_role"
}

// TarisSignUpRoleTemp Model 临时结构
type TarisSignUpRoleTemp struct {
	TarisSignUpRoleTempModel
	ID        int64  `gorm:"type:int(11);column:id;primary_key"`
	RoleID    string `gorm:"type:varchar(255);column:role_id;"`
	UID       string `gorm:"type:varchar(255);column:uid;"`
	LangType  string `gorm:"type:varchar(255);column:lang_type;"`
	FsourceID string `gorm:"type:varchar(255);column:Fsource_id;"`
}

// TarisSendTempModel TODO
type TarisSendTempModel struct {
	model.AppModel
}

// TableName .
func (TarisSendTempModel) TableName() string {
	return "taris_send_temp_asia"
}

// TarisSendTemp Model Model 临时结构
type TarisSendTemp struct {
	TarisSendTempModel
	ID        int64  `gorm:"type:int(11);column:id;primary_key"`
	UID       string `gorm:"type:varchar(255);column:uid;"`
	Status    int    `gorm:"type:int(1);column:status;not null"`
	Tag       string `gorm:"type:varchar(255);column:tag;not null"`
	FsourceID string `gorm:"type:varchar(255);column:Fsource_id;not null"`
	LangType  string `gorm:"type:varchar(255);column:lang_type;"`
}

// TarisUnBindRoleModel TODO
type TarisUnBindRoleModel struct {
	model.AppModel
}

// TableName .
func (TarisUnBindRoleModel) TableName() string {
	return "taris_unbind_role"
}

// TarisUnBindRole TODO
// TarisSendTemp Model Model 临时结构
type TarisUnBindRole struct {
	TarisUnBindRoleModel
	ID     int64  `gorm:"type:int(11);column:id;primary_key"`
	RoleID string `gorm:"type:varchar(255);column:role_id;"`
	Status int    `gorm:"type:int(1);column:status;not null"`
}
