package networkagent

import (
	"context"
	"strings"

	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_network_agent"
)

type NetworkImp struct {
	pb.UnimplementedRemoteAgent
}

func (s *NetworkImp) RemoteAgentRequest(ctx context.Context, req *pb.RemoteAgentRequestReq) (rsp *pb.RemoteAgentRequestRsp, err error) {
	rsp = &pb.RemoteAgentRequestRsp{}
	// 签名校验
	err = ValidateSecurityKey(ctx, req.SecondTimestamp, req.SecurityKey)
	if err != nil {
		return rsp, err
	}
	method := strings.ToLower(req.Method)
	switch method {
	case "postform":
		responseData, err := SendPostFromRequest(ctx, req.Url, req.ContentType, req.Body, req.Timeout)
		rsp.RspData = responseData
		return rsp, err
	case "get":
		responseData, err := SendGetRequest(ctx, req.Url, req.Timeout)
		rsp.RspData = responseData
		return rsp, err
	case "post":
		responseData, err := SendPostRequest(ctx, req.Url, req.ContentType, req.Body, req.Timeout)
		rsp.RspData = responseData
		return rsp, err
	}
	return rsp, nil
}
