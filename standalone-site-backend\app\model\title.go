package model

// 用户称号
type Title struct {
	*Model
	TitleLanguage *TitleLanguage `json:"-"`
	Avatar        string         `json:"avatar"`
	UpTime        int64          `json:"up_time"`
	DownTime      int64          `json:"down_time"`
	InitHot       int64          `json:"init_hot"`
	PossessNum    int64          `json:"possess_num"`
	Status        int64          `json:"status"`
	GameId        string         `json:"game_id"`
	AreaId        string         `json:"area_id"`
}

// 用户称号
type TitleNewListFormated struct {
	ID            int64                  `json:"id"`
	TitleLanguage *TitleLanguageFormated `json:"language"`
	Avatar        string                 `json:"avatar"`
	UpTime        int64                  `json:"up_time"`
	DownTime      int64                  `json:"down_time"`
	InitHot       int64                  `json:"init_hot"`
	PossessNum    int64                  `json:"possess_num"`
	Status        int64                  `json:"status"`
	GameId        string                 `json:"game_id"`
	AreaId        string                 `json:"area_id"`
}

type TitleFormated struct {
	ID         int64  `json:"id"`
	Avatar     string `json:"avatar"`
	InitHot    int64  `json:"init_hot"`
	PossessNum int64  `json:"possess_num"`
	Status     int64  `json:"status"`
	GameId     string `json:"game_id"`
	AreaId     string `json:"area_id"`
}

func (t *Title) TableName() string {
	return "p_title"
}

type UserBindTitleItem struct {
	UserId  int64 `json:"user_id"`
	TitleId int64 `json:"title_id"`
	InitHot int64 `json:"init_hot"`
}

type BindTitle struct {
	Titles []*TitleFormated `json:"titles"`
}

func (t *Title) Format() *TitleFormated {
	if t.Model != nil {
		return &TitleFormated{
			ID:         t.ID,
			Avatar:     t.Avatar,
			InitHot:    t.InitHot,
			PossessNum: t.PossessNum,
			Status:     t.Status,
			GameId:     t.GameId,
			AreaId:     t.AreaId,
		}
	}

	return nil
}

func (t *Title) TitleNewListFormated() *TitleNewListFormated {
	if t.Model != nil {
		return &TitleNewListFormated{
			ID:            t.ID,
			TitleLanguage: &TitleLanguageFormated{},
			Avatar:        t.Avatar,
			InitHot:       t.InitHot,
			PossessNum:    t.PossessNum,
			Status:        t.Status,
			GameId:        t.GameId,
			AreaId:        t.AreaId,
		}
	}

	return nil
}
