package dao

import (
	"context"
	"errors"
	"time"

	"trpc.publishing_application.standalonesite/app/config"

	"github.com/jinzhu/copier"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/util"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/model"
)

type PostConditions struct {
	PostUuids          []string
	CompositeCondition *CompositeCondition
	PostHideCondition  *CompositeCondition
	IntlOpenid         string
	Order              []*OrderConditions
	Language           []string
	IsAudit            int
	LtId               int64
	PlateId            int64
	IsTop              string
	IsIgnore           bool
	LtCreatedOnMs      int64
	IsHide             bool
}

func CreatePost(post *model.Post) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName()).Create(&post).Error
}

func CreatePostNew(post *model.Post, postContent []*model.PostContent, currentTime time.Time, isDemotion bool) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		// 写入总表
		postErr := tx.InstanceSet("now_time", currentTime).Table((&model.Post{}).TableName()).Create(&post).Error
		if postErr != nil {
			return postErr
		}
		// 写入多语言表
		for _, content := range postContent {
			languageTable := (&model.PostLanguage{}).GetTableName(util.ZHTWLanguageToSql(content.Language))
			if languageTable == "" {
				// todo 报错
				return errors.New("post language table failed")
			}
			var postLanguageData = &model.PostLanguage{}
			postCopyErr := copier.Copy(&postLanguageData, post)
			if postCopyErr != nil {
				// todo 报错
				return errors.New("post language copy post failed")
			}
			// 重置postLanguage的主键id
			postLanguageData.ID = 0
			postLanguageErr := tx.Table(languageTable).Create(postLanguageData).Error
			if postLanguageErr != nil {
				// todo 报错
				return errors.New("create post language failed")
			}
			// 写入内容表
			tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(content.PostUUID)
			if err != nil {
				return err
			}
			contentErr := tx.Table(tableName).Create(&content).Error
			if contentErr != nil {
				return contentErr
			}
		}
		// 写入数据统计表
		var postStats = &model.PostStats{
			PostUUID:  post.PostUUID,
			UpvoteMap: "{}",
		}
		if isDemotion {
			postStats.DemotionNum = config.GetConfig().Dynamic.DemotionNum
		}
		err := tx.Table((&model.PostStats{}).TableName()).Create(postStats).Error
		if err != nil {
			return err
		}
		return nil
	})
}

func UpdatePostNew(needRefreshFriendCard, needRefreshGuildCard int32, post *model.Post, postContents []*model.PostContent, deleteContentIds []int64, deleteLanguages []string) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		// 更新动态表
		updateParams := map[string]interface{}{
			"post_uuid":     post.PostUUID,
			"intl_openid":   post.IntlOpenid,
			"plate_id":      post.PlateID,
			"language":      post.Language,
			"type":          post.Type,
			"is_top":        post.IsTop,
			"top_sort":      post.TopSort,
			"top_on":        post.TopOn,
			"is_audit":      post.IsAudit,
			"game_id":       post.GameId,
			"area_id":       post.AreaId,
			"created_on":    post.Model.CreatedOn,
			"created_on_ms": post.CreatedOnMs,
			"platform":      post.Platform,
			"publish_on":    post.PublishOn,
			"is_official":   post.IsOfficial,
		}
		postErr := tx.Table((&model.Post{}).TableName()).Where("post_uuid = ?", post.PostUUID).Updates(updateParams).Error
		if postErr != nil {
			return postErr
		}
		tableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(post.PostUUID)
		if err != nil {
			return err
		}
		// 更新动态内容表
		if postContents != nil && len(postContents) > 0 {
			for _, postContentItem := range postContents {
				// 更新对应多语言主表
				languageTable := (&model.PostLanguage{}).GetTableName(util.ZHTWLanguageToSql(postContentItem.Language))
				if languageTable == "" {
					// todo 报错
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("post language table failed: %s", postContentItem.Language)
					continue
					// return errors.New("post language table failed")
				}

				// 判断是新增还是更新
				if postContentItem.Model != nil && postContentItem.Model.ID > 0 {
					updateLangParam := map[string]interface{}{
						"post_uuid":     post.PostUUID,
						"intl_openid":   post.IntlOpenid,
						"plate_id":      post.PlateID,
						"type":          post.Type,
						"is_top":        post.IsTop,
						"top_sort":      post.TopSort,
						"top_on":        post.TopOn,
						"is_audit":      post.IsAudit,
						"game_id":       post.GameId,
						"area_id":       post.AreaId,
						"created_on_ms": post.CreatedOnMs,
						"created_on":    post.Model.CreatedOn,
						"platform":      post.Platform,
						"publish_on":    post.PublishOn,
						"is_official":   post.IsOfficial,
					}
					postLanguageErr := tx.Table(languageTable).Where("post_uuid = ?", post.PostUUID).Updates(updateLangParam).Error
					if postLanguageErr != nil {
						return postLanguageErr
					}
					updateContentParam := map[string]interface{}{
						"post_uuid":              postContentItem.PostUUID,
						"intl_openid":            postContentItem.IntlOpenid,
						"platform":               postContentItem.Platform,
						"title":                  postContentItem.Title,
						"content":                postContentItem.Content,
						"pic_urls":               postContentItem.PicUrls,
						"content_summary":        postContentItem.ContentSummary,
						"ext_info":               postContentItem.ExtInfo,
						"language":               postContentItem.Language,
						"creator_statement_type": postContentItem.CreatorStatementType,
						"risk_remind_type":       postContentItem.RiskRemindType,
						"ai_content_type":        postContentItem.AiContentType,
						"original_url":           postContentItem.OriginalURL,
					}
					// 是否需要更新好友卡，0=不变更；1=需要更新为最新保存角色数据；2=删除好友卡
					if needRefreshFriendCard == 1 {
						updateContentParam["friend_card_info"] = postContentItem.FriendCardInfo
					} else if needRefreshFriendCard == 2 {
						updateContentParam["friend_card_info"] = ""
					}
					if needRefreshGuildCard == 1 {
						updateContentParam["guild_id"] = postContentItem.GuildId
					} else if needRefreshGuildCard == 2 {
						updateContentParam["guild_id"] = ""
					}
					contentErr := tx.Table(tableName).Where("id = ?", postContentItem.Model.ID).Updates(updateContentParam).Error
					if contentErr != nil {
						return contentErr
					}
				} else {
					var postLanguageData = &model.PostLanguage{
						PostUUID:    post.PostUUID,
						IntlOpenid:  post.IntlOpenid,
						PlateID:     post.PlateID,
						Type:        post.Type,
						IsTop:       post.IsTop,
						TopSort:     post.TopSort,
						TopOn:       post.TopOn,
						IsAudit:     post.IsAudit,
						GameId:      post.GameId,
						AreaId:      post.AreaId,
						CreatedOnMs: post.CreatedOnMs,
						Platform:    post.Platform,
						PublishOn:   post.PublishOn,
						IsOfficial:  post.IsOfficial,
					}
					var currentTime time.Time
					if post.Model.CreatedOn > 0 {
						// 如果传递进来的createdOn是有值的话，就要使用这个值，没有的话就用当前时间
						currentTime = time.Unix(post.Model.CreatedOn, 0)
					} else {
						currentTime = time.Now()
					}
					postLanguageErr := tx.InstanceSet("now_time", currentTime).Table(languageTable).Create(&postLanguageData).Error
					if postLanguageErr != nil {
						// todo 报错
						log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("create post language db error: %+v", postLanguageErr)
						return errors.New("create post language failed")
					}
					// 如果没有id的话那就是新增的
					contentErr := tx.Table(tableName).Create(&postContentItem).Error
					if contentErr != nil {
						return contentErr
					}
				}
			}
		}
		if len(deleteContentIds) > 0 {
			// 删除这条记录
			delErr := tx.Table(tableName).Where("id in ?", deleteContentIds).Delete(&model.PostContent{}).Error
			if delErr != nil {
				return delErr
			}
		}

		if len(deleteLanguages) > 0 {
			// 是否需要删除某些多语言
			for _, language := range deleteLanguages {
				languageTable := (&model.PostLanguage{}).GetTableName(util.ZHTWLanguageToSql(language))
				if languageTable == "" {
					// todo 报错
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("post language table failed: %s", language)
					continue
					// return errors.New("post language table failed")
				}
				// 删除这条记录
				delErr := tx.Table(languageTable).Where("post_uuid = ?", post.PostUUID).Delete(&model.PostContent{}).Error
				if delErr != nil {
					return delErr
				}
			}
		}

		return nil
	})
}

// UpdateNormalUserPost 普通用户更新帖子
func UpdateNormalUserPost(post *model.Post, postContents []*model.PostContent, oldLanguage, newLangauge string, oldPlateID, newPlateID int32, needRefreshFriendCard, needRefreshGuildCard int32) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		// 1、更新p_post总表的数据
		updateParams := make(map[string]interface{})
		if oldLanguage != newLangauge && newLangauge != "" {
			updateParams["language"] = newLangauge
		}
		if oldPlateID != newPlateID && newPlateID > 0 {
			updateParams["plate_id"] = newPlateID
		}
		if len(updateParams) > 0 {
			postErr := tx.Table((&model.Post{}).TableName()).Where("post_uuid = ?", post.PostUUID).Updates(updateParams).Error
			if postErr != nil {
				return postErr
			}
		}
		postContentTableName, err := (&model.PostContent{}).GetPostContentSubMeterTable(post.PostUUID)
		if err != nil {
			return err
		}

		// 2、更新对应多语言p_post_{language}主表
		// 如果更新了语言，则需要新增新语言记录，删除旧语言记录
		if oldLanguage != newLangauge && newLangauge != "" {
			// 删除旧语言主表记录
			oldLanguageTable := (&model.PostLanguage{}).GetTableName(util.ZHTWLanguageToSql(oldLanguage))
			if oldLanguageTable == "" {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UpdateNormalUserPost oldLanguage table failed: %s", oldLanguage)
				return errors.New("post language table failed")
			}
			// 删除这条记录
			delErr := tx.Table(oldLanguageTable).Where("post_uuid = ?", post.PostUUID).Delete(&model.Post{}).Error
			if delErr != nil {
				return delErr
			}

			// 新增新语言主表记录
			var postLanguageData = &model.PostLanguage{
				PostUUID:    post.PostUUID,
				IntlOpenid:  post.IntlOpenid,
				PlateID:     post.PlateID,
				Type:        post.Type,
				IsTop:       post.IsTop,
				TopSort:     post.TopSort,
				TopOn:       post.TopOn,
				IsAudit:     post.IsAudit,
				GameId:      post.GameId,
				AreaId:      post.AreaId,
				CreatedOnMs: post.CreatedOnMs,
				Platform:    post.Platform,
				PublishOn:   post.PublishOn,
				IsOfficial:  post.IsOfficial,
			}

			if oldPlateID != newPlateID && newPlateID > 0 {
				postLanguageData.PlateID = newPlateID
			}
			var currentTime time.Time
			if post.Model.CreatedOn > 0 {
				// 如果传递进来的createdOn是有值的话，就要使用这个值，没有的话就用当前时间
				currentTime = time.Unix(post.Model.CreatedOn, 0)
			} else {
				currentTime = time.Now()
			}
			newLanguageTable := (&model.PostLanguage{}).GetTableName(util.ZHTWLanguageToSql(newLangauge))
			if newLanguageTable == "" {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UpdateNormalUserPost newLangauge table failed: %s", newLangauge)
				return errors.New("post language table failed")
			}
			postLanguageErr := tx.InstanceSet("now_time", currentTime).Table(newLanguageTable).Create(&postLanguageData).Error
			if postLanguageErr != nil {
				// todo 报错
				return errors.New("create post language failed")
			}
		} else if oldPlateID != newPlateID && newPlateID > 0 {
			// 如果仅更新了板块信息
			updateLangParam := map[string]interface{}{
				"plate_id": post.PlateID,
			}
			oldLanguageTable := (&model.PostLanguage{}).GetTableName(util.ZHTWLanguageToSql(oldLanguage))
			if oldLanguageTable == "" {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UpdateNormalUserPost oldLanguage table failed: %s", oldLanguage)
				return errors.New("post language table failed2")
			}
			postLanguageErr := tx.Table(oldLanguageTable).Where("post_uuid = ?", post.PostUUID).Updates(updateLangParam).Error
			if postLanguageErr != nil {
				return postLanguageErr
			}
		}

		// 3、更新p_post_content_xx表的语言字段
		if postContents != nil && len(postContents) > 0 {
			for _, postContentItem := range postContents {
				if postContentItem.Model != nil && postContentItem.Model.ID > 0 {
					updateContentParam := map[string]interface{}{
						"risk_remind_type":       postContentItem.RiskRemindType,
						"original_url":           postContentItem.OriginalURL,
						"ai_content_type":        postContentItem.AiContentType,
						"creator_statement_type": postContentItem.CreatorStatementType,
					}
					if oldLanguage != newLangauge && newLangauge != "" {
						updateContentParam["language"] = newLangauge
					}
					// 是否需要更新好友卡，0=不变更；1=需要更新为最新保存角色数据；2=删除好友卡
					if needRefreshFriendCard == 1 {
						updateContentParam["friend_card_info"] = postContentItem.FriendCardInfo
					} else if needRefreshFriendCard == 2 {
						updateContentParam["friend_card_info"] = ""
					}
					if needRefreshGuildCard == 1 {
						updateContentParam["guild_id"] = postContentItem.GuildId
					} else if needRefreshGuildCard == 2 {
						updateContentParam["guild_id"] = ""
					}
					if len(updateContentParam) > 0 {
						contentErr := tx.Table(postContentTableName).Where("id = ?", postContentItem.Model.ID).Updates(updateContentParam).Error
						if contentErr != nil {
							return contentErr
						}
					}
				}
			}
		}
		return nil
	})
}

// 删除动态和动态内容
func DeletePost(postUUID string) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		err := tx.Table((&model.Post{}).TableName()).Where("post_uuid = ?", postUUID).Updates(map[string]interface{}{
			"deleted_on": time.Now().Unix(),
			"is_del":     1,
		}).Error
		if err != nil {
			return err
		}
		// 删除语言表中的数据
		for _, language := range constants.AllPostLanguages {
			pLanguageTable := (&model.PostLanguage{}).GetTableName(util.ZHTWLanguageToSql(language))
			err = tx.Table(pLanguageTable).Where("post_uuid = ?", postUUID).Updates(map[string]interface{}{
				"deleted_on": time.Now().Unix(),
				"is_del":     1,
			}).Error
			if err != nil {
				return err
			}
		}
		// 删除帖子统计表中的数据
		err = tx.Table((&model.PostStats{}).TableName()).Where("post_uuid = ?", postUUID).Updates(map[string]interface{}{
			"deleted_on": time.Now().Unix(),
			"is_del":     1,
		}).Error
		if err != nil {
			return err
		}
		// 删除内容表
		pContentTable, err := (&model.PostContent{}).GetPostContentSubMeterTable(postUUID)
		if err != nil {
			return err
		}
		return tx.Table(pContentTable).Where("post_uuid = ?", postUUID).Updates(map[string]interface{}{
			"deleted_on": time.Now().Unix(),
			"is_del":     1,
		}).Error
	})
}

func GetPostNoIgnoreDel(postUuid string) (*model.Post, error) {
	db := DB.SelectConnect("db_standalonesite")
	var post model.Post
	if postUuid != "" {
		db = db.Where("post_uuid = ?", postUuid)
	} else {
		return nil, gorm.ErrRecordNotFound
	}

	err := db.Table((&model.Post{}).TableName()).Unscoped().First(&post).Error
	if err != nil {
		return &post, err
	}

	return &post, nil
}

func GetPostNoIgnoreDels(postUuids []string) ([]*model.Post, error) {
	if len(postUuids) == 0 {
		return nil, nil
	}
	db := DB.SelectConnect("db_standalonesite")
	var posts []*model.Post
	db = db.Where("post_uuid in ?", postUuids)

	err := db.Table((&model.Post{}).TableName()).Unscoped().Find(&posts).Error
	if err != nil {
		return nil, err
	}
	return posts, nil
}

func GetPost(postUUID string) (*model.Post, error) {
	var post model.Post
	db := DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName())
	if postUUID != "" {
		db = db.Where("post_uuid = ? AND is_del = ?", postUUID, 0)
	} else {
		return nil, gorm.ErrRecordNotFound
	}

	err := db.First(&post).Error
	if err != nil {
		return &post, err
	}

	return &post, nil
}

func GetPostAllowDEL(id int64) (*model.Post, error) {
	var post model.Post
	db := DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName())
	if id > 0 {
		db = db.Where("id = ?", id)
	} else {
		return nil, gorm.ErrRecordNotFound
	}

	err := db.First(&post).Error
	if err != nil {
		return &post, err
	}

	return &post, nil
}

func GetPostList(conditions *PostConditions, offset, limit int) ([]*model.Post, error) {
	var posts []*model.Post
	// 数据量会很大，如果没有查询条件则直接返回
	if len(conditions.PostUuids) == 0 && limit == 0 {
		return posts, nil
	}
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName())
	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	if len(conditions.PostUuids) > 0 {
		db = db.Where("post_uuid in ?", conditions.PostUuids)
	}
	if conditions.IsIgnore {
		db = db.Unscoped()
	} else {
		db.Where("is_del = ?", 0)
	}

	if err = db.Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}

func GetPostListByUserIntlOpenid(otherUserId string, myOpenid string, limit, offset int) ([]*model.Post, error) {
	var posts []*model.Post
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName())
	if offset >= 0 && limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	err = db.Where("is_audit = 1 or (is_audit = 2 and user_id = ?)", myOpenid).Find(&posts).Error
	return posts, err
}

func FetchPost(conditions *PostConditions, limit int) ([]*model.Post, error) {
	var posts []*model.Post
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName())
	if limit > 0 {
		db = db.Limit(limit)
	}
	if len(conditions.PostUuids) > 0 {
		db = db.Where("post_uuid in ?", conditions.PostUuids)
	}
	if conditions.CompositeCondition != nil {
		db = db.Where(conditions.CompositeCondition.ToSQL())
	}
	if conditions.PostHideCondition != nil {
		db = db.Where(conditions.PostHideCondition.ToSQL())
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if len(conditions.Language) > 0 {
		db = db.Where("language in ?", conditions.Language)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if conditions.IsAudit > 0 {
		db = db.Where("is_audit = ?", conditions.IsAudit)
	}
	if conditions.PlateId > 0 {
		db = db.Where("plate_id = ?", conditions.PlateId)
	}
	if conditions.IsTop != "" {
		db = db.Where("is_top = ?", conditions.IsTop)
	}
	if conditions.LtCreatedOnMs > 0 {
		db = db.Where("created_on_ms < ?", conditions.LtCreatedOnMs)
	}
	if conditions.IsHide {
		db = db.Where("is_hide = ?", 0)
	}

	if err = db.Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}

func FetchPostByLanguage(conditions *PostConditions, limit int, language string) ([]*model.Post, error) {
	var posts []*model.Post
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.PostLanguage{}).GetTableName(language))
	if limit > 0 {
		db = db.Limit(limit)
	}
	if len(conditions.PostUuids) > 0 {
		db = db.Where("post_uuid in ?", conditions.PostUuids)
	}
	if conditions.CompositeCondition != nil {
		db = db.Where(conditions.CompositeCondition.ToSQL())
	}
	if conditions.IntlOpenid != "" {
		db = db.Where("intl_openid = ?", conditions.IntlOpenid)
	}
	if conditions.LtId > 0 {
		db = db.Where("id < ?", conditions.LtId)
	}
	if len(conditions.Language) > 0 {
		db = db.Where("language in ?", conditions.Language)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	if conditions.IsAudit > 0 {
		db = db.Where("is_audit = ?", conditions.IsAudit)
	}
	if conditions.PlateId > 0 {
		db = db.Where("plate_id = ?", conditions.PlateId)
	}
	if conditions.IsTop != "" {
		db = db.Where("is_top = ?", conditions.IsTop)
	}
	if conditions.LtCreatedOnMs > 0 {
		db = db.Where("created_on_ms < ?", conditions.LtCreatedOnMs)
	}

	if err = db.Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}

func PostCountBy(predicates model.Predicates) (count int64, err error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName())
	for query, args := range predicates {
		if query != "ORDER" {
			db = db.Where(query, args...)
		}
	}
	// 指定表名
	err = db.Where("is_del = ?", 0).Count(&count).Error
	return
}

func UpdatePost(post *model.Post) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName()).Where("id = ? AND is_del = ?", post.Model.ID, 0).Save(post).Error
}

func UpdatePostInfo(postUUID string, updateData map[string]interface{}) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName()).Where("post_uuid = ?", postUUID).Updates(updateData).Error
}

// 同步获取全量的数据
func SyncGetAllPost(limit int) ([]*model.Post, error) {
	var posts []*model.Post
	var err error
	db := DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName())
	if limit > 0 {
		db = db.Limit(limit)
	}
	if err = db.Unscoped().Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}

// 获取官方发布的任务，定时发布时间在当前时间之后的数据
func GetOfficialPostPublish() ([]string, error) {
	var postUuids []string
	err := DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName()).Where("is_audit = 2 and is_del = 0 and is_official = 1 and publish_on < ?", time.Now().Unix()).Pluck("post_uuid", &postUuids).Error
	if err != nil {
		return nil, err
	}
	return postUuids, nil
}

// 更新官方帖子审核上线
func UpdateOfficialPostAudit(postUUID []string, languageMap map[string][]string) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		err := tx.Table((&model.Post{}).TableName()).Where("post_uuid in ?", postUUID).Updates(map[string]interface{}{
			"modified_on": time.Now().Unix(),
			"is_audit":    1,
		}).Error
		if err != nil {
			return err
		}
		for language, postUuids := range languageMap {
			tableName := (&model.PostLanguage{}).GetTableName(language)
			err = tx.Table(tableName).Where("post_uuid in ? ", postUuids).Updates(map[string]interface{}{
				"modified_on": time.Now().Unix(),
				"is_audit":    1,
			}).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// 批量删除帖子
func DeletePostInfoAndLanguage(postUUID string, updateData map[string]interface{}, languages []string) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		// 主表必删
		pErr := tx.Table((&model.Post{}).TableName()).Where("post_uuid = ?", postUUID).Updates(updateData).Error
		if pErr != nil {
			return pErr
		}
		if len(languages) == 0 {
			// 如果不传指定的多语言，删除全部的语言
			languages = constants.AllPostLanguages
		}
		// 删除多语言表
		for _, language := range languages {
			// 不返回报错，只删除对应的数据
			tx.Table((&model.PostLanguage{}).GetTableName(language)).Where("post_uuid = ?", postUUID).Updates(updateData)
		}
		// 删除帖子审核表
		aErr := tx.Table((&model.PostAudit{}).TableName()).Where("post_uuid = ?", postUUID).Updates(map[string]interface{}{
			"is_del":     1,
			"deleted_on": time.Now().Unix(),
		}).Error
		if aErr != nil {
			return aErr
		}
		return nil
	})

}

// 查看帖子是否被删除
func GetNotDeletePostCount(postUuids []string) (int64, error) {
	var count int64
	err := DB.SelectConnect("db_standalonesite").Table((&model.Post{}).TableName()).Where("post_uuid in ? and is_del = 0", postUuids).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}
