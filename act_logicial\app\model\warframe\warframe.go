// Package warframe TODO
package warframe

import (
	"trpc.act.logicial/app/model"
)

// WarframeUserDetailModel TODO
type WarframeUserDetailModel struct {
	model.AppModel
}

// TableName .
func (WarframeUserDetailModel) TableName() string {
	return "warframe20241114_user_detail"
}

// WarframeUserDetail TODO
type WarframeUserDetail struct {
	WarframeUserDetailModel
	ID          uint32 `json:"id" column:"id"`
	UID         string `json:"uid" column:"uid"`
	AccountType int16  `json:"account_type" column:"account_type"`
	ShareStatus int8   `json:"share_status" column:"share_status"` // '分享状态0:未分享;1:已分享'
	PlayerType  int8   `json:"player_type" column:"player_type"`   // '玩家类型1:新玩家;2:老玩家'
}

// WarframeUserWishRecordModel TODO
type WarframeUserWishRecordModel struct {
	model.AppModel
}

// TableName .
func (WarframeUserWishRecordModel) TableName() string {
	return "warframe20241114_user_wish_record"
}

// WarframeUserWishRecord TODO
type WarframeUserWishRecord struct {
	WarframeUserWishRecordModel
	ID            uint32 `json:"id" column:"id"`
	UID           string `json:"uid" column:"uid"`
	AccountType   int16  `json:"account_type" column:"account_type"`
	Default       int16  `json:"default" column:"default"`
	ShareActivity int16  `json:"share_activity" column:"share_activity"`
	OldPlayer     int16  `json:"old_player" column:"old_player"`
}

// WarframeUserWishPoolModel TODO
type WarframeUserWishPoolModel struct {
	model.AppModel
}

// TableName .
func (WarframeUserWishPoolModel) TableName() string {
	return "warframe20241114_user_wish_pool"
}

// WarframeUserWishPool TODO
type WarframeUserWishPool struct {
	WarframeUserWishPoolModel
	ID           uint32 `json:"id" column:"id"`
	UID          string `json:"uid" column:"uid"`
	AccountType  int16  `json:"account_type" column:"account_type"`
	Level        int16  `json:"level" column:"level"`
	IsWin        int16  `json:"is_win" column:"is_win"`
	LevelDay     string `json:"level_day" column:"level_day"`
	SteamId      string `json:"steam_id" column:"steam_id"`
	SteamName    string `json:"steam_name" column:"steam_name"`
	IsAlreadyWin int16  `json:"is_already_win" column:"is_already_win"`
}
type WarframeUserPlayTimeForeverModel struct {
	model.AppModel
}

// TableName .
func (WarframeUserPlayTimeForeverModel) TableName() string {
	return "warframe20241114_user_play_time_forever"
}

// WarframeUserPlayTimeForever TODO
type WarframeUserPlayTimeForever struct {
	WarframeUserPlayTimeForeverModel
	ID              uint32 `json:"id" column:"id"`
	UID             string `json:"uid" column:"uid"`
	AccountType     int16  `json:"account_type" column:"account_type"`
	Day             string `json:"day" column:"day"`
	PlayTimeForever int64  `json:"play_time_forever" column:"play_time_forever"`
}
