package df_tmp

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"trpc.act.logicial/app/cache"
	"trpc.act.logicial/app/logic/df_tmp_common"
)

// GetGunActTimeProc
type GetGunActTimeProc struct {
}

// GetGunActTime 获取拼枪活动时间
func GetGunActTime(ctx context.Context, req *pb.GetGunActTimeReq) (*pb.GetGunActTimeRsp, error) {
	log.DebugContextf(ctx, "GetGunActTime enter, req: %v", req)
	rsp := &pb.GetGunActTimeRsp{}
	proc := &GetGunActTimeProc{}
	err := proc.Process(ctx, req, rsp)
	if nil != err {
		log.ErrorContextf(ctx, "GetGunActTime rsp error:%v", err)
		return nil, err
	}
	return rsp, nil
}
func (p *GetGunActTimeProc) Process(ctx context.Context,
	req *pb.GetGunActTimeReq, rsp *pb.GetGunActTimeRsp) error {
	// 校验活动时间
	err := df_tmp_common.CheckGunActivity(ctx, req.GameId, req.ActivityId)
	if nil != err {
		log.ErrorContextf(ctx, "CheckGunActivity error:%v", err)
		return err
	}
	key := fmt.Sprintf("%s_%s", req.GameId, req.ActivityId)
	act, _ := cache.GetGunActivityFromCache(ctx, key)
	if nil != act {
		rsp.StartTime = act.StartTimestamp
		rsp.EndTime = act.EndTimestamp
	}
	return nil
}
