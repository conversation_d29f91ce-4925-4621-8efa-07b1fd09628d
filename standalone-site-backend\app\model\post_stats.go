package model

type PostStats struct {
	*Model
	PostUUID        string  `gorm:"column:post_uuid" json:"post_uuid"`                        //动态唯一ID
	PowerNum        int64   `gorm:"column:power_num;default:1;" json:"power_num"`             //权重
	PowerNumFloat   float32 `gorm:"column:power_num_float;default:1;" json:"power_num_float"` //权重新字段
	CommentCount    int64   `gorm:"column:comment_count" json:"comment_count"`                //评论数
	CollectionCount int64   `gorm:"column:collection_count" json:"collection_count"`          //收藏数
	UpvoteMap       string  `gorm:"column:upvote_map" json:"upvote_map"`                      //点赞类型和总数，json类型
	UpvoteCount     int64   `gorm:"column:upvote_count" json:"upvote_count"`                  //点赞数
	BrowseCount     int64   `gorm:"column:browse_count" json:"browse_count"`                  //浏览数
	ForwardCount    int64   `gorm:"column:forward_count" json:"forward_count"`                //转发数
	DemotionNum     int64   `gorm:"column:demotion_num" json:"demotion_num"`                  // 降权值
}

// TableName sets the insert table name for this struct type
func (p *PostStats) TableName() string {
	return "p_post_stats"
}
