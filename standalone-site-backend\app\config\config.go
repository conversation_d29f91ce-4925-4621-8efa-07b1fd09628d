// Package config 配置
package config

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"log"
	"os"
	"strings"

	"github.com/fsnotify/fsnotify"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
)

// Conf 配置
var Conf = new(Config)

var MidasPrivateKey *rsa.PrivateKey

// Init 初始化
func Init() (*Config, error) {
	// 默认使用trpc_go.yaml 配置文件

	viper.AddConfigPath("./conf/config/")
	viper.SetConfigName("custom")
	viper.SetConfigType("yaml")

	// 读取环境变量的前缀为 ACT_COMMUNITY
	viper.AutomaticEnv()
	viper.SetEnvPrefix("ACT_COMMUNITY")

	// viper.get("default.app.name") 可读取 ACT_GATEWAY_DEFAULT_APP_NAME
	replacer := strings.NewReplacer(".", "_")
	viper.SetEnvKeyReplacer(replacer)

	// 加载配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	if err := viper.Unmarshal(Conf); err != nil {
		return nil, errors.Wrapf(err, "failed to load config")
	}

	// 开启配置热加载
	viper.WatchConfig()
	viper.OnConfigChange(func(in fsnotify.Event) {
		log.Println("config file changed:", in.Name)
		newConf := new(Config)
		if err := viper.Unmarshal(newConf); err != nil {
			log.Println("viper Unmarshal tmpConf failed, error:", err.Error())
		} else {
			Conf = newConf
			log.Println("viper Unmarshal tmpConf success, newConf:", *newConf)
		}
	})
	return Conf, nil
}

// GetConfig 获取配置
func GetConfig() *Config {
	return Conf
}

func InitMidasPrivateKey() error {
	exPath, _ := os.Getwd()
	file, err := os.Open(exPath + "/conf/midas/lip_midas_rsa_private_key_pkcs8.pem")
	if err != nil {
		return errors.Wrapf(err, "Failed to load lip_midas_rsa_private_key_pkcs8.pem")
	}
	defer file.Close()
	//读取文件的内容
	info, _ := file.Stat()
	buf := make([]byte, info.Size())
	file.Read(buf)
	//pem解码
	block, _ := pem.Decode(buf)
	//x509解码
	if block == nil {
		err = errors.New("Midas Private Key decode error")
		return err
	}
	// 3、解析DER编码的私钥，生成私钥对象
	pKCS8PrivateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		err = errors.Wrapf(err, "Failed to ParsePKCS8PrivateKey")
		return err
	}
	MidasPrivateKey = pKCS8PrivateKey.(*rsa.PrivateKey)
	return nil
}

func (s *ObjectStorageS) TempDirSlash() string {
	return strings.Trim(s.TempDir, " /") + "/"
}
