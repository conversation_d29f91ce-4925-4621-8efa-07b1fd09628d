package taristmp

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/trpc-go/trpc-go/log"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_taristmp"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/model/taris"
)

var asiaInstanceIDs = []string{"31310", "31330", "31350", "31360", "31370", "31340", "31380"}
var presentList = [][]string{
	{"Wand-20240813032721-P8317d59b21de"}, // 鱼人国王
	{"Wand-20240813033104-P0926cf7f51c2"}, // 战争古树
	{"Wand-20240813033231-P3514b369f5f5"}, // 合成博士
	{"Wand-20240813033344-P5d5a21ee93a7"}, // 森林双子
	{"Wand-20240813033503-P36bc708fd75c"}, // 大德鲁伊
	{"Wand-20240813033614-P3844a9e08191", "Wand-20240813033714-Pe3c6b35c5866", "Wand-20240813033806-P2fd8487b679a",
		"Wand-20240813033902-P50802632a8dd"}, // 蜘蛛女王
	{"Wand-20240813034009-P7826356d0708", "Wand-20240813034058-Pf95db2a01b1e", "Wand-20240813034148-P136bf89c8bb6",
		"Wand-20240813034241-P7932036f7e0b"}, // 噩兆巨龙
}
var speedAsiaTimeIntervals = []TimeInterval{
	{time.Date(2024, 8, 27, 0, 0, 0, 0, loc), time.Date(2024, 9, 3, 23, 59, 59, 59, loc)},
	{time.Date(2024, 9, 4, 0, 0, 0, 0, loc), time.Date(2024, 9, 10, 23, 59, 59, 59, loc)},
	{time.Date(2024, 9, 11, 0, 0, 0, 0, loc), time.Date(2024, 9, 17, 23, 59, 59, 59, loc)},
	{time.Date(2024, 9, 18, 0, 0, 0, 0, loc), time.Date(2024, 9, 24, 23, 59, 59, 59, loc)},
	{time.Date(2024, 9, 25, 0, 0, 0, 0, loc), time.Date(2024, 10, 1, 23, 59, 59, 59, loc)},
	{time.Date(2024, 10, 2, 0, 0, 0, 0, loc), time.Date(2024, 10, 8, 23, 59, 59, 59, loc)},
	{time.Date(2024, 10, 9, 0, 0, 0, 0, loc), time.Date(2024, 10, 15, 23, 59, 59, 59, loc)},
}

// GetAsiaSelf 获取用户的数据
func GetAsiaSelf(ctx context.Context, instanceID int32, roleInfo *gamePb.RoleInfo) (self *pb.SelfMsg, err error) {
	// 获取对应服务器 查询角色 在查询对应的数据
	self = &pb.SelfMsg{}
	condition := &taris.TarisRankTemp{
		RoleID:     roleInfo.RoleId,
		AreaID:     roleInfo.AreaId,
		ZoneID:     roleInfo.ZoneId,
		InstanceID: asiaInstanceIDs[instanceID-1],
	}

	var data taris.TarisRankTemp
	result := DB.DefaultConnect().Debug().WithContext(ctx).Table("taris_rank_temp_asia").
		Where(condition).Order("clear_level_at desc").First(&data)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 没有找到记录
			return self, err
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return nil, err
	}

	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("[taris] get self: [%+v]",
		data))

	self.ClearLevelTimestamp = data.ClearLevelAt
	self.RoleName = data.RoleName
	self.UnionName = data.UnionName
	self.Service = fmt.Sprintf("%v", data.ZoneID)
	self.Rank = data.Rank
	return
}

// GetAsiaTotal 获取排行榜总数量
func GetAsiaTotal(ctx context.Context, instanceID int32) (total int32, err error) {
	var count int64
	result := DB.DefaultConnect().Debug().WithContext(ctx).Table("taris_rank_temp_asia").
		Where("area_id = 11 and instance_id = ?", asiaInstanceIDs[instanceID-1]).
		Count(&count)
	if result.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	total = int32(count)
	return
}

// GetSpeedRunAsiaRankList 获取sea区域排行榜列表
func GetSpeedRunAsiaRankList(ctx context.Context, instanceID int32, pageSize int32, pageNum int32) (
	rankList []*pb.RankList, err error) {
	rankList = make([]*pb.RankList, 0)
	// 在库表里查询
	condition := &taris.TarisRankTemp{
		InstanceID: asiaInstanceIDs[instanceID-1],
		AreaID:     11,
	}
	var data []taris.TarisRankTemp
	result := DB.DefaultConnect().Debug().WithContext(ctx).Table("taris_rank_temp_asia").
		Where(condition).Order("`rank` asc").
		Limit(int(pageSize)).Offset((int(pageNum) - 1) * int(pageSize)).
		Find(&data)

	if result.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return
	}

	for _, item := range data {
		rankList = append(rankList, &pb.RankList{
			Rank:                int64(item.Rank),
			RoleName:            item.RoleName,
			UnionName:           item.UnionName,
			ClearLevelTimestamp: item.ClearLevelAt,
			Service:             fmt.Sprintf("%v", item.ZoneID),
		})
	}

	return
}

// UpdateSpeedRunAsiaData 更新竞速sea区域数据
func UpdateSpeedRunAsiaData(ctx context.Context, Fsource_id string, instanceId int32) (err error) {
	scheduleCtx := context.Background()
	var roleIDList = []*taris.TarisSignUpRoleTemp{}
	resultSQL := DB.DefaultConnect().Debug().WithContext(scheduleCtx).
		Where("Fsource_id = ?", Fsource_id).Table("taris_sign_up_role_asia").
		Find(&roleIDList)

	if resultSQL.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", resultSQL.Error)
		return
	}

	groups := SplitListLength(30, roleIDList)
	// 判断时间如果在时间范围内就更新数据
	timestamp := time.Now().UTC().Unix()

	maxGoroutines := 90
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	for instanceIndex, id := range asiaInstanceIDs {
		// 更新数据
		if (timestamp >= speedAsiaTimeIntervals[instanceIndex].StartDate.Unix() && timestamp <=
			speedAsiaTimeIntervals[instanceIndex].EndDate.Unix()) ||
			int32(instanceIndex) == instanceId-1 {
			for _, group := range groups {
				wg.Add(1)
				go func(list []string, id string) {
					semaphore <- struct{}{}
					defer func() {
						<-semaphore
						wg.Done()
					}()

					respData, err := GetRemoteSpeedData(ctx, list, id)
					if err != nil {
						log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf("[taris] GetClearInstanceData err:%v",
							err.Error()))
					}

					// respData.Data 格式需要转化
					for _, item := range respData.Data {
						if item.DtEventTime != "" {
							startTime := speedAsiaTimeIntervals[instanceIndex].StartDate
							startTime = startTime.AddDate(0, 0, -1)
							endTime := speedAsiaTimeIntervals[instanceIndex].EndDate
							endTime = endTime.AddDate(0, 0, -1)
							layout := "2006-01-02 15:04:05"
							// 使用time.Parse解析时间字符串
							parsedTime, err := time.Parse(layout, item.DtEventTime)
							if err != nil {
								fmt.Println("Error parsing time:", err)
								log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf("[taris] set DtEventTime error: %+v",
									item))
								return
							}
							if item.UsedTime != "" && parsedTime.After(startTime) && parsedTime.Before(endTime) {
								ClearLevelAt, err := strconv.ParseInt(item.UsedTime, 10, 64)
								if err != nil {
									log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf("[taris] set ClearLevelAt error: %v",
										item.UsedTime))
									ClearLevelAt = 0
								}
								ZoneID, err := strconv.ParseInt(item.IZoneAreaID, 10, 64)
								if err != nil {
									log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf("[taris] set ZoneID error: %v",
										item.IZoneAreaID))
									ZoneID = 0
								}
								AreaID := 11
								layout := "2006-01-02 15:04:05"
								t, _ := time.Parse(layout, item.DtEventTime)

								decodedBytes, err := base64.StdEncoding.DecodeString(item.VRoleName)
								if err != nil {
									log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf("[taris] base64 error: %v",
										item.VRoleName))
								}

								insertDataItem := &taris.TarisRankTemp{
									UnionName:    item.VGuildName,
									RoleID:       item.VRoleID,
									RoleName:     string(decodedBytes),
									DtEventTime:  t.Unix(),
									ClearLevelAt: ClearLevelAt,
									AreaID:       int64(AreaID),
									ZoneID:       ZoneID,
									InstanceID:   id,
									Now:          timestamp,
								}
								// 以人为维度判断是否有重复有重复就干掉
								var first *taris.TarisRankTemp

								resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table("taris_rank_temp_asia").
									Where("area_id = ? and zone_id = ? and role_id = ? and instance_id = ?", insertDataItem.AreaID,
										insertDataItem.ZoneID,
										insertDataItem.RoleID, id).
									First(&first)

								if resultSQL.Error != nil {
									if errors.Is(resultSQL.Error, gorm.ErrRecordNotFound) {
										// 没有找到记录
										// 需要插入
										resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table("taris_rank_temp_asia").
											Save(&insertDataItem)
										if resultSQL.Error != nil {
											err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
												"mysql error, \t [Error]:{%v} ", resultSQL.Error)
											log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf(
												"[taris] GetClearInstanceData err:%v",
												err.Error()))
										}
									} else {
										err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
											"mysql error, \t [Error]:{%v} ", resultSQL.Error)
										log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf(
											"[taris] GetClearInstanceData err:%v",
											err.Error()))
									}
								}
								if first.RoleID != "" {
									// 需要更新
									if first.ClearLevelAt > insertDataItem.ClearLevelAt {
										first.ClearLevelAt = insertDataItem.ClearLevelAt
										first.Now = insertDataItem.Now
										first.DtEventTime = insertDataItem.DtEventTime
										first.RoleName = insertDataItem.RoleName
										first.UnionName = insertDataItem.UnionName
									}
									resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table("taris_rank_temp_asia").
										Where("id = ?", first.ID).
										Save(&first)
									if resultSQL.Error != nil {
										err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
											"mysql error, \t [Error]:{%v} ", resultSQL.Error)
										log.WithFieldsContext(scheduleCtx, "log_type", "error").Infof(fmt.Sprintf(
											"[taris] GetClearInstanceData err:%v",
											err.Error()))
									}
								}

							}
						}
					}
					// 等1s防止超qps
					time.Sleep(time.Second * 1)
				}(group, id)
			}
			wg.Wait()
			// 更新排名
			UpdateSpeedRank(ctx, id)
		}
	}
	return
}

// UpdateSpeedRank 更新排名
func UpdateSpeedRank(ctx context.Context, instanceID string) (err error) {
	scheduleCtx := context.Background()

	var wg sync.WaitGroup
	maxGoroutines := 90
	semaphore := make(chan struct{}, maxGoroutines)

	var list []*taris.TarisRankTemp
	// 添加rank字段
	result := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table("taris_rank_temp_asia").
		Where("instance_id = ? and area_id = 11", instanceID).
		Order("clear_level_at asc, dteventtime asc, role_name asc").Find(&list)

	if result.Error != nil {

		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", result.Error)
		return err
	}

	for index, item := range list {
		wg.Add(1)
		go func(detail *taris.TarisRankTemp, num int32) {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] update data: [%v - %v - %v]",
				instanceID, detail.ID, num+1))
			// 更新数据
			result = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(
				"taris_rank_temp_asia").
				Where("id = ? and instance_id = ?", detail.ID, instanceID).
				Updates(&taris.TarisRankTemp{
					Rank: num + 1,
				})
			if result.Error != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql error, \t [Error]:{%v} ", result.Error)
				return
			}
		}(item, int32(index))
	}
	wg.Wait()
	return
}

// GetRemoteSpeedData 获取竞速排行榜数据通过url
func GetRemoteSpeedData(ctx context.Context, list []string, id string) (
	respData ClearInstanceDataInfo, err error,
) {
	scheduleCtx := context.Background()
	rankUrl := config.GetConfig().TarisGetClearInstanceUrl

	var keys []string
	for _, roleID := range list {
		key := fmt.Sprintf("%s|%s", id, roleID)
		keys = append(keys, key)
	}

	resultKey := strings.Join(keys, ",")

	header := SetCommHeader()

	data := httpclient.ClientOption{
		URL: rankUrl,
		// Timeout: 2 * time.Second,
		Header:     header,
		Type:       "POST",
		PostString: fmt.Sprintf("keys=%v", url.QueryEscape(resultKey)),
	}
	result := httpclient.RequestOne(scheduleCtx, data)

	if result.RequestError != nil {
		// 请求失败
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeHttp, code.PubgHttpError,
			"http error, \t [Error]:{%v} {%v} ", rankUrl, result)
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] GetClearInstanceData err:%v",
			err.Error()))
	}
	response := result.Result

	err = json.Unmarshal([]byte(response), &respData)

	return
}

// SplitListLength 根据长度拆分列表
func SplitListLength(length int, list []*taris.TarisSignUpRoleTemp) (
	groups [][]string,
) {
	for i := 0; i < len(list); i += length {
		end := i + length
		if end > len(list) {
			end = len(list)
		}
		// 将子切片转换为 []string 类型
		var group []string
		for _, role := range list[i:end] {
			group = append(group, role.RoleID)
		}
		groups = append(groups, group)
	}
	return
}

// IsSameDay 判断时间戳是否是同一天
func IsSameDay(ts1, ts2 int64) bool {
	t1 := time.Unix(ts1, 0)
	t2 := time.Unix(ts2, 0)

	return t1.Year() == t2.Year() &&
		t1.Month() == t2.Month() &&
		t1.Day() == t2.Day()
}

// AddAsiaSendMessage 添加发奖记录
func AddAsiaSendMessage(ctx context.Context, Fsource_id string, instanceId int32) (err error) {
	scheduleCtx := context.Background()
	timestamp := time.Now().UTC().Unix()

	maxGoroutines := 90
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	for instanceIndex, id := range asiaInstanceIDs {
		if (IsSameDay(timestamp, speedAsiaTimeIntervals[instanceIndex].EndDate.Unix())) ||
			int32(instanceIndex) == instanceId-1 {
			// 查询对应的前100
			var topList []*taris.TarisRankTemp
			resultSQL := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table("taris_rank_temp_asia").
				Where("area_id = 11 and instance_id = ?", id).Limit(100).Order("`rank` asc").Find(&topList)
			if resultSQL.Error != nil {
				err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"mysql error, \t [Error]:{%v} ", resultSQL.Error)
				return
			}
			// 添加发货记录
			for _, topItem := range topList {
				wg.Add(1)
				go func(id string, item *taris.TarisRankTemp) {
					semaphore <- struct{}{}
					defer func() {
						<-semaphore
						wg.Done()
					}()
					// 查询对应uid
					var signUpItem taris.TarisSignUpRoleTemp
					resultSQL = DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table("taris_sign_up_role_asia").
						Where("role_id = ? and Fsource_id = ?", item.RoleID, Fsource_id).First(&signUpItem)
					if resultSQL.Error != nil {
						if errors.Is(resultSQL.Error, gorm.ErrRecordNotFound) {
						} else {
							log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf("[taris] GetClearInstanceData err:%v",
								err.Error()))
							err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
								"mysql error, \t [Error]:{%v} ", resultSQL.Error)
						}
					}
					if signUpItem.UID != "" {
						// 添加发奖记录可能有多个礼包
						AddSendMessage(ctx, Fsource_id, item.Rank, instanceIndex, &signUpItem)
					}
				}(id, topItem)
			}
			wg.Wait()
		}
	}
	return
}

// AddSendMessage 在表里插入发奖记录
func AddSendMessage(ctx context.Context, Fsource_id string, rank int32, instanceIndex int,
	signUpItem *taris.TarisSignUpRoleTemp) (
	err error) {
	scheduleCtx := context.Background()
	sendData := &taris.TarisSendTemp{
		UID:       signUpItem.UID,
		Status:    0,
		FsourceID: Fsource_id,
		LangType:  signUpItem.LangType,
	}
	sendData.Tag = presentList[instanceIndex][0]
	if instanceIndex >= 5 {
		switch {
		case rank >= 1 && rank <= 10:
			sendData.Tag = presentList[instanceIndex][0]
		case rank >= 11 && rank <= 20:
			sendData.Tag = presentList[instanceIndex][1]
		case rank >= 21 && rank <= 30:
			sendData.Tag = presentList[instanceIndex][2]
		default:
			sendData.Tag = presentList[instanceIndex][3]
		}
	}
	resultSQL := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(taris.TarisSendTemp{}.TableName()).
		Where("uid = ? and tag = ? and Fsource_id = ?", sendData.UID, sendData.Tag, Fsource_id).
		FirstOrCreate(&sendData)
	if resultSQL.Error != nil {
		log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
			"[taris] GetClearInstanceData err:%v",
			err.Error()))
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"mysql error, \t [Error]:{%v} ", resultSQL.Error)
	}
	return
}
