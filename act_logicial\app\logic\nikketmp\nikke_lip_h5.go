package nikketmp

import (
	"context"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	dynamicsPb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.act.logicial/app/code"
)

// CheckUserPostByNIKKEH5Page .
func CheckUserPostByNIKKEH5Page(ctx context.Context) error {

	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}

	clientProxy := dynamicsPb.NewDynamicsClientProxy()
	postStatus, err := clientProxy.CheckIfUserHasPostedByNIKKEH5Page(ctx, &dynamicsPb.CheckIfUserHasPostedByNIKKEH5PageReq{
		IntlOpenid: userAccount.Uid,
	})
	if err != nil {
		return err
	}
	if !postStatus.GetStatus() {
		// 未发贴报错
		return errs.NewCustomError(ctx, code.PostingDataNotObtained, "Posting data not obtained")
	}
	return nil
}
