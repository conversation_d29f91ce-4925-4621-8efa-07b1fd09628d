// Package nikke nikke活动
package nikke

import "trpc.act.logicial/app/model"

// SendTempModel Model
type SendTempModel struct {
	model.AppModel
}

// TableName .
func (SendTempModel) TableName() string {
	return "nikke1025_send_temp"
}

// SendTemp 临时结构
type SendTemp struct {
	SendTempModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(32);column:uid;"`
	AccountType int32  `gorm:"type:smallint(6);column:account_type;0"`
	FsourceID   string `gorm:"type:varchar(255);column:Fsource_id;not null"`
	Tag         int32  `gorm:"type:tinyint(4);column:tag;not null"`
	LangType    string `gorm:"type:varchar(64);column:lang_type;"`
	Status      int    `gorm:"type:int(1);column:status;not null"`
}

// LipPointNikke2Point5Model TODO
type LipPointNikke2Point5Model struct {
	model.AppModel
}

// TableName .
func (LipPointNikke2Point5Model) TableName() string {
	return "lip_point_nikke_2point5"
}

// LipPointNikke2Point5 TODO
// SendTemp 临时结构
type LipPointNikke2Point5 struct {
	LipPointNikke2Point5Model
	ID    int64  `gorm:"type:int(11);column:id;primary_key"`
	UID   string `gorm:"type:varchar(32);column:uid;"`
	Point int32  `gorm:"type:int(11);column:point;"` // lip充值积分
}
