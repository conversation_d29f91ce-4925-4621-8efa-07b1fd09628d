// Package df_pve_speed TODO
package df_pve_speed

import (
	"context"
	"strconv"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_pve_speed"
	dfPveSpeed "trpc.act.logicial/app/logic/df_pve_speed"
)

// DfPveSpeedImpl TODO
type DfPveSpeedImpl struct {
	pb.UnimplementedDfPveSpeed
}

// SyncPveSpeedRank 同步pve排行榜
func (s *DfPveSpeedImpl) SyncPveSpeedRank(ctx context.Context, req *pb.SyncPveSpeedRankReq) (
	rsp *pb.SyncPveSpeedRankRsp, err error,
) {
	rsp = &pb.SyncPveSpeedRankRsp{}
	go dfPveSpeed.SyncPveSpeedData(ctx)
	return
}

// GetPveSpeedRank 获取pve排行榜
func (s *DfPveSpeedImpl) GetPveSpeedRank(ctx context.Context, req *pb.GetPveSpeedRankReq) (
	rsp *pb.GetPveSpeedRankRsp, err error,
) {
	rsp = &pb.GetPveSpeedRankRsp{}
	pageNum := req.PageNum
	if pageNum == 0 {
		pageNum = 1
	}
	playNum, err := dfPveSpeed.GetRedis(ctx, "df-pve-speed-total-num")
	if err != nil {
		return
	}
	if playNum == "" {
		rsp.TotalRankPlayerNum = 0
	} else {
		num, err := strconv.ParseInt(playNum, 10, 64)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", "GetPveSpeedRank_redis_playNum").Infof("playNum: %v, err: %v",
				playNum, err)
			return rsp, err
		}
		rsp.TotalRankPlayerNum = num
	}

	rankList, err := dfPveSpeed.GetPveSpeedRankRedis(ctx, pageNum)
	if err != nil {
		return
	}

	rsp.RankList = rankList

	_, err = metadata.GetUserAccount(ctx)
	if err != nil {
		rsp.SelfItem = &pb.PveRankItem{}
		err = nil
		return
	}

	selfItem, err := dfPveSpeed.GetSelfPveSpeedData(ctx)
	if err != nil {
		return
	}
	rsp.SelfItem = selfItem
	return
}

// GetSelfPveSpeedRankNum TODO
func (s *DfPveSpeedImpl) GetSelfPveSpeedRankNum(ctx context.Context, req *pb.GetSelfPveSpeedRankNumReq) (
	rsp *pb.GetSelfPveSpeedRankNumRsp, err error,
) {
	rsp = &pb.GetSelfPveSpeedRankNumRsp{}
	selfItem, err := dfPveSpeed.GetSelfPveSpeedData(ctx)
	if err != nil {
		return
	}
	rsp.Rank = selfItem.PlaytimeRank
	return
}
