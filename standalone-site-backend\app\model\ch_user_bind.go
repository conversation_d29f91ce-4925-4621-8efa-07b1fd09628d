package model

import "trpc.publishing_application.standalonesite/app/constants"

type ChUserBind struct {
	*Model
	IntlOpenid string `gorm:"column:intl_openid" json:"intl_openid"`
	ChUid      string `gorm:"column:ch_uid" json:"ch_uid"`             // 网红用户id
	IsAutoSync int    `gorm:"column:is_auto_sync" json:"is_auto_sync"` // 是否开启自动同步
	// IsFreezed      int    `gorm:"column:is_freezed" json:"is_freezed"`       // 是否冻结
	SyncLanguage   string                          `gorm:"column:sync_language" json:"sync_language"` // 自动同步的多语言
	GameId         string                          `gorm:"column:game_id" json:"game_id"`
	AreaId         string                          `gorm:"column:area_id" json:"area_id"`
	AbnormalStatus constants.ECreatorHubUserStatus `gorm:"column:abnormal_status" json:"abnormal_status"`
	Country        string                          `gorm:"column:country" json:"country"` // 网红作者的国家地区
}

func (a *ChUserBind) TableName() string {
	return "p_ch_user_bind"
}
