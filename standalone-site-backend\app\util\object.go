package util

import (
	"reflect"
	"strings"
)

func GetUpdateColumns(o interface{}, excludeFields ...string) []string {
	// excludeFields := []string{"id", "post_uuid"}
	val := reflect.ValueOf(o).Elem()
	typ := val.Type()

	excludeMap := make(map[string]struct{})
	for _, field := range excludeFields {
		excludeMap[field] = struct{}{}
	}

	var jsonFields []string
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		// 获取 JSON 标签
		jsonTag := field.Tag.Get("json")
		if jsonTag != "" {
			// 处理标签中可能的选项，例如 `json:"email,omitempty"`
			jsonFieldName := strings.Split(jsonTag, ",")[0]
			if _, ok := excludeMap[jsonFieldName]; !ok {
				jsonFields = append(jsonFields, jsonFieldName)
			}
		}
	}
	return jsonFields
}
