package formatted

import (
	pb_dynamic "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/model"
)

func ReturnCommentBubbleItems(commentBubbles []*model.CommentBubble) []*pb_dynamic.CommentBubbleItem {
	commentBubblesFormat := []*pb_dynamic.CommentBubbleItem{}
	for _, commentBubble := range commentBubbles {
		commentBubbleItem := &pb_dynamic.CommentBubbleItem{
			Id:            commentBubble.ID,
			CommentBubble: commentBubble.Icon,
			BgColor:       commentBubble.BgColor,
		}
		commentBubblesFormat = append(commentBubblesFormat, commentBubbleItem)
	}
	return commentBubblesFormat
}
