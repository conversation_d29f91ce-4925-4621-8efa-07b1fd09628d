package model

type UserCollection struct {
	*Model
	User         *UserContent `json:"-" gorm:"-"`
	IntlOpenid   string       `json:"intl_openid"`
	ToIntlOpenid string       `json:"to_intl_openid"`
	IsMutual     int          `json:"is_mutual"` // 是否是互相关注
}

type UserCollectionFormat struct {
	Id             int64         `json:"id"`
	User           *UserFormated `json:"fans_user"`
	IntlOpenid     string        `json:"intl_openid"`
	ToIntlOpenid   string        `json:"to_intl_openid"`
	IsMutualFollow bool          `json:"is_mutual_follow"`
	IsFollow       bool          `json:"is_follow"`
}

type UserCollectionFollowFormat struct {
	Id             int64         `json:"id"`
	User           *UserFormated `json:"follow_user"`
	IntlOpenid     string        `json:"intl_openid"`
	ToIntlOpenid   string        `json:"to_intl_openid"`
	IsMutualFollow bool          `json:"is_mutual_follow"`
	IsFollow       bool          `json:"is_follow"`
}

//func (p *UserCollection) FormatFans() *UserCollectionFormat {
//	if p.Model != nil {
//		return &UserCollectionFormat{
//			Id:           p.Model.ID,
//			User:         &UserFormated{},
//			IntlOpenid:   p.IntlOpenid,
//			ToIntlOpenid: p.ToIntlOpenid,
//			IsFollow:     false,
//			IsMutualFollow: p.IsMutual == 1,
//		}
//	}
//
//	return nil
//}

func (p *UserCollection) FormatFollow() *UserCollectionFormat {
	if p.Model != nil {
		return &UserCollectionFormat{
			Id:             p.Model.ID,
			User:           &UserFormated{},
			IntlOpenid:     p.IntlOpenid,
			ToIntlOpenid:   p.ToIntlOpenid,
			IsFollow:       false,
			IsMutualFollow: false,
		}
	}

	return nil
}

func (p *UserCollection) TableName() string {
	return "p_user_collection"
}
