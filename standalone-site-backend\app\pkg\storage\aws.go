package storage

import (
	"context"
	"io"
	"os"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"trpc.publishing_application.standalonesite/app/util"
)

/*
	参考文档：

https://aws.github.io/aws-sdk-go-v2/docs/configuring-sdk/
https://github.com/awsdocs/aws-doc-sdk-examples/blob/main/gov2/s3/hello/hello.go
https://github.com/awsdocs/aws-doc-sdk-examples/blob/main/gov2/s3/actions/bucket_basics.go
AWS的秘钥配置文件需要通过configmap容器挂载卷，挂载到/root/.aws/目录下
*/
var (
	_ ObjectStorageService = (*awsS3Servant)(nil)
	_ OssCreateService     = (*awsS3CreateServant)(nil)
	_ OssCreateService     = (*awsS3CreateTempDirServant)(nil)
)

type awsS3CreateServant struct {
	client     *s3.Client
	domain     string
	bucketName string
}

type awsS3CreateTempDirServant struct {
	client     *s3.Client
	domain     string
	bucketName string
	bucketUrl  string
	tempDir    string
}

type awsS3Servant struct {
	OssCreateService

	client     *s3.Client
	domain     string
	bucketName string
}

// DownloadFile implements ObjectStorageService.
func (s *awsS3Servant) DownloadFile(objectKey string) (error, *os.File) {
	result, err := s.client.GetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(objectKey),
	})
	if err != nil {
		return err, nil
	}
	defer result.Body.Close()
	return util.WriteFile(result.Body, "siteMessagePackage")
}

func (s *awsS3CreateServant) PutObject(objectKey string, reader io.Reader, objectSize int64, contentType string, _persistance bool) (string, error) {
	_, err := s.client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(objectKey),
		Body:        reader,
		ContentType: &contentType,
	})
	if err != nil {
		return "", err
	}
	return s.domain + objectKey, nil
}

func (s *awsS3CreateServant) PersistObject(_objectKey string) error {
	// empty
	return nil
}

func (s *awsS3CreateTempDirServant) PutObject(objectKey string, reader io.Reader, objectSize int64, contentType string, persistance bool) (string, error) {
	objectName := objectKey
	if !persistance {
		objectName = s.tempDir + objectKey
	}
	_, err := s.client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(objectName),
		Body:        reader,
		ContentType: &contentType,
	})
	if err != nil {
		return "", err
	}
	return s.domain + objectKey, nil
}

func (s *awsS3CreateTempDirServant) PersistObject(objectKey string) error {
	return nil
}

func (s *awsS3Servant) DeleteObject(objectKey string) error {
	var objectIds []types.ObjectIdentifier
	objectIds = append(objectIds, types.ObjectIdentifier{Key: aws.String(objectKey)})
	_, err := s.client.DeleteObjects(context.TODO(), &s3.DeleteObjectsInput{
		Bucket: aws.String(s.bucketName),
		Delete: &types.Delete{Objects: objectIds},
	})

	return err
}

func (s *awsS3Servant) DeleteObjects(objectKeys []string) error {
	var objectIds []types.ObjectIdentifier
	for _, key := range objectKeys {
		objectIds = append(objectIds, types.ObjectIdentifier{Key: aws.String(key)})
	}
	_, err := s.client.DeleteObjects(context.TODO(), &s3.DeleteObjectsInput{
		Bucket: aws.String(s.bucketName),
		Delete: &types.Delete{Objects: objectIds},
	})

	return err
}

func (s *awsS3Servant) IsObjectExist(objectKey string) (bool, error) {
	return false, nil
}

func (s *awsS3Servant) SignURL(objectKey string, expiredInSec int64) (string, error) {
	return "", nil
}

func (s *awsS3Servant) ObjectURL(objetKey string) string {
	return s.domain + objetKey
}

func (s *awsS3Servant) ObjectKey(objectUrl string) string {
	return strings.Replace(objectUrl, s.domain, "", -1)
}

func (s *awsS3Servant) Name() string {
	return "awsS3"
}
