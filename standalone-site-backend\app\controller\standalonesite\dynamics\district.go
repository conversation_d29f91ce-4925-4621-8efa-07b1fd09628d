package dynamics

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/logic/district"
)

// 获取金刚区配置
func (s *DynamicsImpl) GetDistrictList(c context.Context, req *pb.GetDistrictListReq) (rsp *pb.GetDistrictListRsp, err error) {
	// 默认写死分页类型和分页数
	req.PageType = 0
	req.Limit = 10

	language := metadata.GetLangType(c)
	if language == "" {
		language = "en"
	}

	list, err := district.GetDistrictList(c, req, language)
	if err != nil {
		return nil, err
	}
	return list, nil
}
