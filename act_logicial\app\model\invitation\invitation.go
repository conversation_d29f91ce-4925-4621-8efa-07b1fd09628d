// Package invitation 要求
package invitation

import (
	"trpc.act.logicial/app/model"
)

// Invitation invitation_log_$id表
type Invitation struct {
	model.AppModel
	ID                 int64  `gorm:"type:int(11);column:id;primary_key"`
	UID                string `gorm:"type:varchar(64);column:uid"` // 用户ID
	AccountType        int32  `gorm:"type:tinyint(4);column:account_type;0"`
	InviteeUid         string `gorm:"type:varchar(64);column:invitee_uid"` // 被邀请人ID
	InviteeAccountType int32  `gorm:"type:tinyint(4);column:invitee_account_type;0"`
	FsourceId          string `gorm:"type:varchar(255);column:Fsource_id;not null"` // 页面id
	Status             int    `gorm:"type:tinyint(4);column:status;0"`              // 0:log 1:success
	IsDelete           int    `gorm:"type:tinyint(1);column:is_delete;0"`           // 0:正常 1:删除
}

// ShortCode short_code_log_$id表
type ShortCode struct {
	model.AppModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(64);column:uid"`                  // 用户ID
	AccountType int32  `gorm:"type:tinyint(4);column:account_type;0"`        // 用户类型
	ShareCode   string `gorm:"type:varchar(64);column:share_code"`           // 邀请短码
	FsourceId   string `gorm:"type:varchar(255);column:Fsource_id;not null"` // 页面id
}

// ShareDrawLog 抽奖
type ShareDrawLog struct {
	model.AppModel
	ID        int64  `gorm:"type:int(11);column:id;primary_key"`
	UID       string `gorm:"type:varchar(64);column:uid"`                  // 用户ID
	Prize     string `gorm:"type:varchar(64);column:prize"`                // 奖品名
	FsourceId string `gorm:"type:varchar(255);column:Fsource_id;not null"` // 页面id
}
