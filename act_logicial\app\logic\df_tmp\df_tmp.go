// Package df_tmp TODO
package df_tmp

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	ams_pb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/ams"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	amsPb "git.code.oa.com/trpcprotocol/publishing_marketing/game_ams"
	gameAmsPb "git.code.oa.com/trpcprotocol/publishing_marketing/game_ams"
	amsPresentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present_ams"
	launcerPb "git.woa.com/trpcprotocol/publishing_marketing/account_launcher"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	redisOrigin "github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/global"
	model "trpc.act.logicial/app/model/df"
)

// GetCommonLibraryListParam TODO
type GetCommonLibraryListParam struct {
	PageSeq     int    `json:"page_seq"`
	PageSize    int    `json:"page_size"`
	LibraryName string `json:"library_name"`
}

// CommonLibraryListRsp TODO
type CommonLibraryListRsp struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	RequestId string `json:"request_id"`
	Data      struct {
		Errmsg   string `json:"errmsg"`
		DataList []struct {
			JsonData      string `json:"json_data"`
			LibraryDataId int    `json:"library_data_id"`
			LibraryName   string `json:"library_name"`
		} `json:"data_list"`
		IsFinish bool `json:"is_finish"`
		Result   int  `json:"result"`
	} `json:"data"`
}

// TwitchList TODO
type TwitchList struct {
	ID     string   `json:"id"`
	Name   string   `json:"name"`
	Score  string   `json:"score"`
	Avatar []string `json:"avatar"`
}

// LeaderboardData TODO
type LeaderboardData struct {
	HavocRank  string       `json:"havoc_rank"`
	HazardRank string       `json:"hazard_rank"`
	RankDate   int64        `json:"rank_date"`
	TwitchList []TwitchList `json:"twitch_list"`
}

// StreamerHotRank TODO
type StreamerHotRank struct {
	Id         string   `json:"id,omitempty"`          //  id
	Name       string   `json:"name,omitempty"`        // 名字
	Avatar     []string `json:"avatar,omitempty"`      // 头像
	Score      float64  `json:"score,omitempty"`       // 分数
	RankChange int      `json:"rank_change,omitempty"` // 排位变动
	Rank       int32    `json:"rank,omitempty"`        // 排位
}

// ByRankDateDesc TODO
type ByRankDateDesc []LeaderboardData

// Len 用于排序
func (a ByRankDateDesc) Len() int { return len(a) }

// Swap 用于排序
func (a ByRankDateDesc) Swap(i, j int) { a[i], a[j] = a[j], a[i] }

// Less 用于排序
func (a ByRankDateDesc) Less(i, j int) bool { return a[i].RankDate > a[j].RankDate }

// SortLeaderboardDataByRankDateDesc 根据RankDate倒序排序并返回排序后的切片
func SortLeaderboardDataByRankDateDesc(data []LeaderboardData) []LeaderboardData {
	sort.Sort(ByRankDateDesc(data))
	return data
}

// ByScoreDesc TODO
type ByScoreDesc []StreamerHotRank

// Len 用于排序
func (a ByScoreDesc) Len() int { return len(a) }

// Swap 用于排序
func (a ByScoreDesc) Swap(i, j int) { a[i], a[j] = a[j], a[i] }

// Less 用于排序
func (a ByScoreDesc) Less(i, j int) bool { return a[i].Score > a[j].Score }

// SortStreamerHotRankByScoreDesc 根据Score倒序排序并返回排序后的切片
func SortStreamerHotRankByScoreDesc(data []StreamerHotRank) []StreamerHotRank {
	sort.Sort(ByScoreDesc(data))
	return data
}

// SignRequestParams TODO
type SignRequestParams struct {
	URL         string
	Method      string
	Headers     http.Header
	Data        interface{}
	SignVersion string
	SignKey     string
	SignSecret  string
}

// UpdateStreamerHotRank 更新主播排行数据
func UpdateStreamerHotRank(ctx context.Context) error {
	// const libraryName = "Hazard Operation Rank"
	const libraryNameCacheName = "hazard_operation_rank"
	getConfig := config.GetConfig()
	hokCommonLibraryListUrl := getConfig.DFCommonLibraryListUrl
	libraryName := getConfig.HazardOperationRankName
	// 获取接口数据
	libraryListRsp, err := GetCommonLibraryListByName(ctx, libraryName, hokCommonLibraryListUrl)
	if err != nil {
		return err
	}
	return UpdateStreamerHotRankCache(ctx, libraryListRsp, libraryNameCacheName)
}

// UpdateHavocWarfareRank TODO
func UpdateHavocWarfareRank(ctx context.Context) error {
	// const libraryName = "Havoc Warfare Rank"
	const libraryNameCacheName = "havoc_warfare_rank"
	getConfig := config.GetConfig()
	hokCommonLibraryListUrl := getConfig.DFCommonLibraryListUrl
	libraryName := getConfig.HavocWarfareRankName
	// 获取接口数据
	libraryListRsp, err := GetCommonLibraryListByName(ctx, libraryName, hokCommonLibraryListUrl)
	if err != nil {
		return err
	}
	return UpdateStreamerHotRankCache(ctx, libraryListRsp, libraryNameCacheName)
}

// UpdateStreamerHotRankCache TODO
func UpdateStreamerHotRankCache(ctx context.Context, libraryListRsp CommonLibraryListRsp, cacheName string) error {
	// 解析json data数据
	var leaderboardDataList []LeaderboardData
	for _, v := range libraryListRsp.Data.DataList {
		var leaderboardData LeaderboardData
		if err := json.Unmarshal([]byte(v.JsonData), &leaderboardData); err != nil {
			log.WithFieldsContext(ctx, "log_type", "debug").
				Infof("UpdateStreamerHotRank Unmarshal err.v.JsonData:[%v],err:[%v]", v.JsonData, err)
			return err
		}
		leaderboardDataList = append(leaderboardDataList, leaderboardData)
	}
	// 排序取最近的两条数据
	sortLeaderboardDataByRankDateDesc := SortLeaderboardDataByRankDateDesc(leaderboardDataList)
	if len(sortLeaderboardDataByRankDateDesc) == 0 {
		return errs.NewCustomError(ctx, code.CommonDataError, "request data is empty")
	}
	var leaderboardData []LeaderboardData
	if len(sortLeaderboardDataByRankDateDesc) == 1 {
		leaderboardData = append(leaderboardData, sortLeaderboardDataByRankDateDesc[0])
	}
	if len(sortLeaderboardDataByRankDateDesc) >= 2 {
		leaderboardData = sortLeaderboardDataByRankDateDesc[:2]
	}
	var newStreamerHotRank []StreamerHotRank
	var oldStreamerHotRank []StreamerHotRank
	var args []*redisOrigin.Z
	newStreamerHotRankMap := make(map[string]StreamerHotRank)
	oldStreamerHotRankMap := make(map[string]StreamerHotRank)
	for idx, v := range leaderboardData {
		if idx == 0 {
			for _, userInfo := range v.TwitchList {
				userItem := StreamerHotRank{
					Id:     userInfo.ID,
					Name:   userInfo.Name,
					Avatar: userInfo.Avatar,
					Score:  cast.ToFloat64(userInfo.Score),
				}
				newStreamerHotRank = append(newStreamerHotRank, userItem)
				newStreamerHotRankMap[userItem.Id] = userItem
			}
		}
		if idx == 1 {
			for _, userInfo := range v.TwitchList {
				userItem := StreamerHotRank{
					Id:     userInfo.ID,
					Name:   userInfo.Name,
					Avatar: userInfo.Avatar,
					Score:  cast.ToFloat64(userInfo.Score),
				}
				oldStreamerHotRank = append(oldStreamerHotRank, userItem)
				oldStreamerHotRankMap[userItem.Id] = userItem
			}
		}
	}
	// 将最近两条的用户数据按照得分排序
	newStreamerHotRankDesc := SortStreamerHotRankByScoreDesc(newStreamerHotRank)
	for rank, v := range newStreamerHotRankDesc {
		// 排名只存储前250名的数据
		if rank >= 250 {
			break
		}
		hotRank := newStreamerHotRankMap[v.Id]
		hotRank.Rank = int32(rank)
		newStreamerHotRankMap[v.Id] = hotRank
		// 添加得分
		args = append(args, &redisOrigin.Z{
			Score:  v.Score,
			Member: v.Id,
		})
	}
	oldStreamerHotRankDesc := SortStreamerHotRankByScoreDesc(oldStreamerHotRank)
	for rank, v := range oldStreamerHotRankDesc {
		hotRank := oldStreamerHotRankMap[v.Id]
		hotRank.Rank = int32(rank)
		oldStreamerHotRankMap[v.Id] = hotRank
	}
	// 计算排名变动
	for uid, v := range newStreamerHotRankMap {
		if len(oldStreamerHotRankDesc) == 0 {
			// 没有可对比数据
			v.RankChange = int(pb.StreamerHotRankChange_NotChange)
			newStreamerHotRankMap[uid] = v
			continue
		}
		if oldValue, ok := oldStreamerHotRankMap[uid]; !ok {
			// 上次不在排行榜中
			v.RankChange = int(pb.StreamerHotRankChange_New)
			newStreamerHotRankMap[uid] = v
		} else {
			// 排名上升
			if v.Rank < oldValue.Rank {
				v.RankChange = int(pb.StreamerHotRankChange_Up)
				newStreamerHotRankMap[uid] = v
			}
			// 排名下降
			if v.Rank > oldValue.Rank {
				v.RankChange = int(pb.StreamerHotRankChange_Down)
				newStreamerHotRankMap[uid] = v
			}
		}
	}
	// 序列化map
	newStreamerHotRankCacheMap := make(map[string]string)
	for k, v := range newStreamerHotRankMap {
		bytes, _ := json.Marshal(v)
		newStreamerHotRankCacheMap[k] = string(bytes)
	}
	// 更新缓存
	hashCacheKey := global.GetRedisKey(fmt.Sprintf("hash_%s", cacheName))
	zSetCacheKey := global.GetRedisKey(fmt.Sprintf("zset_%s", cacheName))
	hazardRankCacheKey := global.GetRedisKey(fmt.Sprintf("hazard_rank_%s", cacheName))
	havocRankCacheKey := global.GetRedisKey(fmt.Sprintf("havoc_rank_%s", cacheName))

	// 缓存5分钟
	expiration := 5 * time.Minute

	// 先删除旧数据再新增
	pipeline := redis.GetClient().Pipeline()
	pipeline.Del(ctx, zSetCacheKey, hashCacheKey)
	pipeline.HMSet(ctx, hashCacheKey, newStreamerHotRankCacheMap)
	pipeline.ZAdd(ctx, zSetCacheKey, args...)
	pipeline.Expire(ctx, hashCacheKey, expiration)
	pipeline.Expire(ctx, zSetCacheKey, expiration)
	if _, err := pipeline.Exec(ctx); err != nil {
		return err
	}

	if err := redis.GetClient().SetEX(ctx, hazardRankCacheKey, leaderboardData[0].HazardRank, expiration).
		Err(); err != nil {
		return err
	}
	if err := redis.GetClient().SetEX(ctx, havocRankCacheKey, leaderboardData[0].HavocRank, expiration).Err(); err != nil {
		return err
	}
	return nil
}

// GetCommonLibraryListByName 根据library_name获取排行榜数据
func GetCommonLibraryListByName(ctx context.Context, libraryName, urlStr string) (CommonLibraryListRsp, error) {

	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	var commonLibraryListRsp CommonLibraryListRsp
	var appkey, appsecret string
	envName := trpc.GlobalConfig().Global.EnvName
	switch envName {
	case "dev":
		appkey, appsecret = "common-test", "8mQsXOBlLGh5Cae7rJz1"
	case "test":
		appkey, appsecret = "common-test", "8mQsXOBlLGh5Cae7rJz1"
	case "prod":
		appkey, appsecret = "community-common", "sXOBmQh5ClLGJz8ae7r1"
	default:
		return commonLibraryListRsp, errs.NewCustomError(ctx, code.CommonDataError, "envName err")
	}

	u, err := url.Parse(urlStr)
	if err != nil {
		return commonLibraryListRsp, err
	}
	pathname := u.Path
	lastPath := strings.Split(pathname, "/")[len(strings.Split(pathname, "/"))-1]
	mac := hmac.New(sha256.New, []byte(appsecret))
	mac.Write([]byte(lastPath + "\n" + appkey + "\n" + timestamp))
	sign := hex.EncodeToString(mac.Sum(nil))

	var param = GetCommonLibraryListParam{
		PageSeq:     1,
		PageSize:    100,
		LibraryName: libraryName,
	}
	bytes, _ := json.Marshal(param)
	optionOne := httpclient.ClientOption{
		URL: urlStr,
		Header: map[string]string{
			"Content-Type":     "application/json",
			"x-areaid":         "global",
			"x-language":       "en",
			"x-source":         "pc_web",
			"x-gameid":         "30029",
			"x-auth-version":   "v1.0.1",
			"X-AUTH-Sign":      sign,
			"X-AUTH-Appkey":    appkey,
			"X-AUTH-Timestamp": timestamp,
		},
		Type:       http.MethodPost,
		PostString: string(bytes),
	}
	result := httpclient.RequestOne(ctx, optionOne)
	if result.RequestError != nil {
		// 请求失败
		return commonLibraryListRsp, errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"http error, \t [Error]:{%v} ", urlStr)
	}
	response := result.Result

	if err = json.Unmarshal([]byte(response), &commonLibraryListRsp); err != nil {
		return commonLibraryListRsp, errs.NewCustomError(ctx, code.CommonParamJsonError, "json Unmarshal err")
	}
	return commonLibraryListRsp, nil
}

// GetZSetCount 获取zset中的数据总数
func GetZSetCount(ctx context.Context, key string) (int64, error) {
	count, err := redis.GetClient().ZCard(ctx, key).Result()
	if err != nil {
		return 0, err
	}
	return count, nil
}

// StreamerHotRankChange 定义枚举类型（假设）
type StreamerHotRankChange int32

// ByRank 实现sort.Interface接口
type ByRank []*pb.StreamerHotRank

// Len 用于排序
func (a ByRank) Len() int { return len(a) }

// Less 用于排序
func (a ByRank) Less(i, j int) bool { return a[i].Rank < a[j].Rank }

// Swap 用于排序
func (a ByRank) Swap(i, j int) { a[i], a[j] = a[j], a[i] }

// SortByRank 对StreamerHotRank切片根据Rank字段进行正序排序
func SortByRank(streamers []*pb.StreamerHotRank) []*pb.StreamerHotRank {
	sort.Sort(ByRank(streamers))
	return streamers
}

// GetStreamerHotRank TODO
func GetStreamerHotRank(ctx context.Context, req *pb.GetStreamerHotRankReq) (*pb.GetStreamerHotRankRsp, error) {

	var cacheName string
	var streamerHotRankList []*pb.StreamerHotRank
	newStreamerHotRankMap := make(map[string]StreamerHotRank)
	getConfig := config.GetConfig()
	hazardOperationRankName := getConfig.HazardOperationRankName
	havocWarfareRankName := getConfig.HavocWarfareRankName

	switch req.LibraryName {
	case hazardOperationRankName:
		cacheName = "hazard_operation_rank"
	case havocWarfareRankName:
		cacheName = "havoc_warfare_rank"
	default:
		return nil, errs.NewCustomError(ctx, code.CommonDataError, "not current LibraryName,LibraryName:[%v]",
			req.LibraryName)
	}
	// 获取缓存
	hashCacheKey := global.GetRedisKey(fmt.Sprintf("hash_%s", cacheName))
	zSetCacheKey := global.GetRedisKey(fmt.Sprintf("zset_%s", cacheName))
	hazardRankCacheKey := global.GetRedisKey(fmt.Sprintf("hazard_rank_%s", cacheName))
	havocRankCacheKey := global.GetRedisKey(fmt.Sprintf("havoc_rank_%s", cacheName))

	totalCount, err := GetZSetCount(ctx, zSetCacheKey)
	if err != nil {
		return nil, err
	}
	// 分页获取数据
	start := (req.PageSeq - 1) * req.PageSize
	end := start + req.PageSize - 1
	// 限制最大获取250条数据
	if totalCount > 250 {
		totalCount = 250
	}
	if end > 249 {
		end = 249
	}

	userIds, err := redis.GetClient().ZRevRange(ctx, zSetCacheKey, int64(start), int64(end)).Result()
	if err != nil && !errors.Is(err, redisOrigin.Nil) {
		return nil, err
	}
	rankCacheData, err := redis.GetClient().HGetAll(ctx, hashCacheKey).Result()
	if err != nil && !errors.Is(err, redisOrigin.Nil) {
		return nil, err
	}
	hazardRankData, err := redis.GetClient().Get(ctx, hazardRankCacheKey).Result()
	if err != nil && !errors.Is(err, redisOrigin.Nil) {
		return nil, err
	}
	havocRankData, err := redis.GetClient().Get(ctx, havocRankCacheKey).Result()
	if err != nil && !errors.Is(err, redisOrigin.Nil) {
		return nil, err
	}
	if errors.Is(err, redisOrigin.Nil) {
		return nil, errs.NewCustomError(ctx, code.CommonDataError, "not cache data")
	}
	for _, v := range rankCacheData {
		var streamerHotRank StreamerHotRank
		if err = json.Unmarshal([]byte(v), &streamerHotRank); err != nil {
			return nil, errs.NewCustomError(ctx, code.JsonParseError, "GetStreamerHotRank Unmarshal err,rankCacheData:[%v]", v)
		}
		newStreamerHotRankMap[streamerHotRank.Id] = streamerHotRank
	}
	for _, uid := range userIds {
		streamerHotRank, ok := newStreamerHotRankMap[uid]
		if !ok {
			return nil, errs.NewCustomError(ctx, code.CommonDataError,
				"newStreamerHotRankMap not current uid; UID:[%V], newStreamerHotRankMap[%V]", uid, newStreamerHotRankMap)
		}
		streamerHotRankList = append(streamerHotRankList, &pb.StreamerHotRank{
			Id:         streamerHotRank.Id,
			Name:       streamerHotRank.Name,
			Avatar:     streamerHotRank.Avatar,
			Score:      float32(streamerHotRank.Score),
			RankChange: pb.StreamerHotRankChange(streamerHotRank.RankChange),
			Rank:       streamerHotRank.Rank,
		})
	}
	// 避免同分问题，根据排名正序排序一次
	sortByRank := SortByRank(streamerHotRankList)
	return &pb.GetStreamerHotRankRsp{
		TotalNum:   int32(totalCount),
		List:       sortByRank,
		HazardRank: cast.ToInt32(hazardRankData),
		HavocRank:  cast.ToInt32(havocRankData),
	}, nil
}

// UASendCdkey 发送cdkey
func UASendCdkey(ctx context.Context, dayNum int32) (cdkey string, err error) {
	// 取日期
	redisKeyPrefix := global.GetPrefix()
	ipRedisKey := fmt.Sprintf("%v_df0824_ip", redisKeyPrefix)
	// 判断用户IP是否在列表中
	userIp := metadata.GetUserIP(ctx)

	memberResult, errR := redis.GetClient().SIsMember(ctx, ipRedisKey, userIp).Result()
	if errR != nil && errR != redisOrigin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errR.Error())
		return
	}
	if memberResult {
		// 查找这个IP发放的cdkey
		cdkey, err = GetSendKeyFromDB(ctx, userIp)
		return
	}
	date := time.Now().Format("0102")
	redisKey := fmt.Sprintf("%v_df0824_num_%v", redisKeyPrefix, date)
	// 判断redis key是否存在 预设key
	result, errR := redis.GetClient().Get(ctx, redisKey).Result()
	if errR != nil && errR != redisOrigin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errR.Error())
		return
	}
	if result == "" {
		hasNum, errD := GetTodayNumFromDB(ctx)
		if errD != nil {
			err = errD
			return
		}
		errR := redis.GetClient().Set(ctx, redisKey, hasNum, time.Hour*24+time.Second*10).Err()
		if errR != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
				errR.Error())
			return
		}
	} else {
		// 如果已达最大 不处理
		nowNum, _ := strconv.Atoi(result)
		if nowNum >= int(dayNum) {
			return
		}
	}

	incrVal, errR := redis.GetClient().Incr(ctx, redisKey).Result()
	if errR != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errR.Error())
		return
	}
	if incrVal > int64(dayNum) {
		return
	}
	cdkey, err = SendKeyFromDB(ctx, userIp)
	if err != nil {
		redis.GetClient().Decr(ctx, redisKey)
		return
	}
	redis.GetClient().SAdd(ctx, ipRedisKey, userIp)
	return
}

// GetTodayNumFromDB TODO
func GetTodayNumFromDB(ctx context.Context) (hasNum int64, err error) {
	//
	date := time.Now().Format("0102")
	dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Table(model.CdkeyTmp0824{}.TableName()).Where("send_date = ?",
		date).Count(&hasNum).Error
	if dbErr != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr)
		return
	}
	return
}

// SendKeyFromDB TODO
func SendKeyFromDB(ctx context.Context, ip string) (cdkey string, err error) {
	date := time.Now().Format("0102")
	rand.Seed(time.Now().UnixNano())
	num := uint32(rand.Int63n(int64(9000))) + 1000
	uniqueKey := fmt.Sprintf("%v%v", time.Now().UnixNano(), num)
	updates := map[string]interface{}{
		"status":     0,
		"unique_key": uniqueKey,
		"send_date":  date,
		"send_ip":    ip,
	}
	result := DB.DefaultConnect().WithContext(ctx).Debug().Table(model.CdkeyTmp0824{}.TableName()).Where("status = ?", -1).
		Limit(1).Updates(updates)
	if result.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", result.Error)
		return
	}
	if result.RowsAffected == 0 {
		cdkey = ""
		return
	}
	var cdkeyTmp model.CdkeyTmp0824
	if dbErr := DB.DefaultConnect().WithContext(ctx).Table(cdkeyTmp.TableName()).Where("status = ?", 0).
		Where("unique_key = ?", uniqueKey).First(&cdkeyTmp).Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			cdkey = ""
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error)
		return
	}
	cdkey = cdkeyTmp.Cdkey
	log.WithFieldsContext(ctx, "log_type", "df_send_key", "int_field_1", "1").Infof("df send key success, cdkey = %v",
		cdkey)
	return
}

// GetSendKeyFromDB TODO
func GetSendKeyFromDB(ctx context.Context, ip string) (cdkey string, err error) {
	var cdkeyTmp model.CdkeyTmp0824
	if dbErr := DB.DefaultConnect().WithContext(ctx).Table(cdkeyTmp.TableName()).Where("send_ip = ?", ip).First(&cdkeyTmp).
		Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			cdkey = ""
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error)
		return
	}
	cdkey = cdkeyTmp.Cdkey
	return
}

// RedeemCdkey TODO
func RedeemCdkey(ctx context.Context, userAccount *accountPb.UserAccount, cdkey string) (err error) {
	var cdkeyTmp model.CdkeyTmp0824
	// 查询这个人是否兑换过
	dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Table(model.CdkeyTmp0824{}.TableName()).Where("uid = ?",
		userAccount.Uid).First(&cdkeyTmp).Error
	if dbErr != nil {
		if !errors.Is(dbErr, gorm.ErrRecordNotFound) {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", dbErr.Error)
			return
		}
	} else {
		return
	}
	if cdkey == "" {
		err = errs.NewCustomError(ctx, code.DFCdkey0824KeyError, "Error")
		return
	}

	dbErr = DB.DefaultConnect().WithContext(ctx).Debug().Table(model.CdkeyTmp0824{}.TableName()).Where("cdkey = ?", cdkey).
		First(&cdkeyTmp).Error
	if dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			err = errs.NewCustomError(ctx, code.DFCdkey0824KeyError, "Err")
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error)
		return
	}

	if cdkeyTmp.Status == 1 && cdkeyTmp.UID == userAccount.Uid {
		return
	}
	if cdkeyTmp.Status != 0 {
		err = errs.NewCustomError(ctx, code.DFCdkey0824KeyError, "err")
		return
	}

	updates := map[string]interface{}{
		"status":       1,
		"uid":          userAccount.Uid,
		"account_type": 1,
	}
	result := DB.DefaultConnect().WithContext(ctx).Debug().Table(model.CdkeyTmp0824{}.TableName()).Where("cdkey = ?",
		cdkey).Where("status = ?", 0).Updates(updates)
	if result.Error != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", result.Error)
		return
	}
	if result.RowsAffected == 0 {
		err = errs.NewCustomError(ctx, code.DFCdkey0824KeyError, "error")
		return
	}
	log.WithFieldsContext(ctx, "log_type", "df_redeem_key", "int_field_1", "1").Infof("df redeem key success, cdkey = %v",
		cdkey)
	return
}

// UASendCdkeyScript TODO
func UASendCdkeyScript() {
	ctx := context.Background()
	var list []model.CdkeyTmp0824
	if dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Table(model.CdkeyTmp0824{}.TableName()).Where("status = ?",
		1).Find(&list).Error; dbErr != nil {
		errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", dbErr.Error)
		return
	}
	fmt.Println("---------------list---------------")
	fmt.Printf("%#v\n", list)
	for _, item := range list {
		uidList := strings.Split(item.UID, "-")
		if len(uidList) != 2 {
			continue
		}
		openId := uidList[1]
		accountData, _ := proto.Marshal(&accountPb.UserAccount{
			Uid:         item.UID,
			AccountType: accountPb.AccountType_INTL,
			IntlAccount: &accountPb.IntlAccount{
				OpenId:    openId,
				ChannelId: 131,
			},
		})
		fmt.Println("---------------accountData---------------")
		fmt.Printf("%#v\n", accountData)
		callopts := []client.Option{
			client.WithMetaData(metadata.UserAccount, accountData),
		}
		launcherProxy := launcerPb.NewLauncherClientProxy()
		_, errL := launcherProxy.UpdateUserGroupMember(ctx, &launcerPb.UpdateUserGroupMemberReq{
			LauncherGameId: "********",
			IsAdd:          true,
			GroupId:        "179",
		}, callopts...)
		if errL != nil {
			log.WithFieldsContext(ctx, "log_type", "df_send_key_laucher_error").Infof("err  = %v", errL)
		}

		amsProxy := amsPb.NewAmsClientProxy()
		_, errA := amsProxy.DFActiveGame(ctx, &amsPb.DFActiveGameReq{}, callopts...)
		if errA != nil {
			log.WithFieldsContext(ctx, "log_type", "df_send_key_ams_error").Infof("err  = %v", errA)
		}
	}
}

// GetRechargeRebateStatus TODO
func GetRechargeRebateStatus(ctx context.Context, req *pb.GetRechargeRebateStatusReq) (
	*pb.GetRechargeRebateStatusRsp, error) {
	rsp := &pb.GetRechargeRebateStatusRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_get_recharge_rebate_status").Infof("GetUserAccount err  = %v", err)
		return nil, err
	}
	userOpenid := userAccount.IntlAccount.OpenId
	// redis 读取缓存 TODO
	redisKey := fmt.Sprintf("df_recharge_rebate_status_%v", userOpenid)
	redisStr, err := redis.GetClient().Get(ctx, redisKey).Result()
	if err != nil || redisStr == "" {
		log.WithFieldsContext(ctx, "log_type", "df_get_recharge_rebate_status").Warnf("redis error, \t [Error]:{%v} ",
			err.Error)
	} else {
		// json反序列化
		if err := json.Unmarshal([]byte(redisStr), rsp); err != nil {
			log.WithFieldsContext(ctx, "log_type", "df_get_recharge_rebate_status").Warnf(
				"json unmarshal error, \t [Error]:{%v} ", err.Error)
		} else {
			return rsp, nil
		}
	}
	// db内查数据
	userMdsFlow := model.DfMdsFlow{}
	dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfMdsFlow{}.TableName()).Where("user_openid = ?",
		userOpenid).First(&userMdsFlow).Error
	if dbErr != nil {
		if !errors.Is(dbErr, gorm.ErrRecordNotFound) {
			log.WithFieldsContext(ctx, "log_type", "df_get_recharge_rebate_status").Infof("db error, \t [Error]:{%v} ",
				dbErr.Error)
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"df_get_recharge_rebate_status | find none ")
		} else {
			// 没有充值记录
			rsp.CoinAmmount = 0
			return rsp, nil
		}
	}
	rsp.CoinAmmount = userMdsFlow.GameCoinsAmount
	if userMdsFlow.HasReceived == 1 {
		rsp.HasReceived = true
	} else {
		rsp.HasReceived = false
	}
	// 写入缓存
	rspStr, err := json.Marshal(rsp)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_get_recharge_rebate_status").Warnf("json marshal error, \t [Error]:{%v} ",
			err.Error)
	} else {
		redis.GetClient().Set(ctx, redisKey, rspStr, 24*time.Hour)
	}
	return rsp, nil
}

// HasGameCoins 拥有未领取的游戏币
func HasGameCoins() {

}

// HasNotReceived 领取未领取游戏币
func HasNotReceived() {

}

// ReceiveRechargeRebateCoins 领取未领取游戏币
func ReceiveRechargeRebateCoins(ctx context.Context, req *pb.ReceiveRechargeRebateCoinsReq) (
	*pb.ReceiveRechargeRebateCoinsRsp, error) {
	rsp := &pb.ReceiveRechargeRebateCoinsRsp{}
	userMdsFlow := model.DfMdsFlow{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_receive_recharge_rebate_coins").Infof("GetUserAccount err  = %v", err)
		return nil, err
	}
	userOpenid := userAccount.IntlAccount.OpenId
	dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfMdsFlow{}.TableName()).Where("user_openid = ?",
		userOpenid).First(&userMdsFlow).Error
	if dbErr != nil {
		if !errors.Is(dbErr, gorm.ErrRecordNotFound) {
			log.WithFieldsContext(ctx, "log_type", "df_receive_recharge_rebate_coins").Infof("db error, \t [Error]:{%v} ",
				dbErr.Error)
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"df_receive_recharge_rebate_coins | find db error")
		} else {
			return rsp, errs.NewCustomError(ctx, code.DFHasNotGameCoin, "没有充值记录")
		}
	}
	// 已经领取，不能重复领取
	if userMdsFlow.HasReceived == 1 {
		return rsp, errs.NewCustomError(ctx, code.DFHasReceivedGameCoin, "已经领取过")
	}
	// 发放游戏币
	serial, err := sendGameCoins(ctx, userMdsFlow.GameCoinsAmount, req.AmsId, req.GroupId)
	if err != nil || serial == "" {
		log.WithFieldsContext(ctx, "log_type", "df_receive_recharge_rebate_coins", "int_field_1", fmt.Sprintf("%d",
			code.DFSendGameCoinFailed)).Infof("sendGameCoins err  = %v", err)
		return nil, errs.NewCustomError(ctx, code.DFSendGameCoinFailed, "发放游戏币失败")
	}
	// 更新数据库
	dbErr = DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfMdsFlow{}.TableName()).Where("id = ?",
		userMdsFlow.ID).Updates(map[string]interface{}{
		"has_received": 1,
		"sended_at":    time.Now().Unix(),
		"serial_id":    serial,
	}).Error
	if dbErr != nil {
		log.WithFieldsContext(ctx, "log_type", "df_receive_recharge_rebate_coins", "int_field_1", fmt.Sprintf("%d",
			code.DFUpdateSendStatusFailed)).Infof("update df send status failed, \t [Error]:{%v} ", dbErr.Error)
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, code.DFUpdateSendStatusFailed,
			"df_receive_recharge_rebate_coins | update db error")
	}
	redisKey := fmt.Sprintf("df_recharge_rebate_status_%v", userOpenid)
	redis.GetClient().Del(ctx, redisKey)
	return rsp, nil
}

// sendGameCoins 发送游戏币
func sendGameCoins(ctx context.Context, count int32, amsId string, groupId string) (serial string, err error) {
	if count <= 0 {
		log.WithFieldsContext(ctx, "log_type", "df_send_game_coins").Infof("count <= 0")
		return "", nil
	}
	if amsId == "" || groupId == "" {
		log.WithFieldsContext(ctx, "log_type", "df_send_game_coins").Infof("amsId or groupId is empty")
		return "", nil
	}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_send_game_coins").Infof("GetUserAccount err  = %v", err)
		return serial, err
	}
	language := metadata.GetLangType(ctx)
	// 生成流水号
	serialId := ams_pb.CreateAmsSerial("projectd_oversea", "IEG-AMS-4000031")
	amsPresentProxy := amsPresentPb.NewAmsClientProxy()
	sendRsp, err := amsPresentProxy.SendOpenidAmsPresent(ctx, &amsPresentPb.SendOpenidAmsPresentReq{
		AmsId:      amsId,   // 老许提供 (平台配置)
		GroupId:    groupId, // 老许提供 (平台配置)
		PackageNum: count,
		Openid:     userAccount.IntlAccount.OpenId, // meta内取数据
		Serial:     serialId,                       // 计算
		LangType:   language,                       // meta内取数据
		RoleInfo: &gamePb.RoleInfo{
			GameId:   "29158",
			GameName: "projectd_oversea",
			AreaId:   66, // 待定
		},
	})
	log.WithFieldsContext(ctx, "log_type", "df_send_game_coins").Infof(
		"amsPresentProxy.SendOpenidAmsPresent amsid: %s, groupId: %s, count: %d, openid: %s, serial: %s, langType: %s, sendRsp = %v", amsId, groupId, count, userAccount.IntlAccount.OpenId, serialId, language, sendRsp)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_send_game_coins").Infof("amsPresentProxy.SendOpenidAmsPresent err  = %v",
			err)
		return serial, err
	}
	serial = sendRsp.Serial
	return serial, nil
}

// OneKeyMultiRedeemRecord 一码多用分支，给一个码添加兑换记录
func OneKeyMultiRedeemRecord(ctx context.Context, req *pb.OneKeyMultiRedeemRecordReq) (
	*pb.OneKeyMultiRedeemRecordRsp, error) {

	rsp := &pb.OneKeyMultiRedeemRecordRsp{}

	// userAllowRedeemNumber := 1	//单用户允许兑换的次数
	// cdkeyAllowRedeemNumber := 5	//单cdkey允许兑换的次数
	// // cdkeyAllowRedeemNumber := 10000	//单cdkey允许兑换的次数

	// 获取一些基本参数
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_get_recharge_rebate_status").Infof("GetUserAccount err  = %v", err)
		return nil, err
	}
	userOpenid := userAccount.IntlAccount.OpenId
	redisKeyPrefix := global.GetPrefix()
	cdkey := req.Cdkey
	gameid := req.GameId
	userAllowRedeemNumber := req.UserAllowRedeemNumber   // 单用户允许兑换的次数
	cdkeyAllowRedeemNumber := req.CdkeyAllowRedeemNumber // 单cdkey允许兑换的次数

	userRecordKey := fmt.Sprintf("%v_cdkey_user_redeem_record_%v", redisKeyPrefix, gameid)
	cdkeyRecordKey := fmt.Sprintf("%v_cdkey_all_redeem_record_%v", redisKeyPrefix, gameid)

	// 主动回滚
	if req.IsRollBack {
		// 礼包发送失败，回滚数量
		res1, errs1 := redis.GetClient().HIncrBy(ctx, userRecordKey, userOpenid, -1).Result()
		log.WithFieldsContext(ctx, "log_type", "df_one_key_multi_redeem_rollback_after_present").
			Infof("userOpenid:%v, after rollback, redeem count:%v", userOpenid, int(res1))

		res1, errs1 = redis.GetClient().HIncrBy(ctx, cdkeyRecordKey, cdkey, -1).Result()
		log.WithFieldsContext(ctx, "log_type", "df_one_key_multi_redeem_rollback_after_present").
			Infof("cdkey:%v, after rollback, redeem count:%v", cdkey, int(res1))
		if errs1 != nil && errs1 != redisOrigin.Nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
				errs1.Error())
			return nil, err
		}
		return rsp, nil
	}

	// 判断单用户限量
	res, errsR := redis.GetClient().HIncrBy(ctx, userRecordKey, userOpenid, 1).Result()
	if errsR != nil && errsR != redisOrigin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errsR.Error())
		return nil, err
	}
	log.WithFieldsContext(ctx, "log_type", "df_one_key_multi_redeem").Infof("userOpenid:%v want redeem :%v", userOpenid,
		cdkey)

	useRedeemCount := int32(res)
	log.WithFieldsContext(ctx, "log_type", "df_one_key_multi_redeem").Infof("userOpenid:%v , redeem count:%v", userOpenid,
		useRedeemCount)
	if useRedeemCount > userAllowRedeemNumber {
		// 用户已经兑换过
		// 重新扣掉数量
		res, errsR = redis.GetClient().HIncrBy(ctx, userRecordKey, userOpenid, -1).Result()
		log.WithFieldsContext(ctx, "log_type", "df_one_key_multi_redeem_rollback").
			Infof("userOpenid:%v  , after rollback,redeem count:%v", userOpenid, int(res))
		err = errs.NewCustomError(ctx, code.OneKeyMultiRedeemUserCountExcess, "error")
		return nil, err
	}

	// 判断单CDKEY限量
	res, errsR = redis.GetClient().HIncrBy(ctx, cdkeyRecordKey, cdkey, 1).Result()
	if errsR != nil && errsR != redisOrigin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			errsR.Error())
		return nil, err
	}
	cdkeyRedeemCount := int32(res)
	log.WithFieldsContext(ctx, "log_type", "df_one_key_multi_redeem").Infof("cdkey:%v , redeem count:%v", cdkey,
		cdkeyRedeemCount)
	if cdkeyRedeemCount > cdkeyAllowRedeemNumber {
		// 超出限量

		// 重新扣掉数量
		res, errsR = redis.GetClient().HIncrBy(ctx, userRecordKey, userOpenid, -1).Result()
		log.WithFieldsContext(ctx, "log_type", "df_one_key_multi_redeem_rollback").
			Infof("userOpenid:%v  , after rollback,redeem count:%v", userOpenid, int(res))
		res, errsR = redis.GetClient().HIncrBy(ctx, cdkeyRecordKey, cdkey, -1).Result()
		log.WithFieldsContext(ctx, "log_type", "df_one_key_multi_redeem_rollback").
			Infof("cdkey:%v , after rollback, redeem count:%v", cdkey, int(res))
		err = errs.NewCustomError(ctx, code.OneKeyMultiRedeemCdkeyCountExcess, "error")
		return nil, err
	}

	return rsp, nil
}

// GetCBTRechargeRebateStatus TODO
func GetCBTRechargeRebateStatus(ctx context.Context, req *pb.GetCBTRechargeRebateStatusReq) (
	*pb.GetCBTRechargeRebateStatusRsp, error) {
	rsp := &pb.GetCBTRechargeRebateStatusRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_cbt_get_recharge_rebate_status").Infof("GetUserAccount err  = %v", err)
		return nil, err
	}
	userOpenid := userAccount.IntlAccount.OpenId
	redisKey := fmt.Sprintf("df_cbt_charge_rebate_status_%s", userOpenid)
	redisStr, err := redis.GetClient().Get(ctx, redisKey).Result()
	if err != nil || redisStr == "" {
		log.WithFieldsContext(ctx, "log_type", "df_cbt_get_recharge_rebate_status").Warnf("redis error, \t [Error]:{%v} ",
			err.Error)
	} else {
		if err := json.Unmarshal([]byte(redisStr), rsp); err != nil {
			log.WithFieldsContext(ctx, "log_type", "df_cbt_get_recharge_rebate_status").
				Warnf("json unmarshal error, \t [Error]:{%v} ", err.Error)
		} else {
			return rsp, nil
		}
	}
	// 查询db数据
	userCBTMdsFlow := model.DfCBTMdsFlow{}
	dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("user_openid = ?",
		userOpenid).First(&userCBTMdsFlow).Error
	if dbErr != nil {
		if !errors.Is(dbErr, gorm.ErrRecordNotFound) {
			log.WithFieldsContext(ctx, "log_type", "df_cbt_get_recharge_rebate_status").Infof("db error, \t [Error]:{%v} ",
				dbErr.Error)
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"df_cbt_get_recharge_rebate_status | find none ")
		} else {
			// 没有充值记录
			rsp.CoinAmmount = 0
			return rsp, nil
		}
	}
	rsp.CoinAmmount = userCBTMdsFlow.GameCoinsAmount
	if userCBTMdsFlow.HasReceived == 1 {
		rsp.HasReceived = true
	} else {
		rsp.HasReceived = false
	}
	// 写入缓存
	rspStr, err := json.Marshal(rsp)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_cbt_get_recharge_rebate_status").
			Warnf("json marshal error, \t [Error]:{%v} ", err.Error)
	} else {
		redis.GetClient().Set(ctx, redisKey, rspStr, 2*time.Minute)
	}
	return rsp, nil
}

// ReceiveCBTRechargeRebateCoins TODO
func ReceiveCBTRechargeRebateCoins(ctx context.Context, req *pb.ReceiveCBTRechargeRebateCoinsReq) (
	*pb.ReceiveCBTRechargeRebateCoinsRsp, error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_receive_recharge_rebate_coins").Infof("GetUserAccount err  = %v", err)
		return nil, err
	}
	userOpenid := userAccount.IntlAccount.OpenId
	// userOpenid := "17454255337817015143"
	GreenGunThreshold := int32(1000)
	PurplePendantThreshold := int32(5000)
	BlueGunThreshold := int32(15000)

	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1", userOpenid).
		Infof("receive req:%v", req)
	rsp := &pb.ReceiveCBTRechargeRebateCoinsRsp{}
	// db查询改openid用户
	userCBTMdsFlow, err := getCBTDFRecord(ctx, userOpenid)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1", userOpenid).
			Errorf("getCBTDFRecord err:%v", err)
		return rsp, err
	}
	if userCBTMdsFlow.HasReceived == 1 {
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1", userOpenid).
			Infof("has received")
		return rsp, errs.NewCustomError(ctx, code.DFHasReceivedGameCoin, "have received")
	}
	// 未发送三角币
	notSendCoins := (userCBTMdsFlow.SendedAt == 0 && userCBTMdsFlow.GameCoinsAmount > 0)
	notSendGreenGun := (userCBTMdsFlow.GreenGunSendedAt == 0 && userCBTMdsFlow.GameCoinsAmount >= GreenGunThreshold)
	notSendPurplePendant := (userCBTMdsFlow.PurplePandentSendedAt == 0 && userCBTMdsFlow.GameCoinsAmount >=
		PurplePendantThreshold)
	notSendBlueGun := (userCBTMdsFlow.BlueGunSendedAt == 0 && userCBTMdsFlow.GameCoinsAmount >= BlueGunThreshold)
	//
	if notSendCoins {
		// 发送三角币
		err := sendCBTDFCoins(ctx, userCBTMdsFlow, req.AmsId, req.CoinsGroupId)
		if err != nil {
			return rsp, err
		}
	}
	if notSendGreenGun {
		// 发送绿枪
		err := sendCBTDFGreenGuns(ctx, userCBTMdsFlow, req.AmsId, req.GreenGunGroupId)
		if err != nil {
			return rsp, err
		}
	}
	if notSendPurplePendant {
		// 发送紫色挂件
		err := sendCBTDFPurplePendants(ctx, userCBTMdsFlow, req.AmsId, req.PurplePendantGroupId)
		if err != nil {
			return rsp, err
		}
	}
	if notSendBlueGun {
		// 发送蓝枪
		err := sendCBTDFBlueGuns(ctx, userCBTMdsFlow, req.AmsId, req.BlueGunGroupId)
		if err != nil {
			return rsp, err
		}
	}
	// 更新已完成db
	userCBTMdsFlow, err = getCBTDFRecord(ctx, userOpenid)
	if err != nil {
		return rsp, err
	}
	notSendCoins = (userCBTMdsFlow.SendedAt == 0 && userCBTMdsFlow.GameCoinsAmount > 0)
	notSendGreenGun = (userCBTMdsFlow.SendedAt == 0 && userCBTMdsFlow.GameCoinsAmount >= GreenGunThreshold)
	notSendPurplePendant = (userCBTMdsFlow.SendedAt == 0 && userCBTMdsFlow.GameCoinsAmount >= PurplePendantThreshold)
	notSendBlueGun = (userCBTMdsFlow.SendedAt == 0 && userCBTMdsFlow.GameCoinsAmount >= BlueGunThreshold)
	if !notSendCoins && !notSendGreenGun && !notSendPurplePendant && !notSendBlueGun {
		// 更新已发送状态
		updates := map[string]interface{}{
			"has_received": 1,
		}
		err := DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("id = ?",
			userCBTMdsFlow.ID).Updates(updates).Error
		if err != nil {
			// 写入db错误，腾讯云监控告警
			log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1",
				"update send info failed").Errorf("updates err, \t [Error]:{%v} ", err.Error)
			return rsp, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"df_cbt_receive_recharge_rebate_coins | update none ")
		}
	}
	// 清除缓存
	redisKey := fmt.Sprintf("df_cbt_charge_rebate_status_%s", userOpenid)
	err = redis.GetClient().Del(ctx, redisKey).Err()
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").
			Infof("del redis key failed, \t [Error]:{%v} ", err.Error)
	}
	return rsp, nil
}

func getCBTDFRecord(ctx context.Context, userOpenid string) (*model.DfCBTMdsFlow, error) {
	userCBTMdsFlow := model.DfCBTMdsFlow{}
	dbErr := DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("user_openid = ?",
		userOpenid).First(&userCBTMdsFlow).Error
	if dbErr != nil {
		if !errors.Is(dbErr, gorm.ErrRecordNotFound) {
			log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").Infof("db error, \t [Error]:{%v} ",
				dbErr.Error)
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"df_cbt_receive_recharge_rebate_coins | find none ")
		} else {
			return nil, errs.NewCustomError(ctx, code.DFHasNotGameCoin,
				"df_cbt_receive_recharge_rebate_coins | find none ")
		}
	}
	return &userCBTMdsFlow, nil
}

// sendCBTDFCoins 发送三角币
func sendCBTDFCoins(ctx context.Context, userInfos *model.DfCBTMdsFlow, amsid string, groupId string) error {
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").Infof("sendCBTDFCoins req:%v",
		userInfos)
	if userInfos.SendedAt != 0 {
		return nil
	}
	serialId, err := sendCBTDFPresents(ctx, userInfos.GameCoinsAmount, amsid, groupId, userInfos.UserOpenid,
		userInfos.SerialId)
	if err != nil {
		updates := map[string]interface{}{
			"serial_id": serialId,
		}
		err = DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("id = ?",
			userInfos.ID).Updates(updates).Error
		if err != nil {
			// 写入db错误，腾讯云监控告警
			log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1",
				"update send info failed").Errorf("updates err, \t [Error]:{%v} ", err.Error)
			return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "update serial failed")
		}
		return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "send present failed")
	}
	// 记录db
	updates := map[string]interface{}{
		"sended_at": time.Now().Unix(),
		"serial_id": serialId,
	}
	// 更新发送时间db和流水号
	err = DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("id = ?",
		userInfos.ID).Updates(updates).Error
	if err != nil {
		// 写入db错误，腾讯云监控告警
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1",
			"update send info failed").Errorf("updates err, \t [Error]:{%v} ", err.Error)
		return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "update send info failed")
	}
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").
		Infof("sendCBTDFPresents success serialId: %s", serialId)
	return nil
}

// sendCBTDFGreenGuns 发送绿枪
func sendCBTDFGreenGuns(ctx context.Context, userInfos *model.DfCBTMdsFlow, amsid string, groupId string) error {
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").
		Infof("sendCBTDFGreenGuns req:%v, amsid: %s, groupId: %s", userInfos, amsid, groupId)
	serialId, err := sendCBTDFPresents(ctx, 1, amsid, groupId, userInfos.UserOpenid, userInfos.GreenGunSerialId)
	if err != nil {
		updates := map[string]interface{}{
			"green_gun_serial_id": serialId,
		}
		err = DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("id = ?",
			userInfos.ID).Updates(updates).Error
		if err != nil {
			// 写入db错误，腾讯云监控告警
			log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1",
				"update send info failed").Errorf("updates err, \t [Error]:{%v} ", err.Error)
			return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "update serial failed")
		}
		return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "send present failed")
	}
	// 记录db
	updates := map[string]interface{}{
		"green_gun_sended_at": time.Now().Unix(),
		"green_gun_serial_id": serialId,
	}
	// 更新发送时间db和流水号
	err = DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("id = ?",
		userInfos.ID).Updates(updates).Error
	if err != nil {
		// 写入db错误，腾讯云监控告警
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1",
			"update send info failed").Errorf("updates err, \t [Error]:{%v} ", err.Error)
		return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "update send info failed")
	}
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").
		Infof("sendCBTDFGreenGuns success serialId: %s", serialId)
	return nil
}

func sendCBTDFPurplePendants(ctx context.Context, userInfos *model.DfCBTMdsFlow, amsid string, groupId string) error {
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").
		Infof("sendCBTDFPurplePendants req:%v, amsid: %s, groupId: %s", userInfos, amsid, groupId)
	serialId, err := sendCBTDFPresents(ctx, 1, amsid, groupId, userInfos.UserOpenid, userInfos.PurplePandentSerialId)
	if err != nil {
		updates := map[string]interface{}{
			"purple_pendant_serial_id": serialId,
		}
		err = DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("id = ?",
			userInfos.ID).Updates(updates).Error
		if err != nil {
			// 写入db错误，腾讯云监控告警
			log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1",
				"update send info failed").Errorf("updates err sendCBTDFPurplePendants update serial, \t [Error]:{%v} ", err.Error)
			return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "update serial failed")
		}
		return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "send present failed")
	}
	// 记录db
	updates := map[string]interface{}{
		"purple_pandent_sended_at": time.Now().Unix(),
		"purple_pandent_serial_id": serialId,
	}
	// 更新发送时间db和流水号
	err = DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("id = ?",
		userInfos.ID).Updates(updates).Error
	if err != nil {
		// 写入db错误，腾讯云监控告警
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1",
			"update send info failed").Errorf("updates err, \t [Error]:{%v} ", err.Error)
		return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "update send info failed")
	}
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").
		Infof("sendCBTDFPurplePendants success serialId: %s", serialId)
	return nil
}

func sendCBTDFBlueGuns(ctx context.Context, userInfos *model.DfCBTMdsFlow, amsid string, groupId string) error {
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").
		Infof("sendCBTDFBlueGuns req:%v , amsid: %s, groupId: %s", userInfos, amsid, groupId)
	serialId, err := sendCBTDFPresents(ctx, 1, amsid, groupId, userInfos.UserOpenid, userInfos.BlueGunSerialId)
	if err != nil {
		// 更新db序列号
		updates := map[string]interface{}{
			"blue_gun_serial_id": serialId,
		}
		err = DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("id = ?",
			userInfos.ID).Updates(updates).Error
		if err != nil {
			// 写入db错误，腾讯云监控告警
			log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1",
				"update send info failed").Errorf("updates err sendCBTDFBlueGuns update serial, \t [Error]:{%v} ", err.Error)
			return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "update serial failed")
		}
		return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "send present failed")
	}
	// 记录db
	updates := map[string]interface{}{
		"blue_gun_sended_at": time.Now().Unix(),
		"blue_gun_serial_id": serialId,
	}
	// 更新发送时间db和流水号
	err = DB.DefaultConnect().WithContext(ctx).Debug().Table(model.DfCBTMdsFlow{}.TableName()).Where("id = ?",
		userInfos.ID).Updates(updates).Error
	if err != nil {
		// 写入db错误，腾讯云监控告警
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1",
			"update send info failed").Errorf("updates err sendCBTDFBlueGuns update sendAt, \t [Error]:{%v} ", err.Error)
		return errs.NewCustomError(ctx, code.CBTDFSendPresentFailed, "update send info failed")
	}
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").
		Infof("sendCBTDFBlueGuns success serialId: %s", serialId)
	return nil
}

func sendCBTDFPresents(ctx context.Context, count int32, amsId string, groupId string, intlOpenid string,
	serialId string) (serial string, err error) {
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").
		Infof("sendCBTDFPresents count: %d, amsId: %s, groupId: %s", count, amsId, groupId)
	if count <= 0 {
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").Infof("count <= 0")
		return "", nil
	}
	if amsId == "" || groupId == "" {
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").Infof("amsId or groupId is empty")
		return "", nil
	}
	// TODO 需要开放
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").Infof("GetUserAccount err  = %v", err)
		return serial, err
	}
	language := metadata.GetLangType(ctx)
	// 生成流水号
	if serialId == "" {
		serialId = ams_pb.CreateAmsSerial("projectd_oversea", "IEG-AMS-4000031")
	}

	// // DEBUG
	// sendRsp := &amsPresentPb.SendAmsPresentRsp{
	// 	Serial: serialId,
	// }
	amsPresentProxy := amsPresentPb.NewAmsClientProxy()
	sendRsp, err := amsPresentProxy.SendOpenidAmsPresent(ctx, &amsPresentPb.SendOpenidAmsPresentReq{
		AmsId:      amsId,   // 老许提供 (平台配置)
		GroupId:    groupId, // 老许提供 (平台配置)
		PackageNum: count,
		Openid:     intlOpenid, // meta内取数据
		Serial:     serialId,   // 计算
		LangType:   language,   // meta内取数据
		RoleInfo: &gamePb.RoleInfo{
			GameId:   "29158",
			GameName: "projectd_oversea",
			AreaId:   66, // 待定
		},
	})
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins").
		Infof(
			"amsPresentProxy.SendOpenidAmsPresent meteaccount:%s, amsid: %s, groupId: %s, count: %d, openid: %s, serial: %s, langType: %s, sendRsp = %v, err=%v", userAccount.IntlAccount.OpenId, amsId, groupId, count, intlOpenid, serialId, language, sendRsp, err)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1", "send present failed",
			"str_field_2", intlOpenid, "str_field_3", amsId, "str_field_4", groupId, "str_field_5", serialId, "int_field_1",
			fmt.Sprintf("%d", count)).Errorf("amsPresentProxy.SendOpenidAmsPresent err  = %v", err)
		return serial, err
	}
	// TODO 添加日志告警
	log.WithFieldsContext(ctx, "log_type", "df_cbt_receive_recharge_rebate_coins", "str_field_1", "send present succ",
		"str_field_2", intlOpenid, "str_field_3", amsId, "str_field_4", groupId, "str_field_5", serialId, "int_field_1",
		fmt.Sprintf("%d", count)).Infof("amsPresentProxy.SendOpenidAmsPresent success serialId: %s", serialId)
	serial = sendRsp.Serial
	return serial, nil
}

// DfIsLoginToday TODO
func DfIsLoginToday(ctx context.Context) (rsp *pb.DfIsLoginTodayRsp, err error) {
	rsp = &pb.DfIsLoginTodayRsp{
		IsLogin: false,
	}
	amsProxy := gameAmsPb.NewAmsClientProxy()
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	accountData, _ := proto.Marshal(&userAccount)
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}

	IdipParam := make([]*gameAmsPb.IdipGetItem, 0)
	// IdipParam = append(IdipParam, &gameAmsPb.IdipGetItem{
	// 	Key:   "AreaId",
	// 	Value: "66",
	// })

	amsInfo, err := amsProxy.GetInfoListByAms(ctx, &gameAmsPb.GetInfoListByAmsReq{
		SelectParam: &gameAmsPb.IdipDBParam{
			CmdId:  "********", // 登录
			GameId: "29158",
		},
		IdipParam: IdipParam,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "DfIsLoginToday_err", "str_field_1", userAccount.Uid).Infof("err: %v", err)
		return
	}

	// 获取当前时间（本地时区）
	now := time.Now()
	// 构造当天零点时间对象
	midnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	// 转换为时间戳（秒级）
	timestamp := midnight.Unix()

	for _, item := range amsInfo.Info {
		if item.Key == "last_login_time" {
			num, _ := strconv.ParseInt(item.Value, 10, 64)
			rsp.IsLogin = timestamp <= num
		}
	}

	if !rsp.IsLogin {
		err = errs.NewCustomError(ctx, code.DfIsLoginTodayErr, "no login today")
	}
	return
}

// DfIsPlayToday TODO
func DfIsPlayToday(ctx context.Context) (rsp *pb.DfIsPlayTodayRsp, err error) {
	rsp = &pb.DfIsPlayTodayRsp{}
	amsProxy := gameAmsPb.NewAmsClientProxy()
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	accountData, _ := proto.Marshal(&userAccount)
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}

	IdipParam := make([]*gameAmsPb.IdipGetItem, 0)
	// IdipParam = append(IdipParam, &gameAmsPb.IdipGetItem{
	// 	Key:   "AreaId",
	// 	Value: "66",
	// })
	// 获取当前时间（本地时区）
	now := time.Now()
	// 构造当天零点时间对象
	startTimestamp := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()
	endTimestamp := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location()).Unix()
	IdipParam = append(IdipParam, &gameAmsPb.IdipGetItem{
		Key:   "starttime",
		Value: fmt.Sprintf("%v", startTimestamp),
	})
	IdipParam = append(IdipParam, &gameAmsPb.IdipGetItem{
		Key:   "endtime",
		Value: fmt.Sprintf("%v", endTimestamp),
	})

	amsInfo, err := amsProxy.GetInfoListByAms(ctx, &gameAmsPb.GetInfoListByAmsReq{
		SelectParam: &gameAmsPb.IdipDBParam{
			CmdId:  "********", // 烽火
			GameId: "29158",
		},
		IdipParam: IdipParam,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "DfIsPlayToday_err", "str_field_1", userAccount.Uid).Infof("err: %v", err)
		return
	}
	if len(amsInfo.Info) == 0 {
		amsInfo, err = amsProxy.GetInfoListByAms(ctx, &gameAmsPb.GetInfoListByAmsReq{
			SelectParam: &gameAmsPb.IdipDBParam{
				CmdId:  "********", // 全面
				GameId: "29158",
			},
			IdipParam: IdipParam,
		}, callopts...)
		if err != nil {
			log.WithFieldsContext(ctx, "log_type", "DfIsPlayToday_err", "str_field_1", userAccount.Uid).Infof("err: %v", err)
			return
		}
		if len(amsInfo.Info) == 0 {
			rsp.IsPlay = false
			err = errs.NewCustomError(ctx, code.DfIsPlayTodayErr, "no play today")
			return
		}
		rsp.IsPlay = true
		return
	}
	rsp.IsPlay = true
	return
}

// DfKillNumToday TODO
func DfKillNumToday(ctx context.Context) (rsp *pb.DfKillNumTodayRsp, err error) {
	rsp = &pb.DfKillNumTodayRsp{}
	amsProxy := gameAmsPb.NewAmsClientProxy()
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	accountData, _ := proto.Marshal(&userAccount)
	callopts := []client.Option{
		client.WithMetaData(metadata.UserAccount, accountData),
	}

	IdipParam := make([]*gameAmsPb.IdipGetItem, 0)
	// IdipParam = append(IdipParam, &gameAmsPb.IdipGetItem{
	// 	Key:   "AreaId",
	// 	Value: "66",
	// })
	// 获取当前时间（本地时区）
	now := time.Now()
	// 构造当天零点时间对象
	startTimestamp := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()
	endTimestamp := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location()).Unix()
	IdipParam = append(IdipParam, &gameAmsPb.IdipGetItem{
		Key:   "starttime",
		Value: fmt.Sprintf("%v", startTimestamp),
	})
	IdipParam = append(IdipParam, &gameAmsPb.IdipGetItem{
		Key:   "endtime",
		Value: fmt.Sprintf("%v", endTimestamp),
	})

	amsInfo, err := amsProxy.GetInfoListByAms(ctx, &gameAmsPb.GetInfoListByAmsReq{
		SelectParam: &gameAmsPb.IdipDBParam{
			CmdId:  "********", // 烽火
			GameId: "29158",
		},
		IdipParam: IdipParam,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "DfIsPlayToday_err", "str_field_1", userAccount.Uid).Infof("err: %v", err)
		return
	}
	for _, item := range amsInfo.Info {
		if item.Key == "kill" {
			num, _ := strconv.ParseInt(item.Value, 10, 64)
			rsp.KillNum = rsp.KillNum + int32(num)
		}
	}

	amsInfo, err = amsProxy.GetInfoListByAms(ctx, &gameAmsPb.GetInfoListByAmsReq{
		SelectParam: &gameAmsPb.IdipDBParam{
			CmdId:  "********", // 全面
			GameId: "29158",
		},
		IdipParam: IdipParam,
	}, callopts...)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "DfIsPlayToday_err", "str_field_1", userAccount.Uid).Infof("err: %v", err)
		return
	}
	if len(amsInfo.Info) == 0 {
		rsp.KillNum = 0
		return
	}
	for _, item := range amsInfo.Info {
		if item.Key == "kda" {
			num, _ := strconv.ParseInt(strings.Split(item.Value, "/")[1], 10, 64)

			rsp.KillNum = rsp.KillNum + int32(num)
		}
	}
	return
}
