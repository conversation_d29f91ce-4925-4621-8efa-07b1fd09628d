// Package signin TODO
package signin

import (
	"context"
	"strconv"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/base64encode"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_signin"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	signinlogic "trpc.act.logicial/app/logic/signin"
)

// SigninImpl 结构
type SigninImpl struct {
	pb.UnimplementedSignin
}

// GetUserSignedInDates 获取用户已签到日期列表
func (s *SigninImpl) GetUserSignedInDates(ctx context.Context, req *pb.GetUserSignedInDatesReq) (
	*pb.GetUserSignedInDatesRsp, error) {

	signedInDates, err := signinlogic.GetUserSignedInDates(ctx, req.FsourceId)
	if err != nil {
		return nil, err
	}
	return &pb.GetUserSignedInDatesRsp{
		SignedInDates: signedInDates,
	}, nil
}

// DoSignIn 签到
func (s *SigninImpl) DoSignIn(ctx context.Context, req *pb.DoSignInReq) (rsp *pb.DoSignInRsp, err error) {
	rsp = &pb.DoSignInRsp{}
	// 签到
	var success bool
	var signinDays int32
	var continuousSigninDays int32
	var insertId int
	success, insertId, signinDays, continuousSigninDays, err = signinlogic.DoSignin(ctx, req.FsourceId, req.TimeZone)
	if err != nil {
		return
	}
	if !success {
		err = errs.NewCustomError(ctx, code.HasSignin, "has signin")
		return
	}

	signinNo := base64encode.Base64UrlSafeEncode(strconv.Itoa(insertId), config.GetConfig().Crypto)
	rsp = &pb.DoSignInRsp{
		SigninDays:            signinDays,
		ConsecutiveSigninDays: continuousSigninDays,
		SigninNo:              signinNo,
		Result:                true,
	}
	log.WithFieldsContext(ctx, "log_type", "logicial_signin", "source_id", req.FsourceId).
		Infof("add signin")
	return
}

// HasSignInDays 已签到天数
func (s *SigninImpl) HasSignInDays(ctx context.Context, req *pb.HasSignInDaysReq) (rsp *pb.HasSignInDaysRsp,
	err error) {
	rsp = &pb.HasSignInDaysRsp{}
	var days int32
	days, err = signinlogic.GetSigninDays(ctx, req.FsourceId, req.CheckContinuous)
	if err != nil {
		return
	}
	rsp.Days = days
	return
}

// HasSignIn 是否签到
func (s *SigninImpl) HasSignIn(ctx context.Context, req *pb.HasSignInReq) (rsp *pb.HasSignInRsp, err error) {
	rsp = &pb.HasSignInRsp{}
	var hasSignin bool
	hasSignin, err = signinlogic.CheckHasSignin(ctx, req.FsourceId, req.CheckToday, req.TimeZone)
	if err != nil {
		return
	}
	if !req.CheckNotSignin && !hasSignin {
		err = errs.NewCustomError(ctx, code.HasNotSignin, "has not signin")
		return
	}
	if req.CheckNotSignin && hasSignin {
		err = errs.NewCustomError(ctx, code.HasSignin, "has signin")
		return
	}
	rsp.HasSignin = hasSignin
	return
}

// RollbackSignin TODO
func (s *SigninImpl) RollbackSignin(ctx context.Context, req *pb.RollbackSigninReq) (rsp *pb.RollbackSigninRsp,
	err error) {
	rsp = &pb.RollbackSigninRsp{}
	// 解密
	lastIdStr, err := base64encode.Base64URLDecode(req.SigninNo, config.GetConfig().Crypto)

	lastId, err := strconv.Atoi(lastIdStr)
	if err != nil || lastId == 0 {
		err = errs.NewCustomError(ctx, code.InvalidSigninNo, "invalid signin no")
		return
	}
	err = signinlogic.RollbackSignin(ctx, lastId)
	return
}
