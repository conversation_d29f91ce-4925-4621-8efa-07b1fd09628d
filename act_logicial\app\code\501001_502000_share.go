package code

const (
	// ErrorShareCode  分享码错误
	ErrorShareCode = 501001
	// RequestTooOften 请求太频繁
	RequestTooOften = 501002
	// RecordNumNotMatch 分享数未达标
	RecordNumNotMatch = 501003
	// ShareSelf 无法分享自己
	ShareSelf = 501004
	// UserHasShareCode 用户已有分享码
	UserHasShareCode = 501005
	// GenerateShareCodeFail 生成分享码失败
	GenerateShareCodeFail = 501006
	// ShortShareCodeAlreadyRecord    用户已被邀请过
	ShortShareCodeAlreadyRecord = 501007
	// ReachedMaxLimitOfInvitedUsers TODO
	ReachedMaxLimitOfInvitedUsers = 501008
	// ShortShareCodeNoAlreadyRecord TODO
	ShortShareCodeNoAlreadyRecord = 501009
	// NoCreateShareCode 没有生成分享码
	NoCreateShareCode = 501010
	// HaveCreateShareCode 已经生成分享码了
	HaveCreateShareCode                = 501011
	ShortShareCodeAlreadyRecordThisOne = 501010
	// GenerateTeamCodeFail 生成分享码失败
	GenerateTeamCodeFail = 501015
	// NoJoinTeam 没有加入队伍
	NoJoinTeam = 501012
	// MaxTeamLimit 已经达到队伍人数上限
	MaxTeamLimit = 501013
	// TeamCodeIsNotExit 队伍已经解散
	TeamCodeIsNotExit = 501014
)
