package cron

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"time"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/constant"
	"trpc.act.logicial/app/logic/cag_common"
	"trpc.act.logicial/app/logic/df_tmp_common"
	"trpc.act.logicial/app/model/df_activity"
	"trpc.act.logicial/app/mysql/cag_acvivity_repo"
	"trpc.act.logicial/app/mysql/df_activity_repo"
	"trpc.act.logicial/app/mysql/functional"
	"trpc.act.logicial/app/redis"
)

type CagRoleLevelPrizeTaskProc struct {
}

// CagRoleLevelPrizeTask 更新cag角色等级奖励活动任务
func CagRoleLevelPrizeTask(ctx context.Context) error {
	// 到数据库查询数据
	log.DebugContextf(ctx, "start cag role level prize send check tasks")
	defer func() {
		if r := recover(); r != nil {
			log.ErrorContextf(ctx, "CagRoleLevelPrizeTask Recovered from panic:", r)
		}
	}()
	// 加锁时长，默认90秒
	lockTime := config.GetConfig().CagNewRoleAct.CheckPrizeSendLockTime
	if lockTime <= 0 {
		lockTime = 90
	}
	log.DebugContextf(ctx, "CagRoleLevelPrizeTask lock time: %v", lockTime)
	redisKey := redis.GetCagRoleLevelPrizeSendCheckLockKey()
	if redis.LockByKey(ctx, redisKey, lockTime) {
		log.DebugContextf(ctx, "task monitor get lock success, process")
	} else {
		log.DebugContextf(ctx, "task monitor get lock fail, return")
		return nil
	}
	// 监控时长
	startTime := time.Now().Unix()
	defer func() {
		endTime := time.Now().Unix()
		expireTime := endTime - startTime
		log.DebugContextf(ctx, "CagRoleLevelPrizeTask process finish, cost: %v", expireTime)
		if expireTime+20 > lockTime {
			// TODO 告警
			log.ErrorContextf(ctx, "CagRoleLevelPrizeTask process cost too much time, expireTime: %v", expireTime)
		} else {
			// 释放锁
			redis.UnLockByKey(ctx, redisKey)
		}
	}()
	proc := &CagRoleLevelPrizeTaskProc{}
	proc.process(ctx)
	return nil
}

// process 处理
func (p *CagRoleLevelPrizeTaskProc) process(ctx context.Context) {
	log.DebugContextf(ctx, "CagRoleLevelPrizeTaskProc process start")
	// 检查是否创建角色
	p.checkCreateRole(ctx)

	// 发奖重试
	p.retrySendPrize(ctx)
}

// checkTaskComplete 检查任务是否完成
func (p *CagRoleLevelPrizeTaskProc) checkTaskComplete(ctx context.Context, task *df_activity.GunActivityTask) bool {
	if task == nil {
		return false
	}
	if task.Status != df_activity.GunTaskStatusProcessing {
		return false
	}

	// 查询任务是否完成助力
	// 获取枪械拼枪完成任务数
	needHelpNum, err := df_tmp_common.GetHelpNumByGunId(task.GunId)
	if nil != err {
		log.ErrorContextf(ctx, "checkTaskComplete GetHelpNumByGunId error:%v, taskId: %v", err, task.TaskId)
		return false
	}
	// 查询任务已经完成的助力数
	num, err := df_activity_repo.GunActivityRepoClient.GetTaskHelpNum(ctx, task.TaskId, 0)
	if nil != err {
		log.ErrorContextf(ctx, "checkTaskComplete GetTaskHelpNum error:%v, taskId: %v",
			err, task.TaskId)
		return false
	}
	if int32(num) < needHelpNum { // 还未完成，不处理
		log.DebugContextf(ctx, "checkTaskComplete task not finished, "+
			"taskId: %v, helpNum: %v, needNum: %v",
			task.TaskId, num, needHelpNum)
		return false
	}
	return true
}

func (p *CagRoleLevelPrizeTaskProc) getLimitNum(ctx context.Context) int {
	limit := config.GetConfig().CagNewRoleAct.CheckPrizeSendLimitNum
	if limit <= 0 { // 默认1000
		limit = 1000
	}
	log.DebugContextf(ctx, "CagRoleLevelPrizeTaskProc getLimitNum: %v", limit)
	return limit
}

func (p *CagRoleLevelPrizeTaskProc) getCheckCreateRoleNum(ctx context.Context) int {
	limit := config.GetConfig().CagNewRoleAct.CheckCreateRoleNum
	if limit <= 0 { // 默认100
		limit = 100
	}
	log.DebugContextf(ctx, "CagRoleLevelPrizeTaskProc getCheckCreateRoleNum: %v", limit)
	return limit
}

// retrySendPrize 发奖重试
func (p *CagRoleLevelPrizeTaskProc) retrySendPrize(ctx context.Context) {
	limit := p.getLimitNum(ctx)
	maxSendTimes := 3 // TODO 发奖重试10次
	// 获取发奖失败的任务
	activityId := config.GetConfig().CagNewRoleAct.ActivityId
	tasks, err := cag_acvivity_repo.CreateRoleActivityClient.GetUserPrizeRecord(ctx, []functional.Option{
		functional.WithActivityId(activityId),
		functional.WithGameId(constant.GAMEID_CAG),
		functional.WithSendPrizeTimesLessThan(maxSendTimes + 1),
		functional.WithStatus(constant.CagRoleLevelPrizeStatusSendFail),
		functional.WithLimit(limit),
		functional.WithUpdateTimeOrder(true)})
	if err != nil {
		log.ErrorContextf(ctx, "retrySendPrize GetUserPrizeRecord failed, err:%v", err)
		return
	}
	num := len(tasks)
	log.DebugContextf(ctx, "CagRoleLevelPrizeTaskProc retrySendPrize, activityID: %v, taskNum: %v",
		activityId, num)
	if num >= limit {
		// TODO 告警
		log.ErrorContextf(ctx, "CagRoleLevelPrizeTaskProc retrySendPrize too many tasks, num: %v", num)
	}
	// 发奖
	for _, task := range tasks {
		// TODO 监控告警
		log.DebugContextf(ctx, "CagRoleLevelPrizeTaskProc retrySendPrize id: %v, sendPrizeTimes: %v",
			task.ID, task.SendPrizeTimes)
		// 重试发奖
		err = cag_common.DoSendNewRolePrize(ctx, task)
		if nil != err {
			// TODO 告警
			log.ErrorContextf(ctx, "CagRoleLevelPrizeTaskProc retrySendPrize send prize error, "+
				"activityID: %s, task: %+v, err: %v", activityId, task, err)
		}
	}
}

// checkCreateRole 检查创建角色
func (p *CagRoleLevelPrizeTaskProc) checkCreateRole(ctx context.Context) {
	// 检查是否需要继续执行
	// 活动结束时间
	endTime := config.GetConfig().CagNewRoleAct.ActEndTimeStamp
	limitDay := config.GetConfig().CagNewRoleAct.NotCheckCreateRoleAfterActEnd
	if time.Now().Unix() >= endTime+int64(limitDay*86400) {
		log.DebugContextf(ctx, "activity end more than %v days, not checkCreateRole", limitDay)
		return
	}

	limit := p.getCheckCreateRoleNum(ctx)
	// 获取没有创建角色的用户数
	activityId := config.GetConfig().CagNewRoleAct.ActivityId
	tasks, err := cag_acvivity_repo.CreateRoleActivityClient.GetUserPrizeRecord(ctx, []functional.Option{
		functional.WithActivityId(activityId),
		functional.WithGameId(constant.GAMEID_CAG),
		functional.WithStatus(constant.CagRoleLevelPrizeStatusWaitCreateRole),
		functional.WithLimit(limit),
		functional.WithUpdateTimeOrder(true)})
	if err != nil {
		log.ErrorContextf(ctx, "checkCreateRole GetUserPrizeRecord failed, err:%v", err)
		return
	}
	num := len(tasks)
	log.DebugContextf(ctx, "CagRoleLevelPrizeTaskProc checkCreateRole, activityID: %v, taskNum: %v",
		activityId, num)
	if num >= limit {
		// TODO 告警
		log.ErrorContextf(ctx, "CagRoleLevelPrizeTaskProc checkCreateRole too many tasks, num: %v", num)
	}
	// 发奖
	for _, task := range tasks {
		log.DebugContextf(ctx, "CagRoleLevelPrizeTaskProc checkCreateRole info: %+v",
			task)
		// 检查是否创建角色
		hasRole, cagOpenId, err := cag_common.CheckCagRoleCreate(ctx, task.LipOpenID)
		if nil != err {
			// TODO 告警
			log.ErrorContextf(ctx, "CagRoleLevelPrizeTaskProc checkCreateRole check role error, "+
				"activityID: %s, task: %+v, err: %v", activityId, task, err)
			// 更新检查次数
			err = cag_acvivity_repo.CreateRoleActivityClient.UpdateCheckCreateRoleTimes(ctx, task.ID, task.UserID,
				task.CheckCreateRoleTimes+1)
			continue
		}
		if !hasRole {
			// TODO 告警
			log.ErrorContextf(ctx, "CagRoleLevelPrizeTaskProc checkCreateRole no role, "+
				"activityID: %s, task: %+v", activityId, task)
			// 更新检查次数
			err = cag_acvivity_repo.CreateRoleActivityClient.UpdateCheckCreateRoleTimes(ctx, task.ID, task.UserID,
				task.CheckCreateRoleTimes+1)
			continue
		}
		if task.OpenID == "" && cagOpenId != "" { // 如果没有游戏openid，需要更新openid
			log.DebugContextf(ctx, "CagRoleLevelPrizeTaskProc checkCreateRole update openid, "+
				"activityID: %s, task: %+v, cagOpenId: %v", activityId, task, cagOpenId)
			updates := map[string]interface{}{
				"open_id": cagOpenId,
			}
			cag_acvivity_repo.CreateRoleActivityClient.UpdateInfoById(ctx, task.ID,
				task.UserID, updates)
			task.OpenID = cagOpenId
		}
		// 发奖
		err = cag_common.DoSendNewRolePrize(ctx, task)
		if nil != err {
			// TODO 告警
			log.ErrorContextf(ctx, "CagRoleLevelPrizeTaskProc checkCreateRole send prize error, "+
				"activityID: %s, task: %+v, err: %v", activityId, task, err)
		}
	}
}
