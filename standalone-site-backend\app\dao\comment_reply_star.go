package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func CommentReplyStarGet(id, commentReplyId int64, intlOpenID string) (*model.CommentReplyStar, error) {
	var star model.CommentReplyStar
	db := DB.SelectConnect("db_standalonesite").Table((&model.CommentReplyStar{}).TableName())

	if id > 0 {
		db = db.Where("id = ? AND is_del = ?", id, 0)
	}
	if commentReplyId > 0 {
		db = db.Where("comment_reply_id = ?", commentReplyId)
	}
	if intlOpenID != "" {
		db = db.Where("intl_openid =  ?", intlOpenID)
	}

	if err := db.First(&star).Error; err != nil {
		return nil, err
	}
	return &star, nil
}

func CommentReplyStarCheckUserCommentReplysStar(commentReplyIDs []int64, intlOpenID string) ([]int64, error) {
	var starCommentReplyIds []int64
	if err := DB.SelectConnect("db_standalonesite").Table((&model.CommentReplyStar{}).TableName()).Where("comment_reply_id in ?", commentReplyIDs).Where("intl_openid =  ?", intlOpenID).Where("is_del = 0").Pluck("comment_reply_id", &starCommentReplyIds).Error; err != nil {
		return starCommentReplyIds, err
	}
	return starCommentReplyIds, nil

}

func CommentReplyStarCreate(replyStar *model.CommentReplyStar) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.CommentReplyStar{}).TableName()).Omit("CommentReply").Create(&replyStar).Error

	return err
}

func CommentReplyStarDelete(id int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentReplyStar{}).TableName()).Omit("CommentReply").Where("id = ? AND is_del = ?", id, 0).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}
