package exobornefriendsinvite

import "trpc.act.logicial/app/model"

type ExobrnFriendsInviteJoinRankModel struct {
	model.AppModel
}

// TableName .
func (ExobrnFriendsInviteJoinRankModel) TableName() string {
	return "exobrn_friend_invite_join_rank"
}

// SendTemp 临时结构
type ExobrnFriendsInviteJoinRank struct {
	ExobrnFriendsInviteJoinRankModel
	ID       int64  `gorm:"type:int(11);column:id;primary_key"`
	TeamCode string `gorm:"type:varchar(255);column:team_code;"`
}
type ExobrnFriendsInviteOnlineModel struct {
	model.AppModel
}

// TableName .
func (ExobrnFriendsInviteOnlineModel) TableName() string {
	return "exobrn_friend_invite_online"
}

// SendTemp 临时结构
type ExobrnFriendsInviteOnline struct {
	ExobrnFriendsInviteOnlineModel
	ID             int64  `gorm:"type:int(11);column:id;primary_key"`
	TeamCode       string `gorm:"type:varchar(255);column:team_code;"`
	Openid         string `gorm:"type:varchar(255);column:openid;"`
	IsLogin        int32  `gorm:"type:tinyint(1);column:is_login;"`
	OnlineDuration int64  `gorm:"type:int(20);column:online_duration;"`
}
type ExobrnFriendsInviteNameModel struct {
	model.AppModel
}

// TableName .
func (ExobrnFriendsInviteNameModel) TableName() string {
	return "exobrn_friend_invite_name"
}

// SendTemp 临时结构
type ExobrnFriendsInviteName struct {
	ExobrnFriendsInviteNameModel
	ID       int64  `gorm:"type:int(11);column:id;primary_key"`
	RoleName string `gorm:"type:varchar(255);column:role_name;"`
	Openid   string `gorm:"type:varchar(255);column:openid;"`
}
type ExobrnFriendsInviteDeplopytimesModel struct {
	model.AppModel
}

// TableName .
func (ExobrnFriendsInviteDeplopytimesModel) TableName() string {
	return "exobrn_friend_invite_deplopytimes"
}

// SendTemp 临时结构
type ExobrnFriendsInviteDeplopytimes struct {
	ExobrnFriendsInviteDeplopytimesModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	Uids        string `gorm:"type:varchar(255);column:uids;"`
	Delopytimes int64  `gorm:"type:int(20);column:delopytimes;"`
}
