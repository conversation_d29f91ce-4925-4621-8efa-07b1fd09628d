package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm/clause"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetInitInfosByOpenids(intlOpenid []string, gameId string, areaId string) (initDatas []*model.InitAuthUserData, err error) {
	initDatas = make([]*model.InitAuthUserData, 0)
	if len(intlOpenid) == 0 {
		return
	}
	db := DB.SelectConnect("db_standalonesite").Table((&model.InitAuthUserData{}).TableName())
	err = db.Where("intl_openid in ? AND game_id=? AND area_id=?", intlOpenid, gameId, areaId).Find(&initDatas).Error
	return initDatas, err
}

func BatchInsertInitAuthUserDatas(datas []*model.InitAuthUserData) (err error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.InitAuthUserData{}).TableName())
	err = db.Clauses((clause.OnConflict{
		Columns:   []clause.Column{{Name: "intl_openid"}, {Name: "game_id"}, {Name: "area_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"id", "avatar_url", "remark", "nick_name", "updater", "modified_on", "is_del"}),
	})).Error
	return err
}
