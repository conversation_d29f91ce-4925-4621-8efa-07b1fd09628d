package hoktmp

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strings"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	deltaversePb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/deltaverse"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/object"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	gameX1Pb "git.woa.com/trpcprotocol/publishing_marketing/game_x1"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/model/hok"
)

// EventParticipationStatsResponse TODO
type EventParticipationStatsResponse struct {
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	TraceID string `json:"traceId"`
	Data    int    `json:"data"`
}

// GetHokTaskV2Rsp TODO
type GetHokTaskV2Rsp struct {
	HttpStatus int                  `json:"HttpStatus"`
	Code       int                  `json:"code"`
	Data       HokRspData           `json:"data"`
	Datas      map[string]InnerData `json:"datas"`
	Message    string               `json:"message"`
}

// InnerData TODO
type InnerData struct {
	Code    int        `json:"code"`
	Data    HokRspData `json:"data"`
	Message string     `json:"message"`
}

// HokRspData TODO
type HokRspData struct {
	FieldValues map[string]string `json:"_fieldValues"`
	Result      string            `json:"result"`
}

const (
	// PeriodicTaskPackageLoginHok TODO
	PeriodicTaskPackageLoginHok = "Wand-20240902112053-P6664dbd8bcfe" // 周任务-登录hok
	// PeriodicTaskPackageEffectiveRunningGames TODO
	PeriodicTaskPackageEffectiveRunningGames = "Wand-20240902112135-Pf0c20bc2568f" // 周任务-有效办赛
	// PeriodicTaskPackageParticipateInEvent TODO
	PeriodicTaskPackageParticipateInEvent = "Wand-20240902112259-P3721c6a6ec7e" // 周任务-参与赛事
)

// HOKClubAchievementQuestRewardReissue 成就任务奖励补发
func HOKClubAchievementQuestRewardReissue(ctx context.Context, fSourceId string) error {

	newCtx := context.Background()
	// 获取所有参与活动人数
	var count int64
	userParticipationTableName := hok.HOKUserParticipationV2Model{}.TableName()
	if err := DB.DefaultConnect().WithContext(newCtx).Table(userParticipationTableName).
		Count(&count).Error; err != nil {
		return errs.NewSystemError(newCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"HOKClubAchievementQuestRewardReissue count db error, \t [Error]:{%v} ", err)
	}
	// 根据参与的用户总数分页
	pageSize := 50
	totalPages := int(math.Ceil(float64(count) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	gameProxy := gamePb.NewGameClientProxy()
	gameX1Proxy := gameX1Pb.NewX1ClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		// 分页获取参赛人数
		offset := (pageNumber - 1) * pageSize
		var userParticipationList []hok.HokClubUserParticipationV2
		sel := "uid,account_type,lang_type"
		if err := DB.DefaultConnect().Debug().WithContext(newCtx).Table(userParticipationTableName).Select(sel).
			Offset(offset).Limit(pageSize).Order("id asc").Find(&userParticipationList).Error; err != nil {
			return errs.NewSystemError(newCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"HOKClubAchievementQuestRewardReissue Find db error, \t [Error]:{%v} ", err.Error())
		}

		for _, userInfo := range userParticipationList {
			wg.Add(1)
			go func(userItem hok.HokClubUserParticipationV2) {
				defer wg.Done()
				// 获取当前用户已领取礼包列表
				openID := strings.Split(userItem.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         userItem.UID,
					AccountType: accountPb.AccountType(userItem.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId: openID,
						// todo ChannelId暂定为3
						ChannelId: 3,
					},
				})
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(userItem.LangType)),
				}
				presentList, err := sendProxy.GetUserFSourcePresentList(newCtx, &presentPb.GetUserFSourcePresentListReq{
					FsourceId: fSourceId,
				}, callopts...)
				if err != nil {
					log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
						"HOKClubAchievementQuestRewardReissue GetUserFSourcePresentList error: accountData:[%v],err:[%v]",
						accountData, err))
					return
				}

				allPresentIds := []string{
					"Wand-**************-Pf981ba41fe7a",
					"Wand-**************-P29dfecf296a6",
					"Wand-**************-P3a667c85e958",
					"Wand-**************-P1dc116df2729",
					"Wand-**************-Pab9c233622c3",
				}
				var hasPresentIds []string
				var unclaimedPresentIds []string
				for _, v := range presentList.UserHasPresentList {
					for _, presentId := range allPresentIds {
						if v == presentId {
							hasPresentIds = append(hasPresentIds, v)
						}
					}
				}
				if len(hasPresentIds) == 5 {
					// 所有成就奖励已领取
					return
				}
				// 获取累计有效参数人数
				totalValidParticipants, err := gameX1Proxy.GetTotalValidParticipants(newCtx, &gameX1Pb.GetTotalValidParticipantsReq{}, callopts...)
				if err != nil {
					log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
						"HOKClubAchievementQuestRewardReissue GetTotalValidParticipants error: err:[%v]", err))
					return
				}
				//log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
				//	"HOKClubAchievementQuestRewardReissue GetTotalValidParticipants show totalValidParticipants: TotalNum:[%v]", totalValidParticipants.TotalNum))
				totalNum := totalValidParticipants.TotalNum
				switch {
				//case totalNum >= 6:
				case totalNum >= 200:
					for _, v := range allPresentIds {
						if exists, _ := object.InArray(v, hasPresentIds); exists {
							continue
						}
						unclaimedPresentIds = append(unclaimedPresentIds, v)
					}
				//case totalNum >= 5:
				case totalNum >= 100:
					presentIds := []string{
						"Wand-**************-Pf981ba41fe7a",
						"Wand-**************-P29dfecf296a6",
						"Wand-**************-P3a667c85e958",
						"Wand-**************-P1dc116df2729",
					}
					for _, v := range presentIds {
						if exists, _ := object.InArray(v, hasPresentIds); exists {
							continue
						}
						unclaimedPresentIds = append(unclaimedPresentIds, v)
					}
				case totalNum >= 50:
					//case totalNum >= 4:
					presentIds := []string{
						"Wand-**************-Pf981ba41fe7a",
						"Wand-**************-P29dfecf296a6",
						"Wand-**************-P3a667c85e958",
					}
					for _, v := range presentIds {
						if exists, _ := object.InArray(v, hasPresentIds); exists {
							continue
						}
						unclaimedPresentIds = append(unclaimedPresentIds, v)
					}
				case totalNum >= 10:
					//case totalNum >= 3:
					presentIds := []string{
						"Wand-**************-Pf981ba41fe7a",
						"Wand-**************-P29dfecf296a6",
					}
					for _, v := range presentIds {
						if exists, _ := object.InArray(v, hasPresentIds); exists {
							continue
						}
						unclaimedPresentIds = append(unclaimedPresentIds, v)
					}
				case totalNum >= 2:
					presentIds := []string{
						"Wand-**************-Pf981ba41fe7a",
					}
					for _, v := range presentIds {
						if exists, _ := object.InArray(v, hasPresentIds); exists {
							continue
						}
						unclaimedPresentIds = append(unclaimedPresentIds, v)
					}
				default:
					// 没有可领取礼包
					return
				}
				// 获取用户区服角色信息
				roleList, err := gameProxy.GetRoleList(newCtx, &gamePb.GetRoleInfoReq{
					GameId: "29134",
				}, callopts...)
				if err != nil {
					log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
						"HOKClubAchievementQuestRewardReissue GetRoleList err: roleList:[%v],callopts:[%v], err:[%v]",
						roleList, callopts, err))
					return
				}
				if len(roleList.RoleList) != 1 {
					log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
						"HOKClubAchievementQuestRewardReissue GetRoleList len err: roleList:[%v],callopts:[%v]",
						roleList, callopts))
					return
				}
				// 发放未领取成就礼包
				for _, unclaimedPresentId := range unclaimedPresentIds {
					sendPresentRsp, err := sendProxy.SendPresent(newCtx, &presentPb.SendPresentReq{
						FsourceId: fSourceId,
						PresentId: unclaimedPresentId,
						RoleInfo:  roleList.RoleList[0],
					}, callopts...)
					if err != nil {
						log.WithFieldsContext(newCtx, "log_type", "error", "presentId", unclaimedPresentId).Infof(fmt.Sprintf(
							"HOKClubAchievementQuestRewardReissue SendPresent error: UID:[%v],presentId:[%v],RoleInfo:[%v],err:[%v]",
							userItem.UID, unclaimedPresentId, roleList.RoleList, err))
						continue
					}
					if sendPresentRsp.SendType != 1 {
						log.WithFieldsContext(newCtx, "log_type", "error", "send_type", cast.ToString(sendPresentRsp.SendType)).Infof(fmt.Sprintf(
							"HOKClubAchievementQuestRewardReissue SendPresent SendType error: UID:[%v],presentId:[%v],RoleInfo:[%v],sendPresentRsp:[%v]",
							userItem.UID, unclaimedPresentId, roleList.RoleList, sendPresentRsp))
						continue
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf(
						"HOKClubAchievementQuestRewardReissue send success: UID:[%v],presentId:[%v]", userItem.UID,
						sendPresentRsp.PresentId))
				}
			}(userInfo)
		}
	}
	return nil
}

// HOKClubPeriodicMissionRewardReissue 周期任务奖励补发
func HOKClubPeriodicMissionRewardReissue(ctx context.Context, starTime, endTime, timeZone int64,
	fSourceId string) error {

	newCtx := context.Background()
	// 获取所有参与活动人数
	var count int64
	userParticipationTableName := hok.HOKUserParticipationV2Model{}.TableName()
	if err := DB.DefaultConnect().WithContext(newCtx).Table(userParticipationTableName).
		Count(&count).Error; err != nil {
		return errs.NewSystemError(newCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"HOKClubPeriodicMissionRewardReissue count db error, \t [Error]:{%v} ", err)
	}
	// 根据参与的用户总数分页
	pageSize := 50
	totalPages := int(math.Ceil(float64(count) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	gameProxy := gamePb.NewGameClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		// 分页获取参赛人数
		offset := (pageNumber - 1) * pageSize
		var userParticipationList []hok.HokClubUserParticipationV2
		sel := "uid,account_type,lang_type"
		if err := DB.DefaultConnect().Debug().WithContext(newCtx).Table(userParticipationTableName).Select(sel).
			Offset(offset).Limit(pageSize).Order("id asc").Find(&userParticipationList).Error; err != nil {
			return errs.NewSystemError(newCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"HOKClubPeriodicMissionRewardReissue Find db error, \t [Error]:{%v} ", err.Error())
		}

		for _, userInfo := range userParticipationList {
			wg.Add(1)
			go func(userItem hok.HokClubUserParticipationV2) {
				defer wg.Done()
				openID := strings.Split(userItem.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         userItem.UID,
					AccountType: accountPb.AccountType(userItem.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(userItem.LangType)),
				}

				// 获取当前用户周期内已领取的礼包
				var presentIds []string
				db := DB.DefaultConnect().WithContext(newCtx).Table(hok.HokPeriodicGiftClaimModel{}.TableName()).
					Select("present_id").Where(hok.HokPeriodicGiftClaim{
					UID:         userItem.UID,
					AccountType: userItem.AccountType,
				})
				if starTime != 0 && endTime != 0 {
					db = db.Where("created_at > ? and created_at <= ?", starTime, endTime)
				}
				if starTime == 0 {
					db = db.Where("created_at <= ?", endTime)
				}
				if endTime == 0 {
					db = db.Where("created_at > ?", starTime)
				}
				if err := db.Find(&presentIds).Error; err != nil {
					log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
						"HOKClubPeriodicMissionRewardReissue HokPeriodicGiftClaim find err: accountData:[%v],err:[%v]",
						userItem, err))
					return
				}
				if len(presentIds) >= 3 {
					// 周期内所有礼包已领取
					return
				}

				roleList, err := gameProxy.GetRoleList(newCtx, &gamePb.GetRoleInfoReq{
					GameId: "29134",
				}, callopts...)
				if err != nil {
					log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
						"HOKClubPeriodicMissionRewardReissue GetRoleList err: roleList:[%v],callopts:[%v], err:[%v]",
						roleList, callopts, err))
					return
				}
				if len(roleList.RoleList) != 1 {
					log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
						"HOKClubPeriodicMissionRewardReissue GetRoleList len err: roleList:[%v],callopts:[%v]",
						roleList, callopts))
					return
				}
				presentIdsMap := map[string]struct{}{
					PeriodicTaskPackageLoginHok:              {},
					PeriodicTaskPackageEffectiveRunningGames: {},
					PeriodicTaskPackageParticipateInEvent:    {},
				}
				sDay, eDay := ConvertTimeZone(starTime, endTime, timeZone)

				for presentId := range presentIdsMap {
					if exists, _ := object.InArray(presentId, presentIds); exists {
						continue
					}
					// 礼包未领取 获取周期任务完成状态
					var completionStatus bool
					switch presentId {
					case PeriodicTaskPackageLoginHok:
						completionStatus, err = CheckLoginAndTaskCompletionStatus(newCtx, openID, sDay, eDay, 1)
						if err != nil {
							log.WithFieldsContext(newCtx, "log_type", "error", "present_id", PeriodicTaskPackageLoginHok).Infof(fmt.Sprintf(
								"HOKClubPeriodicMissionRewardReissue CheckLoginAndTaskCompletionStatus 1 err: err:[%v]", err))
							continue
						}
						if !completionStatus {
							continue
						}
					case PeriodicTaskPackageEffectiveRunningGames:
						completionStatus, err = CheckLoginAndTaskCompletionStatus(newCtx, openID, sDay, eDay, 2)
						if err != nil {
							log.WithFieldsContext(newCtx, "log_type", "error", "present_id", PeriodicTaskPackageEffectiveRunningGames).Infof(fmt.Sprintf(
								"HOKClubPeriodicMissionRewardReissue CheckLoginAndTaskCompletionStatus 2 err: err:[%v]", err))
							continue
						}
						if !completionStatus {
							continue
						}
					case PeriodicTaskPackageParticipateInEvent:
						completionStatus, err = CheckParticipationInPeriodTaskStatus(newCtx, openID, sDay, eDay)
						if err != nil {
							log.WithFieldsContext(newCtx, "log_type", "error", "present_id", PeriodicTaskPackageParticipateInEvent).Infof(fmt.Sprintf(
								"HOKClubPeriodicMissionRewardReissue CheckParticipationInPeriodTaskStatus err: err:[%v]", err))
							continue
						}
						if !completionStatus {
							continue
						}
					default:
						log.WithFieldsContext(newCtx, "log_type", "error", "present_id", "nil").Infof(fmt.Sprintf(
							"HOKClubPeriodicMissionRewardReissue not current presentId: presentId:[%v]", presentId))
						continue
					}
					// 发送已完成周期任务但未领奖的礼包
					sendPresentRsp, err := sendProxy.SendPresent(newCtx, &presentPb.SendPresentReq{
						FsourceId: fSourceId,
						PresentId: presentId,
						RoleInfo:  roleList.RoleList[0],
					}, callopts...)
					if err != nil {
						log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
							"HOKClubPeriodicMissionRewardReissue SendPresent error: UID:[%v],presentId:[%v],RoleInfo:[%v],err:[%v]",
							userItem.UID, presentId, roleList.RoleList, err))
						continue
					}
					if sendPresentRsp.SendType != 1 {
						log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
							"HOKClubPeriodicMissionRewardReissue SendPresent SendType error: UID:[%v],presentId:[%v],RoleInfo:[%v],sendPresentRsp:[%v]",
							userItem.UID, presentId, roleList.RoleList, sendPresentRsp))
						continue
					}
					if err = RecordCycleTaskRewardClaimedScript(newCtx, presentId, userItem.UID, endTime); err != nil {
						log.WithFieldsContext(newCtx, "log_type", "warn", "UID", userItem.UID, "presentId", presentId).Infof(fmt.Sprintf(
							"HOKClubPeriodicMissionRewardReissue RecordCycleTaskRewardClaimedScript err: UID:[%v],presentId:[%v],endTime:[%v],err:[%v]",
							userItem.UID, presentId, endTime, err))
						continue
					}
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf(
						"HOKClubPeriodicMissionRewardReissue send success: UID:[%v],sendPresentRsp:[%v]", userItem.UID, sendPresentRsp))
				}
			}(userInfo)
		}
		wg.Wait()
	}
	return nil
}

// RecordCycleTaskRewardClaimedScript 补发脚本记录周期任务已领取礼包
func RecordCycleTaskRewardClaimedScript(ctx context.Context, presentId, uid string, createTime int64) error {

	var periodicGiftClaim hok.HokPeriodicGiftClaim
	periodicGiftClaim.CreatedAt = createTime
	if err := DB.DefaultConnect().WithContext(ctx).Table(hok.HokPeriodicGiftClaimModel{}.TableName()).
		Where(hok.HokPeriodicGiftClaim{
			UID:         uid,
			AccountType: int(accountPb.AccountType_INTL),
			PresentID:   presentId,
		}).Attrs(&periodicGiftClaim).FirstOrCreate(&periodicGiftClaim).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"RecordCycleTaskRewardClaimedScript FirstOrCreate db error, \t [Error]:{%v} ", err)
	}
	return nil
}

// ConvertTimeZone 将给定的时间戳转换为指定时区的日期字符串
func ConvertTimeZone(startTime, endTime, timeZone int64) (string, string) {

	// 创建指定时区的 Location
	location := time.FixedZone("Custom", int(timeZone*3600))
	// 将时间戳转换为 time.Time 对象
	startTimeObj := time.Unix(startTime, 0).In(location)
	endTimeObj := time.Unix(endTime, 0).In(location)
	// 格式化日期为 "********"
	sDay := startTimeObj.Format("********")
	eDay := endTimeObj.Format("********")
	return sDay, eDay
}

// CheckParticipationInPeriodTaskStatus 检查周期内参与赛事
func CheckParticipationInPeriodTaskStatus(ctx context.Context, openId, sDay, eDay string) (bool, error) {

	var taskStatus bool
	hokEventsClub := config.GetConfig().HOKEventsClub
	envName := hokEventsClub.Env
	var env int
	if envName == "test" {
		env = 1
	}
	paramMap := make(map[string]interface{})
	paramMap["openid"] = openId
	paramMap["sday"] = sDay
	paramMap["eday"] = eDay
	paramMap["env"] = env

	response, err := deltaversePb.SendRequest(ctx, deltaversePb.SendRequestParam{
		ServiceType:        "hok",
		DestinationService: "dmfeature-13484",
		Path:               "/dmfeature/13484/tournamentCount",
		Data:               paramMap,
		RequestType:        http.MethodGet,
	})
	if err != nil {
		return taskStatus, err
	}
	var eventParticipationStatsRsp EventParticipationStatsResponse
	if err = json.Unmarshal([]byte(response), &eventParticipationStatsRsp); err != nil {
		return taskStatus, errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.CommonParamJsonError,
			"CheckParticipationInPeriod Unmarshal rsp err,response=[%v], \t [Error]:{%v} ", response, err)
	}
	// log.WithFieldsContext(ctx, "log_type", "debug").Infof("CheckParticipationInPeriod show response:[%v]", response)
	if eventParticipationStatsRsp.Data > 0 {
		taskStatus = true
	}
	return taskStatus, nil
}

// CheckLoginAndTaskCompletionStatus 检查周期内登录和有效办赛任务完成状态
func CheckLoginAndTaskCompletionStatus(ctx context.Context, openId, sDay, eDay string, taskTag int32) (bool, error) {

	const method = http.MethodPost
	hokEventsClub := config.GetConfig().HOKEventsClub
	host := hokEventsClub.Host
	envName := hokEventsClub.Env
	var hostStr []string
	var params []string
	var taskDone bool

	if taskTag == 1 {
		// 不区分环境
		params = append(params, fmt.Sprintf("dmsourceid=%s&eday=%s&openid=%s&sday=%s",
			"", eDay, openId, sDay))
		hostStr1 := fmt.Sprintf("%s/x1/%d/%d&%s", host, 41541, 1143548, "85a53f")
		hostStr = append(hostStr, hostStr1)
	} else {
		switch envName {
		case "dev":
			params = append(params, fmt.Sprintf("dmsourceid=%s&eday=%s&env=%s&openid=%s&sday=%s",
				"", eDay, "test", openId, sDay))
		default:
			params = append(params, fmt.Sprintf("dmsourceid=%s&eday=%s&env=%s&openid=%s&sday=%s",
				"", eDay, "formal", openId, sDay))
			params = append(params, fmt.Sprintf("dmsourceid=%s&eday=%s&env=%s&openid=%s&sday=%s",
				"", eDay, "prerelease", openId, sDay))
		}
		hostStr1 := fmt.Sprintf("%s/x1/%d/%d&%s", host, 41541, 1143547, "1b0a84")
		hostStr2 := fmt.Sprintf("%s/x1/%d/%d&%s", host, 41541, 1143546, "cf82a9")
		hostStr = append(hostStr, hostStr1, hostStr2)
	}

	for _, v := range hostStr {
		for _, param := range params {
			var response string
			urlStr := strings.Join([]string{
				v, param,
			}, "?")
			log.WithFieldsContext(ctx, "log_type", "debug").Infof("HOKClubPeriodicMissionRewardReissue CheckLoginAndTaskCompletionStatus show urlStr:[%v]", urlStr)
			optionOne := httpclient.ClientOption{
				URL: urlStr,
				Header: map[string]string{
					"Content-Type": "application/json",
				},
				Type:       method,
				PostString: "",
			}
			resultOption := httpclient.RequestOne(ctx, optionOne)
			if resultOption.RequestError != nil {
				// 请求失败
				errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.CommonHttpPostError,
					"CheckLoginAndTaskCompletionStatus http error, \t [Error]:{%v} ", resultOption.RequestError)
				continue
			}
			// 根据环境mock 返回数据
			response = resultOption.Result
			// response := "{\"HttpStatus\":200,\"code\":0,\"data\":{\"_fieldValues\":{\"D21013696\":\"1\"},\"result\":\"1\"},\"datas\":{\"1143548\":{\"code\":0,\"data\":{\"_fieldValues\":{\"D21013696\":\"1\"},\"result\":\"1\"},\"message\":\"\"}},\"message\":\"\"}"
			taskDone = unmarshalHokTaskRsp(ctx, response)
			if taskDone {
				// 任务完成
				return taskDone, nil
			}
		}
	}
	return taskDone, nil
}

func unmarshalHokTaskRsp(ctx context.Context, response string) bool {
	log.WithFieldsContext(ctx, "log_type", "debug").Infof("HOKClubPeriodicMissionRewardReissue unmarshalHokTaskRsp show response:[%v]", response)
	var hokTaskRsp GetHokTaskV2Rsp
	var taskStatus bool
	if err := json.Unmarshal([]byte(response), &hokTaskRsp); err != nil {
		errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.JsonParseError,
			"unmarshalHokTaskRsp Unmarshal rsp err,response=[%v], \t [Error]:{%v} ", response, err)
		return taskStatus
	}
	if hokTaskRsp.Code != 0 {
		return taskStatus
	}
	if len(hokTaskRsp.Data.FieldValues) == 0 {
		return taskStatus
	}
	for _, v := range hokTaskRsp.Data.FieldValues {
		resultNum := cast.ToInt(v)
		if resultNum == 0 {
			return taskStatus
		}
	}
	taskStatus = true
	return taskStatus
}
