package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func BatchSaveTagPostRelation(tagPosts []*model.TagPost) error {
	bErr := DB.SelectConnect("db_standalonesite").Table((&model.TagPost{}).TableName()).CreateInBatches(tagPosts, len(tagPosts)).Error
	if bErr != nil {
		return bErr
	}

	return nil
}

func BatchDelTagPostRelation(tagId []int64, postUuid string) error {
	bErr := DB.SelectConnect("db_standalonesite").Table((&model.TagPost{}).TableName()).Where("post_uuid = ? and tag_id in ?", postUuid, tagId).Updates(map[string]interface{}{
		"is_del":     1,
		"deleted_on": time.Now().Unix(),
	}).Error
	if bErr != nil {
		return bErr
	}

	return nil
}

func GetTagIdByPostUUID(postUUID string) ([]int64, error) {
	var tagIds []int64
	bErr := DB.SelectConnect("db_standalonesite").Table((&model.TagPost{}).TableName()).Where("post_uuid = ? AND is_del = 0", postUUID).Pluck("tag_id", &tagIds).Error
	if bErr != nil {
		return nil, bErr
	}

	return tagIds, nil
}

func GetTagIdByPostUUIDV2(postUUID string) ([]int64, error) {
	var tagIds []int64
	bErr := DB.SelectConnect("db_standalonesite").Table((&model.TagPost{}).TableName()).Unscoped().Where("post_uuid = ?", postUUID).Pluck("tag_id", &tagIds).Error
	if bErr != nil {
		return nil, bErr
	}

	return tagIds, nil
}

func GetTagIdsByPostUUIds(postUUIDs []string) ([]*model.TagPost, error) {
	postTags := []*model.TagPost{}
	bErr := DB.SelectConnect("db_standalonesite").Table((&model.TagPost{}).TableName()).Where("post_uuid in ? and is_del = 0", postUUIDs).Find(&postTags).Error
	return postTags, bErr
}
