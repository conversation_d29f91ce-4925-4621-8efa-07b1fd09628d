package util

import (
	"crypto/rand"
	"fmt"
	"math"
	"math/big"
	"strconv"
	"sync"
	"time"
)

/*
雪花算法（Snowflake Algorithm）是一种分布式唯一 ID 生成算法，由 Twitter 开发。它生成的 ID 是 64 位的整数，通常用于分布式系统中需要生成全局唯一 ID 的场景。雪花算法生成的 ID 包含以下几个部分：

符号位：1 位，始终为 0。
时间戳：41 位，表示从某个时间起的毫秒数。
数据中心 ID：5 位，用于标识数据中心。
机器 ID：5 位，用于标识机器。
序列号：12 位，用于在同一毫秒内生成多个 ID。
雪花算法生成的 ID 是有序的，并且在高并发情况下性能较好。
*/

/*
workerIDBits、datacenterIDBits 和 sequenceBits 定义了各部分的位数。
maxWorkerID、maxDatacenterID 和 maxSequence 定义了各部分的最大值。
workerIDShift、datacenterIDShift 和 timestampLeftShift 定义了各部分的位移量。
twepoch 是一个自定义的纪元时间，用于计算时间戳。
*/

var snowflackObj *Snowflake

const (
	workerIDBits     = 5
	datacenterIDBits = 5
	sequenceBits     = 12

	maxWorkerID     = -1 ^ (-1 << workerIDBits)
	maxDatacenterID = -1 ^ (-1 << datacenterIDBits)
	maxSequence     = -1 ^ (-1 << sequenceBits)

	workerIDShift      = sequenceBits
	datacenterIDShift  = sequenceBits + workerIDBits
	timestampLeftShift = sequenceBits + workerIDBits + datacenterIDBits

	twepoch = int64(1288834974657)
)

type Snowflake struct {
	sync.Mutex    //使用 sync.Mutex 来保证并发安全。
	lastTimestamp int64
	workerID      int64
	datacenterID  int64
	sequence      int64
}

func InitSnowflakeObj() {
	workerID := int64(3)
	datacenterID := int64(3)
	var err error
	snowflackObj, err = NewSnowflake(workerID, datacenterID)
	if err != nil {
		panic(err)
	}
}

func NewSnowflake(workerID, datacenterID int64) (*Snowflake, error) {
	// 检查 workerID 和 datacenterID 是否在有效范围内
	if workerID < 0 || workerID > maxWorkerID {
		return nil, fmt.Errorf("worker ID out of range")
	}
	if datacenterID < 0 || datacenterID > maxDatacenterID {
		return nil, fmt.Errorf("datacenter ID out of range")
	}
	return &Snowflake{
		workerID:     workerID,
		datacenterID: datacenterID,
	}, nil
}

func (s *Snowflake) NextID() (int64, error) {
	s.Lock()
	defer s.Unlock()

	// 使用 time.Now().UnixNano() / int64(time.Millisecond) 获取当前时间戳（毫秒）。
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	// 检查时钟是否倒退，如果倒退则返回错误。
	if timestamp < s.lastTimestamp {
		return 0, fmt.Errorf("clock moved backwards")
	}

	// 如果在同一毫秒内生成多个 ID，则增加序列号。如果序列号达到最大值，则等待下一毫秒。
	if s.lastTimestamp == timestamp {
		s.sequence = (s.sequence + 1) & maxSequence
		if s.sequence == 0 {
			for timestamp <= s.lastTimestamp {
				timestamp = time.Now().UnixNano() / int64(time.Millisecond)
			}
		}
	} else {
		s.sequence = 0
	}

	s.lastTimestamp = timestamp

	id := ((timestamp - twepoch) << timestampLeftShift) |
		(s.datacenterID << datacenterIDShift) |
		(s.workerID << workerIDShift) |
		s.sequence

	return id, nil
}

func randomizeID(id int64) int64 {
	// 使用随机数生成器生成一个随机数
	randNum, err := rand.Int(rand.Reader, big.NewInt(math.MaxInt64))
	if err != nil {
		panic(err)
	}

	// 将随机数与 ID 进行异或操作
	randomizedID := id ^ randNum.Int64()
	return randomizedID
}

func formatID(id int64) string {
	// 将 ID 转换为字符串
	idStr := strconv.FormatInt(id, 10)

	// 如果 ID 长度小于 18 位，则在前面填充 0
	for len(idStr) < 19 {
		idStr = idStr + "0"
	}

	return idStr
}

func CreateSnowflakeID() string {
	if snowflackObj == nil {
		InitSnowflakeObj()
	}
	id, err := snowflackObj.NextID()
	if err != nil {
		panic(err)
	}

	randomizedID := randomizeID(id)
	randomizedIDStr := formatID(randomizedID)
	return randomizedIDStr
}
