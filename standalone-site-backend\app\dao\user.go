package dao

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetUserByUsername(username string) (*model.UserContent, error) {
	var userData model.UserContent
	for i := 0; i < 100; i++ {
		err := DB.SelectConnect("db_standalonesite").Table(fmt.Sprintf("%s_%d", "p_user", i)).Where("username = ? AND is_del = 0", username).First(&userData).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			return nil, err
		}
		break
	}
	// 判断是否是到了最后还是没有数据
	if userData.Model == nil {
		return &userData, gorm.ErrRecordNotFound
	}
	return &userData, nil
}

func GetUserByIntlOpenid(intlOpenid string) (*model.UserContent, error) {
	var user model.UserContent
	uTable, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return nil, err
	}
	db := DB.SelectConnect("db_standalonesite").Table(uTable)

	db = db.Where("intl_openid = ? AND is_del = ?", intlOpenid, 0)

	err = db.First(&user).Error
	if err != nil {
		return &user, err
	}

	return &user, nil
}

// GetUserListByOpenid 根据用户openid获取用户列表
func GetUserListByOpenid(openids []string) ([]*model.UserContent, error) {
	var tableMap = make(map[string][]string)
	var users []*model.UserContent
	for _, openid := range openids {
		tableName, err := (&model.UserContent{}).GetUserSubMeterTable(openid)
		if err != nil {
			return users, err
		}
		tableMap[tableName] = append(tableMap[tableName], openid)
	}
	// 缓冲通道，同一时间最多并发5个查询
	maxConcurrent := 5
	sem := make(chan struct{}, maxConcurrent)
	var wg sync.WaitGroup
	userContentChan := make(chan []*model.UserContent, len(openids))
	errChan := make(chan error, len(openids))
	for tableName, intlOpenids := range tableMap {
		wg.Add(1)
		sem <- struct{}{} // Acquire semaphore
		go func(tableNameStr string, intlOpenidList []string) {
			defer func() {
				recovery.CatchGoroutinePanic(context.Background())
				<-sem
				wg.Done()
			}()
			var userInfos []*model.UserContent
			uErr := DB.SelectConnect("db_standalonesite").Table(tableNameStr).Where("intl_openid in ? AND is_del = 0", intlOpenidList).Find(&userInfos).Error
			if uErr != nil {
				errChan <- uErr
				return
			}
			userContentChan <- userInfos
		}(tableName, intlOpenids)
	}
	go func() {
		recovery.CatchGoroutinePanic(context.Background())
		wg.Wait()
		close(userContentChan)
		close(errChan)
	}()

	for queryErr := range errChan {
		if queryErr != nil {
			return users, queryErr
		}
	}
	for userContentList := range userContentChan {
		users = append(users, userContentList...)
	}
	return users, nil
}

// GetGuildUserListByOpenids 根据用户openid获取公会用户信息列表
func GetGuildUserListByOpenids(openids []string) ([]*model.GuildUserInfo, error) {
	var tableMap = make(map[string][]string)
	var users []*model.GuildUserInfo
	for _, openid := range openids {
		tableName, err := (&model.UserContent{}).GetUserSubMeterTable(openid)
		if err != nil {
			return users, err
		}
		tableMap[tableName] = append(tableMap[tableName], openid)
	}
	// 缓冲通道，同一时间最多并发5个查询
	maxConcurrent := 5
	sem := make(chan struct{}, maxConcurrent)
	var wg sync.WaitGroup
	guildUserInfoChan := make(chan []*model.GuildUserInfo, len(openids))
	errChan := make(chan error, len(openids))
	for tableName, intlOpenids := range tableMap {
		wg.Add(1)
		sem <- struct{}{} // Acquire semaphore
		go func(tableNameStr string, intlOpenidList []string) {
			defer func() {
				recovery.CatchGoroutinePanic(context.Background())
				<-sem
				wg.Done()
			}()
			var userInfos []*model.GuildUserInfo
			uErr := DB.SelectConnect("db_standalonesite").Table(tableNameStr).Where("intl_openid in ? AND is_del = 0", intlOpenidList).Find(&userInfos).Error
			if uErr != nil {
				errChan <- uErr
				return
			}
			guildUserInfoChan <- userInfos
		}(tableName, intlOpenids)
	}
	go func() {
		recovery.CatchGoroutinePanic(context.Background())
		wg.Wait()
		close(guildUserInfoChan)
		close(errChan)
	}()

	for queryErr := range errChan {
		if queryErr != nil {
			return users, queryErr
		}
	}
	for userContentList := range guildUserInfoChan {
		users = append(users, userContentList...)
	}
	return users, nil
}

// GetUserListByOpenid 根据用户openid获取用户列表
func GetUserListByOpenidV2(openids []string, isIgnore bool) ([]*model.UserContent, error) {
	var tableMap = make(map[string][]string)
	var users []*model.UserContent
	for _, openid := range openids {
		tableName, err := (&model.UserContent{}).GetUserSubMeterTable(openid)
		if err != nil {
			return users, err
		}
		tableMap[tableName] = append(tableMap[tableName], openid)
	}
	for tableName, intlOpenids := range tableMap {
		var userInfos []*model.UserContent
		db := DB.SelectConnect("db_standalonesite").Table(tableName)
		if isIgnore {
			db = db.Unscoped()
		}
		uErr := db.Where("intl_openid in ?", intlOpenids).Find(&userInfos).Error
		if uErr != nil {
			// 如果是查询不到数据就进入下一个循环
			if errors.Is(uErr, gorm.ErrRecordNotFound) {
				continue
			}
			return users, uErr
		}
		users = append(users, userInfos...)
	}
	return users, nil
}

func UserCreate(userOwner *model.UserOwner, userInfo *model.UserContent) error {
	// var user model.User
	// err := DB.SelectConnect("db_standalonesite").Table((&userOwner).TableName()).Where("intl_openid = ? AND is_del = 0", intlOpenid).First(&userOwner).Error
	// if err != nil {
	// 	return nil, err
	// }

	err := DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		err := tx.Table(userOwner.TableName()).Create(&userOwner).Error
		if err != nil {
			return err
		}
		tableName, err := (&model.UserContent{}).GetUserSubMeterTable(userInfo.IntlOpenid)
		if err != nil {
			return err
		}
		err = tx.Table(tableName).Create(&userInfo).Error
		if err != nil {
			return err
		}
		return nil
	})

	return err
}

func UserUpdate(userInfo *model.UserContent) error {
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(userInfo.IntlOpenid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("intl_openid = ? AND is_del = ?", userInfo.IntlOpenid, 0).Updates(userInfo).Error
	if err != nil {
		return err
	}
	return nil

}

func UserUpdateConent(intlOpenid, userName, remark, avatar string, setEmptyRemark bool) error {
	currentTime := time.Now().Unix()
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return err
	}
	newUserInfo := map[string]interface{}{}
	if userName != "" {
		newUserInfo["username"] = userName
		newUserInfo["username_on"] = currentTime
	}
	if avatar != "" {
		newUserInfo["avatar"] = avatar
		newUserInfo["avatar_on"] = currentTime
	}
	if setEmptyRemark {
		newUserInfo["remark"] = ""
		newUserInfo["remark_on"] = currentTime
	} else if remark != "" {
		newUserInfo["remark"] = remark
		newUserInfo["remark_on"] = currentTime
	}
	if len(newUserInfo) == 0 {
		return nil
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Updates(newUserInfo).Error
	if err != nil {
		return err
	}
	return nil
}

// UpdateUserAvatar 更新用户头像链接
func UpdateUserAvatar(userInfo *model.UserContent) error {
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(userInfo.IntlOpenid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("intl_openid = ?", userInfo.IntlOpenid).Updates(map[string]interface{}{
		"avatar_on": time.Now().Unix(),
		"avatar":    userInfo.Remark,
	}).Error
	return err
}

// UpdateUserRemark 更新用户个性签名
func UpdateUserRemark(userInfo *model.UserContent) error {
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(userInfo.IntlOpenid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("intl_openid = ?", userInfo.IntlOpenid).Updates(map[string]interface{}{
		"remark_on": time.Now().Unix(),
		"remark":    userInfo.Remark,
	}).Error
	return err
}

// UpdateUserSignPrivacy 更新隐私协议签署状态
func UpdateUserSignPrivacy(intlOpenid string) error {
	// 写分表
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Updates(map[string]interface{}{
		"modified_on":      time.Now().Unix(),
		"has_sign_privacy": 1,
	}).Error
	return err
}

// IsInUserWhiteList 判断用户是否在白名单中
func IsInUserWhiteList(uc *model.UserContext) bool {
	for _, v := range uc.AuditInfo {
		// 判断是否有白名单
		if v.Type == 1 && int64(v.ValidOn) >= time.Now().Unix() {
			return true
		}
	}
	return false
}

// IsInUserAccountBan 判断用户是否在黑名单-用户封禁状态
func IsInUserAccountBan(uc *model.UserContext) bool {
	for _, v := range uc.AuditInfo {
		if v.Type == 2 && v.Status == 3 && int64(v.ValidOn) >= time.Now().Unix() {
			return true
		}
	}
	return false
}

// IsInUserPostBan 判断用户是否在黑名单-动态封禁状态
func IsInUserPostBan(uc *model.UserContext) bool {
	for _, v := range uc.AuditInfo {
		if v.Type == 2 && v.Status == 1 && int64(v.ValidOn) >= time.Now().Unix() {
			return true
		}
	}
	return false
}

// IsInUserCommentBan 判断用户是否在黑名单-评论封禁状态
func IsInUserCommentBan(uc *model.UserContext) bool {
	for _, v := range uc.AuditInfo {
		if v.Type == 2 && v.Status == 2 && int64(v.ValidOn) >= time.Now().Unix() {
			return true
		}
	}
	return false
}

func GetAllUserCountByMap() (map[string]int64, error) {
	var tableCount = make(map[string]int64)
	// 分表计算
	for i := 0; i < 100; i++ {
		var currentCount int64
		var tableName = fmt.Sprintf("p_user_%d", i)
		err := DB.SelectConnect("db_standalonesite").Table(tableName).Count(&currentCount).Error
		if err != nil {
			return tableCount, err
		}
		tableCount[tableName] = currentCount
	}

	return tableCount, nil
}

func BatchSaveUser(users []*model.UserOwner) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.UserOwner{}).TableName()).CreateInBatches(users, 100).Error
}

func BatchSaveUserContent(users []*model.UserContent) error {
	var tableMap = make(map[string][]*model.UserContent)
	for _, uInfo := range users {
		if uInfo.IntlOpenid == "" {
			continue
		}
		tableName, err := (&model.UserContent{}).GetUserSubMeterTable(uInfo.IntlOpenid)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("BatchSaveUser insert data err: %v, user: %v", err, uInfo)
			continue
		}
		tableMap[tableName] = append(tableMap[tableName], uInfo)
	}
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		for tableName, users := range tableMap {
			err := tx.Table(tableName).CreateInBatches(users, len(users)).Error
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("BatchSaveUser batch insert data err: %v, users: %v", err, users)
				return err
			}
		}
		return nil
	})
}

func GetAllUserIteratively(id, limit int, intlGameId string) ([]*model.UserOwner, error) {
	var allUserData []*model.UserOwner
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserOwner{}).TableName()).Where("id > ?", id)
	if intlGameId != "" {
		db = db.Where("intl_gameid = ?", intlGameId)
	}
	if limit > 0 {
		db = db.Limit(limit)
	}
	err := db.Order("id asc").Find(&allUserData).Error

	return allUserData, err
}

// 获取用户隐私协议开关
func GetUserPrivacySwitch(intlOpenid string) (*model.UserPrivacySwitch, error) {
	var userPrivacySwitch model.UserPrivacySwitch
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return nil, err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).First(&userPrivacySwitch).Error
	return &userPrivacySwitch, err
}

// 设置用户开关
func UpdateUserPrivacySwitch(intlOpenid string, data map[string]interface{}) error {
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Debug().Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Updates(&data).Error
	if err != nil {
		return err
	}
	redisKey := cache.GetUserPrivacySwitchCacheKey(intlOpenid)
	redis.GetClient().Del(context.Background(), redisKey)
	return err
}

// 获取用户配置的三方links
func GetUserLinks(intlOpenid string) (*model.UserLinksConfig, error) {
	var userLinks model.UserLinksConfig
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return nil, err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Where("intl_openid = ? ", intlOpenid).First(&userLinks).Error
	return &userLinks, err
}

// 设置第三方links
func SetUserLinks(intlOpenid string, links string) error {
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Debug().Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Update("home_page_links", &links).Error
	return err
}

// 设置用户游戏心情
func SetUserMood(intlOpenid string, mood string) error {
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Debug().Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Update("mood", mood).Error
	return err
}

// 设置用户游戏心情
func SetUserGameTag(intlOpenid string, tag int64) error {
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return err
	}
	err = DB.SelectConnect("db_standalonesite").Table(tableName).Debug().Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Update("game_tag", tag).Error
	return err
}

func SetIntlUserOpenid(intlOpenid string, intlUserOpenid string) error {
	err := DB.SelectConnect("db_standalonesite").Table((&model.UserOwner{}).TableName()).Debug().Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Update("intl_openid2", intlUserOpenid).Error
	return err
}

// 更新用户Shiftyspad隐私开关
func UpdateUserShiftyspadPrivacySwitch(c context.Context, intlOpenid, privacySwitch string) (err error) {
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return err
	}
	updateData := map[string]interface{}{
		"shiftyspad_switch_str": privacySwitch,
	}
	err = DB.SelectConnect("db_standalonesite").WithContext(c).Table(tableName).Debug().Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).Updates(updateData).Error
	return
}

// 获取用户Shiftyspad隐私开关
func GetUserShiftyspadPrivacySwitch(c context.Context, intlOpenid string) (privacySwitchStr string, err error) {
	privacySwitchStr = ""
	tableName, err := (&model.UserContent{}).GetUserSubMeterTable(intlOpenid)
	if err != nil {
		return
	}
	data := &model.UserContent{}
	err = DB.SelectConnect("db_standalonesite").WithContext(c).Table(tableName).Debug().Where("intl_openid = ? AND is_del = ?", intlOpenid, 0).First(data).Error
	if err != nil {
		return
	}
	privacySwitchStr = data.ShiftyspadSwitchStr
	return
}

// 获取用户活动消息隐私开关
func GetUserMsgActivityNotifySwitch(c context.Context, intlOpenids []string) ([]*model.UserMsgActivityNotifyData, error) {
	var userMsgActivityNotify = make([]*model.UserMsgActivityNotifyData, 0)
	var openidTables = make(map[string][]string)
	for _, openid := range intlOpenids {
		tableName, err := (&model.UserContent{}).GetUserSubMeterTable(openid)
		if err != nil {
			return nil, err
		}
		openidTables[tableName] = append(openidTables[tableName], openid)
	}

	for table, openids := range openidTables {
		var userMsgActicityNotifyItem []*model.UserMsgActivityNotifyData
		err := DB.SelectConnect("db_standalonesite").WithContext(c).Table(table).Where("intl_openid in ? AND is_del = ?", openids, 0).Find(&userMsgActicityNotifyItem).Error
		// 屏蔽用户找不到问题
		if err == nil {
			userMsgActivityNotify = append(userMsgActivityNotify, userMsgActicityNotifyItem...)
		}

	}
	return userMsgActivityNotify, nil

}

func GetUserList(limit, ltId int) ([]*model.UserOwner, error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserOwner{}).TableName())
	if ltId > 0 {
		db.Where("id < ?", ltId)
	}
	var userList []*model.UserOwner
	err := db.Unscoped().Order("id desc").Limit(limit).Find(&userList).Error
	if err != nil {
		return nil, err
	}
	return userList, nil
}

// GetUserListBySyncUserState 根据用户openid获取用户列表
func GetUserListBySyncUserState(openids []string, isIgnore bool) ([]*model.UserContentTemp, error) {
	var tableMap = make(map[string][]string)
	var users []*model.UserContentTemp
	for _, openid := range openids {
		tableName, err := (&model.UserContent{}).GetUserSubMeterTable(openid)
		if err != nil {
			return users, err
		}
		tableMap[tableName] = append(tableMap[tableName], openid)
	}
	for tableName, intlOpenids := range tableMap {
		var userInfos []*model.UserContentTemp
		db := DB.SelectConnect("db_standalonesite").Table(tableName)
		if isIgnore {
			db = db.Unscoped()
		}
		uErr := db.Where("intl_openid in ?", intlOpenids).Find(&userInfos).Error
		if uErr != nil {
			// 如果是查询不到数据就进入下一个循环
			if errors.Is(uErr, gorm.ErrRecordNotFound) {
				continue
			}
			return users, uErr
		}
		users = append(users, userInfos...)
	}
	return users, nil
}
