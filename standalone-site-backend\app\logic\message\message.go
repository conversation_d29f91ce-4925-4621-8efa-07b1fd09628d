package message

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"sync"
	"time"

	"trpc.publishing_application.standalonesite/app/config"

	"trpc.publishing_application.standalonesite/app/logic/comment"
	"trpc.publishing_application.standalonesite/app/logic/user"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	redisClient "github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"

	"github.com/spf13/cast"
)

// MAX_WHISPER_NUM_DAILY 当日单用户私信总数限制（TODO 配置化、积分兑换等）
const MAX_WHISPER_NUM_DAILY = 20

type ReadMessageReq struct {
	ID int64 `json:"id" binding:"required"`
}

type WhisperReq struct {
	UserID  int64  `json:"user_id" binding:"required"`
	Content string `json:"content" binding:"required"`
}

type MyMsgResp struct {
	MessageList []*model.MessageFormatedReduce `json:"message_list"`
	TotalNum    int64                          `json:"total_num"`
}

type LIPUserFirstGetPointsReq struct {
	UserID int64 `json:"user_id" binding:"required"`
}

type PushMessageData struct {
	Title   string `json:"title"`
	Content string `json:"Content" binding:"required"`
}

type SiteMessageReq struct {
	GameId     string                     `json:"game_id" binding:"required"` //游戏id
	AreaId     string                     `json:"area_id" binding:"required"` //大区id
	Data       map[string]PushMessageData `json:"data" binding:"required"`    // 推送的多语言数据，key是多语言
	Type       int                        `json:"type" binding:"required"`    //类型PUSHTYPE
	PackageUrl string                     `json:"package_url"`                // 号码包url
	Href       string                     `json:"href"`                       //页面跳转链接
	ID         int64                      `json:"id" binding:"required"`      // 消息id
}

// CreateWhisper 创建私信
func CreateWhisper(c context.Context, msg *model.Message) (*model.Message, error) {
	whisperKey := fmt.Sprintf("WhisperTimes:%s", msg.SenderUserIntlOpenid)

	// 今日频次限制
	if res, _ := redis.GetClient().Get(c, whisperKey).Result(); cast.ToInt(res) >= MAX_WHISPER_NUM_DAILY {
		return nil, errs.NewCustomError(c, code.TooManyWhisperNum, "CreateWhisper | The number of private messages today has reached the upper limit")
	}

	// 创建私信
	err := dao.MessageCreate(msg)
	if err != nil {
		return nil, err
	}

	// 写入当日（自然日）计数缓存
	redis.GetClient().Incr(c, whisperKey).Result()

	currentTime := time.Now()
	endTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 23, 59, 59, 0, currentTime.Location())
	redis.GetClient().Expire(c, whisperKey, endTime.Sub(currentTime))

	return msg, err
}

func GetUnreadCount(openid string) (*pb.UnReadMessageCountRsp, error) {
	// TODO 这里还是需要改成优先读redis，redis没有就读db，每个redis key都需要设置过期时间
	var msgCount = &pb.UnReadMessageCountRsp{
		CommentCount: 0,
		FollowCount:  0,
		LikeCount:    0,
		SiteMsgCount: 0,
	}
	// 改成读取缓存，缓存读取失败了就从数据表中读取
	res := redis.GetClient().HMGet(context.Background(), cache.GetMessageUnReadKey(openid), constants.CommentMessageCount, constants.FollowMessageCount, constants.LikeMessageCount, constants.SiteMessageCount)
	msgCountRes, err := res.Result()
	isNotExistData := false
	for _, item := range msgCountRes {
		if item == nil {
			isNotExistData = true
		}
	}
	// hash表中获取这个方式是更具入参的key来决定顺序的，所以必定会有4个值
	if err != nil || isNotExistData {
		// 查询4中类型的未读类型
		unreadCount, uErr := dao.MessageCountV2(openid)
		if uErr == nil {
			redis.GetClient().HMSet(context.Background(), cache.GetMessageUnReadKey(openid), constants.CommentMessageCount, unreadCount.Comment, constants.FollowMessageCount, unreadCount.Follow, constants.LikeMessageCount, unreadCount.Like, constants.SiteMessageCount, unreadCount.SiteMessage)
			return &pb.UnReadMessageCountRsp{
				CommentCount: int32(unreadCount.Comment),
				FollowCount:  int32(unreadCount.Follow),
				LikeCount:    int32(unreadCount.Like),
				SiteMsgCount: int32(unreadCount.SiteMessage),
			}, uErr
		}
		return &pb.UnReadMessageCountRsp{
			CommentCount: int32(unreadCount.Comment),
			FollowCount:  int32(unreadCount.Follow),
			LikeCount:    int32(unreadCount.Like),
			SiteMsgCount: int32(unreadCount.SiteMessage),
		}, uErr
	}
	for i, count := range msgCountRes {
		if count != nil {
			switch i {
			case 0:
				msgCount.CommentCount = cast.ToInt32(count.(string))
				continue
			case 1:
				msgCount.FollowCount = cast.ToInt32(count.(string))
				continue
			case 2:
				msgCount.LikeCount = cast.ToInt32(count.(string))
				continue
			case 3:
				msgCount.SiteMsgCount = cast.ToInt32(count.(string))
				continue
			}
		}
	}
	return msgCount, nil
}
func ReadMessage(c context.Context, id int64, openid string) error {
	// 获取message
	message, err := dao.MessageGet(id, openid)
	if err != nil {
		return err
	}

	if message.ReceiverUserIntlOpenid != openid {
		return errs.NewCustomError(c, code.NoPermission, "ReadMessage | No permission to perform this request")
	}
	message.IsRead = int(constants.ReadMessage)
	// 已读消息
	err = dao.MessageUpdate(message)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		res := redis.GetClient().Get(context.Background(), cache.GetMessageUnReadKey(openid))
		count, rErr := res.Result()
		if rErr == nil {
			// 只有在查询成功的情况下处理才有效
			var total int64 = 0
			if cast.ToInt64(count)-1 > 0 {
				total = cast.ToInt64(count) - 1
			}
			redis.GetClient().Set(context.Background(), cache.GetMessageUnReadKey(openid), total, 0)
		}

	}()
	return err
}

func ReadMessageAll(openid string, readType int32) error {
	// 加入搜索类型

	searchType := writemessage.GetSearchType(readType)
	// 获取message
	messages, err := dao.MessageList(&dao.MessageConditions{
		ReceiverUserIntlOpenid: openid,
		IsRead:                 "0",
		Type:                   searchType,
	}, 0, 0, openid)
	if err != nil {
		return err
	}
	if len(messages) == 0 {
		return nil
	}
	var messageIds = make([]int64, 0, len(messages))
	var cmsMessageIds = make([]int64, 0)
	for _, message := range messages {
		messageIds = append(messageIds, message.ID)
		if message.CmsMsgID > 0 {
			cmsMessageIds = append(cmsMessageIds, message.CmsMsgID)
		}
	}
	go ReadMessageSetCount0(readType, openid)
	// 已读消息
	message := &model.Message{
		ReceiverUserIntlOpenid: openid,
	}
	go func() {
		// 统计站内信的阅读人数
		if len(cmsMessageIds) > 0 {
			for _, id := range cmsMessageIds {
				redis.GetClient().Incr(context.Background(), cache.GetSiteMessageTotalReadKey(id))
			}

		}
	}()
	go batchReadMessages(message, messageIds)
	// return dao.MessageUpdateIn(message, messageIds)
	return nil
}

func batchReadMessages(message *model.Message, messageIds []int64) {
	defer recovery.CatchGoroutinePanic(context.Background())
	message.IsRead = 1
	// 将messageids 按找500 条一批进行更新
	searchLimit := 500
	for i := 0; i < len(messageIds); i += searchLimit {
		end := i + searchLimit
		if end > len(messageIds) {
			end = len(messageIds)
		}
		batchMessageIds := messageIds[i:end]
		dao.MessageUpdateIn(message, batchMessageIds)
	}
}

func GetMessagesNew(c context.Context, req *pb.GetMessageReq, openID, language string) (*pb.GetMessageRsp, error) {
	rsp := &pb.GetMessageRsp{
		List:     make([]*pb.GetMessageItem, 0),
		PageInfo: &pb.UserPageInfo{},
	}
	searchLimit := 11
	total := 0

	needQueryDB := false
	myMsgRedisKey := cache.GetMyMessageInfosWithLangKey(openID, req.NextPageCursor, int64(searchLimit-1), req.Type, language)
	if myMsgCacheInfo, err := redis.GetClient().Get(c, myMsgRedisKey).Result(); err == nil {
		err = json.Unmarshal([]byte(myMsgCacheInfo), &rsp)
		if err != nil {
			needQueryDB = true
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetMessagesNew json.Unmarshal err: %v", err)
		}
	} else {
		needQueryDB = true
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetMessagesNew redis err: %v", err)
		}
	}

	var err error
	if needQueryDB {
		var messages []*model.Message
		var nextPageCursor, previousPageCursor string
		// 1.分页查询消息表记录
		conditions := &dao.MessageConditions{
			ReceiverUserIntlOpenid: openID,
			Order: []*dao.OrderConditions{
				&dao.OrderConditions{
					Column: "id",
					IsDesc: true,
				},
			},
		}
		searchMsgType := writemessage.GetSearchType(req.Type)
		conditions.Type = searchMsgType
		// 查询类型：下一页数据
		if req.PageType == pb.UserPageType_PAGEDOWN {
			var idCursor int64
			// 如果是首页
			if req.NextPageCursor == "" {
				idCursor = 0
			} else {
				previousPageCursor = req.NextPageCursor
				idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
				if err != nil {
					return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
				}
				// 因为上面是根据id 倒叙排序得，所以这里下面应该是上一页的id
				conditions.LtId = idCursor
			}
			messages, err = dao.MessageListNew(conditions, searchLimit, openID)
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return rsp, nil
				}
				return nil, err
			}
			if len(messages) > 0 {
				total = len(messages)
				if total > (searchLimit - 1) { //判断是否有9条数据，没有的话直接切割会导致panic
					messages = messages[:(searchLimit - 1)] //截取前10条， 第10条的下标是9
				}
				nextPageCursor, err = util.EncryptPageCursorI(messages[len(messages)-1].ID)
				if err != nil {
					return nil, errs.NewCustomError(c, code.GetMessageFailed, "GetMessagesNew | Failed to create comments nextPageCursor")
				}
			}
			if len(messages) == 0 {
				rsp.PageInfo.IsFinish = true
				return rsp, nil
			}
		}

		var wg sync.WaitGroup
		var msgItem = make(chan *pb.GetMessageItem)
		for _, message := range messages {
			wg.Add(1)
			go func(msg *model.Message, searchType int32) {
				defer recovery.CatchGoroutinePanic(context.Background())
				defer wg.Done()
				//var isStar = false
				// 查询是否可以点赞（只能是评论类型的筛选才可以）
				// if searchType == 1 {
				// 	if _, err = comment.GetCommentStarNew(msg.CommentUUID, msg.ReceiverUserIntlOpenid); err == nil {
				// 		isStar = true
				// 	}
				// }
				msgData := &pb.GetMessageItem{
					Id:                     msg.ID,
					Type:                   int32(msg.Type),
					Brief:                  msg.Brief,
					Content:                msg.Content,
					PostUuid:               msg.PostUUID,
					IsRead:                 int32(msg.IsRead),
					CreatedOn:              msg.CreatedOn,
					ModifiedOn:             msg.ModifiedOn,
					AreaId:                 msg.AreaID,
					GameId:                 msg.GameID,
					ExtInfo:                msg.ExtInfo,
					CmsMsgId:               msg.CmsMsgID,
					SenderUserIntlOpenid:   msg.SenderUserIntlOpenid,
					ReceiverUserIntlOpenid: msg.ReceiverUserIntlOpenid,
					IsStar:                 false,
					IsFollow:               false,
					CommentUuid:            msg.CommentUUID,
					ReplyUuid:              msg.ReplyUUID,
					OriginalData:           "",
					OriginalDel:            0,
					CommentDel:             0,
					CommentContent:         "",
					ReplyToReplyUuid:       msg.Reply2ReplyUUID,
					ReplyDel:               0,
					ImgUrl:                 "",
				}
				// 查询发送者的信息,如果是系统下发的不查询用户信息
				if searchType != 4 {
					userInfo, err := dao.GetUserByIntlOpenid(msg.SenderUserIntlOpenid)
					if err != nil {
						log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetMessagesNew GetUserByIntlOpenid err, user_intlopenid:(%s), err=(%v)", msg.SenderUserIntlOpenid, err)
						return
					}
					// 重置认证用户昵称个签多语言
					authUserName, authUserRemark, _ := user.GetUserCerificationUserLanguage(c, userInfo.IntlOpenid, language)
					if authUserName != "" {
						userInfo.Username = authUserName
					}
					if authUserRemark != "" {
						userInfo.Remark = authUserRemark
					}
					msgData.SendUserInfo = formatted.ReturnDynamicProtoUserInfoFormatted(userInfo.Format())
					// 查询是否互相关注（除了站内信之外都要查询是否互相关注）
					if _, err = user.GetUserCollectionUser(msg.ReceiverUserIntlOpenid, msg.SenderUserIntlOpenid, false); err == nil {
						msgData.IsFollow = true
					}
				}
				msgItem <- msgData
			}(message, req.Type)
		}
		go func() {
			defer recovery.CatchGoroutinePanic(context.Background())
			wg.Wait()
			close(msgItem)
		}()

		for item := range msgItem {
			rsp.List = append(rsp.List, item)
		}
		// 重新排序
		sort.Slice(rsp.List, func(i, j int) bool {
			return rsp.List[i].Id > rsp.List[j].Id
		})
		if len(rsp.List) == 0 || total < searchLimit {
			rsp.PageInfo.IsFinish = true
		} else {
			rsp.PageInfo.NextPageCursor = nextPageCursor
		}
		rsp.PageInfo.PreviousPageCursor = previousPageCursor
		msgCacheByte, err := json.Marshal(rsp)
		if err == nil {
			redis.GetClient().SetEX(c, myMsgRedisKey, string(msgCacheByte), 2*time.Minute).Result()
		}
	}
	var wg sync.WaitGroup
	for _, item := range rsp.List {
		wg.Add(1)
		go func(data *pb.GetMessageItem) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done()
			if req.Type == 1 {
				if _, err = comment.GetCommentStarNew(data.CommentUuid, data.ReceiverUserIntlOpenid); err == nil {
					data.IsStar = true
				}
			}
			// 查询消息内容，站内信、关注不需要
			if req.Type == 1 || req.Type == 3 {
				getMsgContentByMsgType(data, language)
			}
		}(item)
	}
	wg.Wait()
	// 获取站内信列表
	if constants.GetMessageType(req.Type) == constants.SiteMessageType {
		var allSiteMsgMap map[int64]*model.SiteMessageWithLangMap
		allSiteMsgMap, err = dao.GetAllSiteMessageMapWithLang()
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetAllSiteMessageMapFailed, "Failed to get site message info")
		}
		for _, rspItem := range rsp.List {
			// 如果是站内信，则直接赋值
			if constants.MsgTypeSiteMessage == constants.MessageT(rspItem.Type) {
				if siteMsgItem, ok := allSiteMsgMap[rspItem.CmsMsgId]; ok {
					if languageItem, ok2 := siteMsgItem.LanguageMap[language]; ok2 {
						rspItem.Brief = languageItem.Title
						rspItem.Content = languageItem.MessageBody
						rspItem.ImgUrl = languageItem.ImgUrl
					} else if languageItem, ok3 := siteMsgItem.LanguageMap["en"]; ok3 {
						rspItem.Brief = languageItem.Title
						rspItem.Content = languageItem.MessageBody
						rspItem.ImgUrl = languageItem.ImgUrl
					}
				}
			}
		}
	}

	// 兜底逻辑，将redis的数据清理
	go ReadMessageSetCount0(req.Type, openID)
	return rsp, nil
}

// 根据消息类型去查询对应的内容, 返回一个id， 一个正文内容即可(ps： 如果查询不到或者查询失败直接返回消息已经被删除了）
func getMsgContentByMsgType(msgData *pb.GetMessageItem, language string) {
	switch msgData.Type {
	case int32(constants.MsgTypeComment):
		post, err := dao.GetPost(msgData.PostUuid)
		if err != nil {
			msgData.OriginalData = ""
			msgData.OriginalDel = 1
		} else {
			content, err := dao.GetPostContentOrOfficialData(msgData.PostUuid, post.IsOfficial, language, true)
			if err != nil {
				msgData.OriginalData = ""
				msgData.OriginalDel = 1
			} else {
				msgData.OriginalData = content.Title
			}
		}
		commentContent, err := comment.GetCommentContent(msgData.CommentUuid)
		if err != nil {
			msgData.Content = ""
			msgData.CommentDel = 1
		} else {
			msgData.Content = commentContent.Content
		}
		return
	case int32(constants.MsgTypeStar):
		post, err := dao.GetPost(msgData.PostUuid)
		if err != nil {
			msgData.OriginalData = ""
			msgData.OriginalDel = 1
		} else {
			content, err := dao.GetPostContentOrOfficialData(msgData.PostUuid, post.IsOfficial, language, true)
			if err != nil {
				msgData.OriginalData = ""
				msgData.OriginalDel = 1
			} else {
				msgData.OriginalData = content.Title
			}
		}
		return
	case int32(constants.MsgTypeReply):
		//var err error

		post, err := dao.GetPost(msgData.PostUuid)
		if err != nil {
			msgData.OriginalData = ""
			msgData.OriginalDel = 1
		} else {
			content, err := dao.GetPostContentOrOfficialData(msgData.PostUuid, post.IsOfficial, language, true)
			if err != nil {
				msgData.OriginalData = ""
				msgData.OriginalDel = 1
			} else {
				msgData.OriginalData = content.Title
			}
		}
		commentContent, err := comment.GetCommentContent(msgData.CommentUuid)
		if err != nil {
			msgData.Content = ""
			msgData.ContentDel = 1
		} else {
			msgData.Content = commentContent.Content
		}
		var replyContent *model.CommentContent
		// 判断是否是回复的回复
		if msgData.ReplyToReplyUuid != "" {
			// 回复的回复
			replyContent, err = comment.GetCommentContent(msgData.ReplyToReplyUuid)
			if err != nil {
				msgData.CommentContent = ""
				msgData.ReplyDel = 1
			}

		} else {
			// 回复评论
			replyContent, err = comment.GetCommentContent(msgData.ReplyUuid)
			if err != nil {
				msgData.CommentContent = ""
				msgData.CommentDel = 1
			}
		}
		if replyContent != nil {
			msgData.CommentContent = replyContent.Content
		}
		return
	case int32(constants.MsgTypeCommentStar):
		commentContent, err := comment.GetCommentContent(msgData.CommentUuid)
		if err != nil {
			msgData.Content = ""
			msgData.CommentDel = 1
		} else {
			msgData.Content = commentContent.Content
		}
		return
	case int32(constants.MsgTypeCommentReplyStar):
		commentContent, err := comment.GetCommentContent(msgData.CommentUuid)
		if err != nil {
			msgData.Content = ""
			msgData.CommentDel = 1
		} else {
			msgData.Content = commentContent.Content
		}
		return
	default:
		return
	}
}

// 消息发送数和接收数同步策略
func SyncSiteMessageStat(c context.Context) error {
	// 分布式锁，只能有一个抢占成功
	redisKey := cache.GetSiteMessageSyncStatCacheNxKey()
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncSiteMessageStat | sync site message stat start, at: %d", time.Now().Unix())
	if ok, _ := redis.GetClient().SetNX(c, redisKey, "1", config.GetConfig().Dynamic.PostMachineReviewTimeoutDuration*time.Second).Result(); ok {
		defer func() {
			recovery.CatchGoroutinePanic(context.Background())
			redis.GetClient().Del(context.Background(), redisKey)
		}()
		// 捞取已经推送完成的数据，然后查询redis
		msgIds, err := dao.GetSiteMessageListByStatus(constants.SiteMessageSent)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncSiteMessageStat | get site message list failed")
			return err
		}
		for _, id := range msgIds {
			sendRedisKey := cache.GetSiteMessageTotalSendKey(id)
			readRedisKey := cache.GetSiteMessageTotalReadKey(id)

			sendCount, err := redis.GetClient().Get(c, sendRedisKey).Result()
			if err != nil && !errors.Is(err, redisClient.Nil) {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncSiteMessageStat | get site message send count failed, redis key:%s,err: %v", sendRedisKey, err)
			}
			readCount, err := redis.GetClient().Get(c, readRedisKey).Result()
			if err != nil && !errors.Is(err, redisClient.Nil) {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncSiteMessageStat | get site message read count failed, redis key:%s,err: %v", readRedisKey, err)
			}
			sendTotal := cast.ToInt64(sendCount)
			readTotal := cast.ToInt64(readCount)
			if sendTotal > 0 || readTotal > 0 {
				err = dao.UpdateSiteMessageStat(id, sendTotal, readTotal)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncSiteMessageStat | udpate site message stat failed, id: %d, sendTotal: %d, readCount: %d,err: %v", id, sendTotal, readTotal, err)
				}
			}

		}

	}
	return nil
}

func ReadMessageSetCount0(readType int32, openid string) {
	defer recovery.CatchGoroutinePanic(context.Background())
	// 阅读全部消息直接把缓存的信息给改成0
	switch readType {
	case int32(constants.CommentMessageType):
		redis.GetClient().HMSet(context.Background(), cache.GetMessageUnReadKey(openid), constants.CommentMessageCount, 0)
		break
	case int32(constants.FollowMessageType):
		redis.GetClient().HMSet(context.Background(), cache.GetMessageUnReadKey(openid), constants.FollowMessageCount, 0)
		break
	case int32(constants.LikeMessageType):
		redis.GetClient().HMSet(context.Background(), cache.GetMessageUnReadKey(openid), constants.LikeMessageCount, 0)
		break
	case int32(constants.SiteMessageType):
		redis.GetClient().HMSet(context.Background(), cache.GetMessageUnReadKey(openid), constants.SiteMessageCount, 0)
		break
	default:
		redis.GetClient().HMSet(context.Background(), cache.GetMessageUnReadKey(openid), constants.CommentMessageCount, 0)
		break
	}
}
