// Package common 公用
package common

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"trpc.publishing_application.standalonesite/app/code"

	"git.code.oa.com/trpc-go/trpc-go"
)

type CommonParams struct {
	GameId string `json:"game_id"`
	AreaId string `json:"area_id"`
	Source string `json:"source"`
}

// GetCommonCtx
func GetCommonCtx() (ctx context.Context) {
	ctx = context.Background()
	return
}

// GetRedisKeyWithEnv 获取缓存key，按环境区分
func GetRedisKeyWithEnv(key string) (redisKey string) {
	globalConfig := trpc.GlobalConfig()
	prefix := fmt.Sprintf("%v_%v_%v", globalConfig.Server.App, globalConfig.Server.Server, globalConfig.Global.EnvName)
	redisKey = fmt.Sprintf("%v_%v", prefix, key)
	return
}

// GetRedisKeyWithoutEnv 获取缓存key，不区分环境
func GetRedisKeyWithoutEnv(key string) (redisKey string) {
	globalConfig := trpc.GlobalConfig()
	prefix := fmt.Sprintf("%v_%v", globalConfig.Server.App, globalConfig.Server.Server)
	redisKey = fmt.Sprintf("%v_%v", prefix, key)
	return
}

func JsonDecodeHeaderCommonParams(params string) (*CommonParams, error) {
	var commonParams CommonParams
	err := json.Unmarshal([]byte(params), &commonParams)
	if err != nil {
		return nil, err
	}
	return &commonParams, nil
}

// 根据key去获取x-common-params
func GetCommonValue(ctx context.Context) (*CommonParams, error) {
	header := metadata.GetHTTPHeader(ctx)
	source := header.Get("X-Common-Params")
	if source == "" {
		err := errs.NewCustomError(ctx, code.GetXCommonParamsIsEmptyErr, "ConvertSource header params not x-common-params is empty")
		return nil, err
	}
	commonParams, err := JsonDecodeHeaderCommonParams(source)
	if err != nil {
		err = errs.NewCustomError(ctx, code.GetXCommonParamsDeCodeErr, "ConvertSource header params json decode error, err: %v", err)
		return nil, err
	}
	return commonParams, nil
}
