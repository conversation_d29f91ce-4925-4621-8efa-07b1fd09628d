package comment

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/model"
)

// CreateEmptyCommentState 创建一个空的评论数据，防止查询不到的结果
func CreateEmptyCommentState(c context.Context, commentUuid string) error {
	err := dao.CreateCommentState(commentUuid)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("Create empty comment state failed, err:%v", err)
	}
	return err
}

// UpdateCommentReplyCount 调用CMS-TOC接口更新评论回复数
func UpdateCommentReplyCount(c context.Context, commentState *model.CommentState) error {
	err := dao.CommentStateUpdateReplyCount(commentState)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UpdateCommentReplyCount comment content update failed. err: %v, commentstateInfo: %+v", err, commentState)
		return err
	}
	return nil
}
