module trpc.publishing_application.standalonesite

go 1.23

require (
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/database v0.0.15
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs v0.1.2
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter v0.0.15
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata v1.0.18
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/report v1.0.6
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/util v0.0.2
	git.code.oa.com/trpc-go/trpc-config-tconf v0.1.9
	git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.6
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.4
	git.code.oa.com/trpc-go/trpc-filter/validation v0.1.3
	git.code.oa.com/trpc-go/trpc-go v0.16.0
	git.code.oa.com/trpc-go/trpc-log-cls v0.0.0-**************-3be80a92c1fc
	git.code.oa.com/trpc-go/trpc-metrics-prometheus v0.1.8
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.3.3
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.5.19
	git.code.oa.com/trpc-go/trpc-opentracing-skywalking v0.2.0
	git.code.oa.com/trpcprotocol/publishing_marketing/account_intlgame v1.1.25
	git.code.oa.com/trpcprotocol/publishing_marketing/game v1.1.29
	git.woa.com/trpcprotocol/publishing_application/lipass_points v1.2.16
	git.woa.com/trpcprotocol/publishing_application/stand_alone_site_game_game v1.1.63
	git.woa.com/trpcprotocol/publishing_application/stand_alone_site_game_tools v1.1.25
	git.woa.com/trpcprotocol/publishing_application/standalonesite_activity v1.1.8
	git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics v1.3.67
	git.woa.com/trpcprotocol/publishing_application/standalonesite_monitor v1.1.6
	git.woa.com/trpcprotocol/publishing_application/standalonesite_user v1.2.31
	git.woa.com/wegame_app_go/common/trpc-config-local v0.1.2
	github.com/IBM/sarama v1.43.1
	github.com/aws/aws-sdk-go-v2 v1.30.5
	github.com/aws/aws-sdk-go-v2/config v1.27.33
	github.com/aws/aws-sdk-go-v2/credentials v1.17.32
	github.com/aws/aws-sdk-go-v2/service/s3 v1.61.2
	github.com/aws/aws-sdk-go-v2/service/sts v1.30.7
	github.com/fsnotify/fsnotify v1.7.0
	github.com/go-redis/redis/v8 v8.11.5
	github.com/gofrs/uuid v4.3.1+incompatible
	github.com/jinzhu/copier v0.4.0
	github.com/olivere/elastic/v7 v7.0.32
	github.com/pkg/errors v0.9.1
	github.com/spf13/cast v1.5.1
	github.com/spf13/viper v1.13.0
	github.com/stretchr/testify v1.9.0
	github.com/tencentyun/cos-go-sdk-v5 v0.7.63
	github.com/tencentyun/qcloud-cos-sts-sdk v0.0.0-20230815133100-78b611a90975
	github.com/thoas/go-funk v0.9.3
	github.com/xuri/excelize/v2 v2.9.0
	go.uber.org/automaxprocs v1.4.0
	gorm.io/gorm v1.24.1
	gorm.io/plugin/soft_delete v1.2.1
)

require (
	cloud.google.com/go/compute v1.6.1 // indirect
	git.code.oa.com/trpc-go/trpc v0.1.2 // indirect
	git.code.oa.com/trpc-go/trpc-selector-dsn v0.2.0 // indirect
	git.woa.com/trpcprotocol/gpts_community/nikke_common v1.1.41 // indirect
	git.woa.com/trpcprotocol/publishing_marketing/logicial_record v1.1.12 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/eapache/go-resiliency v1.6.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/googleapis/gax-go/v2 v2.4.0 // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.4 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/xuri/efp v0.0.0-20240408161823-9ad904a10d6d // indirect
	github.com/xuri/nfp v0.0.0-20240318013403-ab9948c2c4a7 // indirect
	go.opencensus.io v0.23.0 // indirect
	golang.org/x/crypto v0.28.0 // indirect
	golang.org/x/oauth2 v0.0.0-20220411215720-9780585627b5 // indirect
	golang.org/x/time v0.3.0 // indirect
	google.golang.org/appengine v1.6.7 // indirect
)

require (
	git.code.oa.com/devsec/protoc-gen-secv v0.3.4 // indirect
	git.code.oa.com/goom/mocker v0.3.7 // indirect
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/constants v0.0.7 // indirect
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient v0.0.22
	git.code.oa.com/iegg_distribution/Marketing_group/act.common/metric v0.0.5 // indirect
	git.code.oa.com/polaris/polaris-go v0.12.12 // indirect
	git.code.oa.com/trpc-go/trpc-database/kafka v0.2.23
	git.code.oa.com/trpcprotocol/publishing_marketing/account v1.1.22 // indirect
	git.code.oa.com/trpcprotocol/tconf/tconfserver v1.1.2 // indirect
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.4 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.3 // indirect
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	git.woa.com/trpc-go/tnet v0.0.15 // indirect
	git.woa.com/wegame_app_global/common/config-tconf v0.1.3 // indirect
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/SkyAPM/go2sky v1.3.0 // indirect
	github.com/andybalholm/brotli v1.0.4 // indirect
	github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream v1.6.4 // indirect
	github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.16.13 // indirect
	github.com/aws/aws-sdk-go-v2/internal/configsources v1.3.17 // indirect
	github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.6.17 // indirect
	github.com/aws/aws-sdk-go-v2/internal/ini v1.8.1 // indirect
	github.com/aws/aws-sdk-go-v2/internal/v4a v1.3.17 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.11.4 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/checksum v1.3.19 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.11.19 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/s3shared v1.17.17 // indirect
	github.com/aws/aws-sdk-go-v2/service/sso v1.22.7 // indirect
	github.com/aws/aws-sdk-go-v2/service/ssooidc v1.26.7 // indirect
	github.com/aws/smithy-go v1.20.4 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.1.2 // indirect
	github.com/clbanning/mxj v1.8.4 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/go-playground/form/v4 v4.2.0 // indirect
	github.com/go-sql-driver/mysql v1.6.0 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/flatbuffers v2.0.0+incompatible // indirect
	github.com/google/go-querystring v1.0.0 // indirect
	github.com/google/uuid v1.1.2 // indirect
	github.com/hashicorp/errwrap v1.0.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12
	github.com/klauspost/compress v1.17.7 // indirect
	github.com/lestrrat-go/strftime v1.0.6 // indirect
	github.com/magiconair/properties v1.8.6 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.1 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mozillazg/go-httpheader v0.2.1 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/panjf2000/ants/v2 v2.4.6
	github.com/pelletier/go-toml v1.9.5 // indirect
	github.com/pelletier/go-toml/v2 v2.0.5 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/prometheus/client_golang v1.11.1 // indirect
	github.com/prometheus/client_model v0.2.0 // indirect
	github.com/prometheus/common v0.26.0 // indirect
	github.com/prometheus/procfs v0.6.0 // indirect
	github.com/smartystreets/goconvey v1.8.0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/afero v1.8.2 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/subosito/gotenv v1.4.1 // indirect
	github.com/tencentcloud/tencentcloud-cls-sdk-go v1.0.11 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.43.0 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.6.0 // indirect
	go.uber.org/zap v1.24.0 // indirect
	golang.org/x/net v0.30.0
	golang.org/x/sync v0.8.0 // indirect
	golang.org/x/sys v0.26.0 // indirect
	golang.org/x/text v0.19.0 // indirect
	google.golang.org/api v0.81.0
	google.golang.org/genproto v0.0.0-20220519153652-3a47de7e79bd // indirect
	google.golang.org/grpc v1.46.2 // indirect
	google.golang.org/protobuf v1.30.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.4.3 // indirect
	skywalking.apache.org/repo/goapi v0.0.0-20210401062122-a049ca15c62d // indirect
)

replace google.golang.org/protobuf v1.30.0 => google.golang.org/protobuf v1.25.0
