package formatted

import (
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"trpc.publishing_application.standalonesite/app/model"
)

func ReturnProtoMessageInfoData(message *model.MessageFormatedReduce) *pb.GetMessageItem {
	return &pb.GetMessageItem{
		Id:           message.ID,
		SendUserInfo: ReturnDynamicProtoUserInfoFormatted(message.SenderUser),
		Type:         int32(message.Type),
		Brief:        message.Brief,
		Content:      message.Content,
		//PostUuid:               message.PostUUID,
		IsRead:                 int32(message.IsRead),
		CreatedOn:              message.CreatedOn,
		ModifiedOn:             message.ModifiedOn,
		AreaId:                 message.AreaId,
		GameId:                 message.GameId,
		ExtInfo:                message.ExtInfo,
		CmsMsgId:               message.CmsMsgID,
		SenderUserIntlOpenid:   message.SenderUserIntlOpenid,
		ReceiverUserIntlOpenid: message.ReceiverUserIntlOpenid,
	}
}
