package util

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParallelError(t *testing.T) {
	size := 3
	// nil
	tasks := []func() error{
		func() error {
			return nil
		},
		func() error {
			return nil
		},
	}
	err := Parallel(size, tasks)
	assert.Nil(t, err)

	// not nil
	tasks = append(tasks, func() error {
		return fmt.Errorf("task error")
	})
	err = Parallel(size, tasks)
	assert.NotNil(t, err)
}

func TestParallelPanic(t *testing.T) {
	size := 3
	// nil
	tasks := []func() error{
		func() error {
			return nil
		},
		func() error {
			return nil
		},
	}

	// panic
	tasks = append(tasks, func() error {
		panic("task panic")
	})

	err := Parallel(size, tasks)
	assert.NotNil(t, err)
}

func TestParallelSlice(t *testing.T) {
	// deal slice item in parallel
	slice := [...]int{0, 1, 2}
	tasks := make([]func() error, 0)
	for i := range slice {
		task := func(i int) func() error {
			return func() error {
				// incr slice item
				slice[i]++
				return nil
			}
		}
		tasks = append(tasks, task(i))
	}
	// nil
	err := Parallel(3, tasks)
	assert.Nil(t, err)
	// equal
	for i, item := range slice {
		assert.Equal(t, item, i+1)
	}
}

func TestParallelData(t *testing.T) {
	type Data struct {
		Id   int
		Name string
	}
	length := 3
	datas := make([]*Data, length)
	// deal struct data in parallel
	tasks := make([]func() error, 0)
	for i := 0; i < length; i++ {
		task := func(i int, data *Data) func() error {
			return func() error {
				data.Id = i
				data.Name = fmt.Sprintf("%d", i)
				return nil
			}
		}
		datas[i] = &Data{}
		tasks = append(tasks, task(i, datas[i]))
	}
	// nil
	err := Parallel(3, tasks)
	assert.Nil(t, err)
	// equal
	for i := 0; i < length; i++ {
		assert.Equal(t, i, datas[i].Id)
		assert.Equal(t, fmt.Sprintf("%d", i), datas[i].Name)
	}
}
