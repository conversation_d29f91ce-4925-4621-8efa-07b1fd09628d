package lip

import (
	"context"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pointsPb "git.woa.com/trpcprotocol/publishing_application/lipass_points"
	"time"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/model/lip"
	"trpc.act.logicial/app/util"
)

var (
	pointsProxy = pointsPb.NewPointsClientProxy()
)

type TaskPoints struct {
	TaskUuids    []string
	IntegralType int32
	LipOpenid    string
	GameOpenid   string
	IntlGameId   int64
}

// QueryTaskCompletionStatus 查询任务完成状态
func (r *TaskPoints) QueryTaskCompletionStatus(ctx context.Context) ([]*pointsPb.TaskStatusItem, error) {

	tasksStatus, err := pointsProxy.CheckTasksStatus(ctx, &pointsPb.CheckTasksStatusReq{
		TaskUuids:    r.TaskUuids,
		IntegralType: r.IntegralType,
		LipOpenid:    r.LipOpenid,
		IntlGameId:   r.IntlGameId,
	})
	if err != nil {
		return nil, err
	}
	log.DebugContextf(ctx, "CompleteTaskAddPoints AddPointWhileCompletedCommonTask end;TaskUuids:[%v], LipOpenid[%v], LipOpenid[%v]",
		r.TaskUuids, r.LipOpenid, tasksStatus.GetTasks())
	return tasksStatus.GetTasks(), nil

}

// CompleteTaskAddPoint 完成任务添加积分
func (r *TaskPoints) CompleteTaskAddPoint(ctx context.Context) (int32, error) {
	if len(r.TaskUuids) == 0 {
		return 0, errs.NewCustomError(ctx, code.TaskPointsParamErr, "Task Uuids Missing")
	}

	serial := util.NewUUIDReplace()
	rsp, err := pointsProxy.AddPointWhileCompletedCommonTask(ctx, &pointsPb.AddPointWhileCompletedCommonTaskReq{
		LipOpenid:  r.LipOpenid,
		TaskUuid:   r.TaskUuids[0],
		IntlGameId: r.IntlGameId,
		Serial:     serial,
		GameOpenid: r.GameOpenid,
	})
	if err != nil {
		log.ErrorContextf(ctx, "CompleteTaskAddPoints AddPointWhileCompletedCommonTask;serial:[%v] err:[%v]",
			serial, err)
		return 0, err
	}
	log.DebugContextf(ctx, "CompleteTaskAddPoints AddPointWhileCompletedCommonTask end;serial:[%v], status[%v]",
		serial, rsp.GetStatus())
	return rsp.GetStatus(), nil
}

func ReissueUserPoints(ctx context.Context, taskId int32) error {
	log.WithFieldsContext(ctx, "log_type", "send_points_star").
		Debugf("ReissueUserPoints star; task:[%v]", taskId)
	records, err := getUnsentRecords(ctx)
	if err != nil {
		return err
	}

	for _, v := range records {
		db := DB.DefaultConnect().WithContext(ctx).Debug().Table(lip.IntegralTransferErrorRecord{}.TableName()).Begin()
		//  修改数据库记录
		update := db.Where("id = ?", v.ID).Update("status", 1)
		if update.Error != nil || update.RowsAffected == 0 {
			db.Rollback()
			log.WithFieldsContext(ctx, "log_type", "db_update_err").Errorf(
				"ReissueUserPoints Update status err; id:(%d)", v.ID)
			continue
		}
		var userRefundedCoinsRsp *pointsPb.UserRefundedCoinsRsp
		now := time.Now().Unix()
		userRefundedCoinsRsp, err = pointsProxy.UserRefundedCoins(ctx, &pointsPb.UserRefundedCoinsReq{
			TaskId:      int64(taskId),
			IntlOpenids: []string{v.OpenID},
			SerialId:    v.Seq,
			Coin:        int64(v.PointsNum),
			EventTime:   now,
			AddCoinType: 1,
		})
		if err != nil {
			db.Rollback()
			return err
		}
		if userRefundedCoinsRsp.Status == pointsPb.Status_FAILURE {
			log.WithFieldsContext(ctx, "log_type", "userRefundedCoins_err").Errorf(
				"UserRefundedCoins send points err; id:(%d)", v.ID)
			db.Rollback()
			continue
		}
		db.Commit()
		log.WithFieldsContext(ctx, "log_type", "send_points_suc").Infof(
			"ReissueUserPoints send points suc; id:(%d)", v.ID)
	}
	return nil
}

// 查询未发送的记录
func getUnsentRecords(ctx context.Context) ([]lip.IntegralTransferErrorRecord, error) {

	var records []lip.IntegralTransferErrorRecord
	db := DB.DefaultConnect().WithContext(ctx).Debug().Table(lip.IntegralTransferErrorRecord{}.TableName())
	result := db.Where("status = ?", 0).Find(&records)
	if result.Error != nil {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", result.Error)
	}

	return records, nil
}
