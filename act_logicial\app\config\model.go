package config

import (
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/tglog"
)

// Config 统一配置
type Config struct {
	PolarisConf struct {
		Host string
	}
	LIPBandingHOK2504Conf struct {
		TaskMetricsService                string
		TaskMetricsNamespace              string
		TaskMetricsPath                   string
		TaskMetricsActivityID             string
		TaskMetricsXBid                   string
		TaskMetricsCaller                 string
		CumulativeLoginTargetID           string // 登录游戏
		CumulativeTeamPlayTargetID        string // 累计组队对局数
		CumulativeAddedFriendTargetID     string // 新增好友数
		HOKClusterProxyHost               string // HOK集群host
		HOKClusterProxyPath               string // 代理路径
		BandingLIPTaskUUID                string
		CumulativeLoginTaskUUID           string
		CumulativeAddedFriendTaskUUID     string
		CumulativeTeamPlayEventTaskUUID01 string
		CumulativeTeamPlayEventTaskUUID02 string
		CumulativeTeamPlayEventTaskUUID03 string
		CumulativeTeamPlayEventTaskUUID04 string
		CompletedMatchesNum01             int32
		CompletedMatchesNum02             int32
		CompletedMatchesNum03             int32
		CompletedMatchesNum04             int32
	}
	Crypto                    string
	PubgmHttpHost             string
	TarisCheckReservationHost string
	TarisCheckReservationUri  string
	NikkeAnniversaryBotUrl    string
	NikkeRedHoodVotesBotUrl   string
	ReservationMap            map[string]URLConfig
	RegionCodeUrlMap          map[string]string
	TGlog                     map[string]tglog.TGlogConfig
	NikkeLecherKill           struct {
		SinKey string
		Source string
		Host   string
		ActId  string
	}
	Nikke2Point5Anniversary struct {
		StartKafka     bool
		BotUrl         string
		VoteRoleList   map[string]string
		SpecialVoteMap map[string]string
	}
	LipNikke2Point struct {
		StartKafka bool
	}
	HOKMotorcycleEvents struct {
		MaxAttempts                    int    // 最大开奖次数
		SpecifyWinningUID              string // test 指定开奖UID
		RunningHorseLightsRecordNumber int    // 跑马灯记录数量限制
		DrawingTime                    int    // 开奖时间
		DrawingTimeZone                int    // 开奖时区
		CapturePlayerDimensionHost     string // 玩家维度
		CapturePlayerDimensionPath     string // 玩家维度
		GetTaskCompleteStatusService   string // 北极星
		GetTaskCompleteStatusNamespace string // 北极星
		GetTaskCompleteStatusPath      string // 任务完成状态
		GetTaskCompleteStatusAccType   string
		TaskGroupId                    int
		ActivityStartTimeStamp         int64
		ExchangeUserTypeMap            map[int]int
		TagIdClaimedMap                map[int]int
		TaskIdTagIdMap                 map[int]int
		TaskIdLuckNumMap               map[int]int
		PageTagIds                     []int
		WriteRunningLanternGiftList    []struct {
			PresentId    string
			LotteryIndex string
		}
		V2ProductItemMap        map[int32]MotorcycleProductItem
		SkinEmoteCommodityIdMap map[int32]int32
		HeroSkinCmdId           int32
		EmoteCmdId              int32
	}
	HOKEventsClub struct {
		AccessKey string
		SecretKey string
		Host      string
		Path      string
		Env       string
	}

	PrizeList []struct {
		Name      string
		Condition int
		Num       int
	}
	Dragonact0704CheckList  map[string]int32
	NikkeVoteRoleList       map[string]string
	NikkeDataHost           string
	DFCommonLibraryListUrl  string
	HazardOperationRankName string
	HavocWarfareRankName    string
	HokTempUpRankUrl        string
	HokWhatsAppUrl          string
	ContentSecurityTextUri  string
	Authorization           string
	GameSecretMap           map[string]SecretConfig
	PriorityList            []struct {
		Priority int
		Keyword  string
	}
	NikkeValentineNoCheckMakeDate string
	NikkeSpecialVote              []struct {
		Day     int
		VoteMap map[string]string
	}
	TarisGetClearInstanceUrl string
	TarisTaskUrl             string
	TarisDragonGetDataUrl    string
	TarisAreaIDMap           [][]int32
	TarisDragon1000Url       string
	TarisDragonTop30Url      string
	// 拼枪活动枪械id对应需要的助力人数
	GunIdHelpNumMap          map[string]int32
	GunActHelpUrl            string            // 拼枪活动助力链接
	GunActTaskExpireTime     int32             // 拼枪活动任务助力过期时间，单位秒
	GunActUserHelpLimitNum   int32             // 拼枪活动用户助力次数限制
	AmsAppId                 string            // ams应用id
	GameNames                map[string]string // 游戏名称
	GameAreaIds              map[string]int64  // 游戏大区id
	GunActAmsId              string            // 拼枪活动ams活动id
	GunActGunAmsGroupIds     map[string]string // 拼枪活动枪械奖品对应的ams组id
	NeedLoadGunActConf       bool              // 是否需要加载拼枪活动配置
	GunActStartSendPrizeTime uint32            // 拼枪启动开始发奖时间
	GunActTaskCheckLockTime  int64             // 拼枪活动任务检查加锁时长，单位秒
	GunActTaskCheckLimitNum  int               // 拼枪活动任务检查每次处理个数
	CredentialsConfig        map[string]string // p2 google bigquery配置
	DfSteamGATransfer        DfSteamGATransferConfig
	// UserRefundedCoinsTaskId  int64
	CagNewRoleAct struct { // cag游戏新建角色活动配置
		CheckPrizeSendLockTime        int64  // 检查奖品发放加锁时长，单位秒
		CheckPrizeSendLimitNum        int    // 每次检查奖品发放每次处理个数
		CheckCreateRoleNum            int    // 每次检查创建角色个数
		ActStartTimeStamp             int64  // 活动开始时间戳
		ActEndTimeStamp               int64  // 活动结束时间戳
		NotCheckCreateRoleAfterActEnd int    // 活动结束多少天后不检查创建角色
		ActivityId                    string // 活动id
		AbAreaId                      string // ab游戏大区id
		CagAreaId                     string // cag游戏大区id
		RolePrizeAmsId                string // 角色奖励ams单号
		RolePrizeGroupId              string // 角色奖励礼包组id
		LevelPrizeAmsId               string // 等级奖励ams单号
		Level10PrizeGroupId           string // 10级奖励礼包组id
		Level20PrizeGroupId           string // 20级奖励礼包组id
		Level30PrizeGroupId           string // 30级奖励礼包组id
		PressureTest                  bool   // 是否压测
	}
}

// DfSteamGATransferConfig TODO
type DfSteamGATransferConfig struct {
	CountryList []string // df steam ga 转移国家列表
	EmailConfig DfSteamGATransferEmailList
}

// DfSteamGATransferEmailList TODO
type DfSteamGATransferEmailList struct {
	Url          string
	Sender       string
	SenderAlias  string // 发送人别名
	Passwd       string
	ReceiverList []string
	BccList      []string
	Host         string
	Port         int32
	Title        string
	Content      string
	FileList     []DfSteamGATransferEmailFile
}

// DfSteamGATransferEmailFile TODO
type DfSteamGATransferEmailFile struct {
	FileName string
	FilePath string
}

// SecretConfig TODO
type SecretConfig struct {
	ContentSecurityHost string
	SecretId            string
	SecretKey           string
	ExcludeWords        []string
}

// URLConfig 路径配置
type URLConfig struct {
	Host string
	Uri  string
}

// MotorcycleProductItem TODO
type MotorcycleProductItem struct {
	ExchangeLimit int32 // 兑换限制
	ExpendNum     int32 // 消耗数量
}
