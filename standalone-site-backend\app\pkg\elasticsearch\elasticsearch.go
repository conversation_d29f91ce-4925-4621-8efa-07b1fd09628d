package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"

	"trpc.publishing_application.standalonesite/app/config"

	ES "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/elasticsearch"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/constants"
)

type ElasticSearchIndex struct {
	Mappings *ElasticSearchIndexMappings `json:"mappings"`
	Settings *ElasticSearchIndexSettings `json:"settings"`
}

type ElasticSearchIndexSettings struct {
	Index *ElasticSearchIndexSettingsIndex `json:"index"`
}

type ElasticSearchIndexSettingsIndex struct {
	RefreshInterval string                      `json:"refresh_interval"`
	Analysis        *ElasticSearchIndexAnalysis `json:"analysis"`
}

type ElasticSearchIndexNoAnalysis struct {
	Mappings *ElasticSearchIndexMappings           `json:"mappings"`
	Settings *ElasticSearchIndexSettingsNoAnalysis `json:"settings"`
}
type ElasticSearchIndexSettingsNoAnalysis struct {
	Index *ElasticSearchIndexSettingsNoAnalysisIndex `json:"index"`
}

type ElasticSearchIndexSettingsNoAnalysisIndex struct {
	RefreshInterval string `json:"refresh_interval"`
}

type ElasticSearchIndexAnalysis struct {
	Analyzer *ElasticSearchIndexProperty `json:"analyzer"`
}
type ElasticSearchIndexMappings struct {
	Properties *ElasticSearchIndexProperty `json:"properties"`
}

type ElasticSearchIndexProperty map[string]interface{}

type ElasticSearchIndexPropertyT struct {
	Type string `json:"type"`
}

type ElasticSearchIndexPropertyWithAnalyzerT struct {
	Type     string `json:"type"`
	Analyzer string `json:"analyzer"`
}

// 创建索引
func CreateIndex() bool {
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		createTweetIndex()
		createTweetAuditIndex()
		createUserAuditIndex()
		createReportIndex()
		createUserIndex()
		createCommentIndex()
	}()
	return true
}

func createTweetIndex() bool {
	esIndex := config.GetConfig().ElasticSearchSetting.TweetIndex
	ctx := context.Background()
	exists, err := ES.SelectConnect("es_standalonesite").IndexExists(esIndex).Do(ctx)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search IndexExists error:%v", err)
		return false
	}
	if !exists {
		data := &ElasticSearchIndex{
			Settings: &ElasticSearchIndexSettings{
				Index: &ElasticSearchIndexSettingsIndex{
					RefreshInterval: "5s",
					Analysis: &ElasticSearchIndexAnalysis{
						Analyzer: &ElasticSearchIndexProperty{

							"english_analyzer": map[string]interface{}{
								"type": "standard",
							},
							"korean_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "nori_tokenizer",
								"filter": []string{
									"lowercase",
									"nori_part_of_speech",
								},
							},
							"japanese_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "kuromoji_tokenizer",
								"filter": []string{
									"lowercase",
									"kuromoji_part_of_speech",
								},
							},
							"zh_hans_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "ik_max_word",
							},
							"zh_hant_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "ik_max_word",
							},
						},
					},
				},
			},
			Mappings: &ElasticSearchIndexMappings{
				Properties: &ElasticSearchIndexProperty{
					"id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"post_uuid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"plate_id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"creatorhub_task_id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"creatorhub_rank_id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"intl_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"intl_user_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"language": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"title": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"content": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"content_summary": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"all_content_lang_en": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "english_analyzer",
					},
					"all_content_lang_ja": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "japanese_analyzer",
					},
					"all_content_lang_ko": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "korean_analyzer",
					},
					"all_content_lang_zh": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "zh_hans_analyzer",
					},
					"all_content_lang_zh-TW": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "zh_hant_analyzer",
					},
					"pic_urls": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"comment_count": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"collection_count": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"upvote_map": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"browse_count": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"forward_count": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"power_num": &ElasticSearchIndexPropertyT{
						Type: "float",
					},
					"visibility": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"is_top": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"top_sort": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"top_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"is_essence": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"essence_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"is_original": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"original_url": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"original_reprint": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"tags": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"post_languages": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"socialmedia_post_id": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"guild_id": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"latest_replied_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"created_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"modified_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"hot_num": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"game_id": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"area_id": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"is_audit": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"platform": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"is_del": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"deleted_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"ext_info": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"text_risk_level": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"text_risk_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"pic_risk_level": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"pic_risk_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"audit_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"audit_user": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"audit_introduce": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"audit_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"created_on_ms": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"publish_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"is_official": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"machine_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"artificial_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"del_reason": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"del_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"is_hide": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"demotion_num": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
				},
			},
		}
		esIndexBody, err := json.Marshal(data)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search Marshal error:%v", err)
		}
		_, err = ES.SelectConnect("es_standalonesite").CreateIndex(esIndex).BodyString(string(esIndexBody)).Do(ctx)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search CreateIndex error:%v", err)
			return false
		}
	}
	return true
}

func createTweetAuditIndex() bool {
	esIndex := config.GetConfig().ElasticSearchSetting.TweetAuditIndex
	ctx := context.Background()
	exists, err := ES.SelectConnect("es_standalonesite").IndexExists(esIndex).Do(ctx)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search IndexExists error:%v", err)
		return false
	}
	if !exists {
		data := &ElasticSearchIndex{
			Settings: &ElasticSearchIndexSettings{
				Index: &ElasticSearchIndexSettingsIndex{
					RefreshInterval: "5s",
					Analysis: &ElasticSearchIndexAnalysis{
						Analyzer: &ElasticSearchIndexProperty{

							"english_analyzer": map[string]interface{}{
								"type": "standard",
							},
							"korean_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "nori_tokenizer",
								"filter": []string{
									"lowercase",
									"nori_part_of_speech",
								},
							},
							"japanese_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "kuromoji_tokenizer",
								"filter": []string{
									"lowercase",
									"kuromoji_part_of_speech",
								},
							},
							"zh_hans_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "ik_max_word",
							},
							"zh_hant_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "ik_max_word",
							},
						},
					},
				},
			},
			Mappings: &ElasticSearchIndexMappings{
				Properties: &ElasticSearchIndexProperty{
					"id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"post_action_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"post_uuid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"plate_id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"intl_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"intl_user_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"language": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"title": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"content": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"content_summary": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"all_content_lang_en": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "english_analyzer",
					},
					"all_content_lang_ja": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "japanese_analyzer",
					},
					"all_content_lang_ko": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "korean_analyzer",
					},
					"all_content_lang_zh": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "zh_hans_analyzer",
					},
					"all_content_lang_zh-TW": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "zh_hant_analyzer",
					},
					"pic_urls": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"tags": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"created_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"modified_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"game_id": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"area_id": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"platform": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"is_del": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"deleted_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"ext_info": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"text_risk_level": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"text_risk_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"pic_risk_level": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"pic_risk_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"audit_user": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"audit_introduce": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"audit_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"machine_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"artificial_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
				},
			},
		}
		esIndexBody, err := json.Marshal(data)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search Marshal error:%v", err)
		}
		_, err = ES.SelectConnect("es_standalonesite").CreateIndex(esIndex).BodyString(string(esIndexBody)).Do(ctx)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search CreateIndex error:%v", err)
			return false
		}
	}
	return true
}

func createUserIndex() bool {
	esIndex := config.GetConfig().ElasticSearchSetting.UserInfoIndex
	ctx := context.Background()
	exists, err := ES.SelectConnect("es_standalonesite").IndexExists(esIndex).Do(ctx)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search IndexExists error:%v", err)
		return false
	}
	if !exists {
		data := &ElasticSearchIndex{
			Settings: &ElasticSearchIndexSettings{
				Index: &ElasticSearchIndexSettingsIndex{
					RefreshInterval: "5s",
					Analysis: &ElasticSearchIndexAnalysis{
						Analyzer: &ElasticSearchIndexProperty{

							"english_analyzer": map[string]interface{}{
								"type": "standard",
							},
							"korean_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "nori_tokenizer",
								"filter": []string{
									"lowercase",
									"nori_part_of_speech",
								},
							},
							"japanese_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "kuromoji_tokenizer",
								"filter": []string{
									"lowercase",
									"kuromoji_part_of_speech",
								},
							},
							"zh_hans_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "ik_max_word",
							},
							"zh_hant_analyzer": map[string]interface{}{
								"type":      "custom",
								"tokenizer": "ik_max_word",
							},
						},
					},
				},
			},
			Mappings: &ElasticSearchIndexMappings{
				Properties: &ElasticSearchIndexProperty{
					"id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"intl_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"intl_user_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"intl_gameid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"language": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"nickname": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"username": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"username_en": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "english_analyzer",
					},
					"username_ja": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "japanese_analyzer",
					},
					"username_ko": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "korean_analyzer",
					},
					"username_zh": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "zh_hans_analyzer",
					},
					"username_zh-TW": &ElasticSearchIndexPropertyWithAnalyzerT{
						Type:     "text",
						Analyzer: "zh_hant_analyzer",
					},
					"username_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"auth_languages": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"certification_user_languages": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"avatar": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"avatar_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"fans_num": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"follow_num": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"all_post_num": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"post_star_num": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"post_num": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"remark": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"remark_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"home_page_links": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"is_admin": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"admin_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"is_mute": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"mute_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"auth_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"auth_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"mood": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"game_tag": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"created_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"modified_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"deleted_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"is_del": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"mute_reason": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"mute_days": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"is_demotion": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"demotion_off": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
				},
			},
		}
		esIndexBody, err := json.Marshal(data)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search Marshal error:%v", err)
		}
		_, err = ES.SelectConnect("es_standalonesite").CreateIndex(esIndex).BodyString(string(esIndexBody)).Do(ctx)
		if err != nil {
			fmt.Println(err.Error())
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search CreateIndex error:%v", err)
			return false
		}
	}
	return true
}

func createUserAuditIndex() bool {
	esIndex := config.GetConfig().ElasticSearchSetting.UserAuditIndex
	ctx := context.Background()
	exists, err := ES.SelectConnect("es_standalonesite").IndexExists(esIndex).Do(ctx)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search IndexExists error:%v", err)
		return false
	}
	if !exists {
		data := &ElasticSearchIndexNoAnalysis{
			Settings: &ElasticSearchIndexSettingsNoAnalysis{
				Index: &ElasticSearchIndexSettingsNoAnalysisIndex{
					RefreshInterval: "5s",
				},
			},
			Mappings: &ElasticSearchIndexMappings{
				Properties: &ElasticSearchIndexProperty{
					"id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"intl_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"intl_user_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"text_risk_level": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"text_risk_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"pic_risk_level": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"pic_risk_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"audit_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"audit_user": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"context": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"audit_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"audit_introduce": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"created_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"modified_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"deleted_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"is_del": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"language": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"machine_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"artificial_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"del_reason": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"del_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
				},
			},
		}
		esIndexBody, err := json.Marshal(data)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search Marshal error:%v", err)
		}
		_, err = ES.SelectConnect("es_standalonesite").CreateIndex(esIndex).BodyString(string(esIndexBody)).Do(ctx)
		if err != nil {
			fmt.Println(err.Error())
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search CreateIndex error:%v", err)
			return false
		}
	}
	return true
}

func createReportIndex() bool {
	esIndex := config.GetConfig().ElasticSearchSetting.ReportIndex
	ctx := context.Background()
	exists, err := ES.SelectConnect("es_standalonesite").IndexExists(esIndex).Do(ctx)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search IndexExists,esIndex:%s, error:%v", esIndex, err)
		return false
	}
	if !exists {
		data := &ElasticSearchIndexNoAnalysis{
			Settings: &ElasticSearchIndexSettingsNoAnalysis{
				Index: &ElasticSearchIndexSettingsNoAnalysisIndex{
					RefreshInterval: "5s",
				},
			},
			Mappings: &ElasticSearchIndexMappings{
				Properties: &ElasticSearchIndexProperty{
					"id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"report_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"content_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"content_uuid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"plate_id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"reported_intl_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"reported_intl_user_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"report_intl_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"report_intl_user_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"language": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"sub_content_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"title": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"content": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"pic_urls": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"tags": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"ext_info": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"platform": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"reason": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"game_id": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"area_id": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"update_user": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"created_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"modified_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"is_del": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
				},
			},
		}
		esIndexBody, err := json.Marshal(data)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("ReportIndex Elastic Search Marshal error:%v", err)
		}
		_, err = ES.SelectConnect("es_standalonesite").CreateIndex(esIndex).BodyString(string(esIndexBody)).Do(ctx)
		if err != nil {
			fmt.Println(err.Error())
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("ReportIndex Elastic Search CreateIndex error:%v", err)
			return false
		}
	}
	return true
}

func createCommentIndex() bool {
	esIndex := config.GetConfig().ElasticSearchSetting.TweetCommentIndex
	ctx := context.Background()
	exists, err := ES.SelectConnect("es_standalonesite").IndexExists(esIndex).Do(ctx)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search IndexExists error:%v", err)
		return false
	}
	if !exists {
		data := &ElasticSearchIndexNoAnalysis{
			Settings: &ElasticSearchIndexSettingsNoAnalysis{
				Index: &ElasticSearchIndexSettingsNoAnalysisIndex{
					RefreshInterval: "5s",
				},
			},
			Mappings: &ElasticSearchIndexMappings{
				Properties: &ElasticSearchIndexProperty{
					"id": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"intl_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"intl_user_openid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"comment_uuid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"post_uuid": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"text_risk_level": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"text_risk_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"pic_risk_level": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"pic_risk_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"audit_user": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"title": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"content": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"pic_urls": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"hot_num": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"audit_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"audit_introduce": &ElasticSearchIndexPropertyT{
						Type: "text",
					},
					"created_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"modified_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"deleted_on": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"is_del": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"game_id": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"area_id": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"created_on_ms": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
					"language": &ElasticSearchIndexPropertyT{
						Type: "keyword",
					},
					"machine_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"artificial_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"del_reason": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"del_type": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"pos_status": &ElasticSearchIndexPropertyT{
						Type: "integer",
					},
					"pos_set_time": &ElasticSearchIndexPropertyT{
						Type: "long",
					},
				},
			},
		}
		esIndexBody, err := json.Marshal(data)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search Marshal error:%v", err)
		}
		_, err = ES.SelectConnect("es_standalonesite").CreateIndex(esIndex).BodyString(string(esIndexBody)).Do(ctx)
		if err != nil {
			fmt.Println(err.Error())
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("Elastic Search CreateIndex error:%v", err)
			return false
		}
	}
	return true
}
