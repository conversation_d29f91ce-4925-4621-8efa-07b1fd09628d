package tweet

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	redis2 "github.com/go-redis/redis/v8"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/kafka"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

type PostAuditData struct {
	PostUUID    string           `json:"post_uuid"`
	PostAuditID int              `json:"post_audit_id"`
	PostAudit   *model.PostAudit `json:"post_audit"`
}

func OfficialPostPublishTimer(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("OfficialPostPublishTimer ticker start at %v", time.Now())
	redisKey := cache.GetOfficialPublishKey()

	nowTime := time.Now().Unix()
	zRangByScore := &redis2.ZRangeBy{
		Min: "0",
		Max: strconv.FormatInt(nowTime, 10),
	}
	result, err := redis.GetClient().ZRangeByScore(c, redisKey, zRangByScore).Result()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("OfficialPostPublishTimer err, get redis data failed ,err=(%v)", err)
		return
	}
	if len(result) == 0 {
		return
	}
	// 获取内容表数据，找到其对应的语言有哪些，然后更新表
	postContents, err := dao.GetPostContentByPostOpenids(result)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("OfficialPostPublishTimer err,get post content failed, post_uuid: [%v],err=(%v)", result, err)
		return
	}
	var languages = make(map[string][]string)
	var postUsers = make([]string, len(postContents))
	for _, content := range postContents {
		languages[content.Language] = append(languages[content.Language], content.PostUUID)
		postUsers = append(postUsers, content.PostUUID)
	}
	// 更新当前主表
	err = dao.UpdateOfficialPostAudit(result, languages)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("OfficialPostPublishTimer err,update official post audit, post_uuid: [%v],err=(%v)", result, err)
		return
	}
	// 更新当前语言表

	// 更新es
	doc := map[string]interface{}{
		"is_audit":     1,
		"modified_on":  time.Now().Unix(),
		"audit_status": 2,
	}
	for _, postUuid := range result {
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUuid, doc)
	}
	redis.GetClient().ZRem(c, redisKey, result)
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("OfficialPostPublishTimer ticker end at %v", time.Now())
	// 删除用户列表的缓存
	for _, intlOpenid := range postUsers {
		cache.DeleteUserPostsCache(intlOpenid, "", 10)
	}

}

func SetOfficialPublishPostToCache(c context.Context, postUuid string, publishOn int64) {
	redisKey := cache.GetOfficialPublishKey()
	var redisZ = &redis2.Z{
		Score:  float64(publishOn),
		Member: postUuid,
	}
	err := redis.GetClient().ZAdd(c, redisKey, redisZ).Err()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SetOfficialPublishPostToCache err,set post publish to cache, post_uuid: [%s], post publish time: [%d],err=(%v)", postUuid, publishOn, err)
	}
}

func DelOfficialPublishPostByCache(c context.Context, postUuid string) {
	redisKey := cache.GetOfficialPublishKey()

	err := redis.GetClient().ZRem(c, redisKey, postUuid).Err()
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DelOfficialPublishPostByCache err,delete post publish by cache, post_uuid: [%s],err=(%v)", postUuid, err)
	}
}

// 帖子审核，把数据拿出来给机审
func PostMachineAudit(c context.Context, gameId, areaId string) {
	// 分布式锁，只能有一个抢占成功
	redisKey := cache.GetMachineReviewCacheNxKey()
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("PostMachineAudit | review post by machine start, at: %d", time.Now().Unix())
	if ok, _ := redis.GetClient().SetNX(c, redisKey, "1", config.GetConfig().Dynamic.PostMachineReviewTimeoutDuration*time.Second).Result(); ok {
		defer func() {
			recovery.CatchGoroutinePanic(context.Background())
			redis.GetClient().Del(context.Background(), redisKey)
		}()
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("PostMachineAudit | start to machine audit, at: %d", time.Now().Unix())
		needAuditPostRedisKey := cache.GetNeedAuditPostUUIDKey()
		needAuditPostUUIDs, err := redis.GetClient().ZRange(c, needAuditPostRedisKey, 0, 299).Result()
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostMachineAudit err, get redis data failed ,err=(%v)", err)
			return
		}
		if len(needAuditPostUUIDs) == 0 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("PostMachineAudit empty, at: %d", time.Now().Unix())
			return
		}

		// 将结果分成 30 批，每批 10 条数据
		batchSize := 10
		for i := 0; i < len(needAuditPostUUIDs); i += batchSize {
			end := i + batchSize
			if end > len(needAuditPostUUIDs) {
				end = len(needAuditPostUUIDs)
			}
			var batchPostUUIDs []interface{}
			for _, needAuditPostUUID := range needAuditPostUUIDs[i:end] {
				batchPostUUIDs = append(batchPostUUIDs, needAuditPostUUID)

			}

			// push到kafka中
			marshal, err := json.Marshal(batchPostUUIDs)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PostMachineAudit batch write queue json code failed: %v, message: %v", err, batchPostUUIDs)
				return
			}
			key := fmt.Sprintf("machine-audit-post-%d", time.Microsecond)
			err = kafka.Produce(context.Background(), constants.MachieAuditPostProducer, "", string(marshal))
			if err != nil {
				// 写入kafka失败
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PostMachineAudit batch write queue failed: %v, key: %s,message: %v", err, key, batchPostUUIDs)
				return
			}
			// 删除redis中的值
			redis.GetClient().ZRem(context.Background(), needAuditPostRedisKey, batchPostUUIDs...)
		}

		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("PostMachineAudit | review post by machine end, at: %d", time.Now().Unix())
	}

	return
}

func MachineAuditPostConsumeHandler(c context.Context, postUuidList []string) error {
	if len(postUuidList) == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MachineAuditPostConsumeHandler empty post audit info list, postUuidList: %v", postUuidList)
		return nil
	}

	/* 分别兼容三种场景
	1、老的现网只写入了类似{postUuid}：2132043744763159970，1.4迭代代码发现网后就不会再有了
	2、新的预发布和现网写入的类似{postUuid+postAuditId}：2132043744763159970-123
	*/
	var postUuids = make([]string, 0, len(postUuidList))
	var postAuditMap = make(map[string]*model.PostAudit)
	var err error
	for _, postUuidStr := range postUuidList {
		var postUuid string
		var postAuditId int64
		postUuidArr := strings.Split(postUuidStr, "-")
		if len(postUuidArr) == 1 {
			postUuid = postUuidArr[0]
			postAuditId = 0
		} else if len(postUuidArr) == 2 {
			postUuid = postUuidArr[0]
			postAuditId, err = strconv.ParseInt(postUuidArr[1], 10, 64)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MachineAuditPostConsumeHandler Illegal post audit info, postUuid: %v", postUuid)
				continue
			}
		}

		// 查询每个记录是否是最新的审批记录id，如果不是说明当前处理的这条记录已经失效了
		postAuditItem, err := dao.GetLatestPostAudit(postUuid)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MachineAuditPostConsumeHandler GetLatestPostAudit err, postUuid: %v, error: %+v", postUuid, err)
			continue
		}
		if postAuditItem != nil && postAuditItem.Model != nil && ((postAuditId == 0 && postAuditItem.ID > 0 && postAuditItem.Title == "") || postAuditItem.ID == postAuditId) && postAuditItem.Status == constants.PostAuditUnHandler {
			postAuditMap[postUuid] = postAuditItem
			postUuids = append(postUuids, postUuidArr[0])
		} else {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MachineAuditPostConsumeHandler Illegal post audit status, no nedd to audit, postUuidStr: %v", postUuidStr)
		}
	}

	if len(postUuids) == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MachineAuditPostConsumeHandler empty postUuids audit info list, postUuidList: %v", postUuidList)
		return nil
	}

	var userOpenids = make([]string, 0, len(postUuids))
	// 获取帖子内容信息
	postContents, err := dao.GetPostContentByPostOpenids(postUuids)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MachineAuditPostConsumeHandler | get post content failed, post_uuids: %v, err: %s", postUuids, err)
		return err
	}
	// 获取帖子信息
	postList, err := dao.GetPostList(&dao.PostConditions{PostUuids: postUuids}, 0, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MachineAuditPostConsumeHandler | get post failed, post_uuids: %v, err: %s", postUuids, err)
		return err
	}
	for _, post := range postList {
		userOpenids = append(userOpenids, post.IntlOpenid)
	}
	// 获取用户信息
	userInfos, err := dao.GetUserListByOpenidV2(userOpenids, false)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("MachineAuditPostConsumeHandler | get user info failed, user_openids: %v, err: %s", userOpenids, err)
		return err
	}
	for _, post := range postList {
		if post.IsOfficial == 1 {
			// 官方帖子不参与审核
			continue
		}
		var postContentList []*model.PostContent
		var postAudit *model.PostAudit
		var userInfo *model.UserContent
		if postAuditInfo, ok := postAuditMap[post.PostUUID]; ok {
			postAudit = postAuditInfo
			// 说明是老现网生成的审核记录，走老逻辑
			if postAuditInfo.Title == "" {
				for _, content := range postContents {
					if post.PostUUID != content.PostUUID {
						continue
					}
					postContentList = append(postContentList, content)
				}
				// 兼容现网旧数据
				var tagIds []int64
				tagInfos, _ := getPostTagInfo(c, post.PostUUID, "en")
				if len(tagInfos) > 0 {
					for _, tagInfo := range tagInfos {
						tagIds = append(tagIds, tagInfo.Id)
					}
				}
				if len(postContentList) > 0 {
					postContentOldItem := postContentList[0]
					postAudit.Title = postContentOldItem.Title
					postAudit.Content = postContentOldItem.Content
					postAudit.ContentSummary = postContentOldItem.ContentSummary
					postAudit.PicUrls = postContentOldItem.PicUrls
					postAudit.Platform = postContentOldItem.Platform
					postAudit.ExtInfo = postContentOldItem.ExtInfo
				}
				PushPostAuditToES(post, postAudit, tagIds, 0)
			} else {
				// 否则是新版本的审核数据，将草稿数据赋值去走机审
				for _, content := range postContents {
					if post.PostUUID != content.PostUUID {
						continue
					}
					content.Title = postAuditInfo.Title
					content.Content = postAuditInfo.Content
					content.ContentSummary = postAuditInfo.ContentSummary
					content.PicUrls = postAuditInfo.PicUrls
					content.ExtInfo = postAuditInfo.ExtInfo
					postContentList = append(postContentList, content)
				}
			}
		}
		for _, info := range userInfos {
			if info.IntlOpenid == post.IntlOpenid {
				userInfo = info
				break
			}
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("MachineAuditPostConsumeHandler enter machine audit, post_content:%+v, post_audit:%+v, intl_openid:%s", postContentList, postAudit, post.IntlOpenid)
		PushPostToSecurityDetection(c, postContentList, post.IntlOpenid, userInfo.Username, post.Type, post.PostUUID, postAudit)
	}

	return nil
}
