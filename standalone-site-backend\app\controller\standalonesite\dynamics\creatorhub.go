package dynamics

import (
	"context"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/logic/creatorhub"
)

// 获取我的Submission内容
func (s *DynamicsImpl) GetMySubmission(c context.Context, req *pb.GetMySubmissionReq) (*pb.GetMySubmissionRsp, error) {
	userAccount, _ := metadata.GetUserAccount(c)
	// if err != nil {
	// 	return nil, err
	// }
	openid := userAccount.Uid
	rsp, err := creatorhub.GetSubmissionOfUser(c, openid, req.Limit, req.NextIdx)
	return rsp, err
}

func (s *DynamicsImpl) GetRecentTasks(c context.Context, req *pb.GetRecentTasksReq) (*pb.GetRecentTasksRsp, error) {
	// 获取openid
	// userAccount, _ := metadata.GetUserAccount(c)
	// // if err != nil {
	// // 	return nil, err
	// // }
	// openid := userAccount.Uid
	rsp, err := creatorhub.GetRecentTasks(c, req.Limit, req.Offset)
	return rsp, err
}
