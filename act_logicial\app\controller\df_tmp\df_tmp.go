// Package df_tmp TODO
package df_tmp

import (
	"context"
	"strings"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/object"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	"trpc.act.logicial/app/code"
	logic "trpc.act.logicial/app/logic/df_tmp"
)

// DfTmpImpl Df 临时玩法
type DfTmpImpl struct {
	pb.UnimplementedDfTmp
}

// UASendCdkey   UA页发放cdkey
func (s *DfTmpImpl) UASendCdkey(ctx context.Context, req *pb.UASendCdkeyReq) (rsp *pb.UASendCdkeyRsp, err error) {
	rsp = &pb.UASendCdkeyRsp{}
	cdkey, err := logic.UASendCdkey(ctx, req.DayNum)
	if err != nil {
		return
	}
	rsp.Data = cdkey
	return
}

// UARedeemCdkey   UA页兑换cdkey
func (s *DfTmpImpl) UARedeemCdkey(ctx context.Context, req *pb.UARedeemCdkeyReq) (rsp *pb.UARedeemCdkeyRsp, err error) {
	rsp = &pb.UARedeemCdkeyRsp{}
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	err = logic.RedeemCdkey(ctx, &userAccount, req.Data)
	return
}

// GetStreamerHotRank 获取排行榜数据
func (s *DfTmpImpl) GetStreamerHotRank(ctx context.Context, req *pb.GetStreamerHotRankReq) (
	*pb.GetStreamerHotRankRsp, error) {

	rsp, err := logic.GetStreamerHotRank(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// UpdateStreamerHotRank TODO
func (s *DfTmpImpl) UpdateStreamerHotRank(ctx context.Context, req *pb.UpdateStreamerHotRankReq) (
	*pb.UpdateStreamerHotRankRsp, error) {
	// 危险行动排名
	if err := logic.UpdateStreamerHotRank(ctx); err != nil {
		log.WithFieldsContext(ctx, "log_type", "debug").Infof(
			"UpdateStreamerHotRank UpdateStreamerHotRank err.err:[%v]", err)
	}
	// 全面战场排名
	if err := logic.UpdateHavocWarfareRank(ctx); err != nil {
		log.WithFieldsContext(ctx, "log_type", "debug").Infof(
			"UpdateStreamerHotRank UpdateHavocWarfareRank err.err:[%v]", err)
	}
	return &pb.UpdateStreamerHotRankRsp{}, nil
}

// UASendCdkeyScript   UA页兑换cdkey
func (s *DfTmpImpl) UASendCdkeyScript(ctx context.Context, req *pb.UASendCdkeyScriptReq) (rsp *pb.UASendCdkeyScriptRsp,
	err error) {
	rsp = &pb.UASendCdkeyScriptRsp{}
	logic.UASendCdkeyScript()
	return
}

// GetGunActTime TODO
// GetGunActTime 查询拼枪活动时间
func (s *DfTmpImpl) GetGunActTime(ctx context.Context, req *pb.GetGunActTimeReq) (
	*pb.GetGunActTimeRsp, error) {
	rsp, err := logic.GetGunActTime(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// JoinGunAct 加入拼枪活动
func (s *DfTmpImpl) JoinGunAct(ctx context.Context, req *pb.JoinGunActReq) (
	*pb.JoinGunActRsp, error) {
	rsp, err := logic.JoinGunAct(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// CancelGunAct 取消活动任务
func (s *DfTmpImpl) CancelGunAct(ctx context.Context, req *pb.CancelGunActReq) (
	*pb.CancelGunActRsp, error) {
	rsp, err := logic.CancelGunAct(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetGunActTaskStatus 获取拼枪任务状态
func (s *DfTmpImpl) GetGunActTaskStatus(ctx context.Context, req *pb.GetGunActTaskStatusReq) (
	*pb.GetGunActTaskStatusRsp, error) {
	rsp, err := logic.GetGunActTaskStatus(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// HelpGunActTask 助力好友拼枪
func (s *DfTmpImpl) HelpGunActTask(ctx context.Context, req *pb.HelpGunActTaskReq) (
	*pb.HelpGunActTaskRsp, error) {
	rsp, err := logic.HelpGunActTask(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ClaimGunActPrize 领取奖励请求
func (s *DfTmpImpl) ClaimGunActPrize(ctx context.Context, req *pb.ClaimGunActPrizeReq) (
	*pb.ClaimGunActPrizeRsp, error) {
	rsp, err := logic.ClaimGunActPrize(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetGunActTaskHelpUsers 获取助力用户列表
func (s *DfTmpImpl) GetGunActTaskHelpUsers(ctx context.Context, req *pb.GetGunActTaskHelpUsersReq) (
	*pb.GetGunActTaskHelpUsersRsp, error) {
	rsp, err := logic.GetGunActTaskHelpUsers(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GunActLoadConf TODO
// GetGunActTaskHelpUsers 获取助力用户列表
func (s *DfTmpImpl) GunActLoadConf(ctx context.Context, req *pb.GunActLoadConfReq) (
	*pb.GunActLoadConfRsp, error) {
	rsp, err := logic.GunActLoadConf(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GunActUpdateTask 拼枪任务后台更新
func (s *DfTmpImpl) GunActUpdateTask(ctx context.Context, req *pb.GunActUpdateTaskReq) (
	*pb.GunActUpdateTaskRsp, error) {
	return &pb.GunActUpdateTaskRsp{}, nil
	rsp, err := logic.GunActUpdateTask(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetRechargeRebateStatus TODO
func (s *DfTmpImpl) GetRechargeRebateStatus(ctx context.Context, req *pb.GetRechargeRebateStatusReq) (
	*pb.GetRechargeRebateStatusRsp, error) {
	rsp, err := logic.GetRechargeRebateStatus(ctx, req)
	return rsp, err
}

// ReceiveRechargeRebateCoins TODO
func (s *DfTmpImpl) ReceiveRechargeRebateCoins(ctx context.Context, req *pb.ReceiveRechargeRebateCoinsReq) (
	*pb.ReceiveRechargeRebateCoinsRsp, error) {
	rsp, err := logic.ReceiveRechargeRebateCoins(ctx, req)
	return rsp, err
}

// // 拼枪活动相关 ////

// // 掏鸟窝活动相关 start ////

// SetFirstClaimNestInfo 设置用户首次掏鸟窝成功时间
func (s *DfTmpImpl) SetFirstClaimNestInfo(ctx context.Context, req *pb.SetFirstClaimNestInfoReq) (
	*pb.SetFirstClaimNestInfoRsp, error) {
	rsp, err := logic.SetFirstClaimNestInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetFirstClaimNestInfo 获取用户首次掏鸟窝成功时间
func (s *DfTmpImpl) GetFirstClaimNestInfo(ctx context.Context, req *pb.GetFirstClaimNestInfoReq) (
	*pb.GetFirstClaimNestInfoRsp, error) {
	rsp, err := logic.GetFirstClaimNestInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetUserNestLimit 判断用户距离首次成功掏鸟窝是否超过限制
func (s *DfTmpImpl) GetUserNestLimit(ctx context.Context, req *pb.GetUserNestLimitReq) (
	*pb.GetUserNestLimitRsp, error) {
	rsp, err := logic.GetUserNestLimit(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// // 掏鸟窝活动相关 end ////

// CheckCDKInList 检查CDK是否在列表内
func (s *DfTmpImpl) CheckCDKInList(ctx context.Context, req *pb.CheckCDKInListReq) (
	*pb.CheckCDKInListRsp, error) {
	cdkList := strings.Split(req.List, ",")
	exists, _ := object.InArray(req.Cdkey, cdkList)
	if !exists {
		return nil, errs.NewCustomError(ctx, code.CurrentCDKNotInList, "Currently CDK is not in range")
	}
	return &pb.CheckCDKInListRsp{}, nil
}

// OneKeyMultiRedeemRecord 一码多用分支，给一个码添加兑换记录
func (s *DfTmpImpl) OneKeyMultiRedeemRecord(ctx context.Context, req *pb.OneKeyMultiRedeemRecordReq) (
	*pb.OneKeyMultiRedeemRecordRsp, error) {
	rsp, err := logic.OneKeyMultiRedeemRecord(ctx, req)
	return rsp, err
}

// GetCBTRechargeRebateStatus TODO
// DF CBT 充值返利状态
func (s *DfTmpImpl) GetCBTRechargeRebateStatus(ctx context.Context, req *pb.GetCBTRechargeRebateStatusReq) (
	*pb.GetCBTRechargeRebateStatusRsp, error) {
	rsp, err := logic.GetCBTRechargeRebateStatus(ctx, req)
	return rsp, err
}

// ReceiveCBTRechargeRebateCoins TODO
func (s *DfTmpImpl) ReceiveCBTRechargeRebateCoins(ctx context.Context, req *pb.ReceiveCBTRechargeRebateCoinsReq) (
	*pb.ReceiveCBTRechargeRebateCoinsRsp, error) {
	rsp, err := logic.ReceiveCBTRechargeRebateCoins(ctx, req)
	return rsp, err
}

// DfIsLoginToday TODO
func (s *DfTmpImpl) DfIsLoginToday(ctx context.Context, req *pb.DfIsLoginTodayReq) (*pb.DfIsLoginTodayRsp, error) {
	rsp, err := logic.DfIsLoginToday(ctx)
	return rsp, err
}

// DfIsPlayToday TODO
func (s *DfTmpImpl) DfIsPlayToday(ctx context.Context, req *pb.DfIsPlayTodayReq) (*pb.DfIsPlayTodayRsp, error) {
	rsp, err := logic.DfIsPlayToday(ctx)
	return rsp, err
}

// DfKillNumToday TODO
func (s *DfTmpImpl) DfKillNumToday(ctx context.Context, req *pb.DfKillNumTodayReq) (*pb.DfKillNumTodayRsp, error) {
	rsp, err := logic.DfKillNumToday(ctx)
	return rsp, err
}
