// Package lottery 抽奖
package lottery

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_lottery"
	redisOrigin "github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/global"
	LotteryModel "trpc.act.logicial/app/model/lottery"
)

const (
	// LotteryRedisKey 抽奖redis key
	LotteryRedisKey string = "lottery"
)

// LotteryImpl 声明
type LotteryImpl struct{}

// AddOnceLottery 增加一次抽奖机会
func (s *LotteryImpl) AddOnceLottery(ctx context.Context, req *pb.AddOnceLotteryReq) (rsp *pb.AddOnceLotteryRsp,
	err error) {

	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	now := time.Now()
	location := time.FixedZone("Custom", int(req.TimeZone*3600))
	timeNow := now.In(location)
	todayBeginTimestamp := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0,
		location).Unix()
	// 先查询
	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
		"lottery_id":   req.LotteryId,
	}
	var tableName string
	var lotteryData LotteryModel.LotteryTotalData
	tableName, err = model.GetTableNameWithAccount(ctx, &userAccount, lotteryData.TableName())
	if err != nil {
		return
	}
	dbErr := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition).First(&lotteryData).Error
	hasNoData := false
	if dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			// 未找到记录
			hasNoData = true
		} else {
			// 报错,数据库操作异常
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", dbErr.Error())
			return
		}
	}

	// 没有数据或者不是今天数据
	if hasNoData || lotteryData.DayTimestamp != todayBeginTimestamp {

		// 有数据判断总体是否超限
		if req.TotalLimit > 0 && lotteryData.TotalNum >= req.TotalLimit {
			// 超过总限
			err = errs.NewCustomError(ctx, code.LotteryTotalLimit,
				"lottery add chance total limit,lottery_id=%v",
				req.LotteryId)
			return
		}
		createData := LotteryModel.LotteryTotalData{
			DayTimestamp: todayBeginTimestamp,
			Uid:          userAccount.Uid,
			AccountType:  int32(userAccount.AccountType),
			FsourceId:    req.FsourceId,
			LotteryId:    req.LotteryId,
			TotalNum:     lotteryData.TotalNum + 1,
			TodayNum:     1,
			TotalUseNum:  lotteryData.TotalUseNum,
			TodayUseNum:  0,
		}
		// 事务
		tx := DB.DefaultConnect().WithContext(ctx).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
		if txErr := tx.Error; txErr != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", txErr.Error())
			return
		}
		res := tx.Table(tableName).Create(&createData)
		if res.Error != nil {
			tx.Rollback()
			// 如果不是 插入冲突报错，返回错误
			if !strings.HasPrefix(res.Error.Error(), "Error 1062") {
				err = errs.NewCustomError(ctx, code.LotteryAddDuplicateError, "insert today data ,err=%v", res.Error.Error())
			} else {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", res.Error.Error())
			}
			return
		}

		// 将 之前有数据的状态设置为1
		if !hasNoData {
			// fmt.Println(">>>>>>>>>>>>>>>有之前数据<<<<<<<<<<<<<<<")
			// condition["day_timestamp"] = lotteryData.DayTimestamp
			updateRes := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where("id = ?", lotteryData.ID).
				Delete(&lotteryData)
			if updateRes.Error != nil {
				tx.Rollback()
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", res.Error.Error())
				return
			}
			if updateRes.RowsAffected == 0 {
				tx.Rollback()
				err = errs.NewCustomError(ctx, code.LotteryAddNumsUpdateFailed, "update failed,lottery_id=%v", req.LotteryId)
				return
			}

			tx.Commit()
		} else {
			tx.Commit()
		}
	} else {

		// 今天数据 判断日限
		if req.DayLimit > 0 && lotteryData.TodayNum >= req.DayLimit {
			// 超过日限
			err = errs.NewCustomError(ctx, code.LotteryDayLimit,
				"lottery add chance day limit,lottery_id=%v", req.LotteryId)
			return
		}
		// 更新今日数据
		tableName, err = model.GetTableNameWithAccount(ctx, &userAccount, lotteryData.TableName())
		// 先查询
		condition["day_timestamp"] = todayBeginTimestamp
		updateData := map[string]interface{}{
			"total_num": gorm.Expr("total_num + ?", 1),
			"today_num": gorm.Expr("today_num + ?", 1),
		}
		db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition)
		if req.DayLimit > 0 {
			db = db.Where("today_num < ?", req.DayLimit)
		}
		if req.TotalLimit > 0 {
			db = db.Where("total_num < ?", req.TotalLimit)
		}

		res := db.Where("deleted_at = ?", 0).Updates(updateData)
		if res.Error != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", res.Error.Error())
			return
		}
		if res.RowsAffected == 0 {
			var todayLotteryTotalModel LotteryModel.LotteryTotalData
			DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition).First(&todayLotteryTotalModel)
			if todayLotteryTotalModel.TotalNum >= req.TotalLimit {
				// 超过总限
				err = errs.NewCustomError(ctx, code.LotteryTotalLimit,
					"lottery add chance total limit,lottery_id=%v", req.LotteryId)
			} else {
				// 超过日限
				err = errs.NewCustomError(ctx, code.LotteryDayLimit,
					"lottery add chance day limit,lottery_id=%v", req.LotteryId)
			}
			return
		}
	}
	rsp = &pb.AddOnceLotteryRsp{
		LotteryHasAdd: true,
	}
	// 上报
	log.WithFieldsContext(ctx, "log_type", "lottery_add", "source_id", req.FsourceId, "tag_id", strconv.Itoa(int(req.LotteryId))).Infof("add lottery chance")
	return
}

// UseOnceLottery 使用抽奖机会
func (s *LotteryImpl) UseOnceLottery(ctx context.Context, req *pb.UseOnceLotteryReq) (rsp *pb.UseOnceLotteryRsp,
	err error) {

	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	var tableName string
	var lotteryData LotteryModel.LotteryTotalData
	tableName, err = model.GetTableNameWithAccount(ctx, &userAccount, lotteryData.TableName())

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
	}
	updateData := map[string]interface{}{
		"total_use_num": gorm.Expr("total_use_num + ?", 1),
		"today_use_num": gorm.Expr("today_use_num + ?", 1),
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Limit(1).Where(condition)
	isAll := false

	for _, item := range req.LotteryIdList {
		if item == 0 {
			isAll = true
		}
	}
	if !isAll {
		if len(req.LotteryIdList) == 1 {
			db = db.Where("lottery_id = ?", req.LotteryIdList[0])
		} else {
			db = db.Where("lottery_id in (?)", req.LotteryIdList)
		}
	}
	db = db.Where("deleted_at = ?", 0)
	if req.IsOnlyToday {
		now := time.Now()
		location := time.FixedZone("Custom", int(req.TimeZone*3600))
		timeNow := now.In(location)
		todayBeginTimestamp := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0,
			location)
		db = db.Where("day_timestamp = ?", todayBeginTimestamp.Unix())
		db = db.Where("today_num > ?", gorm.Expr("today_use_num"))

	}

	if rowsAffected := db.Where("total_num > ?", gorm.Expr("total_use_num ")).Updates(updateData).
		RowsAffected; rowsAffected == 0 {
		err = errs.NewCustomError(ctx, code.LotteryDelNumNotEnough, "lottery num not enough")
		return
	}
	rsp = &pb.UseOnceLotteryRsp{
		LotteryHasDel: true,
	}
	// 上报
	log.WithFieldsContext(ctx, "log_type", "lottery_del", "source_id", req.FsourceId).Infof("use lottery chance")
	return
}

// UseOnceLotteryReturnLotteryID 使用抽奖机会 返回抽奖ID
func (s *LotteryImpl) UseOnceLotteryReturnLotteryID(ctx context.Context, req *pb.UseOnceLotteryReturnLotteryIDReq) (
	rsp *pb.UseOnceLotteryReturnLotteryIDRsp,
	err error) {

	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	// TODO
	if userAccount.AccountType == accountPb.AccountType_TEST {
		rsp = &pb.UseOnceLotteryReturnLotteryIDRsp{
			UseLotteryId: 1,
		}
		return
	}

	var tableName string
	var lotteryData LotteryModel.LotteryTotalData
	tableName, err = model.GetTableNameWithAccount(ctx, &userAccount, lotteryData.TableName())

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
	}
	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Limit(1).Where(condition)
	isAll := false

	for _, item := range req.LotteryIdList {
		if item == 0 {
			isAll = true
		}
	}
	if !isAll {
		if len(req.LotteryIdList) == 1 {
			db = db.Where("lottery_id = ?", req.LotteryIdList[0])
		} else {
			db = db.Where("lottery_id in (?)", req.LotteryIdList)
		}
	}
	db = db.Where("deleted_at = ?", 0)
	if req.IsOnlyToday {
		now := time.Now()
		location := time.FixedZone("Custom", int(req.TimeZone*3600))
		timeNow := now.In(location)
		todayBeginTimestamp := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, location)
		db = db.Where("day_timestamp = ?", todayBeginTimestamp.Unix())
		db = db.Where("today_num > ?", gorm.Expr("today_use_num"))

	}

	if dbErr := db.Where("total_num > ?", gorm.Expr("total_use_num ")).First(&lotteryData).Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			err = errs.NewCustomError(ctx, code.LotteryDelNumNotEnough, "lottery num not enough")
		} else {
			// 报错,数据库操作异常
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
		}
		return
	}
	// 更新指定ID
	condition = map[string]interface{}{
		"id": lotteryData.ID,
	}
	updateData := map[string]interface{}{
		"total_use_num": gorm.Expr("total_use_num + ?", 1),
		"today_use_num": gorm.Expr("today_use_num + ?", 1),
	}
	db = DB.DefaultConnect().WithContext(ctx).Table(tableName).Limit(1).Where(condition)
	isAll = false

	for _, item := range req.LotteryIdList {
		if item == 0 {
			isAll = true
		}
	}
	if !isAll {
		if len(req.LotteryIdList) == 1 {
			db = db.Where("lottery_id = ?", req.LotteryIdList[0])
		} else {
			db = db.Where("lottery_id in (?)", req.LotteryIdList)
		}
	}
	db = db.Where("deleted_at = ?", 0)
	if req.IsOnlyToday {
		now := time.Now()
		location := time.FixedZone("Custom", int(req.TimeZone*3600))
		timeNow := now.In(location)
		todayBeginTimestamp := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, location)
		db = db.Where("day_timestamp = ?", todayBeginTimestamp.Unix())
		db = db.Where("today_num > ?", gorm.Expr("today_use_num"))

	}

	if rowsAffected := db.Where("total_num > ?", gorm.Expr("total_use_num ")).Updates(updateData).
		RowsAffected; rowsAffected == 0 {
		err = errs.NewCustomError(ctx, code.LotteryDelNumNotEnough, "lottery num not enough")
		return
	}
	rsp = &pb.UseOnceLotteryReturnLotteryIDRsp{
		UseLotteryId: lotteryData.LotteryId,
	}
	return
}

// ShowLotteryNum 展示抽奖次数
func (s *LotteryImpl) ShowLotteryNum(ctx context.Context, req *pb.ShowLotteryNumReq) (rsp *pb.ShowLotteryNumRsp,
	err error) {

	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	if req.OpenCache {
		// 先查缓存 没有则查数据 设置为缓存
		var lotteryNum int64
		var has bool
		lotteryNum, has, err = getCacheLotteryNum(ctx, userAccount, req.FsourceId, req.LotteryIdList, req.IsOnlyToday,
			req.IsOnlyLeft)
		if err != nil {
			return
		}
		if has {
			rsp = &pb.ShowLotteryNumRsp{
				LotteryNum: lotteryNum,
			}
		}
	}

	var tableName string
	var lotteryData LotteryModel.LotteryTotalData
	tableName, err = model.GetTableNameWithAccount(ctx, &userAccount, lotteryData.TableName())

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
	}

	db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition)

	if req.IsOnlyToday {
		now := time.Now()
		location := time.FixedZone("Custom", int(req.TimeZone*3600))
		timeNow := now.In(location)
		todayBeginTimestamp := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, location)
		db = db.Where("day_timestamp = ?", todayBeginTimestamp.Unix())

		if req.IsOnlyLeft {
			db = db.Select("sum(today_num - today_use_num) as today_left_num")
		} else {
			db = db.Select("sum(today_num) as today_left_num")
		}
	} else {

		if req.IsOnlyLeft {
			db = db.Select("sum(total_num - total_use_num) as total_left_num")
		} else {
			db = db.Select("sum(total_num) as total_left_num")
		}
	}

	isAll := false

	for _, item := range req.LotteryIdList {
		if item == 0 {
			isAll = true
		}
	}
	if !isAll {
		if len(req.LotteryIdList) == 1 {
			db = db.Where("lottery_id = ?", req.LotteryIdList[0])
		} else {
			db = db.Where("lottery_id in (?)", req.LotteryIdList)
		}
	}
	db = db.Where("deleted_at = ?", 0)

	var data struct {
		TotalLeftNum int `gorm:"column:total_left_num;not null"`
		TodayLeftNum int `gorm:"column:today_left_num;not null"`
	}

	if errM := db.Scan(&data).Error; err != nil {
		if errors.Is(errM, gorm.ErrRecordNotFound) {
			// 找不到数据
		} else {
			// 报错,数据库操作异常
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
	}
	toatalLeft, _ := strconv.ParseFloat(strconv.Itoa(data.TotalLeftNum), 64)
	todayLeft, _ := strconv.ParseFloat(strconv.Itoa(data.TodayLeftNum), 64)

	LeftNum := math.Max(0, toatalLeft)
	if req.IsOnlyToday {
		LeftNum = math.Max(0, todayLeft)
	}
	rsp = &pb.ShowLotteryNumRsp{
		LotteryNum: int64(LeftNum),
	}
	if req.OpenCache {
		// 先查缓存 没有则查数据 设置为缓存
		err = setCacheLotteryNum(ctx, userAccount, req.FsourceId, req.LotteryIdList, req.IsOnlyToday, req.IsOnlyLeft,
			int64(LeftNum), req.CacheSecond)
		if err != nil {
			return
		}
	}
	return
}

// AddNumsLottery 增加指定数量抽奖机会
func (s *LotteryImpl) AddNumsLottery(ctx context.Context, req *pb.AddNumsLotteryReq) (rsp *pb.AddNumsLotteryRsp,
	err error) {

	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	now := time.Now()
	location := time.FixedZone("Custom", int(req.TimeZone*3600))
	timeNow := now.In(location)
	todayBeginTimestamp := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, location).Unix()

	condition := map[string]interface{}{
		"uid":          userAccount.Uid,
		"account_type": userAccount.AccountType,
		"Fsource_id":   req.FsourceId,
		"lottery_id":   req.LotteryId,
	}
	var tableName string
	var lotteryData LotteryModel.LotteryTotalData
	tableName, err = model.GetTableNameWithAccount(ctx, &userAccount, lotteryData.TableName())
	if err != nil {
		return
	}
	dbErr := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition).First(&lotteryData).Error
	hasNoData := false
	if dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			// 未找到记录
			hasNoData = true
		} else {
			// 报错,数据库操作异常
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", dbErr.Error())
			return
		}
	}

	var addNum float64 = float64(req.AddNum)
	if req.DayLimit > 0 {
		if lotteryData.DayTimestamp == todayBeginTimestamp {
			addNum = math.Min(addNum, float64(req.DayLimit-lotteryData.TodayNum))
		} else {
			addNum = math.Min(addNum, float64(req.DayLimit))
		}
	}
	if req.TotalLimit > 0 {
		addNum = math.Min(addNum, float64(req.TotalLimit-lotteryData.TotalNum))
	}
	addNum = math.Max(0, addNum)
	addRealNum := int64(addNum)
	if addRealNum == 0 {
		rsp = &pb.AddNumsLotteryRsp{
			RealAddNum: addRealNum,
		}
		return
	}
	// 没有数据或者不是今天数据
	if hasNoData || lotteryData.DayTimestamp < todayBeginTimestamp {
		createData := LotteryModel.LotteryTotalData{
			DayTimestamp: todayBeginTimestamp,
			Uid:          userAccount.Uid,
			AccountType:  int32(userAccount.AccountType),
			FsourceId:    req.FsourceId,
			LotteryId:    req.LotteryId,
			TotalNum:     lotteryData.TotalNum + addRealNum,
			TodayNum:     addRealNum,
			TotalUseNum:  lotteryData.TotalUseNum,
			TodayUseNum:  0,
		}
		// 事务
		tx := DB.DefaultConnect().WithContext(ctx).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
		if txErr := tx.Error; txErr != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", txErr.Error())
			return
		}
		res := tx.Table(tableName).Create(&createData)
		if res.Error != nil {
			tx.Rollback()
			// 如果不是 插入冲突报错，返回错误
			if !strings.HasPrefix(res.Error.Error(), "Error 1062") {
				err = errs.NewCustomError(ctx, code.LotteryAddDuplicateError, "insert today data ,err=%v", res.Error.Error())
			} else {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", res.Error.Error())
			}
			return
		}
		// 将 之前有数据的状态设置为1
		if !hasNoData {
			// condition["day_timestamp"] = lotteryData.DayTimestamp

			updateRes := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where("id = ?", lotteryData.ID).
				Delete(&lotteryData)
			if updateRes.Error != nil {
				tx.Rollback()
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db error, \t [Error]:{%v} ", res.Error.Error())
				return
			}
			if updateRes.RowsAffected == 0 {
				tx.Rollback()
				err = errs.NewCustomError(ctx, code.LotteryAddNumsUpdateFailed, "update failed,lottery_id=%v", req.LotteryId)
				return
			} else {
				tx.Commit()
			}
		} else {
			tx.Commit()
		}
		rsp = &pb.AddNumsLotteryRsp{
			RealAddNum: addRealNum,
		}
		return

	} else {

		// 更新今日数据
		tableName, err = model.GetTableNameWithAccount(ctx, &userAccount, lotteryData.TableName())
		// 先查询
		condition["day_timestamp"] = todayBeginTimestamp
		updateData := map[string]interface{}{
			"total_num": gorm.Expr("total_num + ?", addRealNum),
			"today_num": gorm.Expr("today_num + ?", addRealNum),
		}
		db := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where(condition)
		if req.DayLimit > 0 {
			db = db.Where("today_num <= ?", req.DayLimit-addRealNum)
		}
		if req.TotalLimit > 0 {
			db = db.Where("total_num <= ?", req.TotalLimit-addRealNum)
		}
		res := db.Where("deleted_at = ?", 0).Updates(updateData)
		if res.Error != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", res.Error.Error())
			return
		}
		if res.RowsAffected == 0 {
			// 更新失败
			err = errs.NewCustomError(ctx, code.LotteryAddNumsUpdateFailed, "update failed,lottery_id=%v", req.LotteryId)
			return
		}
		rsp = &pb.AddNumsLotteryRsp{
			RealAddNum: addRealNum,
		}
		return
	}
}

// RemoveLotteryCache 删除缓存
func (s *LotteryImpl) RemoveLotteryCache(ctx context.Context, req *pb.RemoveLotteryCacheReq) (
	rsp *pb.RemoveLotteryCacheRsp, err error) {
	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	err = removeCacheLotteryNum(ctx, userAccount, req.FsourceId, req.LotteryIdList, req.IsOnlyToday, req.IsOnlyLeft)
	rsp = &pb.RemoveLotteryCacheRsp{}
	return
}

// 清除Key
func removeCacheLotteryNum(ctx context.Context, userAccount accountPb.UserAccount, FsourceId string,
	lotteryIdList []int64, isOnlyToday bool, isOnlyLeft bool) (err error) {
	userKey := getRedisUserLotteryNumKey(ctx, userAccount, FsourceId, lotteryIdList, isOnlyToday, isOnlyLeft)
	_, err = redis.GetClient().Del(ctx, userKey).Result() // 释放锁

	return
}
func setCacheLotteryNum(ctx context.Context, userAccount accountPb.UserAccount, FsourceId string, lotteryIdList []int64,
	isOnlyToday bool, isOnlyLeft bool, lotteryNum int64, secondNum int64) (err error) {
	userKey := getRedisUserLotteryNumKey(ctx, userAccount, FsourceId, lotteryIdList, isOnlyToday, isOnlyLeft)
	_, err = redis.GetClient().SetEX(ctx, userKey, lotteryNum, time.Duration(secondNum)*time.Second).Result() // 释放锁
	return
}

func getCacheLotteryNum(ctx context.Context, userAccount accountPb.UserAccount, FsourceId string, lotteryIdList []int64,
	isOnlyToday bool, isOnlyLeft bool) (lotteryNum int64, has bool, err error) {
	userKey := getRedisUserLotteryNumKey(ctx, userAccount, FsourceId, lotteryIdList, isOnlyToday, isOnlyLeft)
	lotteryNum, redisError := redis.GetClient().Get(ctx, userKey).Int64()
	if redisError != nil && redisError != redisOrigin.Nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeRedis, redis.RedisConnectErr, "redis connect error, error = %v",
			redisError.Error())
		return
	}
	if redisError == redisOrigin.Nil {
		has = false
		return
	}
	has = true
	return
}

func getRedisUserLotteryNumKey(ctx context.Context, userAccount accountPb.UserAccount, FsourceId string,
	lotteryIdList []int64, isOnlyToday bool, isOnlyLeft bool) (userKey string) {
	redisKeyPrefix := global.GetPrefix()
	sort.Slice(lotteryIdList, func(i, j int) bool {
		return lotteryIdList[i] < lotteryIdList[j]
	})
	lotteryIdStrlist := []string{}
	for _, item := range lotteryIdList {
		lotteryIdStrlist = append(lotteryIdStrlist, fmt.Sprint(item))
	}

	userKeyStr := fmt.Sprintf("%v_%v_%v_%v_%v_%v", userAccount.Uid, int32(userAccount.AccountType), FsourceId,
		strings.Join(lotteryIdStrlist, ","), isOnlyToday, isOnlyLeft)
	userKey = fmt.Sprintf("%v_%v_%v", redisKeyPrefix, LotteryRedisKey, Get16MD5Encode(userKeyStr))
	return
}

// GetMD5Encode 返回一个32位md5加密后的字符串
func GetMD5Encode(data string) string {
	h := md5.New()
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// Get16MD5Encode 返回一个16位md5加密后的字符串
func Get16MD5Encode(data string) string {
	return GetMD5Encode(data)[8:24]
}
