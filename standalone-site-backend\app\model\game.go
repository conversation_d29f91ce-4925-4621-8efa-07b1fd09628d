package model

type Game struct {
	*Model
	GameLanguage   *GameLanguage `json:"-"`
	GameId         string        `json:"game_id"`                     //游戏id
	EnAbbreviation string        `json:"en_abbreviation"`             //英文缩写
	Avatar         string        `json:"avatar"`                      //游戏图像
	BgImagePc      string        `json:"bg_image_pc"`                 //游戏Pc背景图
	BgImageH5      string        `json:"bg_image_h5"`                 //游戏H5背景图
	PosterImagePc  string        `json:"poster_image_pc"`             //游戏Pc竖图
	PosterImageH5  string        `json:"poster_image_h5"`             //游戏H5竖图
	QRCode         string        `json:"qrcode" gorm:"column:qrcode"` //游戏下载二维码图片链接
	UserNumber     int64         `json:"user_number"`                 //用户人数
}

type GameFormated struct {
	ID                   int64                 `json:"id"`
	GameLanguageFormated *GameLanguageFormated `json:"language"`
	GameId               string                `json:"game_id"`         //游戏id
	EnAbbreviation       string                `json:"en_abbreviation"` //英文缩写
	Avatar               string                `json:"avatar"`          //游戏图像
	BgImagePc            string                `json:"bg_image_pc"`     //游戏Pc背景图
	BgImageH5            string                `json:"bg_image_h5"`     //游戏H5背景图
	PosterImagePc        string                `json:"poster_image_pc"` //游戏Pc竖图
	PosterImageH5        string                `json:"poster_image_h5"` //游戏H5竖图
	UserNumber           int64                 `json:"user_number"`     //用户人数
	HotNum               int64                 `json:"hot_num"`         //游戏热度：所有话题热度+权重
	QRCode               string                `json:"qrcode"`          //游戏下载二维码图片链接
}

type GameAreaItem struct {
	GameId string `json:"game_id"`
	AreaId string `json:"area_id"`
}

func (t *Game) Format() *GameFormated {
	if t.Model == nil {
		return &GameFormated{}
	}

	return &GameFormated{
		ID:                   t.ID,
		GameId:               t.GameId,
		GameLanguageFormated: t.GameLanguage.Format(),
		EnAbbreviation:       t.EnAbbreviation,
		Avatar:               t.Avatar,
		BgImagePc:            t.BgImagePc,
		BgImageH5:            t.BgImageH5,
		PosterImagePc:        t.PosterImagePc,
		PosterImageH5:        t.PosterImageH5,
		UserNumber:           t.UserNumber,
		QRCode:               t.QRCode,
	}
}

func (t *Game) TableName() string {
	return "p_game"
}
