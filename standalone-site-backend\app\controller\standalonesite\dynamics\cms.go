package dynamics

import (
	"context"
	"fmt"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/comment"
	"trpc.publishing_application.standalonesite/app/logic/emoticon"
	"trpc.publishing_application.standalonesite/app/logic/plate"
	"trpc.publishing_application.standalonesite/app/logic/tweet"
)

func (s *DynamicsImpl) CMSGetPostList(c context.Context, req *pb.CMSGetPostListReq) (*pb.CMSGetPostListRsp, error) {
	getPostListRsp, err := tweet.CMSQueryPostList(c, req, req.Language, req.GameId, req.AreaId)

	if err != nil {
		return nil, err
	}
	return getPostListRsp, nil
}

func (s *DynamicsImpl) CMSGetPostAuditList(c context.Context, req *pb.CMSGetPostAuditListReq) (*pb.CMSGetPostAuditListRsp, error) {
	getPostListRsp, err := tweet.CMSQueryPostAuditList(c, req, req.Language, req.GameId, req.AreaId)

	if err != nil {
		return nil, err
	}
	return getPostListRsp, nil
}

func (s *DynamicsImpl) CMSGetReportList(c context.Context, req *pb.CMSGetReportListReq) (*pb.CMSGetReportListRsp, error) {
	getPostListRsp, err := tweet.CMSGetReportInfoList(c, req, req.Language, req.GameId, req.AreaId)

	if err != nil {
		return nil, err
	}
	return getPostListRsp, nil
}

func (s *DynamicsImpl) CMSGetPostCommentAuditList(c context.Context, req *pb.CMSGetPostCommentAuditListReq) (*pb.CMSGetPostCommentAuditListRsp, error) {
	getPostListRsp, err := tweet.CMSGetPostCommentAuditList(c, req, req.Language, req.GameId, req.AreaId)

	if err != nil {
		return nil, err
	}
	return getPostListRsp, nil
}

func (s *DynamicsImpl) CMSReviewPost(c context.Context, req *pb.CMSReviewPostReq) (*pb.CMSReviewPostRsp, error) {
	var err error
	switch constants.PostReviewT(req.Type) {
	case constants.PostReviewPass, constants.PostReviewNoPass:
		err = tweet.CMSReviewPost(c, req, false)
	case constants.PostReviewDelete, constants.PostReviewDeleteByReport:
		err = tweet.CMSDeletePost(c, req)
	case constants.PostReviewEssenceOn, constants.PostReviewCancelEssenceOn:
		err = tweet.CMSSetPostEssenceOn(c, req)
	case constants.PostReviewTopOn, constants.PostReviewCancelTopOn:
		err = tweet.CMSSetPostTopOn(c, req)
	case constants.PostReviewIgnoreReport:
		err = tweet.CMSUpdatePostReport(c, req, true)
	case constants.PostReviewUpdatePowerNum:
		err = tweet.CMSUpdatePostPowerNum(c, req)
	case constants.PostReviewUpdateOpenUp:
		err = tweet.CMSUpdatePostOpenUp(c, req)
	default:
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "Parameter error")
	}

	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CMSReviewPost err: %v\n", err)
		return nil, err
	}
	return &pb.CMSReviewPostRsp{}, nil
}

func (s *DynamicsImpl) CMSReviewPostComment(c context.Context, req *pb.CMSReviewPostCommentReq) (*pb.CMSReviewPostCommentRsp, error) {
	if len(req.CommentUuids) == 0 {
		return &pb.CMSReviewPostCommentRsp{}, nil
	}
	var err error
	switch constants.PostCommentReviewT(req.Type) {
	case constants.PostCommentReviewPass, constants.PostCommentReviewNoPass:
		err = comment.CMSReviewCommentInfo(c, req, false)
	case constants.PostCommentReviewDelete, constants.PostCommentReportDelete:
		err = comment.CMSDeletePostComment(c, req)
	case constants.PostCommentIgnoreReport:
		err = comment.CMSUpdatePostCommentReport(c, req, true)
	default:
		return nil, errs.NewSystemError(c, errs.ErrorTypeBusiness, code.InvalidParams, "Parameter error")
	}

	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.CMSReviewPostComment err: %v\n", err)
		return nil, err
	}
	return &pb.CMSReviewPostCommentRsp{}, nil
}

// CMS 通知更新板块
func (s *DynamicsImpl) CMSUpdatePlateListCache(c context.Context, req *pb.CMSUpdateBaseReq) (*pb.CMSUpdateBaseRsp, error) {
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		plate.RemovePlatListCache(context.Background())
	}()
	return &pb.CMSUpdateBaseRsp{}, nil
}

// CMS 通知更新金刚区
func (s *DynamicsImpl) CMSUpdateDistrictListCache(c context.Context, req *pb.CMSUpdateBaseReq) (*pb.CMSUpdateBaseRsp, error) {
	go cache.RemoveDistrictListCache(context.Background())
	return &pb.CMSUpdateBaseRsp{}, nil
}

// CMS 通知更新话题列表
func (s *DynamicsImpl) CMSUpdateTagListCache(c context.Context, req *pb.CMSUpdateBaseReq) (*pb.CMSUpdateBaseRsp, error) {
	go tweet.RemoveTagListCache(context.Background())
	return &pb.CMSUpdateBaseRsp{}, nil
}

// CMS 通知更新表情列表
func (s *DynamicsImpl) CMSUpdateEmoticonsCache(c context.Context, req *pb.CMSUpdateBaseReq) (*pb.CMSUpdateBaseRsp, error) {
	go emoticon.RemoveEmoticonCache(context.Background())
	return &pb.CMSUpdateBaseRsp{}, nil
}

// CMS 设置评论位置信息（置顶或置底）
func (s *DynamicsImpl) CMSSetCommentPositionInfo(ctx context.Context,
	req *pb.CMSSetCommentPositionInfoReq) (*pb.CMSSetCommentPositionInfoRsp, error) {
	log.InfoContextf(ctx, "start CMSSetCommentPositionInfo, req:%v", req.String())
	traceId := metadata.GetCtxTraceID(ctx)
	//rsp, err := tweet.CMSSetCommentPositionInfo(ctx, req)
	topBottomStatus := ""
	if req.PosStatus == constants.CommentPosStatusGeneral {
		topBottomStatus = string(constants.ECommentPosStatusString_Unset)
	}
	if req.PosStatus == constants.CommentPosStatusTop {
		topBottomStatus = string(constants.ECommentPosStatusString_Top)
	}
	if req.PosStatus == constants.CommentPosStatusBottom {
		topBottomStatus = string(constants.ECommentPosStatusString_Bottom)
	}
	err := comment.SetCommentTopOrBottomV2(ctx, req.CommentUuid, constants.ECommentPosStatusString(topBottomStatus), "", true)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).
			Errorf("CMSSetCommentPositionInfo err: %v", err)
		return nil, fmt.Errorf("%w, trace_id:%s", err, traceId)
	}
	rsp := &pb.CMSSetCommentPositionInfoRsp{}
	rsp.TraceId = traceId
	return rsp, nil
}

// CMS 查询评论列表信息
func (s *DynamicsImpl) CMSGetPostComments(ctx context.Context,
	req *pb.CMSGetPostCommentsReq) (*pb.CMSGetPostCommentsRsp, error) {
	log.InfoContextf(ctx, "start CMSGetPostComments, req:%v", req.String())
	traceId := metadata.GetCtxTraceID(ctx)
	rsp, err := tweet.CMSGetPostComments(ctx, req)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).
			Errorf("CMSGetPostComments err: %v", err)
		return nil, fmt.Errorf("%w, trace_id:%s", err, traceId)
	}
	return rsp, nil
}
