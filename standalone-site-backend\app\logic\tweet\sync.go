package tweet

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"

	"trpc.publishing_application.standalonesite/app/logic/cache"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"github.com/jinzhu/copier"
	"github.com/olivere/elastic/v7"
	es7 "github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"gorm.io/plugin/soft_delete"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/util"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	user_pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_user"
	"golang.org/x/net/html"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/user"
	"trpc.publishing_application.standalonesite/app/model"
)

func SyncPostESData(c context.Context) {
	var wg sync.WaitGroup
	posts, err := dao.SyncGetAllPost(10000)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostESData FetchPost err, err=(%v)", err)
		return
	}
	for _, post := range posts {
		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(postItem *model.Post) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			//postContentList, err := dao.GetPostContentList(postItem.PostUUID, false)
			postContentList, err := dao.PostContentGetIgnoreDeleteV2(postItem.PostUUID)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostESData PostContentGet err,postItem.PostUUID=(%v), err=(%v)", postItem.PostUUID, err)
				return
			}

			auditStatus := 1
			if post.IsAudit == 1 || post.IsOfficial == 1 {
				auditStatus = 2
			}
			tags, err := dao.GetTagIdByPostUUIDV2(postItem.PostUUID)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostESData err, GetTagIdByPostUUID failed, post_uuid:(%s), err=(%v)", postItem.PostUUID, err)
				return
			}
			postaudit, err := dao.GetPostAuditV2(0, postItem.PostUUID, "")
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostESData err, GetPostAudit failed, post_uuid:(%s), err=(%v)", postItem.PostUUID, err)
				return
			}
			//postStats, err := dao.GetPostStatsByPostUuid(postItem.PostUUID)
			//if err != nil {
			//	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostESData err, GetPostStats failed, post_uuid:(%s), err=(%v)", postItem.PostUUID, err)
			//	return
			//}

			data := map[string]interface{}{
				"id":          post.ID,
				"post_uuid":   post.PostUUID,
				"plate_id":    post.PlateID,
				"intl_openid": post.IntlOpenid,
				"language":    post.Language,
				"type":        post.Type,

				//"comment_count":    postStats.CommentCount,
				//"collection_count": postStats.CollectionCount,
				//"upvote_count":     postStats.UpvoteCount,
				//"upvote_map":       postStats.UpvoteMap,
				//"browse_count":     postStats.BrowseCount,
				//"forward_count":    postStats.ForwardCount,
				//"power_num":        postStats.PowerNum,
				"visibility": post.Visibility,
				"is_top":     post.IsTop,
				"top_sort":   post.TopSort,
				"top_on":     post.TopOn,

				"tags":                tags,
				"socialmedia_post_id": post.SocialmediaPostId,
				"latest_replied_on":   post.LatestRepliedOn,
				"created_on":          post.CreatedOn,
				"created_on_ms":       post.CreatedOnMs,
				"modified_on":         post.ModifiedOn,
				"hot_num":             0,
				"game_id":             post.GameId,
				"area_id":             post.AreaId,
				"is_audit":            post.IsAudit,

				"is_del": post.IsDel,

				"text_risk_level": postaudit.TextRiskLevel,
				"text_risk_type":  postaudit.TextRiskType,
				"pic_risk_level":  postaudit.PicRiskLevel,
				"pic_risk_type":   postaudit.PicRiskType,
				"audit_status":    auditStatus,
				"audit_user":      postaudit.AuditUser,
				"audit_introduce": postaudit.AuditIntroduce,
				"audit_on":        postaudit.AuditOn,
			}
			postLanguages := make([]string, 0)
			for i, content := range postContentList {
				if i == 0 {
					data["title"] = content.Title
					data["content"] = content.Content
					data["content_summary"] = content.ContentSummary
					data["pic_urls"] = content.PicUrls
					data["essence_on"] = content.EssenceOn
					data["is_essence"] = content.IsEssence
					data["is_original"] = content.IsOriginal
					data["original_url"] = content.OriginalURL
					data["platform"] = content.Platform
					data["ext_info"] = content.ExtInfo
					data["language"] = content.Language
					data["comment_count"] = content.CommentCount
					data["collection_count"] = content.CollectionCount
					data["upvote_count"] = content.UpvoteCount
					data["upvote_map"] = content.UpvoteMap
					data["browse_count"] = content.BrowseCount
					data["forward_count"] = content.ForwardCount
					data["power_num"] = content.PowerNum
					if len(content.ContentSummary) == 0 {
						// 获取富文本中的文字内容和富文本中的所有图片链接
						// 解析 HTML 文档
						htmlStr := strings.Join([]string{"<html><body>", content.Content, "</body></html>"}, "")
						doc, err := html.Parse(strings.NewReader(htmlStr))
						if err != nil {
							log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostESData err, html.Parse failed, post_uuid:(%s), err=(%v)", postItem.PostUUID, err)
							return
						}

						// 用于存储图片链接和文本的数组
						var imgLinks []string
						var texts []string

						var lineBreakCount int
						// 查找并提取 div 和 p 标签文本，同时提取 img 标签的链接
						traverse(doc, []string{"div", "p"}, &imgLinks, &texts, &lineBreakCount)

						data["content_summary"] = strings.Join(texts, " ")
						if data["pic_urls"] == "" {
							data["pic_urls"] = strings.Join(imgLinks, ",")
						}
					}
				}
				postLanguages = append(postLanguages, content.Language)
				// 区分设置每个语言的汇总内容，用于前端搜索。这里是因为ElasticSearch的一个字段只能配置一种分词器
				allContantLangKey := fmt.Sprintf("all_content_lang_%s", post.Language)
				var allContantLangVal string
				var postContentStr string
				if content.ContentSummary != "" {
					postContentStr = content.ContentSummary
				} else {
					postContentStr = content.Content
				}
				// 目前只配置了这几种语言的字段，后续要新增再继续追加
				if post.Language == "en" || post.Language == "ja" || post.Language == "ko" || post.Language == "zh" || post.Language == "zh-TW" {
					allContantLangVal = fmt.Sprintf("%s|||%s", content.Title, postContentStr)
					data[allContantLangKey] = allContantLangVal
				}
			}
			data["post_languages"] = postLanguages
			dao.EsBulkPushDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, "post_uuid", []map[string]interface{}{data})
		}(post)
		// 等待所有 goroutine 完成
		wg.Wait()
	}
}

func SyncNikkeArtPostESData(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncNikkeArtPostESData start, at: %d", time.Now().Unix())
	var wg sync.WaitGroup
	posts, err := dao.SyncGetAllPost(10000)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncNikkeArtPostESData FetchPost err, err=(%v)", err)
		return
	}
	for _, post := range posts {
		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(postItem *model.Post) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			//postContentList, err := dao.GetPostContentList(postItem.PostUUID, false)
			postContentList, err := dao.PostContentGetIgnoreDeleteV2(postItem.PostUUID)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncNikkeArtPostESData PostContentGet err,postItem.PostUUID=(%v), err=(%v)", postItem.PostUUID, err)
				return
			}

			for i, content := range postContentList {
				if i == 0 {
					if len(content.ContentSummary) == 0 {
						// 更新es
						doc := map[string]interface{}{
							"pic_urls": content.PicUrls,
						}
						dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)
						return
					}
				}
			}
		}(post)
		// 等待所有 goroutine 完成
		wg.Wait()
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncNikkeArtPostESData end, at: %d", time.Now().Unix())

}

func SyncUserESData(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserESData start, at: %d", time.Now().Unix())
	var wg sync.WaitGroup
	users, err := dao.GetAllUserIteratively(0, 0, "")
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SyncUserESData GetAllUserIteratively err: %v, limit: %d", err, 0)
		return
	}

	conditionsT := &dao.UserPermissionConditions{}
	userPermissionList, err := dao.UserPermissionList(conditionsT, 0)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SyncUserESData UserPermissionList err: %v", err)
		return
	}
	// 查询认证用户的用户昵称、签名多语言信息

	for _, user := range users {
		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(userOwner *model.UserOwner, userPermissionList []*model.UserPermission) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			userContent, err := dao.GetUserByIntlOpenid(user.IntlOpenid)
			if err != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SyncUserESData GetUserByIntlOpenid err: %v, user: %v", err, user)
				return
			}

			var isMute, isAdmin, authType int
			var adminOn, muteOn, authOn int64
			for _, userPermission := range userPermissionList {
				if userPermission.IntlOpenid == userContent.IntlOpenid {
					if userPermission.ActionType == 1 {
						isAdmin = 1
						adminOn = userPermission.ValidOn
					}
					if userPermission.ActionType == 2 {
						authType = userPermission.ActionValue
						authOn = userPermission.ValidOn
					}
					if userPermission.ActionType == 4 {
						isMute = 1
						muteOn = userPermission.ValidOn
					}
				}
			}
			data := []map[string]interface{}{{
				"id":                           userOwner.ID,
				"intl_openid":                  userContent.IntlOpenid,
				"intl_user_openid":             userOwner.IntlUserOpenid,
				"intl_gameid":                  userContent.IntlGameid,
				"language":                     userContent.Language,
				"nickname":                     userContent.Nickname,
				"username":                     userContent.Username,
				"username_en":                  userContent.Username,
				"username_ja":                  userContent.Username,
				"username_ko":                  userContent.Username,
				"username_zh":                  userContent.Username,
				"username_zh-TW":               userContent.Username,
				"username_on":                  userContent.UsernameOn,
				"auth_languages":               "",
				"certification_user_languages": "",
				"avatar":                       userContent.Avatar,
				"avatar_on":                    userContent.AvatarOn,
				//"fans_num":                     userContent.FansNum,
				//"follow_num":                   userContent.FollowNum,
				//"all_post_num":                 userContent.AllPostNum,
				//"post_star_num":                userContent.PostStarNum,
				//"post_num":                     userContent.PostNum,
				"remark":      userContent.Remark,
				"remark_on":   userContent.RemarkOn,
				"is_admin":    isAdmin,
				"admin_on":    adminOn,
				"is_mute":     isMute,
				"mute_on":     muteOn,
				"auth_type":   authType,
				"auth_on":     authOn,
				"mood":        userContent.Mood,
				"game_tag":    userContent.GameTag,
				"created_on":  userContent.CreatedOn,
				"modified_on": userContent.ModifiedOn,
				"deleted_on":  userContent.DeletedOn,
				"is_del":      userContent.IsDel,
			}}

			if authType > 0 {
				if authType == int(constants.USER_AUTH_TYPE_OFFICIAL) || authType == int(constants.USER_AUTH_TYPE_MECHANISM) {
					allUserLanguageInfo, _ := dao.GetCertificationUserAllLanguages(userContent.IntlOpenid, constants.GAMEID_30054, constants.AREAID_global)
					if len(allUserLanguageInfo) > 0 {
						userLanguages := make([]*user_pb.UserInfoLanguageItem, 0)
						for _, languageItem := range allUserLanguageInfo {
							if languageItem.Language == "en" && languageItem.Type == 1 {
								data[0]["username_en"] = languageItem.Content
							}
							if languageItem.Language == "ja" && languageItem.Type == 1 {
								data[0]["username_ja"] = languageItem.Content
							}
							if languageItem.Language == "ko" && languageItem.Type == 1 {
								data[0]["username_ko"] = languageItem.Content
							}
							if languageItem.Language == "zh" && languageItem.Type == 1 {
								data[0]["username_zh"] = languageItem.Content
							}
							if languageItem.Language == "zh-TW" && languageItem.Type == 1 {
								data[0]["username_zh-TW"] = languageItem.Content
							}
							userLanguages = append(userLanguages, &user_pb.UserInfoLanguageItem{
								Language: languageItem.Language,
								Type:     languageItem.Type,
								Desc:     languageItem.Content,
							})
						}
						if len(userLanguages) > 0 {
							userLanguagesBytes, _ := json.Marshal(userLanguages)
							data[0]["certification_user_languages"] = string(userLanguagesBytes)
						}
					}
				}

				allUserAuthTitleLanguageInfo, _ := dao.GetUserAllPermissionLanguages(userContent.IntlOpenid, int32(authType))
				if len(allUserAuthTitleLanguageInfo) > 0 {
					userAuthLanguages := make([]*user_pb.LanguageItem, 0)
					for _, languageItem := range allUserAuthTitleLanguageInfo {
						userAuthLanguages = append(userAuthLanguages, &user_pb.LanguageItem{
							Language: languageItem.Language,
							Desc:     languageItem.Desc,
						})
					}
					if len(userAuthLanguages) > 0 {
						userAuthLanguagesBytes, _ := json.Marshal(userAuthLanguages)
						data[0]["auth_languages"] = string(userAuthLanguagesBytes)
					}
				}

			}

			dao.EsBulkPushDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, "intl_openid", data)
		}(user, userPermissionList)
		// 等待所有 goroutine 完成
		wg.Wait()
	}
}

// 同步用户昵称语言
func SyncUserNameEsData(c context.Context, maxId int) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserEsData start, nowTime: %d", time.Now().Unix())
	firstUserId := 0
	batchSize := 1000
	isFinished := false
	for {
		if isFinished {
			break
		}
		users, err := dao.GetAllUserIteratively(firstUserId, batchSize, "")
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				isFinished = true
				break
			} else {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncUserNameEsData GetAllUserIteratively err: %v", err)
				break
			}
		}
		if len(users) == 0 {
			isFinished = true
			break
		}
		if len(users) < batchSize {
			isFinished = true
		}
		firstUserId = int(users[len(users)-1].ID)
		if maxId != 0 && firstUserId >= maxId {
			isFinished = true
		}
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserNameEsData firstUserId: %d", firstUserId)
		userOpenids := make([]string, 0)
		// 用openid
		for _, user := range users {
			// 压测数据
			if len(user.IntlUserOpenid) > 13 {
				userOpenids = append(userOpenids, user.IntlOpenid)
			}
		}
		userContents, err := dao.GetUserListByOpenid(userOpenids)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncUserNameEsData GetUserListByOpenid err: %v", err)
			break
		}
		for _, userItem := range users {
			if len(userItem.IntlUserOpenid) <= 13 {
				continue
			}
			// 判断是否为认证用户，认证用户多语言不需要更新
			isOfficialAuthUser, _ := user.IsOfficialAuthUser(c, "", "", userItem.IntlOpenid)
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserEsData userItem : %s", userItem.IntlOpenid)
			// 判断当前
			if !isOfficialAuthUser {
				isFound := false
				for _, userContent := range userContents {
					if userContent.IntlOpenid == userItem.IntlOpenid {
						isFound = true
						doc := map[string]interface{}{}
						for _, langItem := range constants.AllPostLanguages {
							userNameKey := fmt.Sprintf("username_%s", langItem)
							doc[userNameKey] = userContent.Username
						}
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserNameEsData userContent UpdateDoc : %s", userContent.IntlOpenid)
						dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, userItem.IntlOpenid, doc)
					}
				}
				if !isFound {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncUserNameEsData err not found user : %s", userItem.IntlOpenid)
				}
			}
		}
		time.Sleep(100 * time.Millisecond)
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserInfoIndex)
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncUserEsData end, nowTime: %d", time.Now().Unix())
}

// 同步用户intl_openid2
func SyncUserIntlUserOpenid(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncUserIntlUserOpenid start, nowTime: %d", time.Now().Unix())
	var wg sync.WaitGroup
	var limit = 10000
	users, err := dao.GetAllUserIteratively(0, limit, "")
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SyncUserIntlUserOpenid GetAllUserIteratively err: %v, limit: %d", err, limit)
		return
	}

	for _, user := range users {
		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(userOwner *model.UserOwner) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			intlUserOpenid := strings.Split(userOwner.IntlOpenid, "-")[1]
			user.IntlUserOpenid = intlUserOpenid
			updateUserErr := dao.SetIntlUserOpenid(user.IntlOpenid, intlUserOpenid)
			if updateUserErr != nil {
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SyncUserIntlUserOpenid SetIntlUserOpenid err: %v, userIntlOpenid: %s, intlUserOpenid(intlOpenid2):%s", err, user.IntlOpenid, user.IntlUserOpenid)
			}

			data := map[string]interface{}{
				"intl_user_openid": intlUserOpenid,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, user.IntlOpenid, data)
		}(user)
		// 等待所有 goroutine 完成
		wg.Wait()
	}

	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncUserIntlUserOpenid end, nowTime: %d", time.Now().Unix())
}

// 同步动态的微秒
func SyncPostCreatedOnMs(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostCreatedOnMs start, nowTime: %d", time.Now().Unix())
	var wg sync.WaitGroup
	posts, err := dao.SyncGetAllPost(10000)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostCreatedOnMs FetchPost err, err=(%v)", err)
		return
	}
	for _, post := range posts {
		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(postItem *model.Post) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			// 对当前的创建时间进行随机数
			randomThreeDigits := rand.Intn(100000)

			// 将秒级时间戳转换为微秒级时间戳
			microsecondTimestamp := postItem.CreatedOn * 1000000

			// 将随机数加到微秒级时间戳上
			microsecondTimestampWithRandom := microsecondTimestamp + int64(randomThreeDigits)

			var intlUserOpenid string

			// 处理用户intl_user_openid, 区别于intl_openid; ps: 这个intl_openid是一定会有的。
			if strings.Contains(postItem.IntlOpenid, "-") && len(strings.Split(postItem.IntlOpenid, "-")) > 1 {
				intlUserOpenid = strings.Split(postItem.IntlOpenid, "-")[1]
			}

			if post.CreatedOnMs == 0 {
				err2 := dao.UpdatePostInfo(postItem.PostUUID, map[string]interface{}{
					"created_on_ms": microsecondTimestampWithRandom,
				})

				if err2 != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostCreatedOnMs UpdatePostInfo err, err=(%v), postUuid: %s, created_on_ms: %d", err, postItem.PostUUID, microsecondTimestampWithRandom)
				}
			}

			data := map[string]interface{}{
				"created_on_ms":    microsecondTimestampWithRandom,
				"intl_user_openid": intlUserOpenid,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postItem.PostUUID, data)
		}(post)
		// 等待所有 goroutine 完成
		wg.Wait()
	}
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostCreatedOnMs end, nowTime: %d", time.Now().Unix())
}

// 同步评论的微秒
func SyncPostCommentCreatedOnMs(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostCommentCreatedOnMs start, nowTime: %d", time.Now().Unix())
	var wg sync.WaitGroup
	postComments, err := dao.SyncGetAllComment(10000)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostESData FetchPost err, err=(%v)", err)
		return
	}
	for _, comment := range postComments {
		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(commentItem *model.Comment) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			// 对当前的创建时间进行随机数
			randomThreeDigits := rand.Intn(100000)

			// 将秒级时间戳转换为微秒级时间戳
			microsecondTimestamp := commentItem.CreatedOn * 1000000

			// 将随机数加到微秒级时间戳上
			microsecondTimestampWithRandom := microsecondTimestamp + int64(randomThreeDigits)

			var intlUserOpenid string

			// 处理用户intl_user_openid, 区别于intl_openid; ps: 这个intl_openid是一定会有的。
			if strings.Contains(commentItem.IntlOpenid, "-") && len(strings.Split(commentItem.IntlOpenid, "-")) > 1 {
				intlUserOpenid = strings.Split(commentItem.IntlOpenid, "-")[1]
			}

			if commentItem.CreatedOnMs == 0 {
				err2 := dao.CommentUpdate(commentItem.CommentUUID, map[string]interface{}{
					"created_on_ms": microsecondTimestampWithRandom,
				})
				if err2 != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostCreatedOnMs UpdatePostInfo err, err=(%v), commentUuid: %s, created_on_ms: %d", err, commentItem.CommentUUID, microsecondTimestampWithRandom)
				}
			}

			data := map[string]interface{}{
				"created_on_ms":    microsecondTimestampWithRandom,
				"intl_user_openid": intlUserOpenid,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetCommentIndex, commentItem.CommentUUID, data)
		}(comment)
		// 等待所有 goroutine 完成
		wg.Wait()
	}
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostCommentCreatedOnMs end, nowTime: %d", time.Now().Unix())
}

// 同步举报的评论
func SyncReportPostCommentCreatedOnMs(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncReportPostCommentCreatedOnMs start, nowTime: %d", time.Now().Unix())
	var wg sync.WaitGroup
	conditions := &dao.PostReportConditions{}
	reportPostComments, err := dao.PostReportListWithIgnoreDelete(conditions, 0, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReportPostCommentCreatedOnMs PostReportListWithIgnoreDelete err, err=(%v)", err)
		return
	}
	for _, reportItem := range reportPostComments {
		wg.Add(1) // 增加 WaitGroup 的计数器
		go func(commentItem *model.PostReport) {
			defer recovery.CatchGoroutinePanic(context.Background())
			defer wg.Done() // 函数结束时减少计数器

			var intlUserOpenid string

			// 处理用户intl_user_openid, 区别于intl_openid; ps: 这个intl_openid是一定会有的。
			if strings.Contains(reportItem.ReportIntlOpenid, "-") && len(strings.Split(reportItem.ReportIntlOpenid, "-")) > 1 {
				intlUserOpenid = strings.Split(reportItem.ReportIntlOpenid, "-")[1]
			}

			var intlUserOpenid2 string

			// 处理用户intl_user_openid, 区别于intl_openid; ps: 这个intl_openid是一定会有的。
			if strings.Contains(reportItem.ReportedIntlOpenid, "-") && len(strings.Split(reportItem.ReportedIntlOpenid, "-")) > 1 {
				intlUserOpenid2 = strings.Split(reportItem.ReportedIntlOpenid, "-")[1]
			}

			data := map[string]interface{}{
				"reported_intl_user_openid": intlUserOpenid2,
				"report_intl_user_openid":   intlUserOpenid,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.ReportIndex, strconv.FormatInt(reportItem.ID, 10), data)
		}(reportItem)
		// 等待所有 goroutine 完成
		wg.Wait()
	}
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncReportPostCommentCreatedOnMs end, nowTime: %d", time.Now().Unix())
}

// 同步p_post 和 p_post_external中的platform
type PostPlatform struct {
	PostUuid string
	Platform string
}

func SyncPostContentPlatformToPost(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostContentPlatformToPost start, nowTime: %d", time.Now().Unix())
	var wg sync.WaitGroup

	// 定义一个通道，往通道内写入数据，开启一个协程专门读取通道内数据并更新数据，另外一个协程专门往里写数据
	channelPostData := make(chan *PostPlatform, 1)
	channelPostExternalData := make(chan *PostPlatform, 1)
	wg.Add(1)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		defer wg.Done() // 函数结束时减少计数器
		// 同步p_post数据
		posts, err := dao.SyncGetAllPost(0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostContentPlatformToPost SyncGetAllPost err, err=(%v)", err)
			return
		}
		var isDone bool
		for i, post := range posts {
			if i == len(posts)-1 {
				isDone = true
			}
			content, err := dao.PostContentGetIgnoreDelete(post.PostUUID)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostContentPlatformToPost Get post content err, post_uuid: [%s], err=(%v)", post.PostUUID, err)
				continue
			}
			if content.Platform == "" {
				continue
			}
			var postPlatformData = &PostPlatform{
				PostUuid: post.PostUUID,
				Platform: content.Platform,
			}
			channelPostData <- postPlatformData
			if isDone {
				channelPostData <- nil
			}
		}
	}()
	wg.Add(1)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		defer wg.Done() // 函数结束时减少计数器
		var gtId int64
		for {
			// 同步p_post_external数据
			posts, err := dao.GetPostExternalAllDataBySync(1000, gtId)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostContentPlatformToPost SyncGetAllPost err, err=(%v)", err)
				return
			}

			for _, post := range posts {
				content, err := dao.PostContentGetIgnoreDelete(post.PostUUID)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostContentPlatformToPost Get post content err, post_uuid: [%s], err=(%v)", post.PostUUID, err)
					continue
				}
				if content.Platform == "" {
					continue
				}
				var postPlatformData = &PostPlatform{
					PostUuid: post.PostUUID,
					Platform: content.Platform,
				}
				channelPostExternalData <- postPlatformData
			}
			if len(posts) < 1000 {
				channelPostExternalData <- nil
				break
			}
			gtId = posts[len(posts)-1].ID

		}
	}()

	// 处理通道内的数据
	wg.Add(1)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		defer wg.Done() // 函数结束时减少计数器
		for data := range channelPostData {
			if data == nil {
				// 如果为空直接退出
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostContentPlatformToPost end, nowTime: %d", time.Now().Unix())
				return
			}
			uErr := dao.UpdatePostInfo(data.PostUuid, map[string]interface{}{
				"platform": data.Platform,
			})
			if uErr != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostContentPlatformToPost update post failed, post uuid:[%s], platform:[%s], err: [%v]", data.PostUuid, data.Platform, uErr)
			}
		}
	}()
	// 处理通道内的数据
	wg.Add(1)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		defer wg.Done() // 函数结束时减少计数器
		for data := range channelPostExternalData {
			if data == nil {
				// 如果为空直接退出
				log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostContentPlatformToPost end, nowTime: %d", time.Now().Unix())
				return
			}
			uErr := dao.UpdatePostExternalData(data.PostUuid, map[string]interface{}{
				"platform": data.Platform,
			})
			if uErr != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostContentPlatformToPost update post failed, post uuid:[%s], platform:[%s], err: [%v]", data.PostUuid, data.Platform, uErr)
			}
		}
	}()
	// 等待所有的协程完成
	wg.Wait()
	close(channelPostData)
	close(channelPostExternalData)
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostContentPlatformToPost end, nowTime: %d", time.Now().Unix())
}

// 迁移p_official_language到p_post_content表中, params: 参数透传过来的id
func SyncPostOfficialToPost(c context.Context, params string) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostOfficialToPost start, nowTime: %d", time.Now().Unix())

	var gtId int64
	// 转换参数类型，如果报错了直接转成0
	if params != "" {
		var toIntErr error
		gtId, toIntErr = cast.ToInt64E(params)
		if toIntErr != nil {
			gtId = 0
		}
	}
	for {
		postOfficialList, err := dao.GetPostOfficialList(true, gtId, 3000)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostOfficialToPost SyncGetAllPost err, err=(%v)", err)
			return
		}

		postUuidsMap := make(map[string]interface{})

		for _, post := range postOfficialList {
			postUuidsMap[post.PostUUID] = struct{}{}
		}

		var postUuids = make([]string, 0, len(postUuidsMap))
		for k, _ := range postUuidsMap {
			postUuids = append(postUuids, k)
		}

		postList, err := dao.GetPostList(&dao.PostConditions{PostUuids: postUuids, IsIgnore: true}, 0, 0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostOfficialToPost Get post err, post_uuids: [%v], err=(%v)", postUuids, err)
			continue
		}

		for index, official := range postOfficialList {
			var postLanguageMap = make(map[string][]*model.PostLanguageTemp)
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostOfficialToPost officialofficial, official: %v", index)
			var postItem *model.Post
			for _, post := range postList {
				if post.PostUUID == official.PostUUID {
					postItem = post
					var postLanguage = &model.PostLanguageTemp{
						CreatedOn:         post.CreatedOn,
						ModifiedOn:        post.ModifiedOn,
						IsDel:             int(post.IsDel),
						DeletedOn:         post.DeletedOn,
						PostUUID:          post.PostUUID,
						IntlOpenid:        post.IntlOpenid,
						PlateID:           post.PlateID,
						Type:              post.Type,
						IsTop:             post.IsTop,
						TopSort:           post.TopSort,
						TopOn:             post.TopOn,
						IsAudit:           post.IsAudit,
						Visibility:        post.Visibility,
						SocialmediaPostId: post.SocialmediaPostId,
						LatestRepliedOn:   post.LatestRepliedOn,
						GameId:            post.GameId,
						AreaId:            post.AreaId,
						CreatedOnMs:       post.CreatedOnMs,
						Platform:          post.Platform,
						PublishOn:         post.PublishOn,
						IsOfficial:        post.IsOfficial,
					}

					postLanguageMap[official.Language] = append(postLanguageMap[official.Language], postLanguage)
					break
				}
			}
			if postItem == nil {
				// 如果没有找到对应的postItem就退出
				continue
			}

			for language, postLanguageList := range postLanguageMap {
				for _, postLanguageOneRow := range postLanguageList {
					createdOn := postLanguageOneRow.CreatedOn
					modifiedOn := postLanguageOneRow.ModifiedOn
					postLanguageRow, err := dao.GetPostLanguageRow(postLanguageOneRow, language)
					if err != nil {
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostOfficialToPost postLanguageOneRow err, post langauge postLanguageOneRow: [%v],err=(%v)", postLanguageOneRow, err)
						continue
					}
					if postLanguageRow != nil && postLanguageRow.ID > 0 {
						err := dao.UpdatePostLanguage(postLanguageOneRow.PostUUID, map[string]interface{}{
							"created_on":  createdOn,
							"modified_on": modifiedOn,
						}, language)
						if err != nil {
							log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("update post plate failed, err:%v", err)
							continue
						}
					}
				}
			}
			// 获取已有数据
			var postContentItem *model.PostContent
			postContentOneItem, err := dao.PostContentGetIgnoreDeleteWithLanguage(official.PostUUID, official.Language)
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					postContentItem = &model.PostContent{
						Model: &model.Model{
							CreatedOn:  official.CreatedOn,
							ModifiedOn: official.ModifiedOn,
							DeletedOn:  official.DeletedOn,
							IsDel:      official.IsDel,
						},
						PostUUID:        official.PostUUID,
						IntlOpenid:      postItem.IntlOpenid,
						IsEssence:       0,
						EssenceOn:       0,
						IsOriginal:      0,
						OriginalURL:     "",
						Platform:        "",
						OriginalReprint: 0,
						Title:           official.Title,
						Content:         official.Content,
						PicUrls:         official.PicUrls,
						ContentSummary:  official.ContentSummary,
						ExtInfo:         official.ExtInfo,
						Order:           official.Order,
						Language:        official.Language,
					}
					err = dao.CreatePostContent(postContentItem)
					if err != nil {
						log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostOfficialToPost insert post content err, post_uuid: [%v], post content data: [%v],err=(%v)", official.PostUUID, postContentItem, err)
						continue
					}
				} else {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostOfficialToPost GetPostContentList err, post_uuid: [%v], err=(%v)", official.PostUUID, err)
				}
				continue
			} else if postContentOneItem != nil && postContentOneItem.ID > 0 {
				postContentItem = &model.PostContent{
					Model: &model.Model{
						ID:         postContentOneItem.ID,
						CreatedOn:  official.CreatedOn,
						ModifiedOn: official.ModifiedOn,
						DeletedOn:  official.DeletedOn,
						IsDel:      official.IsDel,
					},
					PostUUID:        official.PostUUID,
					IntlOpenid:      postItem.IntlOpenid,
					IsEssence:       0,
					EssenceOn:       0,
					IsOriginal:      0,
					OriginalURL:     "",
					Platform:        "",
					OriginalReprint: 0,
					Title:           official.Title,
					Content:         official.Content,
					PicUrls:         official.PicUrls,
					ContentSummary:  official.ContentSummary,
					ExtInfo:         official.ExtInfo,
					Order:           official.Order,
					Language:        official.Language,
				}
				err = dao.UpdatePostContent(postContentItem)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostOfficialToPost update post content err, post_uuid: [%v], post content data: [%v],err=(%v)", official.PostUUID, postContentItem, err)
					continue
				}
			}

		}

		for _, postItem := range postList {
			postContentList, err := dao.PostContentGetIgnoreDeleteV2(postItem.PostUUID)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostOfficialToPost PostContentGet err,postItem.PostUUID=(%v), err=(%v)", postItem.PostUUID, err)
				return
			}

			tags, err := dao.GetTagIdByPostUUIDV2(postItem.PostUUID)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostOfficialToPost err, GetTagIdByPostUUID failed, post_uuid:(%s), err=(%v)", postItem.PostUUID, err)
				return
			}

			var intlUserOpenid string
			if strings.Contains(postItem.IntlOpenid, "-") && len(strings.Split(postItem.IntlOpenid, "-")) > 1 {
				intlUserOpenid = strings.Split(postItem.IntlOpenid, "-")[1]
			}
			data := map[string]interface{}{
				"id":                  postItem.ID,
				"post_uuid":           postItem.PostUUID,
				"plate_id":            postItem.PlateID,
				"intl_openid":         postItem.IntlOpenid,
				"intl_user_openid":    intlUserOpenid,
				"type":                postItem.Type,
				"comment_count":       0,
				"collection_count":    0,
				"upvote_count":        0,
				"upvote_map":          "{}",
				"browse_count":        0,
				"forward_count":       0,
				"power_num":           0,
				"visibility":          postItem.Visibility,
				"is_top":              postItem.IsTop,
				"top_sort":            postItem.TopSort,
				"top_on":              postItem.TopOn,
				"tags":                tags,
				"socialmedia_post_id": postItem.SocialmediaPostId,
				"latest_replied_on":   postItem.LatestRepliedOn,
				"created_on":          postItem.CreatedOn,
				"created_on_ms":       postItem.CreatedOnMs,
				"modified_on":         postItem.ModifiedOn,
				"hot_num":             0,
				"game_id":             postItem.GameId,
				"area_id":             postItem.AreaId,
				"is_audit":            postItem.IsAudit,
				"is_del":              postItem.IsDel,
				"text_risk_level":     1,
				"text_risk_type":      100,
				"pic_risk_level":      1,
				"pic_risk_type":       100,
				"audit_status":        2,
				"audit_user":          "admin",
				"audit_on":            postItem.CreatedOn,
				"is_official":         postItem.IsOfficial,
				"publish_on":          postItem.PublishOn,
				"machine_status":      1, //机审状态默认是0
				"artificial_status":   0, // 人审的状态默认是0
			}
			postLanguages := make([]string, 0)
			j := 0
			for i, content := range postContentList {
				if content.Language == "" {
					j++
					continue
				}
				if i == j {
					data["language"] = content.Language
					data["title"] = content.Title
					data["content"] = content.Content
					data["content_summary"] = content.ContentSummary
					data["pic_urls"] = content.PicUrls
					data["essence_on"] = content.EssenceOn
					data["is_essence"] = content.IsEssence
					data["is_original"] = content.IsOriginal
					data["original_url"] = content.OriginalURL
					data["platform"] = content.Platform
					data["ext_info"] = content.ExtInfo
				}
				postLanguages = append(postLanguages, content.Language)
				// 区分设置每个语言的汇总内容，用于前端搜索。这里是因为ElasticSearch的一个字段只能配置一种分词器
				allContantLangKey := fmt.Sprintf("all_content_lang_%s", content.Language)
				var allContantLangVal string
				var postContentStr string
				if content.ContentSummary != "" {
					postContentStr = content.ContentSummary
				} else {
					postContentStr = content.Content
				}
				// 目前只配置了这几种语言的字段，后续要新增再继续追加
				allContantLangVal = fmt.Sprintf("%s|||%s", content.Title, postContentStr)
				data[allContantLangKey] = allContantLangVal

			}
			data["post_languages"] = postLanguages
			dao.EsBulkPushDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, "post_uuid", []map[string]interface{}{data})
		}

		if len(postOfficialList) < 1000 {
			break
		}
		gtId = postOfficialList[len(postOfficialList)-1].ID
	}

	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostOfficialToPost end, nowTime: %d", time.Now().Unix())
	fmt.Println("----------------------------------同步完成-------------------------------------------")
}

// 迁移p_post中的数据到p_post_en/zh/ko/ja/zh-tw中
// 迁移点赞等数据到p_post_state中

func SyncPostToLanguage(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostToLanguage start, nowTime: %d", time.Now().Unix())
	var wg sync.WaitGroup
	// 开启两个协程，一个处理p_post表，忽略官方动态；另外一个专门迁移点赞等统计数据
	wg.Add(1)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		defer wg.Done() // 函数结束时减少计数器
		posts, err := dao.SyncGetAllPost(0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToLanguage SyncGetAllPost err, err=(%v)", err)
			return
		}
		var postLanguageList = make(map[string][]*model.PostLanguage)
		for _, post := range posts {
			if post.IsOfficial == 1 {
				continue
			}
			var postLanguage = &model.PostLanguage{}
			copier.Copy(&postLanguage, &post)
			postLanguageList[post.Language] = append(postLanguageList[post.Language], postLanguage)
			if len(postLanguageList[post.Language]) == 1000 {
				err = dao.BatchInsertPostLanguage(postLanguageList[post.Language], post.Language)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToLanguage insert post language err, post langauge data: [%v],err=(%v)", postLanguageList[post.Language], err)
					continue
				}
			}
		}
		for language, data := range postLanguageList {
			err = dao.BatchInsertPostLanguage(data, language)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToLanguage insert post language err, post langauge data: [%v],err=(%v)", data, err)
				continue
			}
		}
	}()
	wg.Wait()
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostToLanguage end, nowTime: %d", time.Now().Unix())
	fmt.Println("----------------------------------同步完成-------------------------------------------")
}

func SyncPostToStats(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostToStats start, nowTime: %d", time.Now().Unix())
	var nextPageCursor string
	var sortBys []es7.Sorter
	var limit = 3000
	sortQuery := es7.NewFieldSort("created_on_ms").Desc()
	sortByIdQuery := es7.NewFieldSort("id").Desc()
	sortBys = append(sortBys, sortQuery, sortByIdQuery)
	var lastSortValue []interface{}
	for {
		if nextPageCursor != "" {
			cursorStr, err := util.DecryptPageCursorS(nextPageCursor)
			if err != nil {
				errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
				break
			}
			err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
			if err != nil {
				errs.NewCustomError(c, code.GetIndexPostFailed, "queryByContent2 | Failed to get idCursor")
				break
			}
		}
		resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, es7.NewBoolQuery().Must(es7.NewMatchAllQuery()), sortBys, lastSortValue, int64(limit))
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToStats query data failed, err: %v", err)
			break
		}
		if !(resp != nil && resp.Hits != nil && len(resp.Hits.Hits) > 0) {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Info("SyncPostToStats query data is empty")
			break
		}
		postForm, err := postsFromBySync(c, resp.Hits.Hits, limit)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToStats form data failed, err: %v", err)
			break
		}
		var postStatsList = make([]*model.PostStats, 0, limit)
		for _, post := range postForm.List {
			upvoteMap, _ := json.Marshal(post.UpvoteMap)
			var postStats = &model.PostStats{
				PostUUID:        post.PostUuid,
				PowerNumFloat:   post.PowerNum,
				CommentCount:    post.CommentCount,
				CollectionCount: post.CollectionCount,
				UpvoteMap:       string(upvoteMap),
				UpvoteCount:     post.UpvoteCount,
				BrowseCount:     post.BrowseCount,
				ForwardCount:    int64(post.ForwardCount),
			}

			postStatsList = append(postStatsList, postStats)
			if len(postStatsList) == 1000 {
				err = dao.BatchCreatePostStats(postStatsList)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToLanguage insert post stats err, post stats data: [%v],err=(%v)", postStatsList, err)
					continue
				}
				postStatsList = make([]*model.PostStats, 0, 1000)
			}
		}
		if len(postStatsList) > 0 {
			err = dao.BatchCreatePostStats(postStatsList)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToLanguage insert post stats err, post stats data: [%v],err=(%v)", postStatsList, err)
			}
		}
		if len(postStatsList) < limit {
			break
		}
		nextPageCursor = postForm.PageInfo.NextPageCursor
	}
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostToStats end, nowTime: %d", time.Now().Unix())
}

// 根据db数据重新刷新统计数据
func SyncPostToStatsV2(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostToStatsV2 start, nowTime: %d", time.Now().Unix())

	// 查询所有评论数据
	var conditions *dao.CommentListConditions
	conditions = &dao.CommentListConditions{
		IsAudit: 1,
		IsDel:   0,
	}
	comments, err := dao.CommentList(conditions, 3000)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToStatsV2 CommentList err, err=(%v)", err)
	}
	postuuidCommentCountMap := make(map[string]int)
	for _, commentItem := range comments {
		if _, ok := postuuidCommentCountMap[commentItem.PostUUID]; !ok {
			postuuidCommentCountMap[commentItem.PostUUID] = 1
		} else {
			postuuidCommentCountMap[commentItem.PostUUID]++
		}
	}
	for postuuid, commentCount := range postuuidCommentCountMap {
		dao.UpdatePostStatsComment(postuuid, int32(commentCount))
		doc := map[string]interface{}{
			"comment_count": commentCount,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postuuid, doc)
	}

	// 查询所有动态收藏数据
	conditions2 := &dao.PostCollectionConditions{}
	collections, err := dao.PostCollectionListNotPost(conditions2, 1000)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToStatsV2 PostCollectionListNotPost err, err=(%v)", err)
	}
	postuuidCollectionCountMap := make(map[string]int)
	for _, collectionItem := range collections {
		if _, ok := postuuidCollectionCountMap[collectionItem.PostUUID]; !ok {
			postuuidCollectionCountMap[collectionItem.PostUUID] = 1
		} else {
			postuuidCollectionCountMap[collectionItem.PostUUID]++
		}
	}
	for postuuid, collectionCount := range postuuidCollectionCountMap {
		dao.UpdatePostStatsCollection(postuuid, int64(collectionCount))
		doc := map[string]interface{}{
			"collection_count": collectionCount,
		}
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postuuid, doc)
	}

	// 查询所有用户点赞数据
	postsStars, err := dao.PostStarList(&dao.PostStarConditions{}, 0, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToStatsV2 PostStarList err, err=(%v)", err)
	}
	postuuidStarCountMap := make(map[string]map[int64]int64)
	for _, postsStarItem := range postsStars {
		if _, ok := postuuidStarCountMap[postsStarItem.PostUUID]; !ok {
			postuuidStarCountMap[postsStarItem.PostUUID] = make(map[int64]int64)
			postuuidStarCountMap[postsStarItem.PostUUID][postsStarItem.StarType] = 1
		} else {
			postuuidStarCountMap[postsStarItem.PostUUID][postsStarItem.StarType]++
		}
	}

	for postuuid, upvoteMap := range postuuidStarCountMap {
		err := dao.PostStatsUpvoteCountUpdate(postuuid, upvoteMap)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToStatsV2 PostStatsUpvoteCountUpdate err, postUUID=(%s),err=(%v)", postuuid, err)
		}

		// 更新索引
		upvoteMapJson, err := json.Marshal(upvoteMap)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostToStatsV2 json.Marshal upvoteMap err, postUUID=(%s),err=(%v)", postuuid, err)
		}
		var upvoteCount int64
		if len(upvoteMap) > 0 {
			for _, upvoteI := range upvoteMap {
				upvoteCount += upvoteI
			}
		}
		doc := map[string]interface{}{
			"upvote_map":   string(upvoteMapJson),
			"upvote_count": upvoteCount,
		}
		// 更新所有语言下的动态
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postuuid, doc)
	}

	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostToStatsV2 end, nowTime: %d", time.Now().Unix())
}

// 根据db数据重新刷新统计数据
func SyncPostStarToStatsV1(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostStarToStatsV1 start, nowTime: %d", time.Now().Unix())

	// 查询所有用户点赞数据
	postuuidStarCountMap := make(map[string]map[int64]int64)
	id := 0
	limit := 500
	for {
		postsStars, err := dao.PostStarListV2(&dao.PostStarConditions{}, id, limit)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostStarToStatsV1 PostStarList err, err=(%v)", err)
		}
		for _, postsStarItem := range postsStars {
			if _, ok := postuuidStarCountMap[postsStarItem.PostUUID]; !ok {
				postuuidStarCountMap[postsStarItem.PostUUID] = make(map[int64]int64)
				postuuidStarCountMap[postsStarItem.PostUUID][postsStarItem.StarType] = 1
			} else {
				postuuidStarCountMap[postsStarItem.PostUUID][postsStarItem.StarType]++
			}
		}
		if len(postsStars) == limit {
			id = int(postsStars[len(postsStars)-1].ID)
		} else {
			break
		}
	}

	count := 0
	for postuuid, upvoteMap := range postuuidStarCountMap {
		err := dao.PostStatsUpvoteCountUpdate(postuuid, upvoteMap)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostStarToStatsV1 PostStatsUpvoteCountUpdate err, postUUID=(%s),err=(%v)", postuuid, err)
		}

		// 更新索引
		upvoteMapJson, err := json.Marshal(upvoteMap)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostStarToStatsV1 json.Marshal upvoteMap err, postUUID=(%s),err=(%v)", postuuid, err)
		}
		var upvoteCount int64
		if len(upvoteMap) > 0 {
			for _, upvoteI := range upvoteMap {
				upvoteCount += upvoteI
			}
		}
		doc := map[string]interface{}{
			"upvote_map":   string(upvoteMapJson),
			"upvote_count": upvoteCount,
		}
		// 更新所有语言下的动态
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postuuid, doc)
		count++
		if count > 100 {
			count = 0
			time.Sleep(1 * time.Second)
		}
	}

	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostStarToStatsV1 end, nowTime: %d", time.Now().Unix())
}

// 同步p_post的数据到p_post_content
func SyncPostLanguageToContent(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostLanguageToContent start, nowTime: %d", time.Now().Unix())
	var wg sync.WaitGroup
	// 开启两个协程，一个处理p_post表，忽略官方动态；另外一个专门迁移点赞等统计数据
	wg.Add(1)
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		defer wg.Done() // 函数结束时减少计数器
		posts, err := dao.SyncGetAllPost(0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostLanguageToContent SyncGetAllPost err, err=(%v)", err)
			return
		}
		for _, post := range posts {
			if post.IsOfficial == 1 {
				continue
			}
			err := dao.UpdatePostContentInfoIgnoreDel(post.PostUUID, map[string]interface{}{
				"language": post.Language,
			})
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostLanguageToContent UpdatePostContentInfoIgnoreDel err,post_uuid:[%s], language:[%s], err=(%v)", post.PostUUID, post.Language, err)
				continue
			}
		}

	}()
	wg.Wait()
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostLanguageToContent end, nowTime: %d", time.Now().Unix())
	fmt.Println("----------------------------------同步完成-------------------------------------------")
}

// 同步举报中的语言
func SyncPostReportLanguage(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("sync report language start, at: %d", time.Now().Unix())
	// 获取用户审核表数据
	fmt.Println("-----------------------------开始同步------------------------------")
	var limit = 10000
	list, err := dao.GetPostReportList(limit)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostReportLanguage | get post comment list failed, limit: %d, err:%v", limit, err)
		return
	}
	var userIntlOpenids = make([]string, 0)
	for _, audit := range list {
		userIntlOpenids = append(userIntlOpenids, audit.ReportIntlOpenid)
	}

	if len(userIntlOpenids) == 0 {
		return
	}

	userList, err := dao.GetUserListByOpenidV2(userIntlOpenids, true)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostReportLanguage | get user list failed, intl_openids: %v, err:%v", userIntlOpenids, err)
		return
	}
	var updateData = make(map[string][]int64)
	var updateReportData = make(map[string]string)
	for _, report := range list {
		for _, content := range userList {
			if report.ReportIntlOpenid == content.IntlOpenid {
				updateData[content.Language] = append(updateData[content.Language], report.ID)
				if report.ContentType != constants.REPORT_CONTENT_TYPE_POST {
					updateReportData[cast.ToString(report.ID)] = content.Language
				}
			}
		}
	}
	err = dao.BatchUpdateReportLanguage(updateData)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostReportLanguage | update post report language failed, update_data: %+v, err:%v", updateData, err)
		return
	}

	for id, lang := range updateReportData {

		_, err = dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.ReportIndex, id, map[string]interface{}{
			"language": lang,
		})
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("sync report language es failed, id: %v,err: %v", id, err)
		}
	}
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.ReportIndex)
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("sync report language end, at: %d", time.Now().Unix())
}

// 刷新帖子审核表的数据
func RefreshPostAudit(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("RefreshPostAudit start time at: %d", time.Now().Unix())
	list, err := dao.GetAllPostAuditList(10000)
	if err != nil {
		// 报错
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RefreshPostAudit | get all post audit list failed, err: %v", err)
		return
	}
	posts, err := dao.SyncGetAllPost(10000)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("RefreshPostAudit | get all post list failed, err: %v", err)
		return
	}
	var postDeleteUuids []string
	var auditPostUuids []string
	var awaitMachinePostUuid []string
	var passAuditPostUuid []string
	var unPassAuditPostUuid []string
	for _, audit := range list {
		auditPostUuids = append(auditPostUuids, audit.PostUUID)
		for _, post := range posts {
			if audit.PostUUID == post.PostUUID {
				if int(post.IsDel) == 1 {
					// 已删除的
					postDeleteUuids = append(postDeleteUuids, post.PostUUID)
					continue
				}
				if post.IsOfficial == 1 {
					// 官方帖子直接审核通过
					passAuditPostUuid = append(passAuditPostUuid, audit.PostUUID)
					continue
				}
				// 未处理
				if audit.Status == 1 {
					awaitMachinePostUuid = append(awaitMachinePostUuid, audit.PostUUID)
					continue
				}
				// 已通过
				if audit.Status == 2 {
					passAuditPostUuid = append(passAuditPostUuid, audit.PostUUID)
					continue
				}
				// 已忽略
				if audit.Status == 3 {
					unPassAuditPostUuid = append(unPassAuditPostUuid, audit.PostUUID)
					continue
				}
			}
		}

	}
	if len(awaitMachinePostUuid) > 0 {
		// 更新成机审失败
		dao.UpdatePostAuditInfoByBatchPostUuid(awaitMachinePostUuid, map[string]interface{}{
			"machine_status": 2,
			"modified_on":    time.Now().Unix(),
		})
		for _, postUuid := range awaitMachinePostUuid {
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUuid, map[string]interface{}{
				"machine_status": 2,
				"audit_status":   1,
				"modified_on":    time.Now().Unix(),
			})
		}
	}
	if len(passAuditPostUuid) > 0 {
		// 更新成人审通过
		dao.UpdatePostAuditInfoByBatchPostUuid(passAuditPostUuid, map[string]interface{}{
			"artificial_status": 1,
			"modified_on":       time.Now().Unix(),
		})
		for _, postUuid := range passAuditPostUuid {
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUuid, map[string]interface{}{
				"artificial_status": 1,
				"audit_statue":      2,
				"modified_on":       time.Now().Unix(),
			})
		}
	}
	if len(unPassAuditPostUuid) > 0 {
		// 更新成人审拒绝
		dao.UpdatePostAuditInfoByBatchPostUuid(unPassAuditPostUuid, map[string]interface{}{
			"artificial_status": 2,
			"modified_on":       time.Now().Unix(),
		})
		for _, postUuid := range unPassAuditPostUuid {
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUuid, map[string]interface{}{
				"artificial_status": 2,
				"audit_status":      3,
				"modified_on":       time.Now().Unix(),
			})
		}
	}
	if len(postDeleteUuids) > 0 {
		dao.UpdatePostAuditInfoByBatchPostUuid(postDeleteUuids, map[string]interface{}{
			"is_del":     1,
			"deleted_on": time.Now().Unix(),
		})
	}

	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("RefreshPostAudit end time at: %d", time.Now().Unix())

}

// p_post_language中缺失语言同步
func SyncMissedPostLanguageData(c context.Context) {
	// 找到对应用户的所有帖子
	posts, err := dao.FetchPost(&dao.PostConditions{IntlOpenid: "29080-8468458281233829111"}, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncMissedPostLanguageData | get all post list failed, err: %v", err)
		return
	}
	for _, postItem := range posts {
		postContentLanguages, err := dao.GetPostContentList(postItem.PostUUID, false)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncMissedPostLanguageData | get post content list failed: postuuid: %s, err: %v", postItem.PostUUID, err)
		}
		if len(postContentLanguages) == 0 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncMissedPostLanguageData | get post content list empty: postuuid: %s", postItem.PostUUID)
		}
		// 变量每一种语言
		for _, postContent := range postContentLanguages {
			if postContent.Language == "" {
				continue
			}
			// 判断p_post_language中是否存在该语言
			postLanguages, err := dao.FetchPostLanguage(&dao.PostConditions{PostUuids: []string{postItem.PostUUID}}, 0, postContent.Language)
			// 不存在对应多语言数据
			if err != nil || postLanguages == nil || len(postLanguages) == 0 {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncMissedPostLanguageData | Needed to SyncMissedPostLanguageData: postuuid: %s, language: %s", postItem.PostUUID, postContent.Language)
				err := addMissedSiglePostLanguage(postContent.Language, postItem)
				if err != nil {
					log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncMissedPostLanguageData | addMissedSiglePostLanguage failed: postuuid: %s, language: %s, err: %v", postItem.PostUUID, postContent.Language, err)
				}
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncMissedPostLanguageData | addMissedSiglePostLanguage success: postuuid: %s, language: %s", postItem.PostUUID, postContent.Language)
			}
		}
	}
}

// 添加单条缺失数据
func addMissedSiglePostLanguage(language string, post *model.Post) error {
	languageTable := (&model.PostLanguage{}).GetTableName(util.ZHTWLanguageToSql(language))
	if languageTable == "" {
		// todo 报错
		return errors.New("post language table failed")
	}
	var postLanguageData = &model.PostLanguageTemp{}
	postCopyErr := copier.Copy(&postLanguageData, post)
	if postCopyErr != nil {
		// todo 报错
		return errors.New("post language copy post failed")
	}
	// 重置postLanguage的主键id
	postLanguageData.ID = 0
	postLanguageErr := DB.SelectConnect("db_standalonesite").Table(languageTable).Create(postLanguageData).Error
	if postLanguageErr != nil {
		// todo 报错
		return errors.New("create post language failed")
	}
	return nil
}

// 刷新全量的es数据，把open_up改成1
func SyncPostOpenUp(c context.Context) {
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostOpenUp start, nowTime: %d", time.Now().Unix())
	var nextPageCursor string
	var sortBys []es7.Sorter
	var limit = 3000
	sortQuery := es7.NewFieldSort("created_on_ms").Desc()
	sortByIdQuery := es7.NewFieldSort("id").Desc()
	sortBys = append(sortBys, sortQuery, sortByIdQuery)
	var lastSortValue []interface{}
	for {
		if nextPageCursor != "" {
			cursorStr, err := util.DecryptPageCursorS(nextPageCursor)
			if err != nil {
				errs.NewCustomError(c, code.PagingCursorIsInvalidS, "Paging cursor is invalid")
				break
			}
			err = json.Unmarshal([]byte(cursorStr), &lastSortValue)
			if err != nil {
				errs.NewCustomError(c, code.GetIndexPostFailed, "queryByContent2 | Failed to get idCursor")
				break
			}
		}
		resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, es7.NewBoolQuery().Must(es7.NewMatchAllQuery()), sortBys, lastSortValue, int64(limit))
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SyncPostOpenUp query data failed, err: %v", err)
			break
		}
		if !(resp != nil && resp.Hits != nil && len(resp.Hits.Hits) > 0) {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Info("SyncPostOpenUp query data is empty")
			break
		}
		postForm, err := postsFromBySync(c, resp.Hits.Hits, limit)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("SyncPostOpenUp form data failed, err: %v", err)
			break
		}

		for _, post := range postForm.List {
			doc := map[string]interface{}{
				"is_hide": 1,
			}
			dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUuid, doc)
		}

		if len(postForm.List) < limit {
			break
		}
		nextPageCursor = postForm.PageInfo.NextPageCursor
	}
	log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("SyncPostToStats end, nowTime: %d", time.Now().Unix())
}

// 将待审核的帖子post_uuid加到redis
func SyncNeedAuditPostUUIDIntoRedisSet(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncNeedAuditPostUUIDIntoRedisSet start time at: %d", time.Now().Unix())
	// 获取未送审的数据
	list, err := dao.GetPostNotAuditList("16", "global", 0)
	if err != nil {
		// 报错
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncNeedAuditPostUUIDIntoRedisSet | get all post audit list failed, err: %v", err)
		return
	}
	for _, audit := range list {
		SetNeedAuditPostUUIDToCache(c, audit.PostUUID, audit.CreatedOn, 0)
	}

	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncNeedAuditPostUUIDIntoRedisSet end time at: %d", time.Now().Unix())

}

// 将待审核的帖子post_uuid加到redis
func SyncNeedAuditTWPostUUIDIntoRedisSet(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncNeedAuditTWPostUUIDIntoRedisSet start time at: %d", time.Now().Unix())
	// 获取未送审的数据
	list, err := dao.GetPostNotAuditList("16", "tw", 0)
	if err != nil {
		// 报错
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncNeedAuditTWPostUUIDIntoRedisSet | get all post audit list failed, err: %v", err)
		return
	}
	for _, audit := range list {
		SetNeedAuditPostUUIDToCache(c, audit.PostUUID, audit.CreatedOn, 0)
	}

	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncNeedAuditTWPostUUIDIntoRedisSet end time at: %d", time.Now().Unix())

}

// 刷新帖子审核 已审核帖子
func SyncReviewedToPostContentAudit(c context.Context) {
	var limit = 100
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToPostContentAudit | start time at:%v", time.Now().Unix())
	for {
		// 查询单表，查询已审核的数据，status = 1 and (machine_status = 1 or artificial_status = 0) and is_del = 0
		postNotAuditedList, err := dao.GetPostsNotAuditList(limit)
		if err != nil {
			// 记录报错EE
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToPostContentAudit | GetPostsNotAuditList failed, err: %v", err)
			return
		}
		var postUUids []string
		for _, audit := range postNotAuditedList {
			postUUids = append(postUUids, audit.PostUUID)
		}

		// 获取评论的数据
		list, err := dao.GetPostList(&dao.PostConditions{
			PostUuids: postUUids,
			IsIgnore:  true,
		}, 0, len(postUUids))
		if err != nil {
			// 记录报错
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToPostContentAudit | GetPostList failed, postUUids:%v, err:%v", postUUids, err)
			return
		}
		// 机审/人审通过的评论
		// var publishPostUuid []string
		var humanPublishPostUuid []string
		var humanRejectPostUuid []string
		var machinePublishPostUuid []string
		var machineRejectPostUuid []string
		// 机审/人审未通过的评论
		// var rejectPostUuid []string
		for _, audit := range postNotAuditedList {
			for _, post := range list {
				if audit.PostUUID == post.PostUUID && post.IsAudit == 1 {
					// 人审通过
					if audit.ArtificialStatus == 1 {
						humanPublishPostUuid = append(humanPublishPostUuid, audit.PostUUID)
					} else if audit.MachineStatus == 2 {
						// 人审未通过
						humanRejectPostUuid = append(humanRejectPostUuid, audit.PostUUID)
					} else if audit.MachineStatus == 1 {
						// 机审未通过
						machinePublishPostUuid = append(machinePublishPostUuid, audit.PostUUID)
					} else if audit.MachineStatus == 2 {
						machineRejectPostUuid = append(machineRejectPostUuid, audit.PostUUID)
					}
					break
				}
			}
		}
		// 人审通过 （现网无数据）
		// if len(humanPublishPostUuid) == 0 {
		// 	// 记录日志
		// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToPostContentAudit | empty humanPublishPostUuid list")
		// 	// return
		// } else {
		// 	err = dao.UpdatePostsAuditV2(humanPublishPostUuid, map[string]interface{}{
		// 		"status": 2,
		// 	})
		// 	if err != nil {
		// 		// 记录报错
		// 		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToPostContentAudit | UpdatePostsAuditV2 failed, humanPublishPostUuid:%v, err:%v", humanPublishPostUuid, err)
		// 		return
		// 	}
		// 	for _, postuuid := range humanPublishPostUuid {
		// 		doc := map[string]interface{}{
		// 			"status":            2,
		// 			"artificial_status": 1,
		// 		}
		// 		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postuuid, doc)
		// 	}
		// }
		// 机审通过
		if len(machinePublishPostUuid) == 0 {
			// 记录日志
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToPostContentAudit | empty machinePublishPostUuid list")
			// return
		} else {
			err = dao.UpdatePostsAuditV2(machinePublishPostUuid, map[string]interface{}{
				"status": 2,
			})
			if err != nil {
				// 记录报错
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToPostContentAudit | UpdatePostsAuditV2 failed, machinePublishPostUuid:%v, err:%v", machinePublishPostUuid, err)
				return
			}
			// for _, postuuid := range machinePublishPostUuid {
			// 	doc := map[string]interface{}{
			// 		"audit_status":   2,
			// 		"machine_status": 1,
			// 	}
			// 	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postuuid, doc)
			// }
		}
		// 人审不通过 (现网无数据)
		// if len(humanRejectPostUuid) == 0 {
		// 	// 记录日志
		// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToPostContentAudit | empty humanRejectPostUuid list")
		// 	// return
		// } else {
		// 	err = dao.UpdatePostsAuditV2(humanRejectPostUuid, map[string]interface{}{
		// 		"status": 3,
		// 	})
		// 	if err != nil {
		// 		// 记录报错
		// 		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToPostContentAudit | UpdatePostsAuditV2 failed, humanRejectPostUuid:%v, err:%v", humanRejectPostUuid, err)
		// 		return
		// 	}
		// 	for _, postuuid := range humanRejectPostUuid {
		// 		doc := map[string]interface{}{
		// 			"status":            3,
		// 			"artificial_status": 2,
		// 		}
		// 		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postuuid, doc)
		// 	}
		// }
		// 机审不通过
		if len(machineRejectPostUuid) == 0 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToPostContentAudit | empty rejectPostUuid list")
		} else {
			// err = dao.UpdatePostsAuditV2(machineRejectPostUuid, map[string]interface{}{
			// 	"status": 3,
			// })
			// if err != nil {
			// 	// 记录报错
			// 	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncReviewedToPostContentAudit | UpdatePostsAuditV2 failed, rejectPostUuid:%v, err:%v", rejectPostUuid, err)
			// 	return
			// }
			// for _, postUUid := range machineRejectPostUuid {
			// 	doc := map[string]interface{}{
			// 		"machine_status": 3,
			// 	}
			// 	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, postUUid, doc)
			// }
		}
		if limit > len(postNotAuditedList) {
			break
		}
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncReviewedToPostContentAudit | end time at:%v", time.Now().Unix())
	// dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)
}

// 刷新评论的回复数脚本
func SyncPostCommentReplyCountStats(c context.Context) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncPostCommentReplyCountStats | start time at:%v", time.Now().Unix())

	conditions := &dao.CommentListConditions{
		Type: 2,
	}

	comments, err := dao.CommentList(conditions, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostCommentReplyCountStats | Get Comment List failed, err: %v", err)
		return
	}
	var updateReportData = make(map[string]int)

	for _, comment := range comments {
		updateReportData[comment.ReplyUUID]++
	}
	i := 0
	for commentUuid, replyCount := range updateReportData {
		commentState := &model.CommentState{
			CommentUUID: commentUuid,
			ReplyCount:  replyCount,
		}
		err := dao.CommentStateUpdateReplyCount(commentState)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UpdateCommentReplyCount comment content update failed. err: %v, commentstateInfo: %+v", err, commentState)
		}
		if i == 100 {
			i = 0
			time.Sleep(1 * time.Second)
		}
		i++
	}

	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncPostCommentReplyCountStats | end time at:%v", time.Now().Unix())
	// dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetIndex)
}

// 同步刷新帖子审批历史数据
func SyncPostAuditData(c context.Context, postAuditIdStr string) {
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncPostAuditData | start time at:%v", time.Now().Unix())
	var sortValue []interface{}
	var postAuditId int
	if postAuditIdStr != "" {
		postAuditId, _ = strconv.Atoi(postAuditIdStr)
	}
	var count int
	for {
		var allPostAuditMap = make(map[string]*model.PostAudit)
		var postUuids []string
		if count > 10000 {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncPostAuditData | executing at:%v, post audit id: %v", time.Now().Unix(), postAuditId)
			count = 0
		}
		postAudits, err := dao.GetAllPostAuditListV2(postAuditId, 100)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("SyncPostAuditData | Get GetAllPostAuditListV2 failed, err: %v", err)
			return
		}
		// 最后一页没有数据了
		if len(postAudits) == 0 {
			break
		} else {
			postAuditId = int(postAudits[len(postAudits)-1].ID)
			count += len(postAudits)
		}
		for _, postAuditItem := range postAudits {
			postUuids = append(postUuids, postAuditItem.PostUUID)
			allPostAuditMap[postAuditItem.PostUUID] = postAuditItem
		}

		postList, _, err := GetPostForPostAuditTask(c, postUuids, sortValue, 200)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncPostAuditData GetPostForHotTask err.error: %v", err)
			break
			// continue
		}
		for _, postESDataItem := range postList {
			if postAuditItem, ok := allPostAuditMap[postESDataItem.PostUuid]; ok {
				post := &model.Post{
					IsAudit:    postESDataItem.IsAudit,
					IsOfficial: postESDataItem.IsOfficial,
					IntlOpenid: postESDataItem.IntlOpenid,
					PostUUID:   postESDataItem.PostUuid,
					PlateID:    postESDataItem.PlateID,
					Type:       postESDataItem.Type,
					Language:   postESDataItem.Language,
					Model: &model.Model{
						CreatedOn:  postESDataItem.CreatedOn,
						ModifiedOn: postESDataItem.ModifiedOn,
						IsDel:      soft_delete.DeletedAt(postESDataItem.IsDel),
					},
					GameId: postESDataItem.GameId,
					AreaId: postESDataItem.AreaId,
				}
				postAudit := &model.PostAudit{
					Model: &model.Model{
						ID:         postAuditItem.ID,
						CreatedOn:  postAuditItem.CreatedOn,
						ModifiedOn: postAuditItem.ModifiedOn,
						DeletedOn:  postAuditItem.DeletedOn,
						IsDel:      postAuditItem.IsDel,
					},
					PostActionType:   postAuditItem.PostActionType,
					Title:            postAuditItem.Title,
					Content:          postAuditItem.Content,
					ContentSummary:   postAuditItem.ContentSummary,
					PicUrls:          postAuditItem.PicUrls,
					Platform:         postAuditItem.Platform,
					ExtInfo:          postAuditItem.ExtInfo,
					AuditOn:          postAuditItem.AuditOn,
					PicRiskLevel:     postAuditItem.PicRiskLevel,
					PicRiskType:      postAuditItem.PicRiskType,
					TextRiskLevel:    postAuditItem.TextRiskLevel,
					TextRiskType:     postAuditItem.TextRiskType,
					Status:           postAuditItem.Status,
					MachineStatus:    postAuditItem.MachineStatus,
					ArtificialStatus: postAuditItem.ArtificialStatus,
					AuditUser:        postAuditItem.AuditUser,
					AuditIntroduce:   postAuditItem.AuditIntroduce,
					GameID:           postAuditItem.GameID,
					AreaID:           postAuditItem.AreaID,
				}
				// 如果是空的说明是旧数据
				if postAuditItem.Title == "" {
					postAudit.Title = postESDataItem.Title
					postAudit.Content = postESDataItem.Content
					postAudit.ContentSummary = postESDataItem.ContentSummary
					postAudit.PicUrls = postESDataItem.PicUrls
					postAudit.Platform = postESDataItem.Platform
					postAudit.ExtInfo = postESDataItem.ExtInfo
				}
				tags := postESDataItem.Tags
				UpdatePostAuditToES(post, postAudit, tags)
			}
		}
	}
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("SyncPostAuditData | end time at:%v", time.Now().Unix())
	dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.TweetAuditIndex)
}

// 推送post审核记录到es
func UpdatePostAuditToES(post *model.Post, audit *model.PostAudit, tags []int64) {
	// auditStatus := constants.PostAuditUnHandler
	// // 先定义这个机审和人审的状态，因为官方帖子不需要审核的话这两个机审状态也不需要赋值
	// var machineStatus, artificialStatus int
	if post.IsAudit == 1 || post.IsOfficial == 1 {
		audit.Status = constants.PostAuditPush
	}
	// 处理用户intl_user_openid, 区别于intl_openid; ps: 这个intl_openid是一定会有的。
	var intlUserOpenid string
	if strings.Contains(post.IntlOpenid, "-") && len(strings.Split(post.IntlOpenid, "-")) > 1 {
		intlUserOpenid = strings.Split(post.IntlOpenid, "-")[1]
	}

	var allContentLangVal string
	var postContentStr string

	data := map[string]interface{}{
		"id":                audit.ID,
		"post_action_type":  audit.PostActionType,
		"post_uuid":         post.PostUUID,
		"plate_id":          post.PlateID,
		"intl_openid":       post.IntlOpenid,
		"intl_user_openid":  intlUserOpenid,
		"type":              post.Type,
		"language":          post.Language,
		"title":             audit.Title,
		"content":           audit.Content,
		"content_summary":   audit.ContentSummary,
		"pic_urls":          audit.PicUrls,
		"platform":          audit.Platform,
		"ext_info":          audit.ExtInfo,
		"tags":              tags,
		"created_on":        audit.CreatedOn,
		"modified_on":       audit.ModifiedOn,
		"is_del":            audit.IsDel,
		"deleted_on":        audit.DeletedOn,
		"game_id":           audit.GameID,
		"area_id":           audit.AreaID,
		"audit_user":        audit.AuditUser,
		"audit_on":          audit.AuditOn,
		"audit_introduce":   audit.AuditIntroduce,
		"status":            audit.Status,
		"machine_status":    audit.MachineStatus,    //机审状态默认是0
		"artificial_status": audit.ArtificialStatus, // 人审的状态默认是0
		"pic_risk_type":     audit.PicRiskType,
		"text_risk_type":    audit.TextRiskType,
		"pic_risk_level":    audit.PicRiskLevel,
		"text_risk_level":   audit.TextRiskLevel,
	}
	if post.Language == "en" || post.Language == "ja" || post.Language == "ko" || post.Language == "zh" || post.Language == "zh-TW" {
		// 目前只配置了这几种语言的字段，后续要新增再继续追加
		allContentLangKey := fmt.Sprintf("all_content_lang_%s", post.Language)
		if audit.ContentSummary != "" {
			postContentStr = audit.ContentSummary
		} else {
			postContentStr = audit.Content
		}
		allContentLangVal = fmt.Sprintf("%s|||%s", audit.Title, postContentStr)
		data[allContentLangKey] = allContentLangVal
	}

	postDoc := make([]map[string]interface{}, 0)
	postDoc = append(postDoc, data)
	isSuccess, err := dao.EsBulkPushDoc(config.GetConfig().ElasticSearchSetting.TweetAuditIndex, "id", postDoc)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushPostAuditToES EsPutDoc error: %v, postDoc: %+v", err, postDoc)
		return
	}
	if !isSuccess {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("PushPostAuditToES EsBulkPushDoc fail; postDoc: %+v", postDoc)
		return
	}
}

func GetPostForPostAuditTask(c context.Context, postUuids []string, lastSortValue []interface{}, limit int64) ([]*model.ESPost, []interface{}, error) {
	esPostList := make([]*model.ESPost, 0)
	var lastHitSort []interface{}
	boolQuery := es7.NewBoolQuery()
	for _, postUuid := range postUuids {
		boolQuery.Should(elastic.NewTermQuery("post_uuid", postUuid))
	}

	var sortBys []es7.Sorter
	sortQuery := es7.NewFieldSort("id").Desc()
	sortBys = append(sortBys, sortQuery)
	resp, err := dao.EsQuery(config.GetConfig().ElasticSearchSetting.TweetIndex, boolQuery, sortBys, lastSortValue, limit)
	if err != nil {
		log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostForPostAuditTask EsQuery err, err=(%v)", err)
		return esPostList, lastHitSort, err
	}
	if resp == nil || resp.Hits == nil || len(resp.Hits.Hits) == 0 {
		return esPostList, lastHitSort, nil
	}
	for _, hit := range resp.Hits.Hits {
		espost := &model.ESPost{}
		raw, err := json.Marshal(hit.Source)
		if err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostForPostAuditTask json.Marshal err, err=(%v)", err)
			continue
		}
		if err = json.Unmarshal(raw, espost); err != nil {
			log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetPostForPostAuditTask json.Unmarshal err, err=(%v)", err)
			continue
		}
		esPostList = append(esPostList, espost)
	}
	lastHitSort = resp.Hits.Hits[len(resp.Hits.Hits)-1].Sort
	return esPostList, lastHitSort, nil
}

func SyncPostToPostAudit(c context.Context, params string) {
	log.WithFieldsContext(c, "logs_type", constants.LogType_Standalonesite).Infof("SyncPostToPostAudit | star time: %d", time.Now().Unix())
	var postUuidData []string
	// 切割params
	if strings.Contains(params, ",") {
		postUuidData = strings.Split(params, ",")
	}
	if len(postUuidData) == 0 {
		return
	}
	postuuidMap := make(map[string]struct{})
	for _, postUuid := range postUuidData {
		postuuidMap[postUuid] = struct{}{}
	}
	var postUuids []string
	for postUuid, _ := range postuuidMap {
		postUuids = append(postUuids, postUuid)
	}
	postList, err := dao.GetPostList(&dao.PostConditions{PostUuids: postUuids}, 0, 0)
	if err != nil {
		// 记录报错
		log.WithFieldsContext(c, "logs_type", constants.LogType_Standalonesite).Errorf("SyncPostToPostAudit | get postlist failed, post_uuids:[%v], err: %v", postUuids, err)
		return
	}
	// 查询当前帖子关联的话题
	tagWithPostList, err := dao.GetTagIdsByPostUUIds(postUuids)
	if err != nil {
		// 记录报错
		log.WithFieldsContext(c, "logs_type", constants.LogType_Standalonesite).Errorf("SyncPostToPostAudit | get post with tags list failed, post_uuids:[%v], err: %v", postUuids, err)
		return
	}
	var postTagMap = make(map[string][]int64)
	for _, tag := range tagWithPostList {
		postTagMap[tag.PostUUID] = append(postTagMap[tag.PostUUID], tag.TagID)
	}
	postContentList, err := dao.GetPostContentByPostOpenids(postUuids)
	if err != nil {
		// 记录报错
		log.WithFieldsContext(c, "logs_type", constants.LogType_Standalonesite).Errorf("SyncPostToPostAudit | get post content failed, post_uuids:[%v], err: %v", postUuids, err)
	}
	for _, post := range postList {
		if post.IsOfficial == 1 {
			// 官贴跳出循环
			post.IsAudit = 1
		}
		var postContents = make([]*model.PostContent, 0)
		for _, content := range postContentList {
			if content.PostUUID == post.PostUUID {
				postContents = append(postContents, content)
			}
		}
		var tags []int64
		if tagIds, ok := postTagMap[post.PostUUID]; ok {
			tags = tagIds
		}
		_, err = CreatePostAudit(c, post, postContents, tags, int(post.IsAudit), false)
		// 记录添加错误 需要回滚
		if err != nil {
			// 记录错误
			log.WithFieldsContext(c, "logs_type", constants.LogType_Standalonesite).Errorf("SyncPostToPostAudit | created post audit failed, post:[%+v], post_content:[%+v], tags: [%v], err: %v", post, postContents, tags, err)
			continue
		}

		PushPostToSearch(post, postContents, tags, 0, false)

		userState, err := dao.GetUserStateByUserOpenid(post.IntlOpenid)
		if err == nil {
			userState.AllPostNum++
			userDoc := map[string]interface{}{
				"all_post_num": userState.AllPostNum,
			}
			// 更新用户发布的动态数
			dao.UpdateUserPostNum(userState.IntlOpenid, userState.AllPostNum, userState.PostNum)
			go func() {
				defer recovery.CatchGoroutinePanic(context.Background())
				dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserInfoIndex, post.IntlOpenid, userDoc)
			}()
		}
		go cache.DeleteUserPostsCache(post.IntlOpenid, "", 10)
		if post.IsOfficial == 1 {
			go SetOfficialPublishPostToCache(context.Background(), post.PostUUID, post.PublishOn)
		}
	}

	log.WithFieldsContext(c, "logs_type", constants.LogType_Standalonesite).Infof("SyncPostToPostAudit | end time: %d", time.Now().Unix())

}
