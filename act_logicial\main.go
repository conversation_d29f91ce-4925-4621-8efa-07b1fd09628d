package main

import (
	captchaPb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/captcha"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	deltaversePb "git.code.oa.com/iegg_distribution/Marketing_group/act.common/deltaverse"
	_ "git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/log"
	_ "git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	trpcKafka "git.code.oa.com/trpc-go/trpc-database/kafka"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	trpc "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-log-cls"
	_ "git.code.oa.com/trpc-go/trpc-metrics-prometheus"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-skywalking"
	addressPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_address"
	cardPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_card"
	commonPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_common"
	creatorhubPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_creatorhub"
	dragonact0704Pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_dragonact0704"
	lotteryPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_lottery"
	missionPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_mission"
	nikketmpPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_nikke_tmp"
	reservationPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_reservation"
	sharePb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_share"
	signinPb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_signin"
	votePb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_vote"
	cagTmpPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_cag_tmp"
	dfTransferPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_account_tranfer"
	dfBhdPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_bhd"
	dfGlobalChallengePb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_global_challenge"
	dfPveSpeedPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_pve_speed"
	dfTmpPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_df_tmp"
	exobroneFriendsInvitePb "git.woa.com/trpcprotocol/publishing_marketing/logicial_exoborne_friends_invite"
	exobrntmpPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_exobrn_tmp"
	hoktmpPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_hok_tmp"
	"git.woa.com/trpcprotocol/publishing_marketing/logicial_lip_points"
	nbaPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nba_tmp"
	networkAgent "git.woa.com/trpcprotocol/publishing_marketing/logicial_network_agent"
	nikkeTwondAnniversaryPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_2nd_anniversary"
	nikkeMbtiPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_mbti"
	nikkeMusicPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_music"
	nikkevotePb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nikkevote0615"
	pointPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_point"
	recordPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_record"
	shareTeamPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_share_team"
	signupPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_signup"
	tarisInvitationPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_taris_invitation"
	taristmpPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_taristmp"
	teamPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_team"
	userdataPb "git.woa.com/trpcprotocol/publishing_marketing/logicial_userdata"
	warframeTmp "git.woa.com/trpcprotocol/publishing_marketing/logicial_warframe_tmp"
	local "git.woa.com/wegame_app_go/common/trpc-config-local"
	_ "go.uber.org/automaxprocs"
	"go.uber.org/automaxprocs/maxprocs"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/controller/address"
	"trpc.act.logicial/app/controller/cag_tmp"
	"trpc.act.logicial/app/controller/card"
	"trpc.act.logicial/app/controller/common"
	"trpc.act.logicial/app/controller/creatorhub"
	dfBhd "trpc.act.logicial/app/controller/df_bhd"
	"trpc.act.logicial/app/controller/df_global_challenge"
	"trpc.act.logicial/app/controller/df_pve_speed"
	"trpc.act.logicial/app/controller/df_tmp"
	dfTransfer "trpc.act.logicial/app/controller/df_transfer"
	"trpc.act.logicial/app/controller/dragonact0704"
	exoborneFriendsInvite "trpc.act.logicial/app/controller/exoborne_friends_invite"
	exobrntmp "trpc.act.logicial/app/controller/exobrn_tmp"
	hoktmp "trpc.act.logicial/app/controller/hok_tmp"
	"trpc.act.logicial/app/controller/lip_points"
	"trpc.act.logicial/app/controller/lottery"
	"trpc.act.logicial/app/controller/mission"
	"trpc.act.logicial/app/controller/nba_tmp"
	network_agent "trpc.act.logicial/app/controller/network_agent"
	nikkeTwondAnniversary "trpc.act.logicial/app/controller/nikke_2nd_anniversary"
	nikkembti "trpc.act.logicial/app/controller/nikke_mbti"
	nikkemusic "trpc.act.logicial/app/controller/nikke_music"
	nikketmp "trpc.act.logicial/app/controller/nikke_tmp"
	"trpc.act.logicial/app/controller/nikkevote0615"
	"trpc.act.logicial/app/controller/point"
	"trpc.act.logicial/app/controller/record"
	"trpc.act.logicial/app/controller/reservation"
	"trpc.act.logicial/app/controller/share"
	"trpc.act.logicial/app/controller/share_team"
	"trpc.act.logicial/app/controller/signin"
	"trpc.act.logicial/app/controller/signup"
	tarisinvitation "trpc.act.logicial/app/controller/taris_invitation"
	"trpc.act.logicial/app/controller/taristmp"
	"trpc.act.logicial/app/controller/team"
	"trpc.act.logicial/app/controller/userdata"
	"trpc.act.logicial/app/controller/vote"
	"trpc.act.logicial/app/controller/warframe"
	"trpc.act.logicial/app/handler"
)

func changeDefaultConfig() {
	// 默认mysql连接DB
	mysql.DefaultConnectName = "logicial"
	// 修改redis默认连接
	redis.DefaultConnectName = "logicial"
	// 修改es默认链接
	// elasticsearch.DefaultConnectName = "logicial"
	// 修改trpc_go文件路径
	trpc.ServerConfigPath = "./conf/config/trpc_go.yaml"
	// 服务配置路径
	local.ServiceYamlPath = "./conf/"
	// client.yaml路径
	local.ClientYamlName = "client/client.yaml"
}
func main() {
	// 修改默认配置
	changeDefaultConfig()

	s := trpc.NewServer()

	var err error
	// 初始化数据库配置
	err = database.InitConfig()
	if err != nil {
		panic(err)
	}
	err = deltaversePb.InitConfig()
	if err != nil {
		log.Errorf("deltaverse init err=%v", err)
	}
	err = captchaPb.InitConfig()
	if err != nil {
		log.Errorf("captcha init err=%v", err)
	}
	maxprocs.Set(maxprocs.Logger(log.Debugf))

	// 初始化自定义配置文件
	_, err = config.Init()
	if err != nil {
		panic(err)
	}
	// report.InitSocialReport()
	// tglog初始化
	// err = tglog.InitConfig()
	// if err != nil {
	// 	log.Errorf("tglog init err=%v", err)
	// }
	// 注册服务
	reservationPb.RegisterReservationService(s, &reservation.ReservationServiceTmp{})
	sharePb.RegisterShareService(s, &share.ShareServiceTmp{})
	lotteryPb.RegisterLotteryService(s, &lottery.LotteryImpl{})
	commonPb.RegisterCommonService(s, &common.CommonImpl{})
	addressPb.RegisterAddressService(s, &address.AddressImpl{})
	creatorhubPb.RegisterCreatorhubService(s, &creatorhub.CreatorhubImpl{})
	shareTeamPb.RegisterShareTeamService(s, &share_team.TeamServiceImpl{})
	teamPb.RegisterTeamService(s, &team.TeamServiceImpl{})
	recordPb.RegisterRecordService(s, &record.RecordImpl{})
	votePb.RegisterVoteService(s, &vote.VoteServiceImpl{})
	missionPb.RegisterMissionService(s, &mission.MissionServiceImpl{})
	signinPb.RegisterSigninService(s, &signin.SigninImpl{})
	cardPb.RegisterCardService(s, &card.CardImpl{})
	signupPb.RegisterSignupService(s, &signup.SignupImpl{})
	logicial_lip_points.RegisterLipPointsService(s, &lip_points.ReissuePointsImpl{})

	// 临时gy玩法
	nikkevotePb.RegisterNikkevote0615Service(s, &nikkevote0615.NikkevoteServiceImpl{})
	dragonact0704Pb.RegisterDragonact0704Service(s, &dragonact0704.Dragonact0704Impl{})
	nikketmpPb.RegisterNikkeTmpService(s, &nikketmp.NikkeTmpImpl{})
	hoktmpPb.RegisterHokTmpService(s, &hoktmp.HokTmpImpl{})
	taristmpPb.RegisterTaristmpService(s, &taristmp.TaristmpImpl{})
	// nikkeMbtiPb.RegisterNikkeMbtiService(s, &nikkeMbti.nikkeMbtiImpl{})
	nikkeMbtiPb.RegisterNikkeMbtiService(s, &nikkembti.NikkeMbtiImpl{})
	nikkeMusicPb.RegisterNikkeMusicService(s, &nikkemusic.NikkeMusicImpl{})
	nikkeTwondAnniversaryPb.RegisterNikke2NdAnniversaryService(s, &nikkeTwondAnniversary.Nikke2ndAnniversaryImpl{})
	tarisInvitationPb.RegisterTarisInvitationService(s, &tarisinvitation.TarisInvitationTmpl{})
	nbaPb.RegisterNbaTmpService(s, &nba_tmp.NbaTmpImpl{})
	dfTmpPb.RegisterDfTmpService(s, &df_tmp.DfTmpImpl{})
	dfGlobalChallengePb.RegisterDfGlobalChallengeService(s, &df_global_challenge.DfGlobalChallengeImpl{})
	dfPveSpeedPb.RegisterDfPveSpeedService(s, &df_pve_speed.DfPveSpeedImpl{})
	dfBhdPb.RegisterDfBhdService(s, &dfBhd.DfBhdImpl{})
	exobrntmpPb.RegisterExobrnTmpService(s, &exobrntmp.ExobrnTmpImpl{})
	exobroneFriendsInvitePb.RegisterExoborneFriendsInviteService(s, &exoborneFriendsInvite.ExobrnFriendsInviteImpl{})
	warframeTmp.RegisterWarframeTmpService(s, &warframe.WarframeImpl{})
	userdataPb.RegisterUserdataService(s, &userdata.UserdataImpl{})
	dfTransferPb.RegisterDfAccountTranferService(s, &dfTransfer.DfAccountTranferImpl{})
	cagTmpPb.RegisterCagTmpService(s, &cag_tmp.CagTmpImpl{})
	pointPb.RegisterPointService(s, &point.PointImpl{})
	// kafka
	if startKafka := config.GetConfig().Nikke2Point5Anniversary.StartKafka; startKafka {
		trpcKafka.RegisterBatchHandlerService(s.Service("trpc.kafka.nikke_2Point5_anniversary_vote_consumer.service"),
			handler.Anniversary2Point5thVoteHandler)
	}
	if startKafka := config.GetConfig().LipNikke2Point.StartKafka; startKafka {
		trpcKafka.RegisterBatchHandlerService(s.Service("trpc.kafka.lip_points_midaspay_act_logicial.service"),
			handler.UserMidasPayMsgHandler)
	}
	// 网络代理
	networkAgent.RegisterRemoteAgentService(s, &network_agent.NetworkImp{})
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
