package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm/clause"
	"trpc.publishing_application.standalonesite/app/model"
)

func CreateSubmissionPost(intlOpenid string, chUid string, postUid string, subMissionId string) error {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChSubmissionPost{}).TableName())
	err := tx.Clauses((clause.OnConflict{
		Columns: []clause.Column{
			{
				Name: "intl_openid",
			},
			{
				Name: "ch_uid",
			},
			{
				Name: "submission_id",
			},
			{
				Name: "post_uid",
			},
		},
		DoUpdates: clause.AssignmentColumns([]string{"is_del", "deleted_on"}),
	})).Create(&map[string]interface{}{
		"intl_openid":   intlOpenid,
		"ch_uid":        chUid,
		"submission_id": subMissionId,
		"post_uid":      postUid,
		"is_del":        0,
		"deleted_on":    0,
		"created_on":    0,
		"updated_on":    0,
	}).Error
	return err
}

func BatchGetSubmissionPosts(intlOpenid string, chUid string, subMissionIds []string) ([]*model.ChSubmissionPost, error) {
	tx := DB.SelectConnect("db_standalonesite").Table((&model.ChSubmissionPost{}).TableName())
	err := tx.Where("intl_openid = ? AND ch_uid = ? AND submission_id IN (?) AND is_del = 0 AND deleted_on = 0", intlOpenid, chUid, subMissionIds).Find(&[]*model.ChSubmissionPost{}).Error
	return nil, err
}
