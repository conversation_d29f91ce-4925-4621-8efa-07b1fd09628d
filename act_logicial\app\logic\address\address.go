// Package address TODO
package address

import (
	"context"
	"errors"
	"fmt"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpcprotocol/publishing_marketing/logicial_address"
	"git.woa.com/gpts/baselib/crypto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/model/address"
)

// GetAccountAddressInfo 获取地址信息
func GetAccountAddressInfo(ctx context.Context, FsourceId string) (*address.AddressAccountLog, error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	var conf = config.GetConfig()
	var addressAccountLog address.AddressAccountLog
	// AesSecret 秘钥

	tableName, err := model.GetTableNameWithAccount(ctx, &userAccount, addressAccountLog.TableName())
	if err != nil {
		return nil, err
	}
	if errM := DB.DefaultConnect().WithContext(ctx).Table(tableName).Where("uid = ? and Fsource_id = ?", userAccount.Uid,
		FsourceId).
		Where("account_type = ? and is_del = 0", userAccount.AccountType).First(&addressAccountLog).Error; errM != nil {
		if errors.Is(errM, gorm.ErrRecordNotFound) {
			return &address.AddressAccountLog{}, nil
		} else {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", errM.Error())
			return nil, err
		}
	}

	aesSecret := conf.Crypto
	aesUtil := crypto.NewAesCrypto()
	addressAccountLogInfo := &address.AddressAccountLog{}
	if addressAccountLog.Name != "" {
		nameByte, _ := aesUtil.DecryptBase64([]byte(addressAccountLog.Name), []byte(aesSecret))
		addressAccountLogInfo.Name = string(nameByte)
	}
	if addressAccountLog.PhoneNumber != "" {
		phoneByte, _ := aesUtil.DecryptBase64([]byte(addressAccountLog.PhoneNumber), []byte(aesSecret))
		addressAccountLogInfo.PhoneNumber = string(phoneByte)
	}
	if addressAccountLog.Country != "" {
		countryByte, _ := aesUtil.DecryptBase64([]byte(addressAccountLog.Country), []byte(aesSecret))
		addressAccountLogInfo.Country = string(countryByte)
	}
	if addressAccountLog.Address != "" {
		addressByte, _ := aesUtil.DecryptBase64([]byte(addressAccountLog.Address), []byte(aesSecret))
		addressAccountLogInfo.Address = string(addressByte)
	}
	if addressAccountLog.ZipCode != "" {
		zipCodeByte, _ := aesUtil.DecryptBase64([]byte(addressAccountLog.ZipCode), []byte(aesSecret))
		addressAccountLogInfo.ZipCode = string(zipCodeByte)
	}
	if addressAccountLog.Email != "" {
		emailByte, _ := aesUtil.DecryptBase64([]byte(addressAccountLog.Email), []byte(aesSecret))
		addressAccountLogInfo.Email = string(emailByte)
	}

	return addressAccountLogInfo, nil
}

// SaveAccountAddressInfo 保存地址信息
func SaveAccountAddressInfo(ctx context.Context, req *logicial_address.SaveAccountAddressInfoReq) (err error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	aesUtil := crypto.NewAesCrypto()
	var conf = config.GetConfig()
	// AesSecret 秘钥
	aesSecret := conf.Crypto
	var addressAccountLogInfo address.AddressAccountLog
	tableName, err := model.GetTableNameWithAccount(ctx, &userAccount, addressAccountLogInfo.TableName())
	if err != nil {
		return err
	}
	errM := DB.DefaultConnect().WithContext(ctx).Table(tableName).
		Where("uid = ? and Fsource_id = ?", userAccount.Uid, req.FsourceId).
		Where("account_type = ? and is_del = 0", userAccount.AccountType).Limit(1).
		Find(&addressAccountLogInfo).Error
	if errM != nil && !errors.Is(errM, gorm.ErrRecordNotFound) {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", errM.Error())
	}
	// 不可修改
	if req.Unmodifiable && addressAccountLogInfo.UID != "" {
		return errs.NewCustomError(ctx, code.AddressUnmodifiable, "The address information cannot be modified")
	}

	fmt.Println("---------------aesSecret---------------")
	fmt.Printf("%#v\n", aesSecret)
	fmt.Printf("%#v\n", req)
	nameByte, _ := aesUtil.EncryptBase64([]byte(req.Name), []byte(aesSecret))
	phoneByte, _ := aesUtil.EncryptBase64([]byte(req.PhoneNumber), []byte(aesSecret))
	countryByte, _ := aesUtil.EncryptBase64([]byte(req.Country), []byte(aesSecret))
	addressByte, _ := aesUtil.EncryptBase64([]byte(req.Address), []byte(aesSecret))
	zipCodeByte, _ := aesUtil.EncryptBase64([]byte(req.ZipCode), []byte(aesSecret))
	emailByte, _ := aesUtil.EncryptBase64([]byte(req.Email), []byte(aesSecret))

	addressAccountLog := address.AddressAccountLog{
		UID:         userAccount.Uid,
		AccountType: int(userAccount.AccountType),
		Name:        string(nameByte),
		PhoneNumber: string(phoneByte),
		Country:     string(countryByte),
		Address:     string(addressByte),
		Email:       string(emailByte),
		ZipCode:     string(zipCodeByte),
		FsourceID:   string(req.FsourceId),
	}
	fmt.Println("---------------addressAccountLog---------------")
	fmt.Printf("%#v\n", addressAccountLogInfo)
	if addressAccountLogInfo.UID == "" {
		if errM = DB.DefaultConnect().WithContext(ctx).Table(tableName).Create(&addressAccountLog).Error; errM != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr, "db create error, \t [Error]:{%v} ",
				errM.Error())
			return
		}
		return
	} else {
		if errM = DB.DefaultConnect().WithContext(ctx).Table(tableName).Where("uid = ?", userAccount.Uid).
			Where("Fsource_id = ?", req.FsourceId).
			Where("account_type = ?", userAccount.AccountType).Updates(addressAccountLog).Error; errM != nil {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db update error, \t [Error]:{%v} ", errM.Error())
			return
		}
	}
	return
}
