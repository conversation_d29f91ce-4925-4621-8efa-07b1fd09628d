package model

type CreatorHubActivity struct {
	*Model
	TaskID      int32  `gorm:"column:task_id" json:"task_id"`           //活动任务id
	EndTime     int64  `gorm:"column:end_time" json:"end_time"`         //活动结束时间
	StartTime   int64  `gorm:"column:start_time" json:"start_time"`     //活动开始时间
	PublishTime int64  `gorm:"column:publish_time" json:"publish_time"` //发布时间
	ImageURL    string `gorm:"column:image_url" json:"image_url"`       //活动图片地址
	TaskStatus  int32  `gorm:"column:task_status" json:"task_status"`   //任务状态
	TaskType    int32  `gorm:"column:task_type" json:"task_type"`       //任务类型
}

// TableName sets the insert table name for this struct type
func (c *CreatorHubActivity) TableName() string {
	return "p_ch_activity"
}

type CreatorHubActivityLanguage struct {
	*Model
	Name       string `gorm:"column:name" json:"name"`               //活动名称
	ActivityID int64  `gorm:"column:activity_id" json:"activity_id"` //活动id
	Language   string `gorm:"column:language" json:"language"`       //语言

}

// TableName sets the insert table name for this struct type
func (c *CreatorHubActivityLanguage) TableName() string {
	return "p_ch_activity_language"
}

type CreatorHubActivityLanguageList struct {
	CreatorHubActivity
	Languages []*CreatorHubActivityLanguage `json:"languages" gorm:"-"`
}

type CreatorHubActivityWhere struct {
	LtId    int64
	Limit   int
	NowTime int64
	Status  int
	TaskId  int32
	TaskIds []int32
}

type CreatorHubActivityWhereByStatus struct {
	Status  []int
	TaskId  int32
	TaskIds []int32
	Order   map[string]string
	Limit   int
}
