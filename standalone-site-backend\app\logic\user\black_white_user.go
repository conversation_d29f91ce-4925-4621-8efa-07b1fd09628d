package user

import (
	"context"
	"errors"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	redisC "github.com/go-redis/redis/v8"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/formatted"
)

func IsInUserWhiteList(c context.Context, gameId, areaId string, intlOpenID string) (bool, error) {
	// 这个改成redis set
	redisKey := cache.GetUserInWhiteListKey(intlOpenID, gameId, areaId)
	exists, err := redis.GetClient().Exists(context.Background(), redisKey).Result()
	if err != nil {
		if errors.Is(err, redisC.Nil) {
			return false, nil
		}
		return false, errs.NewCustomError(c, code.GetUserWhiteListInfoError, "Failed to obtain user white list information, please check")
	}
	return exists > 0, nil
}

func IsOfficialAuthUser(c context.Context, gameId, areaId string, intlOpenID string) (bool, error) {
	authType := formatted.GetUserAuth(intlOpenID)
	if authType > 0 {
		if authType == constants.USER_AUTH_TYPE_OFFICIAL || authType == constants.USER_AUTH_TYPE_MECHANISM {
			return true, nil
		}
	}
	return false, nil
}

func RefreshUserBlackWhiteListRedisData(ctx context.Context) {
	conditionsT := &dao.AuditPermissionConditions{
		ValidOn: time.Now().Unix(),
		Order:   make([]*dao.OrderConditions, 0),
	}
	conditionsT.Order = append(conditionsT.Order, &dao.OrderConditions{Column: "id", IsDesc: true})
	auditPermissions, err := dao.AuditPermissionList(conditionsT, 0, 1)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("RefreshUserBlackWhiteListRedisData GetAuditPermissions err: %v, condition: %v", err, conditionsT)
	}
	for _, auditItem := range auditPermissions {
		expireTime := int64(auditItem.ValidOn) - time.Now().Unix()
		var redisKey string
		if auditItem.Type == 1 {
			redisKey = cache.GetUserInWhiteListKey(auditItem.IntlOpenid, auditItem.GameId, auditItem.AreaId)
		}
		if auditItem.Type == 2 {
			if auditItem.Status == 1 {
				redisKey = cache.GetUserPostBanKey(auditItem.IntlOpenid, auditItem.GameId, auditItem.AreaId)
			}
			if auditItem.Status == 2 {
				redisKey = cache.GetUserCommentBanKey(auditItem.IntlOpenid, auditItem.GameId, auditItem.AreaId)
			}
			if auditItem.Status == 3 {
				redisKey = cache.GetUserAccounttBanKey(auditItem.IntlOpenid, auditItem.GameId, auditItem.AreaId)
			}
		}
		if redisKey == "" {
			continue
		}
		redis.GetClient().SetNX(ctx, redisKey, 1, time.Duration(expireTime)*time.Second)
	}
}

func SetUserWhiteListCache(c context.Context, intlOpenID string, gameID, areaID string) error {
	redisKey := cache.GetUserInWhiteListKey(intlOpenID, gameID, areaID)
	err := setUserPermAuditCache(c, intlOpenID, 1, 0, redisKey, gameID, areaID)
	return err
}

func setUserPermAuditCache(c context.Context, intlOpenID string, banType, status int64, redisKey, gameID, areaID string) error {
	// 将用户相关白名单、黑名单状态添加到缓存中
	conditionsT := &dao.AuditPermissionConditions{
		Type:       banType,
		Status:     status,
		GameId:     gameID,
		AreaId:     areaID,
		IntlOpenid: intlOpenID,
		ValidOn:    time.Now().Unix(),
		Order:      make([]*dao.OrderConditions, 0),
	}
	conditionsT.Order = append(conditionsT.Order, &dao.OrderConditions{Column: "id", IsDesc: true})
	auditPermissions, err := dao.AuditPermissionList(conditionsT, 0, 1)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("setUserPermAuditCache GetAuditPermissions err: %v, condition: %v", err, conditionsT)
		return errs.NewCustomError(c, code.GetUserPermissionAuditInfoError, "Failed to obtain user black and white audit list, please check")
	}
	if len(auditPermissions) > 0 {
		auditPermission := auditPermissions[0]
		expireTime := int64(auditPermission.ValidOn) - time.Now().Unix()
		redis.GetClient().SetEX(c, redisKey, 1, time.Duration(expireTime)*time.Second)
	}
	return nil
}

func DeleteUserWhiteListCache(c context.Context, intlOpenID string, gameID, areaID string) {
	redisKey := cache.GetUserInWhiteListKey(intlOpenID, gameID, areaID)
	redis.GetClient().Del(c, redisKey)
}
