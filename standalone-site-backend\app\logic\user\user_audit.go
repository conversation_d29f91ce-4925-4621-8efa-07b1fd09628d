package user

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

type ParamsItem struct {
	RangeKey  string      `json:"range_key"`
	Value     interface{} `json:"value"`
	MastQuery bool        `json:"mast_query"`
}

type UserAuditResp struct {
	Items []*model.UserAuditFormatted
	Total int64
}

func GetUserAuditListByDB(queryParam *dao.UserAuditConditions, limit, offset int, isIgnoreDelete bool) (*UserAuditResp, error) {
	if queryParam == nil {
		return nil, nil
	}
	list, err := dao.UserAuditList(queryParam, offset, limit, isIgnoreDelete)

	if err != nil {
		return nil, err
	}
	count, err := dao.UserAuditCount(queryParam, isIgnoreDelete)
	if err != nil {
		return nil, err
	}
	return &UserAuditResp{
		Items: auditFormUserAuditDbData(list),
		Total: count,
	}, nil

}

func auditFormUserAuditDbData(list []*model.UserAudit) []*model.UserAuditFormatted {
	var auditList []*model.UserAuditFormatted
	for _, item := range list {
		auditList = append(auditList, &model.UserAuditFormatted{
			Id:             item.ID,
			TextRiskLevel:  item.TextRiskLevel,
			TextRiskType:   item.TextRiskType,
			PicRiskLevel:   item.PicRiskLevel,
			PicRiskType:    item.PicRiskType,
			AuditStatus:    item.AuditStatus,
			AuditUser:      item.AuditUser,
			Type:           item.Type,
			Context:        item.Context,
			AuditOn:        item.AuditOn,
			AuditIntroduce: item.AuditIntroduce,
			IntlOpenid:     item.IntlOpenid,
			CreatedOn:      item.CreatedOn,
			ModifiedOn:     item.ModifiedOn,
			DeletedOn:      item.DeletedOn,
			IsDel:          int8(item.IsDel),
		})
	}
	return auditList
}

func UserAuditCreate(userAudit *model.UserAudit) error {
	err := dao.UserAuditCreate(userAudit)
	if err != nil {
		return err
	}
	go func(auditData *model.UserAudit) {
		defer recovery.CatchGoroutinePanic(context.Background())
		var intlUserOpenid string
		// 判断intl_openid中是有含有-这个字符串，没有的话直接跳过，防止panic
		if strings.Contains(auditData.IntlOpenid, "-") && len(strings.Split(auditData.IntlOpenid, "-")) == 2 {
			intlUserOpenid = strings.Split(auditData.IntlOpenid, "-")[1]
		}
		// 写入es
		var userAuditFormatted = &model.ESUserAuditInfo{
			Id:             auditData.ID,
			IntlOpenid:     auditData.IntlOpenid,
			IntlUserOpenid: intlUserOpenid,
			TextRiskLevel:  int64(auditData.TextRiskLevel),
			TextRiskType:   int64(auditData.TextRiskType),
			PicRiskLevel:   int64(auditData.PicRiskLevel),
			PicRiskType:    int64(auditData.PicRiskType),
			AuditStatus:    int32(auditData.AuditStatus),
			Context:        auditData.Context,
			Type:           int32(auditData.Type),
			CreatedOn:      int32(auditData.CreatedOn),
			ModifiedOn:     auditData.ModifiedOn,
			IsDel:          0,
			AuditOn:        int32(auditData.AuditOn),
			AuditIntroduce: auditData.AuditIntroduce,
			Language:       auditData.Language,
		}
		isSuccess, err := dao.EsPutDoc(config.GetConfig().ElasticSearchSetting.UserAuditIndex, fmt.Sprintf("%d", auditData.ID), userAuditFormatted)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UserAuditCreate EsPutDoc error: %s", err.Error)
			return
		}
		if !isSuccess {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UserAuditCreate EsPutDoc fail; setData: %+v, userAuditData: %+v", userAuditFormatted, auditData)
			return
		}
	}(userAudit)
	return nil
}

func UserAuditDelete(id int64) error {
	err := dao.UserAuditDelete(id)
	if err != nil {
		return err
	}
	// 更新es中的数据
	go func(id int64) {
		defer recovery.CatchGoroutinePanic(context.Background())
		isSuccess, err := dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.UserAuditIndex, fmt.Sprintf("%d", id), map[string]interface{}{"is_del": 1, "delete_on": time.Now().Unix})
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UserAuditDelete EsUpdateDoc error: %s", err.Error)
			return
		}
		if !isSuccess {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("UserAuditDelete EsPutDoc fail; id: %d", id)
			return
		}
		dao.EsRefreshIndex(config.GetConfig().ElasticSearchSetting.UserAuditIndex)
	}(id)
	return nil
}

func UserAuditListByDb(conditions *dao.UserAuditConditions, offset, limit int, ignoreDelete bool) ([]*model.UserAuditAndUserFormatted, error) {
	var userAudits []*model.UserAuditAndUserFormatted
	userAuditList, err := GetUserAuditListByDB(conditions, offset, limit, ignoreDelete)
	if err != nil {
		return nil, err
	}
	if userAuditList.Total == 0 {
		return userAudits, nil
	}
	var userOpenids = make([]string, 0)
	for _, audit := range userAuditList.Items {
		userOpenids = append(userOpenids, audit.IntlOpenid)
	}
	if len(userOpenids) == 0 {
		return userAudits, err
	}

	userInfos, err := dao.GetUserListByOpenid(userOpenids)
	if err != nil {
		return userAudits, err
	}
	for _, audit := range userAuditList.Items {
		userAudit := &model.UserAuditAndUserFormatted{
			IntlOpenid:     audit.IntlOpenid,
			TextRiskLevel:  audit.TextRiskLevel,
			TextRiskType:   audit.TextRiskType,
			PicRiskLevel:   audit.PicRiskLevel,
			PicRiskType:    audit.PicRiskType,
			AuditStatus:    audit.AuditStatus,
			AuditUser:      audit.AuditUser,
			Context:        audit.Context,
			Type:           audit.Type,
			AuditOn:        audit.CreatedOn,
			AuditIntroduce: audit.AuditIntroduce,
			Id:             audit.Id,
			IsDel:          audit.IsDel,
			DeletedOn:      audit.DeletedOn,
			CreatedOn:      audit.CreatedOn,
			ModifiedOn:     audit.ModifiedOn,
		}
		for _, info := range userInfos {
			if audit.IntlOpenid == info.IntlOpenid {
				userAudit.User = info
				break
			}
		}
		userAudits = append(userAudits, userAudit)
	}
	return userAudits, nil
}
func UserAuditUpdate(userAudit *model.UserAudit) error {
	err := dao.UserAuditUpdate(userAudit)
	if err != nil {
		return err
	}
	return nil
}

func CMSGetUserAuditList() {

}

// 从缓存中获取数据，获取失败时，读db
func GetUserAuthDescByCache(c context.Context, IntlOpenid string, actionValue int32, language string) (string, error) {
	if IntlOpenid == "" || actionValue == 0 || language == "" {
		return "", nil
	}
	// 先从缓存总获取
	userAuthDescLanguage := cache.GetAuthLangugeDescKey(IntlOpenid, actionValue, language)
	res, err := redis.GetClient().Get(context.Background(), userAuthDescLanguage).Result()
	if err != nil || res == "" {
		// 从db中获取
		userPermissionLanguageItem, err := dao.GetUserPermissionLanguages(IntlOpenid, actionValue, language)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserAuthDescByCache UserPermissionList error: %s", err.Error)
			return "", errs.NewSystemError(c, errs.ErrorTypeMysql, code.GetUserTitleError, "get user permission language item error")
		}
		redis.GetClient().Set(context.Background(), userAuthDescLanguage, userPermissionLanguageItem.Desc, 0)
		return userPermissionLanguageItem.Desc, nil
	}
	return res, nil
}

// 用户审核信息定时机审
func UserInfoByMachineAduit(c context.Context) {
	// 分布式锁，只能有一个抢占成功
	redisKey := cache.GetUserInfoMachineReviewCacheNxKey()
	log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Infof("UserInfoByMachineAduit | review user info by machine start, at: %d", time.Now().Unix())
	if ok, _ := redis.GetClient().SetNX(c, redisKey, "1", config.GetConfig().Dynamic.PostMachineReviewTimeoutDuration*time.Second).Result(); ok {
		defer func() {
			recovery.CatchGoroutinePanic(context.Background())
			redis.GetClient().Del(context.Background(), redisKey)
		}()
		var limit = 100
		for {
			// 获取未送审的数据
			list, err := dao.UserAuditList(&dao.UserAuditConditions{
				AuditStatus: 1,
				Order: []*dao.OrderConditions{
					&dao.OrderConditions{
						Column: "id",
						IsDesc: true,
					},
				},
				LtId: 0,
			}, 0, limit, true)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserInfoByMachineAduit | get user info not audit failed, err: %s", err)
				return
			}
			var userOpenids = make([]string, 0, len(list))
			for _, item := range list {
				userOpenids = append(userOpenids, item.IntlOpenid)
			}

			// 获取用户信息
			userInfos, err := dao.GetUserListByOpenidV2(userOpenids, false)
			if err != nil {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserInfoByMachineAduit | get user info failed, user_openids: %v, err: %s", userOpenids, err)
				return
			}
			for _, item := range list {
				var sceneId = 101
				if item.Type == int8(constants.USER_AUDIT_TYPE_NAME) {
					sceneId = 102
				}
				for _, info := range userInfos {
					if item.IntlOpenid == info.IntlOpenid {
						PushUserInfoToSecurityDetection(c, item.Context, int64(sceneId), item.IntlOpenid, info.Username, item.ID)
						break
					}
				}
			}
			if len(list) < limit {
				break
			}

		}
	}
	return
}
