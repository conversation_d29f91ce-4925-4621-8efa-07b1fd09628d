package util

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/base64encode"
	configModel "git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/model"
	"git.code.oa.com/trpcprotocol/publishing_marketing/account"
)

var (
	splitIdCode    = "|||"
	splitShortCode = "-"
	// 大写字母数字集
	alphanumericSet = []rune{
		'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W',
		'X', 'Y', 'Z',
	}
)

// AutoCode 结构
type AutoCode struct {
	Ai string `json:"ai"`
	At int32  `json:"at"`
	Si string `json:"si"`
}

// IdCode id 结构
type IdCode struct {
	Id int64  `json:"id"`
	Si string `json:"si"`
}

// ShortCode open——id 结构
type ShortCode struct {
	Uid string `json:"uid"`
	Si  string `json:"si"`
}

const (
	aesKey string = "7c7488e7f40921e97359788b129ddf02" // 邀请码加密秘钥

	configTable = "invitation_config"
	tablePrefix = "invitation_log"

	shortCodeLen     = 12 // 分享短码长度
	shortTablePrefix = "inviation_account_shortcode"
	shortConfigTable = "invitation_shortcode_config" // 分享短码配置表

	tableNum = 100
)

// GetShareCode 获取分享码
func GetShareCode(uid string, accountType account.AccountType, sourceId string) (string, error) {
	codeStruct := AutoCode{
		Ai: uid,
		At: int32(accountType),
		Si: sourceId,
	}

	codeResult, err := json.Marshal(codeStruct)
	if err != nil {
		return "", err
	}
	shareCode := base64encode.Base64UrlSafeEncode(string(codeResult), aesKey)
	return shareCode, nil
}

// GetShortShareCode 获取分享短码
func GetShortShareCode(uid string, sourceId string) (string, error) {
	codeStruct := ShortCode{
		Uid: uid,
		Si:  sourceId,
	}
	codeResult := fmt.Sprintf("%v%v%v", codeStruct.Uid, splitShortCode, codeStruct.Si)
	sum := md5.Sum([]byte(codeResult))
	var code []rune
	for i := 0; i < shortCodeLen; i++ {
		idx := sum[i] % byte(len(alphanumericSet))
		code = append(code, alphanumericSet[idx])
	}
	return string(code), nil
}

// ParseCode 解析
func ParseCode(shareCode string) (*AutoCode, error) {
	if len(shareCode) == 0 {
		return nil, fmt.Errorf("shareCode length is 0")
	}
	en, err := base64encode.Base64URLDecode(shareCode, aesKey)
	if err != nil {
		return nil, err
	}
	if len(en) <= 0 {
		return nil, fmt.Errorf("decode shareCode length is 0")
	}

	autoCode := &AutoCode{}
	err = json.Unmarshal([]byte(en), &autoCode)
	if err != nil {
		return nil, err
	}
	return autoCode, nil
}

// GetShareCodeById 获取分享码 通过id
func GetShareCodeById(id int64, sourceId string) (string, error) {
	codeStruct := IdCode{
		Id: id,
		Si: sourceId,
	}

	// codeResult, err := json.Marshal(codeStruct)
	// if err != nil {
	// 	return "", err
	// }
	codeResult := fmt.Sprintf("%v%v%v", codeStruct.Id, splitIdCode, codeStruct.Si)
	shareCode := base64encode.Base64UrlSafeEncode(codeResult, aesKey)
	return shareCode, nil
}

// ParseCodeById 解析 通过id
func ParseCodeById(shareCode string) (*IdCode, error) {
	if len(shareCode) == 0 {
		return nil, fmt.Errorf("shareCode length is 0")
	}
	en, err := base64encode.Base64URLDecode(shareCode, aesKey)
	if err != nil {
		return nil, err
	}
	if len(en) <= 0 {
		return nil, fmt.Errorf("decode shareCode length is 0")
	}

	resList := strings.Split(en, splitIdCode)
	if len(resList) != 2 {
		return nil, fmt.Errorf("decode shareCode length is %v", len(resList))
	}
	id, _ := strconv.ParseInt(resList[0], 10, 64)
	if id <= 0 {
		return nil, fmt.Errorf("decode shareCode err")
	}
	idCode := &IdCode{
		Id: id,
		Si: resList[1],
	}

	return idCode, nil
}

// GetShareTableName 获取表名
func GetShareTableName(ctx context.Context, sourceId string) (string, error) {
	tableName, err := configModel.GetTabNameWithSourceId(ctx, configTable, sourceId, tablePrefix, tableNum)
	if err != nil {
		return "", errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return tableName, nil
}

// GetShortShareTableName 获取表名
func GetShortShareTableName(ctx context.Context, sourceId string) (string, error) {
	tableName, err := configModel.GetTabNameWithSourceId(ctx, shortConfigTable, sourceId, shortTablePrefix, tableNum)
	if err != nil {
		return "", errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db error, \t [Error]:{%v} ", err)
	}
	return tableName, nil
}
