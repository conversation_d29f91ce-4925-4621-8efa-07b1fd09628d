package tweet

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	redisClient "github.com/go-redis/redis/v8"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/util"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	hotService "trpc.publishing_application.standalonesite/app/logic/hot"
	plateService "trpc.publishing_application.standalonesite/app/logic/plate"
	"trpc.publishing_application.standalonesite/app/logic/privacy"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetPostCollection(postUUID string, intlOpenid string, needDelRecord bool) (*model.PostCollection, error) {
	return dao.PostCollectionGet(postUUID, intlOpenid, needDelRecord)
}

// GetUserPostCollections 获取用户动态收藏列表
func GetUserPostCollections(c context.Context, req *pb.GetUserPostCollectionListReq, language, gameId, areaId, myOpenid string) (*pb.GetUserPostCollectionListRsp, error) {
	var rsp = &pb.GetUserPostCollectionListRsp{
		List:     make([]*pb.GetPostRsp, 0),
		PageInfo: &pb.PageInfo{},
	}
	// 参数检查
	if req.IntlOpenid == "" {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserPostCollections intlOpenid is empty")
		return rsp, errs.NewCustomError(c, code.InvalidParamsErr, "intlOpenid is empty")
	}
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}
	queryIntlOpenID := req.IntlOpenid
	if queryIntlOpenID != myOpenid {
		privacySwitch, err := privacy.GetUserPrivacySwitch(c, queryIntlOpenID)
		if err != nil {
			return rsp, err
		}
		if privacySwitch.ShowMyCollection == 0 {
			rsp.PageInfo.IsFinish = true
			return rsp, nil
		}
	}

	// 先获取缓存数据
	var collections []*model.PostCollection
	var nextPageCursor, previousPageCursor string
	var pageTotal int //当前页面的数量
	var addLimit = 1  //加量分页
	var limit = addLimit + int(req.Limit)
	// 加入主客态状态查询，避免出现主客态抢占缓存问题
	postBasesRedisKey := cache.GetUserCollectPostListKey(queryIntlOpenID, req.NextPageCursor, req.Limit)
	postBasesCacheInfo, err := redis.GetClient().Get(c, postBasesRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postBasesCacheInfo), &collections)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserPostCollections postBases cache json.Unmarshal error.postBasesRedisKey: %s, err: %v", postBasesRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetUserCollectPostsBaseJsonUnmarshalError, "Failed to obtain post info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserPostCollections postBases redis err: %v", err)
		}

		// 查询类型：下一页数据
		if req.PageType == pb.PageType_NEXTPAGE {
			previousPageCursor = req.NextPageCursor
			var idCursor int64
			// 如果是首页
			if req.NextPageCursor != "" {
				idCursor, err = util.DecryptPageCursorI(req.NextPageCursor)
				if err != nil {
					return nil, errs.NewCustomError(c, code.PagingCursorIsInvalidI, "Paging cursor is invalid")
				}
			}

			conditions := &dao.PostCollectionConditions{
				IntlOpenid: queryIntlOpenID,
				Order: []*dao.OrderConditions{
					&dao.OrderConditions{
						Column: "id",
						IsDesc: true,
					},
				},
			}
			if idCursor > 0 {
				conditions.LtId = idCursor
			}
			//if queryIntlOpenID != myOpenid {
			//	conditions.Language = language
			//}
			collections, err = dao.PostCollectionListNotPost(conditions, limit)
			if err != nil {
				return nil, err
			}
		}
		collectionsByte, err := json.Marshal(collections)
		if err == nil {
			redis.GetClient().SetEX(c, postBasesRedisKey, string(collectionsByte), 2*time.Minute).Result()
			if myOpenid != "" {
				keysKey := cache.GetUserPostCacheKeysKey(myOpenid)
				redis.GetClient().SAdd(c, keysKey, postBasesRedisKey).Result()
			}
		}
	}

	if len(collections) > 0 {
		pageTotal = len(collections)
		if pageTotal == limit {
			// 切割当前收藏数据
			collections = collections[0:(limit - addLimit)]
		}
		nextPageCursor, err = util.EncryptPageCursorI(collections[len(collections)-1].ID)
		if err != nil {
			return nil, errs.NewCustomError(c, code.GetUserPostCollectionsEncryptPageCursorError, "GetUserPostCollections | Failed to create comments nextPageCursor")
		}
	}
	if len(collections) == 0 {
		rsp.PageInfo.IsFinish = true
		return rsp, nil
	}

	// 接下来获取收藏动态列表的分表的详细数据
	collectionPostItems := make([]*pb.GetPostRsp, 0, len(collections))
	var postUUIDsStr string
	for _, collectionItem := range collections {
		postUUIDsStr = fmt.Sprintf("%s_%s", postUUIDsStr, collectionItem)
	}

	hash := sha256.New()
	hash.Write([]byte(postUUIDsStr))
	hashValue := hash.Sum(nil)
	postUUIDsMd5Str := hex.EncodeToString(hashValue)

	// 先获取缓存数据
	postDetailsRedisKey := cache.GetUserCollectPostDetailListKey(postUUIDsMd5Str, language, myOpenid)
	postDetailsCacheInfo, err := redis.GetClient().Get(c, postDetailsRedisKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(postDetailsCacheInfo), &collectionPostItems)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetUserPostCollections postDetails cache json.Unmarshal error.postDetailsRedisKey: %s, err: %v", postDetailsRedisKey, err)
			return nil, errs.NewCustomError(c, code.GetUserCollectPostsDetailJsonUnmarshalError, "Failed to obtain post info, data parsing exception")
		}
	} else {
		if !errors.Is(err, redisClient.Nil) {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetUserPostCollections postDetails redis err: %v", err)
		}
		var wg sync.WaitGroup
		// 获取板块多语言信息
		getAllPlateLangMap, err := plateService.GetAllPlateNameMap(c)
		if err != nil {
			return rsp, err
		}
		var postCollectionMap = make(map[int64]*pb.GetPostRsp)
		var collectionIds []int64
		for _, collection := range collections {
			wg.Add(1)
			go func(collectionItem *model.PostCollection, language string, allPlateLangMap map[int32]map[string]string, index int64, intlOpenid string) {
				defer recovery.CatchGoroutinePanic(context.Background())
				defer wg.Done()

				post, err := GetPostInfo(context.Background(), intlOpenid, collectionItem.PostUUID, language, 0)
				if err != nil {
					log.WithFieldsContext(c, "log type", constants.LogType_Standalonesite).Errorf("GetUserCollections GetPost err, post_uuid:(%s), err=(%v)", collectionItem.PostUUID, err)
					return
				}
				if plateLangMap, ok := allPlateLangMap[post.PlateId]; ok {
					if plateLang, ok2 := plateLangMap[language]; ok2 {
						post.PlateName = plateLang
					} else if plateEnName, ok3 := plateLangMap["en"]; ok3 {
						post.PlateName = plateEnName
					}
				}
				postCollectionMap[index] = post
			}(collection, language, getAllPlateLangMap, collection.ID, myOpenid)
			collectionIds = append(collectionIds, collection.ID)
		}
		wg.Wait()

		for _, id := range collectionIds {
			if value, ok := postCollectionMap[id]; ok {
				collectionPostItems = append(collectionPostItems, value)
			}
		}
		collectionPostItemsByte, err := json.Marshal(collectionPostItems)
		if err == nil {
			redis.GetClient().SetEX(c, postDetailsRedisKey, string(collectionPostItemsByte), 2*time.Minute).Result()
			if myOpenid != "" {
				keysKey := cache.GetUserPostCacheKeysKey(myOpenid)
				redis.GetClient().SAdd(c, keysKey, postDetailsRedisKey).Result()
			}
		}
	}
	rsp.List = collectionPostItems
	if len(rsp.List) == 0 || pageTotal < limit {
		rsp.PageInfo.IsFinish = true
	} else {
		rsp.PageInfo.NextPageCursor = nextPageCursor
	}
	rsp.PageInfo.PreviousPageCursor = previousPageCursor
	return rsp, nil
}

func DeleteUserCollectionCache(c context.Context, queryIntlOpenID, nextPageCursor string, limit int64) {
	defer recovery.CatchGoroutinePanic(c)
	postBasesRedisKey := cache.GetUserCollectPostListKey(queryIntlOpenID, nextPageCursor, limit)
	redis.GetClient().Del(context.Background(), postBasesRedisKey)
}

func CreatePostCollection(postUUID, intlOpenid string) (*model.PostCollection, error) {
	collection := &model.PostCollection{}
	// 加载Post
	post, err := dao.GetPost(postUUID)
	if err != nil {
		return collection, err
	}

	postStats, err := dao.GetPostStatsByPostUuid(postUUID)
	if err != nil {
		return collection, err
	}

	// 私密post不可操作
	if post.Visibility == int32(constants.PostVisitPrivate) {
		return collection, errors.New("no permision")
	}

	// post未审核，不可操作, 但是自己可以收藏自己的
	if post.IsAudit != 1 && post.IntlOpenid != intlOpenid {
		return collection, errors.New("NotReview")
	}
	collection.PostUUID = postUUID
	collection.IntlOpenid = intlOpenid
	collection.GameID = post.GameId
	collection.AreaID = post.AreaId
	collection.Language = post.Language
	err = dao.PostCollectionCreate(collection)
	if err != nil {
		return collection, err
	}

	// 更新Post点赞数
	postStats.CollectionCount++
	dao.UpdatePostStatsCollection(postUUID, postStats.CollectionCount)

	// todo 更新索引
	// PushPostToSearch(post)
	hotNum := hotService.CalculatingPostHotNum(postStats)
	doc := map[string]interface{}{
		"collection_count": postStats.CollectionCount,
		"hot_num":          hotNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)

	collection.Post = post
	go DeleteUserCollectionCache(context.Background(), intlOpenid, "", 10)
	return collection, nil
}

func DeletePostCollection(c context.Context, collection *model.PostCollection) (*model.PostCollection, error) {
	err := dao.PostCollectionDelete(collection.ID)
	if err != nil {
		return collection, err
	}
	// 加载Post
	post, err := dao.GetPost(collection.PostUUID)
	if err != nil {
		return collection, err
	}
	postStats, err := dao.GetPostStatsByPostUuid(collection.PostUUID)
	if err != nil {
		return collection, err
	}
	// 私密post不可操作
	if post.Visibility == int32(constants.PostVisitPrivate) {
		return collection, errs.NewCustomError(c, code.NoPermission, "DeletePostCollection | not permission")
	}

	// 更新Post点赞数
	postStats.CollectionCount--
	if postStats.CollectionCount < 0 {
		postStats.CollectionCount = 0
	}
	dao.UpdatePostStatsCollection(postStats.PostUUID, postStats.CollectionCount)

	// todo 更新索引
	// PushPostToSearch(post)
	hotNum := hotService.CalculatingPostHotNum(postStats)
	doc := map[string]interface{}{
		"collection_count": postStats.CollectionCount,
		"hot_num":          hotNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)
	collection.Post = post
	go DeleteUserCollectionCache(context.Background(), collection.IntlOpenid, "", 10)
	return collection, nil
}

func PostReCollection(c context.Context, collection *model.PostCollection) (*model.PostCollection, error) {
	err := dao.PostReCollection(collection.ID)
	if err != nil {
		return collection, err
	}
	// 加载Post
	post, err := dao.GetPost(collection.PostUUID)
	if err != nil {
		return collection, err
	}
	postStats, err := dao.GetPostStatsByPostUuid(collection.PostUUID)
	if err != nil {
		return collection, err
	}

	// 更新Post点赞数
	postStats.CollectionCount++
	if postStats.CollectionCount < 0 {
		postStats.CollectionCount = 0
	}
	dao.UpdatePostStatsCollection(postStats.PostUUID, postStats.CollectionCount)

	// todo 更新索引
	// PushPostToSearch(post)
	hotNum := hotService.CalculatingPostHotNum(postStats)
	doc := map[string]interface{}{
		"collection_count": postStats.CollectionCount,
		"hot_num":          hotNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)
	collection.Post = post
	return collection, nil
}

func UserPostCollection(c context.Context, postUUID string, intlOpenID string) (bool, *model.PostCollection, error) {
	// TODO 后面可以改成用锁机制
	limitRedisKey := cache.GetUserPostCollectionLimitKey(intlOpenID, postUUID)
	if ok, err := redis.GetClient().SetNX(c, limitRedisKey, 1, 2*time.Minute).Result(); ok {
		defer redis.GetClient().Del(c, limitRedisKey)
		status := false
		// 判断是否存在该用户对该条动态的收藏信息
		collection, err := GetPostCollection(postUUID, intlOpenID, true)
		if err != nil {
			if err.Error() != "record not found" {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserPostCollection | UserPostCollection GetPostCollection err: %v, postID is %d, userID is %s", err, postUUID, intlOpenID)
				return status, nil, errs.NewCustomError(c, code.GetUserPosCollectionFailed, "UserPostCollection | Failed to obtain dynamic collection information. ")
			}
			// 没有收藏记录，则创建收藏
			status = true
			// 创建collection
			collection, err = CreatePostCollection(postUUID, intlOpenID)
			if err != nil {
				if err.Error() == "NotReview" {
					return status, nil, errs.NewCustomError(c, code.PostNotReviewStarFailed, "UserPostCollection | The dynamic content has not been reviewed and the likes failed.")
				}
				return status, nil, errs.NewCustomError(c, code.CreatePostCollectionFailed, "UserPostCollection | Failed to collect the post.")
			}
		} else {
			// 已有记录
			if collection != nil && collection.Model != nil {
				if collection.IsDel == 0 {
					// 从收藏变成不收藏
					collection, err = DeletePostCollection(c, collection)
					if err != nil {
						return status, nil, errs.NewCustomError(c, code.DeletePostCollectionFailed, "UserPostCollection | Failed to cancel user's favorite post operation.")
					}
				} else {
					// 从不收藏变成收藏
					status = true
					collection, err = PostReCollection(c, collection)
					if err != nil {
						return status, nil, errs.NewCustomError(c, code.PostReCollectionFailed, "UserPostCollection | Failed to re collection user's favorite post operation.")
					}
				}
			}
		}
		return status, collection, nil
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserPostCollection redis return error: %v", err)
		return false, nil, errs.NewCustomError(c, code.UserCollectPostFrequentLimit, "UserPostCollection | Restrictions on frequent user operations on post collections.")
	}
}

func GetPostListMyIsCollection(c context.Context, intlOpenid string, postUUIDs []string, posts []*pb.GetPostRsp) error {
	postsCollections, err := dao.PostCollectionListNotPost(&dao.PostCollectionConditions{
		IntlOpenid: intlOpenid,
		PostUuids:  postUUIDs,
	}, 0)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetPostListMyIsCollection err: %v\n", err)
		return errs.NewCustomError(c, code.GetPostListMyIsCollectionFailed, "GetPostListMyIsCollection | Failed to get post list my is collection")
	}
	for _, post := range posts {
		for _, collection := range postsCollections {
			if post.PostUuid == collection.PostUUID {
				post.IsCollection = true
			}
		}
	}
	return nil
}
