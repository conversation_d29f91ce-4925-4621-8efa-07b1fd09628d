package nikke

import "trpc.act.logicial/app/model"

type SendTemp0101Model struct {
	model.AppModel
}

// TableName .
func (SendTemp0101Model) TableName() string {
	return "nikke0101_send_temp"
}

// SendTemp 临时结构
type SendTemp0101 struct {
	SendTemp0101Model
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(32);column:uid;"`
	AccountType int32  `gorm:"type:smallint(6);column:account_type;0"`
	FsourceID   string `gorm:"type:varchar(255);column:Fsource_id;not null"`
	PresentID   string `gorm:"type:varchar(255);column:present_id;not null"`
	LangType    string `gorm:"type:varchar(64);column:lang_type;"`
	Status      int    `gorm:"type:int(1);column:status;not null"`
}

type Nikke0101MessageTempModel struct {
	model.AppModel
}

func (m *Nikke0101MessageTempModel) TableName() string {
	return "nikke0101_message_temp"
}

// nikke0101_message_temp
type Nikke0101MessageTemp struct {
	Nikke0101MessageTempModel
	ID        uint   `gorm:"column:id;primary_key;AUTO_INCREMENT"` // primary key
	Message   string `gorm:"column:message"`                       // 消息
	Level     int    `gorm:"column:level;default:0;NOT NULL"`      // 彩蛋等级
	Sentiment string `gorm:"column:sentiment;NOT NULL"`            // 情感
}
