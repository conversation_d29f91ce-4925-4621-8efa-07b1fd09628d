// Package config 配置
package config

import (
	"log"
	"strings"

	"github.com/fsnotify/fsnotify"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
)

// Conf 配置
var Conf = new(Config)

// Init 初始化
func Init() (*Config, error) {

	// 默认使用trpc_go.yaml 配置文件
	viper.AddConfigPath("./conf/config/")
	viper.SetConfigName("custom")
	viper.SetConfigType("yaml")
	// 读取环境变量的前缀为 ACT_LOGICIAL
	viper.AutomaticEnv()
	viper.SetEnvPrefix("ACT_LOGICIAL")

	// viper.get("default.app.name") 可读取 ACT_LOGICIAL_DEFAULT_APP_NAME
	replacer := strings.NewReplacer(".", "_")
	viper.SetEnvKeyReplacer(replacer)
	// 加载配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	if err := viper.Unmarshal(Conf); err != nil {
		return nil, errors.Wrapf(err, "failed to load config")
	}

	// 开启配置热加载
	viper.WatchConfig()
	viper.OnConfigChange(func(in fsnotify.Event) {
		log.Println("config file changed:", in.Name)
		newConf := new(Config)
		if err := viper.Unmarshal(newConf); err != nil {
			log.Println("viper Unmarshal tmpConf failed, error:", err.Error())
		} else {
			Conf = newConf
			log.Println("viper Unmarshal tmpConf success, newConf:", *newConf)
		}
	})
	return Conf, nil
}

// GetConfig 获取配置
func GetConfig() *Config {
	return Conf
}
