package tweet

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	monitorPb "git.woa.com/trpcprotocol/publishing_application/standalonesite_monitor"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"
	"trpc.publishing_application.standalonesite/app/model"
	"trpc.publishing_application.standalonesite/app/util"
)

// 帖子审核通过的逻辑
func PostReviewPass(c context.Context, post *model.Post, updateUser, auditIntroduce string, isMachineReview bool) error {
	postUpdateData := map[string]interface{}{
		"is_audit":    1,
		"modified_on": time.Now().Unix(),
	}
	if post.IsAudit == 1 {
		// 已审核
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost post is already reviewed ")
		return errs.NewCustomError(c, code.PostAlreadyReviewed, " post already reviewed")
	}

	cErr := dao.UpdatePostInfo(post.PostUUID, postUpdateData)
	if cErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostInfo err: %v", cErr)
		return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post info.")

	}
	// 更新语言分表的数据
	cErr = dao.UpdatePostAllLanguage(post.PostUUID, postUpdateData)
	if cErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostAllLanguage err: %v", cErr)
		return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post info.")
	}

	postAuditUpdateData := map[string]interface{}{
		"audit_user":      updateUser,
		"audit_on":        time.Now().Unix(),
		"status":          constants.PostAuditPush,
		"modified_on":     time.Now().Unix(),
		"audit_introduce": auditIntroduce,
	}
	if !isMachineReview {
		postAuditUpdateData["artificial_status"] = constants.ReviewStatusPass
	}

	cErr = dao.UpdatePostAuditInfo(post.PostUUID, postAuditUpdateData)
	if cErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostAuditInfo err: %v", cErr)

		return errs.NewCustomError(c, code.UpdatePostAuditErr, "Failed to update post audit info.")
	}

	// 更新es
	doc := map[string]interface{}{
		"is_audit":        1,
		"audit_user":      updateUser,
		"audit_on":        time.Now().Unix(),
		"audit_status":    constants.PostAuditPush,
		"modified_on":     time.Now().Unix(),
		"audit_introduce": auditIntroduce,
	}
	if !isMachineReview {
		doc["artificial_status"] = constants.ReviewStatusPass
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)

	// 推送到舆情bot AI分类
	if postContent, err := dao.PostContentGetIgnoreDelete(post.PostUUID); err == nil {
		var checkPostContent string
		if postContent.ContentSummary != "" {
			checkPostContent = util.RemoveSymbol(postContent.ContentSummary)
		} else {
			if postContent.Content != "" {
				// 用于存储从文本字段抽离出来的图片链接、文本的数组、换行次数
				_, contentTexts, _, err := util.TrimHtmlLabel(postContent.Content)
				if err == nil {
					checkPostContent = util.RemoveSymbol(strings.Join(contentTexts, " "))
				} else {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost | trim html label failed, err:%v, content: %s", err, postContent.Content)
					checkPostContent = postContent.Content
				}
			}
		}
		classifyParam := &monitorPb.ContentClassifyReq{
			ContentType: 1,
			PostUuid:    post.PostUUID,
			Language:    postContent.Language,
			Title:       postContent.Title,
			Content:     checkPostContent,
		}
		monitorProxy.ContentClassify(c, classifyParam)
	}
	return nil
}

func PostReviewReject(c context.Context, post *model.Post, userState *model.UserState, updateUser, auditIntroduce string, delReason int32) error {
	gameId := "30054"
	areaId := "global"
	senderUserID, err := strconv.ParseInt(gameId, 10, 64)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost get senderUserID err: %v", err)
		return errs.NewCustomError(c, code.GetOfficialGameUserError, "CMSReviewPost | Failed to obtain official user account.")
	}
	// 审核不通过的需要删除数据
	postUpdateData := map[string]interface{}{
		"is_del":     1,
		"deleted_on": time.Now().Unix(),
		"del_reason": delReason,
		"del_type":   constants.BReviewDeleted,
	}
	uErr := dao.UpdatePostInfo(post.PostUUID, postUpdateData)
	if uErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostInfo err: %v", uErr)
		return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post info.")
	}
	// 删除语言数据
	uErr = dao.UpdatePostAllLanguage(post.PostUUID, postUpdateData)
	if uErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostAllLanguage err: %v", uErr)
		return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post language.")
	}
	// 删除动态内容
	uErr = dao.UpdatePostContentInfo(post.PostUUID, map[string]interface{}{
		"is_del":     1,
		"deleted_on": time.Now().Unix(),
	})
	if uErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostCommentInfo err: %v", uErr)
		return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post content info.")
	}
	postAuditUpdateData := map[string]interface{}{
		"audit_user":        updateUser,
		"audit_on":          time.Now().Unix(),
		"status":            constants.PostAuditIgnore,
		"modified_on":       time.Now().Unix(),
		"audit_introduce":   auditIntroduce,
		"artificial_status": constants.ReviewStatusReject,
		"is_del":            1, //同步删除审核数据
	}

	cErr := dao.UpdatePostAuditInfo(post.PostUUID, postAuditUpdateData)
	if cErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostAuditInfo err: %v", cErr)
		return errs.NewCustomError(c, code.UpdatePostAuditErr, "Failed to update post audit info.")
	}
	// 帖子话题数引用数量减1
	UpdateTagReferencesToPost(c, post.PostUUID)
	// 删除redis的待审核数据
	if post.IsAudit == 2 {
		redis.GetClient().ZRem(context.Background(), cache.GetNeedAuditPostUUIDKey(), post.PostUUID)
	}

	// 更新es
	doc := map[string]interface{}{
		"audit_user":        updateUser,
		"audit_on":          time.Now().Unix(),
		"audit_status":      constants.PostAuditIgnore,
		"is_del":            1,
		"deleted_on":        time.Now().Unix(),
		"audit_introduce":   auditIntroduce,
		"del_reason":        delReason,
		"artificial_status": constants.ReviewStatusReject,
		"del_type":          constants.BReviewDeleted,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)

	go writemessage.SetUserMessage(&model.Message{
		Type:                   constants.MsgTypeOfficialIgnorePost,
		PostUUID:               post.PostUUID,
		GameID:                 gameId,
		AreaID:                 areaId,
		SenderUserIntlOpenid:   fmt.Sprintf("%d", senderUserID),
		ReceiverUserIntlOpenid: post.IntlOpenid,
		ExtInfo:                fmt.Sprintf("{\"del_type\": %d, \"del_reason\": %d}", constants.BReviewDeleted, delReason),
	}, post.IntlOpenid, constants.SiteMessageCount)
	return nil
}

// 帖子审核通过的逻辑
func PostReviewPassV2(c context.Context, post *model.Post, postAuditInfo *model.PostAudit, updateUser, auditIntroduce string, isMachineReview bool) error {
	if postAuditInfo.PostActionType == constants.PostAuditActionAdd {
		postUpdateData := map[string]interface{}{
			"is_audit":    1,
			"modified_on": time.Now().Unix(),
		}
		if post.IsAudit == 1 {
			// 已审核
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost post is already reviewed ")
			return errs.NewCustomError(c, code.PostAlreadyReviewed, " post already reviewed")
		}

		cErr := dao.UpdatePostInfo(post.PostUUID, postUpdateData)
		if cErr != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostInfo err: %v", cErr)
			return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post info.")

		}
		// 更新语言分表的数据
		cErr = dao.UpdatePostAllLanguage(post.PostUUID, postUpdateData)
		if cErr != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostAllLanguage err: %v", cErr)
			return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post info.")
		}
	}

	postAuditUpdateData := map[string]interface{}{
		"audit_user":      updateUser,
		"audit_on":        time.Now().Unix(),
		"status":          constants.PostAuditPush,
		"modified_on":     time.Now().Unix(),
		"audit_introduce": auditIntroduce,
	}
	if !isMachineReview {
		postAuditUpdateData["artificial_status"] = constants.ReviewStatusPass
	}

	cErr := dao.UpdatePostAuditInfoByID(postAuditInfo.ID, postAuditUpdateData)
	if cErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostReviewPassV2 UpdatePostAuditInfoByID err: %v", cErr)
		return errs.NewCustomError(c, code.UpdatePostAuditErr, "Failed to update post audit info.")
	}

	postAuditDoc := map[string]interface{}{
		"audit_user":      updateUser,
		"audit_on":        time.Now().Unix(),
		"status":          constants.PostAuditPush,
		"modified_on":     time.Now().Unix(),
		"audit_introduce": auditIntroduce,
	}
	postDoc := map[string]interface{}{
		"is_audit":        1,
		"audit_user":      updateUser,
		"audit_on":        time.Now().Unix(),
		"audit_status":    constants.PostAuditPush,
		"modified_on":     time.Now().Unix(),
		"audit_introduce": auditIntroduce,
	}
	if !isMachineReview {
		postAuditDoc["artificial_status"] = constants.ReviewStatusPass
		postDoc["artificial_status"] = constants.ReviewStatusPass
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetAuditIndex, fmt.Sprintf("%d", postAuditInfo.ID), postAuditDoc)

	if postAuditInfo.PostActionType == constants.PostAuditActionEdit {
		postUpdateData := map[string]interface{}{
			"is_audit":    1,
			"type":        postAuditInfo.Type,
			"modified_on": time.Now().Unix(),
		}

		cErr := dao.UpdatePostInfo(post.PostUUID, postUpdateData)
		if cErr != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostInfo err: %v", cErr)
			return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post info.")

		}
		// 更新语言分表的数据
		cErr = dao.UpdatePostAllLanguage(post.PostUUID, postUpdateData)
		if cErr != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostAllLanguage err: %v", cErr)
			return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post info.")
		}

		// 如果是更新，则需要将草稿数据更新到对应的post_content表里去，因为普通用户的只有一种语言
		updateContentParam := map[string]interface{}{
			"platform":        postAuditInfo.Platform,
			"title":           postAuditInfo.Title,
			"content":         postAuditInfo.Content,
			"content_summary": postAuditInfo.ContentSummary,
			"pic_urls":        postAuditInfo.PicUrls,
			"ext_info":        postAuditInfo.ExtInfo,
		}
		contentErr := dao.UpdatePostContentInfoByLanguage(postAuditInfo.PostUUID, post.Language, updateContentParam)
		if contentErr != nil {
			return contentErr
		}

		postDoc["type"] = postAuditInfo.Type
		postDoc["platform"] = postAuditInfo.Platform
		postDoc["title"] = postAuditInfo.Title
		postDoc["content"] = postAuditInfo.Content
		postDoc["content_summary"] = postAuditInfo.ContentSummary
		postDoc["pic_urls"] = postAuditInfo.PicUrls
		postDoc["ext_info"] = postAuditInfo.ExtInfo
		var postContentStr string
		if postAuditInfo.ContentSummary != "" {
			postContentStr = postAuditInfo.ContentSummary
		} else {
			postContentStr = postAuditInfo.Content
		}
		if post.Language == "en" || post.Language == "ja" || post.Language == "ko" || post.Language == "zh" || post.Language == "zh-TW" {
			// 目前只配置了这几种语言的字段，后续要新增再继续追加
			allContentLangKey := fmt.Sprintf("all_content_lang_%s", post.Language)
			allContentLangVal := fmt.Sprintf("%s|||%s", postAuditInfo.Title, postContentStr)
			postDoc[allContentLangKey] = allContentLangVal
		}
	}

	// 更新es
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, postDoc)

	// 推送到舆情bot AI分类
	if postContent, err := dao.PostContentGetIgnoreDelete(post.PostUUID); err == nil {
		var checkPostContent string
		if postContent.ContentSummary != "" {
			checkPostContent = util.RemoveSymbol(postContent.ContentSummary)
		} else {
			if postContent.Content != "" {
				// 用于存储从文本字段抽离出来的图片链接、文本的数组、换行次数
				_, contentTexts, _, err := util.TrimHtmlLabel(postContent.Content)
				if err == nil {
					checkPostContent = util.RemoveSymbol(strings.Join(contentTexts, " "))
				} else {
					log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost | trim html label failed, err:%v, content: %s", err, postContent.Content)
					checkPostContent = postContent.Content
				}
			}
		}
		classifyParam := &monitorPb.ContentClassifyReq{
			ContentType: 1,
			PostUuid:    post.PostUUID,
			Language:    postContent.Language,
			Title:       postContent.Title,
			Content:     checkPostContent,
		}
		monitorProxy.ContentClassify(c, classifyParam)
	}
	return nil
}

func PostReviewRejectV2(c context.Context, post *model.Post, postAuditInfo *model.PostAudit, userState *model.UserState, updateUser, auditIntroduce string, delReason int32) error {
	gameId := "30054"
	areaId := "global"
	senderUserID, err := strconv.ParseInt(gameId, 10, 64)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost get senderUserID err: %v", err)
		return errs.NewCustomError(c, code.GetOfficialGameUserError, "CMSReviewPost | Failed to obtain official user account.")
	}
	// 审核不通过的需要删除数据
	postUpdateData := map[string]interface{}{
		"is_del":     1,
		"deleted_on": time.Now().Unix(),
		"del_reason": delReason,
		"del_type":   constants.BReviewDeleted,
	}
	uErr := dao.UpdatePostInfo(post.PostUUID, postUpdateData)
	if uErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostInfo err: %v", uErr)
		return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post info.")
	}
	// 删除语言数据
	uErr = dao.UpdatePostAllLanguage(post.PostUUID, postUpdateData)
	if uErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostAllLanguage err: %v", uErr)
		return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post language.")
	}
	// 删除动态内容
	uErr = dao.UpdatePostContentInfo(post.PostUUID, map[string]interface{}{
		"is_del":     1,
		"deleted_on": time.Now().Unix(),
	})
	if uErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostCommentInfo err: %v", uErr)
		return errs.NewCustomError(c, code.CMSUpdatePostError, "Failed to update post content info.")
	}
	postAuditUpdateData := map[string]interface{}{
		"audit_user":        updateUser,
		"audit_on":          time.Now().Unix(),
		"status":            constants.PostAuditIgnore,
		"modified_on":       time.Now().Unix(),
		"audit_introduce":   auditIntroduce,
		"artificial_status": constants.ReviewStatusReject,
		"is_del":            1, //同步删除审核数据
	}

	cErr := dao.UpdatePostAuditInfo(post.PostUUID, postAuditUpdateData)
	if cErr != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CMSReviewPost UpdatePostAuditInfo err: %v", cErr)
		return errs.NewCustomError(c, code.UpdatePostAuditErr, "Failed to update post audit info.")
	}
	postAuditDoc := map[string]interface{}{
		"audit_user":        updateUser,
		"audit_on":          time.Now().Unix(),
		"status":            constants.PostAuditIgnore,
		"is_del":            1,
		"deleted_on":        time.Now().Unix(),
		"audit_introduce":   auditIntroduce,
		"del_reason":        delReason,
		"artificial_status": constants.ReviewStatusReject,
		"del_type":          constants.BReviewDeleted,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetAuditIndex, fmt.Sprintf("%d", postAuditInfo.ID), postAuditDoc)

	// 帖子话题数引用数量减1
	UpdateTagReferencesToPost(c, post.PostUUID)
	// 删除redis的待审核数据
	if post.IsAudit == 2 {
		redis.GetClient().ZRem(context.Background(), cache.GetNeedAuditPostUUIDKey(), post.PostUUID)
	}

	// 更新es
	doc := map[string]interface{}{
		"audit_user":        updateUser,
		"audit_on":          time.Now().Unix(),
		"audit_status":      constants.PostAuditIgnore,
		"is_del":            1,
		"deleted_on":        time.Now().Unix(),
		"audit_introduce":   auditIntroduce,
		"del_reason":        delReason,
		"artificial_status": constants.ReviewStatusReject,
		"del_type":          constants.BReviewDeleted,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)

	go writemessage.SetUserMessage(&model.Message{
		Type:                   constants.MsgTypeOfficialIgnorePost,
		PostUUID:               post.PostUUID,
		GameID:                 gameId,
		AreaID:                 areaId,
		SenderUserIntlOpenid:   fmt.Sprintf("%d", senderUserID),
		ReceiverUserIntlOpenid: post.IntlOpenid,
		ExtInfo:                fmt.Sprintf("{\"del_type\": %d, \"del_reason\": %d}", constants.BReviewDeleted, delReason),
	}, post.IntlOpenid, constants.SiteMessageCount)
	return nil
}
