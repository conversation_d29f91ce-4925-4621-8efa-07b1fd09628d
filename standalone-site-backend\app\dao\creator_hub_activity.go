package dao

import (
	"errors"
	"fmt"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm"
	"time"
	"trpc.publishing_application.standalonesite/app/model"
)

func GetCreatorHubActivityListByTaskIds(taskIds []int64) ([]*model.CreatorHubActivityLanguageList, error) {
	var list []*model.CreatorHubActivityLanguageList

	err := DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubActivity{}).TableName()).Where("task_id in ?", taskIds).Find(&list).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return make([]*model.CreatorHubActivityLanguageList, 0), nil
		}
		return nil, err
	}
	var ids []int64
	for _, item := range list {
		ids = append(ids, item.ID)
	}
	if len(ids) == 0 {
		return list, nil
	}
	var activityLanguages []*model.CreatorHubActivityLanguage
	err = DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubActivityLanguage{}).TableName()).Where("activity_id in ?", ids).Find(&activityLanguages).Error
	if err != nil {
		return list, nil
	}
	for i, item := range list {
		for _, language := range activityLanguages {
			if language.ActivityID == item.ID {
				list[i].Languages = append(list[i].Languages, language)
				continue
			}
		}
	}
	return list, nil
}

func BatchUpdateCreatorHubActivity(rows []*model.CreatorHubActivity) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		for _, row := range rows {
			err := tx.Where("id = ?", row.ID).Updates(row).Error
			if err != nil {
				return err
			}
		}
		return nil
	})

}

func BatchUpdateCreatorHubActivityLanguage(rows []*model.CreatorHubActivityLanguage) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		for _, row := range rows {
			err := tx.Save(row).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func BatchInsertCreatorHubActivity(rows []*model.CreatorHubActivityLanguageList) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		for _, row := range rows {
			err := tx.Table((&model.CreatorHubActivity{}).TableName()).Omit("languages").Create(row).Error
			if err != nil {
				return err
			}
			for i, _ := range row.Languages {
				row.Languages[i].ActivityID = row.ID
			}
			err = tx.Table((&model.CreatorHubActivityLanguage{}).TableName()).CreateInBatches(row.Languages, 100).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	//return DB.SelectConnect("db_standalonesite").Create(rows).Error
}

func GetRankListById(ids []int64) ([]*model.CreatorHubRankLanguageList, error) {
	var rankRows []*model.CreatorHubRank
	var rankLanguages []*model.CreatorHubRankLanguage
	var result []*model.CreatorHubRankLanguageList
	err := DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubRank{}).TableName()).Where("id in ?", ids).Find(&rankRows).Error
	if err != nil {
		return nil, err
	}
	var rankIds = make([]int64, 0, len(rankRows))
	for _, item := range rankRows {
		rankIds = append(rankIds, item.ID)
	}
	err = DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubRankLanguage{}).TableName()).Where("rank_id in ? ", rankIds).Find(&rankLanguages).Error
	if err != nil {
		return nil, err
	}
	for _, row := range rankRows {
		var resItem = &model.CreatorHubRankLanguageList{
			Model:     row.Model,
			RankKey:   row.RankKey,
			Languages: make([]*model.CreatorHubRankLanguage, 0),
		}
		for _, lang := range rankLanguages {
			if row.ID == lang.RankID {
				resItem.Languages = append(resItem.Languages, lang)
			}
		}
		result = append(result, resItem)
	}
	return result, nil
}

// 赛道更新
func BatchUpdateRank(ranks []*model.CreatorHubRankLanguage) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		for _, rank := range ranks {
			err := tx.Table((&model.CreatorHubRankLanguage{}).TableName()).Where("id = ?", rank.ID).Update("name", rank.Name).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// 赛道更新
func BatchSaveRankLanguage(ranks []*model.CreatorHubRankLanguage) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubRankLanguage{}).TableName()).Create(ranks).Error
}

// 赛道新增
func BatchCreateRank(ranks []*model.CreatorHubRankLanguageList) error {
	return DB.SelectConnect("db_standalonesite").Transaction(func(tx *gorm.DB) error {
		err := tx.Table((&model.CreatorHubRank{}).TableName()).Omit("Languages").CreateInBatches(ranks, 100).Error
		if err != nil {
			return err
		}
		var languages []*model.CreatorHubRankLanguage
		for _, rank := range ranks {
			for _, language := range rank.Languages {
				language.RankID = rank.ID
				languages = append(languages, language)
			}
		}
		err = tx.Table((&model.CreatorHubRankLanguage{}).TableName()).CreateInBatches(languages, 100).Error
		if err != nil {
			return err
		}
		return nil
	})

}

// 创建赛道和活动的关系
func SaveActivityWithRank(list []*model.CreatorHubActivityRank) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubActivityRank{}).TableName()).CreateInBatches(list, 100).Error
}

// 获取活动和赛道的关联关系
func GetActivityWithRankList(activityId []int64) (map[int64][]*model.CreatorHubActivityRank, error) {
	var data []*model.CreatorHubActivityRank
	err := DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubActivityRank{}).TableName()).Where("task_id in ?", activityId).Find(&data).Error
	if err != nil {
		return nil, err
	}
	var res = make(map[int64][]*model.CreatorHubActivityRank)
	for _, datum := range data {
		res[datum.TaskID] = append(res[datum.TaskID], datum)
	}
	return res, nil
}

// 删除活动和赛道的关联关系
func DeleteActivityWithRank(ids []int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubActivityRank{}).TableName()).Where("id in ?", ids).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
}

// 获取活动
func GetActivityListByGtId(condition *model.CreatorHubActivityWhere) ([]*model.CreatorHubActivity, int64, error) {
	var activities []*model.CreatorHubActivity
	var count int64
	db := DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubActivity{}).TableName())

	if condition.NowTime > 0 {
		db.Where("end_time > ? AND start_time < ?", condition.NowTime, condition.NowTime)
	}
	if condition.Status >= 0 {
		db.Where("task_status = ?", condition.Status)
	}
	err := db.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}
	if condition.Limit > 0 {
		db.Limit(condition.Limit)
	}
	if condition.LtId > 0 {
		db.Where("id < ?", condition.LtId)
	}
	if condition.TaskId > 0 {
		db.Where("task_id = ?", condition.TaskId)
	}
	if len(condition.TaskIds) > 0 && condition.TaskId == 0 {
		db.Where("task_id in ?", condition.TaskId)
	} else if len(condition.TaskIds) > 0 && condition.TaskId > 0 {
		db.Or("task_id in ?", condition.TaskId)
	}
	err = db.Order("publish_time desc, id desc").Find(&activities).Error
	if err != nil {
		return nil, count, err
	}
	return activities, count, nil
}

// 根据活动id获取多语言
func GetActivityLanguageListByActivityIds(activityIds []int64) ([]*model.CreatorHubActivityLanguage, error) {
	var activityLanguage []*model.CreatorHubActivityLanguage

	err := DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubActivityLanguage{}).TableName()).Where("activity_id in ?", activityIds).Find(&activityLanguage).Error
	if err != nil {
		return nil, err
	}
	return activityLanguage, nil
}

// 获取活动
func GetAllActivityListByStatus(condition *model.CreatorHubActivityWhereByStatus) ([]*model.CreatorHubActivity, error) {
	var activities []*model.CreatorHubActivity
	db := DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubActivity{}).TableName())

	if condition.Limit > 0 {
		db.Limit(condition.Limit)
	}

	if len(condition.Status) > 0 {
		db.Where("task_status in ?", condition.Status)
	}
	if condition.TaskId > 0 {
		db.Where("task_id = ?", condition.TaskId)
	}
	if len(condition.TaskIds) > 0 && condition.TaskId == 0 {
		db.Where("task_id in ?", condition.TaskId)
	} else if len(condition.TaskIds) > 0 && condition.TaskId > 0 {
		db.Or("task_id in ?", condition.TaskId)
	}
	if len(condition.Order) > 0 {
		for key, value := range condition.Order {
			db.Order(fmt.Sprintf("%s %s", key, value))
		}
	}

	err := db.Find(&activities).Error
	if err != nil {
		return nil, err
	}
	return activities, nil
}

// 获取所有的活动Id
func GetAllActivityID() ([]int64, error) {
	var activityIds []int64
	err := DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubActivity{}).TableName()).Pluck("task_id", &activityIds).Error
	if err != nil {
		return nil, err
	}
	return activityIds, nil
}

// 根据task_id删除活动
func DeleteActivityByTaskId(taskIds []int64) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CreatorHubActivity{}).TableName()).Where("task_id in ?", taskIds).Updates(map[string]interface{}{
		"is_del":     1,
		"deleted_on": time.Now().Unix(),
	}).Error
}
