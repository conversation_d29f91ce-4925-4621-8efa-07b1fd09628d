package hoktmp

import (
	"context"
	"fmt"
	"strconv"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_hok_tmp"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/logic/hoktmp"
)

// HokTmpImpl hok 临时玩法
type HokTmpImpl struct {
	pb.UnimplementedHokTmp
}

// GetBindWhatsAppState HokTmpWhatsApp 获取绑定whatsapp信息
func (s *HokTmpImpl) GetBindWhatsAppState(
	ctx context.Context,
	req *pb.GetBindWhatsAppStateReq,
) (rsp *pb.GetBindWhatsAppStateRsp, err error) {
	rsp = &pb.GetBindWhatsAppStateRsp{}

	fmt.Println("-----------GetBindWhatsAppState----------")

	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("GetBindWhatsAppState"))
	bindInfo, _ := hoktmp.GetBindWhatsAppState(ctx, req.ChannelId)

	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("bindInfo: %v", bindInfo.IsBind))

	rsp.IsBind = bindInfo.IsBind
	if rsp.IsBind {
		rsp.UserId = bindInfo.CommunityUserInfo.UserId
		rsp.Username = bindInfo.CommunityUserInfo.Username
	} else {
		err = errs.NewSystemError(ctx, errs.DefaultSystemMsg, code.HOKNoBindWhatsApp,
			"system error no bind whatsApp")
	}

	return
}

// SendPresentTeamUp 添加发奖
func (s *HokTmpImpl) SendPresentTeamUp(
	ctx context.Context,
	req *pb.SendPresentTeamUpReq,
) (rsp *pb.SendPresentTeamUpRsp, err error) {
	rsp = &pb.SendPresentTeamUpRsp{}

	rsp.IsSend = false
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("SendPresentTeamUp req: %v", req))
	if req.BindInfo.IsBind {
		// 绑定的需要出发
		// UserIDToPhone := map[string]string{
		// 	"8618190485985": "8618758325661", // 5
		// 	"8619180957165": "8618758325661", // 5
		// 	"8613488943684": "6591061008",    // 4
		// 	"8617612942362": "8618758325661", // 1
		// 	"8619142440558": "8618758325661", // 1
		// }

		// Phone, ok := UserIDToPhone[req.BindInfo.UserId]
		// if !ok {
		// 	Phone = req.BindInfo.UserId
		// }
		// rank, errRank := hoktmp.GetRank(ctx, Phone)
		rank, errRank := hoktmp.GetRank(ctx, req.BindInfo.UserId)
		if errRank != nil {
			err = errRank
			return
		}
		if rank > 0 {
			if rank > 5 {
				rank = 5
			}
			log.WithFieldsContext(ctx, "log_type", "HOKRank", "str_field_1", strconv.Itoa(int(rank))).Infof("HOKRankLog")
			// 发奖
			rankList := []int64{int64(rank)}
			metadata.SetPresentLotteryLevelList(ctx, req.PresentId, rankList)
		} else {
			err = errs.NewSystemError(ctx, errs.DefaultSystemMsg, code.HOKNoSendGift,
				"system error no rank")
		}
		fmt.Printf("bindInfo: %v", rank)
	}

	return
}

// GetHokTaskStatus TODO
func (s *HokTmpImpl) GetHokTaskStatus(ctx context.Context, req *pb.GetHokTaskStatusReq) (*pb.GetHokTaskStatusRsp,
	error) {

	rsp := &pb.GetHokTaskStatusRsp{}
	err := hoktmp.GetHokTaskStatus(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// HokClubRecordUserActivity HOK记录参加活动的用户
func (s *HokTmpImpl) HokClubRecordUserActivity(ctx context.Context, req *pb.HokClubRecordUserActivityReq) (
	rsp *pb.HokClubRecordUserActivityRsp, err error) {
	if err = hoktmp.HOKRecordUserActivity(ctx); err != nil {
		return nil, err
	}
	return &pb.HokClubRecordUserActivityRsp{}, nil
}

// HOKClubAutoDistributeGifts 自动发放未领取礼包
func (s *HokTmpImpl) HOKClubAutoDistributeGifts(ctx context.Context, req *pb.HOKClubAutoDistributeGiftsReq) (
	*pb.HOKClubAutoDistributeGiftsRsp, error) {
	if err := hoktmp.HOKClubAutoDistributeGifts(ctx, req.FsourceId); err != nil {
		return nil, err
	}
	return &pb.HOKClubAutoDistributeGiftsRsp{}, nil
}

// RecordCycleTaskRewardClaimed 记录周期任务已领取礼包
func (s *HokTmpImpl) RecordCycleTaskRewardClaimed(ctx context.Context, req *pb.RecordCycleTaskRewardClaimedReq) (
	*pb.RecordCycleTaskRewardClaimedRsp, error) {
	if err := hoktmp.RecordCycleTaskRewardClaimed(ctx, req.PresentId); err != nil {
		return nil, err
	}
	return &pb.RecordCycleTaskRewardClaimedRsp{}, nil
}

// GetClaimedCycleTaskRewards 查询周期任务已领取礼包列表
func (s *HokTmpImpl) GetClaimedCycleTaskRewards(ctx context.Context, req *pb.GetClaimedCycleTaskRewardsReq) (
	*pb.GetClaimedCycleTaskRewardsRsp, error) {
	presentIds, err := hoktmp.GetClaimedCycleTaskRewards(ctx, req.TimeList)
	if err != nil {
		return nil, err
	}
	return &pb.GetClaimedCycleTaskRewardsRsp{
		UserHasPresentList: presentIds,
	}, nil
}

// IsCurrentCycleRewardClaimed 判断当前周期礼包是否已领取
func (s *HokTmpImpl) IsCurrentCycleRewardClaimed(ctx context.Context, req *pb.IsCurrentCycleRewardClaimedReq) (
	*pb.IsCurrentCycleRewardClaimedRsp, error) {
	if err := hoktmp.IsCurrentCycleRewardClaimed(ctx, req.TimeList, req.PresentId); err != nil {
		return nil, err
	}
	return &pb.IsCurrentCycleRewardClaimedRsp{}, nil
}

// HokClubRecordUserActivityV2 HOK二期记录参加活动的用户
func (s *HokTmpImpl) HokClubRecordUserActivityV2(ctx context.Context, req *pb.HokClubRecordUserActivityV2Req) (
	rsp *pb.HokClubRecordUserActivityV2Rsp, err error) {
	if err = hoktmp.HOKRecordUserActivityV2(ctx); err != nil {
		return nil, err
	}
	return &pb.HokClubRecordUserActivityV2Rsp{}, nil
}

// SyncHOKHotRankV2 HOK二期排行榜数据定时同步
func (s *HokTmpImpl) SyncHOKHotRankV2(ctx context.Context, req *pb.SyncHOKHotRankV2Req) (*pb.SyncHOKHotRankV2Rsp,
	error) {

	ctx2 := context.Background()
	if err := hoktmp.SyncHOKHotRankV2(ctx2); err != nil {
		return nil, err
	}
	return &pb.SyncHOKHotRankV2Rsp{}, nil
}

// GetUserHokHotRankList 获取用户排行榜数据
func (s *HokTmpImpl) GetUserHokHotRankList(ctx context.Context, req *pb.GetUserHokHotRankListReq) (
	*pb.GetUserHokHotRankListRsp, error) {

	// ctx2 := context.Background()
	rsp, err := hoktmp.GetUserHokHotRankList(ctx)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// HOKClubPeriodicMissionRewardReissue 周期任务奖励补发
func (s *HokTmpImpl) HOKClubPeriodicMissionRewardReissue(ctx context.Context,
	req *pb.HOKClubPeriodicMissionRewardReissueReq) (
	*pb.HOKClubPeriodicMissionRewardReissueRsp, error) {

	if err := hoktmp.HOKClubPeriodicMissionRewardReissue(ctx, req.StarTime, req.EndTime, req.TimeZone,
		req.FsourceId); err != nil {
		return nil, err
	}
	return &pb.HOKClubPeriodicMissionRewardReissueRsp{}, nil
}

// HOKClubAchievementQuestRewardReissue 成就任务奖励补发
func (s *HokTmpImpl) HOKClubAchievementQuestRewardReissue(ctx context.Context,
	req *pb.HOKClubAchievementQuestRewardReissueReq) (
	*pb.HOKClubAchievementQuestRewardReissueRsp, error) {
	if err := hoktmp.HOKClubAchievementQuestRewardReissue(ctx, req.FsourceId); err != nil {
		return nil, err
	}
	return &pb.HOKClubAchievementQuestRewardReissueRsp{}, nil
}
