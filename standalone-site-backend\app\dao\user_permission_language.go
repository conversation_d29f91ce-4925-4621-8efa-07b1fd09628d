package dao

import (
	"fmt"
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"gorm.io/gorm/clause"
	"trpc.publishing_application.standalonesite/app/model"
)

type UserPermissionLanguages struct {
	GameId           string   `json:"game_id"`
	AreaId           string   `json:"area_id"`
	UserPermissionId int64    `json:"user_permission_id"`
	Languages        []string `json:"languages"`
}

// 删除game_id area_id user_permission_id 下不存在的language
func DeleteNotInLanguages(languages []*model.UserPermissionLanguage) error {
	gameAreaUserPermissionMap := make(map[string]*UserPermissionLanguages, 0)

	// db.Begin()
	// defer func() {
	// 	if r := recover(); r != nil {
	// 		db.Rollback()
	// 	} else {
	// 		// 正常流程不用处理
	// 	}
	// }()
	for _, languageItem := range languages {
		key := fmt.Sprintf("%s-%s-%s", languageItem.GameId, languageItem.AreaId, languageItem.UserPermissionId)
		if _, ok := gameAreaUserPermissionMap[key]; !ok {
			gameAreaUserPermissionMap[key] = &UserPermissionLanguages{
				GameId:           languageItem.GameId,
				AreaId:           languageItem.AreaId,
				UserPermissionId: languageItem.UserPermissionId,
				Languages:        []string{languageItem.Language},
			}
		} else {
			items := gameAreaUserPermissionMap[key]
			items.Languages = append(items.Languages, languageItem.Language)
		}
	}
	for _, languageItem := range gameAreaUserPermissionMap {
		db := DB.SelectConnect("db_standalonesite").Table((&model.UserPermissionLanguage{}).TableName())
		err := db.Where("game_id = ? AND area_id = ? AND user_permission_id = ? AND language NOT IN ?", languageItem.GameId, languageItem.AreaId, languageItem.UserPermissionId, languageItem.Languages).Updates(map[string]interface{}{
			"deleted_on": time.Now().Unix(),
			"is_del":     1,
		}).Error
		if err != nil {
			// db.Rollback()
			return err
		}
	}
	// db.Commit()
	return nil
}

func UpdateUserPermissionLanguages(languages []*model.UserPermissionLanguage) error {
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserPermissionLanguage{}).TableName())
	err := db.Clauses((clause.OnConflict{
		Columns:   []clause.Column{{Name: "game_id"}, {Name: "area_id"}, {Name: "user_permission_id"}, {Name: "language"}, {Name: "intl_openid"}},
		DoUpdates: clause.AssignmentColumns([]string{"id", "desc", "creator", "updater", "modified_on", "deleted_on", "is_del", "deleted_on", "action_value"}),
	})).Create(&languages).Error
	if err != nil {
		return err
	}
	// 删除不在languages内的记录
	err = DeleteNotInLanguages(languages)
	return err
}

func GetUserPermissionLanguages(IntlOpenid string, actionValue int32, language string) (*model.UserPermissionLanguage, error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserPermissionLanguage{}).TableName())
	languageItem := model.UserPermissionLanguage{}
	err := db.Where("intl_openid = ? AND action_value = ? AND language = ?", IntlOpenid, actionValue, language).First(&languageItem).Error
	if err != nil {
		return nil, err
	}
	return &languageItem, nil
}

func GetUserAllPermissionLanguages(IntlOpenid string, actionValue int32) ([]*model.UserPermissionLanguage, error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserPermissionLanguage{}).TableName())
	languageItems := make([]*model.UserPermissionLanguage, 0)
	err := db.Where("intl_openid = ? AND action_value = ?", IntlOpenid, actionValue).Find(&languageItems).Error
	if err != nil {
		return nil, err
	}
	return languageItems, nil
}

func RemoveUserPermissionLanguages(intlOpenids []string, actionValue int32) error {
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserPermissionLanguage{}).TableName())
	err := db.Where("intl_openid in ? AND action_value = ?", intlOpenids, actionValue).Updates(map[string]interface{}{
		"deleted_on": time.Now().Unix(),
		"is_del":     1,
	}).Error
	return err
}

// 获取已经存在的多语言
func GetAlreadyExistUserPermissionLanguages(intlOpenids []string, actionValue int32) ([]*model.UserPermissionLanguage, error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.UserPermissionLanguage{}).TableName())
	languageItem := make([]*model.UserPermissionLanguage, 0)
	err := db.Where("intl_openid in ? AND action_value = ?", intlOpenids, actionValue).Find(&languageItem).Error
	if err != nil {
		return languageItem, err
	}
	return languageItem, nil
}
