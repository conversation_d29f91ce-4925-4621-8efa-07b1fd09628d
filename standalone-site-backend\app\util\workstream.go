package util

import (
	"context"
	"sync"
)

// 工作流任务
type WorkerPoolTask[I any, O any] struct {
	index  int   // 下标索引
	input  I     // 方法入参
	output O     // 方法回参
	err    error // 错误
}

// 工作流池子
type WorkerPoolJob[I any, O any] struct {
	data    []I
	workers int
	task    func(I) (O, error)
	ctx     context.Context
}

// 默认多少个工作任务
const defaultNumWorkers = 10

func (job *WorkerPoolJob[I, O]) WithWorkers(workers int) *WorkerPoolJob[I, O] {
	if workers == 0 {
		job.workers = defaultNumWorkers
	} else if workers > len(job.data) {
		job.workers = len(job.data)
	} else {
		job.workers = workers
	}

	return job
}

// 初始化工作
func NewJob[I any, O any](task func(I) (O, error), data []I) *WorkerPoolJob[I, O] {
	return &WorkerPoolJob[I, O]{
		workers: defaultNumWorkers,
		task:    task,
		data:    data,
		ctx:     context.Background(),
	}
}

// 工作流的处理方法
func WorkerPoolDo[I any, O any](job *WorkerPoolJob[I, O]) ([]O, []error, error) {
	results := make([]O, len(job.data))  // 返回的切片
	errs := make([]error, len(job.data)) // 错误的切片

	if len(job.data) == 0 {
		// 没有入参的话直接返回
		return results, errs, nil
	}

	// 任务队列
	tasksQueue := make(chan *WorkerPoolTask[I, O])
	// 结果队列
	resultsQueue := make(chan *WorkerPoolTask[I, O])

	var wg sync.WaitGroup

	for range job.workers {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for t := range tasksQueue {
				t.output, t.err = job.task(t.input)
				resultsQueue <- t
			}
		}()
	}

	var err error

	go func() {
	loop:
		for i := range job.data {
			select {
			default:
				tasksQueue <- &WorkerPoolTask[I, O]{
					index: i,
					input: job.data[i],
				}
			case <-job.ctx.Done():
				err = job.ctx.Err()
				break loop
			}
		}

		close(tasksQueue)
		wg.Wait()
		close(resultsQueue)
	}()

	for task := range resultsQueue {
		errs[task.index] = task.err
		results[task.index] = task.output
	}

	return results, errs, err
}
