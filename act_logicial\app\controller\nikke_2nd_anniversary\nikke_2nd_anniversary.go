// Package nikketwondanniversary 活动
package nikketwondanniversary

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_nikke_2nd_anniversary"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/logic/nikketmp"
)

// Nikke2ndAnniversaryImpl TODO
type Nikke2ndAnniversaryImpl struct {
	pb.UnimplementedNikke2NdAnniversary
}

// ResponseInfo TODO
type ResponseInfo struct {
	Ret  int    `json:"ret"`
	Msg  string `json:"msg"`
	Data map[string]struct {
		Dimensions map[string]struct {
			Metrics MetricsType `json:"metrics"`
		} `json:"dimensions"`
	} `json:"data"`
}

// MetricsType TODO
type MetricsType struct {
	C1  string `json:"c1"`
	C2  string `json:"c2"`
	C3  string `json:"c3"`
	C4  string `json:"c4"`
	C5  string `json:"c5"`
	C6  string `json:"c6"`
	C7  string `json:"c7"`
	C8  string `json:"c8"`
	C9  string `json:"c9"`
	C10 string `json:"c10"`
	C11 string `json:"c11"`
	C12 string `json:"c12"`
	C13 string `json:"c13"`
	C14 string `json:"c14"`
	C15 string `json:"c15"`
	C16 string `json:"c16"`
	C17 string `json:"c17"`
	C18 string `json:"c18"`
	C19 string `json:"c19"`
	C20 string `json:"c20"`
	C21 string `json:"c21"`
	C22 string `json:"c22"`
	C23 string `json:"c23"`
	C24 string `json:"c24"`
}

// RoleTwoThMetrics 角色两周年字段
func (s *Nikke2ndAnniversaryImpl) RoleTwoThMetrics(ctx context.Context, req *pb.RoleTwoThMetricsReq) (
	rsp *pb.RoleTwoThMetricsRsp, err error,
) {
	rsp = &pb.RoleTwoThMetricsRsp{}
	postData, err := nikketmp.GetDimensionsPostData(ctx, req.RoleInfo.GameId, req.RoleInfo.AreaId)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "error").Errorf("[nikke] get2ndPostData err: ", err.Error())
		return
	}

	url, err := nikketmp.GetDimensionsURL(ctx, req.RoleInfo.GameId, req.RoleInfo.AreaId, postData)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "error").Errorf("[nikke] get2ndUrl err: ", err.Error())
		return
	}

	optionOne := httpclient.ClientOption{
		URL: url,
		// Timeout: 2 * time.Second,
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		Type:     "POST",
		PostData: postData,
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	if resultOption.RequestError != nil {
		// 请求失败
		err = errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"http error, \t [Error]:{%v} ", url)
		return
	}
	response := resultOption.Result
	var respData ResponseInfo
	err = json.Unmarshal([]byte(response), &respData)
	if err != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.JsonParseError,
			"[nikke] 2nd parse result,result=%v, \t [Error]:{%v} ", response, err)
		return
	}

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}

	userKey := fmt.Sprintf(
		"%v%v%v",
		userAccount.IntlAccount.OpenId+"|",
		"0|",
		strconv.FormatInt(req.RoleInfo.GetAreaId(), 10),
	)

	if respData.Ret != 1 {
		tags, err := nikketmp.GetHavePresentTag(ctx, req.FsourceId)
		if err != nil {
			return rsp, err
		}
		rsp.Tags = tags
		metrics := respData.Data[userKey].Dimensions["10001"].Metrics
		rsp.C1 = metrics.C1
		rsp.C2 = metrics.C2
		rsp.C3 = metrics.C3
		rsp.C4 = metrics.C4
		rsp.C5 = metrics.C5
		rsp.C6 = metrics.C6
		rsp.C7 = metrics.C7
		rsp.C8 = metrics.C8
		rsp.C9 = metrics.C9
		rsp.C10 = metrics.C10
		rsp.C11 = metrics.C11
		rsp.C12 = metrics.C12
		rsp.C13 = metrics.C13
		rsp.C14 = metrics.C14
		rsp.C15 = metrics.C15
		rsp.C16 = metrics.C16
		rsp.C17 = metrics.C17
		rsp.C18 = metrics.C18
		rsp.C19 = metrics.C19
		rsp.C20 = metrics.C20
		rsp.C21 = metrics.C21
		rsp.C22 = metrics.C22
		rsp.C23 = metrics.C23
		rsp.C24 = metrics.C24
	}
	return
}

// TwoThAddSendLog 角色两周年字段
func (s *Nikke2ndAnniversaryImpl) TwoThAddSendLog(ctx context.Context, req *pb.TwoThAddSendLogReq) (
	rsp *pb.TwoThAddSendLogRsp, err error,
) {
	rsp = &pb.TwoThAddSendLogRsp{}
	err = nikketmp.TwoThAddSendLog(ctx, req)

	if err != nil {
		return rsp, err
	}

	rsp.LogHasAdd = true
	return
}

// TwoThScheduledSend 角色两周年字段
func (s *Nikke2ndAnniversaryImpl) TwoThScheduledSend(ctx context.Context, req *pb.TwoThScheduledSendReq) (
	rsp *pb.TwoThScheduledSendRsp, err error,
) {
	rsp = &pb.TwoThScheduledSendRsp{}

	go nikketmp.TwoThScheduledSend(ctx)
	return
}
