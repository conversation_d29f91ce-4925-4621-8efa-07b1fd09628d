package model

type UserPermissionLanguage struct {
	*Model
	UserPermissionId int64  `json:"user_permission_id"`
	Language         string `json:"language"` // 多语言
	Desc             string `json:"desc"`
	Creator          string `json:"creator"`
	Updater          string `json:"updater"`
	GameId           string `json:"game_id"`
	AreaId           string `json:"area_id"`
	IntlOpenid       string `json:"intl_openid"`
	ActionValue      int    `json:"action_value"`
}

func (p *UserPermissionLanguage) TableName() string {
	return "p_user_permission_language"
}
