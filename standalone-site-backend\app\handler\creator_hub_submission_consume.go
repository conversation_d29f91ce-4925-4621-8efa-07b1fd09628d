package handler

import (
	"context"
	"encoding/json"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/IBM/sarama"
	"trpc.publishing_application.standalonesite/app/common"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/message"
	"trpc.publishing_application.standalonesite/app/model"
)

func CreatorHubSubmissionConsumer(ctx context.Context, msgArray []*sarama.ConsumerMessage) (err error) {
	var handlers []func() error
	for _, v := range msgArray {
		value := string(v.Value)
		handlers = append(handlers, func() error {
			err := ConsumeCreatorSubmissionMessage(common.GetCommonCtx(), value)
			return err
		})
	}
	err = trpc.GoAndWait(handlers...)
	return
}

func ConsumeCreatorSubmissionMessage(ctx context.Context, sendStr string) (err error) {
	defer recovery.CatchGoroutinePanic(context.Background())
	log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Infof("consume creator hub submission message: %v", sendStr)
	creatorHubMsgData := &model.CreatorHubWorkKafkaData{}
	if err := json.Unmarshal([]byte(sendStr), creatorHubMsgData); err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("consume creator hub submission message to unmarshal failed: %v, message: %v", err, sendStr)
		return nil
	}
	err = message.CtreatorHubWorkMessageConsumeHandle(ctx, creatorHubMsgData, 0)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", constants.LogType_Standalonesite).Errorf("message.CtreatorHubWorkMessageConsumeHandle failed: %v, message: %v", err, sendStr)
		return nil
	}
	// 重试失败的作品
	message.RetryFailedWork(ctx)
	return
}
