package util

import (
	"fmt"
	"github.com/spf13/cast"
	"math/rand"
	"time"
)

func GetCurrentTimestamp() int64 {
	return time.Now().Unix()
}

func GetOneYearAgoTimestamp() int64 {
	// location, _ := time.LoadLocation("Asia/Shanghai")
	location := time.UTC
	// 获取当前时间
	now := time.Now().In(location)
	// 计算一年前的时间
	oneYearAgo := now.AddDate(-1, 0, 1)
	// 设置时间为当天零点
	zeroTime := time.Date(oneYearAgo.Year(), oneYearAgo.Month(), oneYearAgo.Day(), 0, 0, 0, 0, location)
	timestamp := zeroTime.Unix()
	return timestamp
}

func GetTodayStartAndEndTimestamp() (int64, int64) {
	// location, _ := time.LoadLocation("Asia/Shanghai")
	location := time.UTC
	// 获取当前时间
	now := time.Now().In(location)
	start := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location)
	end := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, location)
	startTimestamp := start.Unix()
	endTimestamp := end.Unix()
	return startTimestamp, endTimestamp
}

func GetThisWeekStartAndEndTimestamp() (int64, int64) {
	// location, _ := time.LoadLocation("Asia/Shanghai")
	location := time.UTC
	// 获取当前时间
	now := time.Now().In(location)
	weekday := int(now.Weekday())

	// 计算起始时间
	start := now.AddDate(0, 0, -weekday+1)
	start = time.Date(start.Year(), start.Month(), start.Day(), 0, 0, 0, 0, location)

	// 计算结束时间
	end := start.AddDate(0, 0, 6)
	end = time.Date(end.Year(), end.Month(), end.Day(), 23, 59, 59, 0, location)

	startTimestamp := start.Unix()
	endTimestamp := end.Unix()

	return startTimestamp, endTimestamp
}

func GetThisMonthStartAndEndTimestamp() (int64, int64) {
	// location, _ := time.LoadLocation("Asia/Shanghai")
	location := time.UTC
	// 获取当前时间
	now := time.Now().In(location)
	year, month, _ := now.Date()

	start := time.Date(year, month, 1, 0, 0, 0, 0, location)
	end := start.AddDate(0, 1, 0).Add(-time.Nanosecond)

	startTimestamp := start.Unix()
	endTimestamp := end.Unix()

	return startTimestamp, endTimestamp
}

func GetTimeBefore(d time.Duration) int64 {
	return time.Now().Add(-d).Unix()
}

func GetTimeAfter(d time.Duration) int64 {
	return time.Now().Add(d).Unix()
}

// CheckTimeBetweenStartAndEndByArray 校验一个数组（必须是两个数量的数组）是否包含当前时间
func CheckTimeBetweenStartAndEndByArray(startAndTimes []int64) bool {
	if len(startAndTimes) != 2 {
		// 如果不是数量为2那就是不合法数组，不需要走剩下的逻辑
		return false
	}
	currentTime := time.Now().Unix()
	if startAndTimes[0] < currentTime && startAndTimes[1] > currentTime {
		return true
	}
	return false
}

func SecondToRandMicro(second int64) int64 {
	rand.Seed(time.Now().UnixNano())
	return cast.ToInt64(fmt.Sprintf("%d%d", second, rand.Intn(900000)+100000))
}
