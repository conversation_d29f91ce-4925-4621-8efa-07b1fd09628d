package model

type EmoticonGroup struct {
	*Model
	Name    string `gorm:"column:name" json:"name"`       //表情包合集名称
	PicURL  string `gorm:"column:pic_url" json:"pic_url"` //表情包封面图
	Status  int    `gorm:"column:status" json:"status"`   //状态：1、不可见，2、可见
	Order   int    `gorm:"column:order" json:"order"`     //顺序值
	Creator string `gorm:"column:creator" json:"creator"` //创建人
	Updater string `gorm:"column:updater" json:"updater"` //操作人
	GameID  string `gorm:"column:game_id" json:"game_id"` //游戏id
	AreaID  string `gorm:"column:area_id" json:"area_id"` //大区id

}

func (e *EmoticonGroup) TableName() string {
	return "p_emoticon_group"
}

type EmoticonGroupJoinIconInfo struct {
	GroupID     int64  `json:"group_id"`
	GroupName   string `json:"group_name"`    //表情包合集名称
	GroupPicURL string `json:"group_pic_url"` //表情包封面图
	GroupOrder  int    `json:"group_order"`   //顺序值
	IconID      int64  `json:"icon_id"`
	IconName    string `json:"icon_name"`  //表情名称
	Icon        string `json:"icon"`       //表情图
	IconOrder   int    `json:"icon_order"` //顺序值
}

type EmoticonIconInfo struct {
	ID    int64  `json:"id"`
	Name  string `json:"name"`  //表情名称
	Icon  string `json:"icon"`  //表情图
	Order int    `json:"order"` //顺序值
}

type EmoticonGroupInfo struct {
	ID                int64               `json:"id"`
	Name              string              `json:"name"`    //表情包合集名称
	PicURL            string              `json:"pic_url"` //表情包封面图
	Order             int                 `json:"order"`   //顺序值
	EmoticonIconInfos []*EmoticonIconInfo `json:"icons"`
}
