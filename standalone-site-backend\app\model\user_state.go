package model

type UserState struct {
	*Model
	IntlOpenid  string `gorm:"column:intl_openid" json:"intl_openid"`     //动态评论唯一ID
	FansNum     int32  `gorm:"column:fans_num" json:"fans_num"`           //粉丝数
	FollowNum   int32  `gorm:"column:follow_num" json:"follow_num"`       //关注数
	AllPostNum  int32  `gorm:"column:all_post_num" json:"all_post_num"`   //主态下看到的用户所有动态数（未审批+已审批）
	PostStarNum int32  `gorm:"column:post_star_num" json:"post_star_num"` //用户所有动态的点赞数总和
	PostNum     int32  `gorm:"column:post_num" json:"post_num"`           //已发布生效的动态数
}

// TableName sets the insert table name for this struct type
func (u *UserState) TableName() string {
	return "p_user_state"
}
