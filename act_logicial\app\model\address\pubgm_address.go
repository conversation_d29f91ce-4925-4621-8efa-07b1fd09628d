package address

import (
	"trpc.act.logicial/app/model"
)

// PUBGMAdressModel 抽奖表
type PUBGMAdressModel struct {
	model.AppModel
}

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名
func (PUBGMAdressModel) TableName() string {
	return "pubgm_address_log"
}

// PUBGMAdressData 抽奖记录表
type PUBGMAdressData struct {
	PUBGMAdressModel
	Uid         string `gorm:"type:varchar(32);column:uid;''"`
	AccountType int32  `gorm:"type:tinyint(1);column:account_type;0"`
	GameId      string `gorm:"type:varchar(64);column:game_id;null"`
	Name        string `gorm:"type:varchar(255);column:name;null"`
	Country     string `gorm:"type:varchar(64);column:country;null"`
	Address     string `gorm:"type:text;column:address;null"`
	PhoneNumber string `gorm:"type:varchar(32);column:phone_number;null"`
	ZipCode     string `gorm:"type:varchar(32);column:zip_code;null"`
}

// PresentLotteryLevelList 设置礼包抽奖等级
type PresentLotteryLevelList struct {
	PresentID string
	LevelList []int64
}
