// Package hoktmp TODO
package hoktmp

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/util/object"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	gameX1Pb "git.woa.com/trpcprotocol/publishing_marketing/game_x1"
	"git.woa.com/trpcprotocol/publishing_marketing/logicial_hok_tmp"
	"google.golang.org/protobuf/proto"
	"math"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	baseLogic "trpc.act.logicial/app/logic/base"
	"trpc.act.logicial/app/logic/sensitivewords"
	baseTotalModel "trpc.act.logicial/app/model/base"
	"trpc.act.logicial/app/model/hok"
)

const storageKey = "mission"
const (
	SuccessfullyLoggedHoKClub                                     = "1"
	UseHoKClubCreateTournament                                    = "2"
	UseHoKClubCreateAndCompleteEffectiveTournament                = "3"
	UsingHoKClubSuccessfullyParticipateTournamentCreatedBySomeone = "4"
	ALLHokClubTaskStatus                                          = "5"

	// 礼包资源ID
	SuccessfullyLoggedHoKClubSourceId                                     = "Wand-20240716031714-P32dfe22b699b"
	UseHoKClubCreateTournamentSourceId                                    = "Wand-20240716031742-Pf0db9380813a"
	UseHoKClubCreateAndCompleteEffectiveTournamentSourceId                = "Wand-20240716031830-Pff740c5540bf"
	UsingHoKClubSuccessfullyParticipateTournamentCreatedBySomeoneSourceId = "Wand-20240716031909-P4b4d7820fefa"
	ALLHokClubTaskStatusSourceId                                          = "Wand-20240716031941-Pae57909d11bf"
)

type GetTaskCompleteStatusParam struct {
	ActivityId   int32  `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`        // 活动id
	RuleId       int32  `protobuf:"varint,2,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`                    // 规则id
	RuleSecretId string `protobuf:"bytes,3,opt,name=rule_secret_id,json=ruleSecretId,proto3" json:"rule_secret_id,omitempty"` // 规则secretID
	TagId        string `json:"tag_id"`
}

// CommunityUserInfo TODO
type CommunityUserInfo struct {
	UserId   string `json:"userId"`
	Username string `json:"username"`
}

// BindInfoResponseInfo TODO
type BindInfoResponseInfo struct {
	IsBind            bool              `json:"isBind"`
	CommunityUserInfo CommunityUserInfo `json:"communityUserInfo"`
}

// RewardInfo TODO
type RewardInfo struct {
	LeaderPhone string `json:"leader_phone"`
	Code        string `json:"code"`
	Rank        int32  `json:"rank"`
}

// RankReturnInfo TODO
type RankReturnInfo struct {
	Data RewardInfo `json:"data"`
}

// BindInfoResponseReturnInfo TODO
type BindInfoResponseReturnInfo struct {
	Data BindInfoResponseInfo `json:"data"`
}

// HOKRecordUserActivity 记录参加活动的用户
func HOKRecordUserActivity(ctx context.Context) error {

	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return err
	}
	langType := metadata.GetLangType(ctx)

	// 记录参与活动的用户
	var userParticipation hok.HokClubUserParticipation
	if err = DB.DefaultConnect().WithContext(ctx).Where(hok.HokClubUserParticipation{
		UID:         userAccount.Uid,
		AccountType: int(userAccount.AccountType),
	}).Assign(hok.HokClubUserParticipation{
		LangType: langType,
	}).FirstOrCreate(&userParticipation).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"HOKRecordUserActivity FirstOrCreate db error, \t [Error]:{%v} ", err)
	}
	return nil
}

// GetBindWhatsAppState TODO
func GetBindWhatsAppState(ctx context.Context, channelId int32) (res BindInfoResponseInfo, err error) {
	url := config.GetConfig().HokWhatsAppUrl
	postData := map[string]interface{}{
		"channelId": channelId,
	}
	userAccount, err := metadata.GetUserAccount(ctx)

	header := map[string]string{
		"Content-Type": "application/json",
		"campSource":   "HOK",
		"gameOpenId":   userAccount.IntlAccount.OpenId,
		"channel":      strconv.Itoa(int(userAccount.AccountType)),
		"gameToken":    userAccount.IntlAccount.Token,
		"gameId":       userAccount.IntlAccount.GameId,
		// "gameOpenId": "12657636916784752675",
		// "channel":    "1",
		// "gameToken":  "358d8858ee895f62da9a0c408e1414d7bcbe81ad",
		// "gameId":     "29134",
	}

	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("header: %v", header))
	data := httpclient.ClientOption{
		URL: url,
		// Timeout: 2 * time.Second,
		Header:   header,
		Type:     "POST",
		PostData: postData,
	}
	result := httpclient.RequestOne(ctx, data)
	if result.RequestError != nil {
		// 请求失败
		err = errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"http error, \t [Error]:{%v} ", url)
		return
	}
	response := result.Result
	var respData BindInfoResponseReturnInfo
	err = json.Unmarshal([]byte(response), &respData)
	res = respData.Data
	return
}

// GetRank TODO
func GetRank(ctx context.Context, phone string) (rank int32, err error) {
	url := config.GetConfig().HokTempUpRankUrl
	postData := map[string]interface{}{
		"phone": phone,
	}
	header := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": config.GetConfig().Authorization,
	}
	// userAccount, err := metadata.GetUserAccount(ctx)

	data := httpclient.ClientOption{
		URL: url,
		// Timeout: 2 * time.Second,
		Header:   header,
		Type:     "POST",
		PostData: postData,
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("GetRank header: %v", header))
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("GetRank postData: %v", postData))
	result := httpclient.RequestOne(ctx, data)
	if result.RequestError != nil {
		// 请求失败
		err = errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"http error, \t [Error]:{%v} ", url)
		return
	}
	response := result.Result
	var respData RankReturnInfo
	err = json.Unmarshal([]byte(response), &respData)
	rank = respData.Data.Rank
	return
}

func GenBodyDigest(body []byte) string {
	digestBody := sha256.New()
	digestBody.Write(body)
	return base64.StdEncoding.EncodeToString(digestBody.Sum(nil))
}

// GetSignatureString 获取签名，method只能为GET或POST
func GetSignatureString(method string, accessKey string, secretKey string, uri string, gmtDate string,
	queryParams map[string]string, body []byte) string {

	canonicalQueryString := sensitivewords.GetCanonicalQueryString(queryParams)
	bodyDigest := GenBodyDigest(body)
	signHeaders := fmt.Sprintf("X-HMAC-DIGEST:%v\n", bodyDigest)

	res := method + "\n"
	res = res + uri + "\n"
	res = res + canonicalQueryString + "\n"
	res = res + accessKey + "\n"
	res = res + gmtDate + "\n"
	res = res + signHeaders

	hash := hmac.New(sha256.New, []byte(secretKey))
	hash.Write([]byte(res))
	hex.EncodeToString(hash.Sum(nil))

	hashResult := base64.StdEncoding.EncodeToString(hash.Sum(nil))
	return hashResult
}

// GetHokTaskStatus 获取hok办赛任务状态
func GetHokTaskStatus(ctx context.Context, req *logicial_hok_tmp.GetHokTaskStatusReq) error {

	const method = http.MethodPost
	hokEventsClub := config.GetConfig().HOKEventsClub
	accessKey := hokEventsClub.AccessKey
	secretKey := hokEventsClub.SecretKey
	path := hokEventsClub.Path
	host := hokEventsClub.Host

	// 获取用户信息
	//userAccount, err := metadata.GetUserAccount(ctx)
	//if err != nil {
	//	return err
	//}

	urlStr := fmt.Sprintf("%s%s", host, path)
	queryParams := make(map[string]string)
	//body := []byte("{}")
	getGroupTask := hok.GetGroupTaskReq{
		App: req.App,
		User: hok.User{
			Uin:      "6720399011037240221",
			AreaId:   "88",
			RoleId:   "6720399011037240221",
			Platform: "1",
			//AccType:     "",
			PartitionId: "108003",
			//Ext:         nil,
		},
		GroupId:  req.Groupid,
		UseCache: req.UseCache,
	}
	//if roleInfo := req.GetRoleInfo(); roleInfo != nil {
	//	getGroupTask.User.RoleId = roleInfo.RoleId
	//	getGroupTask.User.AreaId = cast.ToString(roleInfo.AreaId)
	//	getGroupTask.User.PartitionId = cast.ToString(roleInfo.ZoneId)
	//	getGroupTask.User.Platform = cast.ToString(roleInfo.PlatId)
	//}

	getGroupTaskByte, err := json.Marshal(getGroupTask)
	if err != nil {
		return errs.NewCustomError(ctx, code.CommonParamJsonError,
			"GetHokTaskStatus json Marshal error,getGroupTask[%v], err:[%v]", getGroupTask, err)
	}

	gmtDate := sensitivewords.GetFormatNow()
	sign := GetSignatureString(method, accessKey, secretKey, path, gmtDate, queryParams, getGroupTaskByte)
	log.WithFieldsContext(ctx, "log_type", "debug").Infof("GetHokTaskStatus show sign:[%v],gmtDate:[%v]", sign, gmtDate)
	bodyDigest := GenBodyDigest(getGroupTaskByte)
	optionOne := httpclient.ClientOption{
		URL: urlStr,
		Header: map[string]string{
			"Content-Type":          "application/json",
			"X-HMAC-SIGNATURE":      sign,
			"X-HMAC-ALGORITHM":      "hmac-sha256",
			"X-HMAC-ACCESS-KEY":     accessKey,
			"Date":                  gmtDate,
			"X-HMAC-SIGNED-HEADERS": "X-HMAC-DIGEST",
			"X-HMAC-DIGEST":         bodyDigest,
		},
		Type:       method,
		PostString: string(getGroupTaskByte),
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	if resultOption.RequestError != nil {
		// 请求失败
		return errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"GetHokTaskStatus http error, \t [Error]:{%v} ", resultOption.RequestError)
	}
	response := resultOption.Result
	log.WithFieldsContext(ctx, "log_type", "debug").Infof("GetHokTaskStatus show response:[%v]", response)
	var groupTaskItem hok.GetGroupTaskRsp
	if err = json.Unmarshal([]byte(response), &groupTaskItem); err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeBusiness, code.JsonParseError,
			"GetHokTaskStatus Unmarshal rsp err,response=[%v], \t [Error]:{%v} ", response, err)
	}
	// 记录任务完成
	if len(groupTaskItem.Data.TaskGroup.GroupTasks) != 0 {
		if err = RecordTaskCompletion(ctx, groupTaskItem.Data.TaskGroup.GroupTasks, req.FsourceId); err != nil {
			return err
		}
	} else {
		log.WithFieldsContext(ctx, "log_type", "debug").Infof("GetHokTaskStatus GroupTasks is empty; groupTaskItem:[%v]", groupTaskItem)
	}
	return nil
}

// RecordTaskCompletion 记录hok办赛任务完成状态
func RecordTaskCompletion(ctx context.Context, taskList []*hok.Task, fSourceID string) error {

	for _, v := range taskList {
		switch v.TaskID {
		case 1: // 成功登录HoK Club
			if ok := v.TaskData.IsFinished; !ok {
				continue
			}
			err := recordsTaskCompleteStatus(ctx, fSourceID, SuccessfullyLoggedHoKClub)
			if err != nil {
				return err
			}
		case 2: // 使用HOK CLUB，创建一次比赛
			if ok := v.TaskData.IsFinished; !ok {
				continue
			}
			err := recordsTaskCompleteStatus(ctx, fSourceID, UseHoKClubCreateTournament)
			if err != nil {
				return err
			}
		case 3: // 使用HOK CLUB，创建并完成一次有效的办赛
			if ok := v.TaskData.IsFinished; !ok {
				continue
			}
			err := recordsTaskCompleteStatus(ctx, fSourceID, UseHoKClubCreateAndCompleteEffectiveTournament)
			if err != nil {
				return err
			}
		case 4: // 使用HOK CLUB，成功参加一次其他人创建的赛事
			if ok := v.TaskData.IsFinished; !ok {
				continue
			}
			err := recordsTaskCompleteStatus(ctx, fSourceID, UsingHoKClubSuccessfullyParticipateTournamentCreatedBySomeone)
			if err != nil {
				return err
			}
		default:
			// 无效的taskId
			return errs.NewCustomError(ctx, code.MissionInvalidTaskId,
				"RecordTaskCompletion MissionInvalidTaskId taskId:[%v]", v.TaskID)
		}
	}
	return nil
}

func recordsTaskCompleteStatus(ctx context.Context, fSourceID, tagId string) error {
	// 添加单个任务完成状态
	addTaskData := baseTotalModel.AddParamStruct{
		FsourceID:  fSourceID,
		Type:       2,
		Tag:        tagId,
		StorageKey: storageKey,
		TotalLimit: 1,
		DayLimit:   1,
	}
	// 添加总任务完成状态
	addAllTaskData := baseTotalModel.AddParamStruct{
		FsourceID:  fSourceID,
		Type:       2,
		Tag:        ALLHokClubTaskStatus,
		StorageKey: storageKey,
		TotalLimit: 4,
		DayLimit:   4,
	}
	_, err := baseLogic.AddData(ctx, addTaskData, 0)
	if err != nil {
		return err
	}
	_, err = baseLogic.AddData(ctx, addAllTaskData, 0)
	if err != nil {
		return err
	}
	return nil
}

func buildSourceParamMap() map[string]GetTaskCompleteStatusParam {
	sourceParamMap := make(map[string]GetTaskCompleteStatusParam, 4)
	sourceParamMap[SuccessfullyLoggedHoKClubSourceId] = GetTaskCompleteStatusParam{
		ActivityId:   41529,
		RuleId:       1141300,
		RuleSecretId: "ac1570",
		TagId:        SuccessfullyLoggedHoKClub,
	}
	sourceParamMap[UseHoKClubCreateTournamentSourceId] = GetTaskCompleteStatusParam{
		ActivityId:   41529,
		RuleId:       1141299,
		RuleSecretId: "0f8653",
		TagId:        UseHoKClubCreateTournament,
	}
	sourceParamMap[UseHoKClubCreateAndCompleteEffectiveTournamentSourceId] = GetTaskCompleteStatusParam{
		ActivityId:   41529,
		RuleId:       1141297,
		RuleSecretId: "21b23d",
		TagId:        UseHoKClubCreateAndCompleteEffectiveTournament,
	}
	sourceParamMap[UsingHoKClubSuccessfullyParticipateTournamentCreatedBySomeoneSourceId] = GetTaskCompleteStatusParam{
		ActivityId:   41529,
		RuleId:       1141295,
		RuleSecretId: "ee6df0",
		TagId:        UsingHoKClubSuccessfullyParticipateTournamentCreatedBySomeone,
	}
	return sourceParamMap
}

func HOKClubAutoDistributeGifts(ctx context.Context, fSourceId string) error {

	newCtx := context.Background()
	// 获取所有参与活动人数
	var count int64
	userParticipationTableName := hok.HOKUserParticipationModel{}.TableName()
	if err := DB.DefaultConnect().WithContext(newCtx).Table(userParticipationTableName).
		Count(&count).Error; err != nil {
		return errs.NewSystemError(newCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"HOKClubAutoDistributeGifts count db error, \t [Error]:{%v} ", err)
	}
	// 根据参与的用户总数分页
	pageSize := 50
	totalPages := int(math.Ceil(float64(count) / float64(pageSize)))

	sourceIdMap := map[string]string{
		SuccessfullyLoggedHoKClubSourceId:                                     SuccessfullyLoggedHoKClub,
		UseHoKClubCreateTournamentSourceId:                                    UseHoKClubCreateTournament,
		UseHoKClubCreateAndCompleteEffectiveTournamentSourceId:                UseHoKClubCreateAndCompleteEffectiveTournament,
		UsingHoKClubSuccessfullyParticipateTournamentCreatedBySomeoneSourceId: UsingHoKClubSuccessfullyParticipateTournamentCreatedBySomeone,
		ALLHokClubTaskStatusSourceId:                                          ALLHokClubTaskStatus,
	}

	sourceParamMap := buildSourceParamMap()

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	gameProxy := gamePb.NewGameClientProxy()
	gameX1Proxy := gameX1Pb.NewX1ClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		offset := (pageNumber - 1) * pageSize
		var userParticipationList []hok.HokClubUserParticipation
		sel := "uid,account_type"
		db := DB.DefaultConnect().Debug().WithContext(newCtx).Table(userParticipationTableName).Select(sel).
			Offset(offset).Limit(pageSize).Order("id asc").Find(&userParticipationList)
		if err := db.Error; err != nil {
			return errs.NewSystemError(newCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"HOKClubAutoDistributeGifts Find db error, \t [Error]:{%v} ", err.Error())
		}

		for _, v := range userParticipationList {
			wg.Add(1)
			go func(data hok.HokClubUserParticipation) {
				defer wg.Done()
				// 获取当前用户已领取礼包列表
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId: openID,
						// todo ChannelId暂定为3
						ChannelId: 3,
					},
				})
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				presentList, err := sendProxy.GetUserFSourcePresentList(newCtx, &presentPb.GetUserFSourcePresentListReq{
					FsourceId: fSourceId,
				}, callopts...)
				if err != nil {
					log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
						"HOKClubAutoDistributeGifts GetUserFSourcePresentList error: accountData:[%v],err:[%v]",
						accountData, err))
					return
				}
				if len(presentList.UserHasPresentList) == 5 {
					// 当前用户所有礼包都已领取
					return
				}
				// 获取当前用户任务完成状态
				missionTaskCompleteStatusMap := make(map[string]bool, 5)
				for sourceId, param := range sourceParamMap {
					if ok, _ := object.InArray(sourceId, presentList.UserHasPresentList); ok {
						// 当前礼包已领取
						missionTaskCompleteStatusMap[param.TagId] = true
						continue
					}
					// 获取未领取礼包的任务完成状态
					_, taskCompleteStatusErr := gameX1Proxy.GetTaskCompleteStatus(newCtx, &gameX1Pb.GetTaskCompleteStatusReq{
						ActivityId:   param.ActivityId,
						RuleId:       param.RuleId,
						RuleSecretId: param.RuleSecretId,
					}, callopts...)
					if taskCompleteStatusErr != nil {
						log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
							"HOKClubAutoDistributeGifts GetTaskCompleteStatus error: UID:[%v],err:[%v]",
							data.UID, taskCompleteStatusErr))
						// 当前任务未完成
						missionTaskCompleteStatusMap[param.TagId] = false
						continue
					}
					// 当前任务已完成
					missionTaskCompleteStatusMap[param.TagId] = true
				}

				// 遍历完成列表如果前四个都完成，则第五个任务也完成
				allDone := true
				for _, done := range missionTaskCompleteStatusMap {
					if !done {
						allDone = false
						break
					}
				}
				if allDone {
					missionTaskCompleteStatusMap[ALLHokClubTaskStatus] = true
				}
				log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf(
					"HOKClubAutoDistributeGifts show missionTaskCompleteStatusMap: UID:[%v],missionTaskCompleteStatusMap:[%v]",
					data.UID, missionTaskCompleteStatusMap))
				// 获取用户区服角色信息
				roleList, err := gameProxy.GetRoleList(newCtx, &gamePb.GetRoleInfoReq{
					GameId: "29134",
				}, callopts...)
				if err != nil {
					log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
						"HOKClubAutoDistributeGifts GetRoleList err: roleList:[%v],callopts:[%v], err:[%v]",
						roleList, callopts, err))
					return
				}
				if len(roleList.RoleList) != 1 {
					log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
						"HOKClubAutoDistributeGifts GetRoleList len err: roleList:[%v],callopts:[%v]",
						roleList, callopts))
					return
				}
				// 判断未领取的礼包任务是否完成
				for presentId, tagId := range sourceIdMap {
					taskDone := missionTaskCompleteStatusMap[tagId]
					// 未领奖且任务已完成
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf(
						"HOKClubAutoDistributeGifts show UserHasPresentList: UID:[%v],UserHasPresentList:[%v],taskDone:[%v]",
						data.UID, presentList.UserHasPresentList, taskDone))
					if ok, _ := object.InArray(presentId, presentList.UserHasPresentList); !ok && taskDone {
						// 发送未领取奖品
						sendPresentRsp, err := sendProxy.SendPresent(newCtx, &presentPb.SendPresentReq{
							FsourceId: fSourceId,
							PresentId: presentId,
							RoleInfo:  roleList.RoleList[0],
						}, callopts...)
						if err != nil {
							log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
								"HOKClubAutoDistributeGifts SendPresent error: UID:[%v],presentId:[%v],RoleInfo:[%v],err:[%v]",
								data.UID, presentId, roleList.RoleList, err))
							continue
						}
						if sendPresentRsp.SendType != 1 {
							log.WithFieldsContext(newCtx, "log_type", "error").Infof(fmt.Sprintf(
								"HOKClubAutoDistributeGifts SendPresent SendType error: UID:[%v],presentId:[%v],RoleInfo:[%v],sendPresentRsp:[%v]",
								data.UID, presentId, roleList.RoleList, sendPresentRsp))
							continue
						}
						log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf(
							"HOKClubAutoDistributeGifts send success: UID:[%v],sendPresentRsp:[%v]", data.UID, sendPresentRsp))
					}
				}
			}(v)
		}
		wg.Wait()
	}
	return nil
}
