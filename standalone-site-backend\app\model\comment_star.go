package model

import "trpc.publishing_application.standalonesite/app/constants"

type CommentStar struct {
	*Model
	Comment     *Comment                  `json:"-" gorm:"-"`
	PostUUID    string                    `json:"post_uuid"` //动态唯一ID
	CommentUUID string                    `json:"comment_uuid"`
	Type        constants.CommentStarType `json:"type"`
	IntlOpenid  string                    `json:"intl_openid"`
}

func (c *CommentStar) TableName() string {
	return "p_comment_star"
}
