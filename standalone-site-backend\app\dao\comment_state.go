package dao

import (
	"time"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

func CreateCommentState(commentUuid string) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentState{}).TableName()).Create(&model.CommentState{
		CommentUUID: commentUuid,
		UpvoteCount: 0,
	}).Error
}

func BatchCreateCommentState(data []*model.CommentState) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentState{}).TableName()).CreateInBatches(data, 100).Error
}

func GetCommentStateByCommentUuid(commentUuid string) (*model.CommentState, error) {
	var commentState *model.CommentState
	err := DB.SelectConnect("db_standalonesite").Table((&model.CommentState{}).TableName()).Where("comment_uuid = ?", commentUuid).First(&commentState).Error
	if err != nil {
		return nil, err
	}
	return commentState, nil
}

func CommentStateUpdateUpvoteCount(comment *model.CommentState) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentState{}).TableName()).Where("comment_uuid = ?", comment.CommentUUID).Updates(map[string]interface{}{
		"modified_on":  time.Now().Unix(),
		"upvote_count": comment.UpvoteCount,
	}).Error
}

func CommentStateUpdateReplyCount(comment *model.CommentState) error {
	return DB.SelectConnect("db_standalonesite").Table((&model.CommentState{}).TableName()).Where("comment_uuid = ?", comment.CommentUUID).Updates(map[string]interface{}{
		"modified_on": time.Now().Unix(),
		"reply_count": comment.ReplyCount,
	}).Error
}

func GetCommentStatesByCommentUUids(commentUUids []string) ([]*model.CommentState, error) {
	commentStates := make([]*model.CommentState, 0)
	err := DB.SelectConnect("db_standalonesite").Table((&model.CommentState{}).TableName()).Where("comment_uuid in (?)", commentUUids).Find(&commentStates).Error
	if err != nil {
		return nil, err
	}
	return commentStates, nil
}
