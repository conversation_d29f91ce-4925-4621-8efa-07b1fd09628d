package nikke

import "trpc.act.logicial/app/model"

// 投票记录表
type Nikke2503UserVotingHistoryModel struct {
	model.AppModel
}

type Nikke2503UserVotingHistory struct {
	Nikke2503UserVotingHistoryModel
	ID          uint   `gorm:"primaryKey;column:id;comment:primary key" json:"id"`
	UID         string `gorm:"column:uid;size:64;comment:用户ID" json:"uid"`
	AccountType int16  `gorm:"column:account_type;comment:用户类型" json:"account_type"`
	VoteIdList  string `gorm:"column:vote_id_list;size:512;comment:被投票对象id列表" json:"vote_id_list"`
	LangType    string `gorm:"column:lang_type;size:64;comment:语言类型" json:"lang_type"`
}

func (Nikke2503UserVotingHistoryModel) TableName() string {
	return "nikke2503_user_voting_history"
}

type Nikke2503AllVotingRecordsModel struct {
	model.AppModel
}

// 总体普通投票记录表
type Nikke2503AllVotingRecords struct {
	Nikke2503AllVotingRecordsModel
	ID       uint   `gorm:"primaryKey;column:id;comment:primary key" json:"id"`
	VoteTime int    `gorm:"column:vote_time;comment:投票时间;以天为单位时间戳" json:"vote_time"`
	VoteId   int    `gorm:"column:vote_id;comment:被投票对象id" json:"vote_id"`
	VoteNum  uint64 `gorm:"column:vote_num;comment:得票数" json:"vote_num"`
}

func (Nikke2503AllVotingRecordsModel) TableName() string {
	return "nikke2503_all_voting_records"
}

// 分区统计用户投票总数表
type Nikke2503AreaVotingCountModel struct {
	model.AppModel
}

type Nikke2503AreaVotingCount struct {
	Nikke2503AreaVotingCountModel
	ID       uint   `gorm:"primaryKey;column:id;comment:primary key" json:"id"`
	AreaId   string `gorm:"column:area_id;size:50;comment:大区ID" json:"area_id"`
	VoteName string `gorm:"column:vote_name;size:64;comment:投票对象名称" json:"vote_name"`
	VoteType int16  `gorm:"column:vote_type;comment:投票类型 1:普通，2:特殊" json:"vote_type"`
	VoteId   string `gorm:"column:vote_id;size:64;comment:普通和特殊投票 唯一标识" json:"vote_id"`
	CountNum uint64 `gorm:"column:count_num;comment:得票数" json:"count_num"`
}

func (Nikke2503AreaVotingCountModel) TableName() string {
	return "nikke2503_area_voting_count"
}

// 分语言统计用户投票总数表
type Nikke2503LangVotingCountModel struct {
	model.AppModel
}

type Nikke2503LangVotingCount struct {
	Nikke2503LangVotingCountModel
	ID       uint   `gorm:"primaryKey;column:id;comment:primary key" json:"id"`
	LangType string `gorm:"column:lang_type;size:64;comment:语言类型" json:"lang_type"`
	VoteName string `gorm:"column:vote_name;size:64;comment:投票对象名称" json:"vote_name"`
	VoteType int16  `gorm:"column:vote_type;comment:投票类型 1:普通，2:特殊" json:"vote_type"`
	VoteId   string `gorm:"column:vote_id;size:64;comment:普通和特殊投票 唯一标识" json:"vote_id"`
	CountNum uint64 `gorm:"column:count_num;comment:得票数" json:"count_num"`
}

func (Nikke2503LangVotingCountModel) TableName() string {
	return "nikke2503_lang_voting_count"
}

// 用户参与表
type Nikke2503UserParticipationModel struct {
	model.AppModel
}

type Nikke2503UserParticipation struct {
	Nikke2503UserParticipationModel
	ID          uint   `gorm:"primaryKey;column:id;comment:primary key" json:"id"`
	UID         string `gorm:"column:uid;size:64;comment:用户ID" json:"uid"`
	AccountType int16  `gorm:"column:account_type;comment:用户类型" json:"account_type"`
}

func (Nikke2503UserParticipationModel) TableName() string {
	return "nikke2503_user_participation"
}
