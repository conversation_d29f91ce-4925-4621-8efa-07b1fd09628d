package cron

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"time"
	"trpc.act.logicial/app/cache"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/logic/df_tmp_common"
	"trpc.act.logicial/app/model/df_activity"
	"trpc.act.logicial/app/mysql/df_activity_repo"
	"trpc.act.logicial/app/redis"
)

type UpdateGunActTasksProc struct {
}

// UpdateGunActTasks 更新拼枪活动任务
func UpdateGunActTasks(ctx context.Context) error {
	// 到数据库查询数据
	log.DebugContextf(ctx, "start update gun act tasks")
	defer func() {
		if r := recover(); r != nil {
			log.ErrorContextf(ctx, "UpdateGunActTasks Recovered from panic:", r)
		}
	}()
	// 加锁时长，默认90秒
	lockTime := config.GetConfig().GunActTaskCheckLockTime
	if lockTime <= 0 {
		lockTime = 90
	}
	log.DebugContextf(ctx, "UpdateGunActTasks lock time: %v", lockTime)
	if redis.LockByKey(ctx, redis.GetGunActCheckTaskLockKey(), lockTime) {
		log.DebugContextf(ctx, "task monitor get lock success, process")
	} else {
		log.DebugContextf(ctx, "task monitor get lock fail, return")
		return nil
	}
	// 监控时长
	startTime := time.Now().Unix()
	defer func() {
		endTime := time.Now().Unix()
		expireTime := endTime - startTime
		log.DebugContextf(ctx, "UpdateGunActTasks process finish, cost:", expireTime)
		if expireTime+20 > lockTime {
			// TODO 告警
			log.ErrorContextf(ctx, "UpdateGunActTasks process cost too much time, expireTime: %v", expireTime)
		} else {
			// 释放锁
			redis.UnLockByKey(ctx, redis.GetGunActCheckTaskLockKey())
		}
	}()
	proc := &UpdateGunActTasksProc{}
	proc.process(ctx)
	return nil
}

// process 处理
func (p *UpdateGunActTasksProc) process(ctx context.Context) {
	log.DebugContextf(ctx, "UpdateGunActTasksProc process start")
	activities := p.getActivities(ctx)

	// 处理超时的任务
	p.checkTimeoutTask(ctx, activities)

	// 超时自动发奖
	p.autoSendPrize(ctx, activities)

	// 发奖重试
	p.retrySendPrize(ctx, activities)
}

func (p *UpdateGunActTasksProc) getActivities(ctx context.Context) []*df_activity.GunActivityConf {
	var activities []*df_activity.GunActivityConf
	nowTime := uint32(time.Now().Unix())
	// 获取需要处理的活动
	cache.GunActivityCache.Range(func(key string, val interface{}) bool {
		res, ok := val.(*df_activity.GunActivityConf)
		if !ok {
			return true
		}
		if res.Status != df_activity.ActivityStatusOK {
			return true
		}
		if res.StartTimestamp > nowTime { // 活动未开始
			return true
		}
		if res.EndTimestamp+(3600*24) < nowTime { // 活动已结束超过24小时
			return true
		}
		activities = append(activities, res)
		return true
	})
	num := len(activities)
	log.DebugContextf(ctx, "UpdateGunActTasksProc get activities num: %v", num)
	return activities
}

// checkTimeoutTask 检查超时的任务
func (p *UpdateGunActTasksProc) checkTimeoutTask(ctx context.Context, activities []*df_activity.GunActivityConf) {
	helpExpireTime := config.GetConfig().GunActTaskExpireTime
	if helpExpireTime <= 0 { // 默认24小时
		helpExpireTime = 3600*24 + 10
	}
	num := len(activities)
	log.DebugContextf(ctx, "UpdateGunActTasksProc checkTimeoutTask, num: %d", num)
	for _, activity := range activities {
		// 获取处理超时的任务
		gameID := activity.GameId
		activityID := activity.ActivityId
		limit := p.getLimitNum(ctx)
		tasks, err := df_activity_repo.GunActivityRepoClient.GetExpireTasks(ctx, gameID, activityID,
			df_activity.GunTaskStatusProcessing, int64(helpExpireTime), limit)
		if nil != err {
			log.ErrorContextf(ctx, "UpdateGunActTasksProc checkTimeoutTask get activity task error, "+
				"gameID: %s, activityID: %s, err: %v", gameID, activityID, err)
			continue
		}
		num = len(tasks)
		log.DebugContextf(ctx, "UpdateGunActTasksProc checkTimeoutTask gameID: %v, activityID: %v, taskNum: %v",
			gameID, activityID, num)
		if num >= limit {
			// TODO 告警
			log.ErrorContextf(ctx, "UpdateGunActTasksProc checkTimeoutTask too many tasks, num: %v", num)
		}
		for _, task := range tasks {
			// TODO 监控
			log.ErrorContextf(ctx, "UpdateGunActTasksProc checkTimeoutTask timeout task, gameID: %v, activityID: %v, taskID: %v,"+
				"userID: %v",
				gameID, activityID, task.TaskId, task.UserId)

			// 检查任务是否完成
			finish := p.checkTaskComplete(ctx, task)
			if finish {
				// TODO 监控告警
				// 任务其实已经完成，但是助力时没有设置成功，定时任务重新执行处理
				log.ErrorContextf(ctx, "UpdateGunActTasksProc checkTimeoutTask task finished, "+
					"gameID: %v, activityID: %v, taskID: %v, userID: %v",
					gameID, activityID, task.TaskId, task.UserId)

				err = df_activity_repo.GunActivityRepoClient.UpdateTaskStatus(ctx, task.TaskId,
					df_activity.GunTaskStatusWaitClaimPrize, df_activity.GunTaskStatusProcessing)
				if nil != err { // 更新失败， 后续继续重试
					log.ErrorContextf(ctx, "UpdateGunActTasksProc task finish UpdateTaskStatus error:%v, taskId: %v",
						err, task.TaskId)
				}
				continue
			}

			// 将任务设置为超时
			err = df_activity_repo.GunActivityRepoClient.UpdateTaskStatus(ctx, task.TaskId,
				df_activity.GunTaskStatusNotCompleted, task.Status)
			if nil != err {
				log.ErrorContextf(ctx, "UpdateGunActTasksProc update task status error, err: %v", err)
				continue
			}
		}
	}
}

// checkTaskComplete 检查任务是否完成
func (p *UpdateGunActTasksProc) checkTaskComplete(ctx context.Context, task *df_activity.GunActivityTask) bool {
	if task == nil {
		return false
	}
	if task.Status != df_activity.GunTaskStatusProcessing {
		return false
	}

	// 查询任务是否完成助力
	// 获取枪械拼枪完成任务数
	needHelpNum, err := df_tmp_common.GetHelpNumByGunId(task.GunId)
	if nil != err {
		log.ErrorContextf(ctx, "checkTaskComplete GetHelpNumByGunId error:%v, taskId: %v", err, task.TaskId)
		return false
	}
	// 查询任务已经完成的助力数
	num, err := df_activity_repo.GunActivityRepoClient.GetTaskHelpNum(ctx, task.TaskId, 0)
	if nil != err {
		log.ErrorContextf(ctx, "checkTaskComplete GetTaskHelpNum error:%v, taskId: %v",
			err, task.TaskId)
		return false
	}
	if int32(num) < needHelpNum { // 还未完成，不处理
		log.DebugContextf(ctx, "checkTaskComplete task not finished, "+
			"taskId: %v, helpNum: %v, needNum: %v",
			task.TaskId, num, needHelpNum)
		return false
	}
	return true
}

// autoSendPrize 超时自动发奖
func (p *UpdateGunActTasksProc) autoSendPrize(ctx context.Context, activities []*df_activity.GunActivityConf) {
	helpExpireTime := config.GetConfig().GunActTaskExpireTime
	if helpExpireTime <= 0 { // 默认24小时
		helpExpireTime = 3600*24 + 10
	}
	num := len(activities)
	log.DebugContextf(ctx, "UpdateGunActTasksProc autoSendPrize, num: %d", num)
	for _, activity := range activities {
		gameID := activity.GameId
		activityID := activity.ActivityId
		limit := p.getLimitNum(ctx)
		// 获取已经完成，但是超时还未领奖的任务
		tasks, err := df_activity_repo.GunActivityRepoClient.GetExpireTasks(ctx, gameID, activityID,
			df_activity.GunTaskStatusWaitClaimPrize, int64(helpExpireTime), limit)
		if nil != err {
			log.ErrorContextf(ctx, "UpdateGunActTasksProc autoSendPrize get activity task error, "+
				"gameID: %s, activityID: %s, err: %v", gameID, activityID, err)
			continue
		}
		num = len(tasks)
		log.DebugContextf(ctx, "UpdateGunActTasksProc autoSendPrize gameID: %v, activityID: %v, taskNum: %v",
			gameID, activityID, num)
		if num >= limit {
			// TODO 告警
			log.ErrorContextf(ctx, "UpdateGunActTasksProc autoSendPrize too many tasks, num: %v", num)
		}
		// 发奖
		for _, task := range tasks {
			// TODO 监控告警
			log.DebugContextf(ctx, "UpdateGunActTasksProc autoSendPrize taskId: %v", task.TaskId)
			// 发奖
			df_activity_repo.GunActivityRepoClient.UpdateTaskStatus(ctx, task.TaskId, // 修改任务状态，发奖中
				df_activity.GunTaskStatusWaitDistributePrize, task.Status)
			task.Status = df_activity.GunTaskStatusWaitDistributePrize
			err = df_tmp_common.SendGunActTaskPrize(ctx, task)
			if nil != err {
				// TODO 告警
				log.ErrorContextf(ctx, "UpdateGunActTasksProc autoSendPrize send prize error, "+
					"gameID: %s, activityID: %s, task: %v, err: %v", gameID, activityID, task, err)
			}
		}
	}
}

func (p *UpdateGunActTasksProc) getLimitNum(ctx context.Context) int {
	limit := config.GetConfig().GunActTaskCheckLimitNum
	if limit <= 0 { // 默认1000
		limit = 1000
	}
	log.DebugContextf(ctx, "UpdateGunActTasksProc getLimitNum: %v", limit)
	return limit
}

// retrySendPrize 发奖重试
func (p *UpdateGunActTasksProc) retrySendPrize(ctx context.Context, activities []*df_activity.GunActivityConf) {
	num := len(activities)
	log.DebugContextf(ctx, "UpdateGunActTasksProc retrySendPrize, num: %d", num)
	startSendPrizeTime := config.GetConfig().GunActStartSendPrizeTime
	nowTime := time.Now().Unix()
	if startSendPrizeTime > uint32(nowTime) { // 未到发奖时间，不重发
		log.DebugContextf(ctx, "UpdateGunActTasksProc retrySendPrize not "+
			"reach send time. nowTime: %v, startSendPrizeTime: %v", nowTime, startSendPrizeTime)
		return
	}
	for _, activity := range activities {
		gameID := activity.GameId
		activityID := activity.ActivityId
		limit := p.getLimitNum(ctx)
		maxSendTimes := 3 // TODO 发奖重试10次
		// 获取发奖失败的任务
		tasks, err := df_activity_repo.GunActivityRepoClient.GetSendPrizeFailTasks(ctx, gameID, activityID,
			df_activity.GunTaskStatusGetPrizeFail, maxSendTimes, limit)
		if nil != err {
			log.ErrorContextf(ctx, "UpdateGunActTasksProc retrySendPrize get activity task error, "+
				"gameID: %s, activityID: %s, err: %v", gameID, activityID, err)
			continue
		}
		num = len(tasks)
		log.DebugContextf(ctx, "UpdateGunActTasksProc retrySendPrize gameID: %v, activityID: %v, taskNum: %v",
			gameID, activityID, num)
		if num >= limit {
			// TODO 告警
			log.ErrorContextf(ctx, "UpdateGunActTasksProc retrySendPrize too many tasks, num: %v", num)
		}
		// 发奖
		for _, task := range tasks {
			// TODO 监控告警
			log.DebugContextf(ctx, "UpdateGunActTasksProc retrySendPrize taskId: %v, sendPrizeTimes: %v",
				task.TaskId, task.SendPrizeTimes)
			// 重试发奖
			err = df_tmp_common.SendGunActTaskPrize(ctx, task)
			if nil != err {
				// TODO 告警
				log.ErrorContextf(ctx, "UpdateGunActTasksProc retrySendPrize send prize error, "+
					"gameID: %s, activityID: %s, task: %v, err: %v", gameID, activityID, task, err)
			}
		}
	}
}
