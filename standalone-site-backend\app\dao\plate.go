package dao

import (
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"trpc.publishing_application.standalonesite/app/model"
)

type PlateConditions struct {
	UniqueIdentifier     string
	UniqueIdentifierList []string
	Id                   int64
	Status               int
	Order                []*OrderConditions
	GameId               string
	AreaId               string
	GtId                 int64
}

func PlateGet(conditions *PlateConditions) (*model.Plate, error) {
	var plate model.Plate
	db := DB.SelectConnect("db_standalonesite").Table((&model.Plate{}).TableName())
	if conditions.UniqueIdentifier != "" {
		db = db.Where("unique_identifier = ?", conditions.UniqueIdentifier)
	}
	if conditions.Id > 0 {
		db = db.Where("id = ?", conditions.Id)
	}

	err := db.First(&plate).Error
	if err != nil {
		return &plate, err
	}

	return &plate, nil
}

func GetPlateList(conditions *PlateConditions, limit int) ([]*model.Plate, error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.Plate{}).TableName())
	if limit > 0 {
		db = db.Limit(limit)
	}

	if len(conditions.UniqueIdentifierList) > 0 {
		db = db.Where("unique_identifier in ?", conditions.UniqueIdentifierList)
	}
	if conditions.GtId > 0 {
		db = db.Where("id > ?", conditions.GtId)
	}
	if conditions.Status > 0 {
		db = db.Where("status = ?", conditions.Status)
	}
	if conditions.AreaId != "" {
		db = db.Where("area_id = ?", conditions.AreaId)
	}
	if conditions.GameId != "" {
		db = db.Where("game_id = ?", conditions.GameId)
	}
	if len(conditions.Order) > 0 {
		db = db.Order(AssembleOrder(conditions.Order))
	}
	var plate []*model.Plate
	err := db.Find(&plate).Error
	if err != nil {
		return nil, err
	}
	return plate, nil
}

func GetPlateListLanguage(plateId int) ([]*model.PlateLanguage, error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.PlateLanguage{}).TableName())
	var plateLangs []*model.PlateLanguage
	err := db.Where("plate_id = ? AND is_del = 0", plateId).Find(&plateLangs).Error
	if err != nil {
		return nil, err
	}
	return plateLangs, nil
}

func GetAllPlateListLanguage() ([]*model.PlateLanguage, error) {
	db := DB.SelectConnect("db_standalonesite").Table((&model.PlateLanguage{}).TableName())
	var plateLangs []*model.PlateLanguage
	err := db.Find(&plateLangs).Error
	if err != nil {
		return nil, err
	}
	return plateLangs, nil
}
