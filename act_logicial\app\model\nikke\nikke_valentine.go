package nikke

import "trpc.act.logicial/app/model"

// SendTempValentineModel TODO
type SendTempValentineModel struct {
	model.AppModel
}

// TableName .
func (SendTempValentineModel) TableName() string {
	return "nikke_valentine_send_temp"
}

// SendTempValentine 临时结构
type SendTempValentine struct {
	SendTempValentineModel
	ID          int64  `gorm:"type:int(11);column:id;primary_key"`
	UID         string `gorm:"type:varchar(32);column:uid;"`
	AccountType int32  `gorm:"type:smallint(6);column:account_type;0"`
	FsourceID   string `gorm:"type:varchar(255);column:Fsource_id;not null"`
	PresentID   string `gorm:"type:varchar(255);column:present_id;not null"`
	LangType    string `gorm:"type:varchar(64);column:lang_type;"`
	Tag         int    `gorm:"type:int(11);column:tag;"`
	Status      int    `gorm:"type:int(1);column:status;not null"`
}
