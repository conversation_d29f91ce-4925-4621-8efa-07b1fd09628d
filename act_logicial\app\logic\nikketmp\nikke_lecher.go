package nikketmp

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/httpclient"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	accountPb "git.code.oa.com/trpcprotocol/publishing_marketing/account"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	presentPb "git.code.oa.com/trpcprotocol/publishing_marketing/present"
	redisOrgin "github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/controller/lottery"
	"trpc.act.logicial/app/global"
	"trpc.act.logicial/app/logic/common"
	"trpc.act.logicial/app/model/nikke"
)

type MetricsRequestParam struct {
	Host      string
	ActId     string
	DimIdList []string
	GameId    string
	AreaId    int64
	SinKey    string
	Source    string
	OpenId    string
	PostData  map[string]interface{}
}

type MetricsRsp struct {
	Ret     int    `json:"ret"`
	Msg     string `json:"msg"`
	Metrics struct {
		C1 string `json:"c1"`
	} `json:"metrics"`
	Seq string `json:"seq"`
}
type DimensionsResponseInfo struct {
	Ret  int    `json:"ret"`
	Msg  string `json:"msg"`
	Data map[string]struct {
		Dimensions map[string]struct {
			Metrics MetricsType `json:"metrics"`
		} `json:"dimensions"`
	} `json:"data"`
}

type MetricsType struct {
	C1 string `json:"c1"`
}

type InfiniteLecherKillCacheData struct {
	CurrentKillNum int64 `json:"current_kill_num"` // 击杀数
	CurrentLeftNum int64 `json:"current_left_num"` // 剩余数
	LastKillNum    int64 `json:"last_kill_num"`
	LastLeftNum    int64 `json:"last_left_num"`
}

type UserItem struct {
	ClearanceTime     string `json:"clearance_time"`
	OnlineTimeNum     uint32 `json:"online_time_num"`
	CompletedNum      uint32 `json:"completed_num"`
	HasFinish         bool   `json:"has_finish"`
	FinishTimeKillNum int64  `json:"finish_time_kill_num"`
}

const LecherKillDataCacheKey = "nikke_lecher_update_kill_data_periodically"

// GetRawKillData 获取击杀数据
func GetRawKillData(ctx context.Context) (int64, error) {

	lecherKillConf := config.GetConfig().NikkeLecherKill
	// 获取接口中的最新击杀数据
	param := MetricsRequestParam{
		Host:      lecherKillConf.Host,
		ActId:     lecherKillConf.ActId,
		DimIdList: []string{"10002"},
		GameId:    "29080",
		SinKey:    lecherKillConf.SinKey,
		Source:    lecherKillConf.Source,
	}
	getData, err := SendRequestGetData(ctx, param)
	if err != nil {
		return 0, err
	}
	var metricsRsp MetricsRsp
	if err = json.Unmarshal([]byte(getData), &metricsRsp); err != nil {
		return 0, errs.NewCustomError(ctx, code.CommonJSONUnmarshalErr, "UpdateKillDataPeriodically Unmarshal err:[%v]", err.Error())
	}
	if metricsRsp.Ret != 0 {
		return 0, errs.NewCustomError(ctx, code.LecherGetKillDataPeriodicallyErr,
			"UpdateKillDataPeriodically LecherGetKillDataPeriodicallyErr; meg:[%v]", metricsRsp.Msg)
	}
	rawKillNum, err := cast.ToInt64E(metricsRsp.Metrics.C1)
	if err != nil {
		return 0, errs.NewCustomError(ctx, code.LecherKillNumDataErr,
			"UpdateKillDataPeriodically c1 ToInt64E err;C1:[%v], err:[%v]", metricsRsp.Metrics.C1, err)
	}

	return rawKillNum, nil
}

// UpdateKillDataPeriodically 定时更新击杀数据
func UpdateKillDataPeriodically(ctx context.Context, a1, a2 uint64, randomRange []float32) error {

	// 获取随机范围
	if len(randomRange) < 2 {
		return errs.NewCustomError(ctx, code.CommonDataError, "len randomRange < 2")
	}
	randomNumber := FormatAndGenerateRandomNumber(randomRange[0], randomRange[1])

	// 获取原始击杀数
	rawKillNum, err := GetRawKillData(ctx)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "error").Errorf("UpdateKillDataPeriodically GetRawKillData err:[%v]", err.Error())
	}

	// 获取数据库中最新的数据
	var infiniteLecherKillData nikke.NikkeInfiniteLecherKillData
	db := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeInfiniteLecherKillDataModel{}.TableName()).
		Order("id desc").Limit(1).Find(&infiniteLecherKillData)
	if err = db.Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"UpdateKillDataPeriodically db error, \t [Error]:{%v} ", err)
	}
	if rawKillNum == 0 && (infiniteLecherKillData.KillNum == 0 || infiniteLecherKillData.LeftNum == 0) {
		// 接口和数据库都无数据 返回错误
		return errs.NewCustomError(ctx, code.LecherDataEmpty, "UpdateKillDataPeriodically Data Empty")
	}

	// 测试mock用假数据
	//timestamp := time.Now().Unix()
	//suffix := timestamp % 1000000
	//rawKillNum = suffix * 100
	//number := FormatAndGenerateRandomNumber(34, 35)
	//killsPerMinute := int64(3 * 60 * number) // mock 每分钟击杀数

	var currentKillNum, currentLeftNum int64
	if rawKillNum != 0 {
		// 计算击杀数和剩余数
		//消灭数 = A1  + 每分钟 差值 *随机范围数
		//剩余数 = A2  - 消灭数
		if infiniteLecherKillData.KillNum == 0 {
			// 第一次获取
			currentKillNum = int64(a1) + int64(math.Ceil(float64(rawKillNum-infiniteLecherKillData.RawKillNum)*float64(randomNumber)))
		} else {
			currentKillNum = infiniteLecherKillData.KillNum + int64(math.Ceil(float64(rawKillNum-infiniteLecherKillData.RawKillNum)*float64(randomNumber)))
		}
		log.WithFieldsContext(ctx, "log_type", "info").Errorf(
			"UpdateKillDataPeriodically show rawKillNum:[%v],lastRawKillNum:[%v],randomNumber:[%v],currentKillNum:[%v]",
			rawKillNum, infiniteLecherKillData.RawKillNum, randomNumber, currentKillNum)

		currentLeftNum = int64(a2) - currentKillNum
		if currentLeftNum < 0 {
			currentLeftNum = 0
		}
	} else {
		// 使用上一次的数据
		currentKillNum, currentLeftNum, rawKillNum = infiniteLecherKillData.KillNum, infiniteLecherKillData.LeftNum, infiniteLecherKillData.RawKillNum
	}

	// 获取当前分钟时间戳
	minuteTimestamp := common.GetCurrentMinuteTimestamp()
	nowInfiniteLecherKillData := nikke.NikkeInfiniteLecherKillData{
		RawKillNum:    rawKillNum,
		KillNum:       currentKillNum,
		LeftNum:       currentLeftNum,
		CurrentMinute: minuteTimestamp,
	}
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeInfiniteLecherKillDataModel{}.TableName()).
		Create(&nowInfiniteLecherKillData).Error; err != nil {
		return errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"UpdateKillDataPeriodically Create db error, \t [Error]:{%v} ", err)
	}
	// 更新缓存
	cacheKey := global.GetRedisKey(LecherKillDataCacheKey)
	infiniteLecherKillCacheData := InfiniteLecherKillCacheData{
		CurrentKillNum: currentKillNum,
		CurrentLeftNum: currentLeftNum,
		LastKillNum:    infiniteLecherKillData.KillNum,
		LastLeftNum:    infiniteLecherKillData.LeftNum,
	}
	marshal, _ := json.Marshal(infiniteLecherKillCacheData)
	// 不设置过期
	redis.GetClient().Set(ctx, cacheKey, string(marshal), 0)
	return nil
}

func SendRequestGetData(ctx context.Context, param MetricsRequestParam) (string, error) {

	postData := GetMetricsPostData(param)
	param.PostData = postData
	url, err := GetMetricsURL(param)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "error").Errorf("SendRequestGetData err:[%v]", err.Error())
		return "", errs.NewCustomError(ctx, code.CommonJSONMarshalErr, "GetMetricsURL JSONMarshalErr; err:[%v]", err.Error())
	}

	optionOne := httpclient.ClientOption{
		URL: url,
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		Type:     "POST",
		PostData: postData,
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	if resultOption.RequestError != nil {
		// 请求失败
		return "", errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"http error, \t [Error]:{%v} ", url)
	}
	response := resultOption.Result
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "SendRequestGetData").Errorf(
		"SendRequestGetData success show rsp:[%v]", response)
	return response, nil
}

func GetMetricsURL(param MetricsRequestParam) (url string, err error) {
	channelID := channelIDMap[param.GameId]
	ts := time.Now().Unix()
	actId := param.ActId
	seq := actId + param.DimIdList[0] + gameIDMap[param.GameId] + strconv.FormatInt(ts, 10)
	postDataJSONString, err := json.Marshal(param.PostData)
	if err != nil {
		return "", err
	}
	sigStr := fmt.Sprintf(
		"%v%v%v%v%v%v%v%v%v",
		"/data/v1/general/metrics?",
		"channelid="+channelID,
		"&gameid="+param.GameId,
		"&os=0",
		"&seq="+seq,
		"&source="+param.Source,
		"&timezone=0",
		"&ts="+strconv.FormatInt(ts, 10),
		"&version=1.0",
	)
	sinKey := param.SinKey
	sig := lottery.GetMD5Encode(
		sigStr +
			string(postDataJSONString) +
			sinKey,
	)
	url = fmt.Sprintf(
		"%v%v%v",
		param.Host,
		sigStr,
		"&sig="+sig,
	)
	return
}

// GetMetricsPostData Metrics post请求参数
func GetMetricsPostData(param MetricsRequestParam) map[string]interface{} {

	// 预留可传openid
	metricsReqArr := make([]string, 1)
	cmds := make([]string, 1)
	bindings := make([]string, 0)
	metricsReqArr[0] = "c1"
	dimId := param.DimIdList[0]
	vopenid := param.OpenId
	if vopenid == "" {
		vopenid = "0"
	}
	izoneareaid := cast.ToString(param.AreaId)
	if izoneareaid == "" {
		izoneareaid = "0"
	}

	data := map[string]interface{}{
		"actid":       param.ActId,
		"gameName":    gameIDMap[param.GameId],
		"izoneareaid": izoneareaid,
		"vopenid":     vopenid,
		"vroleid":     "0",
		"dimid":       dimId,
		"interval":    "now",
		//"endInterval":"0", // optional, 如果interval是"day"，则代表时间跨度的截止时间（格式：unix时间戳）；如果interval是"now"，则可以不传
		"intervalAgo": "0",           // 如果有时间跨度的计算，则代表时间跨度（天数），如果没有跨度计算，则传0；intervalAgo最大值为30（时间跨度31天）
		"metrics":     metricsReqArr, // list of string, 代表所取的指标
		"cmds":        cmds,
		"bindings":    bindings,
	}
	return data
}

// GetDimensionsPostDataURL 获取dimensions的请求链接
func GetDimensionsPostDataURL(param MetricsRequestParam) (url string, err error) {
	channelID := channelIDMap[param.GameId]
	ts := time.Now().Unix()

	seq := param.ActId + strings.Join(param.DimIdList, "_") + gameIDMap[param.GameId] + strconv.FormatInt(ts, 10)
	postDataJSONString, err := json.Marshal(param.PostData)
	sigStr := fmt.Sprintf(
		"%v%v%v%v%v%v%v%v%v",
		"/data/v1/general/dimensions?",
		"channelid="+channelID,
		"&gameid="+param.GameId,
		"&os=0",
		"&seq="+seq,
		"&source="+param.Source,
		"&timezone=0",
		"&ts="+strconv.FormatInt(ts, 10),
		"&version=1.0",
	)

	sig := lottery.GetMD5Encode(
		sigStr +
			string(postDataJSONString) +
			param.SinKey,
	)
	url = fmt.Sprintf(
		"%v%v%v",
		param.Host,
		sigStr,
		"&sig="+sig,
	)
	return
}

// GetUserDimensionsPostData 获取Dimensions post请求参数
func GetUserDimensionsPostData(param MetricsRequestParam) (data map[string]interface{}) {

	metricsReqArr := make([]string, 1)
	cmds := make([]string, 1)
	metricsReqArr[0] = "c1"
	vopenid := param.OpenId
	if vopenid == "" {
		vopenid = "0"
	}
	izoneareaid := cast.ToString(param.AreaId)
	if izoneareaid == "" {
		izoneareaid = "0"
	}
	dimensionInfoMap := make(map[string]DimensionInfo)
	for _, v := range param.DimIdList {
		dimensionInfoMap[v] = DimensionInfo{
			Interval:    "now", // 1. "now"（代表最新数据，没有时间跨度）, 2. "day"（读取特定天数的数据）
			EndInterval: 0,
			IntervalAgo: 0,
			Metrics:     metricsReqArr,
			Cmds:        cmds,
		}
	}

	data = map[string]interface{}{
		"actid":    param.ActId,
		"gameName": gameIDMap[param.GameId],
		"users": []UserInfo{
			{
				Izoneareaid: izoneareaid,
				Vroleid:     "0",
				// Vopenid:     "268130863772397056",
				Vopenid: vopenid,
			},
		},
		"dimensions": dimensionInfoMap,
	}
	return
}

func FormatAndGenerateRandomNumber(min, max float32) float32 {

	if min > max {
		min, max = max, min
	}
	// 设置随机数种子
	rand.Seed(time.Now().UnixNano())
	// 生成 [0, 1) 之间的随机数
	random := rand.Float32()
	// 将随机数缩放到 [min, max) 之间
	result := min + random*(max-min)
	// 保留三位小数
	result = float32(math.Round(float64(result)*1000) / 1000)
	return result
}

// GetLecherKillItem 获取击杀数据
func GetLecherKillItem(ctx context.Context) (InfiniteLecherKillCacheData, error) {

	// 获取 缓存
	cacheKey := global.GetRedisKey(LecherKillDataCacheKey)
	infiniteLecherKillCacheData := InfiniteLecherKillCacheData{}
	// 不设置过期
	result, err := redis.GetClient().Get(ctx, cacheKey).Result()
	if err != nil && !errors.Is(err, redisOrgin.Nil) {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "GetLecherKillItem").Errorf(
			"GetLecherKillItem get cache err:[%v]", err)
	}
	if result != "" {
		_ = json.Unmarshal([]byte(result), &infiniteLecherKillCacheData)
	}
	if infiniteLecherKillCacheData.CurrentKillNum != 0 {
		return infiniteLecherKillCacheData, nil
	}

	// 获取数据库中最新的数据
	var infiniteLecherKillDataList []*nikke.NikkeInfiniteLecherKillData
	db := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeInfiniteLecherKillDataModel{}.TableName()).
		Order("id desc").Limit(2).Find(&infiniteLecherKillDataList)
	if err = db.Error; err != nil {
		return infiniteLecherKillCacheData, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"GetLecherKillItem db error, \t [Error]:{%v} ", err)
	}
	if len(infiniteLecherKillDataList) == 0 {
		// 缓存和数据库都无数据 返回错误
		return infiniteLecherKillCacheData, errs.NewCustomError(ctx, code.LecherDataEmpty, "GetLecherKillItem Data Empty")
	}
	if len(infiniteLecherKillDataList) == 1 {
		infiniteLecherKillCacheData.CurrentKillNum = infiniteLecherKillDataList[0].KillNum
		infiniteLecherKillCacheData.CurrentLeftNum = infiniteLecherKillDataList[0].LeftNum
	} else {
		infiniteLecherKillCacheData.CurrentKillNum = infiniteLecherKillDataList[0].KillNum
		infiniteLecherKillCacheData.CurrentLeftNum = infiniteLecherKillDataList[0].LeftNum
		infiniteLecherKillCacheData.LastKillNum = infiniteLecherKillDataList[1].KillNum
		infiniteLecherKillCacheData.LastLeftNum = infiniteLecherKillDataList[1].LeftNum
	}
	// 更新缓存
	marshal, _ := json.Marshal(infiniteLecherKillCacheData)
	// 不设置过期
	redis.GetClient().Set(ctx, cacheKey, string(marshal), 0)
	return infiniteLecherKillCacheData, nil
}

// GetKillLecherUserItem 获取用户击杀数据详情
func GetKillLecherUserItem(ctx context.Context, roleInfo *gamePb.RoleInfo) (*UserItem, error) {

	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}
	// 测试用mock数据
	//openId := "5324649538930218765"
	//openId := "8858734124808105617"
	//gameId := "29080"
	//areaId := int64(2)
	//areaId := int64(81)
	openId := userAccount.IntlAccount.OpenId
	gameId := roleInfo.GameId
	areaId := roleInfo.AreaId

	lecherKillConf := config.GetConfig().NikkeLecherKill
	// 获取接口中的最新击杀数据
	param := MetricsRequestParam{
		Host:      lecherKillConf.Host,
		ActId:     lecherKillConf.ActId,
		DimIdList: []string{"10003", "10004", "10005"},
		GameId:    gameId,
		AreaId:    areaId,
		SinKey:    lecherKillConf.SinKey,
		Source:    lecherKillConf.Source,
		OpenId:    openId,
	}

	postData := GetUserDimensionsPostData(param)
	param.PostData = postData
	url, err := GetDimensionsPostDataURL(param)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "GetKillLecherUserItem").Errorf(
			"GetKillLecherUserItem GetDimensionsPostDataURL err; param:[%v], err:[%v]", param, err)
		return nil, errs.NewCustomError(ctx, code.CommonJSONMarshalErr, "GetKillLecherUserPageItem JSONMarshalErr")
	}

	optionOne := httpclient.ClientOption{
		URL: url,
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		Type:     "POST",
		PostData: postData,
	}
	resultOption := httpclient.RequestOne(ctx, optionOne)
	if resultOption.RequestError != nil {
		// 请求失败
		err = errs.NewSystemError(ctx, errs.ErrorTypeHttp, code.PubgHttpError,
			"http error, \t [Error]:{%v} ", url)
		return nil, err
	}
	response := resultOption.Result
	log.WithFieldsContext(ctx, "log_type", "debug", "m", "GetKillLecherUserItem",
		"OpenId", openId).Errorf(
		"GetKillLecherUserItem show response; response:[%v]", response)
	var respData DimensionsResponseInfo
	err = json.Unmarshal([]byte(response), &respData)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "GetKillLecherUserItem").Errorf(
			"GetKillLecherUserItem response Unmarshal err;response[%v] err:[%v]", response, err)
		//return nil, err
	}

	if respData.Ret != 0 {
		log.WithFieldsContext(ctx, "log_type", "err", "m", "GetKillLecherUserPageItem").Errorf(
			"GetKillLecherUserPageItem ret err; param:[%v], respData:[%v]", param, respData)
		return nil, errs.NewCustomError(ctx, code.LecherDataEmpty, "User Page Item Get User Data err")
	}
	userKey := fmt.Sprintf(
		"%v%v%v",
		openId+"|",
		"0|",
		strconv.FormatInt(areaId, 10),
	)

	// 通关活动的时间
	var hasFinish bool
	var finishTimeKillNum int64
	clearanceTime := respData.Data[userKey].Dimensions["10003"].Metrics.C1
	if err = common.ValidateDateTimeFormat(clearanceTime); err != nil {
		log.WithFieldsContext(ctx, "log_type", "warn", "m", "GetKillLecherUserPageItem").Errorf(
			"GetKillLecherUserPageItem ValidateDateTimeFormat err; clearanceTime:[%v]", clearanceTime)
		clearanceTime = ""
		// 获取最新消灭莱彻数
		var killItem InfiniteLecherKillCacheData
		killItem, err = GetLecherKillItem(ctx)
		if err != nil {
			return nil, err
		}
		finishTimeKillNum = killItem.CurrentKillNum
	} else {
		hasFinish = true
		// 获取对应分钟消灭的莱彻数
		minuteTimestamp, _ := common.ConvertToMinuteTimestamp(clearanceTime)
		db := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeInfiniteLecherKillDataModel{}.TableName()).
			Select("kill_num").Where("current_minute = ?", minuteTimestamp).
			Limit(1).Find(&finishTimeKillNum)
		if err = db.Error; err != nil {
			return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"GetKillLecherUserPageItem db error, \t [Error]:{%v} ", err)
		}
	}
	// 累积在线时间(分钟）
	onlineTime := respData.Data[userKey].Dimensions["10004"].Metrics.C1
	onlineTimeNum, err := cast.ToUint32E(onlineTime)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "warn", "m", "GetKillLecherUserPageItem").Errorf(
			"GetKillLecherUserPageItem onlineTime err; onlineTime:[%v]", onlineTime)
	}
	// 通关的关卡数
	completedNumStr := respData.Data[userKey].Dimensions["10005"].Metrics.C1
	completedNum, err := cast.ToUint32E(completedNumStr)
	if err != nil {
		log.WithFieldsContext(ctx, "log_type", "warn", "m", "GetKillLecherUserPageItem").Errorf(
			"GetKillLecherUserPageItem completedNumStr err; completedNumStr:[%v]", completedNumStr)
	}

	return &UserItem{
		ClearanceTime:     clearanceTime,
		OnlineTimeNum:     onlineTimeNum,
		CompletedNum:      completedNum,
		HasFinish:         hasFinish,
		FinishTimeKillNum: finishTimeKillNum,
	}, nil
}

// LecherShareRecordGift 无限莱彻分享礼包记录
func LecherShareRecordGift(ctx context.Context, presentId string, roleInfo *gamePb.RoleInfo) (bool, error) {
	var isFirst bool
	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return isFirst, err
	}
	langType := metadata.GetLangType(ctx)
	// 是否已获取礼包
	tx := DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeInfiniteLecherShareGiftRecordModel{}.TableName()).
		Where("uid = ? and account_type = ?", userAccount.Uid, int(userAccount.AccountType)).
		Where("present_id = ?", presentId)
	var count int64
	if err = tx.Count(&count).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return isFirst, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"LecherShareRecordGift Count db error, \t [Error]:{%v} ", err)
	}
	if count != 0 {
		return isFirst, nil
	}
	// 首次记录
	isFirst = true
	// 记录礼包信息
	info := buildRoleInfo(roleInfo)
	roleInfoStr, err := json.Marshal(info)
	if err != nil {
		return isFirst, errs.NewCustomError(ctx, code.CommonParamJsonError, fmt.Sprintf("roleInfo json Marshal error, err:[%v]", err))
	}
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeInfiniteLecherShareGiftRecordModel{}.TableName()).
		Create(&nikke.NikkeInfiniteLecherShareGiftRecord{
			UID:         userAccount.Uid,
			AccountType: int16(userAccount.AccountType),
			RoleInfo:    string(roleInfoStr),
			PresentID:   presentId,
			LangType:    langType,
			Status:      0, // 未发货
		}).Error; err != nil {
		return isFirst, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"LecherShareRecordGift Create db error, \t [Error]:{%v} ", err)
	}
	return isFirst, nil

}

// ScheduledSendLecherShareGift 无限莱彻定时发送分享礼包
func ScheduledSendLecherShareGift(oldCtx context.Context) (err error) {
	scheduleCtx := context.Background()
	tableName := nikke.NikkeInfiniteLecherShareGiftRecordModel{}.TableName()
	// 获取tag下待发货的数据
	condition := map[string]interface{}{
		"status": 0,
	}
	var totalRecords int64
	countdb := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).
		Where(condition).Count(&totalRecords)
	if countdb.Error != nil {
		err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"ScheduledSendLecherShareGift db error, \t [Error]:{%v} ", countdb.Error.Error())
		return
	}
	log.WithFieldsContext(scheduleCtx, "log_type", "debug", "m", cast.ToString(totalRecords)).
		Infof(fmt.Sprintf("ScheduledSendLecherShareGift:[%v]", totalRecords))
	if totalRecords == 0 {
		return
	}

	// 分页 每页200条
	pageSize := 200
	totalPages := int(math.Ceil(float64(totalRecords) / float64(pageSize)))

	var wg sync.WaitGroup
	sendProxy := presentPb.NewPresentClientProxy()
	for pageNumber := 1; pageNumber <= totalPages; pageNumber++ {
		offset := (pageNumber - 1) * pageSize
		var logData = []nikke.NikkeInfiniteLecherShareGiftRecord{}
		db := DB.DefaultConnect().Debug().WithContext(scheduleCtx).Table(tableName).Where(condition).Offset(offset).Limit(pageSize).
			Order("id asc").
			Find(&logData)
		if db.Error != nil {
			err = errs.NewSystemError(scheduleCtx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"NikkeInfiniteLecherShareGiftRecord Find db error, \t [Error]:{%v} ", db.Error.Error())
			return
		}
		for _, v := range logData {
			wg.Add(1)
			go func(data nikke.NikkeInfiniteLecherShareGiftRecord) {
				newCtx := context.Background()
				openID := strings.Split(data.UID, "-")[1]
				accountData, _ := proto.Marshal(&accountPb.UserAccount{
					Uid:         data.UID,
					AccountType: accountPb.AccountType(data.AccountType),
					IntlAccount: &accountPb.IntlAccount{
						OpenId:    openID,
						ChannelId: 3,
					},
				})
				callopts := []client.Option{
					client.WithMetaData(metadata.UserAccount, accountData),
					client.WithMetaData(metadata.LangType, []byte(data.LangType)),
				}
				// 获取角色信息
				var gameRoleInfo *gamePb.RoleInfo
				if data.RoleInfo == "" {
					err = errs.NewCustomError(newCtx, code.CurrentUserRoleAbnormal, "ScheduledSendLecherShareGift CurrentUserRoleAbnormal err, data:[%v]", data)
				} else {
					gameRoleInfo, err = buildPBRoleInfo(data.RoleInfo)
				}
				if err == nil {
					log.WithFieldsContext(newCtx, "log_type", "debug").Infof(fmt.Sprintf("ScheduledSendLecherShareGift dataInfo: %#v", data))
					// 发送礼包
					presentMap := map[string]string{
						"Wand-**************-Pfb026da764d1": "pageV3-1843", // 国际-莱彻击杀分享
						"Wand-**************-Pb9853b44e92b": "pageV3-1844", // 港澳台-莱彻击杀分享
						"Wand-**************-P2423605c7a5b": "pageV3-1843", // 国际-莱彻登录
						"Wand-**************-P8ae465e052c5": "pageV3-1844", // 港澳台-莱彻登录
					}
					sendReq := &presentPb.SendPresentReq{
						FsourceId: presentMap[data.PresentID],
						PresentId: data.PresentID,
						RoleInfo:  gameRoleInfo,
					}
					accountData, _ = proto.Marshal(&accountPb.UserAccount{
						Uid:         data.UID,
						AccountType: accountPb.AccountType(data.AccountType),
						IntlAccount: &accountPb.IntlAccount{
							OpenId:    openID,
							GameId:    gameRoleInfo.GameId,
							ChannelId: 3,
						},
					})
					callopts = []client.Option{
						client.WithMetaData(metadata.UserAccount, accountData),
						client.WithMetaData(metadata.LangType, []byte(data.LangType)),
					}
					_, sendErr := sendProxy.SendPresent(newCtx, sendReq, callopts...)
					action := "ScheduledSendLecherShareGift_action"
					if sendErr == nil {
						updates := map[string]interface{}{
							"status":     1,
							"created_at": time.Now().Unix(),
						}
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// 记录日志
						log.WithFieldsContext(newCtx, "log_type", "debug", "action", action).Infof(fmt.Sprintf(
							"ScheduledSendLecherShareGift ReportTlog succ: action:[%v],AreaId:[%v],GameId:[%v],openID:[%v],PresentID:[%v]",
							action, gameRoleInfo.AreaId, gameRoleInfo.GameId, openID, data.PresentID))
					} else {
						log.WithFieldsContext(newCtx, "log_type", "err", "action", action).Infof(fmt.Sprintf(
							"ScheduledSendLecherShareGift ReportTlog fail: action:[%v],AreaId:[%v],GameId:[%v],openID:[%v],PresentID:[%v]",
							action, gameRoleInfo.AreaId, gameRoleInfo.GameId, openID, data.PresentID))
					}
					delErr := errs.ParseError(newCtx, sendErr)
					if delErr.Code == 400018 || delErr.Code == 400042 {
						updates := map[string]interface{}{
							"status":     2,
							"created_at": time.Now().Unix(),
						}
						log.WithFieldsContext(newCtx, "log_type", "debug", "err_code", cast.ToString(delErr.Code)).Infof(fmt.Sprintf("[ScheduledSendLecherShareGift SendPresent] service err222:%v",
							sendErr.Error()))
						// 如果是已发货 兼容处理
						DB.DefaultConnect().WithContext(newCtx).Table(tableName).Where("id", data.ID).Updates(updates)
						// }
					}
					log.WithFieldsContext(newCtx, "log_type", "debug", "err_code", cast.ToString(delErr.Code)).Infof(fmt.Sprintf("[ScheduledSendLecherShareGift SendPresent] service err:%v", sendErr))
					// 修改发货状态
				} else {
					log.WithFieldsContext(newCtx, "log_type", "err").Infof(fmt.Sprintf("ScheduledSendLecherShareGift GetRoleInfo] service err:%v", err))
				}
				wg.Done()
			}(v)
		}
		wg.Wait()
	}
	return
}

func NikkeLecherUserGiftCollectionRecord(ctx context.Context) ([]string, error) {

	// 获取用户信息
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return nil, err
	}

	// 获取当前用户已获取礼包
	var giftDistributionRecord []string
	sel := "present_id"
	if err = DB.DefaultConnect().WithContext(ctx).Table(nikke.NikkeInfiniteLecherShareGiftRecordModel{}.TableName()).Select(sel).
		Where("uid = ? AND account_type = ?", userAccount.Uid, int32(userAccount.AccountType)).
		Find(&giftDistributionRecord).Error; err != nil && err.Error() != gorm.ErrRecordNotFound.Error() {
		return nil, errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"NikkeLecherUserGiftCollectionRecord find db error, \t Uid:[%v],AccountType:[%v],[Error]:{%v}", userAccount.Uid, userAccount.AccountType, err)
	}

	return giftDistributionRecord, nil
}
