syntax = "proto3";

// package 内容格式推荐为 trpc.{app}.{server}，以 trpc 为固定前缀，标识这是一个 trpc 服务协议，app 为你的应用名，server 为你的服务进程名
package trpc.pytest.testdemo;

// 注意：这里 go_package 指定的是协议生成文件 pb.go 在 git 上的地址，不要和上面的服务的 git 仓库地址一样
option go_package="git.woa.com/trpcprotocol/pytest/testdemo_logic";

import "publishing_application/standalonesite/dynamics.proto";


// GetPost 请求参数
message GetPostDataReq {
  int32 type = 1;
  string name = 2;
  string tag = 3;
  trpc.publishing_application.standalonesite.PageInfo page_info = 4;
}


// GetPost 响应参数
message GetPostDataRsp {
  int32 count = 1;
  string msg = 2;
  repeated trpc.publishing_application.standalonesite.RepliesItem list = 3;
}


// 定义服务接口
service Logic {
  rpc GetPost(GetPostDataReq) returns (GetPostDataRsp) {}
}
