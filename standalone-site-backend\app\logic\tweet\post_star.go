package tweet

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"trpc.publishing_application.standalonesite/app/common"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/logic/writemessage"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/dao"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	hotService "trpc.publishing_application.standalonesite/app/logic/hot"
	"trpc.publishing_application.standalonesite/app/model"
)

func UserStarPost(c context.Context, postUUID string, starType int64, userIntlOpenid, gameId, areaId string, postStarLikeType constants.PostLikeTye) (bool, *model.PostStar, error) {
	if postUUID == "" {
		return false, nil, errs.NewCustomError(c, code.PoststarEmptyPostError, "Abnormal like operation and abnormal post information query.")
	}
	// 限制频繁操作
	limitRedisKey := cache.GetUserStarPostLimitKey(userIntlOpenid, postUUID)
	if ok, err := redis.GetClient().SetNX(c, limitRedisKey, 1, 2*time.Minute).Result(); ok {
		defer redis.GetClient().Del(context.Background(), limitRedisKey)
		status := false

		// 先查询帖子是否存在
		post, err := dao.GetPost(postUUID)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserStarPost GetPost err: %v, postUUID is %s, userID is %s", err, postUUID, userIntlOpenid)
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return false, nil, errs.NewCustomError(c, code.PoststarPostNotExistsError, "Abnormal like operation and abnormal post information query.")
			}
			return false, nil, errs.NewCustomError(c, code.PoststarPostQueryError, "Abnormal like operation and abnormal post information query.")
		}

		upvoteMap, err := GetPostUpvoteMapInfo(c, postUUID)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserStarPost GetPostUpvoteMapInfo err: %v, postUUID is %s, userID is %s", err, postUUID, userIntlOpenid)
			return status, nil, errs.NewCustomError(c, code.GetPostUpvoteMapError, "UserStarPost | Failed to obtain dynamic upvote information.")
		}

		// 先查询是否有点赞记录
		star, err := GetPostStar(postUUID, userIntlOpenid, false)
		if err != nil || (star.Model != nil && star.ID == 0) {
			if err.Error() != "record not found" {
				log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserStarPost GetPostStar err: %v, postUUID is %s, userID is %s", err, postUUID, userIntlOpenid)
				return status, nil, errs.NewCustomError(c, code.GetPostStarFailed, "UserStarPost | Failed to obtain dynamic like information.")
			}
			// 没有点赞记录，则创建Star
			status = true
			star, err = CreatePostStar(c, postUUID, starType, userIntlOpenid, gameId, areaId, upvoteMap)
			if err != nil {
				if err.Error() == "NotReview" {
					return status, nil, errs.NewCustomError(c, code.PostNotReviewStarFailed, "UserStarPost | The dynamic content has not been reviewed and the likes failed.")
				}
				return status, nil, errs.NewCustomError(c, code.CreatePostStarFailed, "UserStarPost | Failed to like the post")
			}
		} else {
			// 已有记录
			star.Post = post
			if star.IsDel == 0 {
				// 取消点赞的标识, 如果是取消点赞那么就要把当前传递过来的type替换成上一次表态的type
				if postStarLikeType == constants.PostStarLike {
					starType = star.StarType
				}
				if star.StarType == starType {
					// 要从已点赞变成不点赞
					star, err = DeletePostStar(c, star, starType, upvoteMap)
					if err != nil {
						return status, nil, errs.NewCustomError(c, code.DeletePostStarFailed, "UserStarPost | Cancel post like operation failed")
					}
				} else {
					// 从一种点赞类型切换成另一种点赞类型
					status = true
					star, err = ChangePostStar(c, star, starType, upvoteMap)
					if err != nil {
						return status, nil, errs.NewCustomError(c, code.DeletePostStarFailed, "UserStarPost | Cancel post like operation failed")
					}
				}
			} else {
				// 从不点赞变成点赞
				status = true
				star, err = PostStarRecordRestar(c, star, starType, upvoteMap, gameId, areaId, userIntlOpenid)
				if err != nil {
					return status, nil, errs.NewCustomError(c, code.DeletePostStarFailed, "UserStarPost | Cancel post like operation failed")
				}

			}
		}
		return status, star, nil
	} else {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("UserStarPost redis return error: %v", err)
		return false, nil, errs.NewCustomError(c, code.UserStarPostFrequentLimit, "UserStarPost | Limitations on frequent user operations for liking dynamic content.")
	}
}

func GetPostStar(postUUID string, userIntlOpenid string, isNeedDel bool) (*model.PostStar, error) {
	return dao.PostStarGet(postUUID, userIntlOpenid, isNeedDel)
}

func PostStarRecordRestar(c context.Context, star *model.PostStar, starType int64, upvoteMap map[int64]int64, gameId, areaId, intlOpenid string) (*model.PostStar, error) {
	err := dao.PostStarRecordRestar(star.ID, starType)
	if err != nil {
		return star, err
	}
	if star.Post == nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostStarRecordRestar | The post gone, postUUID is %s", star.PostUUID)
		return nil, errs.NewCustomError(c, code.GetPostInfoByUUIDError, "PostStarRecordRestar | The post gone.")
	}
	// 加载Post
	post := star.Post

	postStats, err := dao.GetPostStatsByPostUuid(star.PostUUID)
	if err != nil {
		return star, err
	}

	//// 私密post不可操作
	//if post.Visibility == constants.PostVisitPrivate {
	//	return star, errors.New("no permision")
	//}

	// 更新Post点赞数
	//post.UpvoteCount--
	//if post.UpvoteCount < 0 {
	//	post.UpvoteCount = 0
	//}
	//dao.UpdatePost(post)
	if _, ok := upvoteMap[starType]; !ok {
		upvoteMap[starType] = 1
	} else {
		upvoteMap[starType]++
	}
	if len(upvoteMap) == 1 && upvoteMap[1] == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostStarRecordRestar upvoteMap set0000: star:%v,starType:%d, intlOpenid:%s", star, starType, intlOpenid)
	}
	if err = dao.PostStatsUpvoteCountUpdate(star.PostUUID, upvoteMap); err != nil {
		go func(ctx context.Context, intlGameId string) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportPostUpVoteLog(c, intlOpenid, post, nil, true, gameId, err)
		}(c, gameId)
	}
	go func(ctx context.Context, intlGameId string) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportPostUpVoteLog(c, intlOpenid, post, nil, true, gameId, nil)
	}(c, gameId)

	// 更新动态的es信息
	var upvoteCount int64
	if len(upvoteMap) > 0 {
		for _, upvoteI := range upvoteMap {
			upvoteCount += upvoteI
		}
	}

	upvoteMapJson, err := json.Marshal(upvoteMap)
	if err != nil {
		return star, errs.NewCustomError(c, code.UpdateDynamicUpvoteCountJsonFailed, "PostStarRecordRestar | update post content upvote failed")
	}

	hotNum := hotService.CalculatingPostHotNum(postStats)
	doc := map[string]interface{}{
		"upvote_map":   string(upvoteMapJson),
		"upvote_count": upvoteCount,
		"hot_num":      hotNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, star.PostUUID, doc)
	// 更新用户发布的动态数点赞数
	userState, err := dao.GetUserStateByUserOpenid(post.IntlOpenid)
	if err != nil {
		return star, err
	}
	userState.PostStarNum++
	if userState.PostStarNum < 0 {
		userState.PostStarNum = 0
	}
	// gameidStr=8是黎明觉醒游戏，目前拉取的是网红的数据，不是用户发的，所以不用提醒
	if err == nil && userState.IntlOpenid != intlOpenid && gameId != "8" {
		go writemessage.SetUserMessage(&model.Message{
			Type:                   constants.MsgTypeStar,
			PostUUID:               post.PostUUID,
			GameID:                 gameId,
			AreaID:                 areaId,
			SenderUserIntlOpenid:   intlOpenid,
			ReceiverUserIntlOpenid: userState.IntlOpenid,
		}, userState.IntlOpenid, constants.LikeMessageCount)
	}
	dao.UpdateUserPostStarNum(userState.IntlOpenid, userState.PostStarNum)
	star.Post = post
	return star, nil

}

func ChangePostStar(c context.Context, star *model.PostStar, starType int64, upvoteMap map[int64]int64) (*model.PostStar, error) {
	err := dao.PostStarRecordRestar(star.ID, starType)
	if err != nil {
		return star, err
	}
	// 加载Post
	// post := star.Post

	//// 私密post不可操作
	//if post.Visibility == constants.PostVisitPrivate {
	//	return star, errors.New("no permision")
	//}

	// 更新Post点赞数
	//post.UpvoteCount--
	//if post.UpvoteCount < 0 {
	//	post.UpvoteCount = 0
	//}
	//dao.UpdatePost(post)
	// star有值代表已经表态了，如果upvoteMap中没有类型1的值那就是取消点赞
	if _, ok := upvoteMap[starType]; !ok {
		upvoteMap[starType] = 1
	} else {
		upvoteMap[starType]++
	}
	if _, ok := upvoteMap[star.StarType]; !ok {
		upvoteMap[star.StarType] = 0
	} else {
		upvoteMap[star.StarType]--
	}
	if len(upvoteMap) == 1 && upvoteMap[1] == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("PostStarRecordRestar upvoteMap set0000: star:%v,starType:%d", star, starType)
	}
	if err = dao.PostStatsUpvoteCountUpdate(star.PostUUID, upvoteMap); err != nil {
		return star, errs.NewCustomError(c, code.UpdateDynamicUpvoteCountFailed, "PostStarRecordRestar | update post content upvote failed")
	}

	// todo 更新索引
	//doc := map[string]interface{}{
	//	"id":           post.PostUUID,
	//	"upvote_count": post.UpvoteCount,
	//}
	//dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, fmt.Sprintf("%d", post.PostUUID), doc)

	return star, nil

}

func CreatePostStar(c context.Context, postUUID string, starType int64, userIntlOpenid, gameId, areaId string, upvoteMap map[int64]int64) (*model.PostStar, error) {
	// 加载Post
	star := &model.PostStar{}
	post, err := dao.GetPost(postUUID)
	if err != nil {
		return star, errs.NewCustomError(c, code.GetPostsFailed, "CreatePostStar | get post failed")
	}

	//// 私密post不可操作
	// if post.Visibility == int32(constants.PostVisitPrivate) {
	// 	return star, errors.New("no permision")
	// }

	// 如果是自己发布的动态可以忽略待审核
	if post.IsAudit != 1 && post.IntlOpenid != userIntlOpenid {
		return star, errors.New("NotReview")
	}
	star.PostUUID = postUUID
	star.IntlOpenid = userIntlOpenid
	star.StarType = starType
	err = dao.PostStarCreate(star)
	if err != nil {
		go func(ctx context.Context, intlGameId string) {
			newC := trpc.CloneContext(ctx)
			defer recovery.CatchGoroutinePanic(newC)
			common.ReportPostUpVoteLog(c, userIntlOpenid, post, nil, true, gameId, err)
		}(c, gameId)
		return star, err
	}

	if _, ok := upvoteMap[starType]; !ok {
		upvoteMap[starType] = 1
	} else {
		upvoteMap[starType]++
	}
	go func(ctx context.Context, intlGameId string) {
		newC := trpc.CloneContext(ctx)
		defer recovery.CatchGoroutinePanic(newC)
		common.ReportPostUpVoteLog(c, userIntlOpenid, post, nil, true, gameId, nil)
	}(c, gameId)

	if len(upvoteMap) == 1 && upvoteMap[1] == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("CreatePostStar upvoteMap set0000: star:%v,starType:%d, intlOpenid:%s", star, starType, userIntlOpenid)
	}
	if err = dao.PostStatsUpvoteCountUpdate(postUUID, upvoteMap); err != nil {
		return star, errs.NewCustomError(c, code.UpdateDynamicUpvoteCountFailed, "CreatePostStar | update post stats upvote failed")
	}
	// 创建用户消息提醒
	userState, err := dao.GetUserStateByUserOpenid(post.IntlOpenid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 兼容用户没有数据的时候
			userState = &model.UserState{
				IntlOpenid:  post.IntlOpenid,
				FansNum:     0,
				FollowNum:   0,
				AllPostNum:  0,
				PostStarNum: 0,
				PostNum:     0,
			}
			dao.CreateEmptyUserState(post.IntlOpenid)

		} else {
			log.WithFieldsContext(c, "log_ty")
			return star, errs.NewCustomError(c, code.GetUserStateError, "get user state failed!")
		}
	}
	// gameidStr=8是黎明觉醒游戏，目前拉取的是网红的数据，不是用户发的，所以不用提醒
	if err == nil && userState.IntlOpenid != userIntlOpenid && gameId != "8" {
		go writemessage.SetUserMessage(&model.Message{
			Type:                   constants.MsgTypeStar,
			PostUUID:               postUUID,
			GameID:                 gameId,
			AreaID:                 areaId,
			SenderUserIntlOpenid:   userIntlOpenid,
			ReceiverUserIntlOpenid: userState.IntlOpenid,
		}, userState.IntlOpenid, constants.LikeMessageCount)
	}

	// 更新索引
	upvoteMapJson, err := json.Marshal(upvoteMap)
	if err != nil {
		return star, errs.NewCustomError(c, code.UpdateDynamicUpvoteCountJsonFailed, "CreatePostStar | update post content upvote failed")
	}
	var upvoteCount int64
	if len(upvoteMap) > 0 {
		for _, upvoteI := range upvoteMap {
			upvoteCount += upvoteI
		}
	}
	postStats, err := dao.GetPostStatsByPostUuid(postUUID)
	if err != nil {
		return star, err
	}
	go func() {
		defer recovery.CatchGoroutinePanic(context.Background())
		hotNum := hotService.CalculatingPostHotNum(postStats)
		doc := map[string]interface{}{
			"upvote_map":   string(upvoteMapJson),
			"upvote_count": upvoteCount,
			"hot_num":      hotNum,
		}
		// 更新所有语言下的动态
		dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)

	}()

	// 更新用户发布的动态数点赞数
	userState.PostStarNum++
	dao.UpdateUserPostStarNum(userState.IntlOpenid, userState.PostStarNum)

	star.Post = post
	return star, nil
}

func DeletePostStar(c context.Context, star *model.PostStar, starType int64, upvoteMap map[int64]int64) (*model.PostStar, error) {
	err := dao.PostStarDelete(star.ID)
	if err != nil {
		return star, err
	}
	// 加载Post
	post := star.Post

	if _, ok := upvoteMap[starType]; !ok {
		upvoteMap[starType] = 0
	} else {
		upvoteMap[starType]--
		if upvoteMap[starType] < 0 {
			upvoteMap[starType] = 0
		}
	}
	if len(upvoteMap) == 1 && upvoteMap[1] == 0 {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("DeletePostStar upvoteMap set0000: star:%v,starType:%d", star, starType)
	}
	if err = dao.PostStatsUpvoteCountUpdate(star.PostUUID, upvoteMap); err != nil {
		return star, errs.NewCustomError(c, code.UpdateDynamicUpvoteCountFailed, "DeletePostStar | update post content upvote failed")
	}

	// todo 更新索引
	// todo 更新索引
	upvoteMapJson, err := json.Marshal(upvoteMap)
	if err != nil {
		return star, errs.NewCustomError(c, code.UpdateDynamicUpvoteCountJsonFailed, "DeletePostStar | update post content upvote failed")
	}
	var upvoteCount int64
	if len(upvoteMap) > 0 {
		for _, upvoteI := range upvoteMap {
			upvoteCount += upvoteI
		}
	}
	postStats, err := dao.GetPostStatsByPostUuid(star.PostUUID)
	if err != nil {
		return star, err
	}
	hotNum := hotService.CalculatingPostHotNum(postStats)
	doc := map[string]interface{}{
		"upvote_map":   string(upvoteMapJson),
		"upvote_count": upvoteCount,
		"hot_num":      hotNum,
	}
	dao.EsUpdateDoc(config.GetConfig().ElasticSearchSetting.TweetIndex, post.PostUUID, doc)

	// 更新用户发布的动态数点赞数
	userState, err := dao.GetUserByIntlOpenid(post.IntlOpenid)
	if err != nil {
		return star, err
	}
	userState.PostStarNum--
	if userState.PostStarNum < 0 {
		userState.PostStarNum = 0
	}
	dao.UpdateUserPostStarNum(userState.IntlOpenid, userState.PostStarNum)
	star.Post = post
	return star, nil
}

func GetPostListMyIsUpvote(c context.Context, userIntlOpenid string, postUUIDs []string, posts []*pb.GetPostRsp) error {
	if len(postUUIDs) > 0 {
		postsStars, err := dao.PostStarList(&dao.PostStarConditions{
			IntlOpenid: userIntlOpenid,
			PostUuids:  postUUIDs,
		}, 0, 0)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("service.GetPostListMyIsUpvote err: %v\n", err)
			return errs.NewCustomError(c, code.GetPostStarFailed, "GetPostListMyIsUpvote | Failed to obtain dynamic like information.")
		}
		for _, post := range posts {
			for _, star := range postsStars {
				if post.PostUuid == star.PostUUID {
					post.MyUpvote = &pb.MyUpvote{
						IsStar:     true,
						UpvoteType: int32(star.StarType),
					}
				}
			}
		}
	}
	return nil
}

func GetPostUpvoteMapInfo(c context.Context, postUUID string) (map[int64]int64, error) {
	upvoteMap := make(map[int64]int64)
	postStats, err := dao.GetPostStatsByPostUuid(postUUID)
	if err != nil {
		log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostUpvoteMapInfo err,get post stats error, post_uuid:(%s), err=(%v)", postUUID, err)
		return nil, errs.NewCustomError(c, code.GetPostContentsFailed, "GetPostUpvoteMapInfo | Failed to obtain post content information.")
	}
	if postStats.UpvoteMap != "" {
		err := json.Unmarshal([]byte(postStats.UpvoteMap), &upvoteMap)
		if err != nil {
			log.WithFieldsContext(c, "log_type", constants.LogType_Standalonesite).Errorf("GetPostUpvoteMapInfo UpvoteMap json.Unmarshal err: %v, postUUID is %s", err, postUUID)
			return nil, errs.NewCustomError(c, code.PostUpvoteMapParseError, "GetPostUpvoteMapInfo | Failed to parse upvote map body.")
		}
	}

	return upvoteMap, nil
}

func ChangeEsPostVote(c context.Context, postUuid string, upvoteMap map[int64]int64) {
	var upvoteCount int64
	if len(upvoteMap) > 0 {
		for _, upvoteI := range upvoteMap {
			upvoteCount += upvoteI
		}
	}
}
