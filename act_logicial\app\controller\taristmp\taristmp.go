// Package taristmp 塔瑞斯临时活动
package taristmp

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/redis"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/datadump"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	gamePb "git.code.oa.com/trpcprotocol/publishing_marketing/game"
	pb "git.woa.com/trpcprotocol/publishing_marketing/logicial_taristmp"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/global"
	"trpc.act.logicial/app/logic/taristmp"
)

// TaristmpImpl TODO
type TaristmpImpl struct {
	pb.UnimplementedTaristmp
}

// GetRank 获取开服竞速排行榜列表
func (s *TaristmpImpl) GetRank(
	ctx context.Context,
	req *pb.GetRankReq,
) (*pb.GetRankRsp, error) {
	rsp := &pb.GetRankRsp{
		RankList: make([]*pb.RankList, 0),
		SelfMsg:  &pb.SelfMsg{},
		Total:    0,
	}

	redisPrefixKey := global.GetPrefix()
	hashKey := fmt.Sprintf("%v-taris-rank-page-26622", redisPrefixKey)
	result, err := redis.GetClient().HGetAll(ctx, hashKey).Result()
	if err != nil {
		return rsp, err
	}

	index := 1
	if req.FsourceId == "page-26627" {
		index = 0
	}
	if req.FsourceId == "page-26635" {
		index = 2
	}
	areaIDMap := config.GetConfig().TarisAreaIDMap

	dataKey := fmt.Sprintf("%v-%v-%v-%v", areaIDMap[index], req.InstanceId, req.PageSize,
		req.PageNum)

	if _, ok := result[dataKey]; ok {

		err = json.Unmarshal([]byte(result[dataKey]), &rsp)
		if err != nil {
			// 告警
			errs.NewCustomError(ctx, 611010, "redis get err,redisKey=%v,val=%v,err=%v", dataKey,
				result[dataKey],
				err)
			return rsp, err
		}
	} else {
		// 如果没有则查询然后加入到redis中
		if index == 2 {
			if req.InstanceId > 7 {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, 101002,
					"param error, \t [InstanceId Error]:{%v} ", req.InstanceId)
				return rsp, err
			}
			rankList, err := taristmp.GetSpeedRunAsiaRankList(ctx, req.InstanceId, req.PageSize, req.PageNum)
			if err != nil {
				return rsp, err
			}
			rsp.RankList = rankList

			total, err := taristmp.GetAsiaTotal(ctx, req.InstanceId)
			if err != nil {
				return rsp, err
			}
			rsp.Total = total
		} else {
			rankList, err := taristmp.GetRankList(ctx, req.InstanceId, req.PageSize, req.PageNum, areaIDMap[index])
			if err != nil {
				return rsp, err
			}
			rsp.RankList = rankList

			total, err := taristmp.GetTotal(ctx, int32(index), req.InstanceId)
			if err != nil {
				return rsp, err
			}
			rsp.Total = total
		}
		personJSON, err := json.Marshal(rsp)
		if err != nil {
			return rsp, err
		}
		result[dataKey] = string(personJSON)
		err = redis.GetClient().HMSet(ctx, hashKey, result).Err()
		if err != nil {
			return rsp, err
		}
		expiration := 2 * 24 * time.Hour
		err = redis.GetClient().Expire(ctx, hashKey, expiration).Err()
		if err != nil {
			return rsp, err
		}
	}

	gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: req.FsourceId}
	gameProxy := gamePb.NewGameClientProxy()
	gameRoleInfo, roleErr := gameProxy.GetSavedRoleInfo(ctx, gameReq)
	if roleErr == nil {
		if index == 2 {
			self, err := taristmp.GetAsiaSelf(ctx, req.InstanceId, gameRoleInfo)
			if err != nil {
				return rsp, err
			}
			log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("taris get rank: %v", self))

			rsp.SelfMsg = self
		} else {
			self, err := taristmp.GetSelf(ctx, req.InstanceId, gameRoleInfo)
			if err != nil {
				return rsp, err
			}
			log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("taris get rank: %v", self))

			rsp.SelfMsg = self
		}
	}

	return rsp, nil
}

// UpdateRank 定时更新数据并且更新报名玩家任务完成情况
func (s *TaristmpImpl) UpdateRank(
	ctx context.Context,
	req *pb.UpdateRankReq,
) (rsp *pb.UpdateRankRsp, err error) {
	rsp = &pb.UpdateRankRsp{}

	// 先通过接口拉数据
	err = taristmp.UpdateSpeedRunAsiaData(ctx, req.FsourceId, req.InstanceId)
	if err != nil {
		return nil, err
	}

	err = taristmp.AddAsiaSendMessage(ctx, req.FsourceId, req.InstanceId)

	if err != nil {
		return nil, err
	}

	// 获取哈希数据
	redisPrefixKey := global.GetPrefix()
	hashKey := fmt.Sprintf("%v-taris-rank-page-26622", redisPrefixKey)
	scheduleCtx := context.Background()
	keys, errs := redis.GetClient().Del(scheduleCtx, hashKey).Result()
	if errs != nil {
		err = errs
		return
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("[taris] rank delete redis keys: [%v]",
		keys))

	// err = taristmp.CreateDemoData(ctx)
	// if err != nil {
	// 	return nil, err
	// }

	return
}

// UpdateTaskState 更新任务完成情况
func (s *TaristmpImpl) UpdateTaskState(
	ctx context.Context,
	req *pb.UpdateTaskStateReq,
) (*pb.UpdateTaskStateRsp, error) {
	rsp := &pb.UpdateTaskStateRsp{}
	// 查询用户3-8哪些没有完成

	err := taristmp.CheckReadyTask(ctx, req.FsourceId)
	if err != nil {
		return rsp, err
	}
	return rsp, err
}

// GetPresentList 获取排行榜奖励列表
func (s *TaristmpImpl) GetPresentList(
	ctx context.Context,
	req *pb.GetTarisOpenPresentListReq,
) (rsp *pb.GetTarisOpenPresentListRsp, err error) {
	rsp = &pb.GetTarisOpenPresentListRsp{
		PresentList: make([]*pb.TarisPresentList, 0),
	}
	// 获取各个副本的用户排名
	list, err := taristmp.GetPresentList(ctx, req.FsourceId)
	if err != nil {
		return
	}
	log.WithFieldsContext(ctx, "log_type", "debug").Infof(fmt.Sprintf("[taris] GetPresentList list: [%v]",
		list))
	if len(list) > 0 {
		rsp.PresentList = list
	}

	return rsp, nil
}

// SyncRankingDetails 同步屠龙分金排行榜明细
func (s *TaristmpImpl) SyncRankingDetails(ctx context.Context, req *pb.SyncRankingDetailsReq) (
	rsp *pb.SyncRankingDetailsRsp, err error,
) {
	rsp = &pb.SyncRankingDetailsRsp{}

	now := time.Now().Unix()
	// 创建一个UTC+8的时区
	location := time.FixedZone("UTC+7", 7*60*60)

	// 将当前时间转换为UTC+8时区的时间
	utc8Time := time.Unix(now, 0).In(location)
	if utc8Time.Unix() < time.Date(2024, 8, 3, 12, 59, 0, 0, location).Unix() {

		// 拉取数据
		err = taristmp.GetDragonDataByHTTP(ctx, req.Date)
		if err != nil {
			return
		}
		// 存储总积分表
		err = taristmp.SyncDragonTotalTable(ctx, req.Date)
		if err != nil {
			return
		}
		// 存储ES
		err = taristmp.InsertDragonEs(ctx, req.Date)
		if err != nil {
			return
		}

		scheduleCtx := context.Background()
		// 延迟一分钟删除防止es的缓存
		var wg sync.WaitGroup
		wg.Add(1) // 增加等待计数
		time.AfterFunc(1*time.Minute, func() {
			redisPrefixKey := global.GetPrefix()
			hashKey := fmt.Sprintf("%v-taris-dragon_total-page-26622", redisPrefixKey)
			_, errH := redis.GetClient().Del(scheduleCtx, hashKey).Result()
			if errH != nil {
				err = errH
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
					"[taris] dragon delete points redis keys: [%v], err: [%v]",
					hashKey, err.Error()))
			}

			hashKey = fmt.Sprintf("%v-taris-dragon-points-page-26622", redisPrefixKey)
			_, errH = redis.GetClient().Del(scheduleCtx, hashKey).Result()
			if errH != nil {
				err = errH
				log.WithFieldsContext(scheduleCtx, "log_type", "debug").Infof(fmt.Sprintf(
					"[taris] dragon delete points redis keys: [%v], err: [%v]",
					hashKey, err.Error()))
			}
			wg.Done()
		})
		wg.Wait()
	}
	return
}

// GetDragonPresentList 获取奖励列表
func (s *TaristmpImpl) GetDragonPresentList(ctx context.Context, req *pb.GetDragonPresentListReq) (
	rsp *pb.GetDragonPresentListRsp, err error,
) {

	rsp = &pb.GetDragonPresentListRsp{
		DragonParesentList: make([]*pb.DragonPresentItem, 0),
	}

	// 获取绑定角色信息
	gameReq := &gamePb.GetSavedRoleInfoReq{FsourceId: req.FsourceId}
	gameProxy := gamePb.NewGameClientProxy()
	gameRoleInfo, err := gameProxy.GetSavedRoleInfo(ctx, gameReq)
	if err != nil {
		return nil, err
	}

	data, err := taristmp.GetDataByRoleID(ctx, req.FsourceId, gameRoleInfo)
	if err != nil {
		return
	}
	list, err := taristmp.TotalDataChangeList(data, gameRoleInfo)
	if err != nil {
		return
	}

	rsp.DragonParesentList = list

	return
}

// GetDragonTotalPointList 获取总积分列表
func (s *TaristmpImpl) GetDragonTotalPointList(ctx context.Context, req *pb.GetDragonTotalPointListReq) (
	rsp *pb.GetDragonTotalPointListRsp, err error,
) {
	rsp = &pb.GetDragonTotalPointListRsp{
		List:  make([]*pb.DragonTotalPointItem, 0),
		Total: 0,
	}

	redisPrefixKey := global.GetPrefix()
	hashKey := fmt.Sprintf("%v-taris-dragon_total-page-26622", redisPrefixKey)
	result, err := redis.GetClient().HGetAll(ctx, hashKey).Result()
	if err != nil {
		return rsp, err
	}

	if req.PageSize == 0 {
		req.PageSize = 10
	}
	if req.PageNum == 0 {
		req.PageNum = 1
	}

	dataKey := fmt.Sprintf("%v-%v", req.PageSize, req.PageNum)

	if _, ok := result[dataKey]; ok {

		err = json.Unmarshal([]byte(result[dataKey]), &rsp)
		if err != nil {
			// 告警
			errs.NewCustomError(ctx, 611010, "redis get err,redisKey=%v,val=%v,err=%v", dataKey,
				result[dataKey],
				err)
			return rsp, err
		}
	} else {
		// 设置缓存
		list, err := taristmp.GetDragonTotalPointTop(ctx, req.PageSize, req.PageNum)

		if err != nil {
			return rsp, err
		}

		count, err := taristmp.GetDragonTotalCount(ctx)
		if err != nil {
			return rsp, err
		}
		rsp.Total = count

		rsp.List = list

		personJSON, err := json.Marshal(rsp)
		if err != nil {
			return rsp, err
		}
		result[dataKey] = string(personJSON)
		err = redis.GetClient().HMSet(ctx, hashKey, result).Err()
		if err != nil {
			return rsp, err
		}
		expiration := 2 * 24 * time.Hour
		err = redis.GetClient().Expire(ctx, hashKey, expiration).Err()
		if err != nil {
			return rsp, err
		}
	}

	return
}

// GetDragonPointList 获取积分列表
func (s *TaristmpImpl) GetDragonPointList(ctx context.Context, req *pb.GetDragonPointListReq) (
	rsp *pb.GetDragonPointListRsp, err error,
) {
	rsp = &pb.GetDragonPointListRsp{
		List: make([]*pb.DragonPointItem, 0),
	}
	redisPrefixKey := global.GetPrefix()
	hashKey := fmt.Sprintf("%v-taris-dragon-points-page-26622", redisPrefixKey)
	result, err := redis.GetClient().HGetAll(ctx, hashKey).Result()
	if err != nil {
		return rsp, err
	}
	dataKey := fmt.Sprintf("%v-%v", req.PageSize, req.PageNum)

	needCache := req.PageNum == 1 && (req.PeriodTime == "") && req.GuildName == "" &&
		req.ZoneId == 0 && req.PageSize == 20

	if _, ok := result[dataKey]; ok && needCache {

		err = json.Unmarshal([]byte(result[dataKey]), &rsp)
		if err != nil {
			// 告警
			errs.NewCustomError(ctx, 611010, "redis get err,redisKey=%v,val=%v,err=%v", dataKey,
				result[dataKey],
				err)
			return rsp, err
		}
	} else {
		// 从es中获取列表

		list, count, errE := taristmp.GetDragonRankByEs(ctx, req)
		if errE != nil {
			err = errE
			return
		}
		rsp.List = list
		rsp.Total = count
		if needCache {
			personJSON, err := json.Marshal(rsp)
			if err != nil {
				return rsp, err
			}
			result[dataKey] = string(personJSON)
			err = redis.GetClient().HMSet(ctx, hashKey, result).Err()
			if err != nil {
				return rsp, err
			}
			expiration := 2 * 24 * time.Hour
			err = redis.GetClient().Expire(ctx, hashKey, expiration).Err()
			if err != nil {
				return rsp, err
			}
		}
	}

	return
}

// AddSignUpRoleID 记录报名的roleId
func (s *TaristmpImpl) AddSignUpRoleID(ctx context.Context, req *pb.AddSignUpRoleIDReq) (
	rsp *pb.AddSignUpRoleIDRsp, err error,
) {
	rsp = &pb.AddSignUpRoleIDRsp{}

	err = taristmp.InsertRoleID(ctx, req.RoleId, req.LangType, req.FsourceId)
	if err != nil {
		return
	}
	return
}

// ScheduledSend 塔瑞斯开服竞速任务
func (s *TaristmpImpl) ScheduledSend(ctx context.Context, req *pb.TarisScheduledSendReq) (
	rsp *pb.TarisScheduledSendRsp, err error,
) {
	rsp = &pb.TarisScheduledSendRsp{}
	fmt.Println(">>>>>>>>>>>>>>ScheduledSend<<<<<<<<<<<<<<<<<")
	go taristmp.ScheduledSend(ctx)
	return
}

// DelBindRole 塔瑞斯开服竞速删除绑定角色
func (s *TaristmpImpl) DelBindRole(ctx context.Context, req *pb.TarisDelBindRoleReq) (
	rsp *pb.TarisDelBindRoleRsp, err error,
) {
	rsp = &pb.TarisDelBindRoleRsp{}
	taristmp.DeleteBindRole(ctx)
	return
}

// GetDragonRankListExcel 塔瑞斯屠龙分金排行列表邮箱
func (s *TaristmpImpl) GetDragonRankListExcel(ctx context.Context, req *pb.GetDragonRankListExcelReq) (
	rsp *pb.GetDragonRankListExcelRsp, err error,
) {
	rsp = &pb.GetDragonRankListExcelRsp{}
	data := make([][]string, 0)
	data = append(data, []string{
		"test1",
	})
	filePath, err := datadump.CreateExcel(ctx, "test.xlsx", data)
	// go taristmp.GetDragonRank(ctx, req.PeriodTime)
	if err != nil {
		return
	}
	mediaId, err := datadump.QWXUploadMedia(ctx, filePath, "268e5d13-f0db-4bf4-afa2-3112dabdd29c", "test.xlsx")
	if err != nil {
		return
	}
	log.WithFieldsContext(ctx, "log_info").Infof("mediaId: %v", mediaId)
	option := &datadump.SendOption{
		Msgtype: "file",
		Chatid:  "wrkSFfCgAA9d0ZmRiwpSS3Y-xrxLcH4g",
		File: &datadump.FileType{
			MediaId: mediaId,
		},
	}
	res, err := datadump.QWXSend(ctx, option, "268e5d13-f0db-4bf4-afa2-3112dabdd29c")
	if err != nil {
		return
	}
	log.WithFieldsContext(ctx, "log_info").Infof("res: %v", res)
	return
}

// GetSpeedRankListExcel 塔瑞斯开服竞速排行列表邮箱
func (s *TaristmpImpl) GetSpeedRankListExcel(ctx context.Context, req *pb.GetSpeedRankListExcelReq) (
	rsp *pb.GetSpeedRankListExcelRsp, err error,
) {
	rsp = &pb.GetSpeedRankListExcelRsp{}
	go taristmp.GetSpeedRank(ctx, req.InstanceId)
	return
}
