package code

import "git.code.oa.com/trpc-go/trpc-go/errs"

const (
	// ActivityNotExistsError 活动不存在
	ActivityNotExistsError = 519001
	// ActivityNotStartError 活动未开始
	ActivityNotStartError = 519002
	// ActivityEndError 活动已结束
	ActivityEndError = 519003
	// ActivityJoinReqLimit 参与活动次数受限
	ActivityJoinReqLimit = 519004
	// ActivityNotFindTask 未找到任务
	ActivityNotFindTask = 519005
	// ActivityTaskCanNotCancel 任务不能取消
	ActivityTaskCanNotCancel = 519006
	// ActivityTaskFinish 助力任务已经完成
	ActivityTaskFinish = 519007
	// ActivityTaskHelpNumLimit 任务助力次数受限
	ActivityTaskHelpNumLimit = 519008
	// ActivityTaskHelpNotComplete 任务助力未完成
	ActivityTaskHelpNotComplete = 519009
	// ActivityNotAllowHelpSelf 不允许助力自己
	ActivityNotAllowHelpSelf = 519010
	// ActivityUserExceedTimeRangeLimit 掏鸟窝活动 超过单个用户起止时间限制
	ActivityUserExceedTimeRangeLimit = 519011
	// ActivityUserExceedTimeRangeLimit 掏鸟窝活动 用户掏鸟窝活动信息不存在
	ActivityUserNestInfoNotFound = 519012
)

var (
	ErrActivityNotExists           = errs.New(ActivityNotExistsError, "Activity Not Exists")
	ErrActivityNotStart            = errs.New(ActivityNotStartError, "Activity Not Start")
	ErrActivityEnd                 = errs.New(ActivityEndError, "Activity End")
	ErrActivityJoinReqLimit        = errs.New(ActivityJoinReqLimit, "Join Request Limit")
	ErrActivityNotFindTask         = errs.New(ActivityNotFindTask, "Not Find Task")
	ErrActivityTaskCanNotCancel    = errs.New(ActivityTaskCanNotCancel, "Activity Task Can Not Cancel")
	ErrActivityTaskFinish          = errs.New(ActivityTaskFinish, "Activity Task Finish")
	ErrActivityTaskHelpNumLimit    = errs.New(ActivityTaskHelpNumLimit, "Activity Task Help Num Limit")
	ErrActivityTaskHelpNotComplete = errs.New(ActivityTaskHelpNotComplete, "Activity Task Help Not Complete")
	ErrActivityNotAllowHelpSelf    = errs.New(ActivityNotAllowHelpSelf, "Activity Not Allow Help Self")
)
