// Package address 地址信息
package address

import (
	"context"
	"errors"
	"fmt"

	DB "git.code.oa.com/iegg_distribution/Marketing_group/act.common/database/mysql"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/metadata"
	pb "git.code.oa.com/trpcprotocol/publishing_marketing/logicial_address"
	"git.woa.com/gpts/baselib/crypto"
	"gorm.io/gorm"
	"trpc.act.logicial/app/code"
	"trpc.act.logicial/app/config"
	"trpc.act.logicial/app/logic/address"
	addressModel "trpc.act.logicial/app/model/address"
)

// AddressImpl 地址
type AddressImpl struct{}

// GetPUBGMAddressInfo 获取地址信息
func (s *AddressImpl) GetPUBGMAddressInfo(ctx context.Context, req *pb.GetPUBGMAddressReq) (rsp *pb.PUBGMAddressData,
	err error) {
	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var PUBGMData addressModel.PUBGMAdressData
	var conf = config.GetConfig()

	// AesSecret 秘钥
	var AesSecret = conf.Crypto
	if errM := DB.DefaultConnect().WithContext(ctx).Table(PUBGMData.TableName()).Where("uid = ?", userAccount.Uid).
		Where("account_type = ?", userAccount.AccountType).Where("game_id = ?", userAccount.IntlAccount.GameId).
		First(&PUBGMData).Error; errM != nil {
		if errors.Is(errM, gorm.ErrRecordNotFound) {
			rsp = &pb.PUBGMAddressData{
				HasSet: false,
			}
			return
		} else {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", errM.Error())
			return
		}
	}
	aesUtil := crypto.NewAesCrypto()
	var name, phone, country, address, zipCode string
	if PUBGMData.Name != "" {
		nameByte, _ := aesUtil.DecryptBase64([]byte(PUBGMData.Name), []byte(AesSecret))
		name = string(nameByte)
	}
	if PUBGMData.PhoneNumber != "" {
		phoneByte, _ := aesUtil.DecryptBase64([]byte(PUBGMData.PhoneNumber), []byte(AesSecret))
		phone = string(phoneByte)
	}
	if PUBGMData.Country != "" {
		countryByte, _ := aesUtil.DecryptBase64([]byte(PUBGMData.Country), []byte(AesSecret))
		country = string(countryByte)
	}
	if PUBGMData.Address != "" {
		addressByte, _ := aesUtil.DecryptBase64([]byte(PUBGMData.Address), []byte(AesSecret))
		address = string(addressByte)
	}
	if PUBGMData.ZipCode != "" {
		zipCodeByte, _ := aesUtil.DecryptBase64([]byte(PUBGMData.ZipCode), []byte(AesSecret))
		zipCode = string(zipCodeByte)
	}

	rsp = &pb.PUBGMAddressData{
		HasSet:      true,
		Name:        name,
		PhoneNumber: phone,
		Country:     country,
		Address:     address,
		ZipCode:     zipCode,
	}
	return
}

// SavePUBGMAddressInfo 保存
func (s *AddressImpl) SavePUBGMAddressInfo(ctx context.Context, req *pb.SavePUBGMAddressReq) (
	rsp *pb.SavePUBGMAddressRsp,
	err error) {
	// 账号判断
	userAccount, err := metadata.GetUserAccount(ctx)

	if err != nil {
		return
	}
	aesUtil := crypto.NewAesCrypto()
	var conf = config.GetConfig()

	// AesSecret 秘钥
	var AesSecret = conf.Crypto
	fmt.Printf("%#v\n", AesSecret)
	nameByte, _ := aesUtil.EncryptBase64([]byte(req.Name), []byte(AesSecret))
	phoneByte, _ := aesUtil.EncryptBase64([]byte(req.PhoneNumber), []byte(AesSecret))
	countryByte, _ := aesUtil.EncryptBase64([]byte(req.Country), []byte(AesSecret))
	addressByte, _ := aesUtil.EncryptBase64([]byte(req.Address), []byte(AesSecret))
	zipCodeByte, _ := aesUtil.EncryptBase64([]byte(req.ZipCode), []byte(AesSecret))
	data := addressModel.PUBGMAdressData{
		Uid:         userAccount.Uid,
		AccountType: int32(userAccount.AccountType),
		Name:        string(nameByte),
		Address:     string(addressByte),
		Country:     string(countryByte),

		PhoneNumber: string(phoneByte),
		ZipCode:     string(zipCodeByte),
		GameId:      req.GameId,
	}

	if errM := DB.DefaultConnect().WithContext(ctx).Table(data.TableName()).Where("uid = ?", userAccount.Uid).
		Where("account_type = ?",
			userAccount.AccountType).First(&data).Error; errM != nil {
		if errors.Is(errM, gorm.ErrRecordNotFound) {
			if errM := DB.DefaultConnect().WithContext(ctx).Table(data.TableName()).Create(&data).Error; errM != nil {
				err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
					"db create error, \t [Error]:{%v} ", errM.Error())
				return
			}
			rsp = &pb.SavePUBGMAddressRsp{}
			return
		}
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db first error, \t [Error]:{%v} ", errM.Error())
		return
	}
	updateData := map[string]interface{}{
		"name":         string(nameByte),
		"address":      string(addressByte),
		"country":      string(countryByte),
		"phone_number": string(phoneByte),
		"zip_code":     string(zipCodeByte),
		"game_id":      req.GameId,
	}
	if errM := DB.DefaultConnect().WithContext(ctx).Table(data.TableName()).Where("uid = ?", userAccount.Uid).
		Where("account_type = ?",
			userAccount.AccountType).Updates(updateData).Error; errM != nil {
		err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
			"db update error, \t [Error]:{%v} ", errM.Error())
		return
	}
	rsp = &pb.SavePUBGMAddressRsp{}
	return
}

// GetPUBGMAddressInfoWithCrypto 保存
func (s *AddressImpl) GetPUBGMAddressInfoWithCrypto(ctx context.Context, req *pb.GetPUBGMAddressReq) (
	rsp *pb.PUBGMAddressData, err error) {
	userAccount, err := metadata.GetUserAccount(ctx)
	if err != nil {
		return
	}
	var PUBGMData addressModel.PUBGMAdressData

	if errM := DB.DefaultConnect().WithContext(ctx).Table(PUBGMData.TableName()).Where("uid = ?", userAccount.Uid).
		Where("account_type = ?",
			userAccount.AccountType).Where("game_id = ?", userAccount.IntlAccount.GameId).First(&PUBGMData).Error; errM != nil {
		if errors.Is(errM, gorm.ErrRecordNotFound) {
			rsp = &pb.PUBGMAddressData{
				HasSet: false,
			}
			return
		} else {
			err = errs.NewSystemError(ctx, errs.ErrorTypeMysql, DB.MySqlConnectErr,
				"db error, \t [Error]:{%v} ", errM.Error())
			return
		}
	}
	rsp = &pb.PUBGMAddressData{
		HasSet:      true,
		Name:        PUBGMData.Name,
		PhoneNumber: PUBGMData.PhoneNumber,
		Country:     PUBGMData.Country,
		Address:     PUBGMData.Address,
		ZipCode:     PUBGMData.ZipCode,
	}
	return
}

// GetAccountAddressInfo 获取用户地址信息
func (s *AddressImpl) GetAccountAddressInfo(ctx context.Context, req *pb.GetAccountAddressInfoReq) (
	rsp *pb.GetAccountAddressInfoRsp, err error) {

	addressInfo, err := address.GetAccountAddressInfo(ctx, req.FsourceId)
	if err != nil {
		return nil, err
	}
	return &pb.GetAccountAddressInfoRsp{
		Name:        addressInfo.Name,
		PhoneNumber: addressInfo.PhoneNumber,
		Country:     addressInfo.Country,
		Address:     addressInfo.Address,
		ZipCode:     addressInfo.ZipCode,
		Email:       addressInfo.Email,
		// FsourceId:   addressInfo.FsourceID,
	}, nil

}

// SaveAccountAddressInfo 保存用户地址信息
func (s *AddressImpl) SaveAccountAddressInfo(ctx context.Context, req *pb.SaveAccountAddressInfoReq) (
	rsp *pb.SaveAccountAddressInfoRsp, err error) {

	if req.Name == "" {
		return nil, errs.NewCustomError(ctx, code.NameIsEmptyError, "Name Is Empty")
	}
	if err = address.SaveAccountAddressInfo(ctx, req); err != nil {
		return nil, err
	}
	return &pb.SaveAccountAddressInfoRsp{}, nil
}
