package dynamics

import (
	"context"
	"strconv"
	"time"

	"github.com/spf13/cast"
	"trpc.publishing_application.standalonesite/app/logic/comment"
	"trpc.publishing_application.standalonesite/app/logic/user"
	userService "trpc.publishing_application.standalonesite/app/logic/user"

	"git.code.oa.com/iegg_distribution/Marketing_group/act.common/errs"
	"trpc.publishing_application.standalonesite/app/code"
	"trpc.publishing_application.standalonesite/app/model"

	"trpc.publishing_application.standalonesite/app/logic/creatorhub"

	"trpc.publishing_application.standalonesite/app/logic/message"
	"trpc.publishing_application.standalonesite/app/logic/news"

	pb "git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics"
	hotService "trpc.publishing_application.standalonesite/app/logic/hot"
	messageService "trpc.publishing_application.standalonesite/app/logic/message"
	tweetService "trpc.publishing_application.standalonesite/app/logic/tweet"
)

func (s *DynamicsImpl) SyncESData(c context.Context, req *pb.SyncESDataReq) (*pb.SyncESDataRsp, error) {
	syncESDataRsp := &pb.SyncESDataRsp{}
	go tweetService.SyncPostESData(c)
	return syncESDataRsp, nil
}

func (s *DynamicsImpl) SyncUserESData(c context.Context, req *pb.SyncESDataReq) (*pb.SyncESDataRsp, error) {
	syncESDataRsp := &pb.SyncESDataRsp{}
	go tweetService.SyncUserESData(c)
	return syncESDataRsp, nil
}

func (s *DynamicsImpl) HotPostEventTicker(c context.Context, req *pb.HotPostEventTickerReq) (*pb.HotPostEventTickerRsp, error) {
	hotPostEventTickerRsp := &pb.HotPostEventTickerRsp{}
	go hotService.HotPostEventExec()
	return hotPostEventTickerRsp, nil
}

func (s *DynamicsImpl) SyncNewsByMedia(ctx context.Context, req *pb.SyncNewsByMediaReq) (rsp *pb.SyncNewsByMediaRsp, err error) {
	rsp = &pb.SyncNewsByMediaRsp{}
	// 重新赋值一个上下文，避免报错
	go news.SyncYoutubeNews(context.Background())
	return rsp, nil
}

func (s *DynamicsImpl) SiteMessageTicker(ctx context.Context, req *pb.SiteMessageTickerReq) (rsp *pb.SiteMessageTickerRsp, err error) {
	rsp = &pb.SiteMessageTickerRsp{}
	// 重新赋值一个上下文，避免报错
	go message.SiteMessageTicker()
	return rsp, nil
}

func (s *DynamicsImpl) SyncCreatorHubActivityWork(c context.Context, req *pb.SyncCreatorHubActivityWorkReq) (rsp *pb.SyncCreatorHubActivityWorkRsp, err error) {
	if req.GameId == "" || req.AreaId == "" {
		return &pb.SyncCreatorHubActivityWorkRsp{}, nil
	}
	go creatorhub.SyncCreatorHubWork(req.GameId, req.AreaId)
	return &pb.SyncCreatorHubActivityWorkRsp{}, nil
}

func (s *DynamicsImpl) SyncCreatorHubActivityTask(c context.Context, req *pb.SyncCreatorHubActivityTaskReq) (rsp *pb.SyncCreatorHubActivityTaskRsp, err error) {
	if req.GameId == "" || req.AreaId == "" {
		return &pb.SyncCreatorHubActivityTaskRsp{}, nil
	}
	go creatorhub.SyncCreatorHubActivity(req.GameId, req.AreaId)
	return &pb.SyncCreatorHubActivityTaskRsp{}, nil
}

func (s *DynamicsImpl) SyncUserIntlOpenid(c context.Context, req *pb.SyncUserIntlOpenidReq) (*pb.SyncUserIntlOpenidRsp, error) {
	rsp := &pb.SyncUserIntlOpenidRsp{}
	go tweetService.SyncUserIntlUserOpenid(context.Background())
	return rsp, nil
}

func (s *DynamicsImpl) SyncCreatedOnMs(c context.Context, req *pb.SyncCreatedOnMsReq) (*pb.SyncCreatedOnMsRsp, error) {
	rsp := &pb.SyncCreatedOnMsRsp{}
	go tweetService.SyncPostCreatedOnMs(context.Background())
	go tweetService.SyncPostCommentCreatedOnMs(context.Background())
	go tweetService.SyncReportPostCommentCreatedOnMs(context.Background())
	return rsp, nil
}

func (s *DynamicsImpl) SyncBaseData(c context.Context, req *pb.SyncBaseDataReq) (*pb.SyncBaseDataRsp, error) {
	rsp := &pb.SyncBaseDataRsp{}

	switch req.Type {
	case 1:
		// 把动态内容中的platform字段同步到主表当中
		go tweetService.SyncPostContentPlatformToPost(context.Background())
		break
	case 2:
		// 把视频动态内容的封面图转存
		go tweetService.PostVideoContentCoverTransfer(context.Background())
		break
	case 3:
		// 同步用户ES数据
		go tweetService.SyncUserESData(c)
		break
	case 4:
		// 同步点赞数据到stats
		go tweetService.SyncPostToStats(context.Background())
		break
	case 5:
		// 官方数据同步到content
		go tweetService.SyncPostOfficialToPost(context.Background(), req.Params)
		break
	case 6:
		// post 同步到 post_language中
		go tweetService.SyncPostToLanguage(context.Background())
		break
	case 7:
		// post中的language 同步到 post_content中的language字段
		go tweetService.SyncPostLanguageToContent(context.Background())
		break
	case 8:
		go tweetService.SyncPostESData(c)
		break
	case 9:
		// 测试一下站内信逻辑
		data := &model.SiteMessageKafkaData{
			MsgID:          14,
			IntlOpenidList: []string{"29080-12945745392039390084"},
		}
		go messageService.SiteMessageConsumeHandler(c, data)
		break
	case 10:
		go userService.BatchSaveUserByTest() //同步用户数据
		break
	case 11:
		go userService.SyncUserLanguageToUserAudit(context.Background())
		go tweetService.SyncPostReportLanguage(context.Background())
		go comment.SyncPostCommentLanguage(context.Background())
	case 12:
		go userService.SyncUserStateDataToUserState(context.Background())
	case 13:
		go comment.SyncPostCommentUpVote(context.Background())
	case 14:
		go tweetService.SyncNikkeArtPostESData(context.Background())
	case 15:
		go tweetService.RefreshPostAudit(context.Background())
	case 16:
		go tweetService.SyncPostToStatsV2(context.Background())
	case 17:
		go tweetService.SyncMissedPostLanguageData(context.Background())
	case 18:
		go tweetService.SyncPostOpenUp(context.Background())
	case 19:
		go user.UserRegisterActivity(context.Background(), "29080-14074224494942409276")
	case 20:
		go comment.SyncCommentContentAuditToRedis(context.Background())
	case 21:
		go tweetService.SyncNeedAuditPostUUIDIntoRedisSet(context.Background())
	case 22:
		var maxId = 0
		// 将params 转为int
		if req.Params != "" {
			maxId, _ = strconv.Atoi(req.Params)
		}

		go tweetService.SyncUserNameEsData(context.Background(), maxId)
	case 23:
		go comment.SyncReviewedToCommentContentAudit(context.Background())
	case 24:
		go tweetService.SyncReviewedToPostContentAudit(context.Background())
	case 25:
		go tweetService.SyncPostStarToStatsV1(context.Background())
	case 26:
		go tweetService.SyncNeedAuditTWPostUUIDIntoRedisSet(context.Background())
	case 27:
		go tweetService.SyncPostCommentReplyCountStats(context.Background())
	case 28:
		go tweetService.SyncPostAuditData(context.Background(), req.Params)
	case 29:
		go tweetService.MachineAuditPostConsumeHandler(context.Background(), []string{"6598251761268731028-2789"})
	// case 30:
	// 	go user.GetUserMinorcerStatus(context.Background(), req.Params)
	case 31:
		go tweetService.SyncPostToPostAudit(context.Background(), req.Params)
	case 32:
		go comment.SyncInsertCommentToHotCache(context.Background())
	case 33:
		go user.SyncUserAuditStatus(context.Background())
	case 34:
		var endTime int64
		if req.Params == "1" {
			endTime = 1744214400
		}
		_ = user.CMSSetUserDemotion(context.Background(), []string{"29080-17113266706050275782"}, endTime, cast.ToInt(req.Params), "v_grhhuang")
	case 35:
		go creatorhub.SyncCreatorUserCountry(context.Background())
	case 36:
		// 用于测试网关调用微服务超时报错
		time.Sleep(30 * time.Second)
	default:
		return nil, errs.NewSystemError(c, errs.DefaultSystemMsg, code.DefaultErr, "type is required")
	}
	return rsp, nil
}

func (s *DynamicsImpl) SyncSiteMessageStatTicker(c context.Context, req *pb.SyncSiteMessageStatReq) (*pb.SyncSiteMessageStatRsp, error) {
	rsp := &pb.SyncSiteMessageStatRsp{}
	go message.SyncSiteMessageStat(context.Background())
	return rsp, nil
}

// 定时任务审核，帖子审核 TODO，可以改成发布帖子的时候实时写入kafka，没必要定时去捞取
func (s *DynamicsImpl) PostMachineReviewTicker(c context.Context, req *pb.PostMachineReviewReq) (*pb.PostCommentReplyRsp, error) {
	rsp := &pb.PostCommentReplyRsp{}
	go tweetService.PostMachineAudit(context.Background(), "", "")
	return rsp, nil
}

// 定时任务审核，评论审核 TODO，可以改成发布帖子的时候实时写入kafka，没必要定时去捞取
func (s *DynamicsImpl) CommentContentMachineReviewTimer(c context.Context, req *pb.CommentContentMachineReviewTimerReq) (*pb.CommentContentMachineReviewTimerRsp, error) {
	rsp := &pb.CommentContentMachineReviewTimerRsp{}
	go comment.CommentAuditByMachineAudit(context.Background())
	return rsp, nil
}

// 定时任务审核，用户信息审核 TODO，可以改成发布帖子的时候实时写入kafka，没必要定时去捞取
func (s *DynamicsImpl) UserInfoMachineReviewTimer(c context.Context, req *pb.UserInfoMachineReviewTimerReq) (*pb.UserInfoMachineReviewTimerRsp, error) {
	rsp := &pb.UserInfoMachineReviewTimerRsp{}
	go userService.UserInfoByMachineAduit(context.Background())
	return rsp, nil
}

// 定时计算评论热度
func (s *DynamicsImpl) HotCommentEventTicker(c context.Context, req *pb.HotCommentEventTickerReq) (*pb.HotCommentEventTickerRsp, error) {
	hotCommentEventTickerRsp := &pb.HotCommentEventTickerRsp{}
	go hotService.CommentHotPostEventExec()
	return hotCommentEventTickerRsp, nil
}
