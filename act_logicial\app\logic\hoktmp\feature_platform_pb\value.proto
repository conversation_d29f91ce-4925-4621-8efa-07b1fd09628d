syntax = "proto3";
package helloworld;

option go_package = "git.code.oa.com/examples/helloworld";
option java_package = "com.trpc.examples.helloworld";


message StringList { repeated string items = 1; }

message FloatList { repeated float items = 1; }

message Int64List { repeated int64 items = 1; }

message StringFloatMap {
  repeated string keys = 1;
  repeated float values = 2;
}

message Int64FloatMap {
  repeated int64 keys = 1;
  repeated float values = 2;
}

message StringFloatListMap {
  message Item {
    string key = 1;
    repeated float value = 2;
  }
  repeated Item items = 1;
}

message StringInt64Map {
  repeated string keys = 1;
  repeated int64 values = 2;
}

message BytesList { repeated bytes items = 1; }

message Int32List { repeated int32 items = 1; }

message Uint32List { repeated uint32 items = 1; }

message Uint64List { repeated uint64 items = 1; }

message DoubleList { repeated double items = 1; }

message BoolList { repeated bool items = 1; }

message ValueList { repeated Value items = 1; }

message ValueMap { map<string, Value> items = 1; }

message Value {
  oneof kind {
    string string_val = 1;
    bool bool_val = 2;
    float float_val = 3;
    double double_val = 4;
    bytes bytes_val = 5;
    int32 int32_val = 6;
    uint32 uint32_val = 7;
    int64 int64_val = 8;
    uint64 uint64_val = 9;
    StringList string_list_val = 10;
    FloatList float_list_val = 11;
    Int64List int64_list_val = 12;
    StringFloatMap string_float_map_val = 13;
    Int64FloatMap int64_float_map_val = 14;
    StringFloatListMap string_float_list_map_val = 15;
    StringInt64Map string_int64_map_val = 16;
    BytesList bytes_list_val = 17;
    Int32List int32_list_val = 18;
    Uint32List uint32_list_val = 19;
    Uint64List uint64_list_val = 20;
    DoubleList double_list_val = 21;
    BoolList bool_list_val = 22;
  }
}