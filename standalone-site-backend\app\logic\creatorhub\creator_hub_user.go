package creatorhub

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"trpc.publishing_application.standalonesite/app/config"
	"trpc.publishing_application.standalonesite/app/constants"
	"trpc.publishing_application.standalonesite/app/logic/cache"
	"trpc.publishing_application.standalonesite/app/model"
)

type CreatorHubUserRes struct {
	Ret  int32                     `json:"ret"`
	Msg  string                    `json:"msg"`
	Data *model.CreatorHubUserInfo `json:"data"`
}

type CheckTokenValidRes struct {
	Ret  int32  `json:"ret"`
	Msg  string `json:"msg"`
	Data struct {
		IsValid bool `json:"is_vaild"`
	} `json:"data"`
}

// 获取网红作者详情
func GetCreatorHubUserInfo(ctx context.Context, uid string, gameId string, areaId string, withCache bool) (user *model.CreatorHubUserInfo, err error) {
	user = &model.CreatorHubUserInfo{}
	if uid == "" {
		return nil, errors.New("uid is empty")
	}
	redisKey := cache.GetCreatorHubUserInfoKey(uid, gameId, areaId)
	// 缓存
	getCreatorHubUserFromApi := func(c context.Context) (interface{}, error) {
		var userRes CreatorHubUserRes
		user := &model.CreatorHubUserInfo{}
		userDetailUrl := config.GetConfig().CreatorHubSetting.CreatorHubUserDetailPath
		postData := map[string]interface{}{
			"gameid": gameId,
			"areaid": areaId,
			"uid":    uid,
		}
		postDataByte, err := json.Marshal(postData)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubUserInfo | json marshal failed: %v", err)
			return nil, err
		}
		// println(string(postDataByte), userDetailUrl)
		resultOption, err := RequestCreateHub(ctx, gameId, areaId, userDetailUrl, postDataByte)
		// resultOption := "{\"ret\":0,\"msg\":\"OK\",\"data\":{\"user_id\":\"2535\",\"user_name\":\"space@#￥15\",\"email\":\"<EMAIL>\",\"third_channels\":[{\"channel_type\":1,\"channel_name\":\"YouTube\"},{\"channel_type\":4,\"channel_name\":\"Twitch\"},{\"channel_type\":11,\"channel_name\":\"Pixiv\"}],\"status\":2}}"
		// err = nil
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubUserInfo | RequestCreateHub failed: %v", err)
			return nil, err
		} else {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Infof("GetCreatorHubUserInfo | uid: %d resultOption: %s", uid, resultOption)
		}
		err = json.Unmarshal([]byte(resultOption), &userRes)
		if err != nil {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubUserInfo | json unmarshal failed: %v", err)
			return nil, err
		}
		if userRes.Ret != 0 {
			log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("GetCreatorHubUserInfo | userRes ret: %d, msg: %s", userRes.Ret, userRes.Msg)
			return nil, errors.New("userRes ret: " + string(userRes.Ret))
		}
		user = userRes.Data
		// 设置缓存
		cache.SetCacheWithMarshal(ctx, redisKey, user, 2*time.Minute)
		return user, nil
	}

	u, err := cache.GetCacheWithUnmarshal(ctx, redisKey, user, nil, &getCreatorHubUserFromApi, withCache)
	if err != nil {
		return nil, err
	}
	user = u.(*model.CreatorHubUserInfo)
	return user, nil
}

// 账户是否正常
func IsCreatorHubUserNormal(ctx context.Context, uid string, gameId string, areaId string, withCache bool) (bool, constants.ECreatorHubUserStatus, error) {
	user, err := GetCreatorHubUserInfo(ctx, uid, gameId, areaId, withCache)
	if err != nil {
		return false, constants.CH_USER_STATUS_NORMAL, err
	}
	if user == nil {
		return false, constants.CH_USER_STATUS_NORMAL, errors.New("creator hub user is nil")
	}
	return user.Status == constants.ECreatorHubUserStatus(constants.CH_USER_STATUS_NORMAL), constants.ECreatorHubUserStatus(user.Status), nil
}

// 检查用户token是否有效
func IsUserTokenValid(ctx context.Context, uid string, token string, gameId string, areaId string) (bool, error) {
	checkTokenUrl := config.GetConfig().CreatorHubSetting.CreatorHubCheckUserTokenPath
	postData := map[string]interface{}{
		"gameid": gameId,
		"areaid": areaId,
		"uid":    uid,
		"token":  token,
	}
	postDataByte, err := json.Marshal(postData)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("IsUserTokenValid | json marshal failed: %v", err)
		return false, err
	}
	resultOption, err := RequestCreateHub(ctx, gameId, areaId, checkTokenUrl, postDataByte)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("IsUserTokenValid | RequestCreateHub failed: %v", err)
		return false, err
	}
	var checkTokenValidRes CheckTokenValidRes
	err = json.Unmarshal([]byte(resultOption), &checkTokenValidRes)
	if err != nil {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("IsUserTokenValid | json unmarshal failed: %v", err)
		return false, err
	}
	if checkTokenValidRes.Ret != 0 {
		log.WithFieldsContext(context.Background(), "log_type", constants.LogType_Standalonesite).Errorf("IsUserTokenValid | checkTokenValidRes ret: %d, msg: %s", checkTokenValidRes.Ret, checkTokenValidRes.Msg)
		return false, errors.New("checkTokenValidRes ret: " + string(checkTokenValidRes.Ret))
	}
	return checkTokenValidRes.Data.IsValid, nil
}
