package model

type EmoticonIconGroup struct {
	*Model
	GroupID int    `gorm:"column:group_id" json:"group_id"` //表情包id
	IconID  int    `gorm:"column:icon_id" json:"icon_id"`   //表情id
	Creator string `gorm:"column:creator" json:"creator"`   //创建人
	Updater string `gorm:"column:updater" json:"updater"`   //操作人

}

func (p *EmoticonIconGroup) TableName() string {
	return "p_emoticon_icon_group_relationship"
}
