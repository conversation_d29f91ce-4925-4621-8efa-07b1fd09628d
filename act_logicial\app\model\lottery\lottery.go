// Package lottery 抽奖
package lottery

import (
	"gorm.io/plugin/soft_delete"
	"trpc.act.logicial/app/model"
)

// LotteryTotalModel 抽奖表
type LotteryTotalModel struct {
	model.AppModel
}

// TableName 指定表名 这样可以强制指定表名 不指定表名会用struct自动查找表名
func (LotteryTotalModel) TableName() string {
	return "lottery_account_total_log"
}

// LotteryTotalData 抽奖记录表
type LotteryTotalData struct {
	LotteryTotalModel
	ID           int64                 `gorm:"type:int(11);column:id;primary_key"`
	DayTimestamp int64                 `gorm:"type:int(11);column:day_timestamp;not null"`
	Uid          string                `gorm:"type:varchar(32);column:uid;''"`
	AccountType  int32                 `gorm:"type:tinyint(1);column:account_type;0"`
	FsourceId    string                `gorm:"type:varchar(32);column:Fsource_id;null"`
	LotteryId    int64                 `gorm:"type:varchar(30);column:lottery_id;not null"`
	TotalNum     int64                 `gorm:"type:int(11);column:total_num;not null;0"`
	TodayNum     int64                 `gorm:"type:int(11);column:today_num;not null;0"`
	TotalUseNum  int64                 `gorm:"type:int(11);column:total_use_num;not null;0"`
	TodayUseNum  int64                 `gorm:"type:int(11);column:today_use_num;not null;0"`
	DeletedAt    soft_delete.DeletedAt `gorm:"softDelete:milli;column:deleted_at"`
}

// PresentLotteryLevelList 设置礼包抽奖等级
type PresentLotteryLevelList struct {
	PresentID string
	LevelList []int64
}
